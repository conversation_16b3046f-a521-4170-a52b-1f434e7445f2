# FlightPig - AI-Powered Flight Mission Generator for MSFS

FlightPig is an AI-powered console application that generates dynamic flight missions for Microsoft Flight Simulator using Ollama and the DeepSeek-R1 model. It integrates with MSFS through the upgraded FsConnect library to provide real-time aircraft data and mission progress monitoring. Now featuring **voice recognition and text-to-speech** for hands-free operation!

## Features

- **AI-Generated Missions**: Uses Ollama with DeepSeek-R1 (8B) to create realistic flight missions based on your current aircraft and location
- **Voice Recognition**: Whisper-powered speech recognition for hands-free command input
- **Text-to-Speech**: ElevenLabs integration for audio feedback and mission briefings
- **Real-time Integration**: Connects to Microsoft Flight Simulator via SimConnect for live aircraft data
- **Mission Progress Monitoring**: Tracks objective completion in real-time with visual progress indicators
- **Multiple Objective Types**: Supports various mission objectives including:
  - FlyOver: Fly over a specific location
  - LandAt: Land at a specific airport or location
  - LandAtAndWait: Land and wait for a specified time
  - NavigateTo: Navigate to a specific waypoint
  - MaintainAltitude: Maintain a specific altitude for a period
  - MaintainSpeed: Maintain a specific airspeed
  - And more...

## Prerequisites

1. **Microsoft Flight Simulator 2020** - Must be running with an active flight
2. **Ollama** - Must be installed and running locally
   - Download from: https://ollama.ai/
   - Default URL: http://localhost:11434
3. **.NET 8.0 Runtime** - Required to run the application
4. **ElevenLabs API Key** (Optional) - For text-to-speech functionality
   - Sign up at: https://elevenlabs.io/
   - Get your API key from the profile section
5. **Microphone** (Optional) - For voice recognition features

## Installation

1. Clone or download the FlightPig project
2. Ensure all prerequisites are installed and running
3. Build the project:
   ```bash
   dotnet build
   ```

## Usage

1. **Start Microsoft Flight Simulator** and load into any aircraft at any location
2. **Start Ollama** (if not already running):
   ```bash
   ollama serve
   ```
3. **Run FlightPig**:
   ```bash
   dotnet run
   ```

### Main Menu Options

- **1. Start Story Mission**: Generates a new AI-powered mission based on your current aircraft and location
- **2. Show Current Aircraft Info**: Displays detailed information about your current aircraft and position
- **3. Monitor Mission Progress**: Real-time monitoring of active mission objectives
- **4. Toggle Voice Mode**: Enable/disable voice command recognition
- **5. Test Voice**: Test text-to-speech and voice recognition functionality
- **6. Settings**: Display current configuration settings
- **7. Exit**: Close the application

### Voice Commands

When voice mode is enabled, you can use these voice commands:

- **"Start mission"** / **"New mission"** - Generate a new story mission
- **"Show status"** / **"Aircraft status"** - Display current aircraft information
- **"Monitor progress"** / **"Mission progress"** - Monitor active mission progress
- **"Help"** / **"Voice commands"** - Show available voice commands
- **"Settings"** - Display current settings
- **"Test voice"** - Test voice functionality
- **"Exit"** / **"Quit"** - Close the application

### Starting a Mission

1. Select option 1 from the main menu
2. FlightPig will automatically:
   - Retrieve your current aircraft information from MSFS
   - Send this data to Ollama for mission generation
   - Display the generated mission with objectives
3. Press 'S' to start the mission or any other key to return to the menu
4. Once started, the mission will be monitored in real-time

### Mission Progress Monitoring

- Progress is automatically calculated based on your aircraft's position and status
- Visual progress bars show completion percentage for each objective
- Objectives are marked as complete when criteria are met
- Press ESC during monitoring to return to the main menu

## Configuration

### Settings File

FlightPig uses a `settings.json` file for configuration. On first run, a default settings file will be created. You can edit this file to customize the application behavior.

#### ElevenLabs Setup (Text-to-Speech)

1. Sign up for an ElevenLabs account at https://elevenlabs.io/
2. Get your API key from your profile
3. Edit `settings.json` and replace `"YOUR_ELEVENLABS_API_KEY_HERE"` with your actual API key
4. Set `"enabled": true` in the elevenlabs section
5. Optionally customize voice IDs:
   - **defaultVoiceId**: "21m00Tcm4TlvDq8ikWAM" (Rachel - calm, professional)
   - **pilotVoiceId**: "29vD33N1CtxCmqQRPOHJ" (Drew - authoritative, clear)

#### Voice Recognition Setup

Voice recognition is enabled by default using Whisper. You can customize:
- **modelName**: Whisper model to use (default: "ggml-tiny.bin")
- **language**: Recognition language (default: "en")
- **recordingTimeoutSeconds**: Maximum recording time (default: 10)
- **enabled**: Enable/disable voice recognition

#### Ollama Settings

- **baseUrl**: Ollama server URL (default: "http://localhost:11434")
- **modelName**: AI model to use (default: "deepseek-r1:8b")
- **timeoutMinutes**: Model download timeout (default: 30)

### Mission Generation

The AI generates missions based on:
- Current aircraft type and capabilities
- Current location (latitude/longitude)
- Current altitude and flight status
- Whether the aircraft is on the ground or in flight

## Technical Details

### Architecture

- **Models**: Data structures for missions, objectives, and aircraft information
- **Services**: 
  - `OllamaService`: Handles AI communication and mission generation
  - `MissionProgressMonitor`: Tracks and evaluates mission progress
- **FsConnect Integration**: Real-time data from Microsoft Flight Simulator

### Objective Types and Completion Criteria

- **FlyOver**: Complete when within 0.5 nautical miles of target coordinates
- **LandAt**: Complete when within 0.5 nautical miles and on the ground
- **NavigateTo**: Complete when within 0.5 nautical miles of target
- **MaintainAltitude**: Complete when maintaining target altitude (±500 ft) for sufficient time
- **MaintainSpeed**: Complete when maintaining target speed (±10 knots) for sufficient time

## Troubleshooting

### Common Issues

1. **"Failed to connect to MSFS"**
   - Ensure Microsoft Flight Simulator is running
   - Make sure you're loaded into an aircraft (not in the main menu)
   - Check that SimConnect is enabled in MSFS settings

2. **"Failed to initialize Ollama"**
   - Verify Ollama is running: `ollama list`
   - Check if the DeepSeek-R1 model is available: `ollama pull deepseek-r1:8b`
   - Ensure Ollama is accessible at http://localhost:11434

3. **"No aircraft information available"**
   - Make sure you're in an active flight, not in the menu
   - Try requesting aircraft info again (option 2)
   - Restart MSFS if the issue persists

### Performance Tips

- Close unnecessary applications to ensure smooth MSFS performance
- The AI mission generation may take 10-30 seconds depending on your system
- Mission monitoring updates every second - this is normal

## Development

### Building from Source

```bash
git clone <repository-url>
cd FlightPig
dotnet restore
dotnet build
```

### Dependencies

- CTrue.FsConnect (upgraded to .NET 8.0)
- System.Text.Json
- Microsoft.Extensions.Http
- Microsoft.Extensions.DependencyInjection
- Whisper.net (speech recognition)
- NAudio (audio processing)
- Newtonsoft.Json (configuration)

## License

This project uses the FsConnect library by C-True, which is subject to its own license terms.

## Contributing

Feel free to submit issues, feature requests, or pull requests to improve FlightPig!
