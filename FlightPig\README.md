# FlightPig - AI-Powered Flight Mission Generator for MSFS

FlightPig is an AI-powered console application that generates dynamic flight missions for Microsoft Flight Simulator using Ollama and the DeepSeek-R1 model. It integrates with MSFS through the upgraded FsConnect library to provide real-time aircraft data and mission progress monitoring.

## Features

- **AI-Generated Missions**: Uses Ollama with DeepSeek-R1 (8B) to create realistic flight missions based on your current aircraft and location
- **Real-time Integration**: Connects to Microsoft Flight Simulator via SimConnect for live aircraft data
- **Mission Progress Monitoring**: Tracks objective completion in real-time with visual progress indicators
- **Multiple Objective Types**: Supports various mission objectives including:
  - FlyOver: Fly over a specific location
  - LandAt: Land at a specific airport or location
  - LandAtAndWait: Land and wait for a specified time
  - NavigateTo: Navigate to a specific waypoint
  - MaintainAltitude: Maintain a specific altitude for a period
  - MaintainSpeed: Maintain a specific airspeed
  - And more...

## Prerequisites

1. **Microsoft Flight Simulator 2020** - Must be running with an active flight
2. **Ollama** - Must be installed and running locally
   - Download from: https://ollama.ai/
   - Default URL: http://localhost:11434
3. **.NET 8.0 Runtime** - Required to run the application

## Installation

1. Clone or download the FlightPig project
2. Ensure all prerequisites are installed and running
3. Build the project:
   ```bash
   dotnet build
   ```

## Usage

1. **Start Microsoft Flight Simulator** and load into any aircraft at any location
2. **Start Ollama** (if not already running):
   ```bash
   ollama serve
   ```
3. **Run FlightPig**:
   ```bash
   dotnet run
   ```

### Main Menu Options

- **1. Start Story Mission**: Generates a new AI-powered mission based on your current aircraft and location
- **2. Show Current Aircraft Info**: Displays detailed information about your current aircraft and position
- **3. Monitor Mission Progress**: Real-time monitoring of active mission objectives
- **4. Exit**: Close the application

### Starting a Mission

1. Select option 1 from the main menu
2. FlightPig will automatically:
   - Retrieve your current aircraft information from MSFS
   - Send this data to Ollama for mission generation
   - Display the generated mission with objectives
3. Press 'S' to start the mission or any other key to return to the menu
4. Once started, the mission will be monitored in real-time

### Mission Progress Monitoring

- Progress is automatically calculated based on your aircraft's position and status
- Visual progress bars show completion percentage for each objective
- Objectives are marked as complete when criteria are met
- Press ESC during monitoring to return to the main menu

## Configuration

### Ollama Settings

The application uses these default settings:
- **Base URL**: http://localhost:11434
- **Model**: deepseek-r1:8b

To modify these settings, edit the `OllamaService` constructor in `Services/OllamaService.cs`.

### Mission Generation

The AI generates missions based on:
- Current aircraft type and capabilities
- Current location (latitude/longitude)
- Current altitude and flight status
- Whether the aircraft is on the ground or in flight

## Technical Details

### Architecture

- **Models**: Data structures for missions, objectives, and aircraft information
- **Services**: 
  - `OllamaService`: Handles AI communication and mission generation
  - `MissionProgressMonitor`: Tracks and evaluates mission progress
- **FsConnect Integration**: Real-time data from Microsoft Flight Simulator

### Objective Types and Completion Criteria

- **FlyOver**: Complete when within 0.5 nautical miles of target coordinates
- **LandAt**: Complete when within 0.5 nautical miles and on the ground
- **NavigateTo**: Complete when within 0.5 nautical miles of target
- **MaintainAltitude**: Complete when maintaining target altitude (±500 ft) for sufficient time
- **MaintainSpeed**: Complete when maintaining target speed (±10 knots) for sufficient time

## Troubleshooting

### Common Issues

1. **"Failed to connect to MSFS"**
   - Ensure Microsoft Flight Simulator is running
   - Make sure you're loaded into an aircraft (not in the main menu)
   - Check that SimConnect is enabled in MSFS settings

2. **"Failed to initialize Ollama"**
   - Verify Ollama is running: `ollama list`
   - Check if the DeepSeek-R1 model is available: `ollama pull deepseek-r1:8b`
   - Ensure Ollama is accessible at http://localhost:11434

3. **"No aircraft information available"**
   - Make sure you're in an active flight, not in the menu
   - Try requesting aircraft info again (option 2)
   - Restart MSFS if the issue persists

### Performance Tips

- Close unnecessary applications to ensure smooth MSFS performance
- The AI mission generation may take 10-30 seconds depending on your system
- Mission monitoring updates every second - this is normal

## Development

### Building from Source

```bash
git clone <repository-url>
cd FlightPig
dotnet restore
dotnet build
```

### Dependencies

- CTrue.FsConnect (upgraded to .NET 8.0)
- System.Text.Json
- Microsoft.Extensions.Http
- Microsoft.Extensions.DependencyInjection

## License

This project uses the FsConnect library by C-True, which is subject to its own license terms.

## Contributing

Feel free to submit issues, feature requests, or pull requests to improve FlightPig!
