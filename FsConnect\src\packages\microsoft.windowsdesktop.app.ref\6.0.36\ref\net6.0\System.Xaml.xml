﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Xaml</name>
  </assembly>
  <members>
    <member name="T:System.Windows.Markup.AcceptedMarkupExtensionExpressionTypeAttribute">
      <summary>Notates types for legacy reporting of XAML markup extension characteristics.</summary>
    </member>
    <member name="M:System.Windows.Markup.AcceptedMarkupExtensionExpressionTypeAttribute.#ctor(System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Markup.AcceptedMarkupExtensionExpressionTypeAttribute" /> class.</summary>
      <param name="type">The return type that this attribute reports.</param>
    </member>
    <member name="P:System.Windows.Markup.AcceptedMarkupExtensionExpressionTypeAttribute.Type">
      <summary>Gets or sets the return type that this attribute reports.</summary>
      <returns>The return type that this attribute reports.</returns>
    </member>
    <member name="T:System.Windows.Markup.AmbientAttribute">
      <summary>Specifies that a property or type should be treated as ambient. The ambient concept relates to how XAML processors determine type owners of members.</summary>
    </member>
    <member name="M:System.Windows.Markup.AmbientAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Markup.AmbientAttribute" /> class.</summary>
    </member>
    <member name="T:System.Windows.Markup.ArrayExtension">
      <summary>Implements <see langword="x:Array" /> support for .NET XAML Services.</summary>
    </member>
    <member name="M:System.Windows.Markup.ArrayExtension.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Markup.ArrayExtension" /> class. This creates an empty array.</summary>
    </member>
    <member name="M:System.Windows.Markup.ArrayExtension.#ctor(System.Array)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Markup.ArrayExtension" /> class based on the provided raw array.</summary>
      <param name="elements">The array content that populates the created array.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="elements" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Windows.Markup.ArrayExtension.#ctor(System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Markup.ArrayExtension" /> class and initializes the type of the array.</summary>
      <param name="arrayType">The object type of the new array.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="arrayType" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Windows.Markup.ArrayExtension.AddChild(System.Object)">
      <summary>Appends the supplied object to the end of the array.</summary>
      <param name="value">The object to add to the end of the array.</param>
    </member>
    <member name="M:System.Windows.Markup.ArrayExtension.AddText(System.String)">
      <summary>Adds a text node as a new array item.</summary>
      <param name="text">The text to add to the end of the array.</param>
    </member>
    <member name="M:System.Windows.Markup.ArrayExtension.ProvideValue(System.IServiceProvider)">
      <summary>Returns an array that is sized to the number of objects supplied in the <see cref="P:System.Windows.Markup.ArrayExtension.Items" /> values.</summary>
      <param name="serviceProvider">An object that can provide services for the markup extension.</param>
      <exception cref="T:System.InvalidOperationException">Processed an array that did not provide a valid <see cref="P:System.Windows.Markup.ArrayExtension.Type" />.

 -or-

 There is a type mismatch between the declared <see cref="P:System.Windows.Markup.ArrayExtension.Type" /> of the array and one or more of its <see cref="P:System.Windows.Markup.ArrayExtension.Items" /> values.</exception>
      <returns>The created array, or null.</returns>
    </member>
    <member name="P:System.Windows.Markup.ArrayExtension.Items">
      <summary>Gets the contents of the array. Settable in XAML through XAML collection syntax.</summary>
      <returns>The array contents.</returns>
    </member>
    <member name="P:System.Windows.Markup.ArrayExtension.Type">
      <summary>Gets or sets the type of array to be created when calling <see cref="M:System.Windows.Markup.ArrayExtension.ProvideValue(System.IServiceProvider)" />.</summary>
      <returns>The type of the array.</returns>
    </member>
    <member name="T:System.Windows.Markup.ConstructorArgumentAttribute">
      <summary>Specifies that an object can be initialized by using a non-parameterless constructor syntax, and that a property of the specified name supplies construction information.  This information is primarily for XAML serialization.</summary>
    </member>
    <member name="M:System.Windows.Markup.ConstructorArgumentAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Markup.ConstructorArgumentAttribute" /> class.</summary>
      <param name="argumentName">The name of the constructor that will initialize the associated property.</param>
    </member>
    <member name="P:System.Windows.Markup.ConstructorArgumentAttribute.ArgumentName">
      <summary>Gets the name parameter of the constructor that will initialize the associated property.</summary>
      <returns>The name of the constructor. Assuming CLR backing, this corresponds to the <see cref="P:System.Reflection.ParameterInfo.Name" /> of the relevant constructor parameter.</returns>
    </member>
    <member name="T:System.Windows.Markup.ContentPropertyAttribute">
      <summary>Indicates which property of a type is the XAML content property. A XAML processor uses this information when processing XAML child elements of XAML representations of the attributed type.</summary>
    </member>
    <member name="M:System.Windows.Markup.ContentPropertyAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Markup.ContentPropertyAttribute" /> class.</summary>
    </member>
    <member name="M:System.Windows.Markup.ContentPropertyAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Markup.ContentPropertyAttribute" /> class, by using the specified name.</summary>
      <param name="name">The property name for the property that is the content property.</param>
    </member>
    <member name="P:System.Windows.Markup.ContentPropertyAttribute.Name">
      <summary>Gets the name of the property that is the content property.</summary>
      <returns>The name of the property that is the content property.</returns>
    </member>
    <member name="T:System.Windows.Markup.ContentWrapperAttribute">
      <summary>Specifies one or more types on the associated collection type that will be used to wrap foreign content.</summary>
    </member>
    <member name="M:System.Windows.Markup.ContentWrapperAttribute.#ctor(System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Markup.ContentWrapperAttribute" /> class.</summary>
      <param name="contentWrapper">The <see cref="T:System.Type" /> that is declared as a content wrapper for the collection type.</param>
    </member>
    <member name="M:System.Windows.Markup.ContentWrapperAttribute.Equals(System.Object)">
      <summary>Determines whether the specified <see cref="T:System.Windows.Markup.ContentWrapperAttribute" /> is equivalent this <see cref="T:System.Windows.Markup.ContentWrapperAttribute" /> by comparing the <see cref="P:System.Windows.Markup.ContentWrapperAttribute.ContentWrapper" /> properties.</summary>
      <param name="obj">The <see cref="T:System.Windows.Markup.ContentWrapperAttribute" /> to compare.</param>
      <returns>
        <see langword="true" /> if the <see cref="P:System.Windows.Markup.ContentWrapperAttribute.ContentWrapper" /> properties are equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Windows.Markup.ContentWrapperAttribute.GetHashCode">
      <summary>Gets a hash code for this instance.</summary>
      <returns>An integer hash code.</returns>
    </member>
    <member name="P:System.Windows.Markup.ContentWrapperAttribute.ContentWrapper">
      <summary>Gets the type that is declared as a content wrapper for the collection type associated with this attribute.</summary>
      <returns>The type that is declared as a content wrapper for the collection type.</returns>
    </member>
    <member name="P:System.Windows.Markup.ContentWrapperAttribute.TypeId">
      <summary>Gets a unique identifier for this attribute.</summary>
      <returns>A unique identifier for the attribute.</returns>
    </member>
    <member name="T:System.Windows.Markup.DateTimeValueSerializer">
      <summary>Converts instances of <see cref="T:System.String" /> to and from instances of <see cref="T:System.DateTime" />.</summary>
    </member>
    <member name="M:System.Windows.Markup.DateTimeValueSerializer.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Markup.DateTimeValueSerializer" /> class.</summary>
    </member>
    <member name="M:System.Windows.Markup.DateTimeValueSerializer.CanConvertFromString(System.String,System.Windows.Markup.IValueSerializerContext)">
      <summary>Determines if the specified <see cref="T:System.String" /> can be convert to an instance of <see cref="T:System.DateTime" />.</summary>
      <param name="value">The string to evaluate for conversion.</param>
      <param name="context">Context information that is used for conversion.</param>
      <returns>
        <see langword="true" /> if the value can be converted; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Windows.Markup.DateTimeValueSerializer.CanConvertToString(System.Object,System.Windows.Markup.IValueSerializerContext)">
      <summary>Determines if the specified object can be converted to a <see cref="T:System.String" />.</summary>
      <param name="value">The object to evaluate for conversion.</param>
      <param name="context">Context information that is used for conversion.</param>
      <returns>
        <see langword="true" /> if <paramref name="value" /> can be converted into a <see cref="T:System.String" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Windows.Markup.DateTimeValueSerializer.ConvertFromString(System.String,System.Windows.Markup.IValueSerializerContext)">
      <summary>Converts a <see cref="T:System.String" /> into a <see cref="T:System.DateTime" />.</summary>
      <param name="value">The string to convert into a <see cref="T:System.DateTime" />.</param>
      <param name="context">Context information that is used for conversion.</param>
      <exception cref="T:System.NotSupportedException">
        <paramref name="value" /> is <see langword="null" />.</exception>
      <returns>A new instance of <see cref="T:System.DateTime" /> based on the supplied <paramref name="value" />.</returns>
    </member>
    <member name="M:System.Windows.Markup.DateTimeValueSerializer.ConvertToString(System.Object,System.Windows.Markup.IValueSerializerContext)">
      <summary>Converts an instance of <see cref="T:System.DateTime" /> to a <see cref="T:System.String" />.</summary>
      <param name="value">The object to convert into a string.</param>
      <param name="context">Context information that is used for conversion.</param>
      <exception cref="T:System.NotSupportedException">
        <paramref name="value" /> is not a <see cref="T:System.DateTime" /> or is <see langword="null" />.</exception>
      <returns>A string representation of the specified <see cref="T:System.DateTime" />.</returns>
    </member>
    <member name="T:System.Windows.Markup.DependsOnAttribute">
      <summary>Indicates that the attributed property is dependent on the value of another property.</summary>
    </member>
    <member name="M:System.Windows.Markup.DependsOnAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Markup.DependsOnAttribute" /> class.</summary>
      <param name="name">The property that the property associated with this <see cref="T:System.Windows.Markup.DependsOnAttribute" /> depends on.</param>
    </member>
    <member name="P:System.Windows.Markup.DependsOnAttribute.Name">
      <summary>Gets the name of the related property declared in this <see cref="T:System.Windows.Markup.DependsOnAttribute" />.</summary>
      <returns>The name of the related property.</returns>
    </member>
    <member name="P:System.Windows.Markup.DependsOnAttribute.TypeId">
      <summary>Gets a unique identifier for this <see cref="T:System.Windows.Markup.DependsOnAttribute" />.</summary>
      <returns>The unique identifier.</returns>
    </member>
    <member name="T:System.Windows.Markup.DictionaryKeyPropertyAttribute">
      <summary>Specifies a property of the associated class that provides the implicit key value. Implicit keys are used for keys rather than explicit <see langword="x:Key" /> attributes defined in XAML for an item in <see cref="T:System.Collections.IDictionary" /> collections.</summary>
    </member>
    <member name="M:System.Windows.Markup.DictionaryKeyPropertyAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Markup.DictionaryKeyPropertyAttribute" /> class.</summary>
      <param name="name">The name of the property that provides the implicit key value.</param>
    </member>
    <member name="P:System.Windows.Markup.DictionaryKeyPropertyAttribute.Name">
      <summary>Gets the name of the property that provides the implicit key value.</summary>
      <returns>The name of the property that provides the implicit key value.</returns>
    </member>
    <member name="T:System.Windows.Markup.IComponentConnector">
      <summary>Provides markup compile and tools support for named XAML elements and for attaching event handlers to them.</summary>
    </member>
    <member name="M:System.Windows.Markup.IComponentConnector.Connect(System.Int32,System.Object)">
      <summary>Attaches events and names to compiled content.</summary>
      <param name="connectionId">An identifier token to distinguish calls.</param>
      <param name="target">The target to connect events and names to.</param>
    </member>
    <member name="M:System.Windows.Markup.IComponentConnector.InitializeComponent">
      <summary>Loads the compiled page of a component.</summary>
    </member>
    <member name="T:System.Windows.Markup.INameScope">
      <summary>Defines a contract for how names of elements should be accessed within a particular XAML namescope, and how to enforce uniqueness of names within that XAML namescope.</summary>
    </member>
    <member name="M:System.Windows.Markup.INameScope.FindName(System.String)">
      <summary>Returns an object that has the provided identifying name.</summary>
      <param name="name">The name identifier for the object being requested.</param>
      <returns>The object, if found. Returns <see langword="null" /> if no object of that name was found.</returns>
    </member>
    <member name="M:System.Windows.Markup.INameScope.RegisterName(System.String,System.Object)">
      <summary>Registers the provided name into the current XAML namescope.</summary>
      <param name="name">The name to register.</param>
      <param name="scopedElement">The specific element that the provided <paramref name="name" /> refers to.</param>
    </member>
    <member name="M:System.Windows.Markup.INameScope.UnregisterName(System.String)">
      <summary>Unregisters the provided name from the current XAML namescope.</summary>
      <param name="name">The name to unregister.</param>
    </member>
    <member name="T:System.Windows.Markup.INameScopeDictionary">
      <summary>Unifies enumerable, collection, and dictionary support that are useful for exposing a dictionary of names in a XAML namescope.</summary>
    </member>
    <member name="T:System.Windows.Markup.IProvideValueTarget">
      <summary>Represents a service that reports situational object-property relationships for markup extension evaluation.</summary>
    </member>
    <member name="P:System.Windows.Markup.IProvideValueTarget.TargetObject">
      <summary>Gets the target object being reported.</summary>
      <returns>The target object being reported.</returns>
    </member>
    <member name="P:System.Windows.Markup.IProvideValueTarget.TargetProperty">
      <summary>Gets an identifier for the target property being reported.</summary>
      <returns>An identifier for the target property being reported.</returns>
    </member>
    <member name="T:System.Windows.Markup.IQueryAmbient">
      <summary>Queries for whether a specified property should be treated as ambient in the current scope.</summary>
    </member>
    <member name="M:System.Windows.Markup.IQueryAmbient.IsAmbientPropertyAvailable(System.String)">
      <summary>Queries for whether a specified named property can be considered ambient in the current scope.</summary>
      <param name="propertyName">The name of the property to check for ambience state.</param>
      <returns>
        <see langword="true" /> if the requested property can be considered ambient; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Windows.Markup.IUriContext">
      <summary>Represents a service that can use application context to resolve a provided relative URI to an absolute URI.</summary>
    </member>
    <member name="P:System.Windows.Markup.IUriContext.BaseUri">
      <summary>Gets or sets the base URI of the current application context.</summary>
      <returns>The base URI of the application context.</returns>
    </member>
    <member name="T:System.Windows.Markup.IValueSerializerContext">
      <summary>Defines a context that is provided to a <see cref="T:System.Windows.Markup.ValueSerializer" />. The context can be used to enable special cases of serialization or different modes of serialization.</summary>
    </member>
    <member name="M:System.Windows.Markup.IValueSerializerContext.GetValueSerializerFor(System.ComponentModel.PropertyDescriptor)">
      <summary>Gets a <see cref="T:System.Windows.Markup.ValueSerializer" /> for the given property descriptor.</summary>
      <param name="descriptor">The descriptor of the property being converted.</param>
      <returns>A <see cref="T:System.Windows.Markup.ValueSerializer" /> capable of serializing the specified property.</returns>
    </member>
    <member name="M:System.Windows.Markup.IValueSerializerContext.GetValueSerializerFor(System.Type)">
      <summary>Gets the <see cref="T:System.Windows.Markup.ValueSerializer" /> associated with the specified type.</summary>
      <param name="type">The type of the value being converted.</param>
      <returns>A <see cref="T:System.Windows.Markup.ValueSerializer" /> capable of serializing the specified type.</returns>
    </member>
    <member name="T:System.Windows.Markup.IXamlTypeResolver">
      <summary>Represents a service that resolves from named elements in XAML markup to the appropriate CLR type.</summary>
    </member>
    <member name="M:System.Windows.Markup.IXamlTypeResolver.Resolve(System.String)">
      <summary>Resolves a named XAML type to the corresponding CLR <see cref="T:System.Type" />.</summary>
      <param name="qualifiedTypeName">The XAML type name to resolve. The type name is optionally qualified by the prefix for a XML namespace. Otherwise the current default XML namespace is assumed.</param>
      <returns>The <see cref="T:System.Type" /> that <paramref name="qualifiedTypeName" /> resolves to.</returns>
    </member>
    <member name="T:System.Windows.Markup.MarkupExtension">
      <summary>Provides a base class for XAML markup extension implementations that can be supported by .NET XAML Services and other XAML readers and XAML writers.</summary>
    </member>
    <member name="M:System.Windows.Markup.MarkupExtension.#ctor">
      <summary>Initializes a new instance of a class derived from <see cref="T:System.Windows.Markup.MarkupExtension" />.</summary>
    </member>
    <member name="M:System.Windows.Markup.MarkupExtension.ProvideValue(System.IServiceProvider)">
      <summary>When implemented in a derived class, returns an object that is provided as the value of the target property for this markup extension.</summary>
      <param name="serviceProvider">A service provider helper that can provide services for the markup extension.</param>
      <returns>The object value to set on the property where the extension is applied.</returns>
    </member>
    <member name="T:System.Windows.Markup.MarkupExtensionBracketCharactersAttribute">
      <summary>Reports the bracket characters that a markup extension can return.</summary>
    </member>
    <member name="M:System.Windows.Markup.MarkupExtensionBracketCharactersAttribute.#ctor(System.Char,System.Char)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Markup.MarkupExtensionBracketCharactersAttribute" /> class using the provided characters.</summary>
      <param name="openingBracket">A <see cref="T:System.Char" /> that defines the opening bracket character.</param>
      <param name="closingBracket">A <see cref="T:System.Char" /> that defines the closing bracket character.</param>
    </member>
    <member name="P:System.Windows.Markup.MarkupExtensionBracketCharactersAttribute.ClosingBracket">
      <summary>Specifies the character to be used as the closing delimiter in a <see cref="T:System.Windows.Markup.MarkupExtension" />.</summary>
      <returns>A <see cref="T:System.Char" /> representing the character.</returns>
    </member>
    <member name="P:System.Windows.Markup.MarkupExtensionBracketCharactersAttribute.OpeningBracket">
      <summary>Specifies the character to be used as the opening delimiter in a <see cref="T:System.Windows.Markup.MarkupExtension" />.</summary>
      <returns>A <see cref="T:System.Char" /> representing the character.</returns>
    </member>
    <member name="T:System.Windows.Markup.MarkupExtensionReturnTypeAttribute">
      <summary>Reports the type that a markup extension can return.</summary>
    </member>
    <member name="M:System.Windows.Markup.MarkupExtensionReturnTypeAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Markup.MarkupExtensionReturnTypeAttribute" /> class.</summary>
    </member>
    <member name="M:System.Windows.Markup.MarkupExtensionReturnTypeAttribute.#ctor(System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Markup.MarkupExtensionReturnTypeAttribute" /> class using the provided <see cref="T:System.Type" />.</summary>
      <param name="returnType">The return type that this attribute reports.</param>
    </member>
    <member name="M:System.Windows.Markup.MarkupExtensionReturnTypeAttribute.#ctor(System.Type,System.Type)">
      <summary>Deprecated; do not use.</summary>
      <param name="returnType">The return type that this  .NET Framework attribute reports.</param>
      <param name="expressionType">Deprecated; do not use.</param>
    </member>
    <member name="P:System.Windows.Markup.MarkupExtensionReturnTypeAttribute.ExpressionType">
      <summary>Deprecated; do not use.</summary>
      <returns>Deprecated; do not use.</returns>
    </member>
    <member name="P:System.Windows.Markup.MarkupExtensionReturnTypeAttribute.ReturnType">
      <summary>Gets the <see cref="T:System.Windows.Markup.MarkupExtension" /> return type that this .NET Framework attribute reports.</summary>
      <returns>The type-safe return type of the specific <see cref="M:System.Windows.Markup.MarkupExtension.ProvideValue(System.IServiceProvider)" /> implementation of the markup extension where the <see cref="T:System.Windows.Markup.MarkupExtensionReturnTypeAttribute" /> .NET Framework attribute is applied.</returns>
    </member>
    <member name="T:System.Windows.Markup.MemberDefinition">
      <summary>Provides the base class that is used for a markup technique of defining members of a class in declarative XAML.</summary>
    </member>
    <member name="M:System.Windows.Markup.MemberDefinition.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Markup.MemberDefinition" /> class.</summary>
    </member>
    <member name="P:System.Windows.Markup.MemberDefinition.Name">
      <summary>When implemented in a derived class, gets or sets the name of the member to define.</summary>
      <returns>The name of the member to define.</returns>
    </member>
    <member name="T:System.Windows.Markup.NameReferenceConverter">
      <summary>Provides type conversion to convert a string name into an object reference to the object with that name, or to return the name of an object from the object graph.</summary>
    </member>
    <member name="M:System.Windows.Markup.NameReferenceConverter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Markup.NameReferenceConverter" /> class.</summary>
    </member>
    <member name="M:System.Windows.Markup.NameReferenceConverter.CanConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Type)">
      <summary>Returns whether this converter can convert an object of one type to another object.</summary>
      <param name="context">An <see cref="T:System.ComponentModel.ITypeDescriptorContext" /> that provides a format context.</param>
      <param name="sourceType">A <see cref="T:System.Type" /> that represents the type you want to convert from.</param>
      <returns>
        <see langword="true" /> if this converter can perform the conversion; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Windows.Markup.NameReferenceConverter.CanConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Type)">
      <summary>Returns a value that indicates whether the converter can convert an object to the specified destination type.</summary>
      <param name="context">An <see cref="T:System.ComponentModel.ITypeDescriptorContext" /> that provides a format context.</param>
      <param name="destinationType">The type to convert to.</param>
      <returns>
        <see langword="true" /> if the converter can perform the conversion; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Windows.Markup.NameReferenceConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)">
      <summary>Converts the provided object to another object, using the specified context and culture information.</summary>
      <param name="context">An <see cref="T:System.ComponentModel.ITypeDescriptorContext" /> that provides a format context.</param>
      <param name="culture">The <see cref="T:System.Globalization.CultureInfo" /> to use as the current culture.</param>
      <param name="value">The reference name string to convert.</param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="value" /> is a null string or empty string.  
  
 -or-  
  
 <see cref="T:System.Xaml.IXamlNameResolver" /> service is missing or invalid.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="context" /> is <see langword="null" />.</exception>
      <returns>The returned object, which is potentially any object that is type-mapped in the relevant backing assemblies and capable of being declared in XAML with a XAML name reference.</returns>
    </member>
    <member name="M:System.Windows.Markup.NameReferenceConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)">
      <summary>Converts an object to the specified type. This is intended to return XAML reference names for objects in an object graph.</summary>
      <param name="context">An <see cref="T:System.ComponentModel.ITypeDescriptorContext" /> that provides a format context.</param>
      <param name="culture">The <see cref="T:System.Globalization.CultureInfo" /> to use as the current culture.</param>
      <param name="value">The object to retrieve the reference name for.</param>
      <param name="destinationType">The type to return. You should always reference the <see cref="T:System.String" /> type.</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Xaml.IXamlNameProvider" /> service is missing or invalid.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="context" /> is <see langword="null" />.</exception>
      <returns>The reference name of the input <paramref name="value" /> object.</returns>
    </member>
    <member name="T:System.Windows.Markup.NameScopePropertyAttribute">
      <summary>Specifies a property of the associated class that provides the XAML namescope value.</summary>
    </member>
    <member name="M:System.Windows.Markup.NameScopePropertyAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Markup.NameScopePropertyAttribute" /> class with the specified name.</summary>
      <param name="name">The name of the property on the attributed type that provides the XAML namescope.</param>
    </member>
    <member name="M:System.Windows.Markup.NameScopePropertyAttribute.#ctor(System.String,System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Markup.NameScopePropertyAttribute" /> class with the specified name and type.</summary>
      <param name="name">The name of the attachable member that provides the XAML name scope.</param>
      <param name="type">The owner type of the attachable member that provides the XAML name scope.</param>
    </member>
    <member name="P:System.Windows.Markup.NameScopePropertyAttribute.Name">
      <summary>Gets the name of the property that provides the XAML namescope.</summary>
      <returns>A string value that is the name of the property that provides the XAML namescope.</returns>
    </member>
    <member name="P:System.Windows.Markup.NameScopePropertyAttribute.Type">
      <summary>Gets the owner type of the attached property that provides the XAML namescope support.</summary>
      <returns>A <see cref="T:System.Type" /> value that is the owner type of the attached property that provides the XAML namescope support, or <see langword="null" />.</returns>
    </member>
    <member name="T:System.Windows.Markup.NullExtension">
      <summary>Implements a XAML markup extension in order to return a null object, which you can use to explicitly set values to null in XAML.</summary>
    </member>
    <member name="M:System.Windows.Markup.NullExtension.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Markup.NullExtension" /> class.</summary>
    </member>
    <member name="M:System.Windows.Markup.NullExtension.ProvideValue(System.IServiceProvider)">
      <summary>Provides <see langword="null" /> to use as a value as the output of this markup extension.</summary>
      <param name="serviceProvider">An object that can provide services for the markup extension implementation.</param>
      <returns>A null reference.</returns>
    </member>
    <member name="T:System.Windows.Markup.PropertyDefinition">
      <summary>Supports a markup technique of defining properties of a class in declarative XAML.</summary>
    </member>
    <member name="M:System.Windows.Markup.PropertyDefinition.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Markup.PropertyDefinition" /> class.</summary>
    </member>
    <member name="P:System.Windows.Markup.PropertyDefinition.Attributes">
      <summary>Gets a list of associated CLR attributes.</summary>
      <returns>A list of associated attributes.</returns>
    </member>
    <member name="P:System.Windows.Markup.PropertyDefinition.Modifier">
      <summary>Gets or sets the access level of the defined property.</summary>
      <returns>The access level of the defined property, as a string.</returns>
    </member>
    <member name="P:System.Windows.Markup.PropertyDefinition.Name">
      <summary>Gets or sets the name of the property to define.</summary>
      <returns>The name of the property to define.</returns>
    </member>
    <member name="P:System.Windows.Markup.PropertyDefinition.Type">
      <summary>Gets or sets the <see cref="T:System.Xaml.XamlType" /> of the property to define.</summary>
      <returns>The XAML type identifier of the property to define.</returns>
    </member>
    <member name="T:System.Windows.Markup.Reference">
      <summary>Implements the <c>{x:Reference}</c> markup extension.</summary>
    </member>
    <member name="M:System.Windows.Markup.Reference.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Markup.Reference" /> class.</summary>
    </member>
    <member name="M:System.Windows.Markup.Reference.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Markup.Reference" /> class with the <paramref name="name" /> argument.</summary>
      <param name="name">The XAML name of the element to reference.</param>
    </member>
    <member name="M:System.Windows.Markup.Reference.ProvideValue(System.IServiceProvider)">
      <summary>Returns an object that is the value of the target property. For the <see cref="T:System.Windows.Markup.Reference" /> type, this is the object that the provided <see cref="P:System.Windows.Markup.Reference.Name" /> references.</summary>
      <param name="serviceProvider">A class that implements the <see cref="T:System.Xaml.IXamlNameResolver" /> service.</param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="serviceProvider" /> value does not implement the <see cref="T:System.Xaml.IXamlNameResolver" /> service.

 -or-

 <see cref="P:System.Windows.Markup.Reference.Name" /> value has not been set through construction or positional usage.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="serviceProvider" /> is <see langword="null" />.</exception>
      <returns>The value of the target property. This is potentially any object that is type-mapped in the relevant backing assemblies.</returns>
    </member>
    <member name="P:System.Windows.Markup.Reference.Name">
      <summary>Gets or sets the XAML name to obtain the reference for.</summary>
      <returns>The XAML name of the element to obtain the reference for.</returns>
    </member>
    <member name="T:System.Windows.Markup.RootNamespaceAttribute">
      <summary>Represents an assembly level attribute that is used to identify the value of the <see langword="RootNamespace" /> property in a Visual Studio project file.</summary>
    </member>
    <member name="M:System.Windows.Markup.RootNamespaceAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Markup.RootNamespaceAttribute" /> class.</summary>
      <param name="nameSpace">The root namespace value.</param>
    </member>
    <member name="P:System.Windows.Markup.RootNamespaceAttribute.Namespace">
      <summary>Gets the string that corresponds to the value of the <see langword="RootNamespace" /> property in a Visual Studio project file.</summary>
      <returns>The string that corresponds to the value of the <see langword="RootNamespace" /> property in a Visual Studio project file.</returns>
    </member>
    <member name="T:System.Windows.Markup.RuntimeNamePropertyAttribute">
      <summary>Represents a type-level attribute that reports which property of the type maps to the XAML x:Name attribute.</summary>
    </member>
    <member name="M:System.Windows.Markup.RuntimeNamePropertyAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Markup.RuntimeNamePropertyAttribute" /> class.</summary>
      <param name="name">The name of the property to use as the <see langword="x:Name" /> equivalent of the class.</param>
    </member>
    <member name="P:System.Windows.Markup.RuntimeNamePropertyAttribute.Name">
      <summary>Gets the name of the runtime name property that is specified by this <see cref="T:System.Windows.Markup.RuntimeNamePropertyAttribute" />.</summary>
      <returns>The name of the property.</returns>
    </member>
    <member name="T:System.Windows.Markup.StaticExtension">
      <summary>Implements a markup extension that returns static field and property references.</summary>
    </member>
    <member name="M:System.Windows.Markup.StaticExtension.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Markup.StaticExtension" /> class.</summary>
    </member>
    <member name="M:System.Windows.Markup.StaticExtension.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Markup.StaticExtension" /> class using the provided <paramref name="member" /> string.</summary>
      <param name="member">A string that identifies the member to make a reference to. This string uses the format <c>prefix:typeName.fieldOrPropertyName</c>. <c>prefix</c> is the mapping prefix for a XAML namespace, and is only required to reference static values that are not mapped to the default XAML namespace.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="member" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Windows.Markup.StaticExtension.ProvideValue(System.IServiceProvider)">
      <summary>Returns an object value to set on the property where you apply this extension. For <see cref="T:System.Windows.Markup.StaticExtension" />, the return value is the static value that is evaluated for the requested static member.</summary>
      <param name="serviceProvider">An object that can provide services for the markup extension. The service provider is expected to provide a service that implements a type resolver (<see cref="T:System.Windows.Markup.IXamlTypeResolver" />).</param>
      <exception cref="T:System.InvalidOperationException">The <paramref name="member" /> value for the extension is <see langword="null" /> at the time of evaluation.</exception>
      <exception cref="T:System.ArgumentException">Some part of the <paramref name="member" /> string did not parse properly  
  
 -or-  
  
 <paramref name="serviceProvider" /> did not provide a service for <see cref="T:System.Windows.Markup.IXamlTypeResolver" />  
  
 -or-  
  
 <paramref name="member" /> value did not resolve to a static member.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="serviceProvider" /> is <see langword="null" />.</exception>
      <returns>The static value to set on the property where the extension is applied.</returns>
    </member>
    <member name="P:System.Windows.Markup.StaticExtension.Member">
      <summary>Gets or sets a member name string that is used to resolve a static field or property based on the service-provided type resolver.</summary>
      <exception cref="T:System.ArgumentNullException">Attempted to set <see cref="P:System.Windows.Markup.StaticExtension.Member" /> to <see langword="null" />.</exception>
      <returns>A string that identifies the member to make a reference to.</returns>
    </member>
    <member name="P:System.Windows.Markup.StaticExtension.MemberType">
      <summary>Gets or sets the <see cref="T:System.Type" /> that defines the static member to return.</summary>
      <exception cref="T:System.ArgumentNullException">Attempted to set <see cref="P:System.Windows.Markup.StaticExtension.MemberType" /> to <see langword="null" />.</exception>
      <returns>The <see cref="T:System.Type" /> that defines the static member to return.</returns>
    </member>
    <member name="T:System.Windows.Markup.TrimSurroundingWhitespaceAttribute">
      <summary>Indicates to XAML processors that the whitespace surrounding elements of the type in markup should be trimmed when serializing.</summary>
    </member>
    <member name="M:System.Windows.Markup.TrimSurroundingWhitespaceAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Markup.TrimSurroundingWhitespaceAttribute" /> class.</summary>
    </member>
    <member name="T:System.Windows.Markup.TypeExtension">
      <summary>Implements a markup extension that returns a <see cref="T:System.Type" /> based on a string input.</summary>
    </member>
    <member name="M:System.Windows.Markup.TypeExtension.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Markup.TypeExtension" /> class.</summary>
    </member>
    <member name="M:System.Windows.Markup.TypeExtension.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Markup.TypeExtension" /> class, initializing the <see cref="P:System.Windows.Markup.TypeExtension.TypeName" /> value based on the provided <paramref name="typeName" /> string.</summary>
      <param name="typeName">A string that identifies the type to make a reference to. This string uses the format <c>prefix:className</c>. <c>prefix</c> is the mapping prefix for a XAML namespace, and is only required to reference types that are not mapped to the default XAML namespace.</param>
      <exception cref="T:System.ArgumentNullException">Attempted to specify <paramref name="typeName" /> as <see langword="null" />.</exception>
    </member>
    <member name="M:System.Windows.Markup.TypeExtension.#ctor(System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Markup.TypeExtension" /> class, declaring the type directly.</summary>
      <param name="type">The type to be represented by this <see cref="T:System.Windows.Markup.TypeExtension" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="type" /> is <see langword="null" /></exception>
    </member>
    <member name="M:System.Windows.Markup.TypeExtension.ProvideValue(System.IServiceProvider)">
      <summary>Returns an object that should be set on the property where this extension is applied. For <see cref="T:System.Windows.Markup.TypeExtension" /> , this is the <see cref="T:System.Type" /> value as evaluated for the requested type name.</summary>
      <param name="serviceProvider">Object that can provide services for the markup extension. The provider is expected to provide a service for <see cref="T:System.Windows.Markup.IXamlTypeResolver" />.</param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="member" /> value for the extension is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">Some part of the <paramref name="typeName" /> string did not parse properly.  
  
 -or-  
  
 <paramref name="serviceProvider" /> did not provide a service for <see cref="T:System.Windows.Markup.IXamlTypeResolver" />  
  
 -or-  
  
 <paramref name="typeName" /> value did not resolve to a type.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="serviceProvider" /> is <see langword="null" /></exception>
      <returns>The <see cref="T:System.Type" /> to set on the property where the extension is applied.</returns>
    </member>
    <member name="P:System.Windows.Markup.TypeExtension.Type">
      <summary>Gets or sets the type information for this extension.</summary>
      <exception cref="T:System.ArgumentNullException">Attempted to set to <see langword="null" />.</exception>
      <returns>The established type. For runtime purposes, this may be <see langword="null" /> for get access, but cannot be set to <see langword="null" />.</returns>
    </member>
    <member name="P:System.Windows.Markup.TypeExtension.TypeName">
      <summary>Gets or sets the type name represented by this markup extension.</summary>
      <exception cref="T:System.ArgumentNullException">Attempted to set to <see langword="null" />.</exception>
      <returns>A string that identifies the type. This string uses the format prefix<c>:</c>className. (prefix is the mapping prefix for an XML namespace and is only required to reference types that are not mapped to the default XML namespace for WPF (<c>http://schemas.microsoft.com/winfx/2006/xaml/presentation</c>).</returns>
    </member>
    <member name="T:System.Windows.Markup.UidPropertyAttribute">
      <summary>Indicates the CLR property of a class that provides the x:Uid Directive value.</summary>
    </member>
    <member name="M:System.Windows.Markup.UidPropertyAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Markup.UidPropertyAttribute" /> class.</summary>
      <param name="name">The name of the property that provides the <see langword="x:Uid" /> value.</param>
    </member>
    <member name="P:System.Windows.Markup.UidPropertyAttribute.Name">
      <summary>Gets the name of the CLR property that represents the x:Uid Directive value.</summary>
      <returns>The name of the CLR property that represents x:Uid Directive.</returns>
    </member>
    <member name="T:System.Windows.Markup.UsableDuringInitializationAttribute">
      <summary>Indicates whether this type is built top-down during XAML object graph creation.</summary>
    </member>
    <member name="M:System.Windows.Markup.UsableDuringInitializationAttribute.#ctor(System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Markup.UsableDuringInitializationAttribute" /> class.</summary>
      <param name="usable">Defines whether the associated class is usable during initialization.</param>
    </member>
    <member name="P:System.Windows.Markup.UsableDuringInitializationAttribute.Usable">
      <summary>Gets a value that indicates whether the associated class is usable during initialization.</summary>
      <returns>
        <see langword="true" /> if the associated class is usable during initialization; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Windows.Markup.ValueSerializer">
      <summary>Abstract class that defines conversion behavior for serialization from an object representation.</summary>
    </member>
    <member name="M:System.Windows.Markup.ValueSerializer.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Markup.ValueSerializer" /> class.</summary>
    </member>
    <member name="M:System.Windows.Markup.ValueSerializer.CanConvertFromString(System.String,System.Windows.Markup.IValueSerializerContext)">
      <summary>When overridden in a derived class, determines whether the specified <see cref="T:System.String" /> can be converted to an instance of the type that the implementation of <see cref="T:System.Windows.Markup.ValueSerializer" /> supports.</summary>
      <param name="value">The string to evaluate for conversion.</param>
      <param name="context">Context information that is used for conversion.</param>
      <returns>
        <see langword="true" /> if the value can be converted; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Windows.Markup.ValueSerializer.CanConvertToString(System.Object,System.Windows.Markup.IValueSerializerContext)">
      <summary>When overridden in a derived class, determines whether the specified object can be converted into a <see cref="T:System.String" />.</summary>
      <param name="value">The object to evaluate for conversion.</param>
      <param name="context">Context information that is used for conversion.</param>
      <returns>
        <see langword="true" /> if the <paramref name="value" /> can be converted into a <see cref="T:System.String" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Windows.Markup.ValueSerializer.ConvertFromString(System.String,System.Windows.Markup.IValueSerializerContext)">
      <summary>When overridden in a derived class, converts a <see cref="T:System.String" /> to an instance of the type that the implementation of <see cref="T:System.Windows.Markup.ValueSerializer" /> supports.</summary>
      <param name="value">The string to convert.</param>
      <param name="context">Context information that is used for conversion.</param>
      <exception cref="T:System.NotSupportedException">
        <paramref name="value" /> cannot be converted.</exception>
      <returns>A new instance of the type that the implementation of <see cref="T:System.Windows.Markup.ValueSerializer" /> supports based on the supplied <paramref name="value" />.</returns>
    </member>
    <member name="M:System.Windows.Markup.ValueSerializer.ConvertToString(System.Object,System.Windows.Markup.IValueSerializerContext)">
      <summary>When overridden in a derived class, converts the specified object to a <see cref="T:System.String" />.</summary>
      <param name="value">The object to convert into a string.</param>
      <param name="context">Context information that is used for conversion.</param>
      <exception cref="T:System.NotSupportedException">
        <paramref name="value" /> cannot be converted.</exception>
      <returns>A string representation of the specified object.</returns>
    </member>
    <member name="M:System.Windows.Markup.ValueSerializer.GetConvertFromException(System.Object)">
      <summary>Returns an exception to throw when a conversion cannot be performed.</summary>
      <param name="value">The object that could not be converted.</param>
      <returns>An <see cref="T:System.Exception" /> object for the exception to throw when a <see langword="ConvertFrom" /> conversion cannot be performed.</returns>
    </member>
    <member name="M:System.Windows.Markup.ValueSerializer.GetConvertToException(System.Object,System.Type)">
      <summary>Returns an exception to throw when a conversion cannot be performed.</summary>
      <param name="value">The object that could not be converted.</param>
      <param name="destinationType">A type that represents the type the conversion was trying to convert to.</param>
      <returns>An <see cref="T:System.Exception" /> object for the exception to throw when a <see langword="ConvertTo" /> conversion cannot be performed.</returns>
    </member>
    <member name="M:System.Windows.Markup.ValueSerializer.GetSerializerFor(System.ComponentModel.PropertyDescriptor)">
      <summary>Gets the <see cref="T:System.Windows.Markup.ValueSerializer" /> declared for a property, by passing a CLR property descriptor for the property.</summary>
      <param name="descriptor">The CLR property descriptor for the property to be serialized.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="descriptor" /> is <see langword="null" />.</exception>
      <returns>The serializer associated with the specified property. May return <see langword="null" />.</returns>
    </member>
    <member name="M:System.Windows.Markup.ValueSerializer.GetSerializerFor(System.ComponentModel.PropertyDescriptor,System.Windows.Markup.IValueSerializerContext)">
      <summary>Gets the <see cref="T:System.Windows.Markup.ValueSerializer" /> declared for the specified property, using the specified context.</summary>
      <param name="descriptor">Descriptor for the property to be serialized.</param>
      <param name="context">Context information that is used for conversion.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="descriptor" /> is <see langword="null" />.</exception>
      <returns>The serializer associated with the specified property.</returns>
    </member>
    <member name="M:System.Windows.Markup.ValueSerializer.GetSerializerFor(System.Type)">
      <summary>Gets the <see cref="T:System.Windows.Markup.ValueSerializer" /> declared for the specified type.</summary>
      <param name="type">The type to get the <see cref="T:System.Windows.Markup.ValueSerializer" /> for.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="type" /> is <see langword="null" />.</exception>
      <returns>The serializer associated with the specified type. May return <see langword="null" />.</returns>
    </member>
    <member name="M:System.Windows.Markup.ValueSerializer.GetSerializerFor(System.Type,System.Windows.Markup.IValueSerializerContext)">
      <summary>Gets the <see cref="T:System.Windows.Markup.ValueSerializer" /> declared for the specified type, using the specified context.</summary>
      <param name="type">The type to get the <see cref="T:System.Windows.Markup.ValueSerializer" /> for.</param>
      <param name="context">Context information that is used for conversion.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="type" /> is <see langword="null" />.</exception>
      <returns>The serializer associated with the specified type.</returns>
    </member>
    <member name="M:System.Windows.Markup.ValueSerializer.TypeReferences(System.Object,System.Windows.Markup.IValueSerializerContext)">
      <summary>Gets an enumeration of the types referenced by the <see cref="T:System.Windows.Markup.ValueSerializer" />.</summary>
      <param name="value">The value being serialized.</param>
      <param name="context">Context information that is used for conversion.</param>
      <returns>The types converted by this serializer.</returns>
    </member>
    <member name="T:System.Windows.Markup.WhitespaceSignificantCollectionAttribute">
      <summary>Indicates that a collection type should be processed as being whitespace significant by a XAML processor.</summary>
    </member>
    <member name="M:System.Windows.Markup.WhitespaceSignificantCollectionAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Markup.WhitespaceSignificantCollectionAttribute" /> class.</summary>
    </member>
    <member name="T:System.Windows.Markup.XamlDeferLoadAttribute">
      <summary>Indicates that a class or property has a deferred load usage for XAML (such as a template behavior), and reports the class that enables the deferring behavior and its destination/content type.</summary>
    </member>
    <member name="M:System.Windows.Markup.XamlDeferLoadAttribute.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Markup.XamlDeferLoadAttribute" /> class, using string names of types.</summary>
      <param name="loaderType">The string name of the type for the implementation to use for the defer load behavior.</param>
      <param name="contentType">The string name of the type for the destination/content type of the defer load behavior.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="loaderType" /> or <paramref name="contentType" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Windows.Markup.XamlDeferLoadAttribute.#ctor(System.Type,System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Markup.XamlDeferLoadAttribute" /> class, using CLR <see cref="T:System.Type" /> values.</summary>
      <param name="loaderType">The CLR <see cref="T:System.Type" /> value for the implementation to use for the defer load behavior.</param>
      <param name="contentType">The CLR <see cref="T:System.Type" /> value for the destination/content type of the defer load behavior.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="loaderType" /> or <paramref name="contentType" /> is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Windows.Markup.XamlDeferLoadAttribute.ContentType">
      <summary>Gets the CLR <see cref="T:System.Type" /> value for the destination/content type of the defer load behavior.</summary>
      <returns>The CLR <see cref="T:System.Type" /> value for the destination/content type of the defer load behavior.</returns>
    </member>
    <member name="P:System.Windows.Markup.XamlDeferLoadAttribute.ContentTypeName">
      <summary>Gets the string name of the type for the implementation to use for the defer load behavior.</summary>
      <returns>The string name of the type for the converter to use for the defer load behavior.</returns>
    </member>
    <member name="P:System.Windows.Markup.XamlDeferLoadAttribute.LoaderType">
      <summary>Gets the CLR <see cref="T:System.Type" /> value for the implementation to use for the defer load behavior.</summary>
      <returns>The CLR <see cref="T:System.Type" /> value for the implementation to use for the defer load behavior.</returns>
    </member>
    <member name="P:System.Windows.Markup.XamlDeferLoadAttribute.LoaderTypeName">
      <summary>Gets the string name of the type for the destination/content type of the defer load behavior.</summary>
      <returns>The string name of the type for the destination/content type of the defer load behavior.</returns>
    </member>
    <member name="T:System.Windows.Markup.XamlSetMarkupExtensionAttribute">
      <summary>Indicates that a class can use a markup extension to provide a value, and references a handler to use for markup extension set operations.</summary>
    </member>
    <member name="M:System.Windows.Markup.XamlSetMarkupExtensionAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Markup.XamlSetMarkupExtensionAttribute" /> class.</summary>
      <param name="xamlSetMarkupExtensionHandler">The name of the handler to use for markup extension set operations.</param>
    </member>
    <member name="P:System.Windows.Markup.XamlSetMarkupExtensionAttribute.XamlSetMarkupExtensionHandler">
      <summary>Gets the name of the handler to use for markup extension set operations.</summary>
      <returns>The name of the handler to use for markup extension set operations.</returns>
    </member>
    <member name="T:System.Windows.Markup.XamlSetMarkupExtensionEventArgs">
      <summary>Provides data for callbacks that are invoked when a XAML object writer sets a value using a markup extension.</summary>
    </member>
    <member name="M:System.Windows.Markup.XamlSetMarkupExtensionEventArgs.#ctor(System.Xaml.XamlMember,System.Windows.Markup.MarkupExtension,System.IServiceProvider)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Markup.XamlSetMarkupExtensionEventArgs" /> class.</summary>
      <param name="member">XAML type system / schema information for the member being set.</param>
      <param name="value">The markup extension reference to provide for the member being set.</param>
      <param name="serviceProvider">Service provider information passed to the markup extension.</param>
    </member>
    <member name="M:System.Windows.Markup.XamlSetMarkupExtensionEventArgs.CallBase">
      <summary>Provides a way to invoke a callback as defined on a base class of the current acting type.</summary>
    </member>
    <member name="P:System.Windows.Markup.XamlSetMarkupExtensionEventArgs.MarkupExtension">
      <summary>Gets the <see cref="T:System.Windows.Markup.MarkupExtension" /> reference that is relevant to this <see cref="T:System.Windows.Markup.XamlSetMarkupExtensionEventArgs" />.</summary>
      <returns>The markup extension reference that is relevant to this <see cref="T:System.Windows.Markup.XamlSetMarkupExtensionEventArgs" />.</returns>
    </member>
    <member name="P:System.Windows.Markup.XamlSetMarkupExtensionEventArgs.ServiceProvider">
      <summary>Gets service provider information that was passed to the markup extension.</summary>
      <returns>Service provider information that was passed to the markup extension.</returns>
    </member>
    <member name="T:System.Windows.Markup.XamlSetTypeConverterAttribute">
      <summary>Indicates that a class can use a type converter to provide a value, and references a handler to use for type converter setting cases.</summary>
    </member>
    <member name="M:System.Windows.Markup.XamlSetTypeConverterAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Markup.XamlSetTypeConverterAttribute" /> class.</summary>
      <param name="xamlSetTypeConverterHandler">The name of the handler to use for type converter setting operations.</param>
    </member>
    <member name="P:System.Windows.Markup.XamlSetTypeConverterAttribute.XamlSetTypeConverterHandler">
      <summary>Gets the <paramref name="xamlSetTypeConverterHandler" /> initialization value (the handler name) specified in the <see cref="T:System.Windows.Markup.XamlSetTypeConverterAttribute" />.</summary>
      <returns>The <paramref name="xamlSetTypeConverterHandler" /> value specified in the <see cref="T:System.Windows.Markup.XamlSetTypeConverterAttribute" />.</returns>
    </member>
    <member name="T:System.Windows.Markup.XamlSetTypeConverterEventArgs">
      <summary>Provides data for callbacks that are invoked when a XAML writer sets a value using a type converter call.</summary>
    </member>
    <member name="M:System.Windows.Markup.XamlSetTypeConverterEventArgs.#ctor(System.Xaml.XamlMember,System.ComponentModel.TypeConverter,System.Object,System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Markup.XamlSetTypeConverterEventArgs" /> class.</summary>
      <param name="member">XAML type system / schema information for the member being set.</param>
      <param name="typeConverter">The specific type converter instance being invoked.</param>
      <param name="value">The value to provide for the member being set.</param>
      <param name="serviceProvider">Service provider information that can be used by the <paramref name="typeConverter" /> class.</param>
      <param name="cultureInfo">Culture information that can be used by the <paramref name="typeConverter" /> class when calling <see cref="M:System.ComponentModel.TypeConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)" /> and other methods.</param>
    </member>
    <member name="M:System.Windows.Markup.XamlSetTypeConverterEventArgs.CallBase">
      <summary>Provides a way to invoke a callback as defined on a base class of the current acting type.</summary>
    </member>
    <member name="P:System.Windows.Markup.XamlSetTypeConverterEventArgs.CultureInfo">
      <summary>Gets <see cref="T:System.Globalization.CultureInfo" /> information that can be used by the type converter class when calling <see cref="M:System.ComponentModel.TypeConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)" /> and other methods.</summary>
      <returns>Culture information that can be used by the type converter class.</returns>
    </member>
    <member name="P:System.Windows.Markup.XamlSetTypeConverterEventArgs.ServiceProvider">
      <summary>Gets <see cref="T:System.IServiceProvider" /> information that can be used by the type converter class.</summary>
      <returns>Service provider information that can be used by the <paramref name="typeConverter" /> class.</returns>
    </member>
    <member name="P:System.Windows.Markup.XamlSetTypeConverterEventArgs.TypeConverter">
      <summary>Gets the <see cref="T:System.ComponentModel.TypeConverter" /> instance that is invoked and provides type conversion behavior.</summary>
      <returns>The type converter that provides type conversion behavior.</returns>
    </member>
    <member name="T:System.Windows.Markup.XamlSetValueEventArgs">
      <summary>Provides data for callbacks that are invoked when a <see cref="T:System.Xaml.XamlObjectWriter" /> sets certain values.</summary>
    </member>
    <member name="M:System.Windows.Markup.XamlSetValueEventArgs.#ctor(System.Xaml.XamlMember,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Markup.XamlSetValueEventArgs" /> class.</summary>
      <param name="member">XAML type system / schema information for the member being set.</param>
      <param name="value">The value to provide for the member.</param>
    </member>
    <member name="M:System.Windows.Markup.XamlSetValueEventArgs.CallBase">
      <summary>When overridden in a derived class, provides a way to invoke a <see langword="SetValue" /> callback as defined on a base class of the current acting type.</summary>
    </member>
    <member name="P:System.Windows.Markup.XamlSetValueEventArgs.Handled">
      <summary>Gets or sets a value that determines whether a caller that is using the <see cref="T:System.Windows.Markup.XamlSetValueEventArgs" /> can use the values without having to call <see cref="M:System.Windows.Markup.XamlSetValueEventArgs.CallBase" />.</summary>
      <returns>
        <see langword="true" /> if the values were useful and calling <see cref="M:System.Windows.Markup.XamlSetValueEventArgs.CallBase" /> is not necessary; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Windows.Markup.XamlSetValueEventArgs.Member">
      <summary>Gets XAML type system and XAML schema information for the member being set.</summary>
      <returns>XAML type system and XAML schema information for the member being set.</returns>
    </member>
    <member name="P:System.Windows.Markup.XamlSetValueEventArgs.Value">
      <summary>Gets the value to provide for the member being set.</summary>
      <returns>The value to provide for the member being set.</returns>
    </member>
    <member name="T:System.Windows.Markup.XData">
      <summary>Represents literal data that can appear as the value for a <see langword="Value" /> node.</summary>
    </member>
    <member name="M:System.Windows.Markup.XData.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Markup.XData" /> class.</summary>
    </member>
    <member name="P:System.Windows.Markup.XData.Text">
      <summary>Gets or sets the literal value string that this <see cref="T:System.Windows.Markup.XData" /> wraps.</summary>
      <returns>The literal value string.</returns>
    </member>
    <member name="P:System.Windows.Markup.XData.XmlReader">
      <summary>Gets or sets a reader for the literal data.</summary>
      <returns>A reader for the literal data.</returns>
    </member>
    <member name="T:System.Windows.Markup.XmlLangPropertyAttribute">
      <summary>Identifies the property to associate with the xml:lang attribute.</summary>
    </member>
    <member name="M:System.Windows.Markup.XmlLangPropertyAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Markup.XmlLangPropertyAttribute" /> class.</summary>
      <param name="name">The property name to associate with the <see langword="xml:lang" /> attribute.</param>
    </member>
    <member name="P:System.Windows.Markup.XmlLangPropertyAttribute.Name">
      <summary>Gets the name of the property that is specified in this attribute.</summary>
      <returns>The name of the property.</returns>
    </member>
    <member name="T:System.Windows.Markup.XmlnsCompatibleWithAttribute">
      <summary>Specifies that a XAML namespace can be subsumed by another XAML namespace. Typically, the subsuming XAML namespace is indicated in a previously defined <see cref="T:System.Windows.Markup.XmlnsDefinitionAttribute" />.</summary>
    </member>
    <member name="M:System.Windows.Markup.XmlnsCompatibleWithAttribute.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Markup.XmlnsCompatibleWithAttribute" /> class.</summary>
      <param name="oldNamespace">The reference XAML namespace identifier.</param>
      <param name="newNamespace">The subsuming XAML namespace identifier.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="oldNamespace" /> or <paramref name="newNamespace" /> are <see langword="null" />.</exception>
    </member>
    <member name="P:System.Windows.Markup.XmlnsCompatibleWithAttribute.NewNamespace">
      <summary>Gets the subsuming namespace identifier reported by this attribute.</summary>
      <returns>The subsuming namespace identifier reported in the attribute.</returns>
    </member>
    <member name="P:System.Windows.Markup.XmlnsCompatibleWithAttribute.OldNamespace">
      <summary>Gets the reference namespace identifier reported by this attribute.</summary>
      <returns>The reference namespace identifier.</returns>
    </member>
    <member name="T:System.Windows.Markup.XmlnsDefinitionAttribute">
      <summary>Specifies a mapping on a per-assembly basis between a XAML namespace and a CLR namespace, which is then used for type resolution by a XAML object writer or XAML schema context.</summary>
    </member>
    <member name="M:System.Windows.Markup.XmlnsDefinitionAttribute.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Markup.XmlnsDefinitionAttribute" /> class.</summary>
      <param name="xmlNamespace">The XAML namespace identifier.</param>
      <param name="clrNamespace">A string that references a CLR namespace name.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="xmlNamespace" /> or <paramref name="clrNamespace" /> are <see langword="null" />.</exception>
    </member>
    <member name="P:System.Windows.Markup.XmlnsDefinitionAttribute.AssemblyName">
      <summary>Gets or sets the name of the assembly associated with the attribute.</summary>
      <returns>The assembly name.</returns>
    </member>
    <member name="P:System.Windows.Markup.XmlnsDefinitionAttribute.ClrNamespace">
      <summary>Gets the string name of the CLR namespace specified in this attribute.</summary>
      <returns>The CLR namespace, specified as a string.</returns>
    </member>
    <member name="P:System.Windows.Markup.XmlnsDefinitionAttribute.XmlNamespace">
      <summary>Gets the XAML namespace identifier specified in this attribute.</summary>
      <returns>The XAML namespace identifier.</returns>
    </member>
    <member name="T:System.Windows.Markup.XmlnsPrefixAttribute">
      <summary>Identifies a recommended prefix to associate with a XAML namespace for XAML usage, when writing elements and attributes in a XAML file (serialization) or when interacting with a design environment that has XAML editing features.</summary>
    </member>
    <member name="M:System.Windows.Markup.XmlnsPrefixAttribute.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Markup.XmlnsPrefixAttribute" /> class.</summary>
      <param name="xmlNamespace">The XAML namespace identifier.</param>
      <param name="prefix">The recommended prefix string.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="xmlNamespace" /> or <paramref name="prefix" /> is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Windows.Markup.XmlnsPrefixAttribute.Prefix">
      <summary>Gets the recommended prefix associated with this attribute.</summary>
      <returns>The recommended prefix string.</returns>
    </member>
    <member name="P:System.Windows.Markup.XmlnsPrefixAttribute.XmlNamespace">
      <summary>Gets the XAML namespace identifier associated with this attribute.</summary>
      <returns>The XAML namespace identifier.</returns>
    </member>
    <member name="T:System.Xaml.AmbientPropertyValue">
      <summary>Reports information about an ambient property, as part of an <see cref="T:System.Xaml.IAmbientProvider" /> implementation.</summary>
    </member>
    <member name="M:System.Xaml.AmbientPropertyValue.#ctor(System.Xaml.XamlMember,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.AmbientPropertyValue" /> class.</summary>
      <param name="property">The identifier that represents the ambient property.</param>
      <param name="value">The value to report.</param>
    </member>
    <member name="P:System.Xaml.AmbientPropertyValue.RetrievedProperty">
      <summary>Gets the XAML type system identifier (<see cref="T:System.Xaml.XamlMember" />) that represents the ambient property.</summary>
      <returns>The identifier that represents the ambient property.</returns>
    </member>
    <member name="P:System.Xaml.AmbientPropertyValue.Value">
      <summary>Gets the value of the ambient property.</summary>
      <returns>The value of the ambient property.</returns>
    </member>
    <member name="T:System.Xaml.AttachableMemberIdentifier">
      <summary>Provides a XAML type system identifier representation for attachable members. The identifier structure parallels the <paramref name="declaringType" /><see langword="." /><paramref name="memberName" /> string form for attachable member usage.</summary>
    </member>
    <member name="M:System.Xaml.AttachableMemberIdentifier.#ctor(System.Type,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.AttachableMemberIdentifier" /> class.</summary>
      <param name="declaringType">The <paramref name="declaringType" /> component of the identifier, which should match the name of the declaring <see cref="P:System.Xaml.XamlMember.Type" />.</param>
      <param name="memberName">The <paramref name="memberName" /> component of the identifier, which should match the <see cref="P:System.Xaml.XamlMember.Name" />.</param>
    </member>
    <member name="M:System.Xaml.AttachableMemberIdentifier.Equals(System.Object)">
      <summary>Determines whether this instance of <see cref="T:System.Xaml.AttachableMemberIdentifier" /> and a specified object have the same value.</summary>
      <param name="obj">The object to compare with the current <see cref="T:System.Xaml.AttachableMemberIdentifier" />.</param>
      <returns>
        <see langword="true" /> if <paramref name="obj" /> is an <see cref="T:System.Xaml.AttachableMemberIdentifier" /> and if its value is the same as this instance; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xaml.AttachableMemberIdentifier.Equals(System.Xaml.AttachableMemberIdentifier)">
      <summary>Determines whether this instance and another specified <see cref="T:System.Xaml.AttachableMemberIdentifier" /> object have the same value.</summary>
      <param name="other">The <see cref="T:System.Xaml.AttachableMemberIdentifier" /> to compare with the current <see cref="T:System.Xaml.AttachableMemberIdentifier" />.</param>
      <returns>
        <see langword="true" /> if the objects have the same value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xaml.AttachableMemberIdentifier.GetHashCode">
      <summary>Returns the hash code for this <see cref="T:System.Xaml.AttachableMemberIdentifier" />.</summary>
      <returns>An integer hash code.</returns>
    </member>
    <member name="M:System.Xaml.AttachableMemberIdentifier.op_Equality(System.Xaml.AttachableMemberIdentifier,System.Xaml.AttachableMemberIdentifier)">
      <summary>Determines whether two specified <see cref="T:System.Xaml.AttachableMemberIdentifier" /> objects have the same value.</summary>
      <param name="left">An <see cref="T:System.Xaml.AttachableMemberIdentifier" />, or <see langword="null" />.</param>
      <param name="right">An <see cref="T:System.Xaml.AttachableMemberIdentifier" />, or <see langword="null" />.</param>
      <returns>
        <see langword="true" /> if the value of <paramref name="left" /> is the same as the value of <paramref name="right" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xaml.AttachableMemberIdentifier.op_Inequality(System.Xaml.AttachableMemberIdentifier,System.Xaml.AttachableMemberIdentifier)">
      <summary>Determines whether two specified <see cref="T:System.Xaml.AttachableMemberIdentifier" /> objects have different values.</summary>
      <param name="left">An <see cref="T:System.Xaml.AttachableMemberIdentifier" />, or <see langword="null" />.</param>
      <param name="right">An <see cref="T:System.Xaml.AttachableMemberIdentifier" />, or <see langword="null" />.</param>
      <returns>
        <see langword="true" /> if the value of <paramref name="left" /> differs from the value of <paramref name="right" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xaml.AttachableMemberIdentifier.ToString">
      <summary>Returns a <see cref="T:System.String" /> that represents the current <see cref="T:System.Xaml.AttachableMemberIdentifier" />.</summary>
      <returns>A <see cref="T:System.String" /> that represents the current <see cref="T:System.Xaml.AttachableMemberIdentifier" />.</returns>
    </member>
    <member name="P:System.Xaml.AttachableMemberIdentifier.DeclaringType">
      <summary>Gets or sets the <paramref name="declaringType" /> component value of the <see cref="T:System.Xaml.AttachableMemberIdentifier" />.</summary>
      <returns>The <paramref name="declaringType" /> component value of the <see cref="T:System.Xaml.AttachableMemberIdentifier" />.</returns>
    </member>
    <member name="P:System.Xaml.AttachableMemberIdentifier.MemberName">
      <summary>Gets or sets the <paramref name="memberName" /> component value of the <see cref="T:System.Xaml.AttachableMemberIdentifier" />.</summary>
      <returns>The <paramref name="memberName" /> component value of the <see cref="T:System.Xaml.AttachableMemberIdentifier" />.</returns>
    </member>
    <member name="T:System.Xaml.AttachablePropertyServices">
      <summary>Provides static helper methods that obtain values and accessor method information from an attachable property and that work with an attached property store.</summary>
    </member>
    <member name="M:System.Xaml.AttachablePropertyServices.CopyPropertiesTo(System.Object,System.Collections.Generic.KeyValuePair{System.Xaml.AttachableMemberIdentifier,System.Object}[],System.Int32)">
      <summary>Copies all attachable property/value pairs from a specified attachable property store and into a destination array.</summary>
      <param name="instance">A specific attachable property store that implements <see cref="T:System.Xaml.IAttachedPropertyStore" />; or any non-null object to access a static default attachable property store.</param>
      <param name="array">The destination array. The array is a generic array, should be passed undimensioned, and should have components of <see cref="T:System.Xaml.AttachableMemberIdentifier" /> and <see langword="object" />.</param>
      <param name="index">The source index into which to copy.</param>
    </member>
    <member name="M:System.Xaml.AttachablePropertyServices.GetAttachedPropertyCount(System.Object)">
      <summary>Returns the count of the attachable property entries that are in the specified store.</summary>
      <param name="instance">A specific attachable property store that implements <see cref="T:System.Xaml.IAttachedPropertyStore" />; or any non-null object to access a static default attachable property store.</param>
      <returns>The integer count of entries in the store.</returns>
    </member>
    <member name="M:System.Xaml.AttachablePropertyServices.RemoveProperty(System.Object,System.Xaml.AttachableMemberIdentifier)">
      <summary>Removes the entry for the specified attachable property from the specified store.</summary>
      <param name="instance">A specific attachable property store that implements <see cref="T:System.Xaml.IAttachedPropertyStore" />; or any non-null object to access a static default attachable property store.</param>
      <param name="name">The identifier for the attachable property entry to remove from the store.</param>
      <returns>
        <see langword="true" /> if an attachable property entry for <paramref name="name" /> was found in the store and removed from it; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xaml.AttachablePropertyServices.SetProperty(System.Object,System.Xaml.AttachableMemberIdentifier,System.Object)">
      <summary>Sets a value for the specified attachable property in the specified store.</summary>
      <param name="instance">A specific attachable property store that implements <see cref="T:System.Xaml.IAttachedPropertyStore" />; or any non-null object to use a static default attachable property store.</param>
      <param name="name">The identifier of the attachable property entry for which to set a value.</param>
      <param name="value">The value to set.</param>
      <exception cref="T:System.InvalidOperationException">A value could not be set in the store.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xaml.AttachablePropertyServices.TryGetProperty(System.Object,System.Xaml.AttachableMemberIdentifier,System.Object@)">
      <summary>Attempts to get a value for the specified attachable property in the specified store. Does not throw an exception if the specific attachable property does not exist in the store.</summary>
      <param name="instance">A specific attachable property store that implements <see cref="T:System.Xaml.IAttachedPropertyStore" />; or any non-null object to use a static default attachable property store.</param>
      <param name="name">The identifier of the attachable property entry for which to get a value.</param>
      <param name="value">Out parameter. When this method returns, contains the destination object for the value if <paramref name="name" /> exists in the store and has a value.</param>
      <returns>
        <see langword="true" /> if an attachable property entry for <paramref name="name" /> was found in the store and a value was posted to <paramref name="value" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xaml.AttachablePropertyServices.TryGetProperty``1(System.Object,System.Xaml.AttachableMemberIdentifier,``0@)">
      <summary>Attempts to get a value for the specified attachable property in the specified store, returning a generic output form. Does not throw an exception if the specific attachable property does not exist in the store.</summary>
      <param name="instance">A specific attachable property store that implements <see cref="T:System.Xaml.IAttachedPropertyStore" />; or any non-null object to access a static default attachable property store.</param>
      <param name="name">The identifier of the attachable property entry for which to get a value.</param>
      <param name="value">Out parameter. When this method returns, contains the destination object for the value if <paramref name="name" /> exists in the store and has a value.</param>
      <typeparam name="T">The expected type of the output.</typeparam>
      <returns>
        <see langword="true" /> if an attachable property entry for <paramref name="name" /> was found in the store and a value was posted to <paramref name="value" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Xaml.IAmbientProvider">
      <summary>Represents a service that can return information items about ambient properties or ambient types to type converters and markup extensions.</summary>
    </member>
    <member name="M:System.Xaml.IAmbientProvider.GetAllAmbientValues(System.Collections.Generic.IEnumerable{System.Xaml.XamlType},System.Boolean,System.Collections.Generic.IEnumerable{System.Xaml.XamlType},System.Xaml.XamlMember[])">
      <summary>Returns an enumerable set of ambient property information items for the requested set of types and properties.</summary>
      <param name="ceilingTypes">Specifies one or more types that should stop the evaluation when they are encountered traversing upward in the object graph. This type holds the desired ambient property. May be <see langword="null" />.</param>
      <param name="searchLiveStackOnly">
        <see langword="true" /> to not use a saved context; <see langword="false" /> to use a saved context. The default is <see langword="false" />.</param>
      <param name="types">Specifies one or more type identifier objects that identify the types to be considered ambient.</param>
      <param name="properties">Specifies one or more property identifier objects that identify the properties to be considered ambient.</param>
      <returns>An enumerable set of ambient property information items for the requested set of types and properties. The property information for each <see cref="T:System.Xaml.AmbientPropertyValue" /> that is returned will match one of the input <paramref name="types" /> or <paramref name="properties" />.</returns>
    </member>
    <member name="M:System.Xaml.IAmbientProvider.GetAllAmbientValues(System.Collections.Generic.IEnumerable{System.Xaml.XamlType},System.Xaml.XamlMember[])">
      <summary>Returns an enumerable set of ambient property information items for the requested set of properties.</summary>
      <param name="ceilingTypes">Specifies one or more types that should stop the evaluation when they are encountered traversing upward in the object graph. This type holds the desired ambient property. May be <see langword="null" />.</param>
      <param name="properties">Specifies one or more property identifier objects that identify the properties to be considered ambient.</param>
      <returns>An enumerable set of ambient property information items for the requested set of properties. The property information for each <see cref="T:System.Xaml.AmbientPropertyValue" /> that is returned will match one of the input <paramref name="properties" />.</returns>
    </member>
    <member name="M:System.Xaml.IAmbientProvider.GetAllAmbientValues(System.Xaml.XamlType[])">
      <summary>Returns an enumerable set of object instances of possible ambient types for the requested types.</summary>
      <param name="types">The set of types from which to retrieve ambient type information.</param>
      <returns>An enumerable set of objects that represent the values for the requested set of <see cref="T:System.Xaml.XamlType" /> identifiers.</returns>
    </member>
    <member name="M:System.Xaml.IAmbientProvider.GetFirstAmbientValue(System.Collections.Generic.IEnumerable{System.Xaml.XamlType},System.Xaml.XamlMember[])">
      <summary>Returns a single ambient property information item from the requested set of properties, based on which property is first encountered.</summary>
      <param name="ceilingTypes">Specifies one or more types that should stop the evaluation when they are encountered traversing upward in the object graph. This type holds the desired ambient property.</param>
      <param name="properties">Specifies one or more objects that identify the properties to be considered ambient.</param>
      <returns>A single ambient property information item for the first ambient property value from the <paramref name="properties" /> list that is found.</returns>
    </member>
    <member name="M:System.Xaml.IAmbientProvider.GetFirstAmbientValue(System.Xaml.XamlType[])">
      <summary>Returns the first matching object that is a possible ambient type for the requested types.</summary>
      <param name="types">The set of types from which to retrieve ambient type information.</param>
      <returns>The first result object for the requested set.</returns>
    </member>
    <member name="T:System.Xaml.IAttachedPropertyStore">
      <summary>Represents an attachable member store for an object where attachable members are set. This attachable member store can then be referenced with <see cref="T:System.Xaml.AttachablePropertyServices" />.</summary>
    </member>
    <member name="M:System.Xaml.IAttachedPropertyStore.CopyPropertiesTo(System.Collections.Generic.KeyValuePair{System.Xaml.AttachableMemberIdentifier,System.Object}[],System.Int32)">
      <summary>Copies all attachable member/value pairs from this attachable member store into a destination array.</summary>
      <param name="array">The destination array. The array is a generic array, should be passed undimensioned, and should have components of <see cref="T:System.Xaml.AttachableMemberIdentifier" /> and <see langword="object" />.</param>
      <param name="index">The source index where copying should begin.</param>
    </member>
    <member name="M:System.Xaml.IAttachedPropertyStore.RemoveProperty(System.Xaml.AttachableMemberIdentifier)">
      <summary>Removes the entry for the specified attachable member from this attachable member store.</summary>
      <param name="attachableMemberIdentifier">The XAML type system identifier for the attachable member entry to remove.</param>
      <returns>
        <see langword="true" /> if an attachable member entry for <paramref name="attachableMemberIdentifier" /> was found in the store and removed; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xaml.IAttachedPropertyStore.SetProperty(System.Xaml.AttachableMemberIdentifier,System.Object)">
      <summary>Sets a value for the specified attachable member in the specified store.</summary>
      <param name="attachableMemberIdentifier">The XAML type system identifier for the attachable member entry to set.</param>
      <param name="value">The value to set.</param>
    </member>
    <member name="M:System.Xaml.IAttachedPropertyStore.TryGetProperty(System.Xaml.AttachableMemberIdentifier,System.Object@)">
      <summary>Attempts to get a value for the specified attachable member in the specified store.</summary>
      <param name="attachableMemberIdentifier">The XAML type system identifier for the attachable member entry to get.</param>
      <param name="value">Out parameter. When this method returns, contains the destination object for the value if <paramref name="attachableMemberIdentifier" /> exists in the store and has a value.</param>
      <returns>
        <see langword="true" /> if an attachable member entry for <paramref name="attachableMemberIdentifier" /> was found in the store and a value was posted to <paramref name="value" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.IAttachedPropertyStore.PropertyCount">
      <summary>Gets the count of the attachable member entries in this attachable member store.</summary>
      <returns>The integer count of entries in the store.</returns>
    </member>
    <member name="T:System.Xaml.IDestinationTypeProvider">
      <summary>Represents a service that can return a CLR type system identifier for the destination type. The destination type is relevant when the destination type for a property-setting operation is indirectly reported by reflection or other mechanisms.</summary>
    </member>
    <member name="M:System.Xaml.IDestinationTypeProvider.GetDestinationType">
      <summary>Returns the CLR <see cref="T:System.Type" /> that identifies the destination type for the relevant type converter or markup extension.</summary>
      <returns>A CLR <see cref="T:System.Type" /> value for the destination type.</returns>
    </member>
    <member name="T:System.Xaml.INamespacePrefixLookup">
      <summary>Represents a service that can return the recommended prefix for a XAML namespace mapping to consumers. Consumers might include design environments or serializers.</summary>
    </member>
    <member name="M:System.Xaml.INamespacePrefixLookup.LookupPrefix(System.String)">
      <summary>Returns the recommended prefix for a specified XAML namespace identifier.</summary>
      <param name="ns">The XAML namespace identifier string for which to obtain a prefix.</param>
      <returns>The recommended prefix.</returns>
    </member>
    <member name="T:System.Xaml.IRootObjectProvider">
      <summary>Describes a service that can return the root object of markup being parsed.</summary>
    </member>
    <member name="P:System.Xaml.IRootObjectProvider.RootObject">
      <summary>Gets the root object from markup or from an object graph.</summary>
      <returns>The root object.</returns>
    </member>
    <member name="T:System.Xaml.IXamlIndexingReader">
      <summary>Provides a service that represents indexed node control for a simple implementation of a node-based XAML reader.</summary>
    </member>
    <member name="P:System.Xaml.IXamlIndexingReader.Count">
      <summary>Gets the number of nodes in the current external node set.</summary>
      <returns>The number of nodes in the current external node set.</returns>
    </member>
    <member name="P:System.Xaml.IXamlIndexingReader.CurrentIndex">
      <summary>Gets or sets the index number of the current reader position for the indexed list view of XAML nodes.</summary>
      <returns>The index number of the current reader position.</returns>
    </member>
    <member name="T:System.Xaml.IXamlLineInfo">
      <summary>Describes a service for reporting text line information in XAML reader implementations.</summary>
    </member>
    <member name="P:System.Xaml.IXamlLineInfo.HasLineInfo">
      <summary>Gets a value that specifies whether line information is available.</summary>
      <returns>
        <see langword="true" /> if line information is available; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.IXamlLineInfo.LineNumber">
      <summary>Gets the line number to report.</summary>
      <returns>The line number to report.</returns>
    </member>
    <member name="P:System.Xaml.IXamlLineInfo.LinePosition">
      <summary>Gets the line position to report.</summary>
      <returns>The line position to report.</returns>
    </member>
    <member name="T:System.Xaml.IXamlLineInfoConsumer">
      <summary>Describes a service where a XAML writer can use reported line information and then include the information in the output.</summary>
    </member>
    <member name="M:System.Xaml.IXamlLineInfoConsumer.SetLineInfo(System.Int32,System.Int32)">
      <summary>Collects line information.</summary>
      <param name="lineNumber">The line number to use in the output.</param>
      <param name="linePosition">The line position to use in the output.</param>
    </member>
    <member name="P:System.Xaml.IXamlLineInfoConsumer.ShouldProvideLineInfo">
      <summary>Gets a value that determines whether a line information service should provide values and therefore, should also call <see cref="M:System.Xaml.IXamlLineInfoConsumer.SetLineInfo(System.Int32,System.Int32)" /> when relevant.</summary>
      <returns>
        <see langword="true" /> if line information is used by the implementation; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Xaml.IXamlNameProvider">
      <summary>Provides a service that is used during save and write operations to input an object and return a XAML name.</summary>
    </member>
    <member name="M:System.Xaml.IXamlNameProvider.GetName(System.Object)">
      <summary>Retrieves the XAML name of the specified object.</summary>
      <param name="value">The object to get the name for.</param>
      <returns>The XAML name value of the requested object.</returns>
    </member>
    <member name="T:System.Xaml.IXamlNameResolver">
      <summary>Describes a service that can return objects that are specified by XAML name, or alternatively, returns a token that defers name resolution. The service can also return an enumerable set of all named objects that are in the XAML namescope.</summary>
    </member>
    <member name="E:System.Xaml.IXamlNameResolver.OnNameScopeInitializationComplete">
      <summary>Occurs when a XAML processor has registered all the relevant names to the backing XAML namescope.</summary>
    </member>
    <member name="M:System.Xaml.IXamlNameResolver.GetAllNamesAndValuesInScope">
      <summary>Returns an enumerable set of all named objects in the XAML namescope.</summary>
      <returns>An enumerable set of <see cref="T:System.Collections.Generic.KeyValuePair`2" /> objects. For each <see cref="T:System.Collections.Generic.KeyValuePair`2" />, the <see cref="P:System.Collections.Generic.KeyValuePair`2.Key" /> component is a string, and the <see cref="P:System.Collections.Generic.KeyValuePair`2.Value" /> component is the object that uses the <see cref="P:System.Collections.Generic.KeyValuePair`2.Key" /> name in the XAML namescope.</returns>
    </member>
    <member name="M:System.Xaml.IXamlNameResolver.GetFixupToken(System.Collections.Generic.IEnumerable{System.String})">
      <summary>Returns an object that can correct for certain markup patterns that produce forward references.</summary>
      <param name="names">A collection of names that are possible forward references.</param>
      <returns>An object that provides a token for lookup behavior to be evaluated later.</returns>
    </member>
    <member name="M:System.Xaml.IXamlNameResolver.GetFixupToken(System.Collections.Generic.IEnumerable{System.String},System.Boolean)">
      <summary>Returns an object that can correct for certain markup patterns that produce forward references.</summary>
      <param name="names">A collection of names that are possible forward references.</param>
      <param name="canAssignDirectly">
        <see langword="true" /> to immediately assign the resolved name reference to the target property. <see langword="false" /> to call the user code for a reparse. The default behavior is <see langword="false" />.</param>
      <returns>An object that provides a token for lookup behavior to be evaluated later.</returns>
    </member>
    <member name="M:System.Xaml.IXamlNameResolver.Resolve(System.String)">
      <summary>Resolves an object from a name reference.</summary>
      <param name="name">The name reference to resolve.</param>
      <returns>The resolved object; or null.</returns>
    </member>
    <member name="M:System.Xaml.IXamlNameResolver.Resolve(System.String,System.Boolean@)">
      <summary>Resolves an object from a name reference, and provides a tracking value that reports whether the object is fully initialized for object graph purposes.</summary>
      <param name="name">The name reference to resolve.</param>
      <param name="isFullyInitialized">When this method returns, <see langword="true" /> if the returned object has any dependencies on unresolved references; otherwise, <see langword="false" />.</param>
      <returns>An object that provides a token for lookup behavior to be evaluated later.</returns>
    </member>
    <member name="P:System.Xaml.IXamlNameResolver.IsFixupTokenAvailable">
      <summary>Gets a value that determines whether calling <see cref="M:System.Xaml.IXamlNameResolver.GetFixupToken(System.Collections.Generic.IEnumerable{System.String},System.Boolean)" /> is available in order to resolve a name into a token for forward resolution.</summary>
      <returns>
        <see langword="true" /> if <see cref="M:System.Xaml.IXamlNameResolver.GetFixupToken(System.Collections.Generic.IEnumerable{System.String},System.Boolean)" /> is available as an implementation that returns a useful token for forward resolution; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Xaml.IXamlNamespaceResolver">
      <summary>Describes a service that can return a XAML namespace that is based on its prefix as it is mapped in XAML markup.</summary>
    </member>
    <member name="M:System.Xaml.IXamlNamespaceResolver.GetNamespace(System.String)">
      <summary>Retrieves a XAML namespace identifier for the specified prefix string.</summary>
      <param name="prefix">The prefix for which to retrieve the XAML namespace information.</param>
      <returns>The requested XAML namespace identifier, as a string.</returns>
    </member>
    <member name="M:System.Xaml.IXamlNamespaceResolver.GetNamespacePrefixes">
      <summary>Returns all the possible prefix-to-XAML namespace mappings (<see cref="T:System.Xaml.NamespaceDeclaration" /> values) that are available in the active XAML schema context.</summary>
      <returns>An enumerable set of <see cref="T:System.Xaml.NamespaceDeclaration" /> values. To get all the prefix strings, get the <see cref="P:System.Xaml.NamespaceDeclaration.Prefix" /> value from each value in the returned set. To get prefixes for specific XAML namespaces, process any return value that has the desired <see cref="P:System.Xaml.NamespaceDeclaration.Namespace" /> value.</returns>
    </member>
    <member name="T:System.Xaml.IXamlObjectWriterFactory">
      <summary>Represents a service that generates a <see cref="T:System.Xaml.XamlObjectWriter" /> that is based on the current internal parser context.</summary>
    </member>
    <member name="M:System.Xaml.IXamlObjectWriterFactory.GetParentSettings">
      <summary>Returns the <see cref="T:System.Xaml.XamlObjectWriterSettings" /> from the original internal parser context.</summary>
      <returns>The settings from the original internal parser context.</returns>
    </member>
    <member name="M:System.Xaml.IXamlObjectWriterFactory.GetXamlObjectWriter(System.Xaml.XamlObjectWriterSettings)">
      <summary>Returns a <see cref="T:System.Xaml.XamlObjectWriter" /> that is based on active XAML schema context.</summary>
      <param name="settings">The settings to use for construction and initialization of the <see cref="T:System.Xaml.XamlObjectWriter" />.</param>
      <returns>A <see cref="T:System.Xaml.XamlObjectWriter" /> that has the specified settings.</returns>
    </member>
    <member name="T:System.Xaml.IXamlSchemaContextProvider">
      <summary>Represents a service that provides XAML schema context information to type converters and markup extensions.</summary>
    </member>
    <member name="P:System.Xaml.IXamlSchemaContextProvider.SchemaContext">
      <summary>Gets the <see cref="T:System.Xaml.XamlSchemaContext" /> that is reported by the service context.</summary>
      <returns>The XAML schema context that is reported by the service context.</returns>
    </member>
    <member name="T:System.Xaml.NamespaceDeclaration">
      <summary>Declares the identifier and the prefix of a XAML namespace by storing these string values as separate properties.</summary>
    </member>
    <member name="M:System.Xaml.NamespaceDeclaration.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.NamespaceDeclaration" /> class using initial property values.</summary>
      <param name="ns">The XAML namespace identifier, as a string.</param>
      <param name="prefix">The string prefix that is used for the namespace in prefix mappings.</param>
    </member>
    <member name="P:System.Xaml.NamespaceDeclaration.Namespace">
      <summary>Gets the identifier component of a <see cref="T:System.Xaml.NamespaceDeclaration" />.</summary>
      <returns>The identifier of the XAML namespace declaration.</returns>
    </member>
    <member name="P:System.Xaml.NamespaceDeclaration.Prefix">
      <summary>Gets the prefix that is used as the XAML namespace mapping prefix for a <see cref="T:System.Xaml.NamespaceDeclaration" />.</summary>
      <returns>The prefix string for this <see cref="T:System.Xaml.NamespaceDeclaration" />.</returns>
    </member>
    <member name="T:System.Xaml.Schema.AllowedMemberLocations">
      <summary>Specifies the syntax restrictions enforced on a property when it is set in XAML, as reported by a <see cref="T:System.Xaml.XamlDirective" />.</summary>
    </member>
    <member name="F:System.Xaml.Schema.AllowedMemberLocations.Any">
      <summary>Property can be set in either <see cref="F:System.Xaml.Schema.AllowedMemberLocations.Attribute" /> or <see cref="F:System.Xaml.Schema.AllowedMemberLocations.MemberElement" /> location. (This enumeration member is defined as the combination of those values.)</summary>
    </member>
    <member name="F:System.Xaml.Schema.AllowedMemberLocations.Attribute">
      <summary>Property can be set in XAML attribute syntax.</summary>
    </member>
    <member name="F:System.Xaml.Schema.AllowedMemberLocations.MemberElement">
      <summary>Property can be set in XAML property element syntax.</summary>
    </member>
    <member name="F:System.Xaml.Schema.AllowedMemberLocations.None">
      <summary>Property cannot be set in XAML at all. This is the default.</summary>
    </member>
    <member name="T:System.Xaml.Schema.ShouldSerializeResult">
      <summary>Defines serialization behavior as reported by a <see cref="T:System.Xaml.Schema.XamlMemberInvoker" />.</summary>
    </member>
    <member name="F:System.Xaml.Schema.ShouldSerializeResult.Default">
      <summary>Unknown, defer to the type of the member.</summary>
    </member>
    <member name="F:System.Xaml.Schema.ShouldSerializeResult.False">
      <summary>Do not serialize the result.</summary>
    </member>
    <member name="F:System.Xaml.Schema.ShouldSerializeResult.True">
      <summary>Serialize the result.</summary>
    </member>
    <member name="T:System.Xaml.Schema.XamlCollectionKind">
      <summary>Describes the collection metaphor (if any) used by a XAML member.</summary>
    </member>
    <member name="F:System.Xaml.Schema.XamlCollectionKind.Array">
      <summary>XAML member supports an array collection.</summary>
    </member>
    <member name="F:System.Xaml.Schema.XamlCollectionKind.Collection">
      <summary>XAML member supports a list or a collection.</summary>
    </member>
    <member name="F:System.Xaml.Schema.XamlCollectionKind.Dictionary">
      <summary>XAML member supports a dictionary (key-value pairs).</summary>
    </member>
    <member name="F:System.Xaml.Schema.XamlCollectionKind.None">
      <summary>XAML member does not support a collection.</summary>
    </member>
    <member name="T:System.Xaml.Schema.XamlMemberInvoker">
      <summary>Provides an extension point that can access member characteristics of a XAML member through techniques other than reflection.</summary>
    </member>
    <member name="M:System.Xaml.Schema.XamlMemberInvoker.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.Schema.XamlMemberInvoker" /> class.</summary>
    </member>
    <member name="M:System.Xaml.Schema.XamlMemberInvoker.#ctor(System.Xaml.XamlMember)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.Schema.XamlMemberInvoker" /> class, based on a provided <see cref="T:System.Xaml.XamlMember" />.</summary>
      <param name="member">The <see cref="T:System.Xaml.XamlMember" /> value for the specific XAML member relevant to this <see cref="T:System.Xaml.Schema.XamlMemberInvoker" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="member" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xaml.Schema.XamlMemberInvoker.GetValue(System.Object)">
      <summary>Gets a value of the relevant property from an instance.</summary>
      <param name="instance">An instance of the owner type for the member.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="instance" /> is <see langword="null" />.</exception>
      <exception cref="T:System.NotSupportedException">Invoked this method on a <see cref="T:System.Xaml.Schema.XamlMemberInvoker" /> that is based on an unknown <see cref="T:System.Xaml.XamlMember" />.  
  
 -or-  
  
 Invoked this method on a write-only member.  
  
 -or-  
  
 <see cref="P:System.Xaml.Schema.XamlMemberInvoker.UnderlyingGetter" /> is <see langword="null" />.</exception>
      <returns>The requested property value.</returns>
    </member>
    <member name="M:System.Xaml.Schema.XamlMemberInvoker.SetValue(System.Object,System.Object)">
      <summary>Sets a value of the relevant property on an instance.</summary>
      <param name="instance">An instance of the owner type for the member.</param>
      <param name="value">The property value to set.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="instance" /> is <see langword="null" />.</exception>
      <exception cref="T:System.NotSupportedException">Invoked this method on a <see cref="T:System.Xaml.Schema.XamlMemberInvoker" /> that is based on an unknown <see cref="T:System.Xaml.XamlMember" />.  
  
 -or-  
  
 Invoked this method on a read-only member.  
  
 -or-  
  
 <see cref="P:System.Xaml.Schema.XamlMemberInvoker.UnderlyingSetter" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xaml.Schema.XamlMemberInvoker.ShouldSerializeValue(System.Object)">
      <summary>Indicates whether the value needs to be persisted by serialization processes.</summary>
      <param name="instance">The instance with the property to be examined for persistence.</param>
      <returns>A value of the enumeration.</returns>
    </member>
    <member name="P:System.Xaml.Schema.XamlMemberInvoker.UnderlyingGetter">
      <summary>Gets the <see cref="T:System.Reflection.MethodInfo" /> for the CLR method that gets values for the property that is relevant for this <see cref="T:System.Xaml.Schema.XamlMemberInvoker" />.</summary>
      <returns>The <see cref="T:System.Reflection.MethodInfo" /> for the CLR method that gets values for the property that is relevant for this <see cref="T:System.Xaml.Schema.XamlMemberInvoker" />, or <see langword="null" />.</returns>
    </member>
    <member name="P:System.Xaml.Schema.XamlMemberInvoker.UnderlyingSetter">
      <summary>Gets the <see cref="T:System.Reflection.MethodInfo" /> for the CLR method that sets values for the property that is relevant for this <see cref="T:System.Xaml.Schema.XamlMemberInvoker" />.</summary>
      <returns>The <see cref="T:System.Reflection.MethodInfo" /> for the CLR method that sets values for the property that is relevant for this <see cref="T:System.Xaml.Schema.XamlMemberInvoker" />, or <see langword="null" />.</returns>
    </member>
    <member name="P:System.Xaml.Schema.XamlMemberInvoker.UnknownInvoker">
      <summary>Provides a static value that represents an unknown, not fully implemented <see cref="T:System.Xaml.Schema.XamlMemberInvoker" />.</summary>
      <returns>A static value that represents an unknown, not fully implemented <see cref="T:System.Xaml.Schema.XamlMemberInvoker" />.</returns>
    </member>
    <member name="T:System.Xaml.Schema.XamlTypeInvoker">
      <summary>Provides an extension point that can construct instances of a XAML type through techniques other than reflection and constructors.</summary>
    </member>
    <member name="M:System.Xaml.Schema.XamlTypeInvoker.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.Schema.XamlTypeInvoker" /> class.</summary>
    </member>
    <member name="M:System.Xaml.Schema.XamlTypeInvoker.#ctor(System.Xaml.XamlType)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.Schema.XamlTypeInvoker" /> class, based on a provided <see cref="T:System.Xaml.XamlType" />.</summary>
      <param name="type">The <see cref="T:System.Xaml.XamlType" /> value for the specific XAML type relevant to this <see cref="T:System.Xaml.Schema.XamlTypeInvoker" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="type" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xaml.Schema.XamlTypeInvoker.AddToCollection(System.Object,System.Object)">
      <summary>Adds the provided item to an instance of the type that is relevant to this <see cref="T:System.Xaml.Schema.XamlTypeInvoker" />.</summary>
      <param name="instance">An instance of the type specified by the <see cref="T:System.Xaml.XamlType" /> used for constructing this <see cref="T:System.Xaml.Schema.XamlTypeInvoker" />.</param>
      <param name="item">The item to add.</param>
      <exception cref="T:System.NotSupportedException">Invoked this method on a <see cref="T:System.Xaml.Schema.XamlTypeInvoker" /> that is based on an unknown <see cref="T:System.Xaml.XamlType" />.  
  
 -or-  
  
 Invoked this method on a <see cref="T:System.Xaml.Schema.XamlTypeInvoker" /> where the relevant type is not a collection.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="instance" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Xaml.XamlSchemaException">
        <see cref="M:System.Xaml.Schema.XamlTypeInvoker.GetAddMethod(System.Xaml.XamlType)" /> for this <see cref="T:System.Xaml.Schema.XamlTypeInvoker" /> returns <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xaml.Schema.XamlTypeInvoker.AddToDictionary(System.Object,System.Object,System.Object)">
      <summary>Adds the provided key and item value to an instance of the type that is relevant to this <see cref="T:System.Xaml.Schema.XamlTypeInvoker" />.</summary>
      <param name="instance">An instance of the type specified by the <see cref="T:System.Xaml.XamlType" /> used for constructing this <see cref="T:System.Xaml.Schema.XamlTypeInvoker" />.</param>
      <param name="key">Dictionary key for the item to add.</param>
      <param name="item">The item value to add.</param>
      <exception cref="T:System.NotSupportedException">Invoked this method on a <see cref="T:System.Xaml.Schema.XamlTypeInvoker" /> that is based on an unknown <see cref="T:System.Xaml.XamlType" />.  
  
 -or-  
  
 Invoked this method on a <see cref="T:System.Xaml.Schema.XamlTypeInvoker" /> where the relevant type is not a dictionary.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="instance" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Xaml.XamlSchemaException">
        <see cref="M:System.Xaml.Schema.XamlTypeInvoker.GetAddMethod(System.Xaml.XamlType)" /> for this <see cref="T:System.Xaml.Schema.XamlTypeInvoker" /> returns <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xaml.Schema.XamlTypeInvoker.CreateInstance(System.Object[])">
      <summary>Creates an object instance based on the construction-initiated <see cref="T:System.Xaml.XamlType" /> for this <see cref="T:System.Xaml.Schema.XamlTypeInvoker" />.</summary>
      <param name="arguments">An array of objects that supply the <see langword="x:ConstructorArgs" /> for the instance. May be <see langword="null" /> for types that do not require or use <see langword="x:ConstructorArgs" />.</param>
      <exception cref="T:System.MissingMethodException">Could not resolve a constructor.</exception>
      <returns>The created instance based on the construction-initiated <see cref="T:System.Xaml.XamlType" /> for this <see cref="T:System.Xaml.Schema.XamlTypeInvoker" />.</returns>
    </member>
    <member name="M:System.Xaml.Schema.XamlTypeInvoker.GetAddMethod(System.Xaml.XamlType)">
      <summary>Returns the relevant <see langword="Add" /> method for a <see cref="T:System.Xaml.Schema.XamlTypeInvoker" /> that is relevant to a collection or dictionary.</summary>
      <param name="contentType">
        <see cref="T:System.Xaml.XamlType" /> for the item type that is used by the <see langword="Add" /> method.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="contentType" /> is <see langword="null" />.</exception>
      <returns>CLR <see cref="T:System.Reflection.MethodInfo" /> information for the <see langword="Add" /> method, or null.</returns>
    </member>
    <member name="M:System.Xaml.Schema.XamlTypeInvoker.GetEnumeratorMethod">
      <summary>Returns an object representing a method that can enumerate over items.</summary>
      <returns>
        <see cref="T:System.Reflection.MethodInfo" /> for an enumerator method, or <see langword="null" />.</returns>
    </member>
    <member name="M:System.Xaml.Schema.XamlTypeInvoker.GetItems(System.Object)">
      <summary>Returns an <see cref="T:System.Collections.IEnumerator" /> object representing the set of items.</summary>
      <param name="instance">An instance of the type specified by the <see cref="T:System.Xaml.XamlType" /> used for constructing this <see cref="T:System.Xaml.Schema.XamlTypeInvoker" />.</param>
      <exception cref="T:System.NotSupportedException">Invoked this method on a <see cref="T:System.Xaml.Schema.XamlTypeInvoker" /> that is based on an unknown <see cref="T:System.Xaml.XamlType" />.  
  
 -or-  
  
 Invoked this method on a <see cref="T:System.Xaml.Schema.XamlTypeInvoker" /> where the relevant type is not a collection or dictionary.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="instance" /> is <see langword="null" />.</exception>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> object, or <see langword="null" />.</returns>
    </member>
    <member name="P:System.Xaml.Schema.XamlTypeInvoker.SetMarkupExtensionHandler">
      <summary>Gets the handler to use when a <see cref="T:System.Xaml.XamlObjectWriter" /> calls into an implemented <see cref="T:System.Windows.Markup.MarkupExtension" />.</summary>
      <returns>A handler implementation that handles this case.</returns>
    </member>
    <member name="P:System.Xaml.Schema.XamlTypeInvoker.SetTypeConverterHandler">
      <summary>Gets the handler to use when a <see cref="T:System.Xaml.XamlObjectWriter" /> calls into a CLR-implemented <see cref="T:System.ComponentModel.TypeConverter" />.</summary>
      <returns>A handler implementation that handles this case.</returns>
    </member>
    <member name="P:System.Xaml.Schema.XamlTypeInvoker.UnknownInvoker">
      <summary>Provides a static value that represents an unknown, not fully implemented <see cref="T:System.Xaml.Schema.XamlTypeInvoker" />.</summary>
      <returns>A static value that represents an unknown, not fully implemented <see cref="T:System.Xaml.Schema.XamlTypeInvoker" />.</returns>
    </member>
    <member name="T:System.Xaml.Schema.XamlTypeName">
      <summary>Provides a means to specify a XAML type in terms of name and namespace.</summary>
    </member>
    <member name="M:System.Xaml.Schema.XamlTypeName.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.Schema.XamlTypeName" /> class.</summary>
    </member>
    <member name="M:System.Xaml.Schema.XamlTypeName.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.Schema.XamlTypeName" /> class, based on name and namespace strings.</summary>
      <param name="xamlNamespace">The XAML namespace that contains name.</param>
      <param name="name">The name of the type to create a <see cref="T:System.Xaml.Schema.XamlTypeName" /> for.</param>
    </member>
    <member name="M:System.Xaml.Schema.XamlTypeName.#ctor(System.String,System.String,System.Collections.Generic.IEnumerable{System.Xaml.Schema.XamlTypeName})">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.Schema.XamlTypeName" /> class, based on name and namespace strings as well as an array of type arguments.</summary>
      <param name="xamlNamespace">The XAML namespace that contains <paramref name="name" />.</param>
      <param name="name">The name of the type to create a <see cref="T:System.Xaml.Schema.XamlTypeName" /> for.</param>
      <param name="typeArguments">An array of type arguments, each of which must be a <see cref="T:System.Xaml.Schema.XamlTypeName" />.</param>
    </member>
    <member name="M:System.Xaml.Schema.XamlTypeName.#ctor(System.Xaml.XamlType)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.Schema.XamlTypeName" /> class, based on an existing <see cref="T:System.Xaml.XamlType" />.</summary>
      <param name="xamlType">An existing <see cref="T:System.Xaml.XamlType" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="xamlType" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xaml.Schema.XamlTypeName.Parse(System.String,System.Xaml.IXamlNamespaceResolver)">
      <summary>Provides a <see cref="T:System.Xaml.Schema.XamlTypeName" /> value based on a type name and an object that can resolve a markup prefix into a namespace.</summary>
      <param name="typeName">The type name to create a <see cref="T:System.Xaml.Schema.XamlTypeName" /> value for.</param>
      <param name="namespaceResolver">An object or service provider that implements <see cref="T:System.Xaml.IXamlNamespaceResolver" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="typeName" /> or <paramref name="namespaceResolver" /> is <see langword="null" />.</exception>
      <exception cref="T:System.FormatException">String cannot be parsed.</exception>
      <returns>The created <see cref="T:System.Xaml.Schema.XamlTypeName" />.</returns>
    </member>
    <member name="M:System.Xaml.Schema.XamlTypeName.ParseList(System.String,System.Xaml.IXamlNamespaceResolver)">
      <summary>Provides a <see cref="T:System.Xaml.Schema.XamlTypeName" /> value based on a string that can specify multiple type names, and an object that can resolve a markup prefix into a namespace.</summary>
      <param name="typeNameList">A string that contains multiple types.</param>
      <param name="namespaceResolver">An object or service provider that implements <see cref="T:System.Xaml.IXamlNamespaceResolver" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="typeNameList" /> or <paramref name="namespaceResolver" /> is <see langword="null" />.</exception>
      <exception cref="T:System.FormatException">String cannot be parsed.</exception>
      <returns>The created <see cref="T:System.Xaml.Schema.XamlTypeName" />.</returns>
    </member>
    <member name="M:System.Xaml.Schema.XamlTypeName.ToString">
      <summary>Converts the value of this <see cref="T:System.Xaml.Schema.XamlTypeName" /> to its equivalent string representation.</summary>
      <returns>The equivalent string representation of this <see cref="T:System.Xaml.Schema.XamlTypeName" />.</returns>
    </member>
    <member name="M:System.Xaml.Schema.XamlTypeName.ToString(System.Collections.Generic.IList{System.Xaml.Schema.XamlTypeName},System.Xaml.INamespacePrefixLookup)">
      <summary>Converts the value of this <see cref="T:System.Xaml.Schema.XamlTypeName" /> to its equivalent string representation, which can be used in markup syntax for an object element usage of multiple types.</summary>
      <param name="typeNameList">A list of types.</param>
      <param name="prefixLookup">A service reference for prefix lookup.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="prefixLookup" /> or <paramref name="typeNameList" /> is <see langword="null" />.</exception>
      <returns>A concatenated string of all type results.</returns>
    </member>
    <member name="M:System.Xaml.Schema.XamlTypeName.ToString(System.Xaml.INamespacePrefixLookup)">
      <summary>Converts the value of this <see cref="T:System.Xaml.Schema.XamlTypeName" /> to its equivalent string representation, which can be used in markup syntax for an object element usage of a type.</summary>
      <param name="prefixLookup">A service reference for prefix lookup.</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.Xaml.Schema.XamlTypeName.Namespace" /> for this <see cref="T:System.Xaml.Schema.XamlTypeName" /> is <see langword="null" />.  
  
 -or-  
  
 <see cref="P:System.Xaml.Schema.XamlTypeName.Name" /> is not valid.  
  
 -or-  
  
 Could not look up the prefix.</exception>
      <returns>A prefixed usage string.</returns>
    </member>
    <member name="M:System.Xaml.Schema.XamlTypeName.TryParse(System.String,System.Xaml.IXamlNamespaceResolver,System.Xaml.Schema.XamlTypeName@)">
      <summary>Provides a <see cref="T:System.Xaml.Schema.XamlTypeName" /> value based on a type name and an object that can resolve a markup prefix into a namespace.</summary>
      <param name="typeName">The type name to create a <see cref="T:System.Xaml.Schema.XamlTypeName" /> value for.</param>
      <param name="namespaceResolver">An object or service provider that implements <see cref="T:System.Xaml.IXamlNamespaceResolver" />.</param>
      <param name="result">Out parameter that contains the created <see cref="T:System.Xaml.Schema.XamlTypeName" /> if the return value is <see langword="true" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="typeName" /> or <paramref name="namespaceResolver" /> is <see langword="null" />.</exception>
      <returns>
        <see langword="true" /> if the parse was successful and <paramref name="result" /> contains a useful value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xaml.Schema.XamlTypeName.TryParseList(System.String,System.Xaml.IXamlNamespaceResolver,System.Collections.Generic.IList{System.Xaml.Schema.XamlTypeName}@)">
      <summary>Provides a <see cref="T:System.Xaml.Schema.XamlTypeName" /> value based on a string that can specify multiple type names, and an object that can resolve a markup prefix into a namespace.</summary>
      <param name="typeNameList">A string that contains multiple types.</param>
      <param name="namespaceResolver">An object or service provider that implements <see cref="T:System.Xaml.IXamlNamespaceResolver" />.</param>
      <param name="result">Out parameter that contains the created <see cref="T:System.Xaml.Schema.XamlTypeName" /> if the return value is <see langword="true" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="typeNameList" /> or <paramref name="namespaceResolver" /> is <see langword="null" />.</exception>
      <returns>
        <see langword="true" /> if the parse was successful and <paramref name="result" /> contains a useful value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.Schema.XamlTypeName.Name">
      <summary>Gets the name used to construct this <see cref="T:System.Xaml.Schema.XamlTypeName" />.</summary>
      <returns>The name of the type.</returns>
    </member>
    <member name="P:System.Xaml.Schema.XamlTypeName.Namespace">
      <summary>Gets the XAML namespace identifier used to construct this <see cref="T:System.Xaml.Schema.XamlTypeName" />.</summary>
      <returns>The XAML namespace identifier.</returns>
    </member>
    <member name="P:System.Xaml.Schema.XamlTypeName.TypeArguments">
      <summary>Gets the type arguments used to construct this <see cref="T:System.Xaml.Schema.XamlTypeName" />.</summary>
      <returns>The type arguments, if any. May be null.</returns>
    </member>
    <member name="T:System.Xaml.Schema.XamlTypeTypeConverter">
      <summary>Converts a <see cref="T:System.Xaml.XamlType" /> object to and from a string that represents the type name. This functionality is used for XAML extensibility by markup definitions, via <see cref="T:System.Windows.Markup.PropertyDefinition" />.</summary>
    </member>
    <member name="M:System.Xaml.Schema.XamlTypeTypeConverter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.Schema.XamlTypeTypeConverter" /> class.</summary>
    </member>
    <member name="M:System.Xaml.Schema.XamlTypeTypeConverter.CanConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Type)">
      <summary>Determines whether an object of the specified type can be converted to an instance of <see cref="T:System.Xaml.XamlType" />, using the specified context.</summary>
      <param name="context">A format context that provides information about the environment from which this converter is being invoked.</param>
      <param name="sourceType">The type being evaluated for conversion.</param>
      <returns>
        <see langword="true" /> if this converter can perform the operation; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xaml.Schema.XamlTypeTypeConverter.CanConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Type)">
      <summary>Determines whether an instance of <see cref="T:System.Xaml.XamlType" /> can be converted to the specified type, using the specified context.</summary>
      <param name="context">A format context that provides information about the environment from which this converter is being invoked.</param>
      <param name="destinationType">The type being evaluated for conversion.</param>
      <returns>
        <see langword="true" /> if this converter can perform the operation; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xaml.Schema.XamlTypeTypeConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)">
      <summary>Attempts to convert the specified object to a <see cref="T:System.Xaml.XamlType" />, using the specified context.</summary>
      <param name="context">A format context that provides information about the environment from which this converter is being invoked.</param>
      <param name="culture">Culture specific information.</param>
      <param name="value">The object to convert.</param>
      <exception cref="T:System.NotSupportedException">
        <paramref name="value" /> cannot be converted.</exception>
      <returns>The converted object.</returns>
    </member>
    <member name="M:System.Xaml.Schema.XamlTypeTypeConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)">
      <summary>Attempts to convert a <see cref="T:System.Xaml.XamlType" /> to the specified type, using the specified context.</summary>
      <param name="context">A format context that provides information about the environment from which this converter is being invoked.</param>
      <param name="culture">Culture specific information.</param>
      <param name="value">The object to convert.</param>
      <param name="destinationType">The type to convert the object to.</param>
      <exception cref="T:System.NotSupportedException">
        <paramref name="value" /> cannot be converted.</exception>
      <returns>The converted object.</returns>
    </member>
    <member name="T:System.Xaml.Schema.XamlValueConverter`1">
      <summary>Provides a common API surface for techniques that generate initialization or serialization values for XAML based on input other than the eventual destination type. This includes markup extensions and type converters.</summary>
      <typeparam name="TConverterBase">The CLR base class for the particular converter that this <see cref="T:System.Xaml.Schema.XamlValueConverter`1" /> represents. Typically this is one of the following: <see cref="T:System.ComponentModel.TypeConverter" />; <see cref="T:System.Windows.Markup.MarkupExtension" />; <see cref="T:System.Windows.Markup.ValueSerializer" />.</typeparam>
    </member>
    <member name="M:System.Xaml.Schema.XamlValueConverter`1.#ctor(System.Type,System.Xaml.XamlType)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.Schema.XamlValueConverter`1" /> class, based on a converter implementing <see cref="T:System.Type" /> and the target/destination type of the <see cref="T:System.Xaml.Schema.XamlValueConverter`1" />.</summary>
      <param name="converterType">The <see cref="T:System.Type" /> that implements the converter behavior.</param>
      <param name="targetType">The target/destination <see cref="T:System.Xaml.XamlType" /> of the <see cref="T:System.Xaml.Schema.XamlValueConverter`1" />.</param>
    </member>
    <member name="M:System.Xaml.Schema.XamlValueConverter`1.#ctor(System.Type,System.Xaml.XamlType,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.Schema.XamlValueConverter`1" /> class, based on a converter implementing <see cref="T:System.Type" /> the target/destination type of the <see cref="T:System.Xaml.Schema.XamlValueConverter`1" />, and a string name.</summary>
      <param name="converterType">The <see cref="T:System.Type" /> that implements the converter behavior.</param>
      <param name="targetType">The target/destination <see cref="T:System.Xaml.XamlType" /> of the <see cref="T:System.Xaml.Schema.XamlValueConverter`1" />.</param>
      <param name="name">The string name.</param>
      <exception cref="T:System.ArgumentException">All three parameters are <see langword="null" /> (at least one is required to be non-null).</exception>
    </member>
    <member name="M:System.Xaml.Schema.XamlValueConverter`1.CreateInstance">
      <summary>Returns an instance of the converter implementation.</summary>
      <exception cref="T:System.Xaml.XamlSchemaException">Converter did not implement the correct base type.</exception>
      <returns>An instance of the converter implementation, or <see langword="null" />.</returns>
    </member>
    <member name="M:System.Xaml.Schema.XamlValueConverter`1.Equals(System.Object)">
      <summary>Determines whether this instance of <see cref="T:System.Xaml.Schema.XamlValueConverter`1" /> and a specified object, which must also be a <see cref="T:System.Xaml.Schema.XamlValueConverter`1" /> object, have the same value.</summary>
      <param name="obj">The object to compare.</param>
      <returns>
        <see langword="true" /> if <paramref name="obj" /> is a <see cref="T:System.Xaml.Schema.XamlValueConverter`1" /> and its value is the same as this instance; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xaml.Schema.XamlValueConverter`1.Equals(System.Xaml.Schema.XamlValueConverter{`0})">
      <summary>Determines whether this instance of <see cref="T:System.Xaml.Schema.XamlValueConverter`1" /> and another <see cref="T:System.Xaml.Schema.XamlValueConverter`1" /> object have the same value.</summary>
      <param name="other">The <see cref="T:System.Xaml.Schema.XamlValueConverter`1" /> to compare.</param>
      <returns>
        <see langword="true" /> if <paramref name="other" /> is a <see cref="T:System.Xaml.Schema.XamlValueConverter`1" /> and its value is the same as this instance; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xaml.Schema.XamlValueConverter`1.GetHashCode">
      <summary>Returns the hash code for this <see cref="T:System.Xaml.Schema.XamlValueConverter`1" />.</summary>
      <returns>An integer hash code.</returns>
    </member>
    <member name="M:System.Xaml.Schema.XamlValueConverter`1.op_Equality(System.Xaml.Schema.XamlValueConverter{`0},System.Xaml.Schema.XamlValueConverter{`0})">
      <summary>Determines whether two specified <see cref="T:System.Xaml.Schema.XamlValueConverter`1" /> objects have the same value.</summary>
      <param name="converter1">A <see cref="T:System.Xaml.Schema.XamlValueConverter`1" />, or <see langword="null" />.</param>
      <param name="converter2">A <see cref="T:System.Xaml.Schema.XamlValueConverter`1" />, or <see langword="null" />.</param>
      <returns>
        <see langword="true" /> if the value of <paramref name="converter1" /> is the same as the value of <paramref name="converter2" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xaml.Schema.XamlValueConverter`1.op_Inequality(System.Xaml.Schema.XamlValueConverter{`0},System.Xaml.Schema.XamlValueConverter{`0})">
      <summary>Determines whether two specified <see cref="T:System.Xaml.Schema.XamlValueConverter`1" /> objects have different values.</summary>
      <param name="converter1">A <see cref="T:System.Xaml.Schema.XamlValueConverter`1" />, or <see langword="null" />.</param>
      <param name="converter2">A <see cref="T:System.Xaml.Schema.XamlValueConverter`1" />, or <see langword="null" />.</param>
      <returns>
        <see langword="true" /> if the value of <paramref name="converter1" /> is different than the value of <paramref name="converter2" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xaml.Schema.XamlValueConverter`1.ToString">
      <summary>Returns a <see cref="T:System.String" /> that represents this <see cref="T:System.Xaml.Schema.XamlValueConverter`1" />.</summary>
      <returns>A <see cref="T:System.String" /> that represents this <see cref="T:System.Xaml.Schema.XamlValueConverter`1" />.</returns>
    </member>
    <member name="P:System.Xaml.Schema.XamlValueConverter`1.ConverterInstance">
      <summary>Gets a created instance of the converter implementation.</summary>
      <returns>A created instance of the converter implementation, or <see langword="null" />.</returns>
    </member>
    <member name="P:System.Xaml.Schema.XamlValueConverter`1.ConverterType">
      <summary>Gets the <see cref="T:System.Type" /> for the class that implements the converter behavior.</summary>
      <returns>The <see cref="T:System.Type" /> for the class that implements the converter behavior.</returns>
    </member>
    <member name="P:System.Xaml.Schema.XamlValueConverter`1.Name">
      <summary>Gets a string name for this <see cref="T:System.Xaml.Schema.XamlValueConverter`1" />.</summary>
      <returns>A string name for this <see cref="T:System.Xaml.Schema.XamlValueConverter`1" />.</returns>
    </member>
    <member name="P:System.Xaml.Schema.XamlValueConverter`1.TargetType">
      <summary>Gets the target/destination <see cref="T:System.Xaml.XamlType" /> of the <see cref="T:System.Xaml.Schema.XamlValueConverter`1" />.</summary>
      <returns>The target/destination <see cref="T:System.Xaml.XamlType" /> of the <see cref="T:System.Xaml.Schema.XamlValueConverter`1" />.</returns>
    </member>
    <member name="T:System.Xaml.XamlBackgroundReader">
      <summary>Implements a double-buffered <see cref="T:System.Xaml.XamlReader" /> that can split reading and writing to different threads.</summary>
    </member>
    <member name="M:System.Xaml.XamlBackgroundReader.#ctor(System.Xaml.XamlReader)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlBackgroundReader" /> class.</summary>
      <param name="wrappedReader">The <see cref="T:System.Xaml.XamlReader" /> that this <see cref="T:System.Xaml.XamlBackgroundReader" /> is based on.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="wrappedReader" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xaml.XamlBackgroundReader.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Xaml.XamlBackgroundReader" /> and optionally releases the managed resources.</summary>
      <param name="disposing">
        <see langword="true" /> to release managed resources; otherwise, <see langword="false" />.</param>
    </member>
    <member name="M:System.Xaml.XamlBackgroundReader.Read">
      <summary>Provides the next XAML node from the source, if a node is available.</summary>
      <returns>
        <see langword="true" /> if a node is available; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlBackgroundReader.StartThread">
      <summary>Creates and starts a new <see cref="T:System.Threading.Thread" /> (constructed from <see cref="T:System.Threading.ParameterizedThreadStart" />) that handles a named thread for the <see cref="T:System.Xaml.XamlReader" />.</summary>
      <exception cref="T:System.InvalidOperationException">The thread is already started.</exception>
    </member>
    <member name="M:System.Xaml.XamlBackgroundReader.StartThread(System.String)">
      <summary>Creates and starts a new <see cref="T:System.Threading.Thread" /> (constructed from <see cref="T:System.Threading.ParameterizedThreadStart" />) that handles a named thread for the <see cref="T:System.Xaml.XamlReader" />. You specify the thread name as a parameter.</summary>
      <param name="threadName">The name for the thread.</param>
      <exception cref="T:System.InvalidOperationException">The thread is already started.</exception>
    </member>
    <member name="P:System.Xaml.XamlBackgroundReader.HasLineInfo">
      <summary>Gets a value that specifies whether line information is available.</summary>
      <returns>
        <see langword="true" /> if line information is available; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlBackgroundReader.IsEof">
      <summary>Gets a value that reports whether the reader position is at end-of-file.</summary>
      <returns>
        <see langword="true" /> if the position is at end-of-file; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlBackgroundReader.LineNumber">
      <summary>Gets the line number to report.</summary>
      <returns>The line number to report.</returns>
    </member>
    <member name="P:System.Xaml.XamlBackgroundReader.LinePosition">
      <summary>Gets the line position to report.</summary>
      <returns>The line position to report.</returns>
    </member>
    <member name="P:System.Xaml.XamlBackgroundReader.Member">
      <summary>Gets the current member at the reader position, if the reader position is on a <see cref="F:System.Xaml.XamlNodeType.StartMember" />.</summary>
      <returns>The current member; or <see langword="null" />, if the position is not on a member.</returns>
    </member>
    <member name="P:System.Xaml.XamlBackgroundReader.Namespace">
      <summary>Gets the XAML namespace from the current node.</summary>
      <returns>The XAML namespace, if it is available; otherwise, <see langword="null" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlBackgroundReader.NodeType">
      <summary>Gets the type of the current node.</summary>
      <returns>A value of the <see cref="T:System.Xaml.XamlNodeType" /> enumeration.</returns>
    </member>
    <member name="P:System.Xaml.XamlBackgroundReader.SchemaContext">
      <summary>Gets an object that provides schema context information for the information set.</summary>
      <returns>An object that provides schema context information for the information set.</returns>
    </member>
    <member name="P:System.Xaml.XamlBackgroundReader.Type">
      <summary>Gets the <see cref="T:System.Xaml.XamlType" /> of the current node.</summary>
      <returns>The <see cref="T:System.Xaml.XamlType" /> of the current node; or <see langword="null" />, if the current reader position is not on an object.</returns>
    </member>
    <member name="P:System.Xaml.XamlBackgroundReader.Value">
      <summary>Gets the value of the current node.</summary>
      <returns>The value of the current node; or <see langword="null" />, if the current reader position is not on a <see cref="F:System.Xaml.XamlNodeType.Value" /> node type.</returns>
    </member>
    <member name="T:System.Xaml.XamlDeferringLoader">
      <summary>Represents a XAML reader behavior that loads and returns deferred content.</summary>
    </member>
    <member name="M:System.Xaml.XamlDeferringLoader.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlDeferringLoader" /> class.</summary>
    </member>
    <member name="M:System.Xaml.XamlDeferringLoader.Load(System.Xaml.XamlReader,System.IServiceProvider)">
      <summary>Loads XAML content in a deferred mode, which is based on a <see cref="T:System.Xaml.XamlReader" /> and certain required services from a service provider.</summary>
      <param name="xamlReader">The initiating reader that is returned on calls to <see cref="M:System.Xaml.XamlDeferringLoader.Save(System.Object,System.IServiceProvider)" />.</param>
      <param name="serviceProvider">The service provider for the required services.</param>
      <returns>The root object that is produced by the input <see cref="T:System.Xaml.XamlReader" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlDeferringLoader.Save(System.Object,System.IServiceProvider)">
      <summary>Commits a value for deferred loading.</summary>
      <param name="value">The input value to commit for deferred loading.</param>
      <param name="serviceProvider">The service provider for the required services.</param>
      <returns>A XAML reader that can be used to obtain the deferred value as XAML node information.</returns>
    </member>
    <member name="T:System.Xaml.XamlDirective">
      <summary>Provides the XAML type system identifier for a member if the member is also a XAML directive. XAML readers and XAML writers use the <see cref="T:System.Xaml.XamlDirective" /> identifier during processing of member nodes. The identifier is used when the XAML reader is positioned on a <see cref="F:System.Xaml.XamlNodeType.StartMember" /> and <see cref="P:System.Xaml.XamlMember.IsDirective" /> is <see langword="true" />.</summary>
    </member>
    <member name="M:System.Xaml.XamlDirective.#ctor(System.Collections.Generic.IEnumerable{System.String},System.String,System.Xaml.XamlType,System.Xaml.Schema.XamlValueConverter{System.ComponentModel.TypeConverter},System.Xaml.Schema.AllowedMemberLocations)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlDirective" /> class, specifying values for each per-case value of a <see cref="T:System.Xaml.XamlDirective" />.</summary>
      <param name="xamlNamespaces">A set of XAML namespaces where this <see cref="T:System.Xaml.XamlDirective" /> can exist, passed as an enumerable set of the identifier strings.</param>
      <param name="name">The identifying name of the <see cref="T:System.Xaml.XamlDirective" />.</param>
      <param name="xamlType">The XAML type that backs the <see cref="T:System.Xaml.XamlDirective" />.</param>
      <param name="typeConverter">The type converter that this <see cref="T:System.Xaml.XamlDirective" /> uses for text syntax conversion.</param>
      <param name="allowedLocation">A value of the <see cref="T:System.Xaml.Schema.AllowedMemberLocations" /> enumeration.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="xamlType" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xaml.XamlDirective.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlDirective" /> class, specifying values for a name and a single XAML namespace. Use this signature only when you want or expect <see cref="P:System.Xaml.XamlMember.IsUnknown" /> to report <see langword="true" /> for the directive.</summary>
      <param name="xamlNamespace">The primary XAML namespace where this <see cref="T:System.Xaml.XamlDirective" /> can exist.</param>
      <param name="name">The identifying name of the <see cref="T:System.Xaml.XamlDirective" />.</param>
    </member>
    <member name="M:System.Xaml.XamlDirective.GetHashCode">
      <summary>Returns the hash code for this object.</summary>
      <returns>An integer hash code.</returns>
    </member>
    <member name="M:System.Xaml.XamlDirective.GetXamlNamespaces">
      <summary>Returns a list of XAML namespaces where this XAML member can exist.</summary>
      <returns>A list of XAML namespace identifiers, as strings.</returns>
    </member>
    <member name="M:System.Xaml.XamlDirective.LookupCustomAttributeProvider">
      <summary>Returns an <see cref="T:System.Reflection.ICustomAttributeProvider" /> implementation. This implementation always returns <see langword="null" />.</summary>
      <returns>Always returns <see langword="null" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlDirective.LookupDeferringLoader">
      <summary>Returns a <see cref="T:System.Xaml.Schema.XamlValueConverter`1" /> object, which is used during deferred loading of XAML-declared objects. This implementation always returns <see langword="null" />.</summary>
      <returns>Always returns <see langword="null" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlDirective.LookupDependsOn">
      <summary>Returns a list of <see cref="T:System.Xaml.XamlMember" /> objects. The list reports the members where dependency relationships for initialization order exist relative to this <see cref="T:System.Xaml.XamlMember" />. This implementation always returns <see langword="null" />.</summary>
      <returns>Always returns <see langword="null" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlDirective.LookupInvoker">
      <summary>Returns the <see cref="T:System.Xaml.Schema.XamlMemberInvoker" /> that is associated with a <see cref="T:System.Xaml.XamlDirective" />.</summary>
      <returns>The <see cref="T:System.Xaml.Schema.XamlMemberInvoker" /> information for this <see cref="T:System.Xaml.XamlMember" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlDirective.LookupIsAmbient">
      <summary>Returns whether this <see cref="T:System.Xaml.XamlMember" /> is reported as an ambient property.</summary>
      <returns>Always returns <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlDirective.LookupIsEvent">
      <summary>Returns whether this <see cref="T:System.Xaml.XamlDirective" /> represents an event.</summary>
      <returns>Always returns <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlDirective.LookupIsReadOnly">
      <summary>Returns whether this <see cref="T:System.Xaml.XamlDirective" /> represents an intended read-only property.</summary>
      <returns>Always returns <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlDirective.LookupIsReadPublic">
      <summary>Returns whether this <see cref="T:System.Xaml.XamlDirective" /> represents a property that has a public <see langword="get" /> accessor.</summary>
      <returns>Always returns <see langword="true" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlDirective.LookupIsUnknown">
      <summary>Returns whether this <see cref="T:System.Xaml.XamlDirective" /> represents a member that is not resolvable by the backing system that is used for type and member resolution.</summary>
      <returns>
        <see langword="true" /> if this <see cref="T:System.Xaml.XamlDirective" /> represents a non-resolvable member; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlDirective.LookupIsWriteOnly">
      <summary>Returns whether this <see cref="T:System.Xaml.XamlDirective" /> represents an intended write-only property.</summary>
      <returns>Always returns <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlDirective.LookupIsWritePublic">
      <summary>Returns whether this <see cref="T:System.Xaml.XamlDirective" /> represents a property that has a public <see langword="set" /> accessor.</summary>
      <returns>Always returns <see langword="true" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlDirective.LookupTargetType">
      <summary>Returns the <see cref="T:System.Xaml.XamlType" /> of the type where the <see cref="T:System.Xaml.XamlMember" /> can exist. This implementation always returns <see langword="null" />.</summary>
      <returns>Always returns <see langword="null" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlDirective.LookupType">
      <summary>Returns the <see cref="T:System.Xaml.XamlType" /> of the type that is used by the member.</summary>
      <returns>The <see cref="T:System.Xaml.XamlType" /> of the type that is used by the member.</returns>
    </member>
    <member name="M:System.Xaml.XamlDirective.LookupTypeConverter">
      <summary>Returns a type converter implementation that is associated with this <see cref="T:System.Xaml.XamlDirective" />.</summary>
      <returns>A <see cref="T:System.Xaml.Schema.XamlValueConverter`1" /> instance that has <see cref="T:System.ComponentModel.TypeConverter" /> constraint; or <see langword="null" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlDirective.LookupUnderlyingGetter">
      <summary>Returns a <see langword="get" /> accessor that is associated with this <see cref="T:System.Xaml.XamlDirective" />. This implementation always returns <see langword="null" />.</summary>
      <returns>Always returns <see langword="null" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlDirective.LookupUnderlyingMember">
      <summary>Returns a CLR reflection <see cref="T:System.Reflection.MemberInfo" /> that is associated with this <see cref="T:System.Xaml.XamlDirective" />. This implementation always returns <see langword="null" />.</summary>
      <returns>Always returns <see langword="null" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlDirective.LookupUnderlyingSetter">
      <summary>Returns a <see langword="set" /> accessor that is associated with this <see cref="T:System.Xaml.XamlDirective" />. This implementation always returns <see langword="null" />.</summary>
      <returns>Always returns <see langword="null" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlDirective.ToString">
      <summary>Returns a string representation of this <see cref="T:System.Xaml.XamlDirective" />.</summary>
      <returns>A string representation of this <see cref="T:System.Xaml.XamlDirective" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlDirective.AllowedLocation">
      <summary>Gets a value that specifies the XAML node types where the directive can be specified.</summary>
      <returns>A value of the enumeration. The default is the enumeration default, which is <see cref="F:System.Xaml.Schema.AllowedMemberLocations.None" />.</returns>
    </member>
    <member name="T:System.Xaml.XamlDuplicateMemberException">
      <summary>The exception that is thrown when a XAML writer attempts to write a value for a duplicate member into the same object node.</summary>
    </member>
    <member name="M:System.Xaml.XamlDuplicateMemberException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlDuplicateMemberException" /> class with a system-supplied message that describes the error.</summary>
    </member>
    <member name="M:System.Xaml.XamlDuplicateMemberException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlDuplicateMemberException" /> class with serialized data.</summary>
      <param name="info">The object that holds the serialized object data.</param>
      <param name="context">The contextual information about the source or destination.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="info" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xaml.XamlDuplicateMemberException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlDuplicateMemberException" /> class with a specified message that describes the error.</summary>
      <param name="message">The message that describes the exception. The caller of this constructor is required to ensure that this string has been localized for the current system culture.</param>
    </member>
    <member name="M:System.Xaml.XamlDuplicateMemberException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlDuplicateMemberException" /> class with a specified error message and a reference to the inner exception that is the cause of this exception.</summary>
      <param name="message">The message that describes the exception. The caller of this constructor is required to ensure that this string has been localized for the current system culture.</param>
      <param name="innerException">The exception that is the cause of the current exception. If the <paramref name="innerException" /> parameter is not <see langword="null" />, the current exception is raised in a <see langword="catch" /> block that handles the inner exception.</param>
    </member>
    <member name="M:System.Xaml.XamlDuplicateMemberException.#ctor(System.Xaml.XamlMember,System.Xaml.XamlType)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlDuplicateMemberException" /> class with the relevant member and type information to report.</summary>
      <param name="member">The XAML member identifier to report.</param>
      <param name="type">The XAML type identifier to report as the parent type.</param>
    </member>
    <member name="M:System.Xaml.XamlDuplicateMemberException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlDuplicateMemberException" /> class with serialized data.</summary>
      <param name="info">The object that holds the serialized object data.</param>
      <param name="context">The contextual information about the source or destination.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="info" /> is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Xaml.XamlDuplicateMemberException.DuplicateMember">
      <summary>Gets or sets the XAML member identifier for the property to report as a duplicate.</summary>
      <returns>The <see cref="T:System.Xaml.XamlMember" /> object (XAML member identifier) to report.</returns>
    </member>
    <member name="P:System.Xaml.XamlDuplicateMemberException.ParentType">
      <summary>Gets or sets the XAML type identifier to report as the parent type.</summary>
      <returns>The <see cref="T:System.Xaml.XamlType" /> object (XAML type identifier) to report as the parent type.</returns>
    </member>
    <member name="T:System.Xaml.XamlException">
      <summary>The exception that is thrown for a general XAML reader or XAML writer exception.</summary>
    </member>
    <member name="M:System.Xaml.XamlException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlException" /> class. The instance contains a system-supplied message that describes the error.</summary>
    </member>
    <member name="M:System.Xaml.XamlException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlException" /> class with serialized data.</summary>
      <param name="info">The object that holds the serialized object data.</param>
      <param name="context">The contextual information about the source or destination.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="info" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xaml.XamlException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlException" /> class. The instance contains a specified message that describes the error.</summary>
      <param name="message">The message that describes the exception. The caller of this constructor must make sure that this string has been localized for the current system culture.</param>
    </member>
    <member name="M:System.Xaml.XamlException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlException" /> class. The instance contains a specified error message and a reference to the inner exception that is the cause of this exception.</summary>
      <param name="message">The message that describes the exception. The caller of this constructor must make sure that this string has been localized for the current system culture.</param>
      <param name="innerException">The exception that is the cause of the current exception. If the <paramref name="innerException" /> parameter is not <see langword="null" />, the current exception is raised in a <see langword="catch" /> block that handles the inner exception.</param>
    </member>
    <member name="M:System.Xaml.XamlException.#ctor(System.String,System.Exception,System.Int32,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlException" /> class. The instance contains a specified error message, inner exception, and line information.</summary>
      <param name="message">The message that describes the exception. The caller of this constructor must make sure that this string has been localized for the current system culture.</param>
      <param name="innerException">The exception that is the cause of the current exception.</param>
      <param name="lineNumber">The line number to report to debugging or to line information consumers.</param>
      <param name="linePosition">The line position to report to debugging or line information consumers.</param>
    </member>
    <member name="M:System.Xaml.XamlException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Implements <see cref="M:System.Runtime.Serialization.ISerializable.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)" /> and provides serialization support for the line information data.</summary>
      <param name="info">The object that holds the serialized object data.</param>
      <param name="context">The contextual information about the source or destination.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="info" /> is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Xaml.XamlException.LineNumber">
      <summary>Gets or sets the line number component of XAML text line information that the exception reports.</summary>
      <returns>The line number component of the XAML text line information.</returns>
    </member>
    <member name="P:System.Xaml.XamlException.LinePosition">
      <summary>Gets or sets the line position component of XAML text line information that the exception reports.</summary>
      <returns>The line position component of XAML text line information.</returns>
    </member>
    <member name="P:System.Xaml.XamlException.Message">
      <summary>Gets or sets the exception message, and if line information is available, appends the line information to the message.</summary>
      <returns>The exception message that includes the appended line information.</returns>
    </member>
    <member name="T:System.Xaml.XamlInternalException">
      <summary>The exception that is thrown for internal inconsistencies that occur during XAML reading and XAML writing.</summary>
    </member>
    <member name="M:System.Xaml.XamlInternalException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlInternalException" /> class with a system-supplied message that describes the error.</summary>
    </member>
    <member name="M:System.Xaml.XamlInternalException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlInternalException" /> class with serialized data.</summary>
      <param name="info">The object that holds the serialized object data.</param>
      <param name="context">The contextual information about the source or destination.</param>
    </member>
    <member name="M:System.Xaml.XamlInternalException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlInternalException" /> class with a specified message that describes the error.</summary>
      <param name="message">The message that describes the exception.</param>
    </member>
    <member name="M:System.Xaml.XamlInternalException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlInternalException" /> class with a specified error message and a reference to the inner exception that is the cause of this exception.</summary>
      <param name="message">The message that describes the exception.</param>
      <param name="innerException">The exception that is the cause of the current exception. If the <paramref name="innerException" /> parameter is not <see langword="null" />, the current exception is raised in a <see langword="catch" /> block that handles the inner exception.</param>
    </member>
    <member name="T:System.Xaml.XamlLanguage">
      <summary>Defines constants that provide strings or objects that are useful for XAML markup or for programming with XAML types. These strings or objects are relevant to XAML language concepts, to the implementation of XAML language concepts in .NET XAML Services, or to both.</summary>
    </member>
    <member name="F:System.Xaml.XamlLanguage.Xaml2006Namespace">
      <summary>Gets a string value for the string that identifies the XAML (2006) language namespace. That namespace corresponds to the XAML (2006) "x" prefixed namespace as defined in [MS-XAML] Section 5.1.1.</summary>
    </member>
    <member name="F:System.Xaml.XamlLanguage.Xml1998Namespace">
      <summary>Gets a string value for the string that identifies the XML (1998) language namespace. That namespace corresponds to the XML "xml" prefixed namespace as referenced in [MS-XAML] Section 5.1.2.</summary>
    </member>
    <member name="P:System.Xaml.XamlLanguage.AllDirectives">
      <summary>Gets a read-only generic collection of each <see cref="T:System.Xaml.XamlDirective" /> identifier that is defined by .NET XAML Services.</summary>
      <returns>A read-only generic collection of each <see cref="T:System.Xaml.XamlDirective" /> identifier that is defined by .NET XAML Services.</returns>
    </member>
    <member name="P:System.Xaml.XamlLanguage.AllTypes">
      <summary>Gets a read-only generic collection of individual <see cref="T:System.Xaml.XamlType" /> values that match, or alias, a XAML language intrinsic that is defined by .NET XAML Services.</summary>
      <returns>A read-only generic collection of each <see cref="T:System.Xaml.XamlType" /> that matches a XAML language intrinsic.</returns>
    </member>
    <member name="P:System.Xaml.XamlLanguage.Arguments">
      <summary>Gets a <see cref="T:System.Xaml.XamlDirective" /> for the <see langword="Arguments" /> of a factory method or a generic usage.</summary>
      <returns>A <see cref="T:System.Xaml.XamlDirective" /> for the <see langword="Arguments" /> of a factory method or generic usage.</returns>
    </member>
    <member name="P:System.Xaml.XamlLanguage.Array">
      <summary>Gets a <see cref="T:System.Xaml.XamlType" /> for the <see langword="Array" /> XAML language intrinsic.</summary>
      <returns>A <see cref="T:System.Xaml.XamlType" /> for the <see langword="Array" /> XAML language intrinsic.</returns>
    </member>
    <member name="P:System.Xaml.XamlLanguage.AsyncRecords">
      <summary>Gets a <see cref="T:System.Xaml.XamlDirective" /> for the <see langword="AsyncRecords" /> pseudomember.</summary>
      <returns>A <see cref="T:System.Xaml.XamlDirective" /> for the <see langword="AsyncRecords" /> pseudomember.</returns>
    </member>
    <member name="P:System.Xaml.XamlLanguage.Base">
      <summary>Gets a <see cref="T:System.Xaml.XamlDirective" /> for the <see langword="base" /> directive from XML.</summary>
      <returns>A <see cref="T:System.Xaml.XamlDirective" /> for the <see langword="base" /> directive from XML.</returns>
    </member>
    <member name="P:System.Xaml.XamlLanguage.Boolean">
      <summary>Gets a <see cref="T:System.Xaml.XamlType" /> for the <see langword="Boolean" /> XAML language intrinsic.</summary>
      <returns>A <see cref="T:System.Xaml.XamlType" /> for the <see langword="Boolean" /> XAML language intrinsic.</returns>
    </member>
    <member name="P:System.Xaml.XamlLanguage.Byte">
      <summary>Gets a <see cref="T:System.Xaml.XamlType" /> for the <see langword="Byte" /> XAML language intrinsic.</summary>
      <returns>A <see cref="T:System.Xaml.XamlType" /> for the <see langword="Byte" /> XAML language intrinsic.</returns>
    </member>
    <member name="P:System.Xaml.XamlLanguage.Char">
      <summary>Gets a <see cref="T:System.Xaml.XamlType" /> for the <see langword="Char" /> XAML language intrinsic.</summary>
      <returns>A <see cref="T:System.Xaml.XamlType" /> for the <see langword="Char" /> XAML language intrinsic.</returns>
    </member>
    <member name="P:System.Xaml.XamlLanguage.Class">
      <summary>Gets a <see cref="T:System.Xaml.XamlDirective" /> for the <see langword="Class" /> directive from XAML.</summary>
      <returns>A <see cref="T:System.Xaml.XamlDirective" /> for the <see langword="Class" /> directive from XAML.</returns>
    </member>
    <member name="P:System.Xaml.XamlLanguage.ClassAttributes">
      <summary>Gets a <see cref="T:System.Xaml.XamlDirective" /> for the <see langword="ClassAttributes" /> directive from XAML.</summary>
      <returns>A <see cref="T:System.Xaml.XamlDirective" /> for the <see langword="ClassAttributes" /> directive from XAML.</returns>
    </member>
    <member name="P:System.Xaml.XamlLanguage.ClassModifier">
      <summary>Gets a <see cref="T:System.Xaml.XamlDirective" /> for the <see langword="ClassModifier" /> directive from XAML.</summary>
      <returns>A <see cref="T:System.Xaml.XamlDirective" /> for the <see langword="ClassModifier" /> directive from XAML.</returns>
    </member>
    <member name="P:System.Xaml.XamlLanguage.Code">
      <summary>Gets a <see cref="T:System.Xaml.XamlDirective" /> for <see langword="Code" /> as detailed in [MS-XAML].</summary>
      <returns>A <see cref="T:System.Xaml.XamlDirective" /> for <see langword="Code" /> as detailed in [MS-XAML].</returns>
    </member>
    <member name="P:System.Xaml.XamlLanguage.ConnectionId">
      <summary>Gets a <see cref="T:System.Xaml.XamlDirective" /> that identifies a connection point for wiring events to handlers.</summary>
      <returns>A <see cref="T:System.Xaml.XamlDirective" /> that identifies a connection point for wiring events to handlers.</returns>
    </member>
    <member name="P:System.Xaml.XamlLanguage.Decimal">
      <summary>Gets a <see cref="T:System.Xaml.XamlType" /> for the <see langword="Decimal" /> XAML language intrinsic.</summary>
      <returns>A <see cref="T:System.Xaml.XamlType" /> for the <see langword="Decimal" /> XAML language intrinsic.</returns>
    </member>
    <member name="P:System.Xaml.XamlLanguage.Double">
      <summary>Gets a <see cref="T:System.Xaml.XamlType" /> for the <see langword="Double" /> XAML language intrinsic.</summary>
      <returns>A <see cref="T:System.Xaml.XamlType" /> for the <see langword="Double" /> XAML language intrinsic.</returns>
    </member>
    <member name="P:System.Xaml.XamlLanguage.FactoryMethod">
      <summary>Gets a <see cref="T:System.Xaml.XamlDirective" /> that identifies a factory method for XAML.</summary>
      <returns>A <see cref="T:System.Xaml.XamlDirective" /> that identifies a factory method for XAML.</returns>
    </member>
    <member name="P:System.Xaml.XamlLanguage.FieldModifier">
      <summary>Gets a <see cref="T:System.Xaml.XamlDirective" /> for the <see langword="FieldModifier" /> directive from XAML.</summary>
      <returns>A <see cref="T:System.Xaml.XamlDirective" /> for the <see langword="FieldModifier" /> directive from XAML.</returns>
    </member>
    <member name="P:System.Xaml.XamlLanguage.Initialization">
      <summary>Gets a <see cref="T:System.Xaml.XamlDirective" /> for the <see langword="Initialization" /> directive from XAML.</summary>
      <returns>A <see cref="T:System.Xaml.XamlDirective" /> for the <see langword="Initialization" /> directive from XAML.</returns>
    </member>
    <member name="P:System.Xaml.XamlLanguage.Int16">
      <summary>Gets a <see cref="T:System.Xaml.XamlType" /> for the <see cref="T:System.Int16" /> XAML language intrinsic.</summary>
      <returns>A <see cref="T:System.Xaml.XamlType" /> for the <see cref="T:System.Int16" /> XAML language intrinsic.</returns>
    </member>
    <member name="P:System.Xaml.XamlLanguage.Int32">
      <summary>Gets a <see cref="T:System.Xaml.XamlType" /> for the <see cref="T:System.Int32" /> XAML language intrinsic.</summary>
      <returns>A <see cref="T:System.Xaml.XamlType" /> for the <see cref="T:System.Int32" /> XAML language intrinsic.</returns>
    </member>
    <member name="P:System.Xaml.XamlLanguage.Int64">
      <summary>Gets a <see cref="T:System.Xaml.XamlType" /> for the <see cref="T:System.Int64" /> XAML language intrinsic.</summary>
      <returns>A <see cref="T:System.Xaml.XamlType" /> for the <see cref="T:System.Int64" /> XAML language intrinsic.</returns>
    </member>
    <member name="P:System.Xaml.XamlLanguage.Items">
      <summary>Gets a <see cref="T:System.Xaml.XamlDirective" /> for the <see langword="Items" /> directive from XAML.</summary>
      <returns>A <see cref="T:System.Xaml.XamlDirective" /> for the <see langword="Items" /> directive from XAML.</returns>
    </member>
    <member name="P:System.Xaml.XamlLanguage.Key">
      <summary>Gets a <see cref="T:System.Xaml.XamlDirective" /> for the <see langword="Key" /> directive from XAML.</summary>
      <returns>A <see cref="T:System.Xaml.XamlDirective" /> for the <see langword="Key" /> directive from XAML.</returns>
    </member>
    <member name="P:System.Xaml.XamlLanguage.Lang">
      <summary>Gets a <see cref="T:System.Xaml.XamlDirective" /> for the <see langword="lang" /> directive from XML.</summary>
      <returns>A <see cref="T:System.Xaml.XamlDirective" /> for the <see langword="lang" /> directive from XML.</returns>
    </member>
    <member name="P:System.Xaml.XamlLanguage.Member">
      <summary>Gets a <see cref="T:System.Xaml.XamlType" /> for the type that is the item type of <see cref="P:System.Xaml.XamlLanguage.Members" />.</summary>
      <returns>A <see cref="T:System.Xaml.XamlType" /> for the type that is the item type of <see cref="P:System.Xaml.XamlLanguage.Members" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlLanguage.Members">
      <summary>Gets a <see cref="T:System.Xaml.XamlDirective" /> for the <see langword="Members" /> concept in XAML.</summary>
      <returns>A <see cref="T:System.Xaml.XamlDirective" /> for the <see langword="Members" /> concept in XAML.</returns>
    </member>
    <member name="P:System.Xaml.XamlLanguage.Name">
      <summary>Gets a <see cref="T:System.Xaml.XamlDirective" /> for the <see langword="Name" /> directive from XAML.</summary>
      <returns>A <see cref="T:System.Xaml.XamlDirective" /> for the <see langword="Name" /> directive from XAML.</returns>
    </member>
    <member name="P:System.Xaml.XamlLanguage.Null">
      <summary>Gets a <see cref="T:System.Xaml.XamlType" /> for the <see langword="Null" /> or <see langword="NullExtension" /> XAML language intrinsic.</summary>
      <returns>A <see cref="T:System.Xaml.XamlType" /> for the <see langword="Null" />/<see langword="NullExtension" /> XAML language intrinsic.</returns>
    </member>
    <member name="P:System.Xaml.XamlLanguage.Object">
      <summary>Gets a <see cref="T:System.Xaml.XamlType" /> for the <see langword="Object" /> XAML language concept.</summary>
      <returns>A <see cref="T:System.Xaml.XamlType" /> for the <see langword="Object" /> XAML language concept.</returns>
    </member>
    <member name="P:System.Xaml.XamlLanguage.PositionalParameters">
      <summary>Gets a <see cref="T:System.Xaml.XamlDirective" /> for the <see langword="PositionalParameters" /> directive from XAML.</summary>
      <returns>A <see cref="T:System.Xaml.XamlDirective" /> for the <see langword="PositionalParameters" /> directive from XAML.</returns>
    </member>
    <member name="P:System.Xaml.XamlLanguage.Property">
      <summary>Gets a <see cref="T:System.Xaml.XamlType" /> for the <see langword="Property" /> concept in XAML.</summary>
      <returns>A <see cref="T:System.Xaml.XamlType" /> for the <see langword="Property" /> concept in XAML.</returns>
    </member>
    <member name="P:System.Xaml.XamlLanguage.Reference">
      <summary>Gets a <see cref="T:System.Xaml.XamlType" /> that represents a <see langword="Reference" /> for XAML.</summary>
      <returns>A <see cref="T:System.Xaml.XamlType" /> that represents a <see langword="Reference" /> for XAML.</returns>
    </member>
    <member name="P:System.Xaml.XamlLanguage.Shared">
      <summary>Gets a <see cref="T:System.Xaml.XamlDirective" /> for the <see langword="Shared" /> directive for XAML.</summary>
      <returns>A <see cref="T:System.Xaml.XamlDirective" /> for the <see langword="Shared" /> directive for XAML.</returns>
    </member>
    <member name="P:System.Xaml.XamlLanguage.Single">
      <summary>Gets a <see cref="T:System.Xaml.XamlType" /> for the <c>Single</c> XAML language intrinsic.</summary>
      <returns>A <see cref="T:System.Xaml.XamlType" /> for the <c>Single</c> XAML language intrinsic.</returns>
    </member>
    <member name="P:System.Xaml.XamlLanguage.Space">
      <summary>Gets a <see cref="T:System.Xaml.XamlDirective" /> for the <see langword="space" /> directive from XML.</summary>
      <returns>A <see cref="T:System.Xaml.XamlDirective" /> for the <see langword="space" /> directive from XML.</returns>
    </member>
    <member name="P:System.Xaml.XamlLanguage.Static">
      <summary>Gets a <see cref="T:System.Xaml.XamlType" /> for the <see langword="Static" />/<see langword="StaticExtension" /> XAML language intrinsic.</summary>
      <returns>A <see cref="T:System.Xaml.XamlType" /> for the <see langword="Static" />/<see langword="StaticExtension" /> XAML language intrinsic.</returns>
    </member>
    <member name="P:System.Xaml.XamlLanguage.String">
      <summary>Gets a <see cref="T:System.Xaml.XamlType" /> for the <see langword="String" /> XAML language intrinsic.</summary>
      <returns>A <see cref="T:System.Xaml.XamlType" /> for the <see langword="String" /> XAML language intrinsic.</returns>
    </member>
    <member name="P:System.Xaml.XamlLanguage.Subclass">
      <summary>Gets a <see cref="T:System.Xaml.XamlDirective" /> for the <see langword="Subclass" /> directive from XAML.</summary>
      <returns>A <see cref="T:System.Xaml.XamlDirective" /> for the <see langword="Subclass" /> directive from XAML.</returns>
    </member>
    <member name="P:System.Xaml.XamlLanguage.SynchronousMode">
      <summary>Gets a <see cref="T:System.Xaml.XamlDirective" /> that enables loading XAML asynchronously if the XAML processor supports such a mode.</summary>
      <returns>A <see cref="T:System.Xaml.XamlDirective" /> that enables loading XAML asynchronously.</returns>
    </member>
    <member name="P:System.Xaml.XamlLanguage.TimeSpan">
      <summary>Gets a <see cref="T:System.Xaml.XamlType" /> for the <see langword="TimeSpan" /> concept in XAML language.</summary>
      <returns>A <see cref="T:System.Xaml.XamlType" /> for the <see langword="TimeSpan" /> XAML language concept.</returns>
    </member>
    <member name="P:System.Xaml.XamlLanguage.Type">
      <summary>Gets a <see cref="T:System.Xaml.XamlType" /> for the <see langword="Type" />/<see langword="TypeExtension" /> XAML language intrinsic.</summary>
      <returns>A <see cref="T:System.Xaml.XamlType" /> for the <see langword="Type" />/<see langword="TypeExtension" /> XAML language intrinsic.</returns>
    </member>
    <member name="P:System.Xaml.XamlLanguage.TypeArguments">
      <summary>Gets a <see cref="T:System.Xaml.XamlDirective" /> for the <see langword="TypeArguments" /> directive from XAML.</summary>
      <returns>A <see cref="T:System.Xaml.XamlDirective" /> for the <see langword="TypeArguments" /> directive from XAML.</returns>
    </member>
    <member name="P:System.Xaml.XamlLanguage.Uid">
      <summary>Gets a <see cref="T:System.Xaml.XamlDirective" /> for the <see langword="Uid" /> directive from XAML.</summary>
      <returns>A <see cref="T:System.Xaml.XamlDirective" /> for the <see langword="Uid" /> directive from XAML.</returns>
    </member>
    <member name="P:System.Xaml.XamlLanguage.UnknownContent">
      <summary>Gets a <see cref="T:System.Xaml.XamlDirective" /> for the <see langword="UnknownContent" /> directive from XAML.</summary>
      <returns>A <see cref="T:System.Xaml.XamlDirective" /> for the <see langword="UnknownContent" /> directive from XAML.</returns>
    </member>
    <member name="P:System.Xaml.XamlLanguage.Uri">
      <summary>Gets a <see cref="T:System.Xaml.XamlType" /> for the <see langword="Uri" /> XAML language concept.</summary>
      <returns>A <see cref="T:System.Xaml.XamlType" /> for the <see langword="Uri" /> XAML language concept.</returns>
    </member>
    <member name="P:System.Xaml.XamlLanguage.XamlNamespaces">
      <summary>Gets a collection of the namespace identifiers for XAML.</summary>
      <returns>A collection of the namespace identifiers for XAML.</returns>
    </member>
    <member name="P:System.Xaml.XamlLanguage.XData">
      <summary>Gets a <see cref="T:System.Xaml.XamlType" /> for the XAML type that backs an <see langword="XData" /> block in XAML.</summary>
      <returns>The <see cref="T:System.Xaml.XamlType" /> for the XAML type that backs an <see langword="XData" /> block. See [MS-XAML] Section 5.2.23.</returns>
    </member>
    <member name="P:System.Xaml.XamlLanguage.XmlNamespaces">
      <summary>Gets a collection of the namespace identifiers for XML.</summary>
      <returns>A collection of the namespace identifiers for XML.</returns>
    </member>
    <member name="T:System.Xaml.XamlMember">
      <summary>Provides the XAML type system identifier for members of XAML types. The identifier is used by XAML readers and XAML writers during processing of member nodes (when the XAML reader is positioned on a <see cref="F:System.Xaml.XamlNodeType.StartMember" />) and also for general XAML type system logic.</summary>
    </member>
    <member name="M:System.Xaml.XamlMember.#ctor(System.Reflection.EventInfo,System.Xaml.XamlSchemaContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlMember" /> class using CLR type system <see cref="T:System.Reflection.EventInfo" /> and a <see cref="T:System.Xaml.XamlSchemaContext" />.</summary>
      <param name="eventInfo">The CLR type system <see cref="T:System.Reflection.EventInfo" /> that represents the event member.</param>
      <param name="schemaContext">The <see cref="T:System.Xaml.XamlSchemaContext" /> context that qualifies the member.</param>
    </member>
    <member name="M:System.Xaml.XamlMember.#ctor(System.Reflection.EventInfo,System.Xaml.XamlSchemaContext,System.Xaml.Schema.XamlMemberInvoker)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlMember" /> class using CLR type system <see cref="T:System.Reflection.EventInfo" /> and a <see cref="T:System.Xaml.XamlSchemaContext" />, including <see cref="T:System.Xaml.Schema.XamlMemberInvoker" /> information.</summary>
      <param name="eventInfo">The CLR type system <see cref="T:System.Reflection.EventInfo" /> that represents the event member.</param>
      <param name="schemaContext">The <see cref="T:System.Xaml.XamlSchemaContext" /> context that qualifies the member.</param>
      <param name="invoker">The <see cref="T:System.Xaml.Schema.XamlMemberInvoker" /> implementation that handles run-time reflection calls against the <see cref="T:System.Xaml.XamlMember" />.</param>
    </member>
    <member name="M:System.Xaml.XamlMember.#ctor(System.Reflection.PropertyInfo,System.Xaml.XamlSchemaContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlMember" /> class using CLR type system <see cref="T:System.Reflection.PropertyInfo" /> and a <see cref="T:System.Xaml.XamlSchemaContext" />.</summary>
      <param name="propertyInfo">The CLR type system <see cref="T:System.Reflection.PropertyInfo" /> that represents the property member.</param>
      <param name="schemaContext">The <see cref="T:System.Xaml.XamlSchemaContext" /> context that qualifies the member.</param>
    </member>
    <member name="M:System.Xaml.XamlMember.#ctor(System.Reflection.PropertyInfo,System.Xaml.XamlSchemaContext,System.Xaml.Schema.XamlMemberInvoker)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlMember" /> class using reflection <see cref="T:System.Reflection.PropertyInfo" /> and a <see cref="T:System.Xaml.XamlSchemaContext" />, including <see cref="T:System.Xaml.Schema.XamlMemberInvoker" /> information.</summary>
      <param name="propertyInfo">The CLR type system <see cref="T:System.Reflection.PropertyInfo" /> that represents the property member.</param>
      <param name="schemaContext">The <see cref="T:System.Xaml.XamlSchemaContext" /> context that qualifies the member.</param>
      <param name="invoker">The <see cref="T:System.Xaml.Schema.XamlMemberInvoker" /> implementation that handles run-time invocation calls against the <see cref="T:System.Xaml.XamlMember" />.</param>
    </member>
    <member name="M:System.Xaml.XamlMember.#ctor(System.String,System.Reflection.MethodInfo,System.Reflection.MethodInfo,System.Xaml.XamlSchemaContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlMember" /> class for a <see cref="T:System.Xaml.XamlMember" /> that represents an attachable property.</summary>
      <param name="attachablePropertyName">The string name of the attachable property.</param>
      <param name="getter">The CLR type system <see cref="T:System.Reflection.MethodInfo" /> for the <see langword="get" /> accessor of the attachable member's backing implementation.</param>
      <param name="setter">The CLR type system <see cref="T:System.Reflection.MethodInfo" /> for the <see langword="set" /> accessor of the attachable member's backing implementation.</param>
      <param name="schemaContext">The <see cref="T:System.Xaml.XamlSchemaContext" /> context that qualifies the member.</param>
    </member>
    <member name="M:System.Xaml.XamlMember.#ctor(System.String,System.Reflection.MethodInfo,System.Reflection.MethodInfo,System.Xaml.XamlSchemaContext,System.Xaml.Schema.XamlMemberInvoker)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlMember" /> class for a <see cref="T:System.Xaml.XamlMember" /> that represents an attachable property, including <see cref="T:System.Xaml.Schema.XamlMemberInvoker" /> information.</summary>
      <param name="attachablePropertyName">The string name of the attachable property.</param>
      <param name="getter">The CLR type system <see cref="T:System.Reflection.MethodInfo" /> for the <see langword="get" /> accessor of the attachable member's backing implementation.</param>
      <param name="setter">The CLR type system <see cref="T:System.Reflection.MethodInfo" /> for the <see langword="set" /> accessor of the attachable member's backing implementation.</param>
      <param name="schemaContext">The <see cref="T:System.Xaml.XamlSchemaContext" /> context that qualifies the member.</param>
      <param name="invoker">The <see cref="T:System.Xaml.Schema.XamlMemberInvoker" /> implementation that handles run-time invocation calls against the <see cref="T:System.Xaml.XamlMember" />.</param>
    </member>
    <member name="M:System.Xaml.XamlMember.#ctor(System.String,System.Reflection.MethodInfo,System.Xaml.XamlSchemaContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlMember" /> class for a <see cref="T:System.Xaml.XamlMember" /> that represents an attachable event.</summary>
      <param name="attachableEventName">The string name of the attachable event.</param>
      <param name="adder">The CLR type system <see cref="T:System.Reflection.MethodInfo" /> for the handler <see langword="Add" /> method of the attachable member's backing implementation.</param>
      <param name="schemaContext">The <see cref="T:System.Xaml.XamlSchemaContext" /> context that qualifies the member.</param>
    </member>
    <member name="M:System.Xaml.XamlMember.#ctor(System.String,System.Reflection.MethodInfo,System.Xaml.XamlSchemaContext,System.Xaml.Schema.XamlMemberInvoker)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlMember" /> class for a <see cref="T:System.Xaml.XamlMember" /> that represents an attachable event, including <see cref="T:System.Xaml.Schema.XamlMemberInvoker" /> information.</summary>
      <param name="attachableEventName">The string name of the attachable event.</param>
      <param name="adder">The CLR type system <see cref="T:System.Reflection.MethodInfo" /> for the handler <see langword="Add" /> method of the attachable member's backing implementation.</param>
      <param name="schemaContext">The <see cref="T:System.Xaml.XamlSchemaContext" /> context that qualifies the member.</param>
      <param name="invoker">The <see cref="T:System.Xaml.Schema.XamlMemberInvoker" /> implementation that handles run-time invocation calls against the <see cref="T:System.Xaml.XamlMember" />.</param>
    </member>
    <member name="M:System.Xaml.XamlMember.#ctor(System.String,System.Xaml.XamlType,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlMember" /> class using a string name and declaring <see cref="T:System.Xaml.XamlType" /> information. A <see cref="T:System.Xaml.XamlMember" /> that is constructed with this signature has significant limitations.</summary>
      <param name="name">The string name of the member.</param>
      <param name="declaringType">The <see cref="T:System.Xaml.XamlType" /> information for the declaring type.</param>
      <param name="isAttachable">
        <see langword="true" /> to indicate that the member is attachable; otherwise, <see langword="false" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> or <paramref name="declaringType" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xaml.XamlMember.Equals(System.Object)">
      <summary>Indicates whether the current object is equal to another object.</summary>
      <param name="obj">The object to compare with this object.</param>
      <returns>
        <see langword="true" /> if the current object is equal to the <paramref name="obj" /> parameter; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlMember.Equals(System.Xaml.XamlMember)">
      <summary>Indicates whether the current object is equal to another object of the same type.</summary>
      <param name="other">An object to compare with this object.</param>
      <returns>
        <see langword="true" /> if the current object is equal to the <paramref name="other" /> parameter; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlMember.GetHashCode">
      <summary>Returns the hash code for this object.</summary>
      <returns>An integer hash code.</returns>
    </member>
    <member name="M:System.Xaml.XamlMember.GetXamlNamespaces">
      <summary>Returns a list of XAML namespaces where this XAML member can exist.</summary>
      <returns>A list of XAML namespace identifiers as strings.</returns>
    </member>
    <member name="M:System.Xaml.XamlMember.LookupCustomAttributeProvider">
      <summary>When implemented in a derived class, returns an <see cref="T:System.Reflection.ICustomAttributeProvider" /> implementation.</summary>
      <returns>An <see cref="T:System.Reflection.ICustomAttributeProvider" /> implementation.</returns>
    </member>
    <member name="M:System.Xaml.XamlMember.LookupDeferringLoader">
      <summary>Returns a <see cref="T:System.Xaml.Schema.XamlValueConverter`1" /> object, which is used for deferred loading of XAML declared objects.</summary>
      <returns>A <see cref="T:System.Xaml.Schema.XamlValueConverter`1" /> that has a <see cref="T:System.Xaml.XamlDeferringLoader" /> constraint on the generic.</returns>
    </member>
    <member name="M:System.Xaml.XamlMember.LookupDependsOn">
      <summary>Returns a list of <see cref="T:System.Xaml.XamlMember" /> objects. Items in the list report the members where dependency relationships for initialization order exist relative to this <see cref="T:System.Xaml.XamlMember" />.</summary>
      <returns>A list of <see cref="T:System.Xaml.XamlMember" /> objects.</returns>
    </member>
    <member name="M:System.Xaml.XamlMember.LookupInvoker">
      <summary>Returns a <see cref="T:System.Xaml.Schema.XamlMemberInvoker" /> that is associated with this <see cref="T:System.Xaml.XamlMember" />.</summary>
      <returns>The <see cref="T:System.Xaml.Schema.XamlMemberInvoker" /> information for this <see cref="T:System.Xaml.XamlMember" />; or <see langword="null" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlMember.LookupIsAmbient">
      <summary>Returns whether this <see cref="T:System.Xaml.XamlMember" /> is reported as an ambient property.</summary>
      <returns>
        <see langword="true" /> to report this <see cref="T:System.Xaml.XamlMember" /> as an ambient property; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlMember.LookupIsEvent">
      <summary>Returns whether this <see cref="T:System.Xaml.XamlMember" /> represents an event.</summary>
      <returns>
        <see langword="true" /> to report that this <see cref="T:System.Xaml.XamlMember" /> represents an event; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlMember.LookupIsReadOnly">
      <summary>Returns whether this <see cref="T:System.Xaml.XamlMember" /> represents an intended read-only property.</summary>
      <returns>
        <see langword="true" /> to report this <see cref="T:System.Xaml.XamlMember" /> as an intended read-only property; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlMember.LookupIsReadPublic">
      <summary>Returns whether this <see cref="T:System.Xaml.XamlMember" /> represents a property that has a public <see langword="get" /> accessor.</summary>
      <returns>
        <see langword="true" /> if this <see cref="T:System.Xaml.XamlMember" /> represents a property that has a public <see langword="get" /> accessor; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlMember.LookupIsUnknown">
      <summary>Returns whether this <see cref="T:System.Xaml.XamlMember" /> represents a member that is not resolvable by the backing system that is used for type and member resolution.</summary>
      <returns>
        <see langword="true" /> if this <see cref="T:System.Xaml.XamlMember" /> represents a non-resolvable member; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlMember.LookupIsWriteOnly">
      <summary>Returns whether this <see cref="T:System.Xaml.XamlMember" /> represents a member that has a public <see langword="set" /> accessor but not a public <see langword="get" /> accessor.</summary>
      <returns>
        <see langword="true" /> if this <see cref="T:System.Xaml.XamlMember" /> represents a write-only member; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlMember.LookupIsWritePublic">
      <summary>Returns whether this <see cref="T:System.Xaml.XamlMember" /> represents a member that has a public <see langword="set" /> accessor.</summary>
      <returns>
        <see langword="true" /> if this <see cref="T:System.Xaml.XamlMember" /> represents a writable member; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlMember.LookupMarkupExtensionBracketCharacters">
      <summary>Returns the opening and closing bracket characters for a markup extension.</summary>
      <returns>A collection containing the opening and closing bracket characters.</returns>
    </member>
    <member name="M:System.Xaml.XamlMember.LookupTargetType">
      <summary>Returns the <see cref="T:System.Xaml.XamlType" /> of the type where the <see cref="T:System.Xaml.XamlMember" /> can exist.</summary>
      <returns>The type where the <see cref="T:System.Xaml.XamlMember" /> can exist.</returns>
    </member>
    <member name="M:System.Xaml.XamlMember.LookupType">
      <summary>Returns the <see cref="T:System.Xaml.XamlType" /> of the type that is used by the member.</summary>
      <returns>The <see cref="T:System.Xaml.XamlType" /> of the type that is used by the member.</returns>
    </member>
    <member name="M:System.Xaml.XamlMember.LookupTypeConverter">
      <summary>Returns a type converter implementation that is associated with this <see cref="T:System.Xaml.XamlMember" />.</summary>
      <returns>A <see cref="T:System.Xaml.Schema.XamlValueConverter`1" /> instance with <see cref="T:System.ComponentModel.TypeConverter" /> constraint; or <see langword="null" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlMember.LookupUnderlyingGetter">
      <summary>Returns a <see langword="get" /> accessor that is associated with this <see cref="T:System.Xaml.XamlMember" />.</summary>
      <returns>The <see cref="T:System.Reflection.MethodInfo" /> for the associated <see langword="get" /> accessor; or <see langword="null" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlMember.LookupUnderlyingMember">
      <summary>Returns a CLR type system <see cref="T:System.Reflection.MemberInfo" /> that is associated with this <see cref="T:System.Xaml.XamlMember" />.</summary>
      <returns>A CLR type system <see cref="T:System.Reflection.MemberInfo" /> object that is associated with this <see cref="T:System.Xaml.XamlMember" />; or <see langword="null" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlMember.LookupUnderlyingSetter">
      <summary>Returns a <see langword="set" /> accessor that is associated with this <see cref="T:System.Xaml.XamlMember" />.</summary>
      <returns>The <see cref="T:System.Reflection.MethodInfo" /> for the associated <see langword="set" /> accessor; or <see langword="null" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlMember.LookupValueSerializer">
      <summary>Returns a value serializer implementation that is associated with this <see cref="T:System.Xaml.XamlMember" />.</summary>
      <returns>A <see cref="T:System.Xaml.Schema.XamlValueConverter`1" /> instance with <see cref="T:System.Windows.Markup.ValueSerializer" /> constraint, or <see langword="null" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlMember.op_Equality(System.Xaml.XamlMember,System.Xaml.XamlMember)">
      <summary>Determines whether two specified <see cref="T:System.Xaml.XamlMember" /> objects have the same value.</summary>
      <param name="xamlMember1">A <see cref="T:System.Xaml.XamlMember" /> or <see langword="null" />.</param>
      <param name="xamlMember2">A <see cref="T:System.Xaml.XamlMember" /> or <see langword="null" />.</param>
      <returns>
        <see langword="true" /> if the value of <paramref name="xamlMember1" /> is the same as the value of <paramref name="xamlMember2" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlMember.op_Inequality(System.Xaml.XamlMember,System.Xaml.XamlMember)">
      <summary>Determines whether two specified <see cref="T:System.Xaml.XamlMember" /> objects have different values.</summary>
      <param name="xamlMember1">A <see cref="T:System.Xaml.XamlMember" /> or <see langword="null" />.</param>
      <param name="xamlMember2">A <see cref="T:System.Xaml.XamlMember" /> or <see langword="null" />.</param>
      <returns>
        <see langword="true" /> if the value of <paramref name="xamlMember1" /> differs from the value of <paramref name="xamlMember2" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlMember.ToString">
      <summary>Returns a string representation of this <see cref="T:System.Xaml.XamlMember" />.</summary>
      <returns>A string representation of this <see cref="T:System.Xaml.XamlMember" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlMember.DeclaringType">
      <summary>Gets the <see cref="T:System.Xaml.XamlType" /> for the type that declares the member that is associated with this <see cref="T:System.Xaml.XamlMember" />.</summary>
      <returns>The <see cref="T:System.Xaml.XamlType" /> for the type that declares the member that is associated with this <see cref="T:System.Xaml.XamlMember" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlMember.DeferringLoader">
      <summary>Gets a <see cref="T:System.Xaml.Schema.XamlValueConverter`1" /> object, which is used for deferred loading of XAML declared objects.</summary>
      <returns>A <see cref="T:System.Xaml.Schema.XamlValueConverter`1" /> with <see cref="T:System.Xaml.XamlDeferringLoader" /> constraint on the generic.</returns>
    </member>
    <member name="P:System.Xaml.XamlMember.DependsOn">
      <summary>Gets a list of <see cref="T:System.Xaml.XamlMember" /> objects. These report the members where dependency relationships for initialization order exist relative to this <see cref="T:System.Xaml.XamlMember" />.</summary>
      <returns>A list of <see cref="T:System.Xaml.XamlMember" /> objects.</returns>
    </member>
    <member name="P:System.Xaml.XamlMember.Invoker">
      <summary>Gets the <see cref="T:System.Xaml.Schema.XamlMemberInvoker" /> implementation that is associated with this <see cref="T:System.Xaml.XamlMember" />.</summary>
      <returns>The <see cref="T:System.Xaml.Schema.XamlMemberInvoker" /> implementation that is associated with this <see cref="T:System.Xaml.XamlMember" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlMember.IsAmbient">
      <summary>Gets a value that indicates whether this <see cref="T:System.Xaml.XamlMember" /> is reported as an ambient property.</summary>
      <returns>
        <see langword="true" /> if this <see cref="T:System.Xaml.XamlMember" /> is reported as an ambient property; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlMember.IsAttachable">
      <summary>Gets a value that indicates whether this <see cref="T:System.Xaml.XamlMember" /> is an attachable member.</summary>
      <returns>
        <see langword="true" /> if this <see cref="T:System.Xaml.XamlMember" /> is an attachable member; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlMember.IsDirective">
      <summary>Gets a value that indicates whether this <see cref="T:System.Xaml.XamlMember" /> is a XAML directive.</summary>
      <returns>
        <see langword="true" /> if this <see cref="T:System.Xaml.XamlMember" /> is a XAML directive; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlMember.IsEvent">
      <summary>Gets a value that indicates whether this <see cref="T:System.Xaml.XamlMember" /> represents an event member.</summary>
      <returns>
        <see langword="true" /> if this <see cref="T:System.Xaml.XamlMember" /> represents an event; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlMember.IsNameValid">
      <summary>Gets a value that indicates whether this <see cref="T:System.Xaml.XamlMember" /> is initialized with a valid <see langword="xamlName" /> string as its <see cref="P:System.Xaml.XamlMember.Name" />.</summary>
      <returns>
        <see langword="true" /> if this <see cref="T:System.Xaml.XamlMember" /> is initialized with a valid <see langword="xamlName" /> string; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlMember.IsReadOnly">
      <summary>Gets a value that indicates whether this <see cref="T:System.Xaml.XamlMember" /> represents a read-only member.</summary>
      <returns>
        <see langword="true" /> if this <see cref="T:System.Xaml.XamlMember" /> represents a read-only member; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlMember.IsReadPublic">
      <summary>Gets a value that indicates whether this <see cref="T:System.Xaml.XamlMember" /> represents a member with a callable public <see langword="get" /> accessor.</summary>
      <returns>
        <see langword="true" /> if this <see cref="T:System.Xaml.XamlMember" /> represents a callable public <see langword="get" /> accessor; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlMember.IsUnknown">
      <summary>Gets a value that indicates whether the member is not resolvable by the backing system that is used for type and member resolution.</summary>
      <returns>
        <see langword="true" /> if the member is not resolvable; <see langword="false" /> if the member is resolvable.</returns>
    </member>
    <member name="P:System.Xaml.XamlMember.IsWriteOnly">
      <summary>Gets a value that indicates whether this <see cref="T:System.Xaml.XamlMember" /> represents a write-only member.</summary>
      <returns>
        <see langword="true" /> if this <see cref="T:System.Xaml.XamlMember" /> represents a write-only member; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlMember.IsWritePublic">
      <summary>Gets a value that indicates whether this <see cref="T:System.Xaml.XamlMember" /> represents a member that has a callable public <see langword="set" /> accessor.</summary>
      <returns>
        <see langword="true" /> if this <see cref="T:System.Xaml.XamlMember" /> represents a callable public <see langword="set" /> accessor; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlMember.MarkupExtensionBracketCharacters">
      <summary>Defines the opening and closing bracket characters for a markup extension.</summary>
      <returns>A collection containing the opening and closing bracket characters.</returns>
    </member>
    <member name="P:System.Xaml.XamlMember.Name">
      <summary>Gets the <see langword="xamlName" /> name string that declares this <see cref="T:System.Xaml.XamlMember" />.</summary>
      <returns>The <see langword="xamlName" /> name string that declares this <see cref="T:System.Xaml.XamlMember" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlMember.PreferredXamlNamespace">
      <summary>Gets the single XAML namespace URI that identifies the primary XAML namespace for this <see cref="T:System.Xaml.XamlMember" />.</summary>
      <returns>The identifier for the primary XAML namespace for this <see cref="T:System.Xaml.XamlMember" />, as a string.</returns>
    </member>
    <member name="P:System.Xaml.XamlMember.SerializationVisibility">
      <summary>Gets a <see cref="T:System.ComponentModel.DesignerSerializationVisibility" /> value, which indicates how a visual designer should process the member.</summary>
      <returns>A value of the <see cref="T:System.ComponentModel.DesignerSerializationVisibility" /> enumeration. The default is <see cref="F:System.ComponentModel.DesignerSerializationVisibility.Visible" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlMember.TargetType">
      <summary>Gets the <see cref="T:System.Xaml.XamlType" /> of the type where the <see cref="T:System.Xaml.XamlMember" /> can exist.</summary>
      <returns>The type where the <see cref="T:System.Xaml.XamlMember" /> can exist.</returns>
    </member>
    <member name="P:System.Xaml.XamlMember.Type">
      <summary>Gets the <see cref="T:System.Xaml.XamlType" /> of the type that is used by the member.</summary>
      <returns>The <see cref="T:System.Xaml.XamlType" /> of the type that is used by the member.</returns>
    </member>
    <member name="P:System.Xaml.XamlMember.TypeConverter">
      <summary>Gets a <see cref="T:System.Xaml.Schema.XamlValueConverter`1" /> object, which can be used for type conversion construction of XAML declared objects.</summary>
      <returns>A <see cref="T:System.Xaml.Schema.XamlValueConverter`1" /> value, with a <see cref="T:System.ComponentModel.TypeConverter" /> constraint on the generic.</returns>
    </member>
    <member name="P:System.Xaml.XamlMember.UnderlyingMember">
      <summary>Gets the CLR type system <see cref="T:System.Reflection.MemberInfo" /> that is available for a member that is constructed by <see cref="T:System.Reflection.PropertyInfo" />, <see cref="T:System.Reflection.MethodInfo" />, or <see cref="T:System.Reflection.EventInfo" />.</summary>
      <returns>CLR type system <see cref="T:System.Reflection.MemberInfo" /> information, as cast from the initial constructor parameters. A <see cref="T:System.Xaml.XamlMember" /> that is constructed with the <see cref="M:System.Xaml.XamlMember.#ctor(System.String,System.Xaml.XamlType,System.Boolean)" /> signature returns <see langword="null" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlMember.ValueSerializer">
      <summary>Gets a <see cref="T:System.Xaml.Schema.XamlValueConverter`1" /> object, which is used for value serialization of XAML declared objects.</summary>
      <returns>A <see cref="T:System.Xaml.Schema.XamlValueConverter`1" /> with <see cref="T:System.Windows.Markup.ValueSerializer" /> constraint on the generic.</returns>
    </member>
    <member name="T:System.Xaml.XamlNodeList">
      <summary>Provides a list of XAML nodes, which can be used for scenarios such as writing XAML nodes in a deferred manner.</summary>
    </member>
    <member name="M:System.Xaml.XamlNodeList.#ctor(System.Xaml.XamlSchemaContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlNodeList" /> class, using a provided schema context.</summary>
      <param name="schemaContext">The schema context to use for node operations.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="schemaContext" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xaml.XamlNodeList.#ctor(System.Xaml.XamlSchemaContext,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlNodeList" /> class, using a provided schema context and list size.</summary>
      <param name="schemaContext">The schema context to use for node operations.</param>
      <param name="size">The intended item count of the list.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="schemaContext" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xaml.XamlNodeList.Clear">
      <summary>Clears the items in this list.</summary>
    </member>
    <member name="M:System.Xaml.XamlNodeList.GetReader">
      <summary>Returns a XAML reader implementation delegate.</summary>
      <exception cref="T:System.Xaml.XamlException">The <see cref="T:System.Xaml.XamlNodeList" /> is still in Write mode.  
  
 -or-  
  
 The writer that is used for the node list has no schema context.</exception>
      <returns>A XAML reader implementation delegate.</returns>
    </member>
    <member name="P:System.Xaml.XamlNodeList.Count">
      <summary>Gets the number of nodes in this <see cref="T:System.Xaml.XamlNodeList" />.</summary>
      <returns>The number of nodes in this <see cref="T:System.Xaml.XamlNodeList" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlNodeList.Writer">
      <summary>Gets the associated XAML writer.</summary>
      <returns>The associated XAML writer.</returns>
    </member>
    <member name="T:System.Xaml.XamlNodeQueue">
      <summary>Provides a buffer for writing nodes and reading them again.</summary>
    </member>
    <member name="M:System.Xaml.XamlNodeQueue.#ctor(System.Xaml.XamlSchemaContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlNodeQueue" /> class using a provided XAML schema context.</summary>
      <param name="schemaContext">The schema context to use for node operations.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="xamlSchemaContext" /> is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Xaml.XamlNodeQueue.Count">
      <summary>Gets the number of nodes in the <see cref="T:System.Xaml.XamlNodeQueue" />.</summary>
      <returns>The number of nodes in the <see cref="T:System.Xaml.XamlNodeQueue" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlNodeQueue.IsEmpty">
      <summary>Gets a value that determines whether this <see cref="T:System.Xaml.XamlNodeQueue" /> does not contain nodes.</summary>
      <returns>
        <see langword="true" /> if this <see cref="T:System.Xaml.XamlNodeQueue" /> does not contain nodes; <see langword="false" /> if this <see cref="T:System.Xaml.XamlNodeQueue" /> contains nodes.</returns>
    </member>
    <member name="P:System.Xaml.XamlNodeQueue.Reader">
      <summary>Gets a XAML reader implementation delegate.</summary>
      <exception cref="T:System.Xaml.XamlException">The XAML writer that is used for the node list has no valid XAML schema context.</exception>
      <returns>A XAML reader implementation delegate.</returns>
    </member>
    <member name="P:System.Xaml.XamlNodeQueue.Writer">
      <summary>Gets the associated XAML writer.</summary>
      <returns>The associated XAML writer.</returns>
    </member>
    <member name="T:System.Xaml.XamlNodeType">
      <summary>Describes the type of the node that is currently being processed by a XAML reader.</summary>
    </member>
    <member name="F:System.Xaml.XamlNodeType.EndMember">
      <summary>The reader is at the end of a member node.</summary>
    </member>
    <member name="F:System.Xaml.XamlNodeType.EndObject">
      <summary>The reader is at the end of an object node.</summary>
    </member>
    <member name="F:System.Xaml.XamlNodeType.GetObject">
      <summary>The reader is within an object node and writing a default or implicit value, instead of being a specified object value.</summary>
    </member>
    <member name="F:System.Xaml.XamlNodeType.NamespaceDeclaration">
      <summary>The reader is within an XML namespace declaration.</summary>
    </member>
    <member name="F:System.Xaml.XamlNodeType.None">
      <summary>The reader is not positioned at a true node (for example, the reader might be at end-of-file).</summary>
    </member>
    <member name="F:System.Xaml.XamlNodeType.StartMember">
      <summary>The reader is at the start of a member node.</summary>
    </member>
    <member name="F:System.Xaml.XamlNodeType.StartObject">
      <summary>The reader is at the start of an object node.</summary>
    </member>
    <member name="F:System.Xaml.XamlNodeType.Value">
      <summary>The reader is within a node and processing a value.</summary>
    </member>
    <member name="T:System.Xaml.XamlObjectEventArgs">
      <summary>Provides data for callbacks that can be inserted in the sequence for object initialization and property setting. This influences the object graph that is produced by <see cref="T:System.Xaml.XamlObjectWriter" />.</summary>
    </member>
    <member name="M:System.Xaml.XamlObjectEventArgs.#ctor(System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlObjectEventArgs" /> class.</summary>
      <param name="instance">The object instance that is relevant to the event data.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="instance" /> is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Xaml.XamlObjectEventArgs.ElementLineNumber">
      <summary>Gets or sets the line number of the relevant object instance.</summary>
      <returns>The line number of the element.</returns>
    </member>
    <member name="P:System.Xaml.XamlObjectEventArgs.ElementLinePosition">
      <summary>Gets or sets the position in the line of the relevant object instance.</summary>
      <returns>The position in the line of the element.</returns>
    </member>
    <member name="P:System.Xaml.XamlObjectEventArgs.Instance">
      <summary>Gets the object instance that is relevant to the event data.</summary>
      <returns>The object instance that is relevant to the event data.</returns>
    </member>
    <member name="P:System.Xaml.XamlObjectEventArgs.SourceBamlUri">
      <summary>Gets or sets the BAML URI of the relevant object instance.</summary>
      <returns>The BAML URI of the relevant object instance.</returns>
    </member>
    <member name="T:System.Xaml.XamlObjectReader">
      <summary>Provides a <see cref="T:System.Xaml.XamlReader" /> implementation that reads object graphs and generates a XAML node stream.</summary>
    </member>
    <member name="M:System.Xaml.XamlObjectReader.#ctor(System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlObjectReader" /> class.</summary>
      <param name="instance">The root of the object tree / object graph to read.</param>
    </member>
    <member name="M:System.Xaml.XamlObjectReader.#ctor(System.Object,System.Xaml.XamlObjectReaderSettings)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlObjectReader" /> class with the specified reader settings.</summary>
      <param name="instance">The root of the object tree to read.</param>
      <param name="settings">A settings object.</param>
    </member>
    <member name="M:System.Xaml.XamlObjectReader.#ctor(System.Object,System.Xaml.XamlSchemaContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlObjectReader" /> class with the specified schema context.</summary>
      <param name="instance">The root of the object tree to read.</param>
      <param name="schemaContext">The schema context for the reader to use.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="schemaContext" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xaml.XamlObjectReader.#ctor(System.Object,System.Xaml.XamlSchemaContext,System.Xaml.XamlObjectReaderSettings)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlObjectReader" /> class with the specified schema context and reader settings.</summary>
      <param name="instance">The root of the object tree to read.</param>
      <param name="schemaContext">The schema context for the reader to use.</param>
      <param name="settings">A settings object.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="schemaContext" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xaml.XamlObjectReader.Read">
      <summary>Provides the next XAML node from the source object graph, if a node is available.</summary>
      <returns>
        <see langword="true" /> if a node is available; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlObjectReader.Instance">
      <summary>Gets the object instance that is available at the current reader position.</summary>
      <returns>The object instance at the current reader position; or <see langword="null" /> if the reader position is not on an object.</returns>
    </member>
    <member name="P:System.Xaml.XamlObjectReader.IsEof">
      <summary>Gets a value that reports whether the reader position is at the end of the file.</summary>
      <returns>
        <see langword="true" /> if the reader position is at the end of the file; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlObjectReader.Member">
      <summary>Gets the current <see cref="T:System.Xaml.XamlMember" /> at the reader position, if the reader position is on a <see cref="F:System.Xaml.XamlNodeType.StartMember" />.</summary>
      <returns>The current member; or <see langword="null" /> if the reader position is not on a member.</returns>
    </member>
    <member name="P:System.Xaml.XamlObjectReader.Namespace">
      <summary>Gets the XAML namespace declaration from the current reader position.</summary>
      <returns>The XAML namespace declaration if the reader is positioned on a <see cref="F:System.Xaml.XamlNodeType.NamespaceDeclaration" />; otherwise, <see langword="null" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlObjectReader.NodeType">
      <summary>Gets the type of the current node.</summary>
      <returns>The type of the current node.</returns>
    </member>
    <member name="P:System.Xaml.XamlObjectReader.SchemaContext">
      <summary>Gets an object that provides schema information for the information set.</summary>
      <returns>An object that provides schema information for the information set.</returns>
    </member>
    <member name="P:System.Xaml.XamlObjectReader.Type">
      <summary>Gets the <see cref="T:System.Xaml.XamlType" /> of the object at the current reader position.</summary>
      <returns>The <see cref="T:System.Xaml.XamlType" /> of the object at the current reader position; or <see langword="null" /> if the position is not on an object.</returns>
    </member>
    <member name="P:System.Xaml.XamlObjectReader.Value">
      <summary>Gets the value of the node at the current reader position.</summary>
      <returns>The value of the current node; or <see langword="null" />, if the reader position is not on a <see cref="F:System.Xaml.XamlNodeType.Value" /> node type.</returns>
    </member>
    <member name="T:System.Xaml.XamlObjectReaderException">
      <summary>The exception that is thrown by several <see cref="T:System.Xaml.XamlObjectReader" /> internal helper APIs.</summary>
    </member>
    <member name="M:System.Xaml.XamlObjectReaderException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlObjectReaderException" /> class with a system-supplied message that describes the error.</summary>
    </member>
    <member name="M:System.Xaml.XamlObjectReaderException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlObjectReaderException" /> class with serialized data.</summary>
      <param name="info">The object that holds the serialized object data.</param>
      <param name="context">The contextual information about the source or destination.</param>
    </member>
    <member name="M:System.Xaml.XamlObjectReaderException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlObjectReaderException" /> class with a specified message that describes the error.</summary>
      <param name="message">The message that describes the exception. The caller of this constructor is required to ensure that this string has been localized for the current system culture.</param>
    </member>
    <member name="M:System.Xaml.XamlObjectReaderException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlObjectReaderException" /> class with a specified error message and a reference to the inner exception that is the cause of this exception.</summary>
      <param name="message">The message that describes the exception. The caller of this constructor is required to ensure that this string has been localized for the current system culture.</param>
      <param name="innerException">The exception that is the cause of the current exception. If the <paramref name="innerException" /> parameter is not <see langword="null" />, the current exception is raised in a <see langword="catch" /> block that handles the inner exception.</param>
    </member>
    <member name="T:System.Xaml.XamlObjectReaderSettings">
      <summary>Specifies processing rules or option settings for a <see cref="T:System.Xaml.XamlObjectReader" />.</summary>
    </member>
    <member name="M:System.Xaml.XamlObjectReaderSettings.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlObjectReaderSettings" /> class.</summary>
    </member>
    <member name="P:System.Xaml.XamlObjectReaderSettings.RequireExplicitContentVisibility">
      <summary>Gets or sets a value that determines whether writers that use the associated <see cref="T:System.Xaml.XamlObjectReader" /> for context should use designer settings for writing content explicitly.</summary>
      <returns>
        <see langword="true" /> to specify that writers that use this context should use designer settings for writing any output content in cases where <see cref="P:System.Xaml.XamlMember.IsWritePublic" /> reports <see langword="false" />; <see langword="false" /> if designer settings should be ignored.</returns>
    </member>
    <member name="T:System.Xaml.XamlObjectWriter">
      <summary>Creates an object graph from a source XAML node stream.</summary>
    </member>
    <member name="M:System.Xaml.XamlObjectWriter.#ctor(System.Xaml.XamlSchemaContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlObjectWriter" /> class using  the XAML schema context that is used by a <see cref="T:System.Xaml.XamlReader" />.</summary>
      <param name="schemaContext">A XAML schema context that is shared with the XAML reader that provides nodes for writing.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="schemaContext" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xaml.XamlObjectWriter.#ctor(System.Xaml.XamlSchemaContext,System.Xaml.XamlObjectWriterSettings)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlObjectWriter" /> class using the context that is used by a <see cref="T:System.Xaml.XamlReader" /> and its settings.</summary>
      <param name="schemaContext">A XAML schema context that is shared with the XAML reader that provides nodes for writing.</param>
      <param name="settings">A settings object that specifies certain options for <see cref="T:System.Xaml.XamlObjectWriter" /> behavior and output.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="schemaContext" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xaml.XamlObjectWriter.Clear">
      <summary>Resets all possible frames and clears the object graph.</summary>
    </member>
    <member name="M:System.Xaml.XamlObjectWriter.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Xaml.XamlObjectWriter" />, and optionally, releases the managed resources.</summary>
      <param name="disposing">
        <see langword="true" /> to release the managed resources; otherwise, <see langword="false" />.</param>
    </member>
    <member name="M:System.Xaml.XamlObjectWriter.OnAfterBeginInit(System.Object)">
      <summary>Invokes any <see cref="P:System.Xaml.XamlObjectWriterSettings.AfterBeginInitHandler" /> method that is associated with <see cref="T:System.Xaml.XamlObjectWriterSettings" /> for this <see cref="T:System.Xaml.XamlObjectWriter" />.</summary>
      <param name="value">The object to report on. The object is packaged into <see cref="T:System.Xaml.XamlObjectEventArgs" /> when the handler is invoked.</param>
    </member>
    <member name="M:System.Xaml.XamlObjectWriter.OnAfterEndInit(System.Object)">
      <summary>Invokes any <see cref="P:System.Xaml.XamlObjectWriterSettings.AfterEndInitHandler" /> method that is associated with <see cref="T:System.Xaml.XamlObjectWriterSettings" /> for this <see cref="T:System.Xaml.XamlObjectWriter" />.</summary>
      <param name="value">The object to report on. The value  is packaged into <see cref="T:System.Xaml.XamlObjectEventArgs" /> when the handler is invoked.</param>
    </member>
    <member name="M:System.Xaml.XamlObjectWriter.OnAfterProperties(System.Object)">
      <summary>Invokes any <see cref="P:System.Xaml.XamlObjectWriterSettings.AfterPropertiesHandler" /> referenced method that is associated with <see cref="T:System.Xaml.XamlObjectWriterSettings" /> for this <see cref="T:System.Xaml.XamlObjectWriter" />.</summary>
      <param name="value">The object to report on. This is packaged into <see cref="T:System.Xaml.XamlObjectEventArgs" /> when the handler is invoked.</param>
    </member>
    <member name="M:System.Xaml.XamlObjectWriter.OnBeforeProperties(System.Object)">
      <summary>Invokes any <see cref="P:System.Xaml.XamlObjectWriterSettings.BeforePropertiesHandler" /> referenced method that is associated with <see cref="T:System.Xaml.XamlObjectWriterSettings" /> for this <see cref="T:System.Xaml.XamlObjectWriter" />.</summary>
      <param name="value">The value to pass through <see cref="T:System.Xaml.XamlObjectEventArgs" /> when the handler is invoked.</param>
    </member>
    <member name="M:System.Xaml.XamlObjectWriter.OnSetValue(System.Object,System.Xaml.XamlMember,System.Object)">
      <summary>Invokes any <see cref="P:System.Xaml.XamlObjectWriterSettings.XamlSetValueHandler" /> referenced method that is associated with <see cref="T:System.Xaml.XamlObjectWriterSettings" /> for this writer, as long as <paramref name="handled" /> in <see cref="T:System.Windows.Markup.XamlSetValueEventArgs" /> event data is not <see langword="true" />.</summary>
      <param name="eventSender">The object to report as the source to the handler.</param>
      <param name="member">The name of the property to set, which is passed to the handler as part of <see cref="T:System.Windows.Markup.XamlSetValueEventArgs" />.</param>
      <param name="value">The value to provide for the property that is named by <paramref name="property" />. The value is passed to the handler as part of <see cref="T:System.Windows.Markup.XamlSetValueEventArgs" />.</param>
      <returns>A Boolean value that is set by any associated handler; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlObjectWriter.SetLineInfo(System.Int32,System.Int32)">
      <summary>Implements <see cref="M:System.Xaml.IXamlLineInfoConsumer.SetLineInfo(System.Int32,System.Int32)" /> and provides line information for exceptions.</summary>
      <param name="lineNumber">The line number from the reader.</param>
      <param name="linePosition">The line position from the reader.</param>
    </member>
    <member name="M:System.Xaml.XamlObjectWriter.WriteEndMember">
      <summary>Closes the current member scope, and may write the value of the member scope while it closes. The new scope becomes the parent object scope of the member.</summary>
      <exception cref="T:System.Xaml.XamlObjectWriterException">Failed to create member value from a value node that is encountered between this call and a previous <see langword="StartMember" />.</exception>
    </member>
    <member name="M:System.Xaml.XamlObjectWriter.WriteEndObject">
      <summary>Closes the current object scope in the writer. The new object scope becomes the parent member scope.</summary>
    </member>
    <member name="M:System.Xaml.XamlObjectWriter.WriteGetObject">
      <summary>Writes the conceptual <see langword="StartObject" /> into the object graph when the specified object is a default or implicit value of the parent property. The implicit value comes from information that is obtained from the XAML schema context and backing type information, instead of being specified as an object value in the input XAML node set.</summary>
    </member>
    <member name="M:System.Xaml.XamlObjectWriter.WriteNamespace(System.Xaml.NamespaceDeclaration)">
      <summary>Defines a namespace declaration that applies to the next object scope or member scope.</summary>
      <param name="namespaceDeclaration">The namespace declaration to write.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="namespaceDeclaration" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">A component of <paramref name="namespaceDeclaration" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Xaml.XamlObjectReaderException">Tried to write a XAML namespace node in an invalid scope.</exception>
    </member>
    <member name="M:System.Xaml.XamlObjectWriter.WriteStartMember(System.Xaml.XamlMember)">
      <summary>Writes a new member node into the current object scope, and the scope becomes a new member scope.</summary>
      <param name="property">The XAML property to write. Typically you obtain this value from a XAML reader through the XAML node stream intermediate.</param>
      <exception cref="T:System.Xaml.XamlObjectWriterException">
        <paramref name="property" /> has <see cref="P:System.Xaml.XamlMember.IsUnknown" /> set to <see langword="true" />.  
  
 -or-  
  
 <see cref="P:System.Xaml.XamlLanguage.UnknownContent" /> passed, which indicates that the XAML node stream contained content for an object that does not support content.  
  
 -or-  
  
 Attempted to write a start member in an invalid scope.</exception>
      <exception cref="T:System.Xaml.XamlDuplicateMemberException">
        <paramref name="property" /> specifies a property that is already explicitly set in the parent object scope.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="property" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xaml.XamlObjectWriter.WriteStartObject(System.Xaml.XamlType)">
      <summary>Writes an object node into the current scope, and sets the scope to a new object scope.</summary>
      <param name="xamlType">The type to write. Typically you obtain this value from a XAML reader.</param>
      <exception cref="T:System.Xaml.XamlObjectWriterException">Processing an <see cref="P:System.Xaml.XamlType.IsUnknown" /> type and cannot create an object.  
  
 -or-  
  
 Attempted to write a start object in an invalid scope.</exception>
      <exception cref="T:System.Xaml.XamlParseException">Attempted to write a root object when the root object was already provided in the settings.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="xamlType" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xaml.XamlObjectWriter.WriteValue(System.Object)">
      <summary>Writes a value to the current member scope. If the current scope is inside a collection, dictionary, or array object, the value should be added to the collection, dictionary, or array.</summary>
      <param name="value">The value to write.</param>
      <exception cref="T:System.Xaml.XamlObjectWriterException">Attempted to write a value in an invalid scope.</exception>
    </member>
    <member name="P:System.Xaml.XamlObjectWriter.Result">
      <summary>Gets the last object that was written. Typically only called when the node stream is at its end and the object graph is complete.</summary>
      <returns>The last object written, or <see langword="null" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlObjectWriter.RootNameScope">
      <summary>Gets the <see cref="T:System.Windows.Markup.INameScope" /> service that maintains the XAML namescope for the absolute node root of the XAML object writer.</summary>
      <returns>The <see cref="T:System.Windows.Markup.INameScope" /> service that maintains the XAML namescope for the current root of the object writer.</returns>
    </member>
    <member name="P:System.Xaml.XamlObjectWriter.SchemaContext">
      <summary>Gets the active XAML schema context.</summary>
      <returns>The XAML schema context.</returns>
    </member>
    <member name="P:System.Xaml.XamlObjectWriter.ShouldProvideLineInfo">
      <summary>Gets a value that reports whether a line information service should provide values and therefore, should also call the <see cref="M:System.Xaml.IXamlLineInfoConsumer.SetLineInfo(System.Int32,System.Int32)" /> method when it is relevant.</summary>
      <returns>
        <see langword="true" /> if line information is used by the writer; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Xaml.XamlObjectWriterException">
      <summary>The exception that is thrown when a XAML writer (such as the <see cref="T:System.Xaml.XamlObjectWriter" /> class) encounters an error while attempting to produce object graphs from a XAML node stream.</summary>
    </member>
    <member name="M:System.Xaml.XamlObjectWriterException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlObjectWriterException" /> class with a system-supplied message that describes the error.</summary>
    </member>
    <member name="M:System.Xaml.XamlObjectWriterException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlObjectWriterException" /> class with serialized data.</summary>
      <param name="info">The object that holds the serialized object data.</param>
      <param name="context">The contextual information about the source or destination.</param>
    </member>
    <member name="M:System.Xaml.XamlObjectWriterException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlObjectWriterException" /> class with a specified message that describes the error.</summary>
      <param name="message">The message that describes the exception. The caller of this constructor is required to ensure that this string has been localized for the current system culture.</param>
    </member>
    <member name="M:System.Xaml.XamlObjectWriterException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlObjectWriterException" /> class with a specified error message and a reference to the inner exception that is the cause of this exception.</summary>
      <param name="message">The message that describes the exception. The caller of this constructor is required to ensure that this string has been localized for the current system culture.</param>
      <param name="innerException">The exception that is the cause of the current exception. If the <paramref name="innerException" /> parameter is not <see langword="null" />, the current exception is raised in a <see langword="catch" /> block that handles the inner exception.</param>
    </member>
    <member name="T:System.Xaml.XamlObjectWriterSettings">
      <summary>Provides specific XAML writer settings for <see cref="T:System.Xaml.XamlObjectWriter" />.</summary>
    </member>
    <member name="M:System.Xaml.XamlObjectWriterSettings.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlObjectWriterSettings" /> class.</summary>
    </member>
    <member name="M:System.Xaml.XamlObjectWriterSettings.#ctor(System.Xaml.XamlObjectWriterSettings)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlObjectWriterSettings" /> class that is based on the copy of an existing instance.</summary>
      <param name="settings">The settings instance to copy.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="settings" /> is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Xaml.XamlObjectWriterSettings.AccessLevel">
      <summary>Gets or sets <see cref="T:System.Xaml.Permissions.XamlAccessLevel" /> permissions that the XAML writer observes.</summary>
      <returns>The <see cref="T:System.Xaml.Permissions.XamlAccessLevel" /> permissions.</returns>
    </member>
    <member name="P:System.Xaml.XamlObjectWriterSettings.AfterBeginInitHandler">
      <summary>Gets or sets a reference to a callback that is invoked by the XAML writer at the <see cref="M:System.ComponentModel.ISupportInitialize.BeginInit" /> phase of the object lifetime for each created object.</summary>
      <returns>A callback that is invoked by the XAML writer at the <see cref="M:System.ComponentModel.ISupportInitialize.BeginInit" /> phase of object lifetime.</returns>
    </member>
    <member name="P:System.Xaml.XamlObjectWriterSettings.AfterEndInitHandler">
      <summary>Gets or sets a reference to a callback that is invoked by the XAML writer at the <see cref="M:System.ComponentModel.ISupportInitialize.EndInit" /> phase of the object lifetime for each created object.</summary>
      <returns>A callback that is invoked by the XAML writer at the <see cref="M:System.ComponentModel.ISupportInitialize.EndInit" /> phase of object lifetime.</returns>
    </member>
    <member name="P:System.Xaml.XamlObjectWriterSettings.AfterPropertiesHandler">
      <summary>Gets or sets a reference to a callback that is invoked by the XAML writer at the post-member-write phase of the object lifetime for each created object.</summary>
      <returns>A callback that is invoked by the XAML writer at the post-member-write phase of object lifetime.</returns>
    </member>
    <member name="P:System.Xaml.XamlObjectWriterSettings.BeforePropertiesHandler">
      <summary>Gets or sets a reference to a callback that is invoked by the XAML writer at the pre-member-write phase of the object lifetime for each created object.</summary>
      <returns>A callback that is invoked by the XAML writer at the pre-member-write phase of object lifetime.</returns>
    </member>
    <member name="P:System.Xaml.XamlObjectWriterSettings.ExternalNameScope">
      <summary>Gets or sets the XAML namescope to use for registering names from the XAML writer if <see cref="P:System.Xaml.XamlObjectWriterSettings.RegisterNamesOnExternalNamescope" /> is <see langword="true" />.</summary>
      <returns>The XAML namescope to use for registering names. The default is <see langword="null" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlObjectWriterSettings.IgnoreCanConvert">
      <summary>Gets or sets a value that specifies whether the XAML writer should ignore (not call) <see cref="M:System.ComponentModel.TypeConverter.CanConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Type)" /> implementations on a <see cref="T:System.ComponentModel.TypeConverter" /> in type-converter situations.</summary>
      <returns>
        <see langword="true" /> if the XAML writer ignores <see cref="M:System.ComponentModel.TypeConverter.CanConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Type)" /> implementations; otherwise, <see langword="false" />. The default is <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlObjectWriterSettings.PreferUnconvertedDictionaryKeys">
      <summary>Gets or sets a value that determines whether to disable a default <see cref="T:System.Xaml.XamlObjectWriter" /> feature that runs type conversion on the <paramref name="K" /> component of a <see cref="T:System.Collections.Generic.Dictionary`2" /> before writing the object graph representation.</summary>
      <returns>
        <see langword="true" /> if <paramref name="K" /> type conversion for a <see cref="T:System.Collections.Generic.Dictionary`2" /> object should be disabled. <see langword="false" /> if performing <paramref name="K" /> type conversion for a <see cref="T:System.Collections.Generic.Dictionary`2" /> object applies. The default is <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlObjectWriterSettings.RegisterNamesOnExternalNamescope">
      <summary>Gets or sets a value that determines whether name registration should occur against the specified <see cref="P:System.Xaml.XamlObjectWriterSettings.ExternalNameScope" />.</summary>
      <returns>
        <see langword="true" /> if name registration should occur against the <see cref="P:System.Xaml.XamlObjectWriterSettings.ExternalNameScope" />; <see langword="false" /> if name registration should occur into the parent XAML namescope. The default is <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlObjectWriterSettings.RootObjectInstance">
      <summary>Gets or sets a preexisting root object for <see cref="T:System.Xaml.XamlObjectWriter" /> operations.</summary>
      <returns>A preexisting root object for <see cref="T:System.Xaml.XamlObjectWriter" /> operations.</returns>
    </member>
    <member name="P:System.Xaml.XamlObjectWriterSettings.SkipDuplicatePropertyCheck">
      <summary>Gets or sets a value that determines whether the XAML writer omits to check for the code path that checks for duplicate properties.</summary>
      <returns>
        <see langword="true" /> if the duplicate property check should be omitted; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlObjectWriterSettings.SkipProvideValueOnRoot">
      <summary>Gets or sets a value that indicates whether the <see cref="T:System.Xaml.XamlObjectWriter" /> should omit to call <see langword="ProvideValue" /> on a markup extension, which is relevant when the markup extension represents the root of an object graph.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Xaml.XamlObjectWriter" /> should omit to call <see langword="ProvideValue" /> on a markup extension; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlObjectWriterSettings.SourceBamlUri">
      <summary>Used in the <see langword="BeginInitHandler" /> in place of the actual <see langword="BaseUri" />.</summary>
      <returns>A <see cref="T:System.Uri" /> to be used in place of the actual <see langword="BaseUri" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlObjectWriterSettings.XamlSetValueHandler">
      <summary>Gets or sets the handler to use when the object writer calls into a CLR-implemented <see langword="SetValue" /> for dependency properties.</summary>
      <returns>A handler implementation that handles this case.</returns>
    </member>
    <member name="T:System.Xaml.XamlParseException">
      <summary>The exception that is thrown when a XAML reader cannot process elements of the XAML reader source into a XAML node stream.</summary>
    </member>
    <member name="M:System.Xaml.XamlParseException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlParseException" /> class with a system-supplied message that describes the error.</summary>
    </member>
    <member name="M:System.Xaml.XamlParseException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlParseException" /> class with serialized data.</summary>
      <param name="info">The object that holds the serialized object data.</param>
      <param name="context">The contextual information about the source or destination.</param>
    </member>
    <member name="M:System.Xaml.XamlParseException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlParseException" /> class with a specified message that describes the error.</summary>
      <param name="message">The message that describes the exception. The caller of this constructor is required to ensure that this string has been localized for the current system culture.</param>
    </member>
    <member name="M:System.Xaml.XamlParseException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlParseException" /> class with a specified error message and a reference to the inner exception that is the cause of this exception.</summary>
      <param name="message">The message that describes the exception. The caller of this constructor is required to ensure that this string has been localized for the current system culture.</param>
      <param name="innerException">The exception that is the cause of the current exception. If the <paramref name="innerException" /> parameter is not <see langword="null" />, the current exception is raised in a <see langword="catch" /> block that handles the inner exception.</param>
    </member>
    <member name="T:System.Xaml.XamlReader">
      <summary>Provides base definitions for classes that consume XAML input and produce XAML node streams.</summary>
    </member>
    <member name="M:System.Xaml.XamlReader.#ctor">
      <summary>Initializes the <see cref="T:System.Xaml.XamlReader" /> class.</summary>
    </member>
    <member name="M:System.Xaml.XamlReader.Close">
      <summary>Closes the XAML node stream.</summary>
    </member>
    <member name="M:System.Xaml.XamlReader.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Xaml.XamlReader" />, and optionally, releases the managed resources.</summary>
      <param name="disposing">
        <see langword="true" /> to release the managed resources; otherwise, <see langword="false" />.</param>
    </member>
    <member name="M:System.Xaml.XamlReader.Read">
      <summary>When implemented in a derived class, provides the next XAML node from the source, if a node is available.</summary>
      <returns>
        <see langword="true" /> if a node is available; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlReader.ReadSubtree">
      <summary>Returns a <see cref="T:System.Xaml.XamlReader" /> that is based on the current <see cref="T:System.Xaml.XamlReader" />, where the returned <see cref="T:System.Xaml.XamlReader" /> is used to iterate through a subtree of the XAML node structure.</summary>
      <returns>A new XAML reader instance for the subtree.</returns>
    </member>
    <member name="M:System.Xaml.XamlReader.Skip">
      <summary>Skips the current node and advances the reader position to the next node.</summary>
    </member>
    <member name="M:System.Xaml.XamlReader.System#IDisposable#Dispose">
      <summary>Releases all resources used by the current instance of the <see cref="T:System.Xaml.XamlReader" /> class.</summary>
    </member>
    <member name="P:System.Xaml.XamlReader.IsDisposed">
      <summary>Gets whether <see cref="M:System.Xaml.XamlReader.Dispose(System.Boolean)" /> has been called.</summary>
      <returns>
        <see langword="true" /> if <see cref="M:System.Xaml.XamlReader.Dispose(System.Boolean)" /> has been called; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlReader.IsEof">
      <summary>When implemented in a derived class, gets a value that reports whether the reader position is at end-of-file.</summary>
      <returns>
        <see langword="true" /> if the position is at the conceptual end-of-file of the XAML node stream; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlReader.Member">
      <summary>When implemented in a derived class, gets the current member at the reader position, if the reader position is on a <see cref="F:System.Xaml.XamlNodeType.StartMember" />.</summary>
      <returns>The current member; or <see langword="null" />, if the reader position is not on a member.</returns>
    </member>
    <member name="P:System.Xaml.XamlReader.Namespace">
      <summary>When implemented in a derived class, gets the XAML namespace information from the current node.</summary>
      <returns>The XAML namespace information, if it is available; otherwise, <see langword="null" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlReader.NodeType">
      <summary>When implemented in a derived class, gets the type of the current node.</summary>
      <returns>A value of the <see cref="T:System.Xaml.XamlNodeType" /> enumeration.</returns>
    </member>
    <member name="P:System.Xaml.XamlReader.SchemaContext">
      <summary>When implemented in a derived class, gets an object that provides XAML schema context information for the information set.</summary>
      <returns>An object that provides XAML schema context information for the information set.</returns>
    </member>
    <member name="P:System.Xaml.XamlReader.Type">
      <summary>When implemented in a derived class, gets the <see cref="T:System.Xaml.XamlType" /> of the current node.</summary>
      <returns>The <see cref="T:System.Xaml.XamlType" /> of the current node; or <see langword="null" />, if the current reader position is not on an object.</returns>
    </member>
    <member name="P:System.Xaml.XamlReader.Value">
      <summary>When implemented in a derived class, gets the value of the current node.</summary>
      <returns>The value of the current node; or <see langword="null" />, if the current reader position is not on a <see cref="F:System.Xaml.XamlNodeType.Value" /> node type.</returns>
    </member>
    <member name="T:System.Xaml.XamlReaderSettings">
      <summary>Specifies processing rules or option settings for a <see cref="T:System.Xaml.XamlReader" /> implementation.</summary>
    </member>
    <member name="M:System.Xaml.XamlReaderSettings.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlReaderSettings" /> class.</summary>
    </member>
    <member name="M:System.Xaml.XamlReaderSettings.#ctor(System.Xaml.XamlReaderSettings)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlReaderSettings" /> class based on values in another <see cref="T:System.Xaml.XamlReaderSettings" /> object.</summary>
      <param name="settings">An existing <see cref="T:System.Xaml.XamlReaderSettings" /> object.</param>
    </member>
    <member name="P:System.Xaml.XamlReaderSettings.AllowProtectedMembersOnRoot">
      <summary>Gets or sets a value that indicates whether the root object may include members that have a protected code access model when it reports the XAML type representation.</summary>
      <returns>
        <see langword="true" /> if the root object may include members that have a protected code access model; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlReaderSettings.BaseUri">
      <summary>Gets or sets the base URI that is used to resolve relative paths.</summary>
      <returns>The base URI to use. The default is <see langword="null" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlReaderSettings.IgnoreUidsOnPropertyElements">
      <summary>Gets or sets a value that specifies whether the XAML reader should ignore values for <see langword="x:Uid" /> attributes that exist on property elements.</summary>
      <returns>
        <see langword="true" /> if the reader should ignore values for <see langword="x:Uid" /> attributes on property elements. <see langword="false" /> if the reader should process <see langword="x:Uid" /> attributes on property elements. The default is <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlReaderSettings.LocalAssembly">
      <summary>Gets or sets the object that represents the current local assembly for processing. This assembly information is used for calls to helper APIs such as <see cref="M:System.Xaml.XamlType.GetAllMembers" />.</summary>
      <returns>A CLR reflection <see cref="T:System.Reflection.Assembly" /> object.</returns>
    </member>
    <member name="P:System.Xaml.XamlReaderSettings.ProvideLineInfo">
      <summary>Gets or sets a value that specifies whether the reader can provide line number and position.</summary>
      <returns>
        <see langword="true" /> if the reader can provide line number and position information; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlReaderSettings.ValuesMustBeString">
      <summary>Gets or sets a value that specifies whether the reader enforces that all <see langword="Value" /> nodes are processed as a <see langword="String" /> type.</summary>
      <returns>
        <see langword="true" /> if the reader enforces that only <see langword="String" /> is contained in <see langword="Value" /> nodes; otherwise, <see langword="false" />. The default is <see langword="false" />.</returns>
    </member>
    <member name="T:System.Xaml.XamlSchemaContext">
      <summary>Defines a reusable context for interpreting or mapping XAML types, and the types in the assemblies that underlie them.</summary>
    </member>
    <member name="M:System.Xaml.XamlSchemaContext.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlSchemaContext" /> class.</summary>
    </member>
    <member name="M:System.Xaml.XamlSchemaContext.#ctor(System.Collections.Generic.IEnumerable{System.Reflection.Assembly})">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlSchemaContext" /> class, based on a set of reference assemblies.</summary>
      <param name="referenceAssemblies">An enumerable set of assembly information items. Each assembly information item is specified by an <see cref="T:System.Reflection.Assembly" /> value.</param>
    </member>
    <member name="M:System.Xaml.XamlSchemaContext.#ctor(System.Collections.Generic.IEnumerable{System.Reflection.Assembly},System.Xaml.XamlSchemaContextSettings)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlSchemaContext" /> class, based on a set of reference assemblies and a settings object.</summary>
      <param name="referenceAssemblies">An enumerable set of assembly information. Each assembly information item is specified by an <see cref="T:System.Reflection.Assembly" /> value.</param>
      <param name="settings">The settings object to use to construct the <see cref="T:System.Xaml.XamlSchemaContext" />.</param>
    </member>
    <member name="M:System.Xaml.XamlSchemaContext.#ctor(System.Xaml.XamlSchemaContextSettings)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlSchemaContext" /> class, based on a settings object.</summary>
      <param name="settings">The settings object to use to construct the <see cref="T:System.Xaml.XamlSchemaContext" />.</param>
    </member>
    <member name="M:System.Xaml.XamlSchemaContext.Finalize">
      <summary>Provides a nonstandard <see langword="Finalize" /> implementation that does not invoke <see langword="Dispose" />.</summary>
    </member>
    <member name="M:System.Xaml.XamlSchemaContext.GetAllXamlNamespaces">
      <summary>Searches the assemblies that are held by this <see cref="T:System.Xaml.XamlSchemaContext" /> and returns a set of namespaces.</summary>
      <returns>An enumerable set of namespace identifiers. These identifiers are provided as strings.</returns>
    </member>
    <member name="M:System.Xaml.XamlSchemaContext.GetAllXamlTypes(System.String)">
      <summary>Searches the assemblies that are held by this <see cref="T:System.Xaml.XamlSchemaContext" /> and returns a set of types from the specified XAML namespace.</summary>
      <param name="xamlNamespace">The XAML namespace to return types for.</param>
      <returns>An enumerable set of XAML types. Each XAML type in the set is represented by a <see cref="T:System.Xaml.XamlType" /> object.</returns>
    </member>
    <member name="M:System.Xaml.XamlSchemaContext.GetPreferredPrefix(System.String)">
      <summary>Gets a string that is reported as the preferred prefix for consumers to use when they map the specified XAML namespace.</summary>
      <param name="xmlns">The XAML namespace to get the preferred prefix for.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="xmlns" /> is null.</exception>
      <returns>A string that consumers should use when they map the specified XAML namespace for markup use.</returns>
    </member>
    <member name="M:System.Xaml.XamlSchemaContext.GetValueConverter``1(System.Type,System.Xaml.XamlType)">
      <summary>Returns a value converter that can convert to the requested <paramref name="targetType" />.</summary>
      <param name="converterType">The converter implementation.</param>
      <param name="targetType">The desired destination type for the converter.</param>
      <typeparam name="TConverterBase">The specific converter base class.</typeparam>
      <returns>A <see cref="T:System.Xaml.Schema.XamlValueConverter`1" /> with a constraint that matches the constraint that is put on the method call.</returns>
    </member>
    <member name="M:System.Xaml.XamlSchemaContext.GetXamlDirective(System.String,System.String)">
      <summary>Returns a <see cref="T:System.Xaml.XamlDirective" /> value that represents a directive, either for the XAML language or for a particular implementation.</summary>
      <param name="xamlNamespace">The XAML namespace that contains the named directive.</param>
      <param name="name">The name of the directive to obtain.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="xamlNamespace" /> or <paramref name="name" /> is <see langword="null" />.</exception>
      <returns>A <see cref="T:System.Xaml.XamlDirective" /> object that represents the requested directive.</returns>
    </member>
    <member name="M:System.Xaml.XamlSchemaContext.GetXamlType(System.String,System.String,System.Xaml.XamlType[])">
      <summary>Returns a <see cref="T:System.Xaml.XamlType" /> that is based on a XAML namespace and a string for the type name. This signature can specify the type arguments for cases where the desired type is a generic type.</summary>
      <param name="xamlNamespace">The XAML namespace that contains the desired type.</param>
      <param name="name">The string name of the desired type.</param>
      <param name="typeArguments">The initialization type arguments for a generic type.</param>
      <returns>The <see cref="T:System.Xaml.XamlType" /> that matches the input criteria.</returns>
    </member>
    <member name="M:System.Xaml.XamlSchemaContext.GetXamlType(System.Type)">
      <summary>Returns a <see cref="T:System.Xaml.XamlType" /> that is based on a CLR type identifier.</summary>
      <param name="type">The type to get a <see cref="T:System.Xaml.XamlType" /> for.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="type" /> is <see langword="null" />.</exception>
      <returns>The <see cref="T:System.Xaml.XamlType" /> that matches the input <paramref name="type" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlSchemaContext.GetXamlType(System.Xaml.Schema.XamlTypeName)">
      <summary>Returns a <see cref="T:System.Xaml.XamlType" /> that is based on a XAML system type name.</summary>
      <param name="xamlTypeName">The XAML type name to get a <see cref="T:System.Xaml.XamlType" /> for.</param>
      <exception cref="T:System.ArgumentException">A component of <paramref name="xamlTypeName" /> (<see cref="P:System.Xaml.Schema.XamlTypeName.Name" /> or <see cref="P:System.Xaml.Schema.XamlTypeName.Namespace" />) is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="xamlTypeName" /> is <see langword="null" />.</exception>
      <returns>The <see cref="T:System.Xaml.XamlType" /> that matches the input <paramref name="xamlTypeName" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlSchemaContext.OnAssemblyResolve(System.String)">
      <summary>Called when operations use this schema context to resolve an assembly that is required in order to resolve the XAML namespaces that it contains.</summary>
      <param name="assemblyName">The name of the assembly to load.</param>
      <returns>The resolved assembly.</returns>
    </member>
    <member name="M:System.Xaml.XamlSchemaContext.TryGetCompatibleXamlNamespace(System.String,System.String@)">
      <summary>Returns the success or failure of a request for a compatible XAML namespace. A successful request reports that XAML namespace as an out parameter.</summary>
      <param name="xamlNamespace">The <see langword="xmlns" /> string for the XAML namespace to be checked for a compatible result.</param>
      <param name="compatibleNamespace">When this method returns, the <see langword="xmlns" /> string for a compatible XAML namespace request. This might be identical to <paramref name="xamlNamespace" /> if the method returns <see langword="false" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="xamlNamespace" /> is <see langword="null" />.</exception>
      <returns>
        <see langword="true" /> if <paramref name="compatibleNamespace" /> contains a usable result; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlSchemaContext.FullyQualifyAssemblyNamesInClrNamespaces">
      <summary>Gets a value that specifies whether a XAML schema and its context use fully qualified assembly names in the values that are returned by the lookup API.</summary>
      <returns>
        <see langword="true" /> if a XAML schema and its context use fully qualified assembly names in the values that are returned by the lookup APIs; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlSchemaContext.ReferenceAssemblies">
      <summary>Gets an enumerable set of reference assemblies for the XAML schema context.</summary>
      <returns>An enumerable set of reference assemblies for the schema context. Each assembly information item is specified by an <see cref="T:System.Reflection.Assembly" /> value.</returns>
    </member>
    <member name="P:System.Xaml.XamlSchemaContext.SupportMarkupExtensionsWithDuplicateArity">
      <summary>Gets a value that specifies whether a XAML schema and its context support markup extensions that have two constructors with the same arity (number of input parameters).</summary>
      <returns>
        <see langword="true" /> if the schema context permits markup extensions that have duplicate arity; otherwise, <see langword="false" />. The default is <see langword="false" />.</returns>
    </member>
    <member name="T:System.Xaml.XamlSchemaContextSettings">
      <summary>Provides optional settings for a <see cref="T:System.Xaml.XamlSchemaContext" />.</summary>
    </member>
    <member name="M:System.Xaml.XamlSchemaContextSettings.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlSchemaContextSettings" /> class.</summary>
    </member>
    <member name="M:System.Xaml.XamlSchemaContextSettings.#ctor(System.Xaml.XamlSchemaContextSettings)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlSchemaContextSettings" /> class by copying the values of an existing <see cref="T:System.Xaml.XamlSchemaContextSettings" /> instance.</summary>
      <param name="settings">An existing <see cref="T:System.Xaml.XamlSchemaContextSettings" />.</param>
    </member>
    <member name="P:System.Xaml.XamlSchemaContextSettings.FullyQualifyAssemblyNamesInClrNamespaces">
      <summary>Gets or sets a value that specifies whether a XAML schema and its context use fully qualified assembly names in the values that are returned by the lookup API.</summary>
      <returns>
        <see langword="true" /> if a XAML schema and its context use fully qualified assembly names in the values that are returned by the lookup APIs; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlSchemaContextSettings.SupportMarkupExtensionsWithDuplicateArity">
      <summary>Gets or sets a value that specifies whether a XAML schema context allows for markup extensions that have two constructors with the same arity (number of input parameters).</summary>
      <returns>
        <see langword="true" /> if the schema context allows for markup extensions that have duplicate arity; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Xaml.XamlSchemaException">
      <summary>The exception that is thrown when a binding system or another schema representation system for XAML reports an exception to the schema context.</summary>
    </member>
    <member name="M:System.Xaml.XamlSchemaException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlSchemaException" /> class with a system-supplied message that describes the error.</summary>
    </member>
    <member name="M:System.Xaml.XamlSchemaException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlSchemaException" /> class with serialized data.</summary>
      <param name="info">The object that holds the serialized object data.</param>
      <param name="context">The contextual information about the source or destination.</param>
    </member>
    <member name="M:System.Xaml.XamlSchemaException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlSchemaException" /> class with a specified message that describes the error.</summary>
      <param name="message">The message that describes the exception. The caller of this constructor is required to ensure that this string has been localized for the current system culture.</param>
    </member>
    <member name="M:System.Xaml.XamlSchemaException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlSchemaException" /> class with a specified error message and a reference to the inner exception that is the cause of this exception.</summary>
      <param name="message">The message that describes the exception. The caller of this constructor is required to ensure that this string has been localized for the current system culture.</param>
      <param name="innerException">The exception that is the cause of the current exception. If the <paramref name="innerException" /> parameter is not <see langword="null" />, the current exception is raised in a <see langword="catch" /> block that handles the inner exception.</param>
    </member>
    <member name="T:System.Xaml.XamlServices">
      <summary>Provides higher-level services (static methods) for the common XAML tasks of reading XAML and writing an object graph; or reading an object graph and writing XAML file output for serialization purposes.</summary>
    </member>
    <member name="M:System.Xaml.XamlServices.Load(System.IO.Stream)">
      <summary>Loads a <see cref="T:System.IO.Stream" /> source for a XAML reader and writes its output as an object graph.</summary>
      <param name="stream">The stream to load as input.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> is <see langword="null" />.</exception>
      <returns>The object graph that is written as output.</returns>
    </member>
    <member name="M:System.Xaml.XamlServices.Load(System.IO.TextReader)">
      <summary>Creates a XAML reader from a <see cref="T:System.IO.TextReader" />, and returns an object graph.</summary>
      <param name="textReader">The <see cref="T:System.IO.TextReader" /> to use as the basis for the created <see cref="T:System.Xml.XmlReader" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="textReader" /> is <see langword="null" />.</exception>
      <returns>The object graph that is returned.</returns>
    </member>
    <member name="M:System.Xaml.XamlServices.Load(System.String)">
      <summary>Loads a <see cref="T:System.IO.Stream" /> source for a XAML reader and returns an object graph.</summary>
      <param name="fileName">The file name to load and use as source.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="fileName" /> input is <see langword="null" />.</exception>
      <returns>The object graph that is returned.</returns>
    </member>
    <member name="M:System.Xaml.XamlServices.Load(System.Xaml.XamlReader)">
      <summary>Loads a specific XAML reader implementation and returns an object graph.</summary>
      <param name="xamlReader">The XAML reader implementation to use as the reader for this <see langword="Load" /> operation.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="xamlReader" /> input is <see langword="null" />.</exception>
      <returns>The object graph that is returned.</returns>
    </member>
    <member name="M:System.Xaml.XamlServices.Load(System.Xml.XmlReader)">
      <summary>Loads a specific XML reader implementation and returns an object graph.</summary>
      <param name="xmlReader">The <see cref="T:System.Xml.XmlReader" /> implementation to use as the reader for this <see langword="Load" /> operation.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="xamlReader" /> input is <see langword="null" />.</exception>
      <returns>The output object graph.</returns>
    </member>
    <member name="M:System.Xaml.XamlServices.Parse(System.String)">
      <summary>Reads XAML as string output and returns an object graph.</summary>
      <param name="xaml">The XAML string input to parse.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="xaml" /> input is <see langword="null" />.</exception>
      <returns>The object graph that is returned.</returns>
    </member>
    <member name="M:System.Xaml.XamlServices.Save(System.IO.Stream,System.Object)">
      <summary>Processes a provided object graph into a XAML node representation and then into an output stream for serialization.</summary>
      <param name="stream">The destination stream.</param>
      <param name="instance">The root of the object graph to process.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> input is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xaml.XamlServices.Save(System.IO.TextWriter,System.Object)">
      <summary>Processes a provided object graph into a XAML node representation and then into an output that goes to the provided <see cref="T:System.IO.TextWriter" />.</summary>
      <param name="writer">The <see cref="T:System.IO.TextWriter" /> that writes the output.</param>
      <param name="instance">The root of the object graph to process.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="writer" /> input is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xaml.XamlServices.Save(System.Object)">
      <summary>Processes a provided object tree into a XAML node representation, and returns a string representation of the output XAML.</summary>
      <param name="instance">The root of the object graph to process.</param>
      <returns>The XAML markup output as a string.</returns>
    </member>
    <member name="M:System.Xaml.XamlServices.Save(System.String,System.Object)">
      <summary>Processes a provided object graph into a XAML node representation and then writes it to an output file at a provided location.</summary>
      <param name="fileName">The name and location of the file to write the output to.</param>
      <param name="instance">The root of the object graph to process.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fileName" /> is an empty string.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="fileName" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xaml.XamlServices.Save(System.Xaml.XamlWriter,System.Object)">
      <summary>Processes a provided object graph into a XAML node representation and then writes it to the provided XAML writer.</summary>
      <param name="writer">The <see cref="T:System.Xaml.XamlWriter" /> implementation to use.</param>
      <param name="instance">The root of the object graph to process.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="writer" /> input is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xaml.XamlServices.Save(System.Xml.XmlWriter,System.Object)">
      <summary>Processes a provided object graph into a XAML node representation and then writes it to the provided <see cref="T:System.Xml.XmlWriter" />.</summary>
      <param name="writer">The <see cref="T:System.Xml.XmlWriter" /> implementation to use.</param>
      <param name="instance">The root of the object graph to process.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="writer" /> input is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xaml.XamlServices.Transform(System.Xaml.XamlReader,System.Xaml.XamlWriter)">
      <summary>Connects a <see cref="T:System.Xaml.XamlReader" /> and a <see cref="T:System.Xaml.XamlWriter" /> to use a common XAML node set intermediary. Potentially transforms the content, depending on the types of readers and writers that are provided.</summary>
      <param name="xamlReader">The <see cref="T:System.Xaml.XamlReader" /> implementation to use.</param>
      <param name="xamlWriter">The <see cref="T:System.Xaml.XamlWriter" /> to use.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="xamlReader" /> or <paramref name="xamlWriter" /> input is <see langword="null" />.</exception>
      <exception cref="T:System.Xaml.XamlException">The XAML schema context does not match between the provided <paramref name="xamlReader" /> and <paramref name="xamlWriter" />.</exception>
    </member>
    <member name="M:System.Xaml.XamlServices.Transform(System.Xaml.XamlReader,System.Xaml.XamlWriter,System.Boolean)">
      <summary>Connects a <see cref="T:System.Xaml.XamlReader" /> and a <see cref="T:System.Xaml.XamlWriter" /> to use a common XAML node set intermediary. Potentially transforms the content, depending on the types of readers and writers that are provided. Provides a parameter for specifying whether to close the writer after the call is completed.</summary>
      <param name="xamlReader">The <see cref="T:System.Xaml.XamlReader" /> implementation to use.</param>
      <param name="xamlWriter">The <see cref="T:System.Xaml.XamlWriter" /> to use.</param>
      <param name="closeWriter">
        <see langword="true" /> to close the writer after the call is complete; <see langword="false" /> to leave the writer active at the last written position.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="xamlReader" /> or <paramref name="xamlWriter" /> input is <see langword="null" />.</exception>
      <exception cref="T:System.Xaml.XamlException">The XAML schema context does not match between the provided <paramref name="xamlReader" /> and <paramref name="xamlWriter" />.</exception>
    </member>
    <member name="T:System.Xaml.XamlType">
      <summary>Reports information about XAML types as part of the overall XAML system that is implemented in .NET XAML Services.</summary>
    </member>
    <member name="M:System.Xaml.XamlType.#ctor(System.String,System.Collections.Generic.IList{System.Xaml.XamlType},System.Xaml.XamlSchemaContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlType" /> class based on a string name for the type.</summary>
      <param name="typeName">The name of the type to create.</param>
      <param name="typeArguments">The type arguments for a <see cref="T:System.Xaml.XamlType" /> that represents a generic type. Can be (and often is) <see langword="null" />, which indicates that the represented type is not a generic type.</param>
      <param name="schemaContext">XAML schema context for XAML readers and XAML writers.</param>
      <exception cref="T:System.ArgumentNullException">One or more of <paramref name="typeName" /> or <paramref name="schemaContext" /> are <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xaml.XamlType.#ctor(System.String,System.String,System.Collections.Generic.IList{System.Xaml.XamlType},System.Xaml.XamlSchemaContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlType" /> class based on the XAML namespace and a string name for the type. This constructor is exclusively for analysis and XAML-node recording of type usages that are known to not have backing in the supporting type system and XAML schema context.</summary>
      <param name="unknownTypeNamespace">The XAML namespace for the type, as a string.</param>
      <param name="unknownTypeName">The name of the type in the provided <paramref name="unknownTypeNamespace" /> XAML namespace.</param>
      <param name="typeArguments">The type arguments for a <see cref="T:System.Xaml.XamlType" /> that represents a generic type. Can be (and often is) <see langword="null" />, which indicates that the represented type is not a generic type.</param>
      <param name="schemaContext">XAML schema context for XAML readers or XAML writers.</param>
      <exception cref="T:System.ArgumentNullException">One or more of <paramref name="unknownTypeNamespace" />, <paramref name="unknownTypeName" />, or <paramref name="schemaContext" /> are <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xaml.XamlType.#ctor(System.Type,System.Xaml.XamlSchemaContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlType" /> class based on the underlying CLR type information.</summary>
      <param name="underlyingType">The underlying CLR <see cref="T:System.Type" /> for the XAML type to construct.</param>
      <param name="schemaContext">XAML schema context for XAML readers or XAML writers.</param>
      <exception cref="T:System.ArgumentNullException">One or more of <paramref name="underlyingType" /> or <paramref name="schemaContext" /> are <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xaml.XamlType.#ctor(System.Type,System.Xaml.XamlSchemaContext,System.Xaml.Schema.XamlTypeInvoker)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlType" /> class based on underlying type information and a <see cref="T:System.Xaml.Schema.XamlTypeInvoker" /> implementation.</summary>
      <param name="underlyingType">The underlying type for the XAML type to construct.</param>
      <param name="schemaContext">XAML schema context for the XAML reader.</param>
      <param name="invoker">The <see cref="T:System.Xaml.Schema.XamlTypeInvoker" /> implementation that handles run-time reflection calls against the <see cref="T:System.Xaml.XamlType" />.</param>
      <exception cref="T:System.ArgumentNullException">One or more of <paramref name="underlyingType" /> or <paramref name="schemaContext" /> are <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xaml.XamlType.CanAssignTo(System.Xaml.XamlType)">
      <summary>Returns a value that indicates whether an instance of this <see cref="T:System.Xaml.XamlType" /> has the specified <see cref="T:System.Xaml.XamlType" /> in its list of assignable types.</summary>
      <param name="xamlType">The type to check against the current <see cref="T:System.Xaml.XamlType" /> .</param>
      <returns>
        <see langword="true" /> if <paramref name="xamlType" /> is in the assignable types list; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlType.Equals(System.Object)">
      <summary>Indicates whether the current object is equal to another object.</summary>
      <param name="obj">The object to compare with this object.</param>
      <returns>
        <see langword="true" /> if the current object is equal to the <paramref name="obj" /> parameter; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlType.Equals(System.Xaml.XamlType)">
      <summary>Indicates whether the current object is equal to another object of the same type.</summary>
      <param name="other">An object to compare with this object.</param>
      <returns>
        <see langword="true" /> if the current object is equal to the <paramref name="other" /> parameter; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlType.GetAliasedProperty(System.Xaml.XamlDirective)">
      <summary>Returns the XAML member that is aliased to a XAML directive by this <see cref="T:System.Xaml.XamlType" />.</summary>
      <param name="directive">The directive for which to find the aliased member.</param>
      <returns>The aliased member, if found; otherwise, <see langword="null" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlType.GetAllAttachableMembers">
      <summary>Returns a collection that contains all the attachable properties that are exposed by this <see cref="T:System.Xaml.XamlType" />.</summary>
      <returns>A collection that contains zero or more <see cref="T:System.Xaml.XamlMember" /> values.</returns>
    </member>
    <member name="M:System.Xaml.XamlType.GetAllMembers">
      <summary>Returns a collection that contains all the members that are exposed by this <see cref="T:System.Xaml.XamlType" />.</summary>
      <returns>A collection that contains zero or more <see cref="T:System.Xaml.XamlMember" /> values.</returns>
    </member>
    <member name="M:System.Xaml.XamlType.GetAttachableMember(System.String)">
      <summary>Returns a <see cref="T:System.Xaml.XamlMember" /> representing a specific named attachable member of this <see cref="T:System.Xaml.XamlType" />.</summary>
      <param name="name">The name of the attachable member to get, in <c>ownerTypeName.MemberName</c> form.</param>
      <returns>A <see cref="T:System.Xaml.XamlMember" /> object for the requested attachable member; otherwise, <see langword="null" />, if no attachable member by that name exists.</returns>
    </member>
    <member name="M:System.Xaml.XamlType.GetHashCode">
      <summary>Returns the hash code for this object.</summary>
      <returns>An integer hash code.</returns>
    </member>
    <member name="M:System.Xaml.XamlType.GetMember(System.String)">
      <summary>Returns a <see cref="T:System.Xaml.XamlMember" /> for a specific named member from this <see cref="T:System.Xaml.XamlType" />.</summary>
      <param name="name">The name of the member to get (as a string).</param>
      <returns>The <see cref="T:System.Xaml.XamlMember" /> information for the member, if such a member was found; otherwise, <see langword="null" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlType.GetPositionalParameters(System.Int32)">
      <summary>For markup extension types, returns the types of the positional parameters that are supported in a specific markup extension usage for this <see cref="T:System.Xaml.XamlType" />.</summary>
      <param name="parameterCount">The count (arity) of the particular syntax or constructor mode that you want information about.</param>
      <returns>A list of <see cref="T:System.Xaml.XamlType" /> values, where each <see cref="T:System.Xaml.XamlType" /> is the type for that position in the syntax. You must specify the types in the same order when you supply markup input for the markup extension.</returns>
    </member>
    <member name="M:System.Xaml.XamlType.GetXamlNamespaces">
      <summary>Returns a list of string identifiers for XAML namespaces that the type is included in.</summary>
      <returns>A list of string values, where each string is the URI identifier for a XAML namespace.</returns>
    </member>
    <member name="M:System.Xaml.XamlType.LookupAliasedProperty(System.Xaml.XamlDirective)">
      <summary>Returns the XAML member that is aliased to a XAML directive by this <see cref="T:System.Xaml.XamlType" />.</summary>
      <param name="directive">The directive for which to find the aliased member.</param>
      <returns>The aliased member, if found; otherwise, <see langword="null" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlType.LookupAllAttachableMembers">
      <summary>Returns an enumerable set that contains all attachable properties that are exposed by this <see cref="T:System.Xaml.XamlType" />.</summary>
      <returns>An enumerable set that contains zero or more <see cref="T:System.Xaml.XamlMember" /> values; otherwise, <see langword="null" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlType.LookupAllMembers">
      <summary>Returns an enumerable set that contains all the members that are exposed by this <see cref="T:System.Xaml.XamlType" />.</summary>
      <returns>An enumerable set that contains zero or more <see cref="T:System.Xaml.XamlMember" /> values.</returns>
    </member>
    <member name="M:System.Xaml.XamlType.LookupAllowedContentTypes">
      <summary>Returns a list of the types that are usable as the <see cref="P:System.Xaml.XamlType.ContentProperty" /> value for this <see cref="T:System.Xaml.XamlType" />.</summary>
      <returns>A list of possible content types.</returns>
    </member>
    <member name="M:System.Xaml.XamlType.LookupAttachableMember(System.String)">
      <summary>Returns a <see cref="T:System.Xaml.XamlMember" /> for a specific named attachable from this <see cref="T:System.Xaml.XamlType" />.</summary>
      <param name="name">The name of the attachable member to get, in <c>ownerTypeName.MemberName</c> form.</param>
      <returns>A <see cref="T:System.Xaml.XamlMember" /> object for the requested attachable member; otherwise, <see langword="null" />, if no attachable member by that name exists.</returns>
    </member>
    <member name="M:System.Xaml.XamlType.LookupBaseType">
      <summary>Returns the <see cref="T:System.Xaml.XamlType" /> for the immediate base type of this XAML type. Determination of this value is based on the underlying type of this <see cref="T:System.Xaml.XamlType" /> and schema context.</summary>
      <returns>The <see cref="T:System.Xaml.XamlType" /> for the immediate base type of this XAML type.</returns>
    </member>
    <member name="M:System.Xaml.XamlType.LookupCollectionKind">
      <summary>Returns a value of the <see cref="T:System.Xaml.Schema.XamlCollectionKind" /> enumeration that declares which specific collection type this <see cref="T:System.Xaml.XamlType" /> uses.</summary>
      <returns>A value of the <see cref="T:System.Xaml.Schema.XamlCollectionKind" /> enumeration.</returns>
    </member>
    <member name="M:System.Xaml.XamlType.LookupConstructionRequiresArguments">
      <summary>Returns a value that indicates whether this <see cref="T:System.Xaml.XamlType" /> must have arguments (generic constraints through <see langword="x:TypeArguments" />, initialization text, or other XAML techniques) to construct a valid instance of the type.</summary>
      <returns>
        <see langword="true" /> if construction of an instance requires some argument value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlType.LookupContentProperty">
      <summary>Returns <see cref="T:System.Xaml.XamlMember" /> information for the content property of this <see cref="T:System.Xaml.XamlType" />.</summary>
      <returns>
        <see cref="T:System.Xaml.XamlMember" /> information for the content property of this <see cref="T:System.Xaml.XamlType" />. May be <see langword="null" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlType.LookupContentWrappers">
      <summary>Gets a list of <see cref="T:System.Xaml.XamlType" /> values that represent the content wrappers for this <see cref="T:System.Xaml.XamlType" />.</summary>
      <returns>A list of <see cref="T:System.Xaml.XamlType" /> values that represent the content wrappers for this <see cref="T:System.Xaml.XamlType" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlType.LookupCustomAttributeProvider">
      <summary>When implemented in a derived class, returns an <see cref="T:System.Reflection.ICustomAttributeProvider" /> implementation.</summary>
      <returns>An <see cref="T:System.Reflection.ICustomAttributeProvider" /> implementation.</returns>
    </member>
    <member name="M:System.Xaml.XamlType.LookupDeferringLoader">
      <summary>Returns a <see cref="T:System.Xaml.Schema.XamlValueConverter`1" /> object, which is used for deferred loading of XAML-declared objects.</summary>
      <returns>A <see cref="T:System.Xaml.Schema.XamlValueConverter`1" /> that has a <see cref="T:System.Xaml.XamlDeferringLoader" /> constraint on the generic.</returns>
    </member>
    <member name="M:System.Xaml.XamlType.LookupInvoker">
      <summary>Returns a <see cref="T:System.Xaml.Schema.XamlTypeInvoker" /> that is associated with this <see cref="T:System.Xaml.XamlType" />.</summary>
      <returns>The <see cref="T:System.Xaml.Schema.XamlTypeInvoker" /> information for this <see cref="T:System.Xaml.XamlType" />; otherwise, <see langword="null" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlType.LookupIsAmbient">
      <summary>Returns a value that indicates whether this <see cref="T:System.Xaml.XamlType" /> represents an ambient type, as per the XAML definition.</summary>
      <returns>
        <see langword="true" /> if this <see cref="T:System.Xaml.XamlType" /> represents an ambient type; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlType.LookupIsConstructible">
      <summary>Returns a value that indicates whether this <see cref="T:System.Xaml.XamlType" /> represents a constructible type, as per the XAML definition.</summary>
      <returns>
        <see langword="true" /> if this <see cref="T:System.Xaml.XamlType" /> represents a constructible type; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlType.LookupIsMarkupExtension">
      <summary>Returns a value that indicates whether this <see cref="T:System.Xaml.XamlType" /> represents a markup extension.</summary>
      <returns>
        <see langword="true" /> if this <see cref="T:System.Xaml.XamlType" /> represents a markup extension; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlType.LookupIsNameScope">
      <summary>Returns a value that indicates whether this <see cref="T:System.Xaml.XamlType" /> represents a XAML namescope, as per the XAML definition.</summary>
      <returns>
        <see langword="true" /> if this <see cref="T:System.Xaml.XamlType" /> represents a XAML namescope; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlType.LookupIsNullable">
      <summary>Returns a value that indicates whether this <see cref="T:System.Xaml.XamlType" /> represents a nullable type, as per the XAML definition.</summary>
      <returns>
        <see langword="true" /> if this <see cref="T:System.Xaml.XamlType" /> represents a nullable type; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlType.LookupIsPublic">
      <summary>Returns a value that indicates whether this <see cref="T:System.Xaml.XamlType" /> represents a public type in the relevant type system.</summary>
      <returns>
        <see langword="true" /> if this <see cref="T:System.Xaml.XamlType" /> represents a public type; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlType.LookupIsUnknown">
      <summary>Returns a value that indicates whether this <see cref="T:System.Xaml.XamlType" /> represents a type that cannot be resolved in the underlying type system.</summary>
      <returns>
        <see langword="true" /> if this <see cref="T:System.Xaml.XamlType" /> represents a nonresolvable type; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlType.LookupIsWhitespaceSignificantCollection">
      <summary>Returns a value that indicates whether this <see cref="T:System.Xaml.XamlType" /> represents a whitespace significant collection, as per the XML definition.</summary>
      <returns>
        <see langword="true" /> if this <see cref="T:System.Xaml.XamlType" /> represents a white-space significant collection; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlType.LookupIsXData">
      <summary>Returns a value that indicates whether this <see cref="T:System.Xaml.XamlType" /> represents XML <see langword="XDATA" />, as per the XAML definition.</summary>
      <returns>
        <see langword="true" /> if this <see cref="T:System.Xaml.XamlType" /> represents <see langword="XDATA" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlType.LookupItemType">
      <summary>Returns a value that provides the type information for the <see langword="Items" /> property of this <see cref="T:System.Xaml.XamlType" />.</summary>
      <returns>A <see cref="T:System.Xaml.XamlType" /> object for the type of the items in the collection; otherwise, <see langword="null" /> if this <see cref="T:System.Xaml.XamlType" /> does not represent a collection.</returns>
    </member>
    <member name="M:System.Xaml.XamlType.LookupKeyType">
      <summary>Returns a value that provides the type information for the key property of this <see cref="T:System.Xaml.XamlType" />, if the <see cref="T:System.Xaml.XamlType" /> represents a dictionary.</summary>
      <returns>A <see cref="T:System.Xaml.XamlType" /> object for the type of the key for dictionary usage, or <see langword="null" /> if this <see cref="T:System.Xaml.XamlType" /> does not represent a dictionary.</returns>
    </member>
    <member name="M:System.Xaml.XamlType.LookupMarkupExtensionReturnType">
      <summary>Returns a value that provides the type information for the returned <see langword="ProvideValue" /> of this <see cref="T:System.Xaml.XamlType" />, if it represents a markup extension.</summary>
      <returns>A <see cref="T:System.Xaml.XamlType" /> object for the return type for markup extension usage; otherwise, <see langword="null" />, if this <see cref="T:System.Xaml.XamlType" /> does not represent a markup extension.</returns>
    </member>
    <member name="M:System.Xaml.XamlType.LookupMember(System.String,System.Boolean)">
      <summary>Returns the <see cref="T:System.Xaml.XamlMember" /> for a specific named member from this <see cref="T:System.Xaml.XamlType" />.</summary>
      <param name="name">The name of the member to get (as a string).</param>
      <param name="skipReadOnlyCheck">
        <see langword="true" /> to return a member even if that member has a <see langword="true" /> value for <see cref="P:System.Xaml.XamlMember.IsReadOnly" />; <see langword="false" /> to not return a <see cref="P:System.Xaml.XamlMember.IsReadOnly" /> member. The default is <see langword="false" />.</param>
      <returns>The <see cref="T:System.Xaml.XamlMember" /> information for the member, if a member was found; otherwise, <see langword="null" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlType.LookupPositionalParameters(System.Int32)">
      <summary>For markup extension types, returns the types of the positional parameters that are supported in a specific markup extension usage for this <see cref="T:System.Xaml.XamlType" />.</summary>
      <param name="parameterCount">The count (arity) of the particular syntax or constructor mode that you want information about.</param>
      <returns>A list of <see cref="T:System.Xaml.XamlType" /> values where each such <see cref="T:System.Xaml.XamlType" /> is the type for that position in the syntax. You must specify those types in the same order when supplying markup input for the markup extension.</returns>
    </member>
    <member name="M:System.Xaml.XamlType.LookupSetMarkupExtensionHandler">
      <summary>Returns a handler callback to use for the set operations of markup extensions.</summary>
      <returns>A handler callback to use for the set operations of markup extensions.</returns>
    </member>
    <member name="M:System.Xaml.XamlType.LookupSetTypeConverterHandler">
      <summary>Returns a handler to use for type converter setting cases.</summary>
      <returns>A handler to use for type converter setting cases.</returns>
    </member>
    <member name="M:System.Xaml.XamlType.LookupTrimSurroundingWhitespace">
      <summary>Returns a value that indicates whether this <see cref="T:System.Xaml.XamlType" /> should be serialized using a mode that  trims surrounding whitespace.</summary>
      <returns>
        <see langword="true" /> if this <see cref="T:System.Xaml.XamlType" /> should be serialized in a mode that trims surrounding whitespace; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlType.LookupTypeConverter">
      <summary>Returns a <see cref="T:System.Xaml.Schema.XamlValueConverter`1" /> that has a <see cref="T:System.ComponentModel.TypeConverter" /> constraint, which represents type-conversion behavior for values of this <see cref="T:System.Xaml.XamlType" />.</summary>
      <returns>A <see cref="T:System.Xaml.Schema.XamlValueConverter`1" /> with <see cref="T:System.ComponentModel.TypeConverter" /> constraint that represents type-conversion behavior for values of this <see cref="T:System.Xaml.XamlType" />; otherwise, <see langword="null" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlType.LookupUnderlyingType">
      <summary>Returns the CLR <see cref="T:System.Type" /> that underlies this <see cref="T:System.Xaml.XamlType" />.</summary>
      <returns>The CLR <see cref="T:System.Type" /> that underlies this <see cref="T:System.Xaml.XamlType" />; otherwise, <see langword="null" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlType.LookupUsableDuringInitialization">
      <summary>Returns a value that indicates whether this <see cref="T:System.Xaml.XamlType" /> is built top-down during XAML initialization.</summary>
      <returns>
        <see langword="true" /> if this <see cref="T:System.Xaml.XamlType" /> is built top-down during XAML initialization; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlType.LookupValueSerializer">
      <summary>Returns a <see cref="T:System.Xaml.Schema.XamlValueConverter`1" /> that has a <see cref="T:System.Windows.Markup.ValueSerializer" /> constraint, which represents value serialization behavior for values of this <see cref="T:System.Xaml.XamlType" />.</summary>
      <returns>A <see cref="T:System.Xaml.Schema.XamlValueConverter`1" /> with <see cref="T:System.Windows.Markup.ValueSerializer" /> constraint that represents value serialization behavior for values of this <see cref="T:System.Xaml.XamlType" />; otherwise, <see langword="null" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlType.op_Equality(System.Xaml.XamlType,System.Xaml.XamlType)">
      <summary>Determines whether two specified <see cref="T:System.Xaml.XamlType" /> objects have the same value.</summary>
      <param name="xamlType1">A <see cref="T:System.Xaml.XamlType" /> or <see langword="null" />.</param>
      <param name="xamlType2">A <see cref="T:System.Xaml.XamlType" /> or <see langword="null" />.</param>
      <returns>
        <see langword="true" /> if the value of <paramref name="xamlType1" /> is the same as the value of <paramref name="xamlType2" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlType.op_Inequality(System.Xaml.XamlType,System.Xaml.XamlType)">
      <summary>Determines whether two specified <see cref="T:System.Xaml.XamlType" /> objects have different values.</summary>
      <param name="xamlType1">A <see cref="T:System.Xaml.XamlType" /> or <see langword="null" />.</param>
      <param name="xamlType2">A <see cref="T:System.Xaml.XamlType" /> or <see langword="null" />.</param>
      <returns>
        <see langword="true" /> if the value of <paramref name="xamlType1" /> is different from the value of <paramref name="xamlType2" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xaml.XamlType.ToString">
      <summary>Returns a string representation of this <see cref="T:System.Xaml.XamlType" />.</summary>
      <returns>A string representation of this <see cref="T:System.Xaml.XamlType" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlType.AllowedContentTypes">
      <summary>Gets a read-only collection of the types that are usable as the <see cref="P:System.Xaml.XamlType.ContentProperty" /> value for this <see cref="T:System.Xaml.XamlType" />.</summary>
      <returns>A read-only collection of possible content types.</returns>
    </member>
    <member name="P:System.Xaml.XamlType.BaseType">
      <summary>Gets the <see cref="T:System.Xaml.XamlType" /> for the immediate base type of this XAML type. Determination of this value is based on the underlying type of this <see cref="T:System.Xaml.XamlType" /> and schema context.</summary>
      <returns>The <see cref="T:System.Xaml.XamlType" /> for the immediate base type of this XAML type.</returns>
    </member>
    <member name="P:System.Xaml.XamlType.ConstructionRequiresArguments">
      <summary>Gets a value that indicates whether this <see cref="T:System.Xaml.XamlType" /> must have arguments (generic constraints through <see langword="x:TypeArguments" />, initialization text, or other XAML techniques) to construct a valid instance of the type.</summary>
      <returns>
        <see langword="true" /> if construction of an instance requires some argument value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlType.ContentProperty">
      <summary>Gets the <see cref="T:System.Xaml.XamlMember" /> information for the content property of this <see cref="T:System.Xaml.XamlType" />.</summary>
      <returns>
        <see cref="T:System.Xaml.XamlMember" /> information for the content property of this <see cref="T:System.Xaml.XamlType" />. May be <see langword="null" /> if no content property exists.</returns>
    </member>
    <member name="P:System.Xaml.XamlType.ContentWrappers">
      <summary>Gets the types that are used to wrap content for a content property when it is not a strict type match, such as strings in a strongly typed <see langword="Collection&lt;T&gt;" />.</summary>
      <returns>A read-only collection of possible content wrapper types; otherwise, <see langword="null" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlType.DeferringLoader">
      <summary>Gets the <see cref="T:System.Xaml.Schema.XamlValueConverter`1" /> that represents the deferred loading conversion behavior for this type.</summary>
      <returns>The <see cref="T:System.Xaml.Schema.XamlValueConverter`1" /> with <see cref="T:System.Xaml.XamlDeferringLoader" /> constraint that represents the deferred loading behavior for this type.</returns>
    </member>
    <member name="P:System.Xaml.XamlType.Invoker">
      <summary>Gets the <see cref="T:System.Xaml.Schema.XamlTypeInvoker" /> implementation that is associated with this <see cref="T:System.Xaml.XamlType" />.</summary>
      <returns>The <see cref="T:System.Xaml.Schema.XamlTypeInvoker" /> implementation that is associated with this <see cref="T:System.Xaml.XamlType" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlType.IsAmbient">
      <summary>Gets a value that indicates whether this <see cref="T:System.Xaml.XamlType" /> represents an ambient type, as per the XAML definition.</summary>
      <returns>
        <see langword="true" /> if this <see cref="T:System.Xaml.XamlType" /> represents an ambient type; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlType.IsArray">
      <summary>Gets a value that indicates whether this <see cref="T:System.Xaml.XamlType" /> represents an array.</summary>
      <returns>
        <see langword="true" /> if this <see cref="T:System.Xaml.XamlType" /> represents an array; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlType.IsCollection">
      <summary>Gets a value that indicates whether this <see cref="T:System.Xaml.XamlType" /> represents a collection.</summary>
      <returns>
        <see langword="true" /> if this <see cref="T:System.Xaml.XamlType" /> represents a collection; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlType.IsConstructible">
      <summary>Gets a value that indicates whether this <see cref="T:System.Xaml.XamlType" /> represents a constructible type, as per the XAML definition.</summary>
      <returns>
        <see langword="true" /> if this <see cref="T:System.Xaml.XamlType" /> represents a constructible type; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlType.IsDictionary">
      <summary>Gets a value that indicates whether this <see cref="T:System.Xaml.XamlType" /> represents a dictionary, as per the XAML definition.</summary>
      <returns>
        <see langword="true" /> if this <see cref="T:System.Xaml.XamlType" /> represents a dictionary; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlType.IsGeneric">
      <summary>Gets a value that indicates whether this <see cref="T:System.Xaml.XamlType" /> represents a generic type.</summary>
      <returns>
        <see langword="true" /> if this <see cref="T:System.Xaml.XamlType" /> represents a generic type; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlType.IsMarkupExtension">
      <summary>Gets a value that indicates whether this <see cref="T:System.Xaml.XamlType" /> represents a markup extension.</summary>
      <returns>
        <see langword="true" /> if this <see cref="T:System.Xaml.XamlType" /> represents a markup extension; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlType.IsNameScope">
      <summary>Gets a value that indicates whether this <see cref="T:System.Xaml.XamlType" /> represents a XAML namescope, as per the XAML definition.</summary>
      <returns>
        <see langword="true" /> if this <see cref="T:System.Xaml.XamlType" /> represents a XAML namescope; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlType.IsNameValid">
      <summary>Gets a value that indicates whether this <see cref="T:System.Xaml.XamlType" /> is initialized by using a valid <see langword="xamlName" /> string as its <see cref="P:System.Xaml.XamlType.Name" />.</summary>
      <returns>
        <see langword="true" /> if this <see cref="T:System.Xaml.XamlType" /> is initialized by using a valid <see langword="xamlName" /> string; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlType.IsNullable">
      <summary>Gets a value that indicates whether this <see cref="T:System.Xaml.XamlType" /> represents a nullable type, as per the XAML definition.</summary>
      <returns>
        <see langword="true" /> if this <see cref="T:System.Xaml.XamlType" /> represents a nullable type; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlType.IsPublic">
      <summary>Gets a value that indicates whether this <see cref="T:System.Xaml.XamlType" /> represents a public type in the relevant type system.</summary>
      <returns>
        <see langword="true" /> if this <see cref="T:System.Xaml.XamlType" /> represents a public type; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlType.IsUnknown">
      <summary>Gets a value that indicates whether this <see cref="T:System.Xaml.XamlType" /> represents a type that cannot be resolved in the underlying type system.</summary>
      <returns>
        <see langword="true" /> if this <see cref="T:System.Xaml.XamlType" /> represents an unresolvable type; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlType.IsUsableDuringInitialization">
      <summary>Gets a value that indicates whether this <see cref="T:System.Xaml.XamlType" /> is built top-down during XAML initialization.</summary>
      <returns>
        <see langword="true" /> if this <see cref="T:System.Xaml.XamlType" /> is built top-down during XAML initialization; otherwise, <see langword="false" />. The default is <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlType.IsWhitespaceSignificantCollection">
      <summary>Gets a value that indicates whether this <see cref="T:System.Xaml.XamlType" /> represents a whitespace significant collection, as per the XML definition.</summary>
      <returns>
        <see langword="true" /> if this <see cref="T:System.Xaml.XamlType" /> represents a whitespace significant collection; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlType.IsXData">
      <summary>Gets a value that indicates whether this <see cref="T:System.Xaml.XamlType" /> represents XML <see langword="XDATA" />, as per the XAML definition.</summary>
      <returns>
        <see langword="true" /> if this <see cref="T:System.Xaml.XamlType" /> represents <see langword="XDATA" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlType.ItemType">
      <summary>Gets a value that provides the type information for the <see langword="Items" /> property of this <see cref="T:System.Xaml.XamlType" />.</summary>
      <returns>A <see cref="T:System.Xaml.XamlType" /> object for the type of the items in the collection; otherwise, <see langword="null" />, if this <see cref="T:System.Xaml.XamlType" /> does not represent a collection.</returns>
    </member>
    <member name="P:System.Xaml.XamlType.KeyType">
      <summary>Gets a value that provides the type information for the key property of this <see cref="T:System.Xaml.XamlType" />, if the <see cref="T:System.Xaml.XamlType" /> represents a dictionary.</summary>
      <returns>A <see cref="T:System.Xaml.XamlType" /> object for the type of the key for dictionary usage, otherwise, <see langword="null" />, if this <see cref="T:System.Xaml.XamlType" /> does not represent a dictionary.</returns>
    </member>
    <member name="P:System.Xaml.XamlType.MarkupExtensionReturnType">
      <summary>Gets a value that provides the type information for the returned <see langword="ProvideValue" /> of this <see cref="T:System.Xaml.XamlType" />, if it represents a markup extension.</summary>
      <returns>A <see cref="T:System.Xaml.XamlType" /> object for the return type for markup extension usage; otherwise, <see langword="null" />, if this <see cref="T:System.Xaml.XamlType" /> does not represent a markup extension.</returns>
    </member>
    <member name="P:System.Xaml.XamlType.Name">
      <summary>Gets the string name of the type that this <see cref="T:System.Xaml.XamlType" /> represents.</summary>
      <returns>The string name of this XAML type.</returns>
    </member>
    <member name="P:System.Xaml.XamlType.PreferredXamlNamespace">
      <summary>Gets the single XAML namespace that is the primary XAML namespace for this <see cref="T:System.Xaml.XamlType" />.</summary>
      <returns>The identifier, as a string, of the primary XAML namespace for this XAML type.</returns>
    </member>
    <member name="P:System.Xaml.XamlType.SchemaContext">
      <summary>Gets the active <see cref="T:System.Xaml.XamlSchemaContext" /> for processing this <see cref="T:System.Xaml.XamlType" />.</summary>
      <returns>The active <see cref="T:System.Xaml.XamlSchemaContext" /> for processing this <see cref="T:System.Xaml.XamlType" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlType.TrimSurroundingWhitespace">
      <summary>Gets a value that indicates whether this <see cref="T:System.Xaml.XamlType" /> has whitespace handling behavior for serialization that trims the surrounding whitespace in its content.</summary>
      <returns>
        <see langword="true" /> if this <see cref="T:System.Xaml.XamlType" /> represents a type that uses whitespace trimming; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlType.TypeArguments">
      <summary>Gets a list of type arguments for cases where this <see cref="T:System.Xaml.XamlType" /> represents a generic.</summary>
      <returns>A list of type argument types; otherwise, <see langword="null" />, if this <see cref="T:System.Xaml.XamlType" /> does not represent a generic.</returns>
    </member>
    <member name="P:System.Xaml.XamlType.TypeConverter">
      <summary>Gets a <see cref="T:System.Xaml.Schema.XamlValueConverter`1" /> with <see cref="T:System.ComponentModel.TypeConverter" /> constraint that represents type conversion behavior for values of this <see cref="T:System.Xaml.XamlType" />.</summary>
      <returns>A <see cref="T:System.Xaml.Schema.XamlValueConverter`1" /> with <see cref="T:System.ComponentModel.TypeConverter" /> constraint that represents type conversion behavior for values of this <see cref="T:System.Xaml.XamlType" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlType.UnderlyingType">
      <summary>Gets the CLR <see cref="T:System.Type" /> that underlies this <see cref="T:System.Xaml.XamlType" />.</summary>
      <returns>The CLR <see cref="T:System.Type" /> that underlies this <see cref="T:System.Xaml.XamlType" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlType.ValueSerializer">
      <summary>Gets a <see cref="T:System.Xaml.Schema.XamlValueConverter`1" /> with <see cref="T:System.Windows.Markup.ValueSerializer" /> constraint that represents value serialization behavior for values of this <see cref="T:System.Xaml.XamlType" />.</summary>
      <returns>A <see cref="T:System.Xaml.Schema.XamlValueConverter`1" /> with <see cref="T:System.Windows.Markup.ValueSerializer" /> constraint that represents value serialization behavior for values of this <see cref="T:System.Xaml.XamlType" />; otherwise, <see langword="null" />.</returns>
    </member>
    <member name="T:System.Xaml.XamlWriter">
      <summary>Provides default implementation and base class definitions for a XAML writer. This is not a working default XAML writer; you must either derive from <see cref="T:System.Xaml.XamlWriter" /> and implement its abstract members, or use an existing <see cref="T:System.Xaml.XamlWriter" /> derived class.</summary>
    </member>
    <member name="M:System.Xaml.XamlWriter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlWriter" /> class.</summary>
    </member>
    <member name="M:System.Xaml.XamlWriter.Close">
      <summary>Closes the XAML writer object.</summary>
    </member>
    <member name="M:System.Xaml.XamlWriter.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Xaml.XamlWriter" /> and optionally releases the managed resources.</summary>
      <param name="disposing">
        <see langword="true" /> to release the managed resources; otherwise, <see langword="false" />.</param>
    </member>
    <member name="M:System.Xaml.XamlWriter.System#IDisposable#Dispose">
      <summary>See <see cref="M:System.IDisposable.Dispose" />.</summary>
    </member>
    <member name="M:System.Xaml.XamlWriter.WriteEndMember">
      <summary>When implemented in a derived class, produces the representation of an end member node.</summary>
    </member>
    <member name="M:System.Xaml.XamlWriter.WriteEndObject">
      <summary>When implemented in a derived class, produces the representation of an end object node.</summary>
    </member>
    <member name="M:System.Xaml.XamlWriter.WriteGetObject">
      <summary>When implemented in a derived class, produces an object for cases where the object is a default or implicit value of the property being set, instead of being specified as a discrete object value in the input XAML node set.</summary>
    </member>
    <member name="M:System.Xaml.XamlWriter.WriteNamespace(System.Xaml.NamespaceDeclaration)">
      <summary>When implemented in a derived class, writes a XAML namespace declaration node.</summary>
      <param name="namespaceDeclaration">The namespace declaration to write.</param>
    </member>
    <member name="M:System.Xaml.XamlWriter.WriteNode(System.Xaml.XamlReader)">
      <summary>Performs switching based on node type from the XAML reader (<see cref="P:System.Xaml.XamlReader.NodeType" />) and calls the relevant <see langword="Write" /> method for the writer implementation.</summary>
      <param name="reader">The reader to use for node determination.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="reader" /> is <see langword="null" />.</exception>
      <exception cref="T:System.NotImplementedException">The default implementation encountered a <see cref="T:System.Xaml.XamlNodeType" /> that is not in the default enumeration.</exception>
    </member>
    <member name="M:System.Xaml.XamlWriter.WriteStartMember(System.Xaml.XamlMember)">
      <summary>When implemented in a derived class, writes the representation of a start member node.</summary>
      <param name="xamlMember">The member node to write.</param>
    </member>
    <member name="M:System.Xaml.XamlWriter.WriteStartObject(System.Xaml.XamlType)">
      <summary>When implemented in a derived class, writes the representation of a start object node.</summary>
      <param name="type">The XAML type of the object to write.</param>
    </member>
    <member name="M:System.Xaml.XamlWriter.WriteValue(System.Object)">
      <summary>When implemented in a derived class, writes a value node.</summary>
      <param name="value">The value to write.</param>
    </member>
    <member name="P:System.Xaml.XamlWriter.IsDisposed">
      <summary>Gets whether <see cref="M:System.Xaml.XamlWriter.Dispose(System.Boolean)" /> has been called.</summary>
      <returns>
        <see langword="true" /> if <see cref="M:System.Xaml.XamlWriter.Dispose(System.Boolean)" /> has been called; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlWriter.SchemaContext">
      <summary>When implemented in a derived class, gets the active XAML schema context.</summary>
      <returns>The active XAML schema context.</returns>
    </member>
    <member name="T:System.Xaml.XamlWriterSettings">
      <summary>Provides initialization settings for a <see cref="T:System.Xaml.XamlWriter" /> implementation.</summary>
    </member>
    <member name="M:System.Xaml.XamlWriterSettings.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlWriterSettings" /> class.</summary>
    </member>
    <member name="M:System.Xaml.XamlWriterSettings.#ctor(System.Xaml.XamlWriterSettings)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlWriterSettings" /> class using another existing settings instance.</summary>
      <param name="settings">An existing <see cref="T:System.Xaml.XamlWriterSettings" /> object.</param>
    </member>
    <member name="T:System.Xaml.XamlXmlReader">
      <summary>Processes XAML markup from XML files by using an <see cref="T:System.Xml.XmlReader" /> intermediary, and produces a XAML node stream.</summary>
    </member>
    <member name="M:System.Xaml.XamlXmlReader.#ctor(System.IO.Stream)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlXmlReader" /> class, based on a stream.</summary>
      <param name="stream">The initial stream to load into the reader.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xaml.XamlXmlReader.#ctor(System.IO.Stream,System.Xaml.XamlSchemaContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlXmlReader" /> class, based on a stream, and using a supplied XAML schema context.</summary>
      <param name="stream">The initial stream to load into the reader.</param>
      <param name="schemaContext">The XAML schema context for XAML processing.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> or <paramref name="schemaContext" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xaml.XamlXmlReader.#ctor(System.IO.Stream,System.Xaml.XamlSchemaContext,System.Xaml.XamlXmlReaderSettings)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlXmlReader" /> class, based on a stream, with a supplied XAML schema context and XAML-specific settings.</summary>
      <param name="stream">The initial stream to load into the reader.</param>
      <param name="schemaContext">The XAML schema context for XAML processing.</param>
      <param name="settings">The specific reader settings.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> or <paramref name="schemaContext" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xaml.XamlXmlReader.#ctor(System.IO.Stream,System.Xaml.XamlXmlReaderSettings)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlXmlReader" /> class, based on a stream, with XAML-specific settings.</summary>
      <param name="stream">The initial stream to load into the reader.</param>
      <param name="settings">The specific reader settings.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xaml.XamlXmlReader.#ctor(System.IO.TextReader)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlXmlReader" /> class, based on a <see cref="T:System.IO.TextReader" />.</summary>
      <param name="textReader">The <see cref="T:System.IO.TextReader" /> to use for initialization.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="textReader" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xaml.XamlXmlReader.#ctor(System.IO.TextReader,System.Xaml.XamlSchemaContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlXmlReader" /> class, based on a <see cref="T:System.IO.TextReader" />, with a supplied schema context and XAML-specific settings.</summary>
      <param name="textReader">The <see cref="T:System.IO.TextReader" /> to use for initialization.</param>
      <param name="schemaContext">The XAML schema context for XAML processing.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="textReader" /> is <see langword="null" />.  
  
 -or-  
  
 <paramref name="schemaContext" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xaml.XamlXmlReader.#ctor(System.IO.TextReader,System.Xaml.XamlSchemaContext,System.Xaml.XamlXmlReaderSettings)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlXmlReader" /> class, based on a <see cref="T:System.IO.TextReader" />, and using a supplied schema context and XAML-specific settings.</summary>
      <param name="textReader">The <see cref="T:System.IO.TextReader" /> to use for initialization.</param>
      <param name="schemaContext">The XAML schema context for XAML processing.</param>
      <param name="settings">The specific reader settings.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="textReader" /> is <see langword="null" />.  
  
 -or-  
  
 <paramref name="schemaContext" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xaml.XamlXmlReader.#ctor(System.IO.TextReader,System.Xaml.XamlXmlReaderSettings)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlXmlReader" /> class, based on a <see cref="T:System.IO.TextReader" />, and using XAML-specific settings.</summary>
      <param name="textReader">The <see cref="T:System.IO.TextReader" /> to use for initialization.</param>
      <param name="settings">The specific reader settings.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="textReader" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xaml.XamlXmlReader.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlXmlReader" /> class, based on a file name of a file to load into a default XML reader.</summary>
      <param name="fileName">The name of the XML file to load.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="fileName" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xaml.XamlXmlReader.#ctor(System.String,System.Xaml.XamlSchemaContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlXmlReader" /> class, based on the file name of a file to load into a default XML reader, with a supplied XAML schema context.</summary>
      <param name="fileName">The name of the file to load.</param>
      <param name="schemaContext">The XAML schema context for XAML processing.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="fileName" /> or <paramref name="schemaContext" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xaml.XamlXmlReader.#ctor(System.String,System.Xaml.XamlSchemaContext,System.Xaml.XamlXmlReaderSettings)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlXmlReader" /> class, based on the file name of a file to load into a default XML reader, and using a supplied XAML schema context and XAML-specific reader settings.</summary>
      <param name="fileName">The name of the XML file to load.</param>
      <param name="schemaContext">The XAML schema context for XAML processing.</param>
      <param name="settings">The specific reader settings.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="fileName" /> is <see langword="null" />.  
  
 -or-  
  
 <paramref name="schemaContext" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xaml.XamlXmlReader.#ctor(System.String,System.Xaml.XamlXmlReaderSettings)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlXmlReader" /> class, based on the file name of a file to load into a default XML reader, and using XAML-specific reader settings.</summary>
      <param name="fileName">The name of the XML file to load.</param>
      <param name="settings">The specific reader settings.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="fileName" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xaml.XamlXmlReader.#ctor(System.Xml.XmlReader)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlXmlReader" /> class using the provided <see cref="T:System.Xml.XmlReader" />.</summary>
      <param name="xmlReader">The <see cref="T:System.Xml.XmlReader" /> to use as the intermediary XML processor.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="xmlReader" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xaml.XamlXmlReader.#ctor(System.Xml.XmlReader,System.Xaml.XamlSchemaContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlXmlReader" /> class using the provided <see cref="T:System.Xml.XmlReader" /> and schema context.</summary>
      <param name="xmlReader">The <see cref="T:System.Xml.XmlReader" /> to use as the intermediary XML processor.</param>
      <param name="schemaContext">The XAML schema context for XAML processing.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="xmlReader" /> or <paramref name="schemaContext" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xaml.XamlXmlReader.#ctor(System.Xml.XmlReader,System.Xaml.XamlSchemaContext,System.Xaml.XamlXmlReaderSettings)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlXmlReader" /> class using the provided <see cref="T:System.Xml.XmlReader" />, schema context, and reader settings.</summary>
      <param name="xmlReader">The <see cref="T:System.Xml.XmlReader" /> to use as the intermediary XML processor.</param>
      <param name="schemaContext">The XAML schema context for XAML processing.</param>
      <param name="settings">The specific XAML reader settings.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="xmlReader" /> or <paramref name="schemaContext" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xaml.XamlXmlReader.#ctor(System.Xml.XmlReader,System.Xaml.XamlXmlReaderSettings)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlXmlReader" /> class, using the provided <see cref="T:System.Xml.XmlReader" /> and reader settings.</summary>
      <param name="xmlReader">The <see cref="T:System.Xml.XmlReader" /> to use as the intermediary XML processor.</param>
      <param name="settings">The specific XAML reader settings.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="xmlReader" /> or <paramref name="schemaContext" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xaml.XamlXmlReader.Read">
      <summary>Provides the next XAML node from the loaded source, if a XAML node is available.</summary>
      <returns>
        <see langword="true" /> if a node is available; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlXmlReader.HasLineInfo">
      <summary>Gets a value that specifies whether line information is available.</summary>
      <returns>
        <see langword="true" /> if line information is available; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlXmlReader.IsEof">
      <summary>Gets a value that reports whether the reader position in the XAML node stream is at end-of-file.</summary>
      <returns>
        <see langword="true" /> if the position is at the conceptual end-of-file in the node stream; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlXmlReader.LineNumber">
      <summary>Gets the line number to report.</summary>
      <returns>The line number to report.</returns>
    </member>
    <member name="P:System.Xaml.XamlXmlReader.LinePosition">
      <summary>Gets the line position to report.</summary>
      <returns>The line position to report.</returns>
    </member>
    <member name="P:System.Xaml.XamlXmlReader.Member">
      <summary>Gets the current member at the reader position, if the current reader position is on a <see cref="F:System.Xaml.XamlNodeType.StartMember" />.</summary>
      <returns>The current member; or <see langword="null" />, if the current reader position is not on a member.</returns>
    </member>
    <member name="P:System.Xaml.XamlXmlReader.Namespace">
      <summary>Gets the XAML namespace from the current node.</summary>
      <returns>The XAML namespace from the current node, if it is available; otherwise, <see langword="null" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlXmlReader.NodeType">
      <summary>Gets the type of the current node.</summary>
      <returns>A value of the <see cref="T:System.Xaml.XamlNodeType" /> enumeration.</returns>
    </member>
    <member name="P:System.Xaml.XamlXmlReader.SchemaContext">
      <summary>Gets an object that provides schema information for the information set.</summary>
      <returns>An object that provides schema information for the information set.</returns>
    </member>
    <member name="P:System.Xaml.XamlXmlReader.Type">
      <summary>Gets the <see cref="T:System.Xaml.XamlType" /> of the current node.</summary>
      <returns>The <see cref="T:System.Xaml.XamlType" /> of the current node; or <see langword="null" />, if the position is not on an object.</returns>
    </member>
    <member name="P:System.Xaml.XamlXmlReader.Value">
      <summary>Gets the value of the current node.</summary>
      <returns>The value of the current node; or <see langword="null" />, if the position is not on a <see cref="F:System.Xaml.XamlNodeType.Value" /> node type.</returns>
    </member>
    <member name="T:System.Xaml.XamlXmlReaderSettings">
      <summary>Specifies processing rules or option settings for the <see cref="T:System.Xaml.XamlXmlReader" /> XAML reader implementation.</summary>
    </member>
    <member name="M:System.Xaml.XamlXmlReaderSettings.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlXmlReaderSettings" /> class.</summary>
    </member>
    <member name="M:System.Xaml.XamlXmlReaderSettings.#ctor(System.Xaml.XamlXmlReaderSettings)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlXmlReaderSettings" /> class by copying settings from an existing <see cref="T:System.Xaml.XamlXmlReaderSettings" /> object.</summary>
      <param name="settings">The existing <see cref="T:System.Xaml.XamlXmlReaderSettings" /> object to copy.</param>
    </member>
    <member name="P:System.Xaml.XamlXmlReaderSettings.CloseInput">
      <summary>Gets or sets a value that indicates whether the underlying stream or text reader should be closed when the <see cref="T:System.Xaml.XamlXmlReader" /> is closed.</summary>
      <returns>
        <see langword="true" /> if the underlying stream or reader should be closed when the <see cref="T:System.Xaml.XamlXmlReader" /> is closed; otherwise, <see langword="false" />. The default is <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlXmlReaderSettings.SkipXmlCompatibilityProcessing">
      <summary>Gets or sets a value that determines whether the reader should differ from the default <see cref="T:System.Xaml.XamlXmlReader" /> behavior of how markup compatibility content is processed.</summary>
      <returns>
        <see langword="true" /> if the initiating reader is directly used, which means that XML compatibility markup is processed as part of the main stream and compatibility is not considered. <see langword="false" /> if the default behavior is used, which processes XML compatibility separately. The default is <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlXmlReaderSettings.XmlLang">
      <summary>Gets or sets the language setting value that the reader may promote to writers that write <see langword="xml:lang" /> attributes in nodes.</summary>
      <returns>The string to use for possible <see langword="xml:lang" /> output that is based on the reader.</returns>
    </member>
    <member name="P:System.Xaml.XamlXmlReaderSettings.XmlSpacePreserve">
      <summary>Gets or sets a value that determines whether the XAML reader instructs any XAML writers to write <see langword="xml:space" /> attributes in nodes. If that behavior is desired, this information is passed through shared XAML schema context.</summary>
      <returns>
        <see langword="true" /> if writers that are processing the XAML node stream can write <c>xml:space="preserve"</c> in output; <see langword="false" /> if <see langword="xml:space" /> attributes cannot be written in nodes.</returns>
    </member>
    <member name="T:System.Xaml.XamlXmlWriter">
      <summary>Uses a <see cref="T:System.IO.TextWriter" /> or <see cref="T:System.Xml.XmlWriter" /> support class to write a XAML node stream to a text or markup serialized form.</summary>
    </member>
    <member name="M:System.Xaml.XamlXmlWriter.#ctor(System.IO.Stream,System.Xaml.XamlSchemaContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlXmlWriter" /> class from a stream.</summary>
      <param name="stream">The stream to write.</param>
      <param name="schemaContext">The XAML schema context for the XAML writer.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> is <see langword="null" />.  
  
 -or-  
  
 <paramref name="schemaContext" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xaml.XamlXmlWriter.#ctor(System.IO.Stream,System.Xaml.XamlSchemaContext,System.Xaml.XamlXmlWriterSettings)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlXmlWriter" /> class from a stream using a writer settings object.</summary>
      <param name="stream">The stream to write.</param>
      <param name="schemaContext">The XAML schema context for the XAML writer.</param>
      <param name="settings">An instance of <see cref="T:System.Xaml.XamlXmlWriterSettings" />, which typically has specific non-default settings.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> or <paramref name="schemaContext" /> is <see langword="null" /></exception>
    </member>
    <member name="M:System.Xaml.XamlXmlWriter.#ctor(System.IO.TextWriter,System.Xaml.XamlSchemaContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlXmlWriter" /> class from a <see cref="T:System.IO.TextWriter" /> basis.</summary>
      <param name="textWriter">The <see cref="T:System.IO.TextWriter" /> that writes the output.</param>
      <param name="schemaContext">The XAML schema context for the XAML writer.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="textWriter" /> or <paramref name="schemaContext" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xaml.XamlXmlWriter.#ctor(System.IO.TextWriter,System.Xaml.XamlSchemaContext,System.Xaml.XamlXmlWriterSettings)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlXmlWriter" /> class from a <see cref="T:System.IO.TextWriter" /> basis using a settings object.</summary>
      <param name="textWriter">The <see cref="T:System.IO.TextWriter" /> that writes the output.</param>
      <param name="schemaContext">The XAML schema context for the XAML writer.</param>
      <param name="settings">An instance of <see cref="T:System.Xaml.XamlXmlWriterSettings" />, which typically has specific non-default settings.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="textWriter" /> or <paramref name="schemaContext" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xaml.XamlXmlWriter.#ctor(System.Xml.XmlWriter,System.Xaml.XamlSchemaContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlXmlWriter" /> class from a <see cref="T:System.Xml.XmlWriter" /> basis.</summary>
      <param name="xmlWriter">The <see cref="T:System.Xml.XmlWriter" /> that writes the output.</param>
      <param name="schemaContext">The XAML schema context for the XAML writer.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="xmlWriter" /> or <paramref name="schemaContext" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xaml.XamlXmlWriter.#ctor(System.Xml.XmlWriter,System.Xaml.XamlSchemaContext,System.Xaml.XamlXmlWriterSettings)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlXmlWriter" /> class from a <see cref="T:System.Xml.XmlWriter" /> basis using a settings object.</summary>
      <param name="xmlWriter">The <see cref="T:System.Xml.XmlWriter" /> that writes the output.</param>
      <param name="schemaContext">The XAML schema context for the XAML writer.</param>
      <param name="settings">An instance of <see cref="T:System.Xaml.XamlXmlWriterSettings" />, which typically has specific non-default settings.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="xmlWriter" /> or <paramref name="schemaContext" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xaml.XamlXmlWriter.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by <see cref="T:System.Xaml.XamlXmlWriter" /> and optionally releases the managed resources.</summary>
      <param name="disposing">
        <see langword="true" /> to release the unmanaged resources; otherwise, <see langword="false" />.</param>
    </member>
    <member name="M:System.Xaml.XamlXmlWriter.Flush">
      <summary>Calls the <see langword="Flush" /> method of the underlying <see cref="T:System.Xml.XmlWriter" /> or <see cref="T:System.IO.TextWriter" />, which writes anything that is currently in the buffer, and then closes the writer.</summary>
    </member>
    <member name="M:System.Xaml.XamlXmlWriter.WriteEndMember">
      <summary>Writes a XAML end member node to the underlying <see cref="T:System.Xml.XmlWriter" /> or <see cref="T:System.IO.TextWriter" />. Throws an exception if the current position of the XAML node stream is not within a member, or if the internal writer state does not support writing to an end member.</summary>
      <exception cref="T:System.InvalidOperationException">The current position of the XAML node stream is not within a member.</exception>
      <exception cref="T:System.Xaml.XamlXmlWriterException">The current writer state does not support writing an end member.</exception>
    </member>
    <member name="M:System.Xaml.XamlXmlWriter.WriteEndObject">
      <summary>Writes a XAML end object node to the underlying <see cref="T:System.Xml.XmlWriter" /> or <see cref="T:System.IO.TextWriter" />. Throws an exception if the current position of the XAML node stream that is being processed is incompatible with writing an end object.</summary>
      <exception cref="T:System.InvalidOperationException">The current position of the XAML node stream is not in a scope where an end member can be written.</exception>
      <exception cref="T:System.Xaml.XamlXmlWriterException">The current writer state does not support writing an end object.</exception>
    </member>
    <member name="M:System.Xaml.XamlXmlWriter.WriteGetObject">
      <summary>Writes an object for cases where the specified object is a default or implicit value of the property that is being written, instead of being specified as an object value in the input XAML node set.</summary>
    </member>
    <member name="M:System.Xaml.XamlXmlWriter.WriteNamespace(System.Xaml.NamespaceDeclaration)">
      <summary>Writes namespace information to the underlying <see cref="T:System.Xml.XmlWriter" /> or <see cref="T:System.IO.TextWriter" />. May throw an exception for certain states; however, may instead defer writing the namespace information until the writer and the XAML node stream that is being processed reaches a position where a XAML namespace declaration can be inserted.</summary>
      <param name="namespaceDeclaration">The XAML namespace declaration to write.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="namespaceDeclaration" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="namespaceDeclaration" /> is not a valid XAML namespace declaration (has a null prefix or null identifier component).</exception>
      <exception cref="T:System.Xaml.XamlXmlWriterException">The current writer state does not support writing a XAML namespace declaration.</exception>
    </member>
    <member name="M:System.Xaml.XamlXmlWriter.WriteStartMember(System.Xaml.XamlMember)">
      <summary>Writes a XAML start member node to the underlying <see cref="T:System.Xml.XmlWriter" /> or <see cref="T:System.IO.TextWriter" />. Throws an exception if the current position of the XAML node stream is within another member, or if it is not in a scope or writer state where a start member can be written.</summary>
      <param name="property">The XAML member identifier for the member to write.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="property" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="property" /> is not a valid <see cref="T:System.Xaml.XamlMember" />.</exception>
      <exception cref="T:System.InvalidOperationException">The current position of the XAML node stream is invalid for writing a start member.</exception>
      <exception cref="T:System.Xaml.XamlXmlWriterException">The writer state is not valid for writing a start member.  
  
 -or-  
  
 The XAML writer attempted to write a duplicate member. This exception may have a more precise inner exception.</exception>
    </member>
    <member name="M:System.Xaml.XamlXmlWriter.WriteStartObject(System.Xaml.XamlType)">
      <summary>Writes a XAML start object node to the underlying <see cref="T:System.Xml.XmlWriter" /> or <see cref="T:System.IO.TextWriter" />. Throws an exception if the current position of the XAML node stream is not in a scope where a start object can be written, or if the writer is not in a state that can write a start object.</summary>
      <param name="type">The <see cref="T:System.Xaml.XamlType" /> (XAML type identifier) for the object to write.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="type" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="type" /> is not a valid <see cref="T:System.Xaml.XamlType" />.</exception>
      <exception cref="T:System.InvalidOperationException">The current position of the XAML node stream is not valid for writing a new start object.</exception>
      <exception cref="T:System.Xaml.XamlXmlWriterException">The state of the XAML writer is not valid for writing a new start object.</exception>
    </member>
    <member name="M:System.Xaml.XamlXmlWriter.WriteValue(System.Object)">
      <summary>Writes a XAML value node to the underlying <see cref="T:System.Xml.XmlWriter" /> or <see cref="T:System.IO.TextWriter" />. Throws an exception if the current position of the XAML node stream is invalid for writing a value, or the writer is in a state where a value cannot be written.</summary>
      <param name="value">The value information to write.</param>
      <exception cref="T:System.InvalidOperationException">The current position of the XAML node stream is not valid for writing a value.</exception>
      <exception cref="T:System.Xaml.XamlXmlWriterException">The XAML writer state does not support the writing of a value node.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> specifies a value that is not null or a string.</exception>
    </member>
    <member name="P:System.Xaml.XamlXmlWriter.SchemaContext">
      <summary>Gets the XAML schema context that this <see cref="T:System.Xaml.XamlXmlWriter" /> uses for processing.</summary>
      <returns>The XAML schema context that this <see cref="T:System.Xaml.XamlXmlWriter" /> uses for XAML processing.</returns>
    </member>
    <member name="P:System.Xaml.XamlXmlWriter.Settings">
      <summary>Gets the writer settings that this <see cref="T:System.Xaml.XamlXmlWriter" /> uses for XAML processing.</summary>
      <returns>The writer settings that this <see cref="T:System.Xaml.XamlXmlWriter" /> uses for XAML processing.</returns>
    </member>
    <member name="T:System.Xaml.XamlXmlWriterException">
      <summary>The exception that is thrown by certain <see cref="T:System.Xaml.XamlXmlWriter" /> APIs.</summary>
    </member>
    <member name="M:System.Xaml.XamlXmlWriterException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlXmlWriterException" /> class with a system-supplied message that describes the error.</summary>
    </member>
    <member name="M:System.Xaml.XamlXmlWriterException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlXmlWriterException" /> class with serialized data.</summary>
      <param name="info">The object that holds the serialized object data.</param>
      <param name="context">The contextual information about the source or destination.</param>
    </member>
    <member name="M:System.Xaml.XamlXmlWriterException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlXmlWriterException" /> class with a specified message that describes the error.</summary>
      <param name="message">The message that describes the exception. The caller of this constructor is required to ensure that this string has been localized for the current system culture.</param>
    </member>
    <member name="M:System.Xaml.XamlXmlWriterException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlXmlWriterException" /> class with a specified error message and a reference to the inner exception that is the cause of this exception.</summary>
      <param name="message">The message that describes the exception. The caller of this constructor is required to ensure that this string has been localized for the current system culture.</param>
      <param name="innerException">The exception that is the cause of the current exception. If the <paramref name="innerException" /> parameter is not <see langword="null" />, the current exception is raised in a <see langword="catch" /> block that handles the inner exception.</param>
    </member>
    <member name="T:System.Xaml.XamlXmlWriterSettings">
      <summary>Provides initialization settings for the <see cref="T:System.Xaml.XamlXmlWriter" /> XAML writer implementation.</summary>
    </member>
    <member name="M:System.Xaml.XamlXmlWriterSettings.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xaml.XamlXmlWriterSettings" /> class.</summary>
    </member>
    <member name="M:System.Xaml.XamlXmlWriterSettings.Copy">
      <summary>Returns a copy of this <see cref="T:System.Xaml.XamlXmlWriterSettings" /> instance.</summary>
      <returns>The returned copy.</returns>
    </member>
    <member name="P:System.Xaml.XamlXmlWriterSettings.AssumeValidInput">
      <summary>Gets or sets a value that specifies whether the <see cref="T:System.Xaml.XamlXmlWriter" /> should always assume valid XAML input for purposes of duplicate resolution or other error checking.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Xaml.XamlXmlWriter" /> skips certain validation or error checks, such as throwing exceptions on duplicate members. <see langword="false" /> if the <see cref="T:System.Xaml.XamlXmlWriter" /> throws exceptions when invalid XAML is encountered. The default is <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xaml.XamlXmlWriterSettings.CloseOutput">
      <summary>Gets or sets a value that specifies whether the <see cref="T:System.Xaml.XamlXmlWriter" /> should close immediately on <see langword="Dispose" /> or other operations, or whether the XAML writer should instead write the buffer output before closing. Use this setting with caution; closing immediately can result in invalid XAML that cannot be loaded again.</summary>
      <returns>
        <see langword="true" /> if <see cref="T:System.Xaml.XamlXmlWriter" /> immediately closes on a <see langword="Dispose" /> or similar operations. <see langword="false" /> if the remaining buffer output is written before the <see cref="T:System.Xaml.XamlXmlWriter" /> is released. The default is <see langword="false" />.</returns>
    </member>
  </members>
</doc>