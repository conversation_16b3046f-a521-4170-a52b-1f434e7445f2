<?xml version="1.0"?>
<doc>
    <assembly>
        <name>System.CodeDom</name>
    </assembly>
    <members>
        <member name="P:Microsoft.VisualBasic.VBCodeGenerator.IsCurrentModule">
            <summary>Tells whether or not the current class should be generated as a module</summary>
        </member>
        <member name="P:System.CodeDom.Compiler.CompilerParameters.CoreAssemblyFileName">
            <summary>
            The "core" or "standard" assembly that contains basic types such as <code>Object</code>, <code>Int32</code> and the like
            that is to be used for the compilation.<br />
            If the value of this property is an empty string (or <code>null</code>), the default core assembly will be used by the
            compiler (depending on the compiler version this may be <code>mscorlib.dll</code> or <code>System.Runtime.dll</code> in
            a Framework or reference assembly directory).<br />
            If the value of this property is not empty, CodeDom will emit compiler options to not reference <em>any</em> assemblies
            implicitly during compilation. It will also explicitly reference the assembly file specified in this property.<br />
            For compilers that only implicitly reference the "core" or "standard" assembly by default, this option can be used on its own.
            For compilers that implicitly reference more assemblies on top of the "core" / "standard" assembly, using this option may require
            specifying additional entries in the <code>System.CodeDom.Compiler.<bold>ReferencedAssemblies</bold></code> collection.<br />
            Note: An <code>ICodeCompiler</code> / <code>CoodeDomProvider</code> implementation may choose to ignore this property.
            </summary>
        </member>
        <member name="T:System.CSharpHelpers">
            <devdoc>
               <para>Provides a base class for code generators.</para>
            </devdoc>
        </member>
        <member name="M:System.CSharpHelpers.IsValidLanguageIndependentIdentifier(System.String)">
            <devdoc>
               <para>
                  Gets a value indicating whether the specified value is a valid language
                  independent identifier.
               </para>
            </devdoc>
        </member>
        <member name="P:System.SR.CodeDomProvider_NotDefined">
            <summary>There is no CodeDom provider defined for the language.</summary>
        </member>
        <member name="P:System.SR.NotSupported_CodeDomAPI">
            <summary>This CodeDomProvider does not support this method.</summary>
        </member>
        <member name="P:System.SR.CodeGenOutputWriter">
            <summary>The output writer for code generation and the writer supplied don't match and cannot be used. This is generally caused by a bad implementation of a CodeGenerator derived class.</summary>
        </member>
        <member name="P:System.SR.CodeGenReentrance">
            <summary>This code generation API cannot be called while the generator is being used to generate something else.</summary>
        </member>
        <member name="P:System.SR.InvalidElementType">
            <summary>Element type {0} is not supported.</summary>
        </member>
        <member name="P:System.SR.Argument_NullComment">
            <summary>The 'Comment' property of the CodeCommentStatement '{0}' cannot be null.</summary>
        </member>
        <member name="P:System.SR.InvalidPrimitiveType">
            <summary>Invalid Primitive Type: {0}. Consider using CodeObjectCreateExpression.</summary>
        </member>
        <member name="P:System.SR.InvalidIdentifier">
            <summary>Identifier '{0}' is not valid.</summary>
        </member>
        <member name="P:System.SR.ArityDoesntMatch">
            <summary>The total arity specified in '{0}' does not match the number of TypeArguments supplied.  There were '{1}' TypeArguments supplied.</summary>
        </member>
        <member name="P:System.SR.InvalidNullEmptyArgument">
            <summary>Argument {0} cannot be null or zero-length.</summary>
        </member>
        <member name="P:System.SR.DuplicateFileName">
            <summary>The file name '{0}' was already in the collection.</summary>
        </member>
        <member name="P:System.SR.InvalidTypeName">
            <summary>The type name:"{0}" on the property:"{1}" of type:"{2}" is not a valid language-independent type name.</summary>
        </member>
        <member name="P:System.SR.InvalidRegion">
            <summary>The region directive '{0}' contains invalid characters.  RegionText cannot contain any new line characters.</summary>
        </member>
        <member name="P:System.SR.InvalidPathCharsInChecksum">
            <summary>The CodeChecksumPragma file name '{0}' contains invalid path characters.</summary>
        </member>
        <member name="P:System.SR.ExecTimeout">
            <summary>Timed out waiting for a program to execute. The command being executed was {0}.</summary>
        </member>
        <member name="P:System.SR.Provider_does_not_support_options">
            <summary>This CodeDomProvider type does not have a constructor that takes providerOptions - "{0}".</summary>
        </member>
        <member name="P:System.SR.InvalidLanguageIdentifier">
            <summary>The identifier:"{0}" on the property:"{1}" of type:"{2}" is not a valid language-independent identifier name. Check to see if CodeGenerator.IsValidLanguageIndependentIdentifier allows the identifier name.</summary>
        </member>
        <member name="P:System.SR.toStringUnknown">
            <summary>{unknown}</summary>
        </member>
        <member name="P:System.SR.AutoGen_Comment_Line1">
            <summary>auto-generated&gt;</summary>
        </member>
        <member name="P:System.SR.AutoGen_Comment_Line2">
            <summary>This code was generated by a tool.</summary>
        </member>
        <member name="P:System.SR.AutoGen_Comment_Line4">
            <summary>Changes to this file may cause incorrect behavior and will be lost if</summary>
        </member>
        <member name="P:System.SR.AutoGen_Comment_Line5">
            <summary>the code is regenerated.</summary>
        </member>
    </members>
</doc>
