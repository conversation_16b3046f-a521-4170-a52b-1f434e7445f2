<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Extensions.Logging.EventSource</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Extensions.Logging.EventSource.EventSourceLogger">
            <summary>
            A logger that writes messages to EventSource instance.
            </summary>
            <remarks>
            On Windows platforms EventSource will deliver messages using Event Tracing for Windows (ETW) events.
            On Linux EventSource will use LTTng (http://lttng.org) to deliver messages.
            </remarks>
        </member>
        <member name="T:Microsoft.Extensions.Logging.EventSource.EventSourceLogger.ActivityScope">
            <summary>
            ActivityScope is just a IDisposable that knows how to send the ActivityStop event when it is
            desposed.  It is part of the BeginScope() support.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.EventSource.EventSourceLogger.GetExceptionInfo(System.Exception)">
            <summary>
            'serializes' a given exception into an ExceptionInfo (that EventSource knows how to serialize)
            </summary>
            <param name="exception">The exception to get information for.</param>
            <returns>ExceptionInfo object represending a .NET Exception</returns>
            <remarks>ETW does not support a concept of a null value. So we use an un-initialized object if there is no exception in the event data.</remarks>
        </member>
        <member name="M:Microsoft.Extensions.Logging.EventSource.EventSourceLogger.GetProperties(System.Object)">
            <summary>
            Converts an ILogger state object into a set of key-value pairs (That can be send to a EventSource)
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Logging.EventSource.EventSourceLoggerProvider">
            <summary>
            The provider for the <see cref="T:Microsoft.Extensions.Logging.EventSource.EventSourceLogger"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.EventSource.EventSourceLoggerProvider.CreateLogger(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Extensions.Logging.EventSource.EventSourceLoggerProvider.Dispose">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.Extensions.Logging.EventSource.ExceptionInfo">
            <summary>
            Represents information about exceptions that is captured by EventSourceLogger
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Logging.EventSource.LoggingEventSource">
             <summary>
             The LoggingEventSource is the bridge from all ILogger based logging to EventSource/EventListener logging.
            
             You turn this logging on by enabling the EventSource called
            
                  Microsoft-Extensions-Logging
            
             When you enabled the EventSource, the EventLevel you set is translated in the obvious way to the level
             associated with the ILogger (thus Debug = verbose, Informational = Informational ... Critical == Critical)
            
             This allows you to filter by event level in a straightforward way.
            
             For finer control you can specify a EventSource Argument called
            
             FilterSpecs
            
             The FilterSpecs argument is a semicolon separated list of specifications.   Where each specification is
            
             SPEC =                          // empty spec, same as *
                  | NAME                     // Just a name the level is the default level
                  | NAME : LEVEL             // specifies level for a particular logger (can have a * suffix).
            
             When "UseAppFilters" is specified in the FilterSpecs, it avoids disabling all categories which happens by default otherwise.
            
             Where Name is the name of a ILoggger (case matters), Name can have a * which acts as a wildcard
             AS A SUFFIX.   Thus Net* will match any loggers that start with the 'Net'.
            
             The LEVEL is a number or a LogLevel string. 0=Trace, 1=Debug, 2=Information, 3=Warning,  4=Error, Critical=5
             This specifies the level for the associated pattern.  If the number is not specified, (first form
             of the specification) it is the default level for the EventSource.
            
             First match is used if a particular name matches more than one pattern.
            
             In addition the level and FilterSpec argument, you can also set EventSource Keywords.  See the Keywords
             definition below, but basically you get to decide if you wish to have
            
               * Keywords.Message - You get the event with the data in parsed form.
               * Keywords.JsonMessage - you get an event with the data in parse form but as a JSON blob (not broken up by argument ...)
               * Keywords.FormattedMessage - you get an event with the data formatted as a string
            
             It is expected that you will turn only one of these keywords on at a time, but you can turn them all on (and get
             the same data logged three different ways.
            
             Example Usage
            
             This example shows how to use an EventListener to get ILogging information
            
             class MyEventListener : EventListener {
                 protected override void OnEventSourceCreated(EventSource eventSource) {
                     if (eventSource.Name == "Microsoft-Extensions-Logging") {
                         // initialize a string, string dictionary of arguments to pass to the EventSource.
                         // Turn on loggers matching App* to Information, everything else (*) is the default level (which is EventLevel.Error)
                         var args = new Dictionary&lt;string, string&gt;() { { "FilterSpecs", "App*:Information;*" } };
                         // Set the default level (verbosity) to Error, and only ask for the formatted messages in this case.
                         EnableEvents(eventSource, EventLevel.Error, LoggingEventSource.Keywords.FormattedMessage, args);
                     }
                 }
                 protected override void OnEventWritten(EventWrittenEventArgs eventData) {
                     // Look for the formatted message event, which has the following argument layout (as defined in the LoggingEventSource.
                     // FormattedMessage(LogLevel Level, int FactoryID, string LoggerName, string EventId, string FormattedMessage);
                     if (eventData.EventName == "FormattedMessage")
                         Console.WriteLine("Logger {0}: {1}", eventData.Payload[2], eventData.Payload[4]);
                 }
             }
             </summary>
        </member>
        <member name="T:Microsoft.Extensions.Logging.EventSource.LoggingEventSource.Keywords">
            <summary>
            This is public from an EventSource consumer point of view, but since these defintions
            are not needed outside this class
            </summary>
        </member>
        <member name="F:Microsoft.Extensions.Logging.EventSource.LoggingEventSource.Keywords.Meta">
            <summary>
            Meta events are events about the LoggingEventSource itself (that is they did not come from ILogger
            </summary>
        </member>
        <member name="F:Microsoft.Extensions.Logging.EventSource.LoggingEventSource.Keywords.Message">
            <summary>
            Turns on the 'Message' event when ILogger.Log() is called.   It gives the information in a programmatic (not formatted) way
            </summary>
        </member>
        <member name="F:Microsoft.Extensions.Logging.EventSource.LoggingEventSource.Keywords.FormattedMessage">
            <summary>
            Turns on the 'FormatMessage' event when ILogger.Log() is called.  It gives the formatted string version of the information.
            </summary>
        </member>
        <member name="F:Microsoft.Extensions.Logging.EventSource.LoggingEventSource.Keywords.JsonMessage">
            <summary>
            Turns on the 'MessageJson' event when ILogger.Log() is called.   It gives  JSON representation of the Arguments.
            </summary>
        </member>
        <member name="F:Microsoft.Extensions.Logging.EventSource.LoggingEventSource.Instance">
            <summary>
             The one and only instance of the LoggingEventSource.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.EventSource.LoggingEventSource.FormattedMessage(Microsoft.Extensions.Logging.LogLevel,System.Int32,System.String,System.Int32,System.String,System.String)">
            <summary>
            FormattedMessage() is called when ILogger.Log() is called. and the FormattedMessage keyword is active
            This only gives you the human readable formatted message.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.EventSource.LoggingEventSource.Message(Microsoft.Extensions.Logging.LogLevel,System.Int32,System.String,System.Int32,System.String,Microsoft.Extensions.Logging.EventSource.ExceptionInfo,System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}})">
            <summary>
            Message() is called when ILogger.Log() is called. and the Message keyword is active
            This gives you the logged information in a programmatic format (arguments are key-value pairs)
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.EventSource.LoggingEventSource.ActivityStart(System.Int32,System.Int32,System.String,System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}})">
            <summary>
            ActivityStart is called when ILogger.BeginScope() is called
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.EventSource.LoggingEventSource.OnEventCommand(System.Diagnostics.Tracing.EventCommandEventArgs)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Extensions.Logging.EventSource.LoggingEventSource.SetFilterSpec(System.String)">
            <summary>
            Set the filtering specification.  null means turn off all loggers.   Empty string is turn on all providers.
            </summary>
            <param name="filterSpec">The filter specification to set.</param>
        </member>
        <member name="M:Microsoft.Extensions.Logging.EventSource.LoggingEventSource.ParseFilterSpec(System.String,Microsoft.Extensions.Logging.LogLevel)">
             <summary>
             Given a set of specifications  Pat1:Level1;Pat1;Level2 ... Where
             Pat is a string pattern (a logger Name with a optional trailing wildcard * char)
             and Level is a number 0 (Trace) through 5 (Critical).
            
             The :Level can be omitted (thus Pat1;Pat2 ...) in which case the level is 1 (Debug).
            
             A completely empty string act like * (all loggers set to Debug level).
            
             The first specification that 'loggers' Name matches is used.
             </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.EventSource.LoggingEventSource.TryParseLevel(Microsoft.Extensions.Logging.LogLevel,System.String,Microsoft.Extensions.Logging.LogLevel@)">
            <summary>
            Parses the level specification (which should look like :N where n is a  number 0 (Trace)
            through 5 (Critical).   It can also be an empty string (which means 1 (Debug) and ';' marks
            the end of the specification. This specification should start at spec[curPos]
            It returns the value in 'ret' and returns true if successful.  If false is returned ret is left unchanged.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Logging.EventSourceLoggerFactoryExtensions">
            <summary>
            Extension methods for the <see cref="T:Microsoft.Extensions.Logging.ILoggerFactory"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.EventSourceLoggerFactoryExtensions.AddEventSourceLogger(Microsoft.Extensions.Logging.ILoggingBuilder)">
            <summary>
            Adds an event logger named 'EventSource' to the factory.
            </summary>
            <param name="builder">The extension method argument.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> so that additional calls can be chained.</returns>
        </member>
        <member name="T:Microsoft.Extensions.Logging.NullExternalScopeProvider">
            <summary>
            Scope provider that does nothing.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Logging.NullExternalScopeProvider.Instance">
            <summary>
            Returns a cached instance of <see cref="T:Microsoft.Extensions.Logging.NullExternalScopeProvider"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.NullExternalScopeProvider.Microsoft#Extensions#Logging#IExternalScopeProvider#ForEachScope``1(System.Action{System.Object,``0},``0)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Extensions.Logging.NullExternalScopeProvider.Microsoft#Extensions#Logging#IExternalScopeProvider#Push(System.Object)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.Extensions.Logging.NullScope">
            <summary>
            An empty scope without any logic
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.NullScope.Dispose">
            <inheritdoc />
        </member>
    </members>
</doc>
