<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.AspNetCore.Http.Results</name>
    </assembly>
    <members>
        <member name="M:Microsoft.AspNetCore.Http.Result.AcceptedAtRouteResult.#ctor(System.Object,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Http.Result.AcceptedAtRouteResult"/> class with the values
            provided.
            </summary>
            <param name="routeValues">The route data to use for generating the URL.</param>
            <param name="value">The value to format in the entity body.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.AcceptedAtRouteResult.#ctor(System.String,System.Object,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Http.Result.AcceptedAtRouteResult"/> class with the values
            provided.
            </summary>
            <param name="routeName">The name of the route to use for generating the URL.</param>
            <param name="routeValues">The route data to use for generating the URL.</param>
            <param name="value">The value to format in the entity body.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Result.AcceptedAtRouteResult.RouteName">
            <summary>
            Gets the name of the route to use for generating the URL.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Result.AcceptedAtRouteResult.RouteValues">
            <summary>
            Gets the route data to use for generating the URL.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.AcceptedAtRouteResult.ConfigureResponseHeaders(Microsoft.AspNetCore.Http.HttpContext)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.AcceptedResult.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Http.Result.AcceptedResult"/> class with the values
            provided.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.AcceptedResult.#ctor(System.String,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Http.Result.AcceptedResult"/> class with the values
            provided.
            </summary>
            <param name="location">The location at which the status of requested content can be monitored.</param>
            <param name="value">The value to format in the entity body.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.AcceptedResult.#ctor(System.Uri,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Http.Result.AcceptedResult"/> class with the values
            provided.
            </summary>
            <param name="locationUri">The location at which the status of requested content can be monitored.</param>
            <param name="value">The value to format in the entity body.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Result.AcceptedResult.Location">
            <summary>
            Gets or sets the location at which the status of the requested content can be monitored.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.AcceptedResult.ConfigureResponseHeaders(Microsoft.AspNetCore.Http.HttpContext)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Http.Result.ChallengeResult">
            <summary>
            An <see cref="T:Microsoft.AspNetCore.Http.IResult"/> that on execution invokes <see cref="M:HttpContext.ChallengeAsync"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.ChallengeResult.#ctor">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Http.Result.ChallengeResult"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.ChallengeResult.#ctor(System.String)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Http.Result.ChallengeResult"/> with the
            specified authentication scheme.
            </summary>
            <param name="authenticationScheme">The authentication scheme to challenge.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.ChallengeResult.#ctor(System.Collections.Generic.IList{System.String})">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Http.Result.ChallengeResult"/> with the
            specified authentication schemes.
            </summary>
            <param name="authenticationSchemes">The authentication schemes to challenge.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.ChallengeResult.#ctor(Microsoft.AspNetCore.Authentication.AuthenticationProperties)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Http.Result.ChallengeResult"/> with the
            specified <paramref name="properties"/>.
            </summary>
            <param name="properties"><see cref="T:Microsoft.AspNetCore.Authentication.AuthenticationProperties"/> used to perform the authentication
            challenge.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.ChallengeResult.#ctor(System.String,Microsoft.AspNetCore.Authentication.AuthenticationProperties)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Http.Result.ChallengeResult"/> with the
            specified authentication scheme and <paramref name="properties"/>.
            </summary>
            <param name="authenticationScheme">The authentication schemes to challenge.</param>
            <param name="properties"><see cref="T:Microsoft.AspNetCore.Authentication.AuthenticationProperties"/> used to perform the authentication
            challenge.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.ChallengeResult.#ctor(System.Collections.Generic.IList{System.String},Microsoft.AspNetCore.Authentication.AuthenticationProperties)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Http.Result.ChallengeResult"/> with the
            specified authentication schemes and <paramref name="properties"/>.
            </summary>
            <param name="authenticationSchemes">The authentication scheme to challenge.</param>
            <param name="properties"><see cref="T:Microsoft.AspNetCore.Authentication.AuthenticationProperties"/> used to perform the authentication
            challenge.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Result.ContentResult.Content">
            <summary>
            Gets or set the content representing the body of the response.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Result.ContentResult.ContentType">
            <summary>
            Gets or sets the Content-Type header for the response.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Result.ContentResult.StatusCode">
            <summary>
            Gets or sets the HTTP status code.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.ContentResult.ExecuteAsync(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            Writes the content to the HTTP response.
            </summary>
            <param name="httpContext">The <see cref="T:Microsoft.AspNetCore.Http.HttpContext"/> for the current request.</param>
            <returns>A task that represents the asynchronous execute operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.CreatedAtRouteResult.#ctor(System.Object,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Http.Result.CreatedAtRouteResult"/> class with the values
            provided.
            </summary>
            <param name="routeValues">The route data to use for generating the URL.</param>
            <param name="value">The value to format in the entity body.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.CreatedAtRouteResult.#ctor(System.String,System.Object,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Http.Result.CreatedAtRouteResult"/> class with the values
            provided.
            </summary>
            <param name="routeName">The name of the route to use for generating the URL.</param>
            <param name="routeValues">The route data to use for generating the URL.</param>
            <param name="value">The value to format in the entity body.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Result.CreatedAtRouteResult.RouteName">
            <summary>
            Gets or sets the name of the route to use for generating the URL.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Result.CreatedAtRouteResult.RouteValues">
            <summary>
            Gets or sets the route data to use for generating the URL.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.CreatedAtRouteResult.ConfigureResponseHeaders(Microsoft.AspNetCore.Http.HttpContext)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.CreatedResult.#ctor(System.String,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Http.Result.CreatedResult"/> class with the values
            provided.
            </summary>
            <param name="location">The location at which the content has been created.</param>
            <param name="value">The value to format in the entity body.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.CreatedResult.#ctor(System.Uri,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Http.Result.CreatedResult"/> class with the values
            provided.
            </summary>
            <param name="location">The location at which the content has been created.</param>
            <param name="value">The value to format in the entity body.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Result.CreatedResult.Location">
            <summary>
            Gets or sets the location at which the content has been created.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.CreatedResult.ConfigureResponseHeaders(Microsoft.AspNetCore.Http.HttpContext)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.FileContentResult.#ctor(System.Byte[],System.String)">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Http.Result.FileContentResult"/> instance with
            the provided <paramref name="fileContents"/> and the
            provided <paramref name="contentType"/>.
            </summary>
            <param name="fileContents">The bytes that represent the file contents.</param>
            <param name="contentType">The Content-Type header of the response.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Result.FileContentResult.FileContents">
            <summary>
            Gets or sets the file contents.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.FileResult.#ctor(System.String)">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Http.Result.FileResult"/> instance with
            the provided <paramref name="contentType"/>.
            </summary>
            <param name="contentType">The Content-Type header of the response.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Result.FileResult.ContentType">
            <summary>
            Gets the Content-Type header for the response.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Result.FileResult.FileDownloadName">
            <summary>
            Gets the file name that will be used in the Content-Disposition header of the response.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Result.FileResult.LastModified">
            <summary>
            Gets or sets the last modified information associated with the <see cref="T:Microsoft.AspNetCore.Http.Result.FileResult"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Result.FileResult.EntityTag">
            <summary>
            Gets or sets the etag associated with the <see cref="T:Microsoft.AspNetCore.Http.Result.FileResult"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Result.FileResult.EnableRangeProcessing">
            <summary>
            Gets or sets the value that enables range processing for the <see cref="T:Microsoft.AspNetCore.Http.Result.FileResult"/>.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Http.Result.FileStreamResult">
            <summary>
            Represents an <see cref="T:Microsoft.AspNetCore.Http.Result.FileResult"/> that when executed will
            write a file from a stream to the response.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.FileStreamResult.#ctor(System.IO.Stream,System.String)">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Http.Result.FileStreamResult"/> instance with
            the provided <paramref name="fileStream"/> and the
            provided <paramref name="contentType"/>.
            </summary>
            <param name="fileStream">The stream with the file.</param>
            <param name="contentType">The Content-Type header of the response.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Result.FileStreamResult.FileStream">
            <summary>
            Gets or sets the stream with the file that will be sent back as the response.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.ForbidResult.#ctor">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Http.Result.ForbidResult"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.ForbidResult.#ctor(System.String)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Http.Result.ForbidResult"/> with the
            specified authentication scheme.
            </summary>
            <param name="authenticationScheme">The authentication scheme to challenge.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.ForbidResult.#ctor(System.Collections.Generic.IList{System.String})">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Http.Result.ForbidResult"/> with the
            specified authentication schemes.
            </summary>
            <param name="authenticationSchemes">The authentication schemes to challenge.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.ForbidResult.#ctor(Microsoft.AspNetCore.Authentication.AuthenticationProperties)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Http.Result.ForbidResult"/> with the
            specified <paramref name="properties"/>.
            </summary>
            <param name="properties"><see cref="T:Microsoft.AspNetCore.Authentication.AuthenticationProperties"/> used to perform the authentication
            challenge.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.ForbidResult.#ctor(System.String,Microsoft.AspNetCore.Authentication.AuthenticationProperties)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Http.Result.ForbidResult"/> with the
            specified authentication scheme and <paramref name="properties"/>.
            </summary>
            <param name="authenticationScheme">The authentication schemes to challenge.</param>
            <param name="properties"><see cref="T:Microsoft.AspNetCore.Authentication.AuthenticationProperties"/> used to perform the authentication
            challenge.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.ForbidResult.#ctor(System.Collections.Generic.IList{System.String},Microsoft.AspNetCore.Authentication.AuthenticationProperties)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Http.Result.ForbidResult"/> with the
            specified authentication schemes and <paramref name="properties"/>.
            </summary>
            <param name="authenticationSchemes">The authentication scheme to challenge.</param>
            <param name="properties"><see cref="T:Microsoft.AspNetCore.Authentication.AuthenticationProperties"/> used to perform the authentication
            challenge.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Result.ForbidResult.AuthenticationSchemes">
            <summary>
            Gets or sets the authentication schemes that are challenged.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Result.ForbidResult.Properties">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Authentication.AuthenticationProperties"/> used to perform the authentication challenge.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.ForbidResult.ExecuteAsync(Microsoft.AspNetCore.Http.HttpContext)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Http.Result.JsonResult">
            <summary>
            An action result which formats the given object as JSON.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Result.JsonResult.ContentType">
            <summary>
            Gets or sets the <see cref="T:Microsoft.Net.Http.Headers.MediaTypeHeaderValue"/> representing the Content-Type header of the response.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Result.JsonResult.JsonSerializerOptions">
            <summary>
            Gets or sets the serializer settings.
            <para>
            When using <c>System.Text.Json</c>, this should be an instance of <see cref="P:Microsoft.AspNetCore.Http.Result.JsonResult.JsonSerializerOptions" />
            </para>
            <para>
            When using <c>Newtonsoft.Json</c>, this should be an instance of <c>JsonSerializerSettings</c>.
            </para>
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Result.JsonResult.StatusCode">
            <summary>
            Gets or sets the HTTP status code.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Result.JsonResult.Value">
            <summary>
            Gets or sets the value to be formatted.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.JsonResult.Microsoft#AspNetCore#Http#IResult#ExecuteAsync(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            Write the result as JSON to the HTTP response.
            </summary>
            <param name="httpContext">The <see cref="T:Microsoft.AspNetCore.Http.HttpContext"/> for the current request.</param>
            <returns>A task that represents the asynchronous execute operation.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Http.Result.LocalRedirectResult">
            <summary>
            An <see cref="T:Microsoft.AspNetCore.Http.IResult"/> that returns a Found (302), Moved Permanently (301), Temporary Redirect (307),
            or Permanent Redirect (308) response with a Location header to the supplied local URL.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.LocalRedirectResult.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Http.Result.LocalRedirectResult"/> class with the values
            provided.
            </summary>
            <param name="localUrl">The local URL to redirect to.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.LocalRedirectResult.#ctor(System.String,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Http.Result.LocalRedirectResult"/> class with the values
            provided.
            </summary>
            <param name="localUrl">The local URL to redirect to.</param>
            <param name="permanent">Specifies whether the redirect should be permanent (301) or temporary (302).</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.LocalRedirectResult.#ctor(System.String,System.Boolean,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Http.Result.LocalRedirectResult"/> class with the values
            provided.
            </summary>
            <param name="localUrl">The local URL to redirect to.</param>
            <param name="permanent">Specifies whether the redirect should be permanent (301) or temporary (302).</param>
            <param name="preserveMethod">If set to true, make the temporary redirect (307) or permanent redirect (308) preserve the initial request's method.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Result.LocalRedirectResult.Permanent">
            <summary>
            Gets or sets the value that specifies that the redirect should be permanent if true or temporary if false.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Result.LocalRedirectResult.PreserveMethod">
            <summary>
            Gets or sets an indication that the redirect preserves the initial request method.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Result.LocalRedirectResult.Url">
            <summary>
            Gets or sets the local URL to redirect to.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.LocalRedirectResult.ExecuteAsync(Microsoft.AspNetCore.Http.HttpContext)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.ObjectResult.#ctor(System.Object)">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Http.Result.ObjectResult"/> instance with the provided <paramref name="value"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.ObjectResult.#ctor(System.Object,System.Nullable{System.Int32})">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Http.Result.ObjectResult"/> instance with the provided <paramref name="value"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Result.ObjectResult.Value">
            <summary>
            The object result.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Result.ObjectResult.StatusCode">
            <summary>
            Gets the HTTP status code.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Result.ObjectResult.ContentType">
            <summary>
            Gets the value for the <c>Content-Type</c> header.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Http.Result.PhysicalFileResult">
            <summary>
            A <see cref="T:Microsoft.AspNetCore.Http.Result.PhysicalFileResult"/> on execution will write a file from disk to the response
            using mechanisms provided by the host.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.PhysicalFileResult.#ctor(System.String,System.String)">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Http.Result.PhysicalFileResult"/> instance with
            the provided <paramref name="fileName"/> and the provided <paramref name="contentType"/>.
            </summary>
            <param name="fileName">The path to the file. The path must be an absolute path.</param>
            <param name="contentType">The Content-Type header of the response.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Result.PhysicalFileResult.FileName">
            <summary>
            Gets or sets the path to the file that will be sent back as the response.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.RedirectResult.#ctor(System.String,System.Boolean,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Http.Result.RedirectResult"/> class with the values
            provided.
            </summary>
            <param name="url">The URL to redirect to.</param>
            <param name="permanent">Specifies whether the redirect should be permanent (301) or temporary (302).</param>
            <param name="preserveMethod">If set to true, make the temporary redirect (307) or permanent redirect (308) preserve the initial request method.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Result.RedirectResult.Permanent">
            <summary>
            Gets or sets the value that specifies that the redirect should be permanent if true or temporary if false.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Result.RedirectResult.PreserveMethod">
            <summary>
            Gets or sets an indication that the redirect preserves the initial request method.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Result.RedirectResult.Url">
            <summary>
            Gets or sets the URL to redirect to.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.RedirectResult.ExecuteAsync(Microsoft.AspNetCore.Http.HttpContext)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Http.Result.RedirectToRouteResult">
            <summary>
            An <see cref="T:Microsoft.AspNetCore.Http.IResult"/> that returns a Found (302), Moved Permanently (301), Temporary Redirect (307),
            or Permanent Redirect (308) response with a Location header.
            Targets a registered route.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.RedirectToRouteResult.#ctor(System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Http.Result.RedirectToRouteResult"/> with the values
            provided.
            </summary>
            <param name="routeValues">The parameters for the route.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.RedirectToRouteResult.#ctor(System.String,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Http.Result.RedirectToRouteResult"/> with the values
            provided.
            </summary>
            <param name="routeName">The name of the route.</param>
            <param name="routeValues">The parameters for the route.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.RedirectToRouteResult.#ctor(System.String,System.Object,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Http.Result.RedirectToRouteResult"/> with the values
            provided.
            </summary>
            <param name="routeName">The name of the route.</param>
            <param name="routeValues">The parameters for the route.</param>
            <param name="permanent">If set to true, makes the redirect permanent (301). Otherwise a temporary redirect is used (302).</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.RedirectToRouteResult.#ctor(System.String,System.Object,System.Boolean,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Http.Result.RedirectToRouteResult"/> with the values
            provided.
            </summary>
            <param name="routeName">The name of the route.</param>
            <param name="routeValues">The parameters for the route.</param>
            <param name="permanent">If set to true, makes the redirect permanent (301). Otherwise a temporary redirect is used (302).</param>
            <param name="preserveMethod">If set to true, make the temporary redirect (307) or permanent redirect (308) preserve the initial request method.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.RedirectToRouteResult.#ctor(System.String,System.Object,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Http.Result.RedirectToRouteResult"/> with the values
            provided.
            </summary>
            <param name="routeName">The name of the route.</param>
            <param name="routeValues">The parameters for the route.</param>
            <param name="fragment">The fragment to add to the URL.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.RedirectToRouteResult.#ctor(System.String,System.Object,System.Boolean,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Http.Result.RedirectToRouteResult"/> with the values
            provided.
            </summary>
            <param name="routeName">The name of the route.</param>
            <param name="routeValues">The parameters for the route.</param>
            <param name="permanent">If set to true, makes the redirect permanent (301). Otherwise a temporary redirect is used (302).</param>
            <param name="fragment">The fragment to add to the URL.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.RedirectToRouteResult.#ctor(System.String,System.Object,System.Boolean,System.Boolean,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Http.Result.RedirectToRouteResult"/> with the values
            provided.
            </summary>
            <param name="routeName">The name of the route.</param>
            <param name="routeValues">The parameters for the route.</param>
            <param name="permanent">If set to true, makes the redirect permanent (301). Otherwise a temporary redirect is used (302).</param>
            <param name="preserveMethod">If set to true, make the temporary redirect (307) or permanent redirect (308) preserve the initial request method.</param>
            <param name="fragment">The fragment to add to the URL.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Result.RedirectToRouteResult.RouteName">
            <summary>
            Gets or sets the name of the route to use for generating the URL.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Result.RedirectToRouteResult.RouteValues">
            <summary>
            Gets or sets the route data to use for generating the URL.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Result.RedirectToRouteResult.Permanent">
            <summary>
            Gets or sets an indication that the redirect is permanent.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Result.RedirectToRouteResult.PreserveMethod">
            <summary>
            Gets or sets an indication that the redirect preserves the initial request method.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Result.RedirectToRouteResult.Fragment">
            <summary>
            Gets or sets the fragment to add to the URL.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.RedirectToRouteResult.ExecuteAsync(Microsoft.AspNetCore.Http.HttpContext)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Http.Result.SignInResult">
            <summary>
            An <see cref="T:Microsoft.AspNetCore.Http.IResult"/> that on execution invokes <see cref="M:HttpContext.SignInAsync"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.SignInResult.#ctor(System.Security.Claims.ClaimsPrincipal)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Http.Result.SignInResult"/> with the
            default authentication scheme.
            </summary>
            <param name="principal">The claims principal containing the user claims.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.SignInResult.#ctor(System.String,System.Security.Claims.ClaimsPrincipal)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Http.Result.SignInResult"/> with the
            specified authentication scheme.
            </summary>
            <param name="authenticationScheme">The authentication scheme to use when signing in the user.</param>
            <param name="principal">The claims principal containing the user claims.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.SignInResult.#ctor(System.Security.Claims.ClaimsPrincipal,Microsoft.AspNetCore.Authentication.AuthenticationProperties)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Http.Result.SignInResult"/> with the
            default authentication scheme and <paramref name="properties"/>.
            </summary>
            <param name="principal">The claims principal containing the user claims.</param>
            <param name="properties"><see cref="T:Microsoft.AspNetCore.Authentication.AuthenticationProperties"/> used to perform the sign-in operation.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.SignInResult.#ctor(System.String,System.Security.Claims.ClaimsPrincipal,Microsoft.AspNetCore.Authentication.AuthenticationProperties)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Http.Result.SignInResult"/> with the
            specified authentication scheme and <paramref name="properties"/>.
            </summary>
            <param name="authenticationScheme">The authentication schemes to use when signing in the user.</param>
            <param name="principal">The claims principal containing the user claims.</param>
            <param name="properties"><see cref="T:Microsoft.AspNetCore.Authentication.AuthenticationProperties"/> used to perform the sign-in operation.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Result.SignInResult.AuthenticationScheme">
            <summary>
            Gets or sets the authentication scheme that is used to perform the sign-in operation.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Result.SignInResult.Principal">
            <summary>
            Gets or sets the <see cref="T:System.Security.Claims.ClaimsPrincipal"/> containing the user claims.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Result.SignInResult.Properties">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Authentication.AuthenticationProperties"/> used to perform the sign-in operation.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.SignInResult.ExecuteAsync(Microsoft.AspNetCore.Http.HttpContext)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Http.Result.SignOutResult">
            <summary>
            An <see cref="T:Microsoft.AspNetCore.Http.IResult"/> that on execution invokes <see cref="M:HttpContext.SignOutAsync"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.SignOutResult.#ctor">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Http.Result.SignOutResult"/> with the default sign out scheme.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.SignOutResult.#ctor(Microsoft.AspNetCore.Authentication.AuthenticationProperties)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Http.Result.SignOutResult"/> with the default sign out scheme.
            specified authentication scheme and <paramref name="properties"/>.
            </summary>
            <param name="properties"><see cref="T:Microsoft.AspNetCore.Authentication.AuthenticationProperties"/> used to perform the sign-out operation.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.SignOutResult.#ctor(System.String)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Http.Result.SignOutResult"/> with the
            specified authentication scheme.
            </summary>
            <param name="authenticationScheme">The authentication scheme to use when signing out the user.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.SignOutResult.#ctor(System.Collections.Generic.IList{System.String})">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Http.Result.SignOutResult"/> with the
            specified authentication schemes.
            </summary>
            <param name="authenticationSchemes">The authentication schemes to use when signing out the user.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.SignOutResult.#ctor(System.String,Microsoft.AspNetCore.Authentication.AuthenticationProperties)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Http.Result.SignOutResult"/> with the
            specified authentication scheme and <paramref name="properties"/>.
            </summary>
            <param name="authenticationScheme">The authentication schemes to use when signing out the user.</param>
            <param name="properties"><see cref="T:Microsoft.AspNetCore.Authentication.AuthenticationProperties"/> used to perform the sign-out operation.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.SignOutResult.#ctor(System.Collections.Generic.IList{System.String},Microsoft.AspNetCore.Authentication.AuthenticationProperties)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Http.Result.SignOutResult"/> with the
            specified authentication schemes and <paramref name="properties"/>.
            </summary>
            <param name="authenticationSchemes">The authentication scheme to use when signing out the user.</param>
            <param name="properties"><see cref="T:Microsoft.AspNetCore.Authentication.AuthenticationProperties"/> used to perform the sign-out operation.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Result.SignOutResult.AuthenticationSchemes">
            <summary>
            Gets or sets the authentication schemes that are challenged.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Result.SignOutResult.Properties">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Authentication.AuthenticationProperties"/> used to perform the sign-out operation.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.SignOutResult.ExecuteAsync(Microsoft.AspNetCore.Http.HttpContext)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.StatusCodeResult.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Http.Result.StatusCodeResult"/> class
            with the given <paramref name="statusCode"/>.
            </summary>
            <param name="statusCode">The HTTP status code of the response.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Result.StatusCodeResult.StatusCode">
            <summary>
            Gets the HTTP status code.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.StatusCodeResult.ExecuteAsync(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            Sets the status code on the HTTP response.
            </summary>
            <param name="httpContext">The <see cref="T:Microsoft.AspNetCore.Http.HttpContext"/> for the current request.</param>
            <returns>A task that represents the asynchronous execute operation.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Http.Result.VirtualFileResult">
            <summary>
            A <see cref="T:Microsoft.AspNetCore.Http.Result.FileResult" /> that on execution writes the file specified using a virtual path to the response
            using mechanisms provided by the host.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.VirtualFileResult.#ctor(System.String,System.String)">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Http.Result.VirtualFileResult"/> instance with the provided <paramref name="fileName"/>
            and the provided <paramref name="contentType"/>.
            </summary>
            <param name="fileName">The path to the file. The path must be relative/virtual.</param>
            <param name="contentType">The Content-Type header of the response.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Result.VirtualFileResult.FileName">
            <summary>
            Gets or sets the path to the file that will be sent back as the response.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Result.VirtualFileResult.ExecuteAsync(Microsoft.AspNetCore.Http.HttpContext)">
            <inheritdoc/>
        </member>
        <member name="T:Microsoft.AspNetCore.Http.IResultExtensions">
            <summary>
            Provides an interface for registering external methods that provide
            custom <see cref="T:Microsoft.AspNetCore.Http.IResult" /> instances.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Http.ResultExtensions">
            <summary>
            Implements an interface for registering external methods that provide
            custom <see cref="T:Microsoft.AspNetCore.Http.IResult"/> instances.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Http.Results">
            <summary>
            A factory for <see cref="T:Microsoft.AspNetCore.Http.IResult"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Results.Challenge(Microsoft.AspNetCore.Authentication.AuthenticationProperties,System.Collections.Generic.IList{System.String})">
            <summary>
            Creates an <see cref="T:Microsoft.AspNetCore.Http.IResult"/> that on execution invokes <see cref="M:Microsoft.AspNetCore.Authentication.AuthenticationHttpContextExtensions.ChallengeAsync(Microsoft.AspNetCore.Http.HttpContext,System.String,Microsoft.AspNetCore.Authentication.AuthenticationProperties)" />.
            <para>
            The behavior of this method depends on the <see cref="T:Microsoft.AspNetCore.Authentication.IAuthenticationService"/> in use.
            <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status401Unauthorized"/> and <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status403Forbidden"/>
            are among likely status results.
            </para>
            </summary>
            <param name="properties"><see cref="T:Microsoft.AspNetCore.Authentication.AuthenticationProperties"/> used to perform the authentication
            challenge.</param>
            <param name="authenticationSchemes">The authentication schemes to challenge.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Http.IResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Results.Forbid(Microsoft.AspNetCore.Authentication.AuthenticationProperties,System.Collections.Generic.IList{System.String})">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Http.IResult"/> that on execution invokes <see cref="M:Microsoft.AspNetCore.Authentication.AuthenticationHttpContextExtensions.ForbidAsync(Microsoft.AspNetCore.Http.HttpContext,System.String,Microsoft.AspNetCore.Authentication.AuthenticationProperties)"/>.
            <para>
            By default, executing this result returns a <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status403Forbidden"/>. Some authentication schemes, such as cookies,
            will convert <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status403Forbidden"/> to a redirect to show a login page.
            </para>
            </summary>
            <param name="properties"><see cref="T:Microsoft.AspNetCore.Authentication.AuthenticationProperties"/> used to perform the authentication
            challenge.</param>
            <param name="authenticationSchemes">The authentication schemes to challenge.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Http.IResult"/> for the response.</returns>
            <remarks>
            Some authentication schemes, such as cookies, will convert <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status403Forbidden"/> to
            a redirect to show a login page.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Results.SignIn(System.Security.Claims.ClaimsPrincipal,Microsoft.AspNetCore.Authentication.AuthenticationProperties,System.String)">
            <summary>
            Creates an <see cref="T:Microsoft.AspNetCore.Http.IResult"/> that on execution invokes <see cref="M:Microsoft.AspNetCore.Authentication.AuthenticationHttpContextExtensions.SignInAsync(Microsoft.AspNetCore.Http.HttpContext,System.String,System.Security.Claims.ClaimsPrincipal,Microsoft.AspNetCore.Authentication.AuthenticationProperties)" />.
            </summary>
            <param name="principal">The <see cref="T:System.Security.Claims.ClaimsPrincipal"/> containing the user claims.</param>
            <param name="properties"><see cref="T:Microsoft.AspNetCore.Authentication.AuthenticationProperties"/> used to perform the sign-in operation.</param>
            <param name="authenticationScheme">The authentication scheme to use for the sign-in operation.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Http.IResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Results.SignOut(Microsoft.AspNetCore.Authentication.AuthenticationProperties,System.Collections.Generic.IList{System.String})">
            <summary>
            Creates an <see cref="T:Microsoft.AspNetCore.Http.IResult"/> that on execution invokes <see cref="M:Microsoft.AspNetCore.Authentication.AuthenticationHttpContextExtensions.SignOutAsync(Microsoft.AspNetCore.Http.HttpContext,System.String,Microsoft.AspNetCore.Authentication.AuthenticationProperties)" />.
            </summary>
            <param name="properties"><see cref="T:Microsoft.AspNetCore.Authentication.AuthenticationProperties"/> used to perform the sign-out operation.</param>
            <param name="authenticationSchemes">The authentication scheme to use for the sign-out operation.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Http.IResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Results.Content(System.String,System.String,System.Text.Encoding)">
            <summary>
            Writes the <paramref name="content"/> string to the HTTP response.
            <para>
            This is an alias for <see cref="M:Microsoft.AspNetCore.Http.Results.Text(System.String,System.String,System.Text.Encoding)"/>.
            </para>
            </summary>
            <param name="content">The content to write to the response.</param>
            <param name="contentType">The content type (MIME type).</param>
            <param name="contentEncoding">The content encoding.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Http.IResult"/> object for the response.</returns>
            <remarks>
            If encoding is provided by both the 'charset' and the <paramref name="contentEncoding"/> parameters, then
            the <paramref name="contentEncoding"/> parameter is chosen as the final encoding.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Results.Text(System.String,System.String,System.Text.Encoding)">
            <summary>
            Writes the <paramref name="content"/> string to the HTTP response.
            <para>
            This is an alias for <see cref="M:Microsoft.AspNetCore.Http.Results.Content(System.String,System.String,System.Text.Encoding)"/>.
            </para>
            </summary>
            <param name="content">The content to write to the response.</param>
            <param name="contentType">The content type (MIME type).</param>
            <param name="contentEncoding">The content encoding.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Http.IResult"/> object for the response.</returns>
            <remarks>
            If encoding is provided by both the 'charset' and the <paramref name="contentEncoding"/> parameters, then
            the <paramref name="contentEncoding"/> parameter is chosen as the final encoding.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Results.Content(System.String,Microsoft.Net.Http.Headers.MediaTypeHeaderValue)">
            <summary>
            Writes the <paramref name="content"/> string to the HTTP response.
            </summary>
            <param name="content">The content to write to the response.</param>
            <param name="contentType">The content type (MIME type).</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Http.IResult"/> object for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Results.Json(System.Object,System.Text.Json.JsonSerializerOptions,System.String,System.Nullable{System.Int32})">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Http.IResult"/> that serializes the specified <paramref name="data"/> object to JSON.
            </summary>
            <param name="data">The object to write as JSON.</param>
            <param name="options">The serializer options use when serializing the value.</param>
            <param name="contentType">The content-type to set on the response.</param>
            <param name="statusCode">The status code to set on the response.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Http.Result.JsonResult"/> that serializes the specified <paramref name="data"/>
            as JSON format for the response.</returns>
            <remarks>Callers should cache an instance of serializer settings to avoid
            recreating cached data with each call.</remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Results.File(System.Byte[],System.String,System.String,System.Boolean,System.Nullable{System.DateTimeOffset},Microsoft.Net.Http.Headers.EntityTagHeaderValue)">
            <summary>
            Writes the byte-array content to the response.
            <para>
            This supports range requests (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status206PartialContent"/> or
            <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status416RangeNotSatisfiable"/> if the range is not satisfiable).
            </para>
            <para>
            This API is an alias for <see cref="M:Microsoft.AspNetCore.Http.Results.Bytes(System.Byte[],System.String,System.String,System.Boolean,System.Nullable{System.DateTimeOffset},Microsoft.Net.Http.Headers.EntityTagHeaderValue)"/>.</para>
            </summary>
            <param name="fileContents">The file contents.</param>
            <param name="contentType">The Content-Type of the file.</param>
            <param name="fileDownloadName">The suggested file name.</param>
            <param name="enableRangeProcessing">Set to <c>true</c> to enable range requests processing.</param>
            <param name="lastModified">The <see cref="T:System.DateTimeOffset"/> of when the file was last modified.</param>
            <param name="entityTag">The <see cref="T:Microsoft.Net.Http.Headers.EntityTagHeaderValue"/> associated with the file.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Http.IResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Results.Bytes(System.Byte[],System.String,System.String,System.Boolean,System.Nullable{System.DateTimeOffset},Microsoft.Net.Http.Headers.EntityTagHeaderValue)">
            <summary>
            Writes the byte-array content to the response.
            <para>
            This supports range requests (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status206PartialContent"/> or
            <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status416RangeNotSatisfiable"/> if the range is not satisfiable).
            </para>
            <para>
            This API is an alias for <see cref="M:Microsoft.AspNetCore.Http.Results.File(System.Byte[],System.String,System.String,System.Boolean,System.Nullable{System.DateTimeOffset},Microsoft.Net.Http.Headers.EntityTagHeaderValue)"/>.</para>
            </summary>
            <param name="contents">The file contents.</param>
            <param name="contentType">The Content-Type of the file.</param>
            <param name="fileDownloadName">The suggested file name.</param>
            <param name="enableRangeProcessing">Set to <c>true</c> to enable range requests processing.</param>
            <param name="lastModified">The <see cref="T:System.DateTimeOffset"/> of when the file was last modified.</param>
            <param name="entityTag">The <see cref="T:Microsoft.Net.Http.Headers.EntityTagHeaderValue"/> associated with the file.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Http.IResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Results.File(System.IO.Stream,System.String,System.String,System.Nullable{System.DateTimeOffset},Microsoft.Net.Http.Headers.EntityTagHeaderValue,System.Boolean)">
            <summary>
            Writes the specified <see cref="M:Microsoft.AspNetCore.Http.Results.Stream(System.IO.Stream,System.String,System.String,System.Nullable{System.DateTimeOffset},Microsoft.Net.Http.Headers.EntityTagHeaderValue,System.Boolean)"/> to the response.
            <para>
            This supports range requests (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status206PartialContent"/> or
            <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status416RangeNotSatisfiable"/> if the range is not satisfiable).
            </para>
            <para>
            This API is an alias for <see cref="M:Microsoft.AspNetCore.Http.Results.Stream(System.IO.Stream,System.String,System.String,System.Nullable{System.DateTimeOffset},Microsoft.Net.Http.Headers.EntityTagHeaderValue,System.Boolean)"/>.
            </para>
            </summary>
            <param name="fileStream">The <see cref="M:Microsoft.AspNetCore.Http.Results.Stream(System.IO.Stream,System.String,System.String,System.Nullable{System.DateTimeOffset},Microsoft.Net.Http.Headers.EntityTagHeaderValue,System.Boolean)"/> with the contents of the file.</param>
            <param name="contentType">The Content-Type of the file.</param>
            <param name="fileDownloadName">The the file name to be used in the <c>Content-Disposition</c> header.</param>
            <param name="lastModified">The <see cref="T:System.DateTimeOffset"/> of when the file was last modified.
            Used to configure the <c>Last-Modified</c> response header and perform conditional range requests.</param>
            <param name="entityTag">The <see cref="T:Microsoft.Net.Http.Headers.EntityTagHeaderValue"/> to be configure the <c>ETag</c> response header
            and perform conditional requests.</param>
            <param name="enableRangeProcessing">Set to <c>true</c> to enable range requests processing.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Http.IResult"/> for the response.</returns>
            <remarks>
            The <paramref name="fileStream" /> parameter is disposed after the response is sent.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Results.Stream(System.IO.Stream,System.String,System.String,System.Nullable{System.DateTimeOffset},Microsoft.Net.Http.Headers.EntityTagHeaderValue,System.Boolean)">
            <summary>
            Writes the specified <see cref="M:Microsoft.AspNetCore.Http.Results.Stream(System.IO.Stream,System.String,System.String,System.Nullable{System.DateTimeOffset},Microsoft.Net.Http.Headers.EntityTagHeaderValue,System.Boolean)"/> to the response.
            <para>
            This supports range requests (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status206PartialContent"/> or
            <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status416RangeNotSatisfiable"/> if the range is not satisfiable).
            </para>
            <para>
            This API is an alias for <see cref="M:Microsoft.AspNetCore.Http.Results.File(System.IO.Stream,System.String,System.String,System.Nullable{System.DateTimeOffset},Microsoft.Net.Http.Headers.EntityTagHeaderValue,System.Boolean)"/>.
            </para>
            </summary>
            <param name="stream">The <see cref="M:Microsoft.AspNetCore.Http.Results.Stream(System.IO.Stream,System.String,System.String,System.Nullable{System.DateTimeOffset},Microsoft.Net.Http.Headers.EntityTagHeaderValue,System.Boolean)"/> to write to the response.</param>
            <param name="contentType">The <c>Content-Type</c> of the response. Defaults to <c>application/octet-stream</c>.</param>
            <param name="fileDownloadName">The the file name to be used in the <c>Content-Disposition</c> header.</param>
            <param name="lastModified">The <see cref="T:System.DateTimeOffset"/> of when the file was last modified.
            Used to configure the <c>Last-Modified</c> response header and perform conditional range requests.</param>
            <param name="entityTag">The <see cref="T:Microsoft.Net.Http.Headers.EntityTagHeaderValue"/> to be configure the <c>ETag</c> response header
            and perform conditional requests.</param>
            <param name="enableRangeProcessing">Set to <c>true</c> to enable range requests processing.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Http.IResult"/> for the response.</returns>
            <remarks>
            The <paramref name="stream" /> parameter is disposed after the response is sent.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Results.File(System.String,System.String,System.String,System.Nullable{System.DateTimeOffset},Microsoft.Net.Http.Headers.EntityTagHeaderValue,System.Boolean)">
            <summary>
            Writes the file at the specified <paramref name="path"/> to the response.
            <para>
            This supports range requests (<see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status206PartialContent"/> or
            <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status416RangeNotSatisfiable"/> if the range is not satisfiable).
            </para>
            </summary>
            <param name="path">The path to the file. When not rooted, resolves the path relative to <see cref="P:Microsoft.AspNetCore.Hosting.IWebHostEnvironment.WebRootFileProvider"/>.</param>
            <param name="contentType">The Content-Type of the file.</param>
            <param name="fileDownloadName">The suggested file name.</param>
            <param name="lastModified">The <see cref="T:System.DateTimeOffset"/> of when the file was last modified.</param>
            <param name="entityTag">The <see cref="T:Microsoft.Net.Http.Headers.EntityTagHeaderValue"/> associated with the file.</param>
            <param name="enableRangeProcessing">Set to <c>true</c> to enable range requests processing.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Http.IResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Results.Redirect(System.String,System.Boolean,System.Boolean)">
            <summary>
            Redirects to the specified <paramref name="url"/>.
            <list type="bullet">
            <item>When <paramref name="permanent"/> and <paramref name="preserveMethod"/> are set, sets the <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status308PermanentRedirect"/> status code.</item>
            <item>When <paramref name="preserveMethod"/> is set, sets the <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status307TemporaryRedirect"/> status code.</item>
            <item>When <paramref name="permanent"/> is set, sets the <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status301MovedPermanently"/> status code.</item>
            <item>Otherwise, configures <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status302Found"/>.</item>
            </list>
            </summary>
            <param name="url">The URL to redirect to.</param>
            <param name="permanent">Specifies whether the redirect should be permanent (301) or temporary (302).</param>
            <param name="preserveMethod">If set to true, make the temporary redirect (307) or permanent redirect (308) preserve the initial request method.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Http.IResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Results.LocalRedirect(System.String,System.Boolean,System.Boolean)">
            <summary>
            Redirects to the specified <paramref name="localUrl"/>.
            <list type="bullet">
            <item>When <paramref name="permanent"/> and <paramref name="preserveMethod"/> are set, sets the <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status308PermanentRedirect"/> status code.</item>
            <item>When <paramref name="preserveMethod"/> is set, sets the <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status307TemporaryRedirect"/> status code.</item>
            <item>When <paramref name="permanent"/> is set, sets the <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status301MovedPermanently"/> status code.</item>
            <item>Otherwise, configures <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status302Found"/>.</item>
            </list>
            </summary>
            <param name="localUrl">The local URL to redirect to.</param>
            <param name="permanent">Specifies whether the redirect should be permanent (301) or temporary (302).</param>
            <param name="preserveMethod">If set to true, make the temporary redirect (307) or permanent redirect (308) preserve the initial request method.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Http.IResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Results.RedirectToRoute(System.String,System.Object,System.Boolean,System.Boolean,System.String)">
            <summary>
            Redirects to the specified route.
            <list type="bullet">
            <item>When <paramref name="permanent"/> and <paramref name="preserveMethod"/> are set, sets the <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status308PermanentRedirect"/> status code.</item>
            <item>When <paramref name="preserveMethod"/> is set, sets the <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status307TemporaryRedirect"/> status code.</item>
            <item>When <paramref name="permanent"/> is set, sets the <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status301MovedPermanently"/> status code.</item>
            <item>Otherwise, configures <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status302Found"/>.</item>
            </list>
            </summary>
            <param name="routeName">The name of the route.</param>
            <param name="routeValues">The parameters for a route.</param>
            <param name="permanent">Specifies whether the redirect should be permanent (301) or temporary (302).</param>
            <param name="preserveMethod">If set to true, make the temporary redirect (307) or permanent redirect (308) preserve the initial request method.</param>
            <param name="fragment">The fragment to add to the URL.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Http.IResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Results.StatusCode(System.Int32)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Http.Result.StatusCodeResult"/> object by specifying a <paramref name="statusCode"/>.
            </summary>
            <param name="statusCode">The status code to set on the response.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Http.Result.StatusCodeResult"/> object for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Results.NotFound(System.Object)">
            <summary>
            Produces a <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status404NotFound"/> response.
            </summary>
            <param name="value">The value to be included in the HTTP response body.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Http.IResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Results.Unauthorized">
            <summary>
            Produces a <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status401Unauthorized"/> response.
            </summary>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Http.IResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Results.BadRequest(System.Object)">
            <summary>
            Produces a <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status400BadRequest"/> response.
            </summary>
            <param name="error">An error object to be included in the HTTP response body.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Http.IResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Results.Conflict(System.Object)">
            <summary>
            Produces a <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status409Conflict"/> response.
            </summary>
            <param name="error">An error object to be included in the HTTP response body.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Http.IResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Results.NoContent">
            <summary>
            Produces a <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status204NoContent"/> response.
            </summary>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Http.IResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Results.Ok(System.Object)">
            <summary>
            Produces a <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status200OK"/> response.
            </summary>
            <param name="value">The value to be included in the HTTP response body.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Http.IResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Results.UnprocessableEntity(System.Object)">
            <summary>
            Produces a <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status422UnprocessableEntity"/> response.
            </summary>
            <param name="error">An error object to be included in the HTTP response body.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Http.IResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Results.Problem(System.String,System.String,System.Nullable{System.Int32},System.String,System.String,System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            Produces a <see cref="T:Microsoft.AspNetCore.Mvc.ProblemDetails"/> response.
            </summary>
            <param name="statusCode">The value for <see cref="P:Microsoft.AspNetCore.Mvc.ProblemDetails.Status" />.</param>
            <param name="detail">The value for <see cref="P:Microsoft.AspNetCore.Mvc.ProblemDetails.Detail" />.</param>
            <param name="instance">The value for <see cref="P:Microsoft.AspNetCore.Mvc.ProblemDetails.Instance" />.</param>
            <param name="title">The value for <see cref="P:Microsoft.AspNetCore.Mvc.ProblemDetails.Title" />.</param>
            <param name="type">The value for <see cref="P:Microsoft.AspNetCore.Mvc.ProblemDetails.Type" />.</param>
            <param name="extensions">The value for <see cref="P:Microsoft.AspNetCore.Mvc.ProblemDetails.Extensions" />.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Http.IResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Results.Problem(Microsoft.AspNetCore.Mvc.ProblemDetails)">
            <summary>
            Produces a <see cref="T:Microsoft.AspNetCore.Mvc.ProblemDetails"/> response.
            </summary>
            <param name="problemDetails">The <see cref="T:Microsoft.AspNetCore.Mvc.ProblemDetails"/>  object to produce a response from.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Http.IResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Results.ValidationProblem(System.Collections.Generic.IDictionary{System.String,System.String[]},System.String,System.String,System.Nullable{System.Int32},System.String,System.String,System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            Produces a <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status400BadRequest"/> response
            with a <see cref="T:Microsoft.AspNetCore.Http.HttpValidationProblemDetails"/> value.
            </summary>
            <param name="errors">One or more validation errors.</param>
            <param name="detail">The value for <see cref="P:Microsoft.AspNetCore.Mvc.ProblemDetails.Detail" />.</param>
            <param name="instance">The value for <see cref="P:Microsoft.AspNetCore.Mvc.ProblemDetails.Instance" />.</param>
            <param name="statusCode">The status code.</param>
            <param name="title">The value for <see cref="P:Microsoft.AspNetCore.Mvc.ProblemDetails.Title" />. Defaults to "One or more validation errors occurred."</param>
            <param name="type">The value for <see cref="P:Microsoft.AspNetCore.Mvc.ProblemDetails.Type" />.</param>
            <param name="extensions">The value for <see cref="P:Microsoft.AspNetCore.Mvc.ProblemDetails.Extensions" />.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Http.IResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Results.Created(System.String,System.Object)">
            <summary>
            Produces a <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status201Created"/> response.
            </summary>
            <param name="uri">The URI at which the content has been created.</param>
            <param name="value">The value to be included in the HTTP response body.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Http.IResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Results.Created(System.Uri,System.Object)">
            <summary>
            Produces a <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status201Created"/> response.
            </summary>
            <param name="uri">The URI at which the content has been created.</param>
            <param name="value">The value to be included in the HTTP response body.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Http.IResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Results.CreatedAtRoute(System.String,System.Object,System.Object)">
            <summary>
            Produces a <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status201Created"/> response.
            </summary>
            <param name="routeName">The name of the route to use for generating the URL.</param>
            <param name="routeValues">The route data to use for generating the URL.</param>
            <param name="value">The value to be included in the HTTP response body.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Http.IResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Results.Accepted(System.String,System.Object)">
            <summary>
            Produces a <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status202Accepted"/> response.
            </summary>
            <param name="uri">The URI with the location at which the status of requested content can be monitored.</param>
            <param name="value">The optional content value to format in the response body.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Http.IResult"/> for the response.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Results.AcceptedAtRoute(System.String,System.Object,System.Object)">
            <summary>
            Produces a <see cref="F:Microsoft.AspNetCore.Http.StatusCodes.Status202Accepted"/> response.
            </summary>
            <param name="routeName">The name of the route to use for generating the URL.</param>
            <param name="routeValues">The route data to use for generating the URL.</param>
            <param name="value">The optional content value to format in the response body.</param>
            <returns>The created <see cref="T:Microsoft.AspNetCore.Http.IResult"/> for the response.</returns>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Results.Extensions">
            <summary>
            Provides a place for external libraries to extend the default <see cref="T:Microsoft.AspNetCore.Http.Results"/> set
            via extension methods returning custom <see cref="T:Microsoft.AspNetCore.Http.IResult" /> implementations.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.StreamCopyOperationInternal.CopyToAsync(System.IO.Stream,System.IO.Stream,System.Nullable{System.Int64},System.Threading.CancellationToken)">
            <summary>Asynchronously reads the given number of bytes from the source stream and writes them to another stream.</summary>
            <returns>A task that represents the asynchronous copy operation.</returns>
            <param name="source">The stream from which the contents will be copied.</param>
            <param name="destination">The stream to which the contents of the current stream will be copied.</param>
            <param name="count">The count of bytes to be copied.</param>
            <param name="cancel">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.StreamCopyOperationInternal.CopyToAsync(System.IO.Stream,System.IO.Stream,System.Nullable{System.Int64},System.Int32,System.Threading.CancellationToken)">
            <summary>Asynchronously reads the given number of bytes from the source stream and writes them to another stream, using a specified buffer size.</summary>
            <returns>A task that represents the asynchronous copy operation.</returns>
            <param name="source">The stream from which the contents will be copied.</param>
            <param name="destination">The stream to which the contents of the current stream will be copied.</param>
            <param name="count">The count of bytes to be copied.</param>
            <param name="bufferSize">The size, in bytes, of the buffer. This value must be greater than zero. The default size is 4096.</param>
            <param name="cancel">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Internal.ResponseContentTypeHelper.ResolveContentTypeAndEncoding(System.String,System.String,System.ValueTuple{System.String,System.Text.Encoding},System.Func{System.String,System.Text.Encoding},System.String@,System.Text.Encoding@)">
            <summary>
            Gets the content type and encoding that need to be used for the response.
            The priority for selecting the content type is:
            1. ContentType property set on the action result
            2. <see cref="P:Microsoft.AspNetCore.Http.HttpResponse.ContentType"/> property set on <see cref="T:Microsoft.AspNetCore.Http.HttpResponse"/>
            3. Default content type set on the action result
            </summary>
            <remarks>
            The user supplied content type is not modified and is used as is. For example, if user
            sets the content type to be "text/plain" without any encoding, then the default content type's
            encoding is used to write the response and the ContentType header is set to be "text/plain" without any
            "charset" information.
            </remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Internal.RangeHelper">
            <summary>
            Provides a parser for the Range Header in an <see cref="P:Microsoft.AspNetCore.Http.HttpContext.Request"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Internal.RangeHelper.ParseRange(Microsoft.AspNetCore.Http.HttpContext,Microsoft.AspNetCore.Http.Headers.RequestHeaders,System.Int64,Microsoft.Extensions.Logging.ILogger)">
            <summary>
            Returns the normalized form of the requested range if the Range Header in the <see cref="P:Microsoft.AspNetCore.Http.HttpContext.Request"/> is valid.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Http.HttpContext"/> associated with the request.</param>
            <param name="requestHeaders">The <see cref="T:Microsoft.AspNetCore.Http.Headers.RequestHeaders"/> associated with the given <paramref name="context"/>.</param>
            <param name="length">The total length of the file representation requested.</param>
            <param name="logger">The <see cref="T:Microsoft.Extensions.Logging.ILogger"/>.</param>
            <returns>A boolean value which represents if the <paramref name="requestHeaders"/> contain a single valid
            range request. A <see cref="T:Microsoft.Net.Http.Headers.RangeItemHeaderValue"/> which represents the normalized form of the
            range parsed from the <paramref name="requestHeaders"/> or <c>null</c> if it cannot be normalized.</returns>
            <remark>If the Range header exists but cannot be parsed correctly, or if the provided length is 0, then the range request cannot be satisfied (status 416).
            This results in (<c>true</c>,<c>null</c>) return values.</remark>
        </member>
    </members>
</doc>
