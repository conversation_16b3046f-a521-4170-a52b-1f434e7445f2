//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Reflection;

[assembly: System.Reflection.AssemblyCompanyAttribute("C-True")]
[assembly: System.Reflection.AssemblyConfigurationAttribute("Debug")]
[assembly: System.Reflection.AssemblyDescriptionAttribute(@"
      Managers that supports common operations working with Microsoft Flightsimulator 2020, through the use of FsConnect.
      Supports:
        - Aircraft information
        - Retrieving sim objects
        - Setting world time
        - Controlling COM and NAV frequencies
        - Controlling transponder code
        - Initial Autopilot manager
    ")]
[assembly: System.Reflection.AssemblyFileVersionAttribute("*******")]
[assembly: System.Reflection.AssemblyInformationalVersionAttribute("*******+.348053d46ede6b6e3e9ef2c97c85f29ebaaaadf9")]
[assembly: System.Reflection.AssemblyProductAttribute("Flight Simulator Connect Managers")]
[assembly: System.Reflection.AssemblyTitleAttribute("CTrue.FsConnect.Managers")]
[assembly: System.Reflection.AssemblyVersionAttribute("*******")]
[assembly: System.Reflection.AssemblyMetadataAttribute("RepositoryUrl", "https://github.com/c-true/FsConnect")]

// Generated by the MSBuild WriteCodeFragment class.

