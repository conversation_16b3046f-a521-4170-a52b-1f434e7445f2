<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Extensions.Features</name>
    </assembly>
    <members>
        <member name="T:Microsoft.AspNetCore.Http.Features.FeatureCollection">
            <summary>
            Default implementation for <see cref="T:Microsoft.AspNetCore.Http.Features.IFeatureCollection"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Features.FeatureCollection.#ctor">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Http.Features.FeatureCollection"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Features.FeatureCollection.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Http.Features.FeatureCollection"/> with the specified initial capacity.
            </summary>
            <param name="initialCapacity">The initial number of elements that the collection can contain.</param>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="initialCapacity"/> is less than 0</exception>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Features.FeatureCollection.#ctor(Microsoft.AspNetCore.Http.Features.IFeatureCollection)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Http.Features.FeatureCollection"/> with the specified defaults.
            </summary>
            <param name="defaults">The feature defaults.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Features.FeatureCollection.Revision">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Features.FeatureCollection.IsReadOnly">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Features.FeatureCollection.Item(System.Type)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Features.FeatureCollection.GetEnumerator">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Features.FeatureCollection.Get``1">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Features.FeatureCollection.Set``1(``0)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Http.Features.FeatureReference`1">
            <summary>
            A cached reference to a feature.
            </summary>
            <typeparam name="T">The feature type.</typeparam>
        </member>
        <member name="F:Microsoft.AspNetCore.Http.Features.FeatureReference`1.Default">
            <summary>
            Gets the default <see cref="T:Microsoft.AspNetCore.Http.Features.FeatureReference`1"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Features.FeatureReference`1.Fetch(Microsoft.AspNetCore.Http.Features.IFeatureCollection)">
            <summary>
            Gets the feature of type <typeparamref name="T"/> from <paramref name="features"/>.
            </summary>
            <param name="features">The <see cref="T:Microsoft.AspNetCore.Http.Features.IFeatureCollection"/>.</param>
            <returns>The feature.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Features.FeatureReference`1.Update(Microsoft.AspNetCore.Http.Features.IFeatureCollection,`0)">
            <summary>
            Updates the reference to the feature.
            </summary>
            <param name="features">The <see cref="T:Microsoft.AspNetCore.Http.Features.IFeatureCollection"/> to update.</param>
            <param name="feature">The instance of the feature.</param>
            <returns>A reference to <paramref name="feature"/> after the operation has completed.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Http.Features.FeatureReferences`1">
            <summary>
            A reference to a collection of features.
            </summary>
            <typeparam name="TCache">The type of the feature.</typeparam>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Features.FeatureReferences`1.#ctor(Microsoft.AspNetCore.Http.Features.IFeatureCollection)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Http.Features.FeatureReferences`1"/>.
            </summary>
            <param name="collection">The <see cref="T:Microsoft.AspNetCore.Http.Features.IFeatureCollection"/>.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Features.FeatureReferences`1.Initalize(Microsoft.AspNetCore.Http.Features.IFeatureCollection)">
            <summary>
            Initializes the <see cref="T:Microsoft.AspNetCore.Http.Features.FeatureReferences`1"/>.
            </summary>
            <param name="collection">The <see cref="T:Microsoft.AspNetCore.Http.Features.IFeatureCollection"/> to initialize with.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Features.FeatureReferences`1.Initalize(Microsoft.AspNetCore.Http.Features.IFeatureCollection,System.Int32)">
            <summary>
            Initializes the <see cref="T:Microsoft.AspNetCore.Http.Features.FeatureReferences`1"/>.
            </summary>
            <param name="collection">The <see cref="T:Microsoft.AspNetCore.Http.Features.IFeatureCollection"/> to initialize with.</param>
            <param name="revision">The version of the <see cref="T:Microsoft.AspNetCore.Http.Features.IFeatureCollection"/>.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Features.FeatureReferences`1.Collection">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Http.Features.IFeatureCollection"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Features.FeatureReferences`1.Revision">
            <summary>
            Gets the revision number.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Http.Features.FeatureReferences`1.Cache">
            <summary>
            This API is part of ASP.NET Core's infrastructure and should not be referenced by application code.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Features.FeatureReferences`1.Fetch``2(``0@,``1,System.Func{``1,``0})">
            <summary>
            This API is part of ASP.NET Core's infrastructure and should not be referenced by application code.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Features.FeatureReferences`1.Fetch``1(``0@,System.Func{Microsoft.AspNetCore.Http.Features.IFeatureCollection,``0})">
            <summary>
            This API is part of ASP.NET Core's infrastructure and should not be referenced by application code.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Http.Features.IFeatureCollection">
            <summary>
            Represents a collection of HTTP features.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Features.IFeatureCollection.IsReadOnly">
            <summary>
            Indicates if the collection can be modified.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Features.IFeatureCollection.Revision">
            <summary>
            Incremented for each modification and can be used to verify cached results.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Features.IFeatureCollection.Item(System.Type)">
            <summary>
            Gets or sets a given feature. Setting a null value removes the feature.
            </summary>
            <param name="key"></param>
            <returns>The requested feature, or null if it is not present.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Features.IFeatureCollection.Get``1">
            <summary>
            Retrieves the requested feature from the collection.
            </summary>
            <typeparam name="TFeature">The feature key.</typeparam>
            <returns>The requested feature, or null if it is not present.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Features.IFeatureCollection.Set``1(``0)">
            <summary>
            Sets the given feature in the collection.
            </summary>
            <typeparam name="TFeature">The feature key.</typeparam>
            <param name="instance">The feature value.</param>
        </member>
    </members>
</doc>
