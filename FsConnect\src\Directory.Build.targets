<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

    <Target Name="EnsureArtifactsFolderExist" BeforeTargets="Build" Condition="!Exists('$(ArtifactsFolder)')">
        <Message Text="Creating artifacts folder: $(ArtifactsFolder)." Importance="high" />
        <MakeDir Directories="$(ArtifactsFolder)" />
    </Target>

    <Target Name="ZipToArtifactsFolder" AfterTargets="Build" Condition="$(Zip)==true and $(Configuration)=='Release'" >
        <WriteLinesToFile
            File="$(OutputPath)\version.txt"
            Lines="$(MajorVersion).$(MinorVersion).$(BuildCounter)$(Tag)"
            Overwrite="true"
        />
        <ZipDirectory
            SourceDirectory="$(OutputPath)"
            DestinationFile="$(ArtifactsFolder)\$(PackageId)-bin_$(MajorVersion).$(MinorVersion).$(BuildCounter).zip" 
			Overwrite="true"
			/>
    </Target>
	
    <Target Name="CopyPackages" AfterTargets="Pack">
        <Message Text="Copying $(PackageId).$(MajorVersion).$(MinorVersion).$(PatchLevel)$(Tag).nupkg to artifacts folder." Importance="high"/>
        <Copy
            SourceFiles="$(BaseOutputPath)$(Configuration)\$(PackageId).$(MajorVersion).$(MinorVersion).$(PatchLevel)$(Tag).nupkg"
            DestinationFolder="$(ArtifactsFolder)\Packages"
            />
    </Target>
</Project>