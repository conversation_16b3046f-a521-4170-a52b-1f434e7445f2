﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>CTrue.FsConnect</id>
    <version>1.4.0</version>
    <authors>C-True</authors>
    <requireLicenseAcceptance>true</requireLicenseAcceptance>
    <license type="file">LICENSE.txt</license>
    <licenseUrl>https://aka.ms/deprecateLicenseUrl</licenseUrl>
    <readme>README.md</readme>
    <description>An easy to use wrapper for SimConnect, for connection to Flight Simulator 2020.
			Contains SimConnect binaries, as distributed by the Flight Simulator 20202 SDK 0.10.0 release.</description>
    <releaseNotes>Support for registering input events. * Updated SDK version to ******** * Upgraded to .NET 6.0 with C# 8.0 support</releaseNotes>
    <tags>msfs flight-simulator simconnect</tags>
    <repository type="git" url="https://github.com/c-true/FsConnect" commit="348053d46ede6b6e3e9ef2c97c85f29ebaaaadf9" />
    <dependencies>
      <group targetFramework="net8.0">
        <dependency id="Serilog" version="2.10.0" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
  <files>
    <file src="C:\dev\Personal\FlightPig\FsConnect\src\CTrue.FsConnect\bin\Debug\net8.0\CTrue.FsConnect.dll" target="lib\net8.0\CTrue.FsConnect.dll" />
    <file src="C:\dev\Personal\FlightPig\FsConnect\src\CTrue.FsConnect\bin\Debug\net8.0\CTrue.FsConnect.xml" target="lib\net8.0\CTrue.FsConnect.xml" />
    <file src="C:\dev\Personal\FlightPig\FsConnect\src\Dependencies\SimConnect\build\simconnect.dll" target="build\simconnect.dll" />
    <file src="C:\dev\Personal\FlightPig\FsConnect\src\Dependencies\SimConnect\build\CTrue.FsConnect.targets" target="build\CTrue.FsConnect.targets" />
    <file src="C:\dev\Personal\FlightPig\FsConnect\src\Dependencies\SimConnect\lib\net461\Microsoft.FlightSimulator.SimConnect.dll" target="lib\net8.0\Microsoft.FlightSimulator.SimConnect.dll" />
    <file src="C:\dev\Personal\FlightPig\FsConnect\README.md" target="README.md" />
    <file src="C:\dev\Personal\FlightPig\FsConnect\src\CTrue.FsConnect\licenses\LICENSE.txt" target="LICENSE.txt" />
  </files>
</package>