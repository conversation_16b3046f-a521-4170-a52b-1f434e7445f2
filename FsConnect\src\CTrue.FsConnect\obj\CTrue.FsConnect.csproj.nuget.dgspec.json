{"format": 1, "restore": {"C:\\dev\\Personal\\FlightPig\\FsConnect\\src\\CTrue.FsConnect\\CTrue.FsConnect.csproj": {}}, "projects": {"C:\\dev\\Personal\\FlightPig\\FsConnect\\src\\CTrue.FsConnect\\CTrue.FsConnect.csproj": {"version": "1.4.0", "restore": {"projectUniqueName": "C:\\dev\\Personal\\FlightPig\\FsConnect\\src\\CTrue.FsConnect\\CTrue.FsConnect.csproj", "projectName": "CTrue.FsConnect", "projectPath": "C:\\dev\\Personal\\FlightPig\\FsConnect\\src\\CTrue.FsConnect\\CTrue.FsConnect.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\dev\\Personal\\FlightPig\\FsConnect\\src\\CTrue.FsConnect\\obj\\", "projectStyle": "PackageReference", "centralPackageVersionsManagementEnabled": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Serilog": {"target": "Package", "version": "[2.10.0, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"CommandLineParser": "[2.8.0, )", "Microsoft.NET.Test.Sdk": "[16.9.1, )", "NUnit": "[3.13.1, )", "NUnit3TestAdapter": "[3.17.0, )", "Serilog": "[2.10.0, )", "Serilog.Sinks.Console": "[3.1.1, )"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.408/PortableRuntimeIdentifierGraph.json"}}}}}