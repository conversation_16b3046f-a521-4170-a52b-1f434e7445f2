﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
		<LangVersion>8.0</LangVersion>
		<Product>Flight Simulator Connect</Product>
		<Copyright />
		<Description>
			An easy to use wrapper for SimConnect, for connection to Flight Simulator 2020.
			Contains SimConnect binaries, as distributed by the Flight Simulator 20202 SDK 0.10.0 release.
		</Description>
		<GeneratePackageOnBuild>true</GeneratePackageOnBuild>
		<PackageReadmeFile>README.md</PackageReadmeFile>
		<PackageLicenseFile>LICENSE.txt</PackageLicenseFile>
		<PackageRequireLicenseAcceptance>true</PackageRequireLicenseAcceptance>
		<PackageTags>msfs flight-simulator simconnect</PackageTags>
		<GenerateDocumentationFile>true</GenerateDocumentationFile>
		<PackageReleaseNotes>Support for registering input events. * Updated SDK version to ******** * Upgraded to .NET 6.0 with C# 8.0 support</PackageReleaseNotes>
	</PropertyGroup>

	<PropertyGroup>
		<PlatformTarget>x64</PlatformTarget>
	</PropertyGroup>

	<ItemGroup>
		<Reference Include="Microsoft.FlightSimulator.SimConnect">
			<HintPath>..\Dependencies\SimConnect\lib\net461\Microsoft.FlightSimulator.SimConnect.dll</HintPath>
		</Reference>
	</ItemGroup>

	<ItemGroup>
		<None Include="..\Dependencies\SimConnect\build\simconnect.dll" Visible="false" Pack="true" PackagePath="build" />
		<None Include="..\Dependencies\SimConnect\build\CTrue.FsConnect.targets" Visible="false" Pack="true" PackagePath="build" />
		<None Include="..\Dependencies\SimConnect\lib\net461\Microsoft.FlightSimulator.SimConnect.dll" Visible="false" Pack="true" PackagePath="lib\net6.0" />
		<None Include="..\..\README.md" Pack="true" PackagePath="$(PackageReadmeFile)" />
		<None Include="licenses\LICENSE.txt" Pack="true" PackagePath="$(PackageLicenseFile)" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Serilog" />
	</ItemGroup>

</Project>
