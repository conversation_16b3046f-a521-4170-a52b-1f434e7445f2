<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.AspNetCore.Hosting.Server.Abstractions</name>
    </assembly>
    <members>
        <member name="T:Microsoft.AspNetCore.Hosting.Server.Features.IServerAddressesFeature">
            <summary>
            Specifies the address used by the server.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Hosting.Server.Features.IServerAddressesFeature.Addresses">
            <summary>
            An <see cref="T:System.Collections.Generic.ICollection`1" /> of addresses used by the server.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Hosting.Server.Features.IServerAddressesFeature.PreferHostingUrls">
            <summary>
            <see langword="true" /> to prefer URLs configured by the host rather than the server.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Hosting.Server.Abstractions.IHostContextContainer`1">
            <summary>
            When implemented by a Server allows an <see cref="T:Microsoft.AspNetCore.Hosting.Server.IHttpApplication`1"/> to pool and reuse
            its <typeparamref name="TContext"/> between requests.
            </summary>
            <typeparam name="TContext">The <see cref="T:Microsoft.AspNetCore.Hosting.Server.IHttpApplication`1"/> Host context</typeparam>
        </member>
        <member name="P:Microsoft.AspNetCore.Hosting.Server.Abstractions.IHostContextContainer`1.HostContext">
            <summary>
            Represents the <typeparamref name="TContext"/>  of the host.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Hosting.Server.IHttpApplication`1">
            <summary>
            Represents an application.
            </summary>
            <typeparam name="TContext">The context associated with the application.</typeparam>
        </member>
        <member name="M:Microsoft.AspNetCore.Hosting.Server.IHttpApplication`1.CreateContext(Microsoft.AspNetCore.Http.Features.IFeatureCollection)">
            <summary>
            Create a TContext given a collection of HTTP features.
            </summary>
            <param name="contextFeatures">A collection of HTTP features to be used for creating the TContext.</param>
            <returns>The created TContext.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Hosting.Server.IHttpApplication`1.ProcessRequestAsync(`0)">
            <summary>
            Asynchronously processes an TContext.
            </summary>
            <param name="context">The TContext that the operation will process.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Hosting.Server.IHttpApplication`1.DisposeContext(`0,System.Exception)">
            <summary>
            Dispose a given TContext.
            </summary>
            <param name="context">The TContext to be disposed.</param>
            <param name="exception">The Exception thrown when processing did not complete successfully, otherwise null.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Hosting.Server.IServer">
            <summary>
            Represents a server.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Hosting.Server.IServer.Features">
            <summary>
            A collection of HTTP features of the server.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Hosting.Server.IServer.StartAsync``1(Microsoft.AspNetCore.Hosting.Server.IHttpApplication{``0},System.Threading.CancellationToken)">
            <summary>
            Start the server with an application.
            </summary>
            <param name="application">An instance of <see cref="T:Microsoft.AspNetCore.Hosting.Server.IHttpApplication`1"/>.</param>
            <typeparam name="TContext">The context associated with the application.</typeparam>
            <param name="cancellationToken">Indicates if the server startup should be aborted.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Hosting.Server.IServer.StopAsync(System.Threading.CancellationToken)">
            <summary>
            Stop processing requests and shut down the server, gracefully if possible.
            </summary>
            <param name="cancellationToken">Indicates if the graceful shutdown should be aborted.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Hosting.Server.IServerIntegratedAuth">
            <summary>
            Used by servers to advertise if they support integrated Windows authentication, if it's enabled, and it's scheme.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Hosting.Server.IServerIntegratedAuth.IsEnabled">
            <summary>
            Indicates if integrated Windows authentication is enabled for the current application instance.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Hosting.Server.IServerIntegratedAuth.AuthenticationScheme">
            <summary>
            The name of the authentication scheme for the server authentication handler.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Hosting.Server.ServerIntegratedAuth">
            <summary>
            Used by servers to advertise if they support integrated Windows authentication, if it's enabled, and it's scheme.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Hosting.Server.ServerIntegratedAuth.IsEnabled">
            <summary>
            Indicates if integrated Windows authentication is enabled for the current application instance.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Hosting.Server.ServerIntegratedAuth.AuthenticationScheme">
            <summary>
            The name of the authentication scheme for the server authentication handler.
            </summary>
        </member>
    </members>
</doc>
