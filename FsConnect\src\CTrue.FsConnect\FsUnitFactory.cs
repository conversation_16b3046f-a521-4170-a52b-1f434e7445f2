﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace CTrue.FsConnect
{
	internal static class FsUnitFactory
	{
		#region Names

		static private readonly string[] _names = new string[]
		{
			"",
			"amp",
			"ampere",
			"amperes",
			"amps",
			"angl16",
			"angl32",
			"atm",
			"atmosphere",
			"atmospheres",
			"bar",
			"bars",
			"Bco16",
			"bel",
			"bels",
			"Bool",
			"Boolean",
			"boost cmHg",
			"boost inHg",
			"boost psi",
			"celsius",
			"celsius fs7 egt",
			"celsius fs7 oil temp",
			"celsius scaler 1/256",
			"celsius scaler 16k",
			"celsius scaler 256",
			"centimeter",
			"centimeter of mercury",
			"centimeters",
			"centimeters of mercury",
			"cm",
			"cm2",
			"cm3",
			"cmHg",
			"cu cm",
			"cu ft",
			"cu in",
			"cu km",
			"cu m",
			"cu mm",
			"cu yd",
			"cubic centimeter",
			"cubic centimeters",
			"cubic feet",
			"cubic foot",
			"cubic inch",
			"cubic inches",
			"cubic kilometer",
			"cubic kilometers",
			"cubic meter",
			"cubic meters",
			"cubic mile",
			"cubic miles",
			"cubic millimeter",
			"cubic millimeters",
			"cubic yard",
			"cubic yards",
			"day",
			"days",
			"decibel",
			"decibels",
			"decimile",
			"decimiles",
			"decinmile",
			"decinmiles",
			"degree",
			"degree angl16",
			"degree angl32",
			"degree latitude",
			"degree longitude",
			"degree per second",
			"degree per second ang16",
			"degrees",
			"degrees angl16",
			"degrees angl32",
			"degrees latitude",
			"degrees longitude",
			"degrees per second",
			"degrees per second ang16",
			"Enum",
			"fahrenheit",
			"farenheit",
			"feet",
			"feet/minute",
			"feet per minute",
			"feet/second",
			"feet per second",
			"feet per second squared",
			"flags",
			"foot",
			"foot per second squared",
			"foot pound",
			"foot-pound",
			"foot-pounds",
			"foot pounds",
			"Frequency ADF BCD32",
			"Frequency BCD16",
			"Frequency BCD32",
			"fs7 charging amps",
			"fs7 oil quantity",
			"ft",
			"ft lb per second",
			"ft-lbs",
			"ft/min",
			"ft2",
			"ft3",
			"G Force",
			"G Force 624 scaled",
			"gallon",
			"gallon per hour",
			"gallons",
			"gallons per hour",
			"geepound",
			"geepounds",
			"GForce",
			"GLOBALP->delta_heading_rate",
			"GLOBALP->eng1.manifold_pressure",
			"GLOBALP->eng1.oil_prs",
			"GLOBALP->eng1.oil_tmp",
			"GLOBALP->vertical_speed",
			"gph",
			"grad",
			"grads",
			"half",
			"halfs",
			"hectopascal",
			"hectopascals",
			"Hertz",
			"hour",
			"hour over 10",
			"hours",
			"hours over 10",
			"Hz",
			"in",
			"in2",
			"in3",
			"inch",
			"inch of mercury",
			"inches",
			"inches of mercury",
			"inHg",
			"inHg 64 over 64k",
			"kelvin",
			"keyframe",
			"keyframes",
			"kg",
			"kgf meter",
			"kgf meters",
			"KgFSqCm",
			"KHz",
			"kilogram",
			"kilogram force per square centimeter",
			"kilogram meter",
			"kilogram meter squared",
			"kilogram meters",
			"kilogram per cubic meter",
			"kilogram per second",
			"kilograms",
			"kilograms meter squared",
			"kilograms per cubic meter",
			"kilograms per second",
			"Kilohertz",
			"kilometer",
			"kilometer/hour",
			"kilometer per hour",
			"kilometers",
			"kilometers/hour",
			"kilometers per hour",
			"kilopascal",
			"km",
			"km2",
			"km3",
			"knot",
			"knot scaler 128",
			"knots",
			"knots scaler 128",
			"kPa",
			"kph",
			"lbf-feet",
			"lbs",
			"liter",
			"liter per hour",
			"liters",
			"liters per hour",
			"m",
			"m/s",
			"m2",
			"m3",
			"mach",
			"mach 3d2 over 64k",
			"machs",
			"mask",
			"mbar",
			"mbars",
			"Megahertz",
			"meter",
			"meter cubed",
			"meter cubed per second",
			"meter latitude",
			"meter per minute",
			"meter/second",
			"meter per second",
			"meter per second scaler 256",
			"meter per second squared",
			"meter scaler 256",
			"meters",
			"meters cubed",
			"meters cubed per second",
			"meters latitude",
			"meters per minute",
			"meters per second",
			"meters/second",
			"meters per second scaler 256",
			"meters per second squared",
			"meters scaler 256",
			"MHz",
			"mile",
			"mile per hour",
			"miles",
			"miles per hour",
			"millibar",
			"millibar scaler 16",
			"millibars",
			"millibars scaler 16",
			"millimeter",
			"millimeter of mercury",
			"millimeter of water",
			"millimeters",
			"millimeters of mercury",
			"millimeters of water",
			"minute",
			"minute per round",
			"minutes",
			"minutes per round",
			"mm2",
			"mm3",
			"mmHg",
			"more_than_a_half",
			"mph",
			"nautical mile",
			"nautical miles",
			"newton meter",
			"newton meters",
			"newton per square meter",
			"newtons per square meter",
			"nice minute per round",
			"nice minutes per round",
			"Nm",
			"nmile",
			"nmiles",
			"number",
			"numbers",
			"Pa",
			"part",
			"pascal",
			"pascals",
			"per degree",
			"per hour",
			"per minute",
			"per radian",
			"per second",
			"percent",
			"percent over 100",
			"percent scaler 16k",
			"percent scaler 2pow23",
			"percent scaler 32k",
			"percentage",
			"position",
			"position 128",
			"position 16k",
			"position 32k",
			"pound",
			"pound-force per square foot",
			"pound-force per square inch",
			"pound per hour",
			"pound scaler 256",
			"poundal feet",
			"pounds",
			"pounds per hour",
			"pounds scaler 256",
			"pph",
			"psf",
			"psf scaler 16k",
			"psi",
			"psi 4 over 16k",
			"psi fs7 oil pressure",
			"psi scaler 16k",
			"quart",
			"quarts",
			"radian",
			"radian per second",
			"radians",
			"radians per second",
			"rankine",
			"ratio",
			"revolution per minute",
			"revolutions per minute",
			"round",
			"rounds",
			"rpm",
			"rpm 1 over 16k",
			"rpms",
			"scaler",
			"second",
			"seconds",
			"slug",
			"slug feet squared",
			"Slug per cubic feet",
			"Slug per cubic foot",
			"Slug/ft3",
			"slugs",
			"slugs feet squared",
			"Slugs per cubic feet",
			"Slugs per cubic foot",
			"sq cm",
			"sq ft",
			"sq in",
			"sq km",
			"sq m",
			"sq mm",
			"sq yd",
			"square centimeter",
			"square centimeters",
			"square feet",
			"square foot",
			"square inch",
			"square inches",
			"square kilometer",
			"square kilometers",
			"square meter",
			"square meters",
			"square mile",
			"square miles",
			"square millimeter",
			"square millimeters",
			"square yard",
			"square yards",
			"third",
			"thirds",
			"times",
			"volt",
			"volts",
			"Watt",
			"Watts",
			"yard",
			"yards",
			"yd2",
			"yd3",
			"year",
			"years",
		};

		#endregion

		/// <summary>
		/// Gets the unit name used by MSFS.
		/// </summary>
		/// <param name="unit"></param>
		/// <returns></returns>
		public static string GetUnitName(FsUnit unit)
		{
			return _names[(int)unit];
		}
	}
}
