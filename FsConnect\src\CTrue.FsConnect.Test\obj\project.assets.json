{"version": 3, "targets": {"net6.0": {}}, "libraries": {}, "projectFileDependencyGroups": {"net6.0": ["Microsoft.NET.Test.Sdk >= 16.9.1", "NUnit >= 3.13.1", "NUnit3TestAdapter >= 3.17.0"]}, "packageFolders": {"C:\\dev\\Personal\\FlightPig\\FsConnect\\src\\packages": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.4.0", "restore": {"projectUniqueName": "C:\\dev\\Personal\\FlightPig\\FsConnect\\src\\CTrue.FsConnect.Test\\CTrue.FsConnect.Test.csproj", "projectName": "CTrue.FsConnect.Test", "projectPath": "C:\\dev\\Personal\\FlightPig\\FsConnect\\src\\CTrue.FsConnect.Test\\CTrue.FsConnect.Test.csproj", "packagesPath": "C:\\dev\\Personal\\FlightPig\\FsConnect\\src\\packages", "outputPath": "C:\\dev\\Personal\\FlightPig\\FsConnect\\src\\CTrue.FsConnect.Test\\obj\\", "projectStyle": "PackageReference", "centralPackageVersionsManagementEnabled": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\dev\\Personal\\FlightPig\\FsConnect\\src\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files\\dotnet\\library-packs": {}, "C:\\dev\\Personal\\FlightPig\\FsConnect\\artifacts\\packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\dev\\Personal\\FlightPig\\FsConnect\\src\\CTrue.FsConnect\\CTrue.FsConnect.csproj": {"projectPath": "C:\\dev\\Personal\\FlightPig\\FsConnect\\src\\CTrue.FsConnect\\CTrue.FsConnect.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[16.9.1, )", "versionCentrallyManaged": true}, "NUnit": {"target": "Package", "version": "[3.13.1, )", "versionCentrallyManaged": true}, "NUnit3TestAdapter": {"target": "Package", "version": "[3.17.0, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"CommandLineParser": "2.8.0", "Microsoft.NET.Test.Sdk": "16.9.1", "NUnit": "3.13.1", "NUnit3TestAdapter": "3.17.0", "Serilog": "2.10.0", "Serilog.Sinks.Console": "3.1.1"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "logs": [{"code": "NU1301", "level": "Error", "message": "The local source 'C:\\dev\\Personal\\FlightPig\\FsConnect\\artifacts\\packages' doesn't exist.", "libraryId": "NUnit"}, {"code": "NU1301", "level": "Error", "message": "The local source 'C:\\dev\\Personal\\FlightPig\\FsConnect\\artifacts\\packages' doesn't exist.", "libraryId": "Microsoft.NET.Test.Sdk"}, {"code": "NU1301", "level": "Error", "message": "The local source 'C:\\dev\\Personal\\FlightPig\\FsConnect\\artifacts\\packages' doesn't exist.", "libraryId": "NUnit3TestAdapter"}]}