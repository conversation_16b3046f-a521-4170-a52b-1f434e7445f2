﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>Microsoft.VisualBasic.Forms</name>
  </assembly>
  <members>
    <member name="T:Microsoft.VisualBasic.ApplicationServices.ApplicationBase">
      <summary>Provides properties, methods, and events related to the current application.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.ApplicationServices.ApplicationBase.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.ApplicationServices.ApplicationBase" /> class.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.ApplicationServices.ApplicationBase.ChangeCulture(System.String)">
      <summary>Changes the culture used by the current thread for string manipulation and for string formatting.</summary>
      <param name="cultureName">
        <see langword="String" />. Name of the culture as a string. For a list of possible names, see <see cref="T:System.Globalization.CultureInfo" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="cultureName" /> is <see langword="Nothing" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="cultureName" /> is not a valid culture name.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.ApplicationServices.ApplicationBase.ChangeUICulture(System.String)">
      <summary>Changes the culture that the current thread uses for retrieving culture-specific resources.</summary>
      <param name="cultureName">
        <see langword="String" />. Name of the culture as a string. For a list of possible names, see <see cref="T:System.Globalization.CultureInfo" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="cultureName" /> is <see langword="Nothing" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="cultureName" /> is not a valid culture name.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.ApplicationServices.ApplicationBase.GetEnvironmentVariable(System.String)">
      <summary>Returns the value of the specified environment variable.</summary>
      <param name="name">A <see langword="String" /> containing the name of the environment variable.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> is <see langword="Nothing" />.</exception>
      <exception cref="T:System.ArgumentException">The environment variable specified by <paramref name="name" /> does not exist.</exception>
      <exception cref="T:System.Security.SecurityException">The calling code does not have <see cref="T:System.Security.Permissions.EnvironmentPermission" /> with <see langword="Read" /> access.</exception>
      <returns>A <see langword="String" /> containing the value of the environment variable with the name <paramref name="name" />.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.ApplicationServices.ApplicationBase.Culture">
      <summary>Gets the culture that the current thread uses for string manipulation and string formatting.</summary>
      <returns>A <see cref="T:System.Globalization.CultureInfo" /> object that represents the culture the current thread uses for string manipulation and string formatting.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.ApplicationServices.ApplicationBase.Info">
      <summary>Gets an object that provides properties for getting information about the application's assembly, such as the version number, description, and so on.</summary>
      <returns>The <see cref="T:Microsoft.VisualBasic.ApplicationServices.AssemblyInfo" /> object for the current application.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.ApplicationServices.ApplicationBase.Log">
      <summary>Gets an object that provides properties and methods for writing event and exception information to the application's log listeners.</summary>
      <returns>The <see cref="T:Microsoft.VisualBasic.Logging.Log" /> object for the current application.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.ApplicationServices.ApplicationBase.UICulture">
      <summary>Gets the culture that the current thread uses for retrieving culture-specific resources.</summary>
      <returns>A <see cref="T:System.Globalization.CultureInfo" /> object that represents the culture that the current thread uses for retrieving culture-specific resources.</returns>
    </member>
    <member name="T:Microsoft.VisualBasic.ApplicationServices.ApplyApplicationDefaultsEventArgs">
      <summary>Provides context for the ApplyApplicationDefaults event.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.ApplicationServices.ApplyApplicationDefaultsEventArgs.Font">
      <summary>Gets or sets the default font for Forms and UserControls application-wide. If this property is not written, or the event is not handled, the default font for Forms and UserControls is set by the system.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.ApplicationServices.ApplyApplicationDefaultsEventArgs.HighDpiMode">
      <summary>Gets or sets the general <see cref="T:System.Windows.Forms.HighDpiMode" /> for the application.</summary>
      <returns>The general <see cref="T:System.Windows.Forms.HighDpiMode" /> for the application. The default value is <see cref="F:System.Windows.Forms.HighDpiMode.SystemAware" />.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.ApplicationServices.ApplyApplicationDefaultsEventArgs.MinimumSplashScreenDisplayTime">
      <summary>Gets or sets the minimum time, in milliseconds, that an application's Splash dialog is displayed.</summary>
    </member>
    <member name="T:Microsoft.VisualBasic.ApplicationServices.ApplyApplicationDefaultsEventHandler">
      <summary>Represents the callback method that will handle the <see cref="E:Microsoft.VisualBasic.ApplicationServices.WindowsFormsApplicationBase.ApplyApplicationDefaults" /> event.</summary>
      <param name="sender" />
      <param name="e" />
    </member>
    <member name="T:Microsoft.VisualBasic.ApplicationServices.AssemblyInfo">
      <summary>Provides properties for getting the information about the application, such as the version number, description, loaded assemblies, and so on.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.ApplicationServices.AssemblyInfo.#ctor(System.Reflection.Assembly)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.ApplicationServices.AssemblyInfo" /> class with the specified assembly information.</summary>
      <param name="currentAssembly">
        <see cref="T:System.Reflection.Assembly" />. The assembly for which to obtain the information.</param>
    </member>
    <member name="P:Microsoft.VisualBasic.ApplicationServices.AssemblyInfo.AssemblyName">
      <summary>Gets the name, without the extension, of the assembly file for the application.</summary>
      <returns>A <see langword="String" /> containing the file name.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.ApplicationServices.AssemblyInfo.CompanyName">
      <summary>Gets the company name associated with the application.</summary>
      <exception cref="T:System.InvalidOperationException">The assembly does not have an <see cref="T:System.Reflection.AssemblyCompanyAttribute" /> attribute.</exception>
      <returns>A <see langword="String" /> that contains the company name associated with the application.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.ApplicationServices.AssemblyInfo.Copyright">
      <summary>Gets the copyright notice associated with the application.</summary>
      <exception cref="T:System.InvalidOperationException">The assembly does not have an <see cref="T:System.Reflection.AssemblyCopyrightAttribute" /> attribute.</exception>
      <returns>A <see langword="String" /> containing the copyright notice associated with the application.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.ApplicationServices.AssemblyInfo.Description">
      <summary>Gets the description associated with the application.</summary>
      <exception cref="T:System.InvalidOperationException">The assembly does not have an <see cref="T:System.Reflection.AssemblyDescriptionAttribute" /> attribute.</exception>
      <returns>A <see langword="String" /> containing the description associated with the application.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.ApplicationServices.AssemblyInfo.DirectoryPath">
      <summary>Gets the directory where the application is stored.</summary>
      <returns>A <see langword="String" /> that contains the directory where the application is stored.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.ApplicationServices.AssemblyInfo.LoadedAssemblies">
      <summary>Gets a collection of all assemblies loaded by the application.</summary>
      <exception cref="T:System.AppDomainUnloadedException">The application domain is not loaded.</exception>
      <returns>A <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" /> of <see cref="T:System.Reflection.Assembly" /> containing all the assemblies loaded by the application.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.ApplicationServices.AssemblyInfo.ProductName">
      <summary>Gets the product name associated with the application.</summary>
      <exception cref="T:System.InvalidOperationException">The assembly does not have an <see cref="T:System.Reflection.AssemblyProductAttribute" /> attribute.</exception>
      <returns>A <see langword="String" /> containing the product name associated with the application.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.ApplicationServices.AssemblyInfo.StackTrace">
      <summary>Gets the current stack-trace information.</summary>
      <exception cref="T:System.ArgumentOutOfRangeException">The requested stack-trace information is out of range.</exception>
      <returns>A <see langword="String" /> containing the current stack-trace information. The return value can be <see cref="F:System.String.Empty" />.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.ApplicationServices.AssemblyInfo.Title">
      <summary>Gets the title associated with the application.</summary>
      <exception cref="T:System.InvalidOperationException">The assembly does not have an <see cref="T:System.Reflection.AssemblyTitleAttribute" /> attribute.</exception>
      <returns>A <see langword="String" /> containing the <see cref="T:System.Reflection.AssemblyTitleAttribute" /> associated with the application.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.ApplicationServices.AssemblyInfo.Trademark">
      <summary>Gets the trademark notice associated with the application.</summary>
      <exception cref="T:System.InvalidOperationException">The assembly does not have an <see cref="T:System.Reflection.AssemblyTrademarkAttribute" /> attribute.</exception>
      <returns>A <see langword="String" /> containing the trademark notice associated with the application.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.ApplicationServices.AssemblyInfo.Version">
      <summary>Gets the version number of the application.</summary>
      <exception cref="T:System.Security.SecurityException">The application does not have sufficient permissions to access the assembly version.</exception>
      <returns>A <see cref="T:System.Version" /> object containing the version number of the application.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.ApplicationServices.AssemblyInfo.WorkingSet">
      <summary>Gets the amount of physical memory mapped to the process context.</summary>
      <exception cref="T:System.Security.SecurityException">A situation in which partial trust exists and the user lacks necessary permissions.</exception>
      <returns>A <see langword="Long" /> containing the number of bytes of physical memory mapped to the process context.</returns>
    </member>
    <member name="T:Microsoft.VisualBasic.ApplicationServices.AuthenticationMode">
      <summary>Indicates how a Visual Basic application authenticates the user for the <see langword="My.User" /> object.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.ApplicationServices.AuthenticationMode.ApplicationDefined">
      <summary>The <see cref="M:Microsoft.VisualBasic.ApplicationServices.WindowsFormsApplicationBase.#ctor(Microsoft.VisualBasic.ApplicationServices.AuthenticationMode)" /> constructor does not initialize the principal for the application's main thread. The application needs to initialize the principal for the application's main thread.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.ApplicationServices.AuthenticationMode.Windows">
      <summary>The <see cref="M:Microsoft.VisualBasic.ApplicationServices.WindowsFormsApplicationBase.#ctor(Microsoft.VisualBasic.ApplicationServices.AuthenticationMode)" /> constructor initializes the principal for the application's main thread with the current user's Windows user information.</summary>
    </member>
    <member name="T:Microsoft.VisualBasic.ApplicationServices.CantStartSingleInstanceException">
      <summary>This exception is thrown when a subsequent instance of a single-instance application is unable to connect to the first application instance.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.ApplicationServices.CantStartSingleInstanceException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.ApplicationServices.CantStartSingleInstanceException" /> class.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.ApplicationServices.CantStartSingleInstanceException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.ApplicationServices.CantStartSingleInstanceException" /> class with serialized data.</summary>
      <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> object that holds the serialized object data about the exception being thrown.</param>
      <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext" /> object that contains contextual information about the source or destination.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.ApplicationServices.CantStartSingleInstanceException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.ApplicationServices.CantStartSingleInstanceException" /> class with a specified error message.</summary>
      <param name="message">A message that describes the error.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.ApplicationServices.CantStartSingleInstanceException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.ApplicationServices.CantStartSingleInstanceException" /> class with a specified error message and a reference to the inner exception that is the cause of this exception.</summary>
      <param name="message">A <see cref="T:System.String" /> object describing the error.</param>
      <param name="inner">The <see cref="T:System.Exception" /> object that is the cause of the current exception. If the <see cref="P:System.Exception.InnerException" /> parameter is not a null reference (<see langword="Nothing" /> in Visual Basic), the current exception is raised in a <see langword="Catch" /> block that handles the inner exception.</param>
    </member>
    <member name="T:Microsoft.VisualBasic.ApplicationServices.ConsoleApplicationBase">
      <summary>Provides properties, methods, and events related to the current application.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.ApplicationServices.ConsoleApplicationBase.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.ApplicationServices.ConsoleApplicationBase" /> class.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.ApplicationServices.ConsoleApplicationBase.CommandLineArgs">
      <summary>Gets a collection containing the command-line arguments as strings for the current application.</summary>
      <returns>A <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" /> of <see langword="String" />, containing the command-line arguments as strings for the current application.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.ApplicationServices.ConsoleApplicationBase.InternalCommandLine">
      <summary>Sets the values to use as the current application's command-line arguments.</summary>
      <returns>A <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" /> of <see langword="String" />, containing the strings to use as the command-line arguments for the current application.</returns>
    </member>
    <member name="T:Microsoft.VisualBasic.ApplicationServices.NoStartupFormException">
      <summary>This exception is thrown by the Visual Basic Application Model when the <see cref="P:Microsoft.VisualBasic.ApplicationServices.WindowsFormsApplicationBase.MainForm" /> property has not been set.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.ApplicationServices.NoStartupFormException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.ApplicationServices.NoStartupFormException" /> class.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.ApplicationServices.NoStartupFormException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.ApplicationServices.NoStartupFormException" /> class with serialized data.</summary>
      <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> object that holds the serialized object data about the exception being thrown.</param>
      <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext" /> object that contains contextual information about the source or destination.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.ApplicationServices.NoStartupFormException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.ApplicationServices.NoStartupFormException" /> class with a specified error message.</summary>
      <param name="message">A message that describes the error.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.ApplicationServices.NoStartupFormException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.ApplicationServices.NoStartupFormException" /> class with a specified error message and a reference to the inner exception that is the cause of this exception.</summary>
      <param name="message">A <see cref="T:System.String" /> object describing the error.</param>
      <param name="inner">The <see cref="T:System.Exception" /> object that is the cause of the current exception. If the <see cref="P:System.Exception.InnerException" /> parameter is not a null reference (<see langword="Nothing" /> in Visual Basic), the current exception is raised in a catch block that handles the inner exception.</param>
    </member>
    <member name="T:Microsoft.VisualBasic.ApplicationServices.ShutdownEventHandler">
      <summary>Represents the method that will handle the <see langword="My.Application.Shutdown" /> event.</summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A <see cref="T:System.EventArgs" /> object that contains the event data.</param>
    </member>
    <member name="T:Microsoft.VisualBasic.ApplicationServices.ShutdownMode">
      <summary>Indicates which condition should cause a Windows Forms application to shut down.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.ApplicationServices.ShutdownMode.AfterAllFormsClose">
      <summary>Shut down only after the last form closes.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.ApplicationServices.ShutdownMode.AfterMainFormCloses">
      <summary>Shut down when the main form closes.</summary>
    </member>
    <member name="T:Microsoft.VisualBasic.ApplicationServices.StartupEventArgs">
      <summary>Provides data for the <see langword="My.Application.Startup" /> event.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.ApplicationServices.StartupEventArgs.#ctor(System.Collections.ObjectModel.ReadOnlyCollection{System.String})">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.ApplicationServices.StartupEventArgs" /> class.</summary>
      <param name="args">A <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" /> object that contains the command-line arguments of the application.</param>
    </member>
    <member name="P:Microsoft.VisualBasic.ApplicationServices.StartupEventArgs.CommandLine">
      <summary>Gets the command-line arguments of the application.</summary>
      <returns>A <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" /> object that contains the command-line arguments of the application.</returns>
    </member>
    <member name="T:Microsoft.VisualBasic.ApplicationServices.StartupEventHandler">
      <summary>Represents the method that will handle the <see langword="My.Application.Startup" /> event.</summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A <see cref="T:Microsoft.VisualBasic.ApplicationServices.StartupEventArgs" /> object that contains the event data.</param>
    </member>
    <member name="T:Microsoft.VisualBasic.ApplicationServices.StartupNextInstanceEventArgs">
      <summary>Provides data for the <see langword="My.Application.StartupNextInstance" /> event.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.ApplicationServices.StartupNextInstanceEventArgs.#ctor(System.Collections.ObjectModel.ReadOnlyCollection{System.String},System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.ApplicationServices.StartupNextInstanceEventArgs" /> class.</summary>
      <param name="args">A <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" /> object that contains the command-line arguments of the subsequent application instance.</param>
      <param name="bringToForegroundFlag">A <see cref="T:System.Boolean" /> that indicates whether the first application instance should be brought to the foreground upon exiting the exception handler.</param>
    </member>
    <member name="P:Microsoft.VisualBasic.ApplicationServices.StartupNextInstanceEventArgs.BringToForeground">
      <summary>Indicates whether the first application instance should be brought to the foreground upon exiting the exception handler.</summary>
      <returns>A <see cref="T:System.Boolean" /> that indicates whether the first application instance should be brought to the foreground upon exiting the exception handler.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.ApplicationServices.StartupNextInstanceEventArgs.CommandLine">
      <summary>Gets the command-line arguments of the subsequent application instance.</summary>
      <returns>A <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" /> object that contains the command-line arguments of the subsequent application instance.</returns>
    </member>
    <member name="T:Microsoft.VisualBasic.ApplicationServices.StartupNextInstanceEventHandler">
      <summary>Represents the method that will handle the <see langword="My.Application.StartupNextInstance" /> event.</summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A <see cref="T:Microsoft.VisualBasic.ApplicationServices.StartupNextInstanceEventArgs" /> object that contains the event data.</param>
    </member>
    <member name="T:Microsoft.VisualBasic.ApplicationServices.UnhandledExceptionEventArgs">
      <summary>Provides data for the <see langword="My.Application.UnhandledException" /> event.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.ApplicationServices.UnhandledExceptionEventArgs.#ctor(System.Boolean,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.ApplicationServices.UnhandledExceptionEventArgs" /> class.</summary>
      <param name="exitApplication">A <see cref="T:System.Boolean" /> that indicates whether the application should exit upon exiting the exception handler.</param>
      <param name="exception">The <see cref="T:System.Exception" /> that occurred.</param>
    </member>
    <member name="P:Microsoft.VisualBasic.ApplicationServices.UnhandledExceptionEventArgs.ExitApplication">
      <summary>Indicates whether the application should exit upon exiting the exception handler.</summary>
      <returns>A <see cref="T:System.Boolean" /> that indicates whether the application should exit upon exiting the exception handler.</returns>
    </member>
    <member name="T:Microsoft.VisualBasic.ApplicationServices.UnhandledExceptionEventHandler">
      <summary>Represents the method that will handle the <see langword="My.Application.UnhandledException" /> event.</summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A <see cref="T:Microsoft.VisualBasic.ApplicationServices.UnhandledExceptionEventArgs" /> object that contains the event data.</param>
    </member>
    <member name="T:Microsoft.VisualBasic.ApplicationServices.User">
      <summary>Provides access to the information about the current user.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.ApplicationServices.User.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.ApplicationServices.User" /> class.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.ApplicationServices.User.IsInRole(System.String)">
      <summary>Determines whether the current user belongs to the specified role.</summary>
      <param name="role">The name of the role for which to check membership.</param>
      <returns>
        <see langword="True" /> if the current user is a member of the specified role; otherwise, <see langword="False" />.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.ApplicationServices.User.CurrentPrincipal">
      <summary>Gets or sets the current principal (for role-based security).</summary>
      <exception cref="T:System.Security.SecurityException">The caller does not have the permission required to set the principal.</exception>
      <returns>A <see cref="T:System.Security.Principal.IPrincipal" /> value representing the security context.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.ApplicationServices.User.InternalPrincipal">
      <summary>Gets or sets the principal object representing the current user.</summary>
      <returns>An <see cref="T:System.Security.Principal.IPrincipal" /> object representing the current user.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.ApplicationServices.User.IsAuthenticated">
      <summary>Gets a value that indicates whether the user has been authenticated.</summary>
      <returns>
        <see langword="True" /> if the user was authenticated; otherwise, <see langword="False" />.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.ApplicationServices.User.Name">
      <summary>Gets the name of the current user.</summary>
      <returns>
        <see langword="String" />. The name of the current user.</returns>
    </member>
    <member name="T:Microsoft.VisualBasic.ApplicationServices.WindowsFormsApplicationBase">
      <summary>Provides properties, methods, and events related to the current application.</summary>
    </member>
    <member name="E:Microsoft.VisualBasic.ApplicationServices.WindowsFormsApplicationBase.ApplyApplicationDefaults">
      <summary>Occurs when the application is ready to accept default values for various application areas.</summary>
    </member>
    <member name="E:Microsoft.VisualBasic.ApplicationServices.WindowsFormsApplicationBase.NetworkAvailabilityChanged">
      <summary>Occurs when the network availability changes.</summary>
    </member>
    <member name="E:Microsoft.VisualBasic.ApplicationServices.WindowsFormsApplicationBase.Shutdown">
      <summary>Occurs when the application shuts down.</summary>
    </member>
    <member name="E:Microsoft.VisualBasic.ApplicationServices.WindowsFormsApplicationBase.Startup">
      <summary>Occurs when the application starts.</summary>
    </member>
    <member name="E:Microsoft.VisualBasic.ApplicationServices.WindowsFormsApplicationBase.StartupNextInstance">
      <summary>Occurs when attempting to start a single-instance application and the application is already active.</summary>
    </member>
    <member name="E:Microsoft.VisualBasic.ApplicationServices.WindowsFormsApplicationBase.UnhandledException">
      <summary>Occurs when the application encounters an unhandled exception.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.ApplicationServices.WindowsFormsApplicationBase.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.ApplicationServices.WindowsFormsApplicationBase" /> class.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.ApplicationServices.WindowsFormsApplicationBase.#ctor(Microsoft.VisualBasic.ApplicationServices.AuthenticationMode)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.ApplicationServices.WindowsFormsApplicationBase" /> class with the specified authentication mode.</summary>
      <param name="authenticationMode">One of the enumeration values that specifies the application's authentication mode.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.ApplicationServices.WindowsFormsApplicationBase.DoEvents">
      <summary>Processes all Windows messages currently in the message queue.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.ApplicationServices.WindowsFormsApplicationBase.HideSplashScreen">
      <summary>Hides the application's splash screen.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.ApplicationServices.WindowsFormsApplicationBase.OnCreateMainForm">
      <summary>When overridden in a derived class, allows a designer to emit code that configures the splash screen and main form.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.ApplicationServices.WindowsFormsApplicationBase.OnCreateSplashScreen">
      <summary>When overridden in a derived class, allows a designer to emit code that initializes the splash screen.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.ApplicationServices.WindowsFormsApplicationBase.OnInitialize(System.Collections.ObjectModel.ReadOnlyCollection{System.String})">
      <summary>Sets the visual styles, text display styles, and current principal for the main application thread (if the application uses Windows authentication), and initializes the splash screen, if defined.</summary>
      <param name="commandLineArgs">A read-only collection containing the command-line arguments as strings for the current application.</param>
      <returns>A <see cref="T:System.Boolean" /> indicating if application startup should continue.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.ApplicationServices.WindowsFormsApplicationBase.OnRun">
      <summary>Provides the starting point for when the main application is ready to start running, after the initialization is done.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.ApplicationServices.WindowsFormsApplicationBase.OnShutdown">
      <summary>When overridden in a derived class, allows for code to run when the application shuts down.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.ApplicationServices.WindowsFormsApplicationBase.OnStartup(Microsoft.VisualBasic.ApplicationServices.StartupEventArgs)">
      <summary>When overridden in a derived class, allows for code to run when the application starts.</summary>
      <param name="eventArgs">The command-line arguments of the application and indicates whether the application startup should be canceled.</param>
      <returns>A <see cref="T:System.Boolean" /> that indicates if the application should continue starting up.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.ApplicationServices.WindowsFormsApplicationBase.OnStartupNextInstance(Microsoft.VisualBasic.ApplicationServices.StartupNextInstanceEventArgs)">
      <summary>When overridden in a derived class, allows for code to run when a subsequent instance of a single-instance application starts.</summary>
      <param name="eventArgs">The command-line arguments of the subsequent application instance and indicates whether the first application instance should be brought to the foreground upon exiting the exception handler.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.ApplicationServices.WindowsFormsApplicationBase.OnUnhandledException(Microsoft.VisualBasic.ApplicationServices.UnhandledExceptionEventArgs)">
      <summary>When overridden in a derived class, allows for code to run when an unhandled exception occurs in the application.</summary>
      <param name="e">The data for the event.</param>
      <returns>A <see cref="T:System.Boolean" /> that indicates whether the <see cref="E:Microsoft.VisualBasic.ApplicationServices.WindowsFormsApplicationBase.UnhandledException" /> event was raised.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.ApplicationServices.WindowsFormsApplicationBase.Run(System.String[])">
      <summary>Sets up and starts the Visual Basic Application model.</summary>
      <param name="commandLine">The command line from <see langword="Sub Main" />.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.ApplicationServices.WindowsFormsApplicationBase.ShowSplashScreen">
      <summary>Determines if the application has a splash screen defined, and if it does, displays it.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.ApplicationServices.WindowsFormsApplicationBase.ApplicationContext">
      <summary>Gets the <see cref="T:System.Windows.Forms.ApplicationContext" /> object for the current thread of a Windows Forms application.</summary>
      <returns>Contextual information about the current thread.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.ApplicationServices.WindowsFormsApplicationBase.EnableVisualStyles">
      <summary>Determines whether this application will use the Windows XP styles for windows, controls, and so on.</summary>
      <returns>A <see cref="T:System.Boolean" /> value that indicates whether this application will use the XP Windows styles for windows, controls, and so on.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.ApplicationServices.WindowsFormsApplicationBase.HighDpiMode">
      <summary>Gets or sets the HighDpiMode for the application.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.ApplicationServices.WindowsFormsApplicationBase.IsSingleInstance">
      <summary>Gets or sets a value that indicates whether this application is a single-instance application.</summary>
      <returns>
        <see langword="True" /> to indicate this application is a single-instance application; otherwise, <see langword="False" />.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.ApplicationServices.WindowsFormsApplicationBase.MainForm">
      <summary>Gets or sets the main form for this application.</summary>
      <returns>The main form for this application.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.ApplicationServices.WindowsFormsApplicationBase.MinimumSplashScreenDisplayTime">
      <summary>Gets or sets the minimum length of time, in milliseconds, for which the splash screen is displayed.</summary>
      <returns>The minimum length of time, in milliseconds, for which the splash screen is displayed.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.ApplicationServices.WindowsFormsApplicationBase.OpenForms">
      <summary>Gets a collection of all the application's open forms.</summary>
      <returns>A collection that contains all of the application's open forms.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.ApplicationServices.WindowsFormsApplicationBase.SaveMySettingsOnExit">
      <summary>Determines whether the application saves the user settings on exit.</summary>
      <returns>
        <see langword="True" /> to indicate that the application saves the user settings on exit. Otherwise, <see langword="False" /> to indicate the settings are not implicitly saved.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.ApplicationServices.WindowsFormsApplicationBase.ShutdownStyle">
      <summary>Determines what happens when the application's main form closes.</summary>
      <returns>One of the enumeration values that indicates what the application should do when the main form closes.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.ApplicationServices.WindowsFormsApplicationBase.SplashScreen">
      <summary>Gets or sets the splash screen for this application.</summary>
      <exception cref="T:System.ArgumentNullException">The same value is assigned to this property and the <see cref="P:Microsoft.VisualBasic.ApplicationServices.WindowsFormsApplicationBase.MainForm" /> property.</exception>
      <returns>The splash screen for the application.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.ApplicationServices.WindowsFormsApplicationBase.UseCompatibleTextRendering">
      <summary>When overridden in a derived class, this property allows a designer to specify the default text rendering engine for the application's forms.</summary>
      <returns>
        <see langword="Boolean" />. A value of <see langword="False" /> indicates that the application should use the default text rendering engine for Visual Basic 2005. A value of <see langword="True" /> indicates that the application should use the text rendering engine for Visual Basic .NET 2002 and Visual Basic .NET 2003.</returns>
    </member>
    <member name="T:Microsoft.VisualBasic.AudioPlayMode">
      <summary>Indicates how to play sounds when calling audio methods.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.AudioPlayMode.Background">
      <summary>Causes the <see langword="My.Computer.Audio.Play" /> method to play the sound in the background. The calling code continues to execute.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.AudioPlayMode.BackgroundLoop">
      <summary>Causes the <see langword="My.Computer.Audio.Play" /> method to play the sound in the background until the <see cref="M:Microsoft.VisualBasic.Devices.Audio.Stop" /> method is called. The calling code continues to execute.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.AudioPlayMode.WaitToComplete">
      <summary>Causes the <see langword="My.Computer.Audio.Play" /> method to play the sound, and waits until it completes before calling code continues.</summary>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.HostServices">
      <summary>Returns a reference to and information about the current Visual Basic host window.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.HostServices.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.CompilerServices.HostServices" /> class.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.CompilerServices.HostServices.VBHost">
      <summary>Gets the current Visual Basic host object.</summary>
      <returns>A <see cref="P:Microsoft.VisualBasic.CompilerServices.HostServices.VBHost" /> object that returns a reference to and information about the current Visual Basic host window.</returns>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.IVbHost">
      <summary>Represents a host window for Visual Basic.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.IVbHost.GetParentWindow">
      <summary>Gets the host Window for the current Visual Basic environment.</summary>
      <returns>The host window for the current Visual Basic environment.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.IVbHost.GetWindowTitle">
      <summary>Gets the title of the host window for the current Visual Basic environment.</summary>
      <returns>The title of the host window for the current Visual Basic environment.</returns>
    </member>
    <member name="T:Microsoft.VisualBasic.Devices.Audio">
      <summary>Provides methods for playing sounds.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.Devices.Audio.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.Devices.Audio" /> class.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.Devices.Audio.Play(System.Byte[],Microsoft.VisualBasic.AudioPlayMode)">
      <summary>Plays a .wav sound file.</summary>
      <param name="data">
        <see langword="Byte" /> array that represents the sound file.</param>
      <param name="playMode">
        <see cref="T:Microsoft.VisualBasic.AudioPlayMode" /> mode for playing the sound. By default, <see langword="AudioPlayMode.Background" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="data" /> is <see langword="Nothing" />.</exception>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">
        <paramref name="playMode" /> is not one of the <see cref="T:Microsoft.VisualBasic.AudioPlayMode" /> enumeration values.</exception>
      <exception cref="T:System.Security.SecurityException">A partial-trust situation exists in which the user lacks necessary permissions.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Devices.Audio.Play(System.IO.Stream,Microsoft.VisualBasic.AudioPlayMode)">
      <summary>Plays a .wav sound file.</summary>
      <param name="stream">
        <see cref="T:System.IO.Stream" /> that represents the sound file.</param>
      <param name="playMode">
        <see cref="T:Microsoft.VisualBasic.AudioPlayMode" /> mode for playing the sound. By default, <see langword="AudioPlayMode.Background" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> is <see langword="Nothing" />.</exception>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">
        <paramref name="playMode" /> is not one of the <see cref="T:Microsoft.VisualBasic.AudioPlayMode" /> enumeration values.</exception>
      <exception cref="T:System.Security.SecurityException">A partial-trust situation exists in which the user lacks necessary permissions.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Devices.Audio.Play(System.String)">
      <summary>Plays a .wav sound file.</summary>
      <param name="location">A <see langword="String" /> containing the name of the sound file.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="location" /> is an empty string.</exception>
      <exception cref="T:System.IO.IOException">The user does not have sufficient permissions to access the file named by <paramref name="location" />.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The file path is malformed in <paramref name="location" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The path name in <paramref name="location" /> is too long.</exception>
      <exception cref="T:System.Security.SecurityException">A partial-trust situation exists in which the user lacks necessary permissions.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Devices.Audio.Play(System.String,Microsoft.VisualBasic.AudioPlayMode)">
      <summary>Plays a .wav sound file.</summary>
      <param name="location">A <see langword="String" /> containing the name of the sound file.</param>
      <param name="playMode">
        <see cref="T:Microsoft.VisualBasic.AudioPlayMode" /> mode for playing the sound. By default, <see langword="AudioPlayMode.Background" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="location" /> is an empty string.</exception>
      <exception cref="T:System.IO.IOException">The user does not have sufficient permissions to access the file named by <paramref name="location" />.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The file path is malformed in <paramref name="location" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The path name in <paramref name="location" /> is too long.</exception>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">
        <paramref name="playMode" /> is not one of the <see cref="T:Microsoft.VisualBasic.AudioPlayMode" /> enumeration values.</exception>
      <exception cref="T:System.Security.SecurityException">A partial-trust situation exists in which the user lacks necessary permissions.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Devices.Audio.PlaySystemSound(System.Media.SystemSound)">
      <summary>Plays a system sound.</summary>
      <param name="systemSound">
        <see cref="T:System.Media.SystemSound" /> object representing the system sound to play.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="systemSound" /> is <see langword="Nothing" />.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Devices.Audio.Stop">
      <summary>Stops a sound playing in the background.</summary>
    </member>
    <member name="T:Microsoft.VisualBasic.Devices.Clock">
      <summary>Provides properties for accessing the current local time and Universal Coordinated Time (equivalent to Greenwich Mean Time) from the system clock.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.Devices.Clock.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.Devices.Clock" /> class.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.Devices.Clock.GmtTime">
      <summary>Gets a <see langword="Date" /> object that contains the current local date and time on the computer, expressed as a UTC (GMT) time.</summary>
      <returns>A <see langword="Date" /> object that contains the current date and time expressed as UTC (GMT) time.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.Devices.Clock.LocalTime">
      <summary>Gets a <see langword="Date" /> object that contains the current local date and time on this computer.</summary>
      <returns>A <see langword="Date" /> object that contains the current local date and time.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.Devices.Clock.TickCount">
      <summary>Gets the millisecond count from the computer's system timer.</summary>
      <returns>An <see langword="Integer" /> containing the millisecond count from the computer's system timer.</returns>
    </member>
    <member name="T:Microsoft.VisualBasic.Devices.Computer">
      <summary>Provides properties for manipulating computer components such as audio, the clock, the keyboard, the file system, and so on.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.Devices.Computer.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.Devices.Computer" /> class.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.Devices.Computer.Audio">
      <summary>Gets an object that provides properties for methods for playing sounds.</summary>
      <returns>The <see langword="My.Computer.Audio" /> object for the computer.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.Devices.Computer.Clipboard">
      <summary>Gets an object that provides methods for manipulating the Clipboard.</summary>
      <returns>The <see langword="My.Computer.Clipboard" /> object for the computer.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.Devices.Computer.Keyboard">
      <summary>Gets an object that provides properties for accessing the current state of the keyboard, such as what keys are currently pressed, and provides a method to send keystrokes to the active window.</summary>
      <returns>The <see langword="My.Computer.Keyboard" /> object for the computer.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.Devices.Computer.Mouse">
      <summary>Gets an object that provides properties for getting information about the format and configuration of the mouse installed on the local computer.</summary>
      <returns>The <see langword="My.Computer.Mouse" /> object for the computer.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.Devices.Computer.Screen">
      <summary>Gets the <see cref="T:System.Windows.Forms.Screen" /> object that represents the computer's primary display screen.</summary>
      <returns>A <see cref="T:System.Windows.Forms.Screen" /> object that represents the computer's primary screen.</returns>
    </member>
    <member name="T:Microsoft.VisualBasic.Devices.ComputerInfo">
      <summary>Provides properties for getting information about the computer's memory, loaded assemblies, name, and operating system.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.Devices.ComputerInfo.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.Devices.ComputerInfo" /> class.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.Devices.ComputerInfo.AvailablePhysicalMemory">
      <summary>Gets the total amount of free physical memory for the computer.</summary>
      <exception cref="T:System.ComponentModel.Win32Exception">The application cannot obtain the memory status.</exception>
      <returns>A <see langword="ULong" /> containing the number of bytes of free physical memory for the computer.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.Devices.ComputerInfo.AvailableVirtualMemory">
      <summary>Gets the total amount of the computer's free virtual address space.</summary>
      <exception cref="T:System.ComponentModel.Win32Exception">The application cannot obtain the memory status.</exception>
      <returns>A <see langword="ULong" /> containing the number of bytes of the computer's free virtual address space.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.Devices.ComputerInfo.InstalledUICulture">
      <summary>Gets the current UI culture installed with the operating system.</summary>
      <returns>A <see cref="T:System.Globalization.CultureInfo" /> object represents the UI culture installed on the computer.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.Devices.ComputerInfo.OSFullName">
      <summary>Gets the full operating system name.</summary>
      <exception cref="T:System.Security.SecurityException">The calling code does not have full trust.</exception>
      <returns>A <see langword="String" /> containing the operating-system name.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.Devices.ComputerInfo.OSPlatform">
      <summary>Gets the platform identifier for the operating system of the computer.</summary>
      <exception cref="T:System.ExecutionEngineException">The application cannot obtain the operating-system platform information.</exception>
      <returns>A <see langword="String" /> containing the platform identifier for the operating system of the computer, chosen from the member names of the <see cref="T:System.PlatformID" /> enumeration.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.Devices.ComputerInfo.OSVersion">
      <summary>Gets the version of the computer's operating system.</summary>
      <exception cref="T:System.ExecutionEngineException">The application cannot obtain the operating-system version information.</exception>
      <returns>A <see langword="String" /> containing the current version number of the operating system.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.Devices.ComputerInfo.TotalPhysicalMemory">
      <summary>Gets the total amount of physical memory for the computer.</summary>
      <exception cref="T:System.ComponentModel.Win32Exception">The application cannot obtain the memory status.</exception>
      <returns>A <see langword="ULong" /> containing the number of bytes of physical memory for the computer.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.Devices.ComputerInfo.TotalVirtualMemory">
      <summary>Gets the total amount of virtual address space available for the computer.</summary>
      <exception cref="T:System.ComponentModel.Win32Exception">The application cannot obtain the memory status.</exception>
      <returns>A <see langword="ULong" /> containing the number of bytes of virtual address space available for the computer.</returns>
    </member>
    <member name="T:Microsoft.VisualBasic.Devices.Keyboard">
      <summary>Provides properties for accessing the current state of the keyboard, such as what keys are currently pressed, and provides a method to send keystrokes to the active window.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.Devices.Keyboard.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.Devices.Keyboard" /> class.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.Devices.Keyboard.SendKeys(System.String)">
      <summary>Sends one or more keystrokes to the active window, as if typed on the keyboard.</summary>
      <param name="keys">A <see langword="String" /> that defines the keys to send.</param>
      <exception cref="T:System.Security.SecurityException">A partial-trust situation exists in which the user lacks necessary permissions.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Devices.Keyboard.SendKeys(System.String,System.Boolean)">
      <summary>Sends one or more keystrokes to the active window, as if typed on the keyboard.</summary>
      <param name="keys">A <see langword="String" /> that defines the keys to send.</param>
      <param name="wait">Optional. A <see langword="Boolean" /> that specifies whether or not to wait for keystrokes to get processed before the application continues. <see langword="True" /> by default.</param>
      <exception cref="T:System.Security.SecurityException">A partial-trust situation exists in which the user lacks necessary permissions.</exception>
    </member>
    <member name="P:Microsoft.VisualBasic.Devices.Keyboard.AltKeyDown">
      <summary>Gets a value that indicates whether the ALT key is down.</summary>
      <returns>
        <see langword="True" /> if the ALT key is down; otherwise, <see langword="False" />.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.Devices.Keyboard.CapsLock">
      <summary>Gets a value that indicates whether CAPS LOCK is turned on.</summary>
      <returns>A <see langword="Boolean" /> value: <see langword="True" /> if CAPS LOCK is turned on; otherwise, <see langword="False" />.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.Devices.Keyboard.CtrlKeyDown">
      <summary>Gets a value that indicates whether a CTRL key is down.</summary>
      <returns>
        <see langword="True" /> if a CTRL key is down; otherwise, <see langword="False" />.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.Devices.Keyboard.NumLock">
      <summary>Gets a value that indicates whether the NUM LOCK key is on.</summary>
      <returns>
        <see langword="True" /> if NUM LOCK is on; otherwise, <see langword="False" />.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.Devices.Keyboard.ScrollLock">
      <summary>Gets a <see langword="Boolean" /> indicating whether the SCROLL LOCK key is on.</summary>
      <returns>
        <see langword="True" /> if SCROLL LOCK is on; otherwise, <see langword="False" />.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.Devices.Keyboard.ShiftKeyDown">
      <summary>Gets a value that indicates whether a SHIFT key is down.</summary>
      <returns>
        <see langword="True" /> if a SHIFT key is down; otherwise, <see langword="False" />.</returns>
    </member>
    <member name="T:Microsoft.VisualBasic.Devices.Mouse">
      <summary>Provides properties for getting information about the format and configuration of the mouse installed on the local computer.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.Devices.Mouse.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.Devices.Mouse" /> class.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.Devices.Mouse.ButtonsSwapped">
      <summary>Gets a <see langword="Boolean" /> that indicates if the functionality of the left and right mouse buttons has been swapped.</summary>
      <exception cref="T:System.InvalidOperationException">The computer has no mouse installed.</exception>
      <returns>A <see langword="Boolean" /> with a value <see langword="True" /> if the functionality of the left and right mouse buttons has been swapped; otherwise, <see langword="False" />.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.Devices.Mouse.WheelExists">
      <summary>Gets a <see langword="Boolean" /> that indicates if the mouse has a scroll wheel.</summary>
      <exception cref="T:System.InvalidOperationException">The computer has no mouse installed.</exception>
      <returns>A Boolean with value <see langword="True" /> if the mouse has a scroll wheel; otherwise <see langword="False" />.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.Devices.Mouse.WheelScrollLines">
      <summary>Gets a number that indicates how much to scroll when the mouse wheel is rotated one notch.</summary>
      <exception cref="T:System.InvalidOperationException">The mouse has no scroll wheel.</exception>
      <returns>An <see langword="Integer" /> that indicates how much to scroll when the mouse wheel is rotated one notch. A positive value indicates scrolling by that number of lines, while a negative value indicates scrolling by one screen at a time.</returns>
    </member>
    <member name="T:Microsoft.VisualBasic.Devices.Network">
      <summary>Provides a property, event, and methods for interacting with the network to which the computer is connected.</summary>
    </member>
    <member name="E:Microsoft.VisualBasic.Devices.Network.NetworkAvailabilityChanged">
      <summary>Occurs when the network availability changes.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.Devices.Network.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.Devices.Network" /> class.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.Devices.Network.DownloadFile(System.String,System.String)">
      <summary>Downloads the specified remote file and saves it in the specified location.</summary>
      <param name="address">Path of the file to download, including file name and host address.</param>
      <param name="destinationFileName">File name and path of the downloaded file.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destinationFileName" /> ends with a trailing slash.</exception>
      <exception cref="T:System.TimeoutException">The server does not respond within the default timeout (100 seconds).</exception>
      <exception cref="T:System.Security.SecurityException">User lacks necessary permissions to perform a network operation.</exception>
      <exception cref="T:System.Net.WebException">The request is denied by the target web server.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Devices.Network.DownloadFile(System.String,System.String,System.String,System.String)">
      <summary>Downloads the specified remote file and saves it in the specified location.</summary>
      <param name="address">Path of the file to download, including file name and host address.</param>
      <param name="destinationFileName">File name and path of the downloaded file.</param>
      <param name="userName">User name to authenticate. Default is an empty string, "".</param>
      <param name="password">Password to authenticate. Default is an empty string, "".</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destinationFileName" /> ends with a trailing slash.</exception>
      <exception cref="T:System.TimeoutException">The server does not respond within the default timeout (100 seconds).</exception>
      <exception cref="T:System.Security.SecurityException">User lacks necessary permissions to perform a network operation.</exception>
      <exception cref="T:System.Net.WebException">The request is denied by the target web server.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Devices.Network.DownloadFile(System.String,System.String,System.String,System.String,System.Boolean,System.Int32,System.Boolean)">
      <summary>Downloads the specified remote file and saves it in the specified location.</summary>
      <param name="address">Path of the file to download, including file name and host address.</param>
      <param name="destinationFileName">File name and path of the downloaded file.</param>
      <param name="userName">User name to authenticate. Default is an empty string, "".</param>
      <param name="password">Password to authenticate. Default is an empty string, "".</param>
      <param name="showUI">
        <see langword="True" /> to display the progress of the operation; otherwise <see langword="False" />. Default is <see langword="False" />.</param>
      <param name="connectionTimeout">
        <see cref="T:System.Int32" />. Timeout interval, in milliseconds. Default is 100 seconds.</param>
      <param name="overwrite">
        <see langword="True" /> to overwrite existing files; otherwise <see langword="False" />. Default is <see langword="False" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destinationFileName" /> ends with a trailing slash.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="overwrite" /> is set to <see langword="False" /> and the destination file already exists.</exception>
      <exception cref="T:System.TimeoutException">The server does not respond within the specified <paramref name="connectionTimeout" />.</exception>
      <exception cref="T:System.Security.SecurityException">User lacks necessary permissions to perform a network operation.</exception>
      <exception cref="T:System.Net.WebException">The request is denied by the target web server.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Devices.Network.DownloadFile(System.String,System.String,System.String,System.String,System.Boolean,System.Int32,System.Boolean,Microsoft.VisualBasic.FileIO.UICancelOption)">
      <summary>Downloads the specified remote file and saves it in the specified location.</summary>
      <param name="address">Path of the file to download, including file name and host address.</param>
      <param name="destinationFileName">File name and path of the downloaded file.</param>
      <param name="userName">User name to authenticate. Default is an empty string, "".</param>
      <param name="password">Password to authenticate. Default is an empty string, "".</param>
      <param name="showUI">
        <see langword="True" /> to display the progress of the operation; otherwise <see langword="False" />. Default is <see langword="False" />.</param>
      <param name="connectionTimeout">Timeout interval, in milliseconds. Default is 100 seconds.</param>
      <param name="overwrite">
        <see langword="True" /> to overwrite existing files; otherwise <see langword="False" />. Default is <see langword="False" />.</param>
      <param name="onUserCancel">Specifies behavior when the user clicks Cancel or No on the dialog box shown as a result of <c>ShowUI</c> set to <see langword="True" />. Default is <see cref="F:Microsoft.VisualBasic.FileIO.UICancelOption.ThrowException" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destinationFileName" /> ends with a trailing slash.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="overwrite" /> is set to <see langword="False" /> and the destination file already exists.</exception>
      <exception cref="T:System.TimeoutException">The server does not respond within the specified <paramref name="connectionTimeout" />.</exception>
      <exception cref="T:System.Security.SecurityException">User lacks necessary permissions to perform a network operation.</exception>
      <exception cref="T:System.Net.WebException">The request is denied by the target web server.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Devices.Network.DownloadFile(System.Uri,System.String)">
      <summary>Downloads the specified remote file and saves it in the specified location.</summary>
      <param name="address">Path of the file to download, including file name and host address.</param>
      <param name="destinationFileName">File name and path of the downloaded file.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destinationFileName" /> ends with a trailing slash.</exception>
      <exception cref="T:System.TimeoutException">The server does not respond within the default timeout (100 seconds).</exception>
      <exception cref="T:System.Security.SecurityException">User lacks necessary permissions to perform a network operation.</exception>
      <exception cref="T:System.Net.WebException">The request is denied by the target web server.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Devices.Network.DownloadFile(System.Uri,System.String,System.Net.ICredentials,System.Boolean,System.Int32,System.Boolean)">
      <summary>Downloads the specified remote file and saves it in the specified location.</summary>
      <param name="address">
        <see langword="String" /> or <see cref="T:System.Uri" />. Path of the file to download, including file name and host address.</param>
      <param name="destinationFileName">
        <see langword="String" />. File name and path of the downloaded file.</param>
      <param name="networkCredentials">
        <see cref="T:System.Net.ICredentials" />. Credentials to be supplied.</param>
      <param name="showUI">
        <see langword="True" /> to display the progress of the operation; otherwise <see langword="False" />. Default is <see langword="False" />.</param>
      <param name="connectionTimeout">Timeout interval, in milliseconds. Default is 100 seconds.</param>
      <param name="overwrite">
        <see langword="True" /> to overwrite existing files; otherwise <see langword="False" />. Default is <see langword="False" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destinationFileName" /> ends with a trailing slash.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="overwrite" /> is set to <see langword="False" /> and the destination file already exists.</exception>
      <exception cref="T:System.TimeoutException">The server does not respond within the specified <paramref name="connectionTimeout" />.</exception>
      <exception cref="T:System.Security.SecurityException">User lacks necessary permissions to perform a network operation.</exception>
      <exception cref="T:System.Net.WebException">The request is denied by the target web server.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Devices.Network.DownloadFile(System.Uri,System.String,System.Net.ICredentials,System.Boolean,System.Int32,System.Boolean,Microsoft.VisualBasic.FileIO.UICancelOption)">
      <summary>Downloads the specified remote file and saves it in the specified location.</summary>
      <param name="address">Path of the file to download, including file name and host address.</param>
      <param name="destinationFileName">File name and path of the downloaded file.</param>
      <param name="networkCredentials">Credentials to be supplied.</param>
      <param name="showUI">
        <see langword="True" /> to display the progress of the operation; otherwise <see langword="False" />. Default is <see langword="False" />.</param>
      <param name="connectionTimeout">Timeout interval, in milliseconds. Default is 100 seconds.</param>
      <param name="overwrite">
        <see langword="True" /> to overwrite existing files; otherwise <see langword="False" />. Default is <see langword="False" />.</param>
      <param name="onUserCancel">Specifies behavior when the user clicks Cancel or No on the dialog box shown as a result of <paramref name="showUI" /> set to <see langword="True" />. Default is <see cref="F:Microsoft.VisualBasic.FileIO.UICancelOption.ThrowException" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destinationFileName" /> ends with a trailing slash.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="overwrite" /> is set to <see langword="False" /> and the destination file already exists.</exception>
      <exception cref="T:System.TimeoutException">The server does not respond within the specified <paramref name="connectionTimeout" />.</exception>
      <exception cref="T:System.Security.SecurityException">User lacks necessary permissions to perform a network operation.</exception>
      <exception cref="T:System.Net.WebException">The request is denied by the target web server.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Devices.Network.DownloadFile(System.Uri,System.String,System.String,System.String)">
      <summary>Downloads the specified remote file and saves it in the specified location.</summary>
      <param name="address">Path of the file to download, including file name and host address.</param>
      <param name="destinationFileName">File name and path of the downloaded file.</param>
      <param name="userName">User name to authenticate. Default is an empty string, "".</param>
      <param name="password">Password to authenticate. Default is an empty string, "".</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destinationFileName" /> ends with a trailing slash.</exception>
      <exception cref="T:System.TimeoutException">The server does not respond within the default timeout (100 seconds).</exception>
      <exception cref="T:System.Security.SecurityException">User lacks necessary permissions to perform a network operation.</exception>
      <exception cref="T:System.Net.WebException">The request is denied by the target web server.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Devices.Network.DownloadFile(System.Uri,System.String,System.String,System.String,System.Boolean,System.Int32,System.Boolean)">
      <summary>Downloads the specified remote file and saves it in the specified location.</summary>
      <param name="address">Path of the file to download, including file name and host address.</param>
      <param name="destinationFileName">File name and path of the downloaded file.</param>
      <param name="userName">User name to authenticate. Default is an empty string, "".</param>
      <param name="password">Password to authenticate. Default is an empty string, "".</param>
      <param name="showUI">
        <see langword="True" /> to display the progress of the operation; otherwise <see langword="False" />. Default is <see langword="False" />.</param>
      <param name="connectionTimeout">Timeout interval, in milliseconds. Default is 100 seconds.</param>
      <param name="overwrite">
        <see langword="True" /> to overwrite existing files; otherwise <see langword="False" />. Default is <see langword="False" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destinationFileName" /> ends with a trailing slash.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="overwrite" /> is set to <see langword="False" /> and the destination file already exists.</exception>
      <exception cref="T:System.TimeoutException">The server does not respond within the specified <paramref name="connectionTimeout" />.</exception>
      <exception cref="T:System.Security.SecurityException">User lacks necessary permissions to perform a network operation.</exception>
      <exception cref="T:System.Net.WebException">The request is denied by the target web server.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Devices.Network.DownloadFile(System.Uri,System.String,System.String,System.String,System.Boolean,System.Int32,System.Boolean,Microsoft.VisualBasic.FileIO.UICancelOption)">
      <summary>Downloads the specified remote file and saves it in the specified location.</summary>
      <param name="address">Path of the file to download, including file name and host address.</param>
      <param name="destinationFileName">File name and path of the downloaded file.</param>
      <param name="userName">User name to authenticate. Default is an empty string, "".</param>
      <param name="password">Password to authenticate. Default is an empty string, "".</param>
      <param name="showUI">
        <see langword="True" /> to display the progress of the operation; otherwise <see langword="False" />. Default is <see langword="False" />.</param>
      <param name="connectionTimeout">Timeout interval, in milliseconds. Default is 100 seconds.</param>
      <param name="overwrite">
        <see langword="True" /> to overwrite existing files; otherwise <see langword="False" />. Default is <see langword="False" />.</param>
      <param name="onUserCancel">Specifies behavior when the user clicks Cancel or No on the dialog box shown as a result of <c>ShowUI</c> set to <see langword="True" />. Default is <see cref="F:Microsoft.VisualBasic.FileIO.UICancelOption.ThrowException" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destinationFileName" /> ends with a trailing slash.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="overwrite" /> is set to <see langword="False" /> and the destination file already exists.</exception>
      <exception cref="T:System.TimeoutException">The server does not respond within the specified <paramref name="connectionTimeout" />.</exception>
      <exception cref="T:System.Security.SecurityException">User lacks necessary permissions to perform a network operation.</exception>
      <exception cref="T:System.Net.WebException">The request is denied by the target web server.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Devices.Network.Ping(System.String)">
      <summary>Pings the specified server.</summary>
      <param name="hostNameOrAddress">The URL, computer name, or IP number of the server to ping.</param>
      <exception cref="T:System.InvalidOperationException">No network connection is available.</exception>
      <exception cref="T:System.Net.NetworkInformation.PingException">URL was not valid.</exception>
      <returns>
        <see langword="True" /> if the operation was successful; otherwise <see langword="False" />.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.Devices.Network.Ping(System.String,System.Int32)">
      <summary>Pings the specified server.</summary>
      <param name="hostNameOrAddress">The URL, computer name, or IP number of the server to ping.</param>
      <param name="timeout">Time threshold in milliseconds for contacting the destination. Default is 500.</param>
      <exception cref="T:System.InvalidOperationException">No network connection is available.</exception>
      <exception cref="T:System.Net.NetworkInformation.PingException">URL was not valid.</exception>
      <returns>
        <see langword="True" /> if the operation was successful; otherwise <see langword="False" />.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.Devices.Network.Ping(System.Uri)">
      <summary>Pings the specified server.</summary>
      <param name="address">The URI of the server to ping.</param>
      <exception cref="T:System.InvalidOperationException">No network connection is available.</exception>
      <exception cref="T:System.Net.NetworkInformation.PingException">URL was not valid.</exception>
      <returns>
        <see langword="True" /> if the operation was successful; otherwise <see langword="False" />.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.Devices.Network.Ping(System.Uri,System.Int32)">
      <summary>Pings the specified server.</summary>
      <param name="address">The URI of the server to ping.</param>
      <param name="timeout">Time threshold in milliseconds for contacting the destination. Default is 500.</param>
      <exception cref="T:System.InvalidOperationException">No network connection is available.</exception>
      <exception cref="T:System.Net.NetworkInformation.PingException">URL was not valid.</exception>
      <returns>
        <see langword="True" /> if the operation was successful; otherwise <see langword="False" />.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.Devices.Network.UploadFile(System.String,System.String)">
      <summary>Sends the specified file to the specified host address.</summary>
      <param name="sourceFileName">Path and name of file to upload.</param>
      <param name="address">URL, IP address, or URI of destination server.</param>
      <exception cref="T:System.ArgumentException">The source file path is not valid.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="address" /> does not include a file name.</exception>
      <exception cref="T:System.Security.SecurityException">User lacks necessary permissions to perform a network operation.</exception>
      <exception cref="T:System.TimeoutException">The server does not respond within the default timeout (100 seconds).</exception>
      <exception cref="T:System.Net.WebException">The request is denied by the target web server.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Devices.Network.UploadFile(System.String,System.String,System.String,System.String)">
      <summary>Sends the specified file to the specified host address.</summary>
      <param name="sourceFileName">Path and name of file to upload.</param>
      <param name="address">URL, IP address, or URI of destination server.</param>
      <param name="userName">User name to authenticate. Default is an empty string: <c>""</c>.</param>
      <param name="password">Password to authenticate. Default is an empty string: <c>""</c>.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="connectionTimeout" /> is less than or equal to zero.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="address" /> does not include a file name.</exception>
      <exception cref="T:System.Security.SecurityException">User lacks necessary permissions to perform a network operation.</exception>
      <exception cref="T:System.TimeoutException">The server does not respond within the default timeout (100 seconds).</exception>
      <exception cref="T:System.Net.WebException">The request is denied by the target web server.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Devices.Network.UploadFile(System.String,System.String,System.String,System.String,System.Boolean,System.Int32)">
      <summary>Sends the specified file to the specified host address.</summary>
      <param name="sourceFileName">Path and name of file to upload.</param>
      <param name="address">URL, IP address, or URI of destination server.</param>
      <param name="userName">User name to authenticate. Default is an empty string: <c>""</c>.</param>
      <param name="password">Password to authenticate. Default is an empty string: <c>""</c>.</param>
      <param name="showUI">
        <see langword="True" /> to display progress of the operation; otherwise <see langword="False" />. Default is <see langword="False" />.</param>
      <param name="connectionTimeout">Timeout interval in milliseconds. Default is 100 seconds.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="connectionTimeout" /> is less than or equal to zero.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="address" /> does not include a file name.</exception>
      <exception cref="T:System.Security.SecurityException">User lacks necessary permissions to perform a network operation.</exception>
      <exception cref="T:System.TimeoutException">The server does not respond within the specified <paramref name="connectionTimeout" />.</exception>
      <exception cref="T:System.Net.WebException">The request is denied by the target web server.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Devices.Network.UploadFile(System.String,System.String,System.String,System.String,System.Boolean,System.Int32,Microsoft.VisualBasic.FileIO.UICancelOption)">
      <summary>Sends the specified file to the specified host address.</summary>
      <param name="sourceFileName">Path and name of file to upload.</param>
      <param name="address">URL, IP address, or URI of destination server.</param>
      <param name="userName">User name to authenticate. Default is an empty string: <c>""</c>.</param>
      <param name="password">Password to authenticate. Default is an empty string: <c>""</c>.</param>
      <param name="showUI">Whether to display progress of the operation. Default is <see langword="False" />.</param>
      <param name="connectionTimeout">Timeout interval in milliseconds. Default is 100 seconds.</param>
      <param name="onUserCancel">Action to be taken when the user clicks Cancel. Default is <see cref="F:Microsoft.VisualBasic.FileIO.UICancelOption.ThrowException" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="connectionTimeout" /> is less than or equal to zero.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="address" /> does not include a file name.</exception>
      <exception cref="T:System.Security.SecurityException">User lacks necessary permissions to perform a network operation.</exception>
      <exception cref="T:System.TimeoutException">The server does not respond within the specified <paramref name="connectionTimeout" />.</exception>
      <exception cref="T:System.Net.WebException">The request is denied by the target web server.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Devices.Network.UploadFile(System.String,System.Uri)">
      <summary>Sends the specified file to the specified host address.</summary>
      <param name="sourceFileName">Path and name of file to upload.</param>
      <param name="address">URL, IP address, or URI of destination server.</param>
      <exception cref="T:System.ArgumentException">The source file path is not valid.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="address" /> does not include a file name.</exception>
      <exception cref="T:System.Security.SecurityException">User lacks necessary permissions to perform a network operation.</exception>
      <exception cref="T:System.TimeoutException">The server does not respond within the default timeout (100 seconds).</exception>
      <exception cref="T:System.Net.WebException">The request is denied by the target web server.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Devices.Network.UploadFile(System.String,System.Uri,System.Net.ICredentials,System.Boolean,System.Int32)">
      <summary>Sends the specified file to the specified host address.</summary>
      <param name="sourceFileName">Path and name of file to upload.</param>
      <param name="address">URL, IP address, or URI of destination server.</param>
      <param name="networkCredentials">Credentials for authentication.</param>
      <param name="showUI">
        <see langword="True" /> to display progress of the operation; otherwise <see langword="False" />. Default is <see langword="False" />.</param>
      <param name="connectionTimeout">Timeout interval in milliseconds. Default is 100 seconds.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="connectionTimeout" /> is less than or equal to zero.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="address" /> does not include a file name.</exception>
      <exception cref="T:System.Security.SecurityException">User lacks necessary permissions to perform a network operation.</exception>
      <exception cref="T:System.TimeoutException">The server does not respond within the specified <paramref name="connectionTimeout" />.</exception>
      <exception cref="T:System.Net.WebException">The request is denied by the target web server.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Devices.Network.UploadFile(System.String,System.Uri,System.Net.ICredentials,System.Boolean,System.Int32,Microsoft.VisualBasic.FileIO.UICancelOption)">
      <summary>Sends the specified file to the specified host address.</summary>
      <param name="sourceFileName">Path and name of file to upload.</param>
      <param name="address">URL, IP address, or URI of destination server.</param>
      <param name="networkCredentials">Credentials for authentication.</param>
      <param name="showUI">
        <see langword="True" /> to display progress of the operation; otherwise <see langword="False" />. Default is <see langword="False" />.</param>
      <param name="connectionTimeout">Timeout interval in milliseconds. Default is 100 seconds.</param>
      <param name="onUserCancel">Action to be taken when the user clicks Cancel. Default is <see cref="F:Microsoft.VisualBasic.FileIO.UICancelOption.ThrowException" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="connectionTimeout" /> is less than or equal to zero.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="address" /> does not include a file name.</exception>
      <exception cref="T:System.Security.SecurityException">User lacks necessary permissions to perform a network operation.</exception>
      <exception cref="T:System.TimeoutException">The server does not respond within the specified <paramref name="connectionTimeout" />.</exception>
      <exception cref="T:System.Net.WebException">The request is denied by the target web server.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Devices.Network.UploadFile(System.String,System.Uri,System.String,System.String)">
      <summary>Sends the specified file to the specified host address.</summary>
      <param name="sourceFileName">Path and name of file to upload.</param>
      <param name="address">URL, IP address, or URI of destination server.</param>
      <param name="userName">User name to authenticate. Default is an empty string: <c>""</c>.</param>
      <param name="password">Password to authenticate. Default is an empty string: <c>""</c>.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="connectionTimeout" /> is less than or equal to zero.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="address" /> does not include a file name.</exception>
      <exception cref="T:System.Security.SecurityException">User lacks necessary permissions to perform a network operation.</exception>
      <exception cref="T:System.TimeoutException">The server does not respond within the default timeout (100 seconds).</exception>
      <exception cref="T:System.Net.WebException">The request is denied by the target web server.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Devices.Network.UploadFile(System.String,System.Uri,System.String,System.String,System.Boolean,System.Int32)">
      <summary>Sends the specified file to the specified host address.</summary>
      <param name="sourceFileName">Path and name of file to upload.</param>
      <param name="address">URL, IP address, or URI of destination server.</param>
      <param name="userName">User name to authenticate. Default is an empty string: <c>""</c>.</param>
      <param name="password">Password to authenticate. Default is an empty string: <c>""</c>.</param>
      <param name="showUI">
        <see langword="True" /> to display progress of the operation; otherwise <see langword="False" />. Default is <see langword="False" />.</param>
      <param name="connectionTimeout">Timeout interval in milliseconds. Default is 100 seconds.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="connectionTimeout" /> is less than or equal to zero.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="address" /> does not include a file name.</exception>
      <exception cref="T:System.Security.SecurityException">User lacks necessary permissions to perform a network operation.</exception>
      <exception cref="T:System.TimeoutException">The server does not respond within the specified <paramref name="connectionTimeout" />.</exception>
      <exception cref="T:System.Net.WebException">The request is denied by the target web server.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Devices.Network.UploadFile(System.String,System.Uri,System.String,System.String,System.Boolean,System.Int32,Microsoft.VisualBasic.FileIO.UICancelOption)">
      <summary>Sends the specified file to the specified host address.</summary>
      <param name="sourceFileName">Path and name of file to upload.</param>
      <param name="address">URL, IP address, or URI of destination server.</param>
      <param name="userName">User name to authenticate. Default is an empty string: <c>""</c>.</param>
      <param name="password">Password to authenticate. Default is an empty string: <c>""</c>.</param>
      <param name="showUI">Whether to display progress of the operation. Default is <see langword="False" />.</param>
      <param name="connectionTimeout">Timeout interval in milliseconds. Default is 100 seconds.</param>
      <param name="onUserCancel">Action to be taken when the user clicks Cancel. Default is <see cref="F:Microsoft.VisualBasic.FileIO.UICancelOption.ThrowException" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="connectionTimeout" /> is less than or equal to zero.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="address" /> does not include a file name.</exception>
      <exception cref="T:System.Security.SecurityException">User lacks necessary permissions to perform a network operation.</exception>
      <exception cref="T:System.TimeoutException">The server does not respond within the specified <paramref name="connectionTimeout" />.</exception>
      <exception cref="T:System.Net.WebException">The request is denied by the target web server.</exception>
    </member>
    <member name="P:Microsoft.VisualBasic.Devices.Network.IsAvailable">
      <summary>Indicates whether a computer is connected to a network.</summary>
      <returns>
        <see langword="True" /> if the computer is connected to a network; otherwise <see langword="False" />.</returns>
    </member>
    <member name="T:Microsoft.VisualBasic.Devices.NetworkAvailableEventArgs">
      <summary>Provides data for the <see langword="My.Application.NetworkAvailabilityChanged" /> and <see langword="My.Computer.Network.NetworkAvailabilityChanged" /> events.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.Devices.NetworkAvailableEventArgs.#ctor(System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.ApplicationServices.StartupNextInstanceEventArgs" /> class.</summary>
      <param name="networkAvailable">A <see cref="T:System.Boolean" /> that indicates whether a network is available to the application.</param>
    </member>
    <member name="P:Microsoft.VisualBasic.Devices.NetworkAvailableEventArgs.IsNetworkAvailable">
      <summary>Gets a value indicating whether a network is available to the application.</summary>
      <returns>A <see cref="T:System.Boolean" /> that indicates whether a network is available to the application.</returns>
    </member>
    <member name="T:Microsoft.VisualBasic.Devices.NetworkAvailableEventHandler">
      <summary>Represents the method that will handle the <see langword="My.Application.NetworkAvailabilityChanged" /> or <see langword="My.Computer.Network.NetworkAvailabilityChanged" /> event.</summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A <see cref="T:Microsoft.VisualBasic.Devices.NetworkAvailableEventArgs" /> object that contains the event data.</param>
    </member>
    <member name="T:Microsoft.VisualBasic.Devices.ServerComputer">
      <summary>Provides properties for manipulating computer components such as audio, the clock, the keyboard, the file system, and so on.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.Devices.ServerComputer.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.Devices.ServerComputer" /> class.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.Devices.ServerComputer.Clock">
      <summary>Gets an object that provides properties for accessing the current local time and Universal Coordinated Time (the equivalent to Greenwich Mean Time) from the system clock.</summary>
      <returns>The <see langword="My.Computer.Clock" /> object for the computer.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.Devices.ServerComputer.FileSystem">
      <summary>Gets an object that provides properties and methods for working with drives, files, and directories.</summary>
      <returns>The <see langword="My.Computer.FileSystem" /> object for the computer.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.Devices.ServerComputer.Info">
      <summary>Gets an object that provides properties for getting information about the computer's memory, loaded assemblies, name, and operating system.</summary>
      <returns>The <see langword="My.Computer.Info" /> object for the computer.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.Devices.ServerComputer.Name">
      <summary>Gets the computer name.</summary>
      <returns>A <see langword="String" /> containing the name of the computer.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.Devices.ServerComputer.Network">
      <summary>Gets an object that provides a property and methods for interacting with the network to which the computer is connected.</summary>
      <returns>The <see langword="My.Computer.Network" /> object for the computer.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.Devices.ServerComputer.Registry">
      <summary>Gets an object that provides properties and methods for manipulating the registry.</summary>
      <returns>The <see langword="My.Computer.Registry" /> object for the computer.</returns>
    </member>
    <member name="T:Microsoft.VisualBasic.Logging.DiskSpaceExhaustedOption">
      <summary>Determines what to do when the <see cref="T:Microsoft.VisualBasic.Logging.FileLogTraceListener" /> object attempts to write to a log and there is less free disk space available than specified by the <see cref="P:Microsoft.VisualBasic.Logging.FileLogTraceListener.ReserveDiskSpace" /> property.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.Logging.DiskSpaceExhaustedOption.DiscardMessages">
      <summary>Discard log messages.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.Logging.DiskSpaceExhaustedOption.ThrowException">
      <summary>Throw an exception.</summary>
    </member>
    <member name="T:Microsoft.VisualBasic.Logging.FileLogTraceListener">
      <summary>Provides a simple listener that directs logging output to file.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.Logging.FileLogTraceListener.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.Logging.FileLogTraceListener" /> class with the default name.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.Logging.FileLogTraceListener.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.Logging.FileLogTraceListener" /> class with the supplied name.</summary>
      <param name="name">
        <see langword="String" />. The name of the instance object.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.Logging.FileLogTraceListener.Close">
      <summary>Closes the underlying stream for the current log file and releases any resources associated with the current stream.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.Logging.FileLogTraceListener.Dispose(System.Boolean)">
      <summary>Closes the underlying stream and optionally releases the managed resources.</summary>
      <param name="disposing">
        <see langword="True" /> releases both managed and unmanaged resources; <see langword="False" /> releases only unmanaged resources.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.Logging.FileLogTraceListener.Flush">
      <summary>Flushes the underlying stream that writes to the current log file.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.Logging.FileLogTraceListener.GetSupportedAttributes">
      <summary>Gets the custom XML configuration attributes supported by the trace listener.</summary>
      <returns>
        <see langword="String" /> array containing the XML configuration attributes recognized by this listener.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.Logging.FileLogTraceListener.TraceData(System.Diagnostics.TraceEventCache,System.String,System.Diagnostics.TraceEventType,System.Int32,System.Object)">
      <summary>Writes trace information, a data object, and event information to the output file or stream.</summary>
      <param name="eventCache">A <see cref="T:System.Diagnostics.TraceEventCache" /> object that contains the current process ID, thread ID, and stack trace information.</param>
      <param name="source">A name of the trace source that invoked this method.</param>
      <param name="eventType">One of the <see cref="T:System.Diagnostics.TraceEventType" /> enumeration values.</param>
      <param name="id">A numeric identifier for the event.</param>
      <param name="data">The trace data to emit.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.Logging.FileLogTraceListener.TraceData(System.Diagnostics.TraceEventCache,System.String,System.Diagnostics.TraceEventType,System.Int32,System.Object[])">
      <summary>Writes trace information, an array of data objects, and event information to the output file or stream.</summary>
      <param name="eventCache">A <see cref="T:System.Diagnostics.TraceEventCache" /> object that contains the current process ID, thread ID, and stack trace information.</param>
      <param name="source">The name of the trace source that invoked this method.</param>
      <param name="eventType">One of the <see cref="T:System.Diagnostics.TraceEventType" /> enumeration values.</param>
      <param name="id">A numeric identifier for the event.</param>
      <param name="data">An array of objects to emit as data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.Logging.FileLogTraceListener.TraceEvent(System.Diagnostics.TraceEventCache,System.String,System.Diagnostics.TraceEventType,System.Int32,System.String)">
      <summary>Writes trace information, a message and event information to the output file or stream.</summary>
      <param name="eventCache">A <see cref="T:System.Diagnostics.TraceEventCache" /> object that contains the current process ID, thread ID, and stack trace information.</param>
      <param name="source">A name of the trace source that invoked this method.</param>
      <param name="eventType">One of the <see cref="T:System.Diagnostics.TraceEventType" /> enumeration values.</param>
      <param name="id">A numeric identifier for the event.</param>
      <param name="message">A message to write.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.Logging.FileLogTraceListener.TraceEvent(System.Diagnostics.TraceEventCache,System.String,System.Diagnostics.TraceEventType,System.Int32,System.String,System.Object[])">
      <summary>Writes trace information, a formatted array of objects, and event information to the output file or stream.</summary>
      <param name="eventCache">A <see cref="T:System.Diagnostics.TraceEventCache" /> object that contains the current process ID, thread ID, and stack trace information.</param>
      <param name="source">A name of the trace source that invoked this method.</param>
      <param name="eventType">One of the <see cref="T:System.Diagnostics.TraceEventType" /> enumeration values.</param>
      <param name="id">A numeric identifier for the event.</param>
      <param name="format">A format string that contains zero or more format items, which correspond to objects in the <paramref name="args" /> array.</param>
      <param name="args">An <see langword="Object" /> array containing zero or more objects to format.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.Logging.FileLogTraceListener.Write(System.String)">
      <summary>Writes a verbatim message to disk, without any additional context information.</summary>
      <param name="message">
        <see langword="String" />. The custom message to write.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.Logging.FileLogTraceListener.WriteLine(System.String)">
      <summary>Writes a verbatim message to disk, followed by the current line terminator, without any additional context information.</summary>
      <param name="message">
        <see langword="String" />. The custom message to write.</param>
    </member>
    <member name="P:Microsoft.VisualBasic.Logging.FileLogTraceListener.Append">
      <summary>Determines whether to append the output to the current file or write it to a new or existing file.</summary>
      <returns>
        <see langword="Boolean" />, with <see langword="True" /> indicating that the output is appended to the current file, and <see langword="False" /> indicating that output is written to a new file. The default setting for this property is <see langword="True" />.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.Logging.FileLogTraceListener.AutoFlush">
      <summary>Indicates whether or not the writing to the log file stream flushes the buffer.</summary>
      <returns>
        <see langword="Boolean" />, with <see langword="True" /> indicating that the stream is flushed after every write; otherwise the log entries are buffered and written more efficiently. The default setting for this property is <see langword="False" />.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.Logging.FileLogTraceListener.BaseFileName">
      <summary>Gets or sets the base name for the log files, which is used to create the full log-file name.</summary>
      <returns>
        <see langword="String" />. The base name for the log files. The default is the application's product name.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.Logging.FileLogTraceListener.CustomLocation">
      <summary>Gets or sets the log file directory when the <see cref="P:Microsoft.VisualBasic.Logging.FileLogTraceListener.Location" /> property is set to <see cref="F:Microsoft.VisualBasic.Logging.LogFileLocation.Custom" />.</summary>
      <returns>
        <see langword="String" />, which is the name of the log-file directory. The default setting for this property is the user's directory for application data.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.Logging.FileLogTraceListener.Delimiter">
      <summary>Gets or sets the delimiter used to delimit fields within a log message.</summary>
      <returns>
        <see langword="String" />, which is the delimiter used for fields within a log message. The default setting for this property is the TAB character.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.Logging.FileLogTraceListener.DiskSpaceExhaustedBehavior">
      <summary>Determines what to do when writing to the log file and there is less free disk space available than specified by the <see cref="P:Microsoft.VisualBasic.Logging.FileLogTraceListener.ReserveDiskSpace" /> property.</summary>
      <returns>
        <see cref="T:Microsoft.VisualBasic.Logging.DiskSpaceExhaustedOption" />. Determines what to do when attempting to write to the log file and there is less free disk space available than specified by the <see cref="P:Microsoft.VisualBasic.Logging.FileLogTraceListener.ReserveDiskSpace" /> property, or if the log file size is greater than what the <see cref="P:Microsoft.VisualBasic.Logging.FileLogTraceListener.MaxFileSize" /> property allows. The default value is <see cref="F:Microsoft.VisualBasic.Logging.DiskSpaceExhaustedOption.DiscardMessages" />.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.Logging.FileLogTraceListener.Encoding">
      <summary>Gets or sets the encoding to use when creating a new log file.</summary>
      <returns>
        <see cref="T:System.Text.Encoding" />, which is the encoding to use when creating a new log file. The default value of this property is <see cref="T:System.Text.UTF8Encoding" />.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.Logging.FileLogTraceListener.FullLogFileName">
      <summary>Gets the current full log-file name.</summary>
      <returns>
        <see langword="String" />, which is the current full log-file name.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.Logging.FileLogTraceListener.IncludeHostName">
      <summary>Indicates whether or not the host name of the logging machine should be included in the output.</summary>
      <returns>
        <see langword="Boolean" />. Use <see langword="True" /> if the host identifier should be included; otherwise use <see langword="False" />. The default value is <see langword="False" />.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.Logging.FileLogTraceListener.Location">
      <summary>Gets or sets location for the log files.</summary>
      <returns>
        <see cref="T:Microsoft.VisualBasic.Logging.LogFileLocation" />, which is the location for the log file. The default value is <see cref="F:Microsoft.VisualBasic.Logging.LogFileLocation.LocalUserApplicationDirectory" />.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.Logging.FileLogTraceListener.LogFileCreationSchedule">
      <summary>Determines which date to include in the names of the log files.</summary>
      <returns>
        <see cref="T:Microsoft.VisualBasic.Logging.LogFileCreationScheduleOption" />. This indicates which date to include in the log-file names. The default value is <see cref="F:Microsoft.VisualBasic.Logging.LogFileCreationScheduleOption.None" />.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.Logging.FileLogTraceListener.MaxFileSize">
      <summary>Gets or sets the maximum allowed size of the log file, in bytes.</summary>
      <exception cref="T:System.ArgumentException">When this property is set to a value less than 1000.</exception>
      <returns>
        <see langword="Long" />. This is the maximum allowed log-file size, in bytes. The default value is 5000000.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.Logging.FileLogTraceListener.ReserveDiskSpace">
      <summary>Gets or sets the amount of free disk space, in bytes, necessary before messages can be written to the log file.</summary>
      <exception cref="T:System.ArgumentException">When this property is set to a value less than 0.</exception>
      <returns>
        <see langword="Long" />. This is the amount of free disk space necessary. The default value is 10000000.</returns>
    </member>
    <member name="T:Microsoft.VisualBasic.Logging.Log">
      <summary>Provides a property and methods for writing event and exception information to the application's log listeners.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.Logging.Log.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.Logging.Log" /> class.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.Logging.Log.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.Logging.Log" /> class.</summary>
      <param name="name">
        <see cref="T:System.String" />. The name to give to the <see cref="P:Microsoft.VisualBasic.Logging.Log.TraceSource" /> property object.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.Logging.Log.InitializeWithDefaultsSinceNoConfigExists">
      <summary>Creates a new <see cref="T:Microsoft.VisualBasic.Logging.FileLogTraceListener" /> object and adds it to the <see cref="P:System.Diagnostics.TraceSource.Listeners" /> collection.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.Logging.Log.WriteEntry(System.String)">
      <summary>Writes a message to the application's log listeners.</summary>
      <param name="message">Required. The message to log. If <paramref name="message" /> is <see langword="Nothing" />, an empty string is used.</param>
      <exception cref="T:System.Security.SecurityException">Code with partial trust calls the method, but writes to an event log listener that requires full trust.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Logging.Log.WriteEntry(System.String,System.Diagnostics.TraceEventType)">
      <summary>Writes a message to the application's log listeners.</summary>
      <param name="message">Required. The message to log. If <paramref name="message" /> is <see langword="Nothing" />, an empty string is used.</param>
      <param name="severity">The type of message. By default, <see langword="TraceEventType.Information" />.</param>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">The message type is not one of the <see cref="T:System.Diagnostics.TraceEventType" /> enumeration values.</exception>
      <exception cref="T:System.Security.SecurityException">Code with partial trust calls the method, but writes to an event log listener that requires full trust.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Logging.Log.WriteEntry(System.String,System.Diagnostics.TraceEventType,System.Int32)">
      <summary>Writes a message to the application's log listeners.</summary>
      <param name="message">Required. The message to log. If <paramref name="message" /> is <see langword="Nothing" />, an empty string is used.</param>
      <param name="severity">The type of message. By default, <see langword="TraceEventType.Information" />.</param>
      <param name="id">Message identifier, typically used for correlation. By default, related to <c>entryType</c> as described in the table.</param>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">The message type is not one of the <see cref="T:System.Diagnostics.TraceEventType" /> enumeration values.</exception>
      <exception cref="T:System.Security.SecurityException">Code with partial trust calls the method, but writes to an event log listener that requires full trust.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Logging.Log.WriteException(System.Exception)">
      <summary>Writes exception information to the application's log listeners.</summary>
      <param name="ex">Required. Exception to log.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="ex" /> is <see langword="Nothing" />.</exception>
      <exception cref="T:System.Security.SecurityException">Code with partial trust calls the method, but writes to an event log listener that requires full trust.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Logging.Log.WriteException(System.Exception,System.Diagnostics.TraceEventType,System.String)">
      <summary>Writes exception information to the application's log listeners.</summary>
      <param name="ex">Required. Exception to log.</param>
      <param name="severity">The type of message. By default, <see cref="F:System.Diagnostics.TraceEventType.Error" />.</param>
      <param name="additionalInfo">String to append to the message. By default, this is an empty string.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="ex" /> is <see langword="Nothing" />.</exception>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">The message type is not one of the <see cref="T:System.Diagnostics.TraceEventType" /> enumeration values.</exception>
      <exception cref="T:System.Security.SecurityException">Code with partial trust calls the method, but writes to an event log listener that requires full trust.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Logging.Log.WriteException(System.Exception,System.Diagnostics.TraceEventType,System.String,System.Int32)">
      <summary>Writes exception information to the application's log listeners.</summary>
      <param name="ex">Required. Exception to log.</param>
      <param name="severity">The type of message. By default, <see cref="F:System.Diagnostics.TraceEventType.Error" />.</param>
      <param name="additionalInfo">String to append to the message. By default, this is an empty string.</param>
      <param name="id">Message identifier, typically used for correlation. By default, related to <c>entryType</c> as described in the table in the Remarks section.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="ex" /> is <see langword="Nothing" />.</exception>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">The message type is not one of the <see cref="T:System.Diagnostics.TraceEventType" /> enumeration values.</exception>
      <exception cref="T:System.Security.SecurityException">Code with partial trust calls the method, but writes to an event log listener that requires full trust.</exception>
    </member>
    <member name="P:Microsoft.VisualBasic.Logging.Log.DefaultFileLogWriter">
      <summary>Gets the file the <see cref="T:Microsoft.VisualBasic.Logging.FileLogTraceListener" /> object that underlies the <see langword="Log" /> object.</summary>
      <returns>The <see cref="T:Microsoft.VisualBasic.Logging.FileLogTraceListener" /> object that underlies the <see langword="Log" /> object.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.Logging.Log.TraceSource">
      <summary>Gets to the <see cref="T:System.Diagnostics.TraceSource" /> object that underlies the <see langword="Log" /> object.</summary>
      <returns>The <see cref="T:System.Diagnostics.TraceSource" /> object that underlies the <see langword="Log" /> object.</returns>
    </member>
    <member name="T:Microsoft.VisualBasic.Logging.LogFileCreationScheduleOption">
      <summary>Determines which date to include in the names of the <see cref="T:Microsoft.VisualBasic.Logging.FileLogTraceListener" /> class log files.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.Logging.LogFileCreationScheduleOption.Daily">
      <summary>Include the current date in the log file name.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.Logging.LogFileCreationScheduleOption.None">
      <summary>Do not include the date in the log file name.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.Logging.LogFileCreationScheduleOption.Weekly">
      <summary>Include the first day of the current week in the log file name.</summary>
    </member>
    <member name="T:Microsoft.VisualBasic.Logging.LogFileLocation">
      <summary>Determines which predefined path the <see cref="T:Microsoft.VisualBasic.Logging.FileLogTraceListener" /> class uses to write its log files.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.Logging.LogFileLocation.CommonApplicationDirectory">
      <summary>Use the path for the application data that is shared among all users.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.Logging.LogFileLocation.Custom">
      <summary>If the string specified by <see cref="P:Microsoft.VisualBasic.Logging.FileLogTraceListener.CustomLocation" /> is not empty, then use it as the path. Otherwise, use the path for a user's application data.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.Logging.LogFileLocation.ExecutableDirectory">
      <summary>Use the path for the executable file that started the application.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.Logging.LogFileLocation.LocalUserApplicationDirectory">
      <summary>Use the path for a user's application data.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.Logging.LogFileLocation.TempDirectory">
      <summary>Use the path of the current system's temporary folder.</summary>
    </member>
    <member name="T:Microsoft.VisualBasic.MyServices.ClipboardProxy">
      <summary>Provides methods for manipulating the Clipboard.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.ClipboardProxy.Clear">
      <summary>Clears the Clipboard.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.ClipboardProxy.ContainsAudio">
      <summary>Indicates whether the Clipboard contains audio data.</summary>
      <returns>
        <see langword="True" /> if audio data is stored on the Clipboard; otherwise <see langword="False" />.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.ClipboardProxy.ContainsData(System.String)">
      <summary>Indicates whether the Clipboard contains data in the specified custom format.</summary>
      <param name="format">
        <see langword="String" />. Name of the custom format to be checked. Required.</param>
      <returns>
        <see langword="True" /> if data in the specified custom format is stored on the Clipboard; otherwise <see langword="False" />.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.ClipboardProxy.ContainsFileDropList">
      <summary>Returns a <see langword="Boolean" /> indicating whether the Clipboard contains a file drop list.</summary>
      <returns>
        <see langword="True" /> if a file drop list is stored on the Clipboard; otherwise <see langword="False" />.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.ClipboardProxy.ContainsImage">
      <summary>Returns a <see langword="Boolean" /> indicating whether an image is stored on the Clipboard.</summary>
      <returns>
        <see langword="True" /> if an image is stored on the Clipboard; otherwise <see langword="False" />.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.ClipboardProxy.ContainsText">
      <summary>Determines if there is text on the Clipboard.</summary>
      <returns>
        <see langword="True" /> if the Clipboard contains text; otherwise <see langword="False" />.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.ClipboardProxy.ContainsText(System.Windows.Forms.TextDataFormat)">
      <summary>Determines if there is text on the Clipboard.</summary>
      <param name="format">
        <see cref="T:System.Windows.Forms.TextDataFormat" />. If specified, identifies what text format to be checked for. Required.</param>
      <returns>
        <see langword="True" /> if the Clipboard contains text; otherwise <see langword="False" />.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.ClipboardProxy.GetAudioStream">
      <summary>Retrieves an audio stream from the Clipboard.</summary>
      <returns>A <see cref="T:System.IO.Stream" /> object containing audio data or <see langword="Nothing" /> if the Clipboard does not contain any audio data.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.ClipboardProxy.GetData(System.String)">
      <summary>Retrieves data in a custom format from the Clipboard.</summary>
      <param name="format">
        <see langword="String" />. Name of the data format. Required.</param>
      <returns>An <see langword="Object" /> representing the Clipboard data or <see langword="Nothing" /> if the Clipboard does not contain any data that is in the specified format or can be converted to that format.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.ClipboardProxy.GetDataObject">
      <summary>Retrieves data from the Clipboard as an <see cref="T:System.Windows.Forms.IDataObject" />.</summary>
      <returns>An <see cref="T:System.Windows.Forms.IDataObject" /> object that represents the data currently on the Clipboard, or <see langword="Nothing" /> if there is no data on the Clipboard.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.ClipboardProxy.GetFileDropList">
      <summary>Retrieves a collection of strings representing file names from the Clipboard.</summary>
      <returns>A <see cref="T:System.Collections.Specialized.StringCollection" /> containing file names or <see langword="Nothing" /> if the Clipboard does not contain any data that is in the <see cref="F:System.Windows.Forms.DataFormats.FileDrop" /> format or can be converted to that format.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.ClipboardProxy.GetImage">
      <summary>Retrieves an image from the Clipboard.</summary>
      <returns>An <see cref="T:System.Drawing.Image" /> representing the Clipboard image data or <see langword="Nothing" /> if the Clipboard does not contain any data that is in the <see cref="F:System.Windows.Forms.DataFormats.Bitmap" /> format or can be converted to that format.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.ClipboardProxy.GetText">
      <summary>Retrieves text from the Clipboard.</summary>
      <returns>The Clipboard text data or an empty string if the Clipboard does not contain data in the <see cref="F:System.Windows.Forms.DataFormats.Text" /> or <see cref="F:System.Windows.Forms.TextDataFormat.UnicodeText" /> format, depending on the operating system.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.ClipboardProxy.GetText(System.Windows.Forms.TextDataFormat)">
      <summary>Retrieves text from the Clipboard.</summary>
      <param name="format">
        <see cref="T:System.Windows.Forms.TextDataFormat" />. If specified, identifies what text format should be retrieved. Default is <see cref="F:System.Windows.Forms.TextDataFormat.CommaSeparatedValue" />. Required.</param>
      <returns>The Clipboard text data or an empty string if the Clipboard does not contain data in the specified format.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.ClipboardProxy.SetAudio(System.Byte[])">
      <summary>Writes audio data to the Clipboard.</summary>
      <param name="audioBytes">
        <see langword="Byte" /> array. Audio data to be written to the Clipboard. Required.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.ClipboardProxy.SetAudio(System.IO.Stream)">
      <summary>Writes audio data to the Clipboard.</summary>
      <param name="audioStream">
        <see cref="T:System.IO.Stream" /> Audio data to be written to the clipboard. Required.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.ClipboardProxy.SetData(System.String,System.Object)">
      <summary>Writes data in a custom format to the Clipboard.</summary>
      <param name="format">
        <see langword="String" />. Format of data. Required.</param>
      <param name="data">
        <see langword="Object" />. Data object to be written to the Clipboard. Required.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.ClipboardProxy.SetDataObject(System.Windows.Forms.DataObject)">
      <summary>Writes a <see cref="T:System.Windows.Forms.DataObject" /> to the Clipboard.</summary>
      <param name="data">
        <see cref="T:System.Windows.Forms.DataObject" />. Data object to be written to the Clipboard. Required.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.ClipboardProxy.SetFileDropList(System.Collections.Specialized.StringCollection)">
      <summary>Writes a collection of strings representing file paths to the Clipboard.</summary>
      <param name="filePaths">
        <see cref="T:System.Collections.Specialized.StringCollection" />. List of file names. Required.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.ClipboardProxy.SetImage(System.Drawing.Image)">
      <summary>Writes an image to the Clipboard.</summary>
      <param name="image">
        <see cref="T:System.Drawing.Image" />. Image to be written. Required.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.ClipboardProxy.SetText(System.String)">
      <summary>Writes text to the Clipboard.</summary>
      <param name="text">
        <see langword="String" />. Text to be written. Required.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="text" /> is an empty string.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="text" /> is <see langword="Nothing" />.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.ClipboardProxy.SetText(System.String,System.Windows.Forms.TextDataFormat)">
      <summary>Writes text to the Clipboard.</summary>
      <param name="text">
        <see langword="String" />. Text to be written. Required.</param>
      <param name="format">
        <see cref="T:System.Windows.Forms.TextDataFormat" />. Format to be used when writing text. Default is <see cref="F:System.Windows.Forms.TextDataFormat.UnicodeText" />. Required.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="text" /> is an empty string.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="text" /> is <see langword="Nothing" />.</exception>
    </member>
    <member name="T:Microsoft.VisualBasic.MyServices.FileSystemProxy">
      <summary>Provides properties and methods for working with drives, files, and directories.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.CombinePath(System.String,System.String)">
      <summary>Combines two paths and returns a properly formatted path.</summary>
      <param name="baseDirectory">
        <see langword="String" />. First path to be combined.</param>
      <param name="relativePath">
        <see langword="String" />. Second path to be combined.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="baseDirectory" /> or <paramref name="relativePath" /> are malformed paths.</exception>
      <returns>The combination of the specified paths.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.CopyDirectory(System.String,System.String)">
      <summary>Copies a directory to another directory.</summary>
      <param name="sourceDirectoryName">The directory to be copied.</param>
      <param name="destinationDirectoryName">The location to which the directory should be copied.</param>
      <exception cref="T:System.ArgumentException">The path is not valid for one of the following reasons: it is a zero-length string; it contains only white space; it contains invalid characters; or it is a device path (starts with \\.\).</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destinationDirectoryName" /> or <paramref name="sourceDirectoryName" /> is <see langword="Nothing" /> or an empty string.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The source directory does not exist.</exception>
      <exception cref="T:System.IO.IOException">The source path and target path are the same.</exception>
      <exception cref="T:System.InvalidOperationException">The operation is cyclic.</exception>
      <exception cref="T:System.IO.PathTooLongException">The path exceeds the system-defined maximum length.</exception>
      <exception cref="T:System.NotSupportedException">A folder name in the path contains a colon (:) or is in an invalid format.</exception>
      <exception cref="T:System.Security.SecurityException">The user lacks necessary permissions to view the path.</exception>
      <exception cref="T:System.UnauthorizedAccessException">A destination file exists but cannot be accessed.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.CopyDirectory(System.String,System.String,Microsoft.VisualBasic.FileIO.UIOption)">
      <summary>Copies a directory to another directory.</summary>
      <param name="sourceDirectoryName">The directory to be copied.</param>
      <param name="destinationDirectoryName">The location to which the directory should be copied.</param>
      <param name="showUI">Whether to visually track the operation's progress. Default is <see langword="UIOption.OnlyErrorDialogs" />.</param>
      <exception cref="T:System.ArgumentException">The path is not valid for one of the following reasons: it is a zero-length string; it contains only white space; it contains invalid characters; or it is a device path (starts with \\.\).</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destinationDirectoryName" /> or <paramref name="sourceDirectoryName" /> is <see langword="Nothing" /> or an empty string.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The source directory does not exist.</exception>
      <exception cref="T:System.IO.IOException">The source path and target path are the same.</exception>
      <exception cref="T:System.InvalidOperationException">The operation is cyclic.</exception>
      <exception cref="T:System.IO.PathTooLongException">The path exceeds the system-defined maximum length.</exception>
      <exception cref="T:System.NotSupportedException">A folder name in the path contains a colon (:) or is in an invalid format.</exception>
      <exception cref="T:System.Security.SecurityException">The user lacks necessary permissions to view the path.</exception>
      <exception cref="T:System.UnauthorizedAccessException">A destination file exists but cannot be accessed.</exception>
      <exception cref="T:System.OperationCanceledException">
        <paramref name="ShowUI" /> is set to <see langword="UIOption.AllDialogs" /> and the user cancels the operation, or one or more files in the directory cannot be copied.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.CopyDirectory(System.String,System.String,Microsoft.VisualBasic.FileIO.UIOption,Microsoft.VisualBasic.FileIO.UICancelOption)">
      <summary>Copies a directory to another directory.</summary>
      <param name="sourceDirectoryName">The directory to be copied.</param>
      <param name="destinationDirectoryName">The location to which the directory should be copied.</param>
      <param name="showUI">Whether to visually track the operation's progress. Default is <see langword="UIOption.OnlyErrorDialogs" />.</param>
      <param name="onUserCancel">Specifies what should be done if the user clicks Cancel during the operation. Default is <see cref="F:Microsoft.VisualBasic.FileIO.UICancelOption.ThrowException" />.</param>
      <exception cref="T:System.ArgumentException">The path is not valid for one of the following reasons: it is a zero-length string; it contains only white space; it contains invalid characters; or it is a device path (starts with \\.\).</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destinationDirectoryName" /> or <paramref name="sourceDirectoryName" /> is <see langword="Nothing" /> or an empty string.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The source directory does not exist.</exception>
      <exception cref="T:System.IO.IOException">The source path and target path are the same.</exception>
      <exception cref="T:System.InvalidOperationException">The operation is cyclic.</exception>
      <exception cref="T:System.IO.PathTooLongException">The path exceeds the system-defined maximum length.</exception>
      <exception cref="T:System.NotSupportedException">A folder name in the path contains a colon (:) or is in an invalid format.</exception>
      <exception cref="T:System.Security.SecurityException">The user lacks necessary permissions to view the path.</exception>
      <exception cref="T:System.UnauthorizedAccessException">A destination file exists but cannot be accessed.</exception>
      <exception cref="T:System.OperationCanceledException">
        <paramref name="ShowUI" /> is set to <see langword="UIOption.AllDialogs" /> and the user cancels the operation, or one or more files in the directory cannot be copied.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.CopyDirectory(System.String,System.String,System.Boolean)">
      <summary>Copies a directory to another directory.</summary>
      <param name="sourceDirectoryName">The directory to be copied.</param>
      <param name="destinationDirectoryName">The location to which the directory should be copied.</param>
      <param name="overwrite">
        <see langword="True" /> to overwrite existing files; otherwise <see langword="False" />. Default is <see langword="False" />.</param>
      <exception cref="T:System.ArgumentException">The path is not valid for one of the following reasons: it is a zero-length string; it contains only white space; it contains invalid characters; or it is a device path (starts with \\.\).</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destinationDirectoryName" /> or <paramref name="sourceDirectoryName" /> is <see langword="Nothing" /> or an empty string.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The source directory does not exist.</exception>
      <exception cref="T:System.IO.IOException">The source path and target path are the same.</exception>
      <exception cref="T:System.InvalidOperationException">The operation is cyclic.</exception>
      <exception cref="T:System.IO.PathTooLongException">The path exceeds the system-defined maximum length.</exception>
      <exception cref="T:System.NotSupportedException">A folder name in the path contains a colon (:) or is in an invalid format.</exception>
      <exception cref="T:System.Security.SecurityException">The user lacks necessary permissions to view the path.</exception>
      <exception cref="T:System.UnauthorizedAccessException">A destination file exists but cannot be accessed.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.CopyFile(System.String,System.String)">
      <summary>Copies a file to a new location.</summary>
      <param name="sourceFileName">The file to be copied.</param>
      <param name="destinationFileName">The location to which the file should be copied.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destinationFileName" /> contains path information.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destinationFileName" /> or <paramref name="sourceFileName" /> is <see langword="Nothing" /> or an empty string.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The source file is not valid or does not exist.</exception>
      <exception cref="T:System.IO.IOException">A file in the target directory with the same name is in use.</exception>
      <exception cref="T:System.NotSupportedException">A file or directory name in the path contains a colon (:) or is in an invalid format.</exception>
      <exception cref="T:System.IO.PathTooLongException">The path exceeds the system-defined maximum length.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The user does not have required permission.</exception>
      <exception cref="T:System.Security.SecurityException">The user lacks necessary permissions to view the path.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.CopyFile(System.String,System.String,Microsoft.VisualBasic.FileIO.UIOption)">
      <summary>Copies a file to a new location.</summary>
      <param name="sourceFileName">The file to be copied.</param>
      <param name="destinationFileName">The location to which the file should be copied.</param>
      <param name="showUI">Whether to visually track the operation's progress. Default is <see langword="UIOption.OnlyErrorDialogs" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destinationFileName" /> contains path information.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destinationFileName" /> or <paramref name="sourceFileName" /> is <see langword="Nothing" /> or an empty string.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The source file is not valid or does not exist.</exception>
      <exception cref="T:System.IO.IOException">The destination file exists and <paramref name="overwrite" /> is set to <see langword="False" />.</exception>
      <exception cref="T:System.NotSupportedException">A file or directory name in the path contains a colon (:) or is in an invalid format.</exception>
      <exception cref="T:System.IO.PathTooLongException">The path exceeds the system-defined maximum length.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The user does not have required permission.</exception>
      <exception cref="T:System.Security.SecurityException">The user lacks necessary permissions to view the path.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.CopyFile(System.String,System.String,Microsoft.VisualBasic.FileIO.UIOption,Microsoft.VisualBasic.FileIO.UICancelOption)">
      <summary>Copies a file to a new location.</summary>
      <param name="sourceFileName">The file to be copied.</param>
      <param name="destinationFileName">The location to which the file should be copied.</param>
      <param name="showUI">Whether to visually track the operation's progress. Default is <see langword="UIOption.OnlyErrorDialogs" />.</param>
      <param name="onUserCancel">Specifies what should be done if the user clicks Cancel during the operation. Default is <see cref="F:Microsoft.VisualBasic.FileIO.UICancelOption.ThrowException" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destinationFileName" /> contains path information.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destinationFileName" /> or <paramref name="sourceFileName" /> is <see langword="Nothing" /> or an empty string.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The source file is not valid or does not exist.</exception>
      <exception cref="T:System.IO.IOException">The destination file exists and <paramref name="overwrite" /> is set to <see langword="False" />.</exception>
      <exception cref="T:System.NotSupportedException">A file or directory name in the path contains a colon (:) or is in an invalid format.</exception>
      <exception cref="T:System.IO.PathTooLongException">The path exceeds the system-defined maximum length.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The user does not have required permission.</exception>
      <exception cref="T:System.Security.SecurityException">The user lacks necessary permissions to view the path.</exception>
      <exception cref="T:System.OperationCanceledException">
        <paramref name="UICancelOption" /> is set to <see langword="ThrowException" />, and the user has canceled the operation or an unspecified I/O error occurs.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.CopyFile(System.String,System.String,System.Boolean)">
      <summary>Copies a file to a new location.</summary>
      <param name="sourceFileName">The file to be copied.</param>
      <param name="destinationFileName">The location to which the file should be copied.</param>
      <param name="overwrite">
        <see langword="True" /> if existing files should be overwritten; otherwise <see langword="False" />. Default is <see langword="False" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destinationFileName" /> contains path information.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destinationFileName" /> or <paramref name="sourceFileName" /> is <see langword="Nothing" /> or an empty string.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The source file is not valid or does not exist.</exception>
      <exception cref="T:System.IO.IOException">The destination file exists and <paramref name="overwrite" /> is set to <see langword="False" />.</exception>
      <exception cref="T:System.NotSupportedException">A file or directory name in the path contains a colon (:) or is in an invalid format.</exception>
      <exception cref="T:System.IO.PathTooLongException">The path exceeds the system-defined maximum length.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The user does not have required permission.</exception>
      <exception cref="T:System.Security.SecurityException">The user lacks necessary permissions to view the path.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.CreateDirectory(System.String)">
      <summary>Creates a directory.</summary>
      <param name="directory">Name and location of the directory.</param>
      <exception cref="T:System.ArgumentException">The directory name is malformed. For example, it contains illegal characters or is only white space.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="directory" /> is <see langword="Nothing" /> or an empty string.</exception>
      <exception cref="T:System.IO.PathTooLongException">The directory name is too long.</exception>
      <exception cref="T:System.NotSupportedException">The directory name is only a colon (:).</exception>
      <exception cref="T:System.IO.IOException">The parent directory of the directory to be created is read-only.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The user does not have permission to create the directory.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.DeleteDirectory(System.String,Microsoft.VisualBasic.FileIO.DeleteDirectoryOption)">
      <summary>Deletes a directory.</summary>
      <param name="directory">Directory to be deleted.</param>
      <param name="onDirectoryNotEmpty">Specifies what should be done when a directory that is to be deleted contains files or directories. Default is <see langword="DeleteDirectoryOption.DeleteAllContents" />.</param>
      <exception cref="T:System.ArgumentException">The path is a zero-length string, is malformed, contains only white space, or contains invalid characters (including wildcard characters). The path is a device path (starts with \\.\).</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="directory" /> is <see langword="Nothing" /> or an empty string.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The directory does not exist or is a file.</exception>
      <exception cref="T:System.IO.IOException">A file in the directory or subdirectory is in use.</exception>
      <exception cref="T:System.NotSupportedException">The directory name contains a colon (:).</exception>
      <exception cref="T:System.IO.PathTooLongException">The path exceeds the system-defined maximum length.</exception>
      <exception cref="T:System.Security.SecurityException">The user does not have required permissions.</exception>
      <exception cref="T:System.OperationCanceledException">The user cancels the operation or the directory cannot be deleted.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.DeleteDirectory(System.String,Microsoft.VisualBasic.FileIO.UIOption,Microsoft.VisualBasic.FileIO.RecycleOption)">
      <summary>Deletes a directory.</summary>
      <param name="directory">Directory to be deleted.</param>
      <param name="showUI">Specifies whether to visually track the operation's progress. Default is <see langword="UIOption.OnlyErrorDialogs" />.</param>
      <param name="recycle">Specifies whether or not the deleted file should be sent to the Recycle Bin. Default is <see langword="RecycleOption.DeletePermanently" />.</param>
      <exception cref="T:System.ArgumentException">The path is a zero-length string, is malformed, contains only white space, or contains invalid characters (including wildcard characters). The path is a device path (starts with \\.\).</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="directory" /> is <see langword="Nothing" /> or an empty string.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The directory does not exist or is a file.</exception>
      <exception cref="T:System.IO.IOException">A file in the directory or subdirectory is in use.</exception>
      <exception cref="T:System.NotSupportedException">The directory name contains a colon (:).</exception>
      <exception cref="T:System.IO.PathTooLongException">The path exceeds the system-defined maximum length.</exception>
      <exception cref="T:System.Security.SecurityException">The user does not have required permissions.</exception>
      <exception cref="T:System.OperationCanceledException">The user cancels the operation or the directory cannot be deleted.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.DeleteDirectory(System.String,Microsoft.VisualBasic.FileIO.UIOption,Microsoft.VisualBasic.FileIO.RecycleOption,Microsoft.VisualBasic.FileIO.UICancelOption)">
      <summary>Deletes a directory.</summary>
      <param name="directory">Directory to be deleted.</param>
      <param name="showUI">Specifies whether to visually track the operation's progress. Default is <see langword="UIOption.OnlyErrorDialogs" />.</param>
      <param name="recycle">Specifies whether or not the deleted file should be sent to the Recycle Bin. Default is <see langword="RecycleOption.DeletePermanently" />.</param>
      <param name="onUserCancel">Specifies whether to throw an exception if the user clicks Cancel.</param>
      <exception cref="T:System.ArgumentException">The path is a zero-length string, is malformed, contains only white space, or contains invalid characters (including wildcard characters). The path is a device path (starts with \\.\).</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="directory" /> is <see langword="Nothing" /> or an empty string.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The directory does not exist or is a file.</exception>
      <exception cref="T:System.IO.IOException">A file in the directory or subdirectory is in use.</exception>
      <exception cref="T:System.NotSupportedException">The directory name contains a colon (:).</exception>
      <exception cref="T:System.IO.PathTooLongException">The path exceeds the system-defined maximum length.</exception>
      <exception cref="T:System.Security.SecurityException">The user does not have required permissions.</exception>
      <exception cref="T:System.OperationCanceledException">The user cancels the operation or the directory cannot be deleted.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.DeleteFile(System.String)">
      <summary>Deletes a file.</summary>
      <param name="file">Name and path of the file to be deleted.</param>
      <exception cref="T:System.ArgumentException">The path is not valid for one of the following reasons: it is a zero-length string; it contains only white space; it contains invalid characters; it has a trailing slash where a file must be specified; or it is a device path (starts with \\.\).</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="file" /> is <see langword="Nothing" /> or an empty string.</exception>
      <exception cref="T:System.IO.PathTooLongException">The path exceeds the system-defined maximum length.</exception>
      <exception cref="T:System.NotSupportedException">A file or directory name in the path contains a colon (:) or is in an invalid format.</exception>
      <exception cref="T:System.IO.IOException">The file is in use.</exception>
      <exception cref="T:System.Security.SecurityException">The user lacks necessary permissions to view the path.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file does not exist.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The user does not have permission to delete the file or the file is read-only.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.DeleteFile(System.String,Microsoft.VisualBasic.FileIO.UIOption,Microsoft.VisualBasic.FileIO.RecycleOption)">
      <summary>Deletes a file.</summary>
      <param name="file">Name and path of the file to be deleted.</param>
      <param name="showUI">Whether to visually track the operation's progress. Default is <see langword="UIOption.OnlyErrorDialogs" />.</param>
      <param name="recycle">Whether or not the deleted file should be sent to the Recycle Bin. Default is <see langword="RecycleOption.DeletePermanently" />.</param>
      <exception cref="T:System.ArgumentException">The path is not valid for one of the following reasons: it is a zero-length string; it contains only white space; it contains invalid characters; it has a trailing slash where a file must be specified; or it is a device path (starts with \\.\).</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="file" /> is <see langword="Nothing" /> or an empty string.</exception>
      <exception cref="T:System.IO.PathTooLongException">The path exceeds the system-defined maximum length.</exception>
      <exception cref="T:System.NotSupportedException">A file or directory name in the path contains a colon (:) or is in an invalid format.</exception>
      <exception cref="T:System.IO.IOException">The file is in use.</exception>
      <exception cref="T:System.Security.SecurityException">The user lacks necessary permissions to view the path.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file does not exist.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The user does not have permission to delete the file or the file is read-only.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.DeleteFile(System.String,Microsoft.VisualBasic.FileIO.UIOption,Microsoft.VisualBasic.FileIO.RecycleOption,Microsoft.VisualBasic.FileIO.UICancelOption)">
      <summary>Deletes a file.</summary>
      <param name="file">Name and path of the file to be deleted.</param>
      <param name="showUI">Whether to visually track the operation's progress. Default is <see langword="UIOption.OnlyErrorDialogs" />.</param>
      <param name="recycle">Whether or not the deleted file should be sent to the Recycle Bin. Default is <see langword="RecycleOption.DeletePermanently" />.</param>
      <param name="onUserCancel">Specifies whether or not an exception is thrown when the user cancels the operation. Default is <see langword="UICancelOption.ThrowException" />.</param>
      <exception cref="T:System.ArgumentException">The path is not valid for one of the following reasons: it is a zero-length string; it contains only white space; it contains invalid characters; it has a trailing slash where a file must be specified; or it is a device path (starts with \\.\).</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="file" /> is <see langword="Nothing" /> or an empty string.</exception>
      <exception cref="T:System.IO.PathTooLongException">The path exceeds the system-defined maximum length.</exception>
      <exception cref="T:System.NotSupportedException">A file or directory name in the path contains a colon (:) or is in an invalid format.</exception>
      <exception cref="T:System.IO.IOException">The file is in use.</exception>
      <exception cref="T:System.Security.SecurityException">The user lacks necessary permissions to view the path.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file does not exist.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The user does not have permission to delete the file or the file is read-only.</exception>
      <exception cref="T:System.OperationCanceledException">The user cancelled the operation and <paramref name="onUserCancel" /> is set to <see cref="F:Microsoft.VisualBasic.FileIO.UICancelOption.ThrowException" />.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.DirectoryExists(System.String)">
      <summary>Returns <see langword="True" /> if the specified directory exists.</summary>
      <param name="directory">Path of the directory.</param>
      <returns>
        <see langword="True" /> if the directory exists; otherwise <see langword="False" />.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.FileExists(System.String)">
      <summary>Returns <see langword="True" /> if the specified file exists.</summary>
      <param name="file">Name and path of the file.</param>
      <exception cref="T:System.ArgumentException">The name of the file ends with a backslash (\).</exception>
      <returns>Returns <see langword="True" /> if the file exists; otherwise this method returns <see langword="False" />.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.FindInFiles(System.String,System.String,System.Boolean,Microsoft.VisualBasic.FileIO.SearchOption)">
      <summary>Returns a read-only collection of strings representing the names of files containing the specified text.</summary>
      <param name="directory">The directory to be searched.</param>
      <param name="containsText">The search text.</param>
      <param name="ignoreCase">
        <see langword="True" /> if the search should be case-sensitive; otherwise <see langword="False" />. Default is <see langword="True" />.</param>
      <param name="searchType">Whether to include subfolders. Default is <see langword="SearchOption.SearchTopLevelOnly" />.</param>
      <exception cref="T:System.ArgumentException">The path is not valid for one of the following reasons: it is a zero-length string; it contains only white space; it contains invalid characters; or it is a device path (starts with <c>\\.\</c>).</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="directory" /> is <see langword="Nothing" /> or an empty string.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified directory does not exist.</exception>
      <exception cref="T:System.IO.IOException">The specified directory points to an existing file.</exception>
      <exception cref="T:System.IO.PathTooLongException">The path exceeds the system-defined maximum length.</exception>
      <exception cref="T:System.NotSupportedException">The specified directory path contains a colon (:) or is in an invalid format.</exception>
      <exception cref="T:System.Security.SecurityException">The user lacks necessary permissions to view the path.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The user lacks necessary permissions.</exception>
      <returns>Read-only collection of the names of files containing the specified text.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.FindInFiles(System.String,System.String,System.Boolean,Microsoft.VisualBasic.FileIO.SearchOption,System.String[])">
      <summary>Returns a read-only collection of strings representing the names of files containing the specified text.</summary>
      <param name="directory">The directory to be searched.</param>
      <param name="containsText">The search text.</param>
      <param name="ignoreCase">
        <see langword="True" /> if the search should be case-sensitive; otherwise <see langword="False" />. Default is <see langword="True" />.</param>
      <param name="searchType">Whether to include subfolders. Default is <see langword="SearchOption.SearchTopLevelOnly" />.</param>
      <param name="fileWildcards">Pattern to be matched.</param>
      <exception cref="T:System.ArgumentException">The path is not valid for one of the following reasons: it is a zero-length string; it contains only white space; it contains invalid characters; or it is a device path (starts with <c>\\.\</c>).</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="directory" /> is <see langword="Nothing" /> or an empty string.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified directory does not exist.</exception>
      <exception cref="T:System.IO.IOException">The specified directory points to an existing file.</exception>
      <exception cref="T:System.IO.PathTooLongException">The path exceeds the system-defined maximum length.</exception>
      <exception cref="T:System.NotSupportedException">The specified directory path contains a colon (:) or is in an invalid format.</exception>
      <exception cref="T:System.Security.SecurityException">The user lacks necessary permissions to view the path.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The user lacks necessary permissions.</exception>
      <returns>Read-only collection of the names of files containing the specified text.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.GetDirectories(System.String)">
      <summary>Returns a collection of strings representing the path names of subdirectories within a directory.</summary>
      <param name="directory">Name and path of directory.</param>
      <exception cref="T:System.ArgumentException">The path is not valid for one of the following reasons: it is a zero-length string; it contains only white space; it contains invalid characters; or it is a device path (starts with \\.\).</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="directory" /> is <see langword="Nothing" /> or an empty string.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified directory does not exist.</exception>
      <exception cref="T:System.IO.IOException">The specified directory points to an existing file.</exception>
      <exception cref="T:System.IO.PathTooLongException">The path exceeds the system-defined maximum length.</exception>
      <exception cref="T:System.NotSupportedException">A file or directory name in the path contains a colon (:) or is in an invalid format.</exception>
      <exception cref="T:System.Security.SecurityException">The user lacks necessary permissions to view the path.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The user lacks necessary permissions.</exception>
      <returns>Read-only collection of the path names of subdirectories within the specified directory.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.GetDirectories(System.String,Microsoft.VisualBasic.FileIO.SearchOption,System.String[])">
      <summary>Returns a collection of strings representing the path names of subdirectories within a directory.</summary>
      <param name="directory">Name and path of directory.</param>
      <param name="searchType">Whether to include subfolders. Default is <see langword="SearchOption.SearchTopLevelOnly" />.</param>
      <param name="wildcards">Pattern to match names.</param>
      <exception cref="T:System.ArgumentException">The path is not valid for one of the following reasons: it is a zero-length string; it contains only white space; it contains invalid characters; or it is a device path (starts with \\.\).</exception>
      <exception cref="T:System.ArgumentNullException">One or more of the specified wildcard characters is <see langword="Nothing" />, an empty string, or contains only spaces.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified directory does not exist.</exception>
      <exception cref="T:System.IO.IOException">The specified directory points to an existing file.</exception>
      <exception cref="T:System.IO.PathTooLongException">The path exceeds the system-defined maximum length.</exception>
      <exception cref="T:System.NotSupportedException">A file or directory name in the path contains a colon (:) or is in an invalid format.</exception>
      <exception cref="T:System.Security.SecurityException">The user lacks necessary permissions to view the path.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The user lacks necessary permissions.</exception>
      <returns>Read-only collection of the path names of subdirectories within the specified directory.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.GetDirectoryInfo(System.String)">
      <summary>Returns a <see cref="T:System.IO.DirectoryInfo" /> object for the specified path.</summary>
      <param name="directory">
        <see langword="String" />. Path of directory.</param>
      <exception cref="T:System.ArgumentException">The path is not valid for one of the following reasons: it is a zero-length string; it contains only white space; it contains invalid characters; or it is a device path (starts with \\.\).</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="directory" /> is <see langword="Nothing" /> or an empty string.</exception>
      <exception cref="T:System.IO.PathTooLongException">The path exceeds the system-defined maximum length.</exception>
      <exception cref="T:System.NotSupportedException">The directory path contains a colon (:) or is in an invalid format.</exception>
      <exception cref="T:System.Security.SecurityException">The user lacks necessary permissions to view the path.</exception>
      <returns>
        <see cref="T:System.IO.DirectoryInfo" /> object for the specified path.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.GetDriveInfo(System.String)">
      <summary>Returns a <see cref="T:System.IO.DriveInfo" /> object for the specified drive.</summary>
      <param name="drive">Drive to be examined.</param>
      <exception cref="T:System.ArgumentException">The path is not valid for one of the following reasons: it is a zero-length string; it contains only white space; it contains invalid characters; or it is a device path (starts with \\.\).</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="drive" /> is <see langword="Nothing" /> or an empty string.</exception>
      <exception cref="T:System.IO.PathTooLongException">The path exceeds the system-defined maximum length.</exception>
      <exception cref="T:System.Security.SecurityException">The user lacks necessary permissions to view the path.</exception>
      <returns>
        <see cref="T:System.IO.DriveInfo" /> object for the specified drive.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.GetFileInfo(System.String)">
      <summary>Returns a <see cref="T:System.IO.FileInfo" /> object for the specified file.</summary>
      <param name="file">Name and path of the file.</param>
      <exception cref="T:System.ArgumentException">The path name is malformed. For example, it contains invalid characters or is only white space. The file name has a trailing slash mark.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="file" /> is <see langword="Nothing" /> or an empty string.</exception>
      <exception cref="T:System.NotSupportedException">The path contains a colon in the middle of the string.</exception>
      <exception cref="T:System.IO.PathTooLongException">The path is too long.</exception>
      <exception cref="T:System.Security.SecurityException">The user lacks necessary permissions.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The user lacks ACL (access control list) access to the file.</exception>
      <returns>
        <see cref="T:System.IO.FileInfo" /> object for the specified file.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.GetFiles(System.String)">
      <summary>Returns a read-only collection of strings representing the names of files within a directory.</summary>
      <param name="directory">Directory to be searched.</param>
      <exception cref="T:System.ArgumentException">The path is not valid for one of the following reasons: it is a zero-length string; it contains only white space; it contains invalid characters; or it is a device path (starts with \\.\).</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="directory" /> is <see langword="Nothing" />.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The directory to search does not exist.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="directory" /> points to an existing file.</exception>
      <exception cref="T:System.IO.PathTooLongException">The path exceeds the system-defined maximum length.</exception>
      <exception cref="T:System.NotSupportedException">A file or directory name in the path contains a colon (:) or is in an invalid format.</exception>
      <exception cref="T:System.Security.SecurityException">The user lacks necessary permissions to view the path.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The user lacks necessary permissions.</exception>
      <returns>Read-only collection of file names from the specified directory.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.GetFiles(System.String,Microsoft.VisualBasic.FileIO.SearchOption,System.String[])">
      <summary>Returns a read-only collection of strings representing the names of files within a directory.</summary>
      <param name="directory">Directory to be searched.</param>
      <param name="searchType">Whether to include subfolders. Default is <see langword="SearchOption.SearchTopLevelOnly" />.</param>
      <param name="wildcards">Pattern to be matched.</param>
      <exception cref="T:System.ArgumentException">The path is not valid for one of the following reasons: it is a zero-length string; it contains only white space; it contains invalid characters; or it is a device path (starts with \\.\).</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="directory" /> is <see langword="Nothing" />.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The directory to search does not exist.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="directory" /> points to an existing file.</exception>
      <exception cref="T:System.IO.PathTooLongException">The path exceeds the system-defined maximum length.</exception>
      <exception cref="T:System.NotSupportedException">A file or directory name in the path contains a colon (:) or is in an invalid format.</exception>
      <exception cref="T:System.Security.SecurityException">The user lacks necessary permissions to view the path.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The user lacks necessary permissions.</exception>
      <returns>Read-only collection of file names from the specified directory.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.GetName(System.String)">
      <summary>Parses the file name out of the path provided.</summary>
      <param name="path">Required. Path to be parsed. <see langword="String" />.</param>
      <returns>The file name from the specified path.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.GetParentPath(System.String)">
      <summary>Returns the parent path of the provided path.</summary>
      <param name="path">Path to be examined.</param>
      <exception cref="T:System.ArgumentException">Path does not have a parent path because it is a root path.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="path" /> is <see langword="Nothing" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The path exceeds the system-defined maximum length.</exception>
      <exception cref="T:System.NotSupportedException">A file or directory name in the path contains a colon (:) or is in an invalid format.</exception>
      <returns>The parent path of the provided path.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.GetTempFileName">
      <summary>Creates a uniquely named zero-byte temporary file on disk and returns the full path of that file.</summary>
      <returns>
        <see langword="String" /> containing the full path of the temporary file.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.MoveDirectory(System.String,System.String)">
      <summary>Moves a directory from one location to another.</summary>
      <param name="sourceDirectoryName">Path of the directory to be moved.</param>
      <param name="destinationDirectoryName">Path of the directory to which the source directory is being moved.</param>
      <exception cref="T:System.ArgumentException">The path is not valid for one of the following reasons: it is a zero-length string; it contains only white space; it contains invalid characters; or it is a device path (starts with \\.\).</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceDirectoryName" /> or <paramref name="destinationDirectoryName" /> is <see langword="Nothing" /> or an empty string.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The directory does not exist.</exception>
      <exception cref="T:System.IO.IOException">The source is a root directory or The source path and the target path are the same.</exception>
      <exception cref="T:System.IO.PathTooLongException">The path exceeds the system-defined maximum length.</exception>
      <exception cref="T:System.InvalidOperationException">The operation is cyclic.</exception>
      <exception cref="T:System.NotSupportedException">A file or directory name in the path contains a colon (:) or is in an invalid format.</exception>
      <exception cref="T:System.Security.SecurityException">The user lacks necessary permissions to view the path.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The user does not have required permission.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.MoveDirectory(System.String,System.String,Microsoft.VisualBasic.FileIO.UIOption)">
      <summary>Moves a directory from one location to another.</summary>
      <param name="sourceDirectoryName">Path of the directory to be moved.</param>
      <param name="destinationDirectoryName">Path of the directory to which the source directory is being moved.</param>
      <param name="showUI">Specifies whether to visually track the operation's progress. Default is <see langword="UIOption.OnlyErrorDialogs" />.</param>
      <exception cref="T:System.ArgumentException">The path is not valid for one of the following reasons: it is a zero-length string; it contains only white space; it contains invalid characters; or it is a device path (starts with \\.\).</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceDirectoryName" /> or <paramref name="destinationDirectoryName" /> is <see langword="Nothing" /> or an empty string.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The directory does not exist.</exception>
      <exception cref="T:System.IO.IOException">The target directory already exists and <paramref name="overwrite" /> is set to <see langword="False" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The path exceeds the system-defined maximum length.</exception>
      <exception cref="T:System.InvalidOperationException">The operation is cyclic.</exception>
      <exception cref="T:System.NotSupportedException">A file or directory name in the path contains a colon (:) or is in an invalid format.</exception>
      <exception cref="T:System.Security.SecurityException">The user lacks necessary permissions to view the path.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The user does not have required permission.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.MoveDirectory(System.String,System.String,Microsoft.VisualBasic.FileIO.UIOption,Microsoft.VisualBasic.FileIO.UICancelOption)">
      <summary>Moves a directory from one location to another.</summary>
      <param name="sourceDirectoryName">Path of the directory to be moved.</param>
      <param name="destinationDirectoryName">Path of the directory to which the source directory is being moved.</param>
      <param name="showUI">Specifies whether to visually track the operation's progress. Default is <see langword="UIOption.OnlyErrorDialogs" />.</param>
      <param name="onUserCancel">Specifies whether or not an exception is thrown when the user cancels the operation. Default is <see langword="UICancelOption.ThrowException" />.</param>
      <exception cref="T:System.ArgumentException">The path is not valid for one of the following reasons: it is a zero-length string; it contains only white space; it contains invalid characters; or it is a device path (starts with \\.\).</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceDirectoryName" /> or <paramref name="destinationDirectoryName" /> is <see langword="Nothing" /> or an empty string.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The directory does not exist.</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="onUserCancel" /> is set to <see langword="ThrowException" /> and a subdirectory of the file cannot be copied.</exception>
      <exception cref="T:System.OperationCanceledException">
        <paramref name="onUserCancel" /> is set to <see langword="ThrowException" />, and the user cancels the operation, or the operation cannot be completed.</exception>
      <exception cref="T:System.Security.SecurityException">The user lacks necessary permissions to view the path.</exception>
      <exception cref="T:System.IO.PathTooLongException">The path exceeds the system-defined maximum length.</exception>
      <exception cref="T:System.InvalidOperationException">The operation is cyclic.</exception>
      <exception cref="T:System.NotSupportedException">A file or directory name in the path contains a colon (:) or is in an invalid format.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The user does not have required permission.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.MoveDirectory(System.String,System.String,System.Boolean)">
      <summary>Moves a directory from one location to another.</summary>
      <param name="sourceDirectoryName">Path of the directory to be moved.</param>
      <param name="destinationDirectoryName">Path of the directory to which the source directory is being moved.</param>
      <param name="overwrite">
        <see langword="True" /> if existing directories should be overwritten; otherwise <see langword="False" />. Default is <see langword="False" />.</param>
      <exception cref="T:System.ArgumentException">The path is not valid for one of the following reasons: it is a zero-length string; it contains only white space; it contains invalid characters; or it is a device path (starts with \\.\).</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceDirectoryName" /> or <paramref name="destinationDirectoryName" /> is <see langword="Nothing" /> or an empty string.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The directory does not exist.</exception>
      <exception cref="T:System.IO.IOException">The target directory already exists and <paramref name="overwrite" /> is set to <see langword="False" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The path exceeds the system-defined maximum length.</exception>
      <exception cref="T:System.InvalidOperationException">The operation is cyclic.</exception>
      <exception cref="T:System.NotSupportedException">A file or directory name in the path contains a colon (:) or is in an invalid format.</exception>
      <exception cref="T:System.Security.SecurityException">The user lacks necessary permissions to view the path.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The user does not have required permission.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.MoveFile(System.String,System.String)">
      <summary>Moves a file to a new location.</summary>
      <param name="sourceFileName">Path of the file to be moved.</param>
      <param name="destinationFileName">Path of the directory into which the file should be moved.</param>
      <exception cref="T:System.ArgumentException">The path is not valid for one of the following reasons: it is a zero-length string; it contains only white space; it contains invalid characters; or it is a device path (starts with \\.\); it ends with a trailing slash.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destinationFileName" /> is <see langword="Nothing" /> or an empty string.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The source file is not valid or does not exist.</exception>
      <exception cref="T:System.IO.IOException">The file is in use by another process, or an I/O error occurs.</exception>
      <exception cref="T:System.IO.PathTooLongException">The path exceeds the system-defined maximum length.</exception>
      <exception cref="T:System.NotSupportedException">A file or directory name in the path contains a colon (:) or is in an invalid format.</exception>
      <exception cref="T:System.Security.SecurityException">The user lacks necessary permissions to view the path.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.MoveFile(System.String,System.String,Microsoft.VisualBasic.FileIO.UIOption)">
      <summary>Moves a file to a new location.</summary>
      <param name="sourceFileName">Path of the file to be moved.</param>
      <param name="destinationFileName">Path of the directory into which the file should be moved.</param>
      <param name="showUI">Specifies whether to visually track the operation's progress. Default is <see langword="UIOption.OnlyErrorDialogs" />.</param>
      <exception cref="T:System.ArgumentException">The path is not valid for one of the following reasons: it is a zero-length string; it contains only white space; it contains invalid characters; or it is a device path (starts with \\.\); it ends with a trailing slash.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destinationFileName" /> is <see langword="Nothing" /> or an empty string.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The source file is not valid or does not exist.</exception>
      <exception cref="T:System.IO.IOException">The file is in use by another process, or an I/O error occurs.</exception>
      <exception cref="T:System.IO.PathTooLongException">The path exceeds the system-defined maximum length.</exception>
      <exception cref="T:System.NotSupportedException">A file or directory name in the path contains a colon (:) or is in an invalid format.</exception>
      <exception cref="T:System.Security.SecurityException">The user lacks necessary permissions to view the path.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.MoveFile(System.String,System.String,Microsoft.VisualBasic.FileIO.UIOption,Microsoft.VisualBasic.FileIO.UICancelOption)">
      <summary>Moves a file to a new location.</summary>
      <param name="sourceFileName">Path of the file to be moved.</param>
      <param name="destinationFileName">Path of the directory into which the file should be moved.</param>
      <param name="showUI">Specifies whether to visually track the operation's progress. Default is <see langword="UIOption.OnlyErrorDialogs" />.</param>
      <param name="onUserCancel">Specifies whether or not an exception is thrown when the user cancels the operation. Default is <see langword="UICancelOption.ThrowException" />.</param>
      <exception cref="T:System.ArgumentException">The path is not valid for one of the following reasons: it is a zero-length string; it contains only white space; it contains invalid characters; or it is a device path (starts with \\.\); it ends with a trailing slash.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destinationFileName" /> is <see langword="Nothing" /> or an empty string.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The source file is not valid or does not exist.</exception>
      <exception cref="T:System.IO.IOException">The file is in use by another process, or an I/O error occurs.</exception>
      <exception cref="T:System.OperationCanceledException">
        <paramref name="onUserCancel" /> is set to <see langword="ThrowException" />, and either the user has cancelled the operation or an unspecified I/O error occurs.</exception>
      <exception cref="T:System.IO.PathTooLongException">The path exceeds the system-defined maximum length.</exception>
      <exception cref="T:System.NotSupportedException">A file or directory name in the path contains a colon (:) or is in an invalid format.</exception>
      <exception cref="T:System.Security.SecurityException">The user lacks necessary permissions to view the path.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.MoveFile(System.String,System.String,System.Boolean)">
      <summary>Moves a file to a new location.</summary>
      <param name="sourceFileName">Path of the file to be moved.</param>
      <param name="destinationFileName">Path of the directory into which the file should be moved.</param>
      <param name="overwrite">
        <see langword="True" /> to overwrite existing files; otherwise <see langword="False" />. Default is <see langword="False" />.</param>
      <exception cref="T:System.ArgumentException">The path is not valid for one of the following reasons: it is a zero-length string; it contains only white space; it contains invalid characters; or it is a device path (starts with \\.\); it ends with a trailing slash.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destinationFileName" /> is <see langword="Nothing" /> or an empty string.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The source file is not valid or does not exist.</exception>
      <exception cref="T:System.IO.IOException">The file is in use by another process, or an I/O error occurs.</exception>
      <exception cref="T:System.IO.PathTooLongException">The path exceeds the system-defined maximum length.</exception>
      <exception cref="T:System.NotSupportedException">A file or directory name in the path contains a colon (:) or is in an invalid format.</exception>
      <exception cref="T:System.Security.SecurityException">The user lacks necessary permissions to view the path.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.OpenTextFieldParser(System.String)">
      <summary>The <see langword="OpenTextFieldParser" /> method allows you to create a <see cref="T:Microsoft.VisualBasic.FileIO.TextFieldParser" /> object, which provides a way to easily and efficiently parse structured text files, such as logs. The <see langword="TextFieldParser" /> object can be used to read both delimited and fixed-width files.</summary>
      <param name="file">The file to be opened with the <see langword="TextFieldParser" />.</param>
      <exception cref="T:System.ArgumentException">The path is not valid for one of the following reasons: it is a zero-length string; it contains only white space; it contains invalid characters; or it is a device path (starts with \\.\); it ends with a trailing slash.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="file" /> is <see langword="Nothing" />.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file does not exist.</exception>
      <exception cref="T:System.IO.IOException">The file is in use by another process, or an I/O error occurs.</exception>
      <exception cref="T:System.IO.PathTooLongException">The path exceeds the system-defined maximum length.</exception>
      <exception cref="T:System.NotSupportedException">A file or directory name in the path contains a colon (:) or is in an invalid format.</exception>
      <exception cref="T:Microsoft.VisualBasic.FileIO.MalformedLineException">A row cannot be parsed using the specified format. The exception message specifies the line causing the exception, while the <see cref="P:Microsoft.VisualBasic.FileIO.TextFieldParser.ErrorLine" /> property is assigned the text contained in the line.</exception>
      <exception cref="T:System.Security.SecurityException">The user lacks necessary permissions to view the path.</exception>
      <returns>
        <see cref="T:Microsoft.VisualBasic.FileIO.TextFieldParser" /> to read the specified file.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.OpenTextFieldParser(System.String,System.Int32[])">
      <summary>The <see langword="OpenTextFieldParser" /> method allows you to create a <see cref="T:Microsoft.VisualBasic.FileIO.TextFieldParser" /> object, which provides a way to easily and efficiently parse structured text files, such as logs. The <see langword="TextFieldParser" /> object can be used to read both delimited and fixed-width files.</summary>
      <param name="file">The file to be opened with the <see langword="TextFieldParser" />.</param>
      <param name="fieldWidths">Widths of the fields.</param>
      <exception cref="T:System.ArgumentException">The path is not valid for one of the following reasons: it is a zero-length string; it contains only white space; it contains invalid characters; or it is a device path (starts with \\.\); it ends with a trailing slash.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="file" /> is <see langword="Nothing" />.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file does not exist.</exception>
      <exception cref="T:System.IO.IOException">The file is in use by another process, or an I/O error occurs.</exception>
      <exception cref="T:System.IO.PathTooLongException">The path exceeds the system-defined maximum length.</exception>
      <exception cref="T:System.NotSupportedException">A file or directory name in the path contains a colon (:) or is in an invalid format.</exception>
      <exception cref="T:Microsoft.VisualBasic.FileIO.MalformedLineException">A row cannot be parsed using the specified format. The exception message specifies the line causing the exception, while the <see cref="P:Microsoft.VisualBasic.FileIO.TextFieldParser.ErrorLine" /> property is assigned the text contained in the line.</exception>
      <exception cref="T:System.Security.SecurityException">The user lacks necessary permissions to view the path.</exception>
      <returns>
        <see cref="T:Microsoft.VisualBasic.FileIO.TextFieldParser" /> to read the specified file.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.OpenTextFieldParser(System.String,System.String[])">
      <summary>The <see langword="OpenTextFieldParser" /> method allows you to create a <see cref="T:Microsoft.VisualBasic.FileIO.TextFieldParser" /> object, which provides a way to easily and efficiently parse structured text files, such as logs. The <see langword="TextFieldParser" /> object can be used to read both delimited and fixed-width files.</summary>
      <param name="file">The file to be opened with the <see langword="TextFieldParser" />.</param>
      <param name="delimiters">Delimiters for the fields.</param>
      <exception cref="T:System.ArgumentException">The path is not valid for one of the following reasons: it is a zero-length string; it contains only white space; it contains invalid characters; or it is a device path (starts with \\.\); it ends with a trailing slash.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="file" /> is <see langword="Nothing" />.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file does not exist.</exception>
      <exception cref="T:System.IO.IOException">The file is in use by another process, or an I/O error occurs.</exception>
      <exception cref="T:System.IO.PathTooLongException">The path exceeds the system-defined maximum length.</exception>
      <exception cref="T:System.NotSupportedException">A file or directory name in the path contains a colon (:) or is in an invalid format.</exception>
      <exception cref="T:Microsoft.VisualBasic.FileIO.MalformedLineException">A row cannot be parsed using the specified format. The exception message specifies the line causing the exception, while the <see cref="P:Microsoft.VisualBasic.FileIO.TextFieldParser.ErrorLine" /> property is assigned the text contained in the line.</exception>
      <exception cref="T:System.Security.SecurityException">The user lacks necessary permissions to view the path.</exception>
      <returns>
        <see cref="T:Microsoft.VisualBasic.FileIO.TextFieldParser" /> to read the specified file.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.OpenTextFileReader(System.String)">
      <summary>Opens a <see cref="T:System.IO.StreamReader" /> object to read from a file.</summary>
      <param name="file">File to be read.</param>
      <exception cref="T:System.ArgumentException">The file name ends with a backslash (\).</exception>
      <exception cref="T:System.IO.FileNotFoundException">The specified file cannot be found.</exception>
      <exception cref="T:System.Security.SecurityException">The user lacks necessary permissions to read from the file.</exception>
      <returns>
        <see cref="T:System.IO.StreamReader" /> object to read from the file.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.OpenTextFileReader(System.String,System.Text.Encoding)">
      <summary>Opens a <see cref="T:System.IO.StreamReader" /> object to read from a file.</summary>
      <param name="file">File to be read.</param>
      <param name="encoding">The encoding to use for the file contents. Default is ASCII.</param>
      <exception cref="T:System.ArgumentException">The file name ends with a backslash (\).</exception>
      <exception cref="T:System.IO.FileNotFoundException">The specified file cannot be found.</exception>
      <exception cref="T:System.Security.SecurityException">The user lacks necessary permissions to read from the file.</exception>
      <returns>
        <see cref="T:System.IO.StreamReader" /> object to read from the file.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.OpenTextFileWriter(System.String,System.Boolean)">
      <summary>Opens a <see cref="T:System.IO.StreamWriter" /> object to write to the specified file.</summary>
      <param name="file">File to be written to.</param>
      <param name="append">
        <see langword="True" /> to append to the contents of the file; <see langword="False" /> to overwrite the contents of the file. Default is <see langword="False" />.</param>
      <exception cref="T:System.ArgumentException">The file name ends with a trailing slash.</exception>
      <returns>
        <see cref="T:System.IO.StreamWriter" /> object to write to the specified file.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.OpenTextFileWriter(System.String,System.Boolean,System.Text.Encoding)">
      <summary>Opens a <see cref="T:System.IO.StreamWriter" /> to write to the specified file.</summary>
      <param name="file">File to be written to.</param>
      <param name="append">
        <see langword="True" /> to append to the contents in the file; <see langword="False" /> to overwrite the contents of the file. Default is <see langword="False" />.</param>
      <param name="encoding">Encoding to be used in writing to the file. Default is ASCII.</param>
      <exception cref="T:System.ArgumentException">The file name ends with a trailing slash.</exception>
      <returns>
        <see cref="T:System.IO.StreamWriter" /> object to write to the specified file.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.ReadAllBytes(System.String)">
      <summary>Returns the contents of a file as a byte array.</summary>
      <param name="file">File to be read.</param>
      <exception cref="T:System.ArgumentException">The path is not valid for one of the following reasons: it is a zero-length string; it contains only white space; it contains invalid characters; or it is a device path (starts with \\.\); it ends with a trailing slash.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="file" /> is <see langword="Nothing" />.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file does not exist.</exception>
      <exception cref="T:System.IO.IOException">The file is in use by another process, or an I/O error occurs.</exception>
      <exception cref="T:System.IO.PathTooLongException">The path exceeds the system-defined maximum length.</exception>
      <exception cref="T:System.NotSupportedException">A file or directory name in the path contains a colon (:) or is in an invalid format.</exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to write the string to buffer.</exception>
      <exception cref="T:System.Security.SecurityException">The user lacks necessary permissions to view the path.</exception>
      <returns>
        <see langword="Byte" /> array containing the contents of the file.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.ReadAllText(System.String)">
      <summary>Returns the contents of a text file as a <see langword="String" />.</summary>
      <param name="file">Name and path of the file to read.</param>
      <exception cref="T:System.ArgumentException">The path is not valid for one of the following reasons: it is a zero-length string; it contains only white space; it contains invalid characters; or it is a device path (starts with \\.\); it ends with a trailing slash.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="file" /> is <see langword="Nothing" />.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file does not exist.</exception>
      <exception cref="T:System.IO.IOException">The file is in use by another process, or an I/O error occurs.</exception>
      <exception cref="T:System.IO.PathTooLongException">The path exceeds the system-defined maximum length.</exception>
      <exception cref="T:System.NotSupportedException">A file or directory name in the path contains a colon (:) or is in an invalid format.</exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to write the string to buffer.</exception>
      <exception cref="T:System.Security.SecurityException">The user lacks necessary permissions to view the path.</exception>
      <returns>
        <see langword="String" /> containing the contents of the file.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.ReadAllText(System.String,System.Text.Encoding)">
      <summary>Returns the contents of a text file as a <see langword="String" />.</summary>
      <param name="file">Name and path of the file to read.</param>
      <param name="encoding">Character encoding to use in reading the file. Default is UTF-8.</param>
      <exception cref="T:System.ArgumentException">The path is not valid for one of the following reasons: it is a zero-length string; it contains only white space; it contains invalid characters; or it is a device path (starts with \\.\); it ends with a trailing slash.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="file" /> is <see langword="Nothing" />.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file does not exist.</exception>
      <exception cref="T:System.IO.IOException">The file is in use by another process, or an I/O error occurs.</exception>
      <exception cref="T:System.IO.PathTooLongException">The path exceeds the system-defined maximum length.</exception>
      <exception cref="T:System.NotSupportedException">A file or directory name in the path contains a colon (:) or is in an invalid format.</exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to write the string to buffer.</exception>
      <exception cref="T:System.Security.SecurityException">The user lacks necessary permissions to view the path.</exception>
      <returns>
        <see langword="String" /> containing the contents of the file.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.RenameDirectory(System.String,System.String)">
      <summary>Renames a directory.</summary>
      <param name="directory">Path and name of directory to be renamed.</param>
      <param name="newName">New name for directory.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="newName" /> contains path information.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="directory" /> is <see langword="Nothing" />.  
  
 -or-  
  
 <paramref name="newName" /> is <see langword="Nothing" /> or an empty string.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The directory does not exist.</exception>
      <exception cref="T:System.IO.IOException">There is an existing file or directory with the name specified in <paramref name="newName" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The path exceeds the system-defined maximum length.</exception>
      <exception cref="T:System.NotSupportedException">A file or directory name in the path contains a colon (:) or is in an invalid format.</exception>
      <exception cref="T:System.Security.SecurityException">The user lacks necessary permissions to view the path.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The user does not have required permission.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.RenameFile(System.String,System.String)">
      <summary>Renames a file.</summary>
      <param name="file">File to be renamed.</param>
      <param name="newName">New name of file.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="newName" /> contains path information or ends with a backslash (\).</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="file" /> is <see langword="Nothing" />.  
  
 -or-  
  
 <paramref name="newName" /> is <see langword="Nothing" /> or an empty string.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file does not exist.</exception>
      <exception cref="T:System.IO.IOException">There is an existing file or directory with the name specified in <paramref name="newName" />.</exception>
      <exception cref="T:System.IO.PathTooLongException">The path exceeds the system-defined maximum length.</exception>
      <exception cref="T:System.NotSupportedException">A file or directory name in the path contains a colon (:) or is in an invalid format.</exception>
      <exception cref="T:System.Security.SecurityException">The user lacks necessary permissions to view the path.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The user does not have required permission.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.WriteAllBytes(System.String,System.Byte[],System.Boolean)">
      <summary>Writes data to a binary file.</summary>
      <param name="file">Path and name of the file to be written to.</param>
      <param name="data">Data to be written to the file.</param>
      <param name="append">
        <see langword="True" /> to append to the file contents; <see langword="False" /> to overwrite the file contents. Default is <see langword="False" />.</param>
      <exception cref="T:System.ArgumentException">The path is not valid for one of the following reasons: it is a zero-length string; it contains only white space; it contains invalid characters; or it is a device path (starts with \\.\); it ends with a trailing slash.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="file" /> is <see langword="Nothing" />.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file does not exist.</exception>
      <exception cref="T:System.IO.IOException">The file is in use by another process, or an I/O error occurs.</exception>
      <exception cref="T:System.IO.PathTooLongException">The path exceeds the system-defined maximum length.</exception>
      <exception cref="T:System.NotSupportedException">A file or directory name in the path contains a colon (:) or is in an invalid format.</exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to write the string to buffer.</exception>
      <exception cref="T:System.Security.SecurityException">The user lacks necessary permissions to view the path.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.WriteAllText(System.String,System.String,System.Boolean)">
      <summary>Writes text to a file.</summary>
      <param name="file">File to be written to.</param>
      <param name="text">Text to be written to file.</param>
      <param name="append">
        <see langword="True" /> to append to the contents of the file; <see langword="False" /> to overwrite the contents of the file. Default is <see langword="False" />.</param>
      <exception cref="T:System.ArgumentException">The path is not valid for one of the following reasons: it is a zero-length string; it contains only white space; it contains invalid characters; or it is a device path (starts with \\.\); it ends with a trailing slash.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="file" /> is <see langword="Nothing" />.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file does not exist.</exception>
      <exception cref="T:System.IO.IOException">The file is in use by another process, or an I/O error occurs.</exception>
      <exception cref="T:System.IO.PathTooLongException">The path exceeds the system-defined maximum length.</exception>
      <exception cref="T:System.NotSupportedException">A file or directory name in the path contains a colon (:) or is in an invalid format.</exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to write the string to buffer.</exception>
      <exception cref="T:System.Security.SecurityException">The user lacks necessary permissions to view the path.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.FileSystemProxy.WriteAllText(System.String,System.String,System.Boolean,System.Text.Encoding)">
      <summary>Writes text to a file.</summary>
      <param name="file">File to be written to.</param>
      <param name="text">Text to be written to file.</param>
      <param name="append">
        <see langword="True" /> to append to the contents of the file; <see langword="False" /> to overwrite the contents of the file. Default is <see langword="False" />.</param>
      <param name="encoding">What encoding to use when writing to file. Default is UTF-8.</param>
      <exception cref="T:System.ArgumentException">The path is not valid for one of the following reasons: it is a zero-length string; it contains only white space; it contains invalid characters; or it is a device path (starts with \\.\); it ends with a trailing slash.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="file" /> is <see langword="Nothing" />.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file does not exist.</exception>
      <exception cref="T:System.IO.IOException">The file is in use by another process, or an I/O error occurs.</exception>
      <exception cref="T:System.IO.PathTooLongException">The path exceeds the system-defined maximum length.</exception>
      <exception cref="T:System.NotSupportedException">A file or directory name in the path contains a colon (:) or is in an invalid format.</exception>
      <exception cref="T:System.OutOfMemoryException">There is not enough memory to write the string to buffer.</exception>
      <exception cref="T:System.Security.SecurityException">The user lacks necessary permissions to view the path.</exception>
    </member>
    <member name="P:Microsoft.VisualBasic.MyServices.FileSystemProxy.CurrentDirectory">
      <summary>Gets or sets the current directory.</summary>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path is not valid.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The user lacks necessary permissions.</exception>
      <returns>The current directory for file I/O operations.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.MyServices.FileSystemProxy.Drives">
      <summary>Returns a read-only collection of all available drive names.</summary>
      <returns>A read-only collection of all available drives as <see cref="T:System.IO.DriveInfo" /> objects.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.MyServices.FileSystemProxy.SpecialDirectories">
      <summary>Gets an object that provides properties for accessing commonly referenced directories.</summary>
      <returns>This property returns the <see cref="T:Microsoft.VisualBasic.FileIO.SpecialDirectories" /> object for the computer.</returns>
    </member>
    <member name="T:Microsoft.VisualBasic.MyServices.Internal.ContextValue`1">
      <summary>This class supports <see langword="My" /> in Visual Basic.</summary>
      <typeparam name="T">The type of the object to store.</typeparam>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.Internal.ContextValue`1.#ctor">
      <summary>This class supports <see langword="My" /> in Visual Basic.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.MyServices.Internal.ContextValue`1.Value">
      <summary>This property supports <see langword="My" /> in Visual Basic.</summary>
      <returns>The value associated with this class.</returns>
    </member>
    <member name="T:Microsoft.VisualBasic.MyServices.RegistryProxy">
      <summary>Provides properties and methods for manipulating the registry.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.RegistryProxy.GetValue(System.String,System.String,System.Object)">
      <summary>Gets a value from a registry key.</summary>
      <param name="keyName">
        <see langword="String" />. Key from which the value is to be retrieved. Required.</param>
      <param name="valueName">
        <see langword="String" />. Value to be retrieved. Required.</param>
      <param name="defaultValue">
        <see langword="Object" />. Default value to be supplied if the value does not exist. Required.</param>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to read from the registry key.</exception>
      <exception cref="T:System.IO.IOException">The <see cref="T:Microsoft.Win32.RegistryKey" /> that contains the specified value has been marked for deletion.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="keyName" /> does not begin with a valid registry root.</exception>
      <returns>Gets a value from a registry key.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.RegistryProxy.SetValue(System.String,System.String,System.Object)">
      <summary>Writes a value to a registry key.</summary>
      <param name="keyName">
        <see langword="String" />. Name of the key to be written to. Required.</param>
      <param name="valueName">
        <see langword="String" />. Name of the value to be written. Required.</param>
      <param name="value">
        <see langword="Object" />. Value to be written. Required.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="Nothing" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="keyName" /> does not begin with a valid registry root.  
  
 -or-  
  
 <paramref name="valueName" /> is longer than the maximum length allowed (255 characters).</exception>
      <exception cref="T:System.UnauthorizedAccessException">The <see cref="T:Microsoft.Win32.RegistryKey" /> is read-only and thus cannot be written to; for example, it is a root-level node, or it has not been opened with write access.</exception>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to create or modify registry keys.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.MyServices.RegistryProxy.SetValue(System.String,System.String,System.Object,Microsoft.Win32.RegistryValueKind)">
      <summary>Writes a value to a registry key.</summary>
      <param name="keyName">
        <see langword="String" />. Name of the key to be written to. Required.</param>
      <param name="valueName">
        <see langword="String" />. Name of the value to be written. Required.</param>
      <param name="value">
        <see langword="Object" />. Value to be written. Required.</param>
      <param name="valueKind">
        <see cref="T:Microsoft.Win32.RegistryValueKind" />. Required.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="Nothing" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="keyName" /> does not begin with a valid registry root.  
  
 -or-  
  
 <paramref name="keyName" /> is longer than the maximum length allowed (255 characters).  
  
 -or-  
  
 The type of <paramref name="value" /> does not match the registry data type specified by <paramref name="valueKind" />, therefore the data cannot be converted properly.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The <see cref="T:Microsoft.Win32.RegistryKey" /> is read-only, and thus cannot be written to; for example, it is a root-level node, or it has not been opened with write access.</exception>
      <exception cref="T:System.Security.SecurityException">The user does not have the permissions required to create or modify registry keys.</exception>
    </member>
    <member name="P:Microsoft.VisualBasic.MyServices.RegistryProxy.ClassesRoot">
      <summary>Returns a <see cref="T:Microsoft.Win32.RegistryKey" /> type which provides access to <see langword="HKEY_CLASSES_ROOT" />.</summary>
      <returns>
        <see cref="T:Microsoft.Win32.RegistryKey" />
      </returns>
    </member>
    <member name="P:Microsoft.VisualBasic.MyServices.RegistryProxy.CurrentConfig">
      <summary>Returns a <see cref="T:Microsoft.Win32.RegistryKey" /> type which provides access to <see langword="HKEY_CURRENT_CONFIG" />.</summary>
      <returns>
        <see cref="T:Microsoft.Win32.RegistryKey" />
      </returns>
    </member>
    <member name="P:Microsoft.VisualBasic.MyServices.RegistryProxy.CurrentUser">
      <summary>Returns a <see cref="T:Microsoft.Win32.RegistryKey" /> type which provides access to <see langword="HKEY_CURRENT_USER" />.</summary>
      <returns>
        <see cref="T:Microsoft.Win32.RegistryKey" />
      </returns>
    </member>
    <member name="P:Microsoft.VisualBasic.MyServices.RegistryProxy.LocalMachine">
      <summary>Returns a <see cref="T:Microsoft.Win32.RegistryKey" /> type, which provides access to <see langword="HKEY_LOCAL_MACHINE" />.</summary>
      <returns>
        <see cref="T:Microsoft.Win32.RegistryKey" />
      </returns>
    </member>
    <member name="P:Microsoft.VisualBasic.MyServices.RegistryProxy.PerformanceData">
      <summary>Returns a <see cref="T:Microsoft.Win32.RegistryKey" /> type, which provides access to <see langword="HKEY_PERFORMANCE_DATA" />.</summary>
      <returns>
        <see cref="T:Microsoft.Win32.RegistryKey" />
      </returns>
    </member>
    <member name="P:Microsoft.VisualBasic.MyServices.RegistryProxy.Users">
      <summary>Returns a <see cref="T:Microsoft.Win32.RegistryKey" /> type, which provides access to <see langword="HKEY_USERS" />.</summary>
      <returns>
        <see cref="T:Microsoft.Win32.RegistryKey" />
      </returns>
    </member>
    <member name="T:Microsoft.VisualBasic.MyServices.SpecialDirectoriesProxy">
      <summary>Provides properties for accessing commonly referenced directories.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.MyServices.SpecialDirectoriesProxy.AllUsersApplicationData">
      <summary>Gets a path name pointing to the Application Data directory for the all users.</summary>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path is empty, usually because the operating system does not support the directory.</exception>
      <returns>The path to the Application Data directory for the all users.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.MyServices.SpecialDirectoriesProxy.CurrentUserApplicationData">
      <summary>Gets a path name pointing to the Application Data directory for the current user.</summary>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path is empty, usually because the operating system does not support the directory.</exception>
      <returns>The path to the Application Data directory for the current user.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.MyServices.SpecialDirectoriesProxy.Desktop">
      <summary>Gets a path name pointing to the Desktop directory.</summary>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path is empty, usually because the operating system does not support the directory.</exception>
      <returns>The path to the Desktop directory.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.MyServices.SpecialDirectoriesProxy.MyDocuments">
      <summary>Gets a path name pointing to the My Documents directory.</summary>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path is empty, usually because the operating system does not support the directory.</exception>
      <returns>The path to the My Documents directory.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.MyServices.SpecialDirectoriesProxy.MyMusic">
      <summary>Gets a path name pointing to the My Music directory.</summary>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path is empty, usually because the operating system does not support the directory.</exception>
      <returns>The path to the My Music directory.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.MyServices.SpecialDirectoriesProxy.MyPictures">
      <summary>Gets a path name pointing to the My Pictures directory.</summary>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path is empty, usually because the operating system does not support the directory.</exception>
      <returns>The path to the My Pictures directory.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.MyServices.SpecialDirectoriesProxy.ProgramFiles">
      <summary>Gets a path pointing to the Program Files directory.</summary>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path is empty, usually because the operating system does not support the directory.</exception>
      <returns>The path to the Program Files directory.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.MyServices.SpecialDirectoriesProxy.Programs">
      <summary>Gets a path name pointing to the Programs directory.</summary>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path is empty, usually because the operating system does not support the directory.</exception>
      <returns>The path to the Programs directory.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.MyServices.SpecialDirectoriesProxy.Temp">
      <summary>Gets a path name pointing to the Temp directory.</summary>
      <exception cref="T:System.IO.DirectoryNotFoundException">The path is empty, usually because the operating system does not support the directory.</exception>
      <returns>The path to the Temp directory.</returns>
    </member>
  </members>
</doc>