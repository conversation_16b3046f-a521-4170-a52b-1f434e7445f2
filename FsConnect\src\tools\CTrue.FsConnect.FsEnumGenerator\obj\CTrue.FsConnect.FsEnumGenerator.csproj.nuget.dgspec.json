{"format": 1, "restore": {"C:\\dev\\Personal\\FlightPig\\FsConnect\\src\\tools\\CTrue.FsConnect.FsEnumGenerator\\CTrue.FsConnect.FsEnumGenerator.csproj": {}}, "projects": {"C:\\dev\\Personal\\FlightPig\\FsConnect\\src\\tools\\CTrue.FsConnect.FsEnumGenerator\\CTrue.FsConnect.FsEnumGenerator.csproj": {"version": "1.4.0", "restore": {"projectUniqueName": "C:\\dev\\Personal\\FlightPig\\FsConnect\\src\\tools\\CTrue.FsConnect.FsEnumGenerator\\CTrue.FsConnect.FsEnumGenerator.csproj", "projectName": "CTrue.FsConnect.FsEnumGenerator", "projectPath": "C:\\dev\\Personal\\FlightPig\\FsConnect\\src\\tools\\CTrue.FsConnect.FsEnumGenerator\\CTrue.FsConnect.FsEnumGenerator.csproj", "packagesPath": "C:\\dev\\Personal\\FlightPig\\FsConnect\\src\\packages", "outputPath": "C:\\dev\\Personal\\FlightPig\\FsConnect\\src\\tools\\CTrue.FsConnect.FsEnumGenerator\\obj\\", "projectStyle": "PackageReference", "centralPackageVersionsManagementEnabled": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\dev\\Personal\\FlightPig\\FsConnect\\src\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"CommandLineParser": {"target": "Package", "version": "[2.8.0, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"CommandLineParser": "2.8.0", "Microsoft.NET.Test.Sdk": "16.9.1", "NUnit": "3.13.1", "NUnit3TestAdapter": "3.17.0", "Serilog": "2.10.0", "Serilog.Sinks.Console": "3.1.1"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}}}