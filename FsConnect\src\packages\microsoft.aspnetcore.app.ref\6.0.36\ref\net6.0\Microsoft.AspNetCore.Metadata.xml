<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.AspNetCore.Metadata</name>
    </assembly>
    <members>
        <member name="T:Microsoft.AspNetCore.Authorization.IAllowAnonymous">
            <summary>
            Marker interface to allow access to anonymous users.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Authorization.IAuthorizeData">
            <summary>
            Defines the set of data required to apply authorization rules to a resource.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authorization.IAuthorizeData.Policy">
            <summary>
            Gets or sets the policy name that determines access to the resource.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authorization.IAuthorizeData.Roles">
            <summary>
            Gets or sets a comma delimited list of roles that are allowed to access the resource.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authorization.IAuthorizeData.AuthenticationSchemes">
            <summary>
            Gets or sets a comma delimited list of schemes from which user information is constructed.
            </summary>
        </member>
    </members>
</doc>
