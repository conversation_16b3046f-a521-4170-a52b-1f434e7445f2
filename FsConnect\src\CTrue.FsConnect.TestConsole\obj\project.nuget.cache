{"version": 2, "dgSpecHash": "gu+VVXMsJQU=", "success": false, "projectFilePath": "C:\\dev\\Personal\\FlightPig\\FsConnect\\src\\CTrue.FsConnect.TestConsole\\CTrue.FsConnect.TestConsole.csproj", "expectedPackageFiles": [], "logs": [{"code": "NU1301", "level": "Error", "message": "The local source 'C:\\dev\\Personal\\FlightPig\\FsConnect\\artifacts\\packages' doesn't exist.", "libraryId": "Serilog"}, {"code": "NU1301", "level": "Error", "message": "The local source 'C:\\dev\\Personal\\FlightPig\\FsConnect\\artifacts\\packages' doesn't exist.", "libraryId": "CommandLineParser"}, {"code": "NU1301", "level": "Error", "message": "The local source 'C:\\dev\\Personal\\FlightPig\\FsConnect\\artifacts\\packages' doesn't exist.", "libraryId": "Serilog.Sinks.Console"}]}