<?xml version="1.0"?>
<doc>
    <assembly>
        <name>System.Threading.AccessControl</name>
    </assembly>
    <members>
        <member name="M:Interop.Kernel32.GetMessage(System.Int32)">
            <summary>
                Returns a string message for the specified Win32 error code.
            </summary>
        </member>
        <member name="T:Interop.BOOL">
            <summary>
            Blittable version of Windows BOOL type. It is convenient in situations where
            manual marshalling is required, or to avoid overhead of regular bool marshalling.
            </summary>
            <remarks>
            Some Windows APIs return arbitrary integer values although the return type is defined
            as BOOL. It is best to never compare BOOL to TRUE. Always use bResult != BOOL.FALSE
            or bResult == BOOL.FALSE .
            </remarks>
        </member>
        <member name="T:System.IO.Win32Marshal">
            <summary>
            Provides static methods for converting from Win32 errors codes to exceptions, HRESULTS and error messages.
            </summary>
        </member>
        <member name="M:System.IO.Win32Marshal.GetExceptionForLastWin32Error(System.String)">
            <summary>
            Converts, resetting it, the last Win32 error into a corresponding <see cref="T:System.Exception"/> object, optionally
            including the specified path in the error message.
            </summary>
        </member>
        <member name="M:System.IO.Win32Marshal.GetExceptionForWin32Error(System.Int32,System.String)">
            <summary>
            Converts the specified Win32 error into a corresponding <see cref="T:System.Exception"/> object, optionally
            including the specified path in the error message.
            </summary>
        </member>
        <member name="M:System.IO.Win32Marshal.MakeHRFromErrorCode(System.Int32)">
            <summary>
            If not already an HRESULT, returns an HRESULT for the specified Win32 error code.
            </summary>
        </member>
        <member name="M:System.IO.Win32Marshal.TryMakeWin32ErrorCodeFromHR(System.Int32)">
            <summary>
            Returns a Win32 error code for the specified HRESULT if it came from FACILITY_WIN32
            If not, returns the HRESULT unchanged
            </summary>
        </member>
        <member name="M:System.IO.Win32Marshal.GetMessage(System.Int32)">
            <summary>
            Returns a string message for the specified Win32 error code.
            </summary>
        </member>
        <member name="T:System.Threading.OpenExistingResult">
            <summary>
            Mapping of the different success or failure values that can occur when p/invoking Interop.Kernel32.OpenEvent,
            which is called inside the OpenExistingWorker methods defined in EventWaitHandle, Mutex and Semaphore.
            </summary>
        </member>
        <member name="M:System.Threading.EventWaitHandleAcl.Create(System.Boolean,System.Threading.EventResetMode,System.String,System.Boolean@,System.Security.AccessControl.EventWaitHandleSecurity)">
            <summary>Gets or creates an <see cref="T:System.Threading.EventWaitHandle" /> instance, allowing a <see cref="T:System.Security.AccessControl.EventWaitHandleSecurity" /> instance to be optionally specified to set it during the event creation.</summary>
            <param name="initialState"><see langword="true" /> to set the initial state to signaled if the named event is created as a result of this call; <see langword="false" /> to set it to non-signaled.</param>
            <param name="mode">One of the enum values that determines whether the event resets automatically or manually.</param>
            <param name="name">The name, if the event is a system-wide synchronization event; otherwise, <see langword="null" /> or an empty string.</param>
            <param name="createdNew">When this method returns, this argument is always set to <see langword="true" /> if a local event is created; that is, when <paramref name="name" /> is <see langword="null" /> or <see cref="F:System.String.Empty" />. If <paramref name="name" /> has a valid, non-empty value, this argument is set to <see langword="true" /> when the system event is created, or it is set to <see langword="false" /> if an existing system event is found with that name. This parameter is passed uninitialized.</param>
            <param name="eventSecurity">The optional Windows access control security to apply.</param>
            <returns>An object that represents a system event wait handle, if named, or a local event wait handle, if nameless.</returns>
            <exception cref="T:System.ArgumentNullException">.NET Framework only: The <paramref name="name" /> length is beyond MAX_PATH (260 characters).</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="mode" /> enum value was out of legal range.</exception>
            <exception cref="T:System.IO.DirectoryNotFoundException">Could not find a part of the path specified in <paramref name="name" />.</exception>
            <exception cref="T:System.Threading.WaitHandleCannotBeOpenedException">A system-wide synchronization event with the provided <paramref name="name" /> was not found.
            -or-
            An <see cref="T:System.Threading.EventWaitHandle" /> with system-wide name <paramref name="name" /> cannot be created. An <see cref="T:System.Threading.EventWaitHandle" /> of a different type might have the same name.</exception>
            <remarks>If a `name` is passed and the system event already exists, the existing event is returned. If `name` is `null` or <see cref="F:System.String.Empty" />, a new local event is always created.</remarks>
        </member>
        <member name="M:System.Threading.EventWaitHandleAcl.OpenExisting(System.String,System.Security.AccessControl.EventWaitHandleRights)">
            <summary>
            Opens a specified named event wait handle, if it already exists, applying the desired access rights.
            </summary>
            <param name="name">The name of the event wait handle to be opened. If it's prefixed by "Global", it refers to a machine-wide event wait handle. If it's prefixed by "Local", or doesn't have a prefix, it refers to a session-wide event wait handle. Both prefix and name are case-sensitive.</param>
            <param name="rights">The desired access rights to apply to the returned event wait handle.</param>
            <returns>An existing named event wait handle.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="name"/> is <see langword="null" />.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="name"/> is an empty string.</exception>
            <exception cref="T:System.Threading.WaitHandleCannotBeOpenedException">The named event wait handle does not exist or is invalid.</exception>
            <exception cref="T:System.IO.DirectoryNotFoundException">The path was not found.</exception>
            <exception cref="T:System.IO.IOException">A Win32 error occurred.</exception>
            <exception cref="T:System.UnauthorizedAccessException">The named event wait handle exists, but the user does not have the security access required to use it.</exception>
        </member>
        <member name="M:System.Threading.EventWaitHandleAcl.TryOpenExisting(System.String,System.Security.AccessControl.EventWaitHandleRights,System.Threading.EventWaitHandle@)">
            <summary>
            Tries to open a specified named event wait handle, if it already exists, applying the desired access rights, and returns a value that indicates whether the operation succeeded.
            </summary>
            <param name="name">The name of the event wait handle to be opened. If it's prefixed by "Global", it refers to a machine-wide event wait handle. If it's prefixed by "Local", or doesn't have a prefix, it refers to a session-wide event wait handle. Both prefix and name are case-sensitive.</param>
            <param name="rights">The desired access rights to apply to the returned event wait handle.</param>
            <param name="result">When this method returns <see langword="true" />, contains an object that represents the named event wait handle if the call succeeded, or <see langword="null" /> otherwise. This parameter is treated as uninitialized.</param>
            <returns><see langword="true" /> if the named event wait handle was opened successfully; otherwise, <see langword="false" />.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="name"/> is <see langword="null" /></exception>
            <exception cref="T:System.ArgumentException"><paramref name="name"/> is an empty string.</exception>
            <exception cref="T:System.IO.IOException">A Win32 error occurred.</exception>
            <exception cref="T:System.UnauthorizedAccessException">The named event wait handle exists, but the user does not have the security access required to use it.</exception>
        </member>
        <member name="M:System.Threading.MutexAcl.Create(System.Boolean,System.String,System.Boolean@,System.Security.AccessControl.MutexSecurity)">
            <summary>Gets or creates <see cref="T:System.Threading.Mutex" /> instance, allowing a <see cref="T:System.Security.AccessControl.MutexSecurity" /> to be optionally specified to set it during the mutex creation.</summary>
            <param name="initiallyOwned"><see langword="true" /> to give the calling thread initial ownership of the named system mutex if the named system mutex is created as a result of this call; otherwise, <see langword="false" />.</param>
            <param name="name">The optional name of the system mutex. If this argument is set to <see langword="null" /> or <see cref="F:System.String.Empty" />, a local mutex is created.</param>
            <param name="createdNew">When this method returns, this argument is always set to <see langword="true" /> if a local mutex is created; that is, when <paramref name="name" /> is <see langword="null" /> or <see cref="F:System.String.Empty" />. If <paramref name="name" /> has a valid non-empty value, this argument is set to <see langword="true" /> when the system mutex is created, or it is set to <see langword="false" /> if an existing system mutex is found with that name. This parameter is passed uninitialized.</param>
            <param name="mutexSecurity">The optional mutex access control security to apply.</param>
            <returns>An object that represents a system mutex, if named, or a local mutex, if nameless.</returns>
            <exception cref="T:System.ArgumentException">.NET Framework only: The length of the name exceeds the maximum limit.</exception>
            <exception cref="T:System.Threading.WaitHandleCannotBeOpenedException">A mutex handle with system-wide <paramref name="name" /> cannot be created. A mutex handle of a different type might have the same name.</exception>
        </member>
        <member name="M:System.Threading.MutexAcl.OpenExisting(System.String,System.Security.AccessControl.MutexRights)">
            <summary>
            Opens a specified named mutex, if it already exists, applying the desired access rights.
            </summary>
            <param name="name">The name of the mutex to be opened. If it's prefixed by "Global", it refers to a machine-wide mutex. If it's prefixed by "Local", or doesn't have a prefix, it refers to a session-wide mutex. Both prefix and name are case-sensitive.</param>
            <param name="rights">The desired access rights to apply to the returned mutex.</param>
            <returns>An existing named mutex.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="name"/> is <see langword="null" />.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="name"/> is an empty string.</exception>
            <exception cref="T:System.Threading.WaitHandleCannotBeOpenedException">The named mutex does not exist or is invalid.</exception>
            <exception cref="T:System.IO.DirectoryNotFoundException">The path was not found.</exception>
            <exception cref="T:System.IO.IOException">A Win32 error occurred.</exception>
            <exception cref="T:System.UnauthorizedAccessException">The named mutex exists, but the user does not have the security access required to use it.</exception>
        </member>
        <member name="M:System.Threading.MutexAcl.TryOpenExisting(System.String,System.Security.AccessControl.MutexRights,System.Threading.Mutex@)">
            <summary>
            Tries to open a specified named mutex, if it already exists, applying the desired access rights, and returns a value that indicates whether the operation succeeded.
            </summary>
            <param name="name">The name of the mutex to be opened. If it's prefixed by "Global", it refers to a machine-wide mutex. If it's prefixed by "Local", or doesn't have a prefix, it refers to a session-wide mutex. Both prefix and name are case-sensitive.</param>
            <param name="rights">The desired access rights to apply to the returned mutex.</param>
            <param name="result">When this method returns <see langword="true" />, contains an object that represents the named mutex if the call succeeded, or <see langword="null" /> otherwise. This parameter is treated as uninitialized.</param>
            <returns><see langword="true" /> if the named mutex was opened successfully; otherwise, <see langword="false" />.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="name"/> is <see langword="null" /></exception>
            <exception cref="T:System.ArgumentException"><paramref name="name"/> is an empty string.</exception>
            <exception cref="T:System.IO.IOException">A Win32 error occurred.</exception>
            <exception cref="T:System.UnauthorizedAccessException">The named mutex exists, but the user does not have the security access required to use it.</exception>
        </member>
        <member name="M:System.Threading.SemaphoreAcl.Create(System.Int32,System.Int32,System.String,System.Boolean@,System.Security.AccessControl.SemaphoreSecurity)">
            <summary>Gets or creates an <see cref="T:System.Threading.Semaphore" /> instance, allowing a <see cref="T:System.Security.AccessControl.SemaphoreSecurity" /> instance to be optionally specified to set it during the event creation.</summary>
            <param name="initialCount">The initial number of requests for the semaphore that can be satisfied concurrently.</param>
            <param name="maximumCount">The maximum number of requests for the semaphore that can be satisfied concurrently.</param>
            <param name="name">Optional argument to create a system semaphore. Set to <see langword="null" /> or <see cref="F:System.String.Empty" /> to create a local semaphore.</param>
            <param name="createdNew">When this method returns, this argument is always set to <see langword="true" /> if a local semaphore is created; that is, when <paramref name="name" /> is <see langword="null" /> or <see cref="F:System.String.Empty" />. If <paramref name="name" /> has a valid, non-empty value, this argument is set to <see langword="true" /> when the system semaphore is created, or it is set to <see langword="false" /> if an existing system semaphore is found with that name. This parameter is passed uninitialized.</param>
            <param name="semaphoreSecurity">The optional semaphore access control security to apply.</param>
            <returns>An object that represents a system semaphore, if named, or a local semaphore, if nameless.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="initialCount" /> is a negative number.
            -or-
            <paramref name="maximumCount" /> is not a positive number.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="initialCount" /> is greater than <paramref name="maximumCount" />.</exception>
            <exception cref="T:System.Threading.WaitHandleCannotBeOpenedException">A semaphore handle with the system-wide name '<paramref name="name" />' cannot be created. A semaphore handle of a different type might have the same name.</exception>
        </member>
        <member name="M:System.Threading.SemaphoreAcl.OpenExisting(System.String,System.Security.AccessControl.SemaphoreRights)">
            <summary>
            Opens a specified named semaphore, if it already exists, applying the desired access rights.
            </summary>
            <param name="name">The name of the semaphore to be opened. If it's prefixed by "Global", it refers to a machine-wide semaphore. If it's prefixed by "Local", or doesn't have a prefix, it refers to a session-wide semaphore. Both prefix and name are case-sensitive.</param>
            <param name="rights">The desired access rights to apply to the returned semaphore.</param>
            <returns>An existing named semaphore.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="name"/> is <see langword="null" />.</exception>
            <exception cref="T:System.ArgumentException"><paramref name="name"/> is an empty string.</exception>
            <exception cref="T:System.Threading.WaitHandleCannotBeOpenedException">The named semaphore does not exist or is invalid.</exception>
            <exception cref="T:System.IO.IOException">The path was not found.
            -or-
            A Win32 error occurred.</exception>
            <exception cref="T:System.UnauthorizedAccessException">The named semaphore exists, but the user does not have the security access required to use it.</exception>
        </member>
        <member name="M:System.Threading.SemaphoreAcl.TryOpenExisting(System.String,System.Security.AccessControl.SemaphoreRights,System.Threading.Semaphore@)">
            <summary>
            Tries to open a specified named semaphore, if it already exists, applying the desired access rights, and returns a value that indicates whether the operation succeeded.
            </summary>
            <param name="name">The name of the semaphore to be opened. If it's prefixed by "Global", it refers to a machine-wide semaphore. If it's prefixed by "Local", or doesn't have a prefix, it refers to a session-wide semaphore. Both prefix and name are case-sensitive.</param>
            <param name="rights">The desired access rights to apply to the returned semaphore.</param>
            <param name="result">When this method returns <see langword="true" />, contains an object that represents the named semaphore if the call succeeded, or <see langword="null" /> otherwise. This parameter is treated as uninitialized.</param>
            <returns><see langword="true" /> if the named semaphore was opened successfully; otherwise, <see langword="false" />.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="name"/> is <see langword="null" /></exception>
            <exception cref="T:System.ArgumentException"><paramref name="name"/> is an empty string.</exception>
            <exception cref="T:System.IO.IOException">A Win32 error occurred.</exception>
            <exception cref="T:System.UnauthorizedAccessException">The named semaphore exists, but the user does not have the security access required to use it.</exception>
        </member>
        <member name="P:System.SR.ArgumentOutOfRange_Enum">
            <summary>Enum value was out of legal range.</summary>
        </member>
        <member name="P:System.SR.ArgumentOutOfRange_NeedNonNegNum">
            <summary>Non-negative number required.</summary>
        </member>
        <member name="P:System.SR.ArgumentOutOfRange_NeedPosNum">
            <summary>Positive number required.</summary>
        </member>
        <member name="P:System.SR.Argument_CannotBeNullOrEmpty">
            <summary>Argument cannot be null or empty.</summary>
        </member>
        <member name="P:System.SR.Argument_EmptyName">
            <summary>Empty name is not legal.</summary>
        </member>
        <member name="P:System.SR.Argument_SemaphoreInitialMaximum">
            <summary>The initial count for the semaphore must be greater than or equal to zero and less than the maximum count.</summary>
        </member>
        <member name="P:System.SR.Argument_WaitHandleNameTooLong">
            <summary>The length of the name exceeds the maximum limit.</summary>
        </member>
        <member name="P:System.SR.IO_AlreadyExists_Name">
            <summary>Cannot create '{0}' because a file or directory with the same name already exists.</summary>
        </member>
        <member name="P:System.SR.IO_FileExists_Name">
            <summary>The file '{0}' already exists.</summary>
        </member>
        <member name="P:System.SR.IO_FileNotFound">
            <summary>Unable to find the specified file.</summary>
        </member>
        <member name="P:System.SR.IO_FileNotFound_FileName">
            <summary>Could not find file '{0}'.</summary>
        </member>
        <member name="P:System.SR.IO_PathNotFound_NoPathName">
            <summary>Could not find a part of the path.</summary>
        </member>
        <member name="P:System.SR.IO_PathNotFound_Path">
            <summary>Could not find a part of the path '{0}'.</summary>
        </member>
        <member name="P:System.SR.IO_PathTooLong">
            <summary>The specified file name or path is too long, or a component of the specified path is too long.</summary>
        </member>
        <member name="P:System.SR.IO_PathTooLong_Path">
            <summary>The path '{0}' is too long, or a component of the specified path is too long.</summary>
        </member>
        <member name="P:System.SR.IO_SharingViolation_File">
            <summary>The process cannot access the file '{0}' because it is being used by another process.</summary>
        </member>
        <member name="P:System.SR.IO_SharingViolation_NoFileName">
            <summary>The process cannot access the file because it is being used by another process.</summary>
        </member>
        <member name="P:System.SR.PlatformNotSupported_AccessControl">
            <summary>Access Control List (ACL) APIs are part of resource management on Windows and are not supported on this platform.</summary>
        </member>
        <member name="P:System.SR.Threading_WaitHandleCannotBeOpenedException_InvalidHandle">
            <summary>A WaitHandle with system-wide name '{0}' cannot be created. A WaitHandle of a different type might have the same name.</summary>
        </member>
        <member name="P:System.SR.UnauthorizedAccess_IODenied_NoPathName">
            <summary>Access to the path is denied.</summary>
        </member>
        <member name="P:System.SR.UnauthorizedAccess_IODenied_Path">
            <summary>Access to the path '{0}' is denied.</summary>
        </member>
        <member name="P:System.SR.WaitHandleCannotBeOpenedException_InvalidHandle">
            <summary>A WaitHandle with system-wide name '{0}' cannot be created. A WaitHandle of a different type might have the same name.</summary>
        </member>
    </members>
</doc>
