﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Windows.Forms.Primitives</name>
  </assembly>
  <members>
    <member name="T:System.Windows.Forms.Automation.AutomationLiveSetting">
      <summary>Describes the notification characteristics of a particular live region.</summary>
    </member>
    <member name="F:System.Windows.Forms.Automation.AutomationLiveSetting.Assertive">
      <summary>The element sends interruptive notifications if the content of the live region has changed. With this setting, UI Automation clients and assistive technologies are expected to interrupt the user to inform of changes to the live region.</summary>
    </member>
    <member name="F:System.Windows.Forms.Automation.AutomationLiveSetting.Off">
      <summary>The element doesn't send notifications if the content of the live region has changed.</summary>
    </member>
    <member name="F:System.Windows.Forms.Automation.AutomationLiveSetting.Polite">
      <summary>The element sends non-interruptive notifications if the content of the live region has changed. With this setting, UI Automation clients and assistive technologies are expected to not interrupt the user to inform of changes to the live region.</summary>
    </member>
    <member name="T:System.Windows.Forms.Automation.AutomationNotificationKind">
      <summary>Indicates the type of notification when raising the UI automation notification event.</summary>
    </member>
    <member name="F:System.Windows.Forms.Automation.AutomationNotificationKind.ActionAborted">
      <summary>The current element has a notification that an action was abandoned.</summary>
    </member>
    <member name="F:System.Windows.Forms.Automation.AutomationNotificationKind.ActionCompleted">
      <summary>The current element has a notification that an action was completed.</summary>
    </member>
    <member name="F:System.Windows.Forms.Automation.AutomationNotificationKind.ItemAdded">
      <summary>Something that should be presented to the user was added to the current element.</summary>
    </member>
    <member name="F:System.Windows.Forms.Automation.AutomationNotificationKind.ItemRemoved">
      <summary>Something that should be presented to the user was removed from the current element.</summary>
    </member>
    <member name="F:System.Windows.Forms.Automation.AutomationNotificationKind.Other">
      <summary>The current element has a notification that is not due to an addition or removal of something, or to a completed or aborted action.</summary>
    </member>
    <member name="T:System.Windows.Forms.Automation.AutomationNotificationProcessing">
      <summary>Specifies the order in which to process a notification.</summary>
    </member>
    <member name="F:System.Windows.Forms.Automation.AutomationNotificationProcessing.All">
      <summary>These notifications should be presented to the user when possible. All the notifications from this source should be delivered to the user.</summary>
    </member>
    <member name="F:System.Windows.Forms.Automation.AutomationNotificationProcessing.CurrentThenMostRecent">
      <summary>These notifications should be presented to the user when possible. don't interrupt the current notification for this one. If new notifications come in from the same source while the current notification is being presented, keep the most recent and ignore the rest until the current processing is completed. Then use the most recent message as the current message.</summary>
    </member>
    <member name="F:System.Windows.Forms.Automation.AutomationNotificationProcessing.ImportantAll">
      <summary>These notifications should be presented to the user as soon as possible. All of the notifications from this source should be delivered to the user.<br /><br />IMPORTANT: Limit the use of this option, since delivery of all notifications to the user can cause a overwhelming flood of information.</summary>
    </member>
    <member name="F:System.Windows.Forms.Automation.AutomationNotificationProcessing.ImportantMostRecent">
      <summary>These notifications should be presented to the user as soon as possible. The most recent notifications from this source should be delivered to the user because the most recent notification supersedes all other notifications.</summary>
    </member>
    <member name="F:System.Windows.Forms.Automation.AutomationNotificationProcessing.MostRecent">
      <summary>These notifications should be presented to the user when possible. Interrupt the current notification for this one.</summary>
    </member>
    <member name="T:System.Windows.Forms.Automation.IAutomationLiveRegion">
      <summary>Provides support for UI automation live regions.</summary>
    </member>
    <member name="P:System.Windows.Forms.Automation.IAutomationLiveRegion.LiveSetting">
      <summary>Gets or sets the notification characteristics of the live region.</summary>
      <returns>The notification characteristics of the live region.</returns>
    </member>
    <member name="T:System.Windows.Forms.FileDialogCustomPlace">
      <summary>Represents an entry in a <see cref="T:System.Windows.Forms.FileDialog" /> custom place collection for Windows Vista.</summary>
    </member>
    <member name="M:System.Windows.Forms.FileDialogCustomPlace.#ctor(System.Guid)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Forms.FileDialogCustomPlace" /> class with a custom place identified by a Windows Vista Known Folder GUID.</summary>
      <param name="knownFolderGuid">A <see cref="T:System.Guid" /> that represents a Windows Vista Known Folder.</param>
    </member>
    <member name="M:System.Windows.Forms.FileDialogCustomPlace.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Forms.FileDialogCustomPlace" /> class. with a specified folder path to a custom place.</summary>
      <param name="path">A folder path to the custom place.</param>
    </member>
    <member name="M:System.Windows.Forms.FileDialogCustomPlace.ToString">
      <summary>Returns a string that represents this <see cref="T:System.Windows.Forms.FileDialogCustomPlace" /> instance.</summary>
      <returns>A string that represents this <see cref="T:System.Windows.Forms.FileDialogCustomPlace" /> instance.</returns>
    </member>
    <member name="P:System.Windows.Forms.FileDialogCustomPlace.KnownFolderGuid">
      <summary>Gets or sets the Windows Vista Known Folder GUID for the custom place.</summary>
      <returns>A <see cref="T:System.Guid" /> that indicates the Windows Vista Known Folder for the custom place. If the custom place was specified with a folder path, then an empty GUID is returned. For a list of the available Windows Vista Known Folders, see Known Folder GUIDs for File Dialog Custom Places or the KnownFolders.h file in the Windows SDK.</returns>
    </member>
    <member name="P:System.Windows.Forms.FileDialogCustomPlace.Path">
      <summary>Gets or sets the folder path to the custom place.</summary>
      <returns>A folder path to the custom place. If the custom place was specified with a Windows Vista Known Folder GUID, then an empty string is returned.</returns>
    </member>
    <member name="T:System.Windows.Forms.FileDialogCustomPlacesCollection">
      <summary>Represents a collection of Windows Vista custom places for the <see cref="T:System.Windows.Forms.FileDialog" /> class.</summary>
    </member>
    <member name="M:System.Windows.Forms.FileDialogCustomPlacesCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Forms.FileDialogCustomPlacesCollection" /> class.</summary>
    </member>
    <member name="M:System.Windows.Forms.FileDialogCustomPlacesCollection.Add(System.Guid)">
      <summary>Adds a custom place to the <see cref="T:System.Windows.Forms.FileDialogCustomPlacesCollection" /> collection.</summary>
      <param name="knownFolderGuid">A <see cref="T:System.Guid" /> that represents a Windows Vista Known Folder.</param>
    </member>
    <member name="M:System.Windows.Forms.FileDialogCustomPlacesCollection.Add(System.String)">
      <summary>Adds a custom place to the <see cref="T:System.Windows.Forms.FileDialogCustomPlacesCollection" /> collection.</summary>
      <param name="path">A folder path to the custom place.</param>
    </member>
    <member name="T:System.Windows.Forms.HighDpiMode">
      <summary>Specifies the different high DPI modes that can be applied to an application.</summary>
    </member>
    <member name="F:System.Windows.Forms.HighDpiMode.DpiUnaware">
      <summary>The application window does not scale for DPI changes and always assumes a scale factor of 100%.</summary>
    </member>
    <member name="F:System.Windows.Forms.HighDpiMode.DpiUnawareGdiScaled">
      <summary>Similar to <see cref="F:System.Windows.Forms.HighDpiMode.DpiUnaware" />, but improves the quality of GDI/GDI+ based content.</summary>
    </member>
    <member name="F:System.Windows.Forms.HighDpiMode.PerMonitor">
      <summary>The window checks for DPI when it's created and adjusts scale factor when the DPI changes.</summary>
    </member>
    <member name="F:System.Windows.Forms.HighDpiMode.PerMonitorV2">
      <summary>Similar to <see cref="F:System.Windows.Forms.HighDpiMode.PerMonitor" />, but enables child window DPI change notification, improved scaling of comctl32 controls, and dialog scaling.</summary>
    </member>
    <member name="F:System.Windows.Forms.HighDpiMode.SystemAware">
      <summary>The window queries for the DPI of the primary monitor once and uses this for the application on all monitors.</summary>
    </member>
    <member name="T:System.Windows.Forms.Message">
      <summary>Implements a Windows message.</summary>
    </member>
    <member name="M:System.Windows.Forms.Message.Create(System.IntPtr,System.Int32,System.IntPtr,System.IntPtr)">
      <summary>Creates a new <see cref="T:System.Windows.Forms.Message" />.</summary>
      <param name="hWnd">The window handle that the message is for.</param>
      <param name="msg">The message ID.</param>
      <param name="wparam">The message <paramref name="wparam" /> field.</param>
      <param name="lparam">The message <paramref name="lparam" /> field.</param>
      <returns>A <see cref="T:System.Windows.Forms.Message" /> that represents the message that was created.</returns>
    </member>
    <member name="M:System.Windows.Forms.Message.Equals(System.Object)">
      <summary>Determines whether the specified object is equal to the current object.</summary>
      <param name="o">The object to compare with the current object.</param>
      <returns>
        <see langword="true" /> if the specified object is equal to the current object; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Windows.Forms.Message.GetHashCode">
      <summary>Returns the hash code for this instance.</summary>
      <returns>A 32-bit signed integer that is the hash code for this instance.</returns>
    </member>
    <member name="M:System.Windows.Forms.Message.GetLParam(System.Type)">
      <summary>Gets the <see cref="P:System.Windows.Forms.Message.LParam" /> value and converts the value to an object.</summary>
      <param name="cls">The type to use to create an instance. This type must be declared as a structure type.</param>
      <returns>An <see cref="T:System.Object" /> that represents an instance of the class specified by the <paramref name="cls" /> parameter, with the data from the <see cref="P:System.Windows.Forms.Message.LParam" /> field of the message.</returns>
    </member>
    <member name="M:System.Windows.Forms.Message.op_Equality(System.Windows.Forms.Message,System.Windows.Forms.Message)">
      <summary>Determines whether two instances of <see cref="T:System.Windows.Forms.Message" /> are equal.</summary>
      <param name="a">A <see cref="T:System.Windows.Forms.Message" /> to compare to <paramref name="b" />.</param>
      <param name="b">A <see cref="T:System.Windows.Forms.Message" /> to compare to <paramref name="a" />.</param>
      <returns>
        <see langword="true" /> if <paramref name="a" /> and <paramref name="b" /> represent the same <see cref="T:System.Windows.Forms.Message" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Windows.Forms.Message.op_Inequality(System.Windows.Forms.Message,System.Windows.Forms.Message)">
      <summary>Determines whether two instances of <see cref="T:System.Windows.Forms.Message" /> are not equal.</summary>
      <param name="a">A <see cref="T:System.Windows.Forms.Message" /> to compare to <paramref name="b" />.</param>
      <param name="b">A <see cref="T:System.Windows.Forms.Message" /> to compare to <paramref name="a" />.</param>
      <returns>
        <see langword="true" /> if <paramref name="a" /> and <paramref name="b" /> do not represent the same <see cref="T:System.Windows.Forms.Message" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Windows.Forms.Message.ToString">
      <summary>Returns a <see cref="T:System.String" /> that represents the current <see cref="T:System.Windows.Forms.Message" />.</summary>
      <returns>A <see cref="T:System.String" /> that represents the current <see cref="T:System.Windows.Forms.Message" />.</returns>
    </member>
    <member name="P:System.Windows.Forms.Message.HWnd">
      <summary>Gets or sets the window handle of the message.</summary>
      <returns>The window handle of the message.</returns>
    </member>
    <member name="P:System.Windows.Forms.Message.LParam">
      <summary>Specifies the <see cref="P:System.Windows.Forms.Message.LParam" /> field of the message.</summary>
      <returns>The <see cref="P:System.Windows.Forms.Message.LParam" /> field of the message.</returns>
    </member>
    <member name="P:System.Windows.Forms.Message.Msg">
      <summary>Gets or sets the ID number for the message.</summary>
      <returns>The ID number for the message.</returns>
    </member>
    <member name="P:System.Windows.Forms.Message.Result">
      <summary>Specifies the value that is returned to Windows in response to handling the message.</summary>
      <returns>The return value of the message.</returns>
    </member>
    <member name="P:System.Windows.Forms.Message.WParam">
      <summary>Gets or sets the <see cref="P:System.Windows.Forms.Message.WParam" /> field of the message.</summary>
      <returns>The <see cref="P:System.Windows.Forms.Message.WParam" /> field of the message.</returns>
    </member>
    <member name="T:System.Windows.Forms.Padding">
      <summary>Represents padding or margin information associated with a user interface (UI) element.</summary>
    </member>
    <member name="F:System.Windows.Forms.Padding.Empty">
      <summary>Provides a <see cref="T:System.Windows.Forms.Padding" /> object with no padding.</summary>
    </member>
    <member name="M:System.Windows.Forms.Padding.#ctor(System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Forms.Padding" /> class using the supplied padding size for all edges.</summary>
      <param name="all">The number of pixels to be used for padding for all edges.</param>
    </member>
    <member name="M:System.Windows.Forms.Padding.#ctor(System.Int32,System.Int32,System.Int32,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Forms.Padding" /> class using a separate padding size for each edge.</summary>
      <param name="left">The padding size, in pixels, for the left edge.</param>
      <param name="top">The padding size, in pixels, for the top edge.</param>
      <param name="right">The padding size, in pixels, for the right edge.</param>
      <param name="bottom">The padding size, in pixels, for the bottom edge.</param>
    </member>
    <member name="M:System.Windows.Forms.Padding.Add(System.Windows.Forms.Padding,System.Windows.Forms.Padding)">
      <summary>Computes the sum of the two specified <see cref="T:System.Windows.Forms.Padding" /> values.</summary>
      <param name="p1">A <see cref="T:System.Windows.Forms.Padding" />.</param>
      <param name="p2">A <see cref="T:System.Windows.Forms.Padding" />.</param>
      <returns>A <see cref="T:System.Windows.Forms.Padding" /> that contains the sum of the two specified <see cref="T:System.Windows.Forms.Padding" /> values.</returns>
    </member>
    <member name="M:System.Windows.Forms.Padding.Equals(System.Object)">
      <summary>Determines whether the value of the specified object is equivalent to the current <see cref="T:System.Windows.Forms.Padding" />.</summary>
      <param name="other">The object to compare to the current <see cref="T:System.Windows.Forms.Padding" />.</param>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Windows.Forms.Padding" /> objects are equivalent; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Windows.Forms.Padding.GetHashCode">
      <summary>Generates a hash code for the current <see cref="T:System.Windows.Forms.Padding" />.</summary>
      <returns>A 32-bit signed integer hash code.</returns>
    </member>
    <member name="M:System.Windows.Forms.Padding.op_Addition(System.Windows.Forms.Padding,System.Windows.Forms.Padding)">
      <summary>Performs vector addition on the two specified <see cref="T:System.Windows.Forms.Padding" /> objects, resulting in a new <see cref="T:System.Windows.Forms.Padding" />.</summary>
      <param name="p1">The first <see cref="T:System.Windows.Forms.Padding" /> to add.</param>
      <param name="p2">The second <see cref="T:System.Windows.Forms.Padding" /> to add.</param>
      <returns>A new <see cref="T:System.Windows.Forms.Padding" /> that results from adding <paramref name="p1" /> and <paramref name="p2" />.</returns>
    </member>
    <member name="M:System.Windows.Forms.Padding.op_Equality(System.Windows.Forms.Padding,System.Windows.Forms.Padding)">
      <summary>Tests whether two specified <see cref="T:System.Windows.Forms.Padding" /> objects are equivalent.</summary>
      <param name="p1">A <see cref="T:System.Windows.Forms.Padding" /> to test.</param>
      <param name="p2">A <see cref="T:System.Windows.Forms.Padding" /> to test.</param>
      <returns>
        <see langword="true" /> if the two <see cref="T:System.Windows.Forms.Padding" /> objects are equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Windows.Forms.Padding.op_Inequality(System.Windows.Forms.Padding,System.Windows.Forms.Padding)">
      <summary>Tests whether two specified <see cref="T:System.Windows.Forms.Padding" /> objects are not equivalent.</summary>
      <param name="p1">A <see cref="T:System.Windows.Forms.Padding" /> to test.</param>
      <param name="p2">A <see cref="T:System.Windows.Forms.Padding" /> to test.</param>
      <returns>
        <see langword="true" /> if the two <see cref="T:System.Windows.Forms.Padding" /> objects are different; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Windows.Forms.Padding.op_Subtraction(System.Windows.Forms.Padding,System.Windows.Forms.Padding)">
      <summary>Performs vector subtraction on the two specified <see cref="T:System.Windows.Forms.Padding" /> objects, resulting in a new <see cref="T:System.Windows.Forms.Padding" />.</summary>
      <param name="p1">The <see cref="T:System.Windows.Forms.Padding" /> to subtract from (the minuend).</param>
      <param name="p2">The <see cref="T:System.Windows.Forms.Padding" /> to subtract from (the subtrahend).</param>
      <returns>The <see cref="T:System.Windows.Forms.Padding" /> result of subtracting <paramref name="p2" /> from <paramref name="p1" />.</returns>
    </member>
    <member name="M:System.Windows.Forms.Padding.Subtract(System.Windows.Forms.Padding,System.Windows.Forms.Padding)">
      <summary>Subtracts one specified <see cref="T:System.Windows.Forms.Padding" /> value from another.</summary>
      <param name="p1">A <see cref="T:System.Windows.Forms.Padding" />.</param>
      <param name="p2">A <see cref="T:System.Windows.Forms.Padding" />.</param>
      <returns>A <see cref="T:System.Windows.Forms.Padding" /> that contains the result of the subtraction of one specified <see cref="T:System.Windows.Forms.Padding" /> value from another.</returns>
    </member>
    <member name="M:System.Windows.Forms.Padding.ToString">
      <summary>Returns a string that represents the current <see cref="T:System.Windows.Forms.Padding" />.</summary>
      <returns>A <see cref="T:System.String" /> that represents the current <see cref="T:System.Windows.Forms.Padding" />.</returns>
    </member>
    <member name="P:System.Windows.Forms.Padding.All">
      <summary>Gets or sets the padding value for all the edges.</summary>
      <returns>The padding, in pixels, for all edges if the same; otherwise, -1.</returns>
    </member>
    <member name="P:System.Windows.Forms.Padding.Bottom">
      <summary>Gets or sets the padding value for the bottom edge.</summary>
      <returns>The padding, in pixels, for the bottom edge.</returns>
    </member>
    <member name="P:System.Windows.Forms.Padding.Horizontal">
      <summary>Gets the combined padding for the right and left edges.</summary>
      <returns>Gets the sum, in pixels, of the <see cref="P:System.Windows.Forms.Padding.Left" /> and <see cref="P:System.Windows.Forms.Padding.Right" /> padding values.</returns>
    </member>
    <member name="P:System.Windows.Forms.Padding.Left">
      <summary>Gets or sets the padding value for the left edge.</summary>
      <returns>The padding, in pixels, for the left edge.</returns>
    </member>
    <member name="P:System.Windows.Forms.Padding.Right">
      <summary>Gets or sets the padding value for the right edge.</summary>
      <returns>The padding, in pixels, for the right edge.</returns>
    </member>
    <member name="P:System.Windows.Forms.Padding.Size">
      <summary>Gets the padding information in the form of a <see cref="T:System.Drawing.Size" />.</summary>
      <returns>A <see cref="T:System.Drawing.Size" /> containing the padding information.</returns>
    </member>
    <member name="P:System.Windows.Forms.Padding.Top">
      <summary>Gets or sets the padding value for the top edge.</summary>
      <returns>The padding, in pixels, for the top edge.</returns>
    </member>
    <member name="P:System.Windows.Forms.Padding.Vertical">
      <summary>Gets the combined padding for the top and bottom edges.</summary>
      <returns>Gets the sum, in pixels, of the <see cref="P:System.Windows.Forms.Padding.Top" /> and <see cref="P:System.Windows.Forms.Padding.Bottom" /> padding values.</returns>
    </member>
    <member name="T:System.Windows.Forms.PaddingConverter">
      <summary>Provides a type converter to convert <see cref="T:System.Windows.Forms.Padding" /> values to and from various other representations.</summary>
    </member>
    <member name="M:System.Windows.Forms.PaddingConverter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Forms.PaddingConverter" /> class.</summary>
    </member>
    <member name="M:System.Windows.Forms.PaddingConverter.CanConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Type)">
      <summary>Returns whether this converter can convert an object of one type to the type of this converter.</summary>
      <param name="context">An <see cref="T:System.ComponentModel.ITypeDescriptorContext" /> that provides a format context.</param>
      <param name="sourceType">A <see cref="T:System.Type" /> that represents the type you wish to convert from.</param>
      <returns>
        <see langword="true" /> if this object can perform the conversion; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Windows.Forms.PaddingConverter.CanConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Type)">
      <summary>Returns whether this converter can convert the object to the specified type, using the specified context.</summary>
      <param name="context">The format context.</param>
      <param name="destinationType">The type you want to convert to.</param>
      <returns>
        <see langword="true" /> if this converter can perform the conversion; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Windows.Forms.PaddingConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)">
      <summary>Converts the given object to the type of this converter, using the specified context and culture information.</summary>
      <param name="context">An <see cref="T:System.ComponentModel.ITypeDescriptorContext" /> that provides a format context.</param>
      <param name="culture">The <see cref="T:System.Globalization.CultureInfo" /> to use as the current culture.</param>
      <param name="value">The <see cref="T:System.Object" /> to convert.</param>
      <returns>An <see cref="T:System.Object" /> that represents the converted value.</returns>
    </member>
    <member name="M:System.Windows.Forms.PaddingConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)">
      <summary>Converts the given value object to the specified type, using the specified context and culture information.</summary>
      <param name="context">An <see cref="T:System.ComponentModel.ITypeDescriptorContext" /> that provides a format context.</param>
      <param name="culture">A <see cref="T:System.Globalization.CultureInfo" />. If null is passed, the current culture is assumed.</param>
      <param name="value">The <see cref="T:System.Object" /> to convert.</param>
      <param name="destinationType">The <see cref="T:System.Type" /> to convert the value parameter to.</param>
      <returns>An <see cref="T:System.Object" /> that represents the converted value.</returns>
    </member>
    <member name="M:System.Windows.Forms.PaddingConverter.CreateInstance(System.ComponentModel.ITypeDescriptorContext,System.Collections.IDictionary)">
      <summary>Creates an instance of the type that this <see cref="T:System.ComponentModel.TypeConverter" /> is associated with, using the specified context, given a set of property values for the object.</summary>
      <param name="context">An <see cref="T:System.ComponentModel.ITypeDescriptorContext" /> that provides a format context.</param>
      <param name="propertyValues">An <see cref="T:System.Collections.IDictionary" /> of new property values.</param>
      <returns>An <see cref="T:System.Object" /> representing the given <see cref="T:System.Collections.IDictionary" />, or <see langword="null" /> if the object cannot be created. This method always returns <see langword="null" />.</returns>
    </member>
    <member name="M:System.Windows.Forms.PaddingConverter.GetCreateInstanceSupported(System.ComponentModel.ITypeDescriptorContext)">
      <summary>Returns whether changing a value on this object requires a call to <see cref="M:System.ComponentModel.TypeConverter.CreateInstance(System.Collections.IDictionary)" /> to create a new value, using the specified context.</summary>
      <param name="context">An <see cref="T:System.ComponentModel.ITypeDescriptorContext" /> that provides a format context.</param>
      <returns>
        <see langword="true" /> if changing a property on this object requires a call to <see cref="M:System.ComponentModel.TypeConverter.CreateInstance(System.Collections.IDictionary)" /> to create a new value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Windows.Forms.PaddingConverter.GetProperties(System.ComponentModel.ITypeDescriptorContext,System.Object,System.Attribute[])">
      <summary>Returns a collection of properties for the type of array specified by the value parameter, using the specified context and attributes.</summary>
      <param name="context">An <see cref="T:System.ComponentModel.ITypeDescriptorContext" /> that provides a format context.</param>
      <param name="value">An <see cref="T:System.Object" /> that specifies the type of array for which to get properties.</param>
      <param name="attributes">An array of type <see cref="T:System.Attribute" /> that is used as a filter.</param>
      <returns>A <see cref="T:System.ComponentModel.PropertyDescriptorCollection" /> with the properties that are exposed for this data type, or null if there are no properties.</returns>
    </member>
    <member name="M:System.Windows.Forms.PaddingConverter.GetPropertiesSupported(System.ComponentModel.ITypeDescriptorContext)">
      <summary>Returns whether this object supports properties, using the specified context.</summary>
      <param name="context">An <see cref="T:System.ComponentModel.ITypeDescriptorContext" /> that provides a format context.</param>
      <returns>
        <see langword="true" /> if <see cref="Overload:System.ComponentModel.TypeConverter.GetProperties" /> should be called to find the properties of this object; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Windows.Forms.ScreenOrientation">
      <summary>Specifies the angle of the screen.</summary>
    </member>
    <member name="F:System.Windows.Forms.ScreenOrientation.Angle0">
      <summary>The screen is oriented at 0 degrees.</summary>
    </member>
    <member name="F:System.Windows.Forms.ScreenOrientation.Angle180">
      <summary>The screen is oriented at 180 degrees.</summary>
    </member>
    <member name="F:System.Windows.Forms.ScreenOrientation.Angle270">
      <summary>The screen is oriented at 270 degrees.</summary>
    </member>
    <member name="F:System.Windows.Forms.ScreenOrientation.Angle90">
      <summary>The screen is oriented at 90 degrees.</summary>
    </member>
    <member name="T:System.Windows.Forms.VisualStyles.TextMetrics">
      <summary>Provides basic information about the font specified by a visual style for a particular element.</summary>
    </member>
    <member name="P:System.Windows.Forms.VisualStyles.TextMetrics.Ascent">
      <summary>Gets or sets the ascent of characters in the font.</summary>
      <returns>The ascent of characters in the font.</returns>
    </member>
    <member name="P:System.Windows.Forms.VisualStyles.TextMetrics.AverageCharWidth">
      <summary>Gets or sets the average width of characters in the font.</summary>
      <returns>The average width of characters in the font.</returns>
    </member>
    <member name="P:System.Windows.Forms.VisualStyles.TextMetrics.BreakChar">
      <summary>Gets or sets the character used to define word breaks for text justification.</summary>
      <returns>The character used to define word breaks for text justification.</returns>
    </member>
    <member name="P:System.Windows.Forms.VisualStyles.TextMetrics.CharSet">
      <summary>Gets or sets the character set of the font.</summary>
      <returns>One of the <see cref="T:System.Windows.Forms.VisualStyles.TextMetricsCharacterSet" /> values that specifies the character set of the font.</returns>
    </member>
    <member name="P:System.Windows.Forms.VisualStyles.TextMetrics.DefaultChar">
      <summary>Gets or sets the character to be substituted for characters not in the font.</summary>
      <returns>The character to be substituted for characters not in the font.</returns>
    </member>
    <member name="P:System.Windows.Forms.VisualStyles.TextMetrics.Descent">
      <summary>Gets or sets the descent of characters in the font.</summary>
      <returns>The descent of characters in the font.</returns>
    </member>
    <member name="P:System.Windows.Forms.VisualStyles.TextMetrics.DigitizedAspectX">
      <summary>Gets or sets the horizontal aspect of the device for which the font was designed.</summary>
      <returns>The horizontal aspect of the device for which the font was designed.</returns>
    </member>
    <member name="P:System.Windows.Forms.VisualStyles.TextMetrics.DigitizedAspectY">
      <summary>Gets or sets the vertical aspect of the device for which the font was designed.</summary>
      <returns>The vertical aspect of the device for which the font was designed.</returns>
    </member>
    <member name="P:System.Windows.Forms.VisualStyles.TextMetrics.ExternalLeading">
      <summary>Gets or sets the amount of extra leading that the application adds between rows.</summary>
      <returns>The amount of extra leading (space) required between rows.</returns>
    </member>
    <member name="P:System.Windows.Forms.VisualStyles.TextMetrics.FirstChar">
      <summary>Gets or sets the first character defined in the font.</summary>
      <returns>The first character defined in the font.</returns>
    </member>
    <member name="P:System.Windows.Forms.VisualStyles.TextMetrics.Height">
      <summary>Gets or sets the height of characters in the font.</summary>
      <returns>The height of characters in the font.</returns>
    </member>
    <member name="P:System.Windows.Forms.VisualStyles.TextMetrics.InternalLeading">
      <summary>Gets or sets the amount of leading inside the bounds set by the <see cref="P:System.Windows.Forms.VisualStyles.TextMetrics.Height" /> property.</summary>
      <returns>The amount of leading inside the bounds set by the <see cref="P:System.Windows.Forms.VisualStyles.TextMetrics.Height" /> property.</returns>
    </member>
    <member name="P:System.Windows.Forms.VisualStyles.TextMetrics.Italic">
      <summary>Gets or sets a value indicating whether the font is italic.</summary>
      <returns>
        <see langword="true" /> if the font is italic; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Windows.Forms.VisualStyles.TextMetrics.LastChar">
      <summary>Gets or sets the last character defined in the font.</summary>
      <returns>The last character defined in the font.</returns>
    </member>
    <member name="P:System.Windows.Forms.VisualStyles.TextMetrics.MaxCharWidth">
      <summary>Gets or sets the width of the widest character in the font.</summary>
      <returns>The width of the widest character in the font.</returns>
    </member>
    <member name="P:System.Windows.Forms.VisualStyles.TextMetrics.Overhang">
      <summary>Gets or sets the extra width per string that may be added to some synthesized fonts.</summary>
      <returns>The extra width per string that may be added to some synthesized fonts.</returns>
    </member>
    <member name="P:System.Windows.Forms.VisualStyles.TextMetrics.PitchAndFamily">
      <summary>Gets or sets information about the pitch, technology, and family of a physical font.</summary>
      <returns>A bitwise combination of the <see cref="T:System.Windows.Forms.VisualStyles.TextMetricsPitchAndFamilyValues" /> values that specifies the pitch, technology, and family of a physical font.</returns>
    </member>
    <member name="P:System.Windows.Forms.VisualStyles.TextMetrics.StruckOut">
      <summary>Gets or sets a value indicating whether the font specifies a horizontal line through the characters.</summary>
      <returns>
        <see langword="true" /> if the font has a horizontal line through the characters; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Windows.Forms.VisualStyles.TextMetrics.Underlined">
      <summary>Gets or sets a value indicating whether the font is underlined.</summary>
      <returns>
        <see langword="true" /> if the font is underlined; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Windows.Forms.VisualStyles.TextMetrics.Weight">
      <summary>Gets or sets the weight of the font.</summary>
      <returns>The weight of the font.</returns>
    </member>
    <member name="T:System.Windows.Forms.VisualStyles.TextMetricsCharacterSet">
      <summary>Specifies the character set of the font specified by a visual style for a particular element.</summary>
    </member>
    <member name="F:System.Windows.Forms.VisualStyles.TextMetricsCharacterSet.Ansi">
      <summary>The ANSI character set.</summary>
    </member>
    <member name="F:System.Windows.Forms.VisualStyles.TextMetricsCharacterSet.Arabic">
      <summary>The Arabic character set.</summary>
    </member>
    <member name="F:System.Windows.Forms.VisualStyles.TextMetricsCharacterSet.Baltic">
      <summary>The Baltic character set.</summary>
    </member>
    <member name="F:System.Windows.Forms.VisualStyles.TextMetricsCharacterSet.ChineseBig5">
      <summary>The Traditional Chinese character set.</summary>
    </member>
    <member name="F:System.Windows.Forms.VisualStyles.TextMetricsCharacterSet.Default">
      <summary>The default character set.</summary>
    </member>
    <member name="F:System.Windows.Forms.VisualStyles.TextMetricsCharacterSet.EastEurope">
      <summary>The Eastern Europe character set.</summary>
    </member>
    <member name="F:System.Windows.Forms.VisualStyles.TextMetricsCharacterSet.Gb2312">
      <summary>The Gb2312 simplified Chinese character set.</summary>
    </member>
    <member name="F:System.Windows.Forms.VisualStyles.TextMetricsCharacterSet.Greek">
      <summary>The Greek character set.</summary>
    </member>
    <member name="F:System.Windows.Forms.VisualStyles.TextMetricsCharacterSet.Hangul">
      <summary>The Hangul (Korean) character set.</summary>
    </member>
    <member name="F:System.Windows.Forms.VisualStyles.TextMetricsCharacterSet.Hebrew">
      <summary>The Hebrew character set.</summary>
    </member>
    <member name="F:System.Windows.Forms.VisualStyles.TextMetricsCharacterSet.Johab">
      <summary>The Johab (Korean) character set.</summary>
    </member>
    <member name="F:System.Windows.Forms.VisualStyles.TextMetricsCharacterSet.Mac">
      <summary>The Macintosh character set.</summary>
    </member>
    <member name="F:System.Windows.Forms.VisualStyles.TextMetricsCharacterSet.Oem">
      <summary>The OEM character set.</summary>
    </member>
    <member name="F:System.Windows.Forms.VisualStyles.TextMetricsCharacterSet.Russian">
      <summary>The Russian character set.</summary>
    </member>
    <member name="F:System.Windows.Forms.VisualStyles.TextMetricsCharacterSet.ShiftJis">
      <summary>The ShiftJis (Japanese) character set.</summary>
    </member>
    <member name="F:System.Windows.Forms.VisualStyles.TextMetricsCharacterSet.Symbol">
      <summary>The Symbol character set.</summary>
    </member>
    <member name="F:System.Windows.Forms.VisualStyles.TextMetricsCharacterSet.Thai">
      <summary>The Thai character set.</summary>
    </member>
    <member name="F:System.Windows.Forms.VisualStyles.TextMetricsCharacterSet.Turkish">
      <summary>The Turkish character set.</summary>
    </member>
    <member name="F:System.Windows.Forms.VisualStyles.TextMetricsCharacterSet.Vietnamese">
      <summary>The Vietnamese character set.</summary>
    </member>
    <member name="T:System.Windows.Forms.VisualStyles.TextMetricsPitchAndFamilyValues">
      <summary>Specifies information about the pitch, technology, and family of the font specified by a visual style for a particular element.</summary>
    </member>
    <member name="F:System.Windows.Forms.VisualStyles.TextMetricsPitchAndFamilyValues.Device">
      <summary>The font is a device font.</summary>
    </member>
    <member name="F:System.Windows.Forms.VisualStyles.TextMetricsPitchAndFamilyValues.FixedPitch">
      <summary>If this value is set, the font is a variable pitch font. Otherwise, the font is a fixed-pitch font.</summary>
    </member>
    <member name="F:System.Windows.Forms.VisualStyles.TextMetricsPitchAndFamilyValues.TrueType">
      <summary>The font is a TrueType font.</summary>
    </member>
    <member name="F:System.Windows.Forms.VisualStyles.TextMetricsPitchAndFamilyValues.Vector">
      <summary>The font is a vector font.</summary>
    </member>
    <member name="T:System.Windows.Forms.VisualStyles.ThemeSizeType">
      <summary>Specifies the size of the visual style part to retrieve.</summary>
    </member>
    <member name="F:System.Windows.Forms.VisualStyles.ThemeSizeType.Draw">
      <summary>The size that the current visual style uses to draw the part.</summary>
    </member>
    <member name="F:System.Windows.Forms.VisualStyles.ThemeSizeType.Minimum">
      <summary>The minimum size of the visual style part.</summary>
    </member>
    <member name="F:System.Windows.Forms.VisualStyles.ThemeSizeType.True">
      <summary>The size of the visual style part that will best fit the available space.</summary>
    </member>
  </members>
</doc>