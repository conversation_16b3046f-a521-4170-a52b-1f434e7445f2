<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Extensions.Localization.Abstractions</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Extensions.Localization.IStringLocalizer">
            <summary>
            Represents a service that provides localized strings.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Localization.IStringLocalizer.Item(System.String)">
            <summary>
            Gets the string resource with the given name.
            </summary>
            <param name="name">The name of the string resource.</param>
            <returns>The string resource as a <see cref="T:Microsoft.Extensions.Localization.LocalizedString"/>.</returns>
        </member>
        <member name="P:Microsoft.Extensions.Localization.IStringLocalizer.Item(System.String,System.Object[])">
            <summary>
            Gets the string resource with the given name and formatted with the supplied arguments.
            </summary>
            <param name="name">The name of the string resource.</param>
            <param name="arguments">The values to format the string with.</param>
            <returns>The formatted string resource as a <see cref="T:Microsoft.Extensions.Localization.LocalizedString"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Localization.IStringLocalizer.GetAllStrings(System.Boolean)">
            <summary>
            Gets all string resources.
            </summary>
            <param name="includeParentCultures">
            A <see cref="T:System.Boolean"/> indicating whether to include strings from parent cultures.
            </param>
            <returns>The strings.</returns>
        </member>
        <member name="T:Microsoft.Extensions.Localization.IStringLocalizerFactory">
            <summary>
            Represents a factory that creates <see cref="T:Microsoft.Extensions.Localization.IStringLocalizer"/> instances.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Localization.IStringLocalizerFactory.Create(System.Type)">
            <summary>
            Creates an <see cref="T:Microsoft.Extensions.Localization.IStringLocalizer"/> using the <see cref="T:System.Reflection.Assembly"/> and
            <see cref="P:System.Type.FullName"/> of the specified <see cref="T:System.Type"/>.
            </summary>
            <param name="resourceSource">The <see cref="T:System.Type"/>.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Localization.IStringLocalizer"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Localization.IStringLocalizerFactory.Create(System.String,System.String)">
            <summary>
            Creates an <see cref="T:Microsoft.Extensions.Localization.IStringLocalizer"/>.
            </summary>
            <param name="baseName">The base name of the resource to load strings from.</param>
            <param name="location">The location to load resources from.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Localization.IStringLocalizer"/>.</returns>
        </member>
        <member name="T:Microsoft.Extensions.Localization.IStringLocalizer`1">
            <summary>
            Represents an <see cref="T:Microsoft.Extensions.Localization.IStringLocalizer"/> that provides strings for <typeparamref name="T"/>.
            </summary>
            <typeparam name="T">The <see cref="T:System.Type"/> to provide strings for.</typeparam>
        </member>
        <member name="T:Microsoft.Extensions.Localization.LocalizedString">
            <summary>
            A locale specific string.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Localization.LocalizedString.#ctor(System.String,System.String)">
            <summary>
            Creates a new <see cref="T:Microsoft.Extensions.Localization.LocalizedString"/>.
            </summary>
            <param name="name">The name of the string in the resource it was loaded from.</param>
            <param name="value">The actual string.</param>
        </member>
        <member name="M:Microsoft.Extensions.Localization.LocalizedString.#ctor(System.String,System.String,System.Boolean)">
            <summary>
            Creates a new <see cref="T:Microsoft.Extensions.Localization.LocalizedString"/>.
            </summary>
            <param name="name">The name of the string in the resource it was loaded from.</param>
            <param name="value">The actual string.</param>
            <param name="resourceNotFound">Whether the string was not found in a resource. Set this to <c>true</c> to indicate an alternate string value was used.</param>
        </member>
        <member name="M:Microsoft.Extensions.Localization.LocalizedString.#ctor(System.String,System.String,System.Boolean,System.String)">
            <summary>
            Creates a new <see cref="T:Microsoft.Extensions.Localization.LocalizedString"/>.
            </summary>
            <param name="name">The name of the string in the resource it was loaded from.</param>
            <param name="value">The actual string.</param>
            <param name="resourceNotFound">Whether the string was not found in a resource. Set this to <c>true</c> to indicate an alternate string value was used.</param>
            <param name="searchedLocation">The location which was searched for a localization value.</param>
        </member>
        <member name="M:Microsoft.Extensions.Localization.LocalizedString.op_Implicit(Microsoft.Extensions.Localization.LocalizedString)~System.String">
            <summary>
            Implicitly converts the <see cref="T:Microsoft.Extensions.Localization.LocalizedString"/> to a <see cref="T:System.String"/>.
            </summary>
            <param name="localizedString">The string to be implicitly converted.</param>
        </member>
        <member name="P:Microsoft.Extensions.Localization.LocalizedString.Name">
            <summary>
            The name of the string in the resource it was loaded from.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Localization.LocalizedString.Value">
            <summary>
            The actual string.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Localization.LocalizedString.ResourceNotFound">
            <summary>
            Whether the string was not found in a resource. If <c>true</c>, an alternate string value was used.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Localization.LocalizedString.SearchedLocation">
            <summary>
            The location which was searched for a localization value.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Localization.LocalizedString.ToString">
            <summary>
            Returns the actual string.
            </summary>
            <returns>The actual string.</returns>
        </member>
        <member name="T:Microsoft.Extensions.Localization.StringLocalizerExtensions">
            <summary>
            Extension methods for operating on <see cref="T:Microsoft.Extensions.Localization.IStringLocalizer" /> instances.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Localization.StringLocalizerExtensions.GetString(Microsoft.Extensions.Localization.IStringLocalizer,System.String)">
            <summary>
            Gets the string resource with the given name.
            </summary>
            <param name="stringLocalizer">The <see cref="T:Microsoft.Extensions.Localization.IStringLocalizer"/>.</param>
            <param name="name">The name of the string resource.</param>
            <returns>The string resource as a <see cref="T:Microsoft.Extensions.Localization.LocalizedString"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Localization.StringLocalizerExtensions.GetString(Microsoft.Extensions.Localization.IStringLocalizer,System.String,System.Object[])">
            <summary>
            Gets the string resource with the given name and formatted with the supplied arguments.
            </summary>
            <param name="stringLocalizer">The <see cref="T:Microsoft.Extensions.Localization.IStringLocalizer"/>.</param>
            <param name="name">The name of the string resource.</param>
            <param name="arguments">The values to format the string with.</param>
            <returns>The formatted string resource as a <see cref="T:Microsoft.Extensions.Localization.LocalizedString"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Localization.StringLocalizerExtensions.GetAllStrings(Microsoft.Extensions.Localization.IStringLocalizer)">
            <summary>
            Gets all string resources including those for parent cultures.
            </summary>
            <param name="stringLocalizer">The <see cref="T:Microsoft.Extensions.Localization.IStringLocalizer"/>.</param>
            <returns>The string resources.</returns>
        </member>
        <member name="T:Microsoft.Extensions.Localization.StringLocalizer`1">
            <summary>
            Provides strings for <typeparamref name="TResourceSource"/>.
            </summary>
            <typeparam name="TResourceSource">The <see cref="T:System.Type"/> to provide strings for.</typeparam>
        </member>
        <member name="M:Microsoft.Extensions.Localization.StringLocalizer`1.#ctor(Microsoft.Extensions.Localization.IStringLocalizerFactory)">
            <summary>
            Creates a new <see cref="T:Microsoft.Extensions.Localization.StringLocalizer`1"/>.
            </summary>
            <param name="factory">The <see cref="T:Microsoft.Extensions.Localization.IStringLocalizerFactory"/> to use.</param>
        </member>
        <member name="P:Microsoft.Extensions.Localization.StringLocalizer`1.Item(System.String)">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.Extensions.Localization.StringLocalizer`1.Item(System.String,System.Object[])">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Extensions.Localization.StringLocalizer`1.GetAllStrings(System.Boolean)">
            <inheritdoc />
        </member>
    </members>
</doc>
