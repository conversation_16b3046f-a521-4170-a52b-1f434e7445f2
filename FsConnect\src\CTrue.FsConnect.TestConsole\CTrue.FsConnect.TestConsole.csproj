<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net6.0</TargetFramework>
    <LangVersion>8.0</LangVersion>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <PlatformTarget>x64</PlatformTarget>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <PlatformTarget>x64</PlatformTarget>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="CommandLineParser" />
    <PackageReference Include="Serilog" />
    <PackageReference Include="Serilog.Sinks.Console" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\CTrue.FsConnect.Managers\CTrue.FsConnect.Managers.csproj" />
    <ProjectReference Include="..\CTrue.FsConnect\CTrue.FsConnect.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="Microsoft.FlightSimulator.SimConnect">
      <HintPath>..\Dependencies\SimConnect\lib\net461\Microsoft.FlightSimulator.SimConnect.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <None Include="..\Dependencies\SimConnect\build\SimConnect.dll" Visible="false">
      <Link>%(FileName)%(Extension)</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
