using System;
using System.IO;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using NAudio.Wave;
using FlightPig.Models;

namespace FlightPig.Services
{
    /// <summary>
    /// Text-to-speech service using ElevenLabs API
    /// </summary>
    public class TextToSpeechService : IDisposable
    {
        private readonly HttpClient _httpClient;
        private readonly AppSettings _settings;
        private WaveOutEvent _waveOut;
        private readonly object _lockObject = new object();

        public TextToSpeechService(HttpClient httpClient, AppSettings settings)
        {
            _httpClient = httpClient;
            _settings = settings;
            
            if (!string.IsNullOrEmpty(_settings.ElevenLabs.ApiKey))
            {
                _httpClient.DefaultRequestHeaders.Add("xi-api-key", _settings.ElevenLabs.ApiKey);
            }
        }

        /// <summary>
        /// Speak text using the default voice
        /// </summary>
        public async Task SpeakAsync(string text, bool isPilotVoice = false)
        {
            if (!_settings.ElevenLabs.Enabled || string.IsNullOrEmpty(text))
            {
                Console.WriteLine($"TTS: {text}"); // Fallback to console output
                return;
            }

            var voiceId = isPilotVoice ? _settings.ElevenLabs.PilotVoiceId : _settings.ElevenLabs.DefaultVoiceId;
            await SpeakWithVoiceAsync(text, voiceId);
        }

        /// <summary>
        /// Speak text using a specific voice
        /// </summary>
        public async Task SpeakWithVoiceAsync(string text, string voiceId)
        {
            if (!_settings.ElevenLabs.Enabled || string.IsNullOrEmpty(_settings.ElevenLabs.ApiKey))
            {
                Console.WriteLine($"TTS: {text}"); // Fallback to console output
                return;
            }

            try
            {
                Console.WriteLine($"🔊 Speaking: \"{text}\"");
                
                var audioData = await GenerateSpeechAsync(text, voiceId);
                if (audioData != null)
                {
                    await PlayAudioAsync(audioData);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"TTS Error: {ex.Message}");
                Console.WriteLine($"TTS Fallback: {text}"); // Fallback to console output
            }
        }

        /// <summary>
        /// Generate speech audio data from text
        /// </summary>
        private async Task<byte[]> GenerateSpeechAsync(string text, string voiceId)
        {
            try
            {
                var requestBody = new
                {
                    text = text,
                    model_id = "eleven_monolingual_v1",
                    voice_settings = new
                    {
                        stability = _settings.ElevenLabs.Stability,
                        similarity_boost = _settings.ElevenLabs.SimilarityBoost
                    }
                };

                var json = JsonSerializer.Serialize(requestBody);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var url = $"{_settings.ElevenLabs.BaseUrl}/text-to-speech/{voiceId}";
                var response = await _httpClient.PostAsync(url, content);

                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadAsByteArrayAsync();
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"ElevenLabs API Error: {response.StatusCode} - {errorContent}");
                    return null;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error generating speech: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Play audio data through speakers
        /// </summary>
        private async Task PlayAudioAsync(byte[] audioData)
        {
            try
            {
                lock (_lockObject)
                {
                    _waveOut?.Stop();
                    _waveOut?.Dispose();
                }

                using (var audioStream = new MemoryStream(audioData))
                using (var reader = new Mp3FileReader(audioStream))
                {
                    var waveOut = new WaveOutEvent();
                    
                    lock (_lockObject)
                    {
                        _waveOut = waveOut;
                    }

                    waveOut.Volume = (float)_settings.Voice.Volume;
                    waveOut.Init(reader);
                    waveOut.Play();

                    // Wait for playback to complete
                    while (waveOut.PlaybackState == PlaybackState.Playing)
                    {
                        await Task.Delay(100);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error playing audio: {ex.Message}");
            }
            finally
            {
                lock (_lockObject)
                {
                    _waveOut?.Dispose();
                    _waveOut = null;
                }
            }
        }

        /// <summary>
        /// Speak mission information with appropriate voice
        /// </summary>
        public async Task SpeakMissionAsync(Mission mission)
        {
            if (mission == null) return;

            var missionText = $"New mission: {mission.Title}. {mission.Description}";
            await SpeakAsync(missionText, isPilotVoice: true);

            if (mission.Objectives?.Count > 0)
            {
                await Task.Delay(500); // Brief pause
                
                var objectiveText = $"You have {mission.Objectives.Count} objectives:";
                for (int i = 0; i < mission.Objectives.Count; i++)
                {
                    objectiveText += $" {i + 1}. {mission.Objectives[i].Title}.";
                }
                
                await SpeakAsync(objectiveText, isPilotVoice: true);
            }
        }

        /// <summary>
        /// Speak objective completion
        /// </summary>
        public async Task SpeakObjectiveCompletedAsync(Objective objective)
        {
            if (objective == null) return;

            var text = $"Objective completed: {objective.Title}";
            await SpeakAsync(text, isPilotVoice: true);
        }

        /// <summary>
        /// Speak aircraft status information
        /// </summary>
        public async Task SpeakAircraftStatusAsync(AircraftInfo aircraftInfo)
        {
            var statusText = $"Aircraft status: {aircraftInfo.Title}. " +
                           $"Altitude {aircraftInfo.Altitude:F0} feet. " +
                           $"Airspeed {aircraftInfo.AirspeedKnots:F0} knots. " +
                           $"Heading {aircraftInfo.Heading:F0} degrees. " +
                           $"{(aircraftInfo.OnGround ? "On ground" : "In flight")}.";

            await SpeakAsync(statusText, isPilotVoice: true);
        }

        /// <summary>
        /// Speak confirmation messages
        /// </summary>
        public async Task SpeakConfirmationAsync(string message)
        {
            await SpeakAsync(message, isPilotVoice: false);
        }

        /// <summary>
        /// Speak error messages
        /// </summary>
        public async Task SpeakErrorAsync(string error)
        {
            var errorText = $"Error: {error}";
            await SpeakAsync(errorText, isPilotVoice: false);
        }

        /// <summary>
        /// Test TTS with a sample message
        /// </summary>
        public async Task TestSpeechAsync()
        {
            await SpeakAsync("FlightPig text-to-speech is working correctly.", isPilotVoice: false);
            await Task.Delay(1000);
            await SpeakAsync("This is the pilot voice speaking.", isPilotVoice: true);
        }

        /// <summary>
        /// Stop any currently playing audio
        /// </summary>
        public void StopSpeaking()
        {
            lock (_lockObject)
            {
                _waveOut?.Stop();
            }
        }

        public void Dispose()
        {
            lock (_lockObject)
            {
                _waveOut?.Stop();
                _waveOut?.Dispose();
                _waveOut = null;
            }
        }
    }
}
