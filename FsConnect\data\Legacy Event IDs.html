<!DOCTYPE html>
<!-- saved from url=(0084)https://docs.flightsimulator.com/html/Programming_Tools/SimVars/Legacy_Event_IDs.htm -->
<html xmlns="http://www.w3.org/1999/xhtml" class="js-focus-visible" data-js-focus-visible="" lang="en"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <meta http-equiv="X-UA-Compatible">
  
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=3, user-scalable=yes">
  
  <meta name="version" content="2019.0.8">
  
  <link rel="StyleSheet" href="./Legacy Event IDs_files/topic.min.css" type="text/css">
  <link rel="StyleSheet" href="./Legacy Event IDs_files/topic.min.css" type="text/css"> <link rel="StyleSheet" data-skin="true" type="text/css" href="./Legacy Event IDs_files/layout.css">
  <link rel="StyleSheet" data-skin="true" href="./Legacy Event IDs_files/userstyles.css" type="text/css">
  
  
    
    

  
  <meta name="generator" content="Adobe RoboHelp 2020">
  
  
  <link rel="stylesheet" type="text/css" href="./Legacy Event IDs_files/default.css">
  
  
  <meta name="rh-authors" content="Mark Alexander">

  
  
  
  
  <meta name="rh-authors" content="Mark Alexander">
<!--<base href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/">--><base href="."><meta name="search-keywords" content="content"><link rel="shortcut icon" type="image/png" href="https://docs.flightsimulator.com/html/Favicon.png"><title>Legacy Event IDs</title><meta name="rh-index-keywords" content="Variable Lists - Legacy Event IDs"></head>

<body class="rh-BODY-wrapper rh-layout-left-panel-expanded">
  <div class="cookie-widget-holder frameless-hide" id="cookie-status-widget-holder"></div>
<div class="RH-LAYOUT-HEADER-skip-content-container" id="skip-to-content"><a class="RH-LAYOUT-HEADER-skip-content-link" title="Skip To Main Content" href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#rh-topic">Skip To Main Content</a></div>
  <header class="RH-LAYOUT-HEADER-container" role="banner">
    <div class="RH-LAYOUT-HEADER-logo-box  ">
      <div class="RH-LAYOUT-HEADER-logo" id="logo-holder"><a class="rh-layout-HEADER-logo-link" title="Logo" aria-label="Logo" href="https://docs.flightsimulator.com/html/index.htm"></a></div>
      <div class="RH-LAYOUT-HEADER-title" id="topic-title-holder"><a class="rh-layout-HEADER-title-link" href="https://docs.flightsimulator.com/html/index.htm" title="SDK Documentation" aria-label="SDK Documentation">SDK Documentation</a><div class="RH-LAYOUT-HEADER-separator "></div></div>
    </div>
    <div class="RH-LAYOUT-HEADERMENU-container" id="header-menu"></div>
<div class="search-placeholder-class" id="search-with-help"><div class="RH-LAYOUT-SEARCHBOX-search "><div class="rh-layout-SEARCHBOX-searchcontainer" role="search"><div class="RH-LAYOUT-SEARCHBOX-searchbar"><button class="rh-button RH-LAYOUT-SEARCHBOX-search-icon  " title="Search the SDK"></button></div></div></div></div>
</header>
  <main role="main" class="RH-LAYOUT-BODY-container">
    <div class="RH-LAYOUT-SEARCHRESULTS" id="rh-searchresults"><div class="RH-LAYOUT-SEARCHRESULTS-container "><div id="search-results" class="RH-LAYOUT-SEARCHRESULTS-results-outer-box  " role="region"></div></div></div>
    <div class="RH-LAYOUT-LEFTPANEL-container" id="rh-leftpanel"><div tabindex="-1" class="rh-layout-LEFTPANEL-left-panel "><div class="RH-LAYOUT-LEFTPANEL-tab-view  "><div class="RH-LAYOUT-LEFTPANEL-tab-list" role="tablist"><button class="rh-button RH-LAYOUT-LEFTPANEL-tab RH-LAYOUT-LEFTPANEL-selected-tab" id="contents" title="Contents" role="tab" aria-selected="true" aria-controls="contents-panel"><label>Contents</label></button><button class="rh-button RH-LAYOUT-LEFTPANEL-tab  " id="glossary" title="Glossary" tabindex="-1" role="tab" aria-selected="false" aria-controls="glossary-panel"><label>Glossary</label></button></div><div id="contents-panel" class="RH-LAYOUT-LEFTPANEL-tab-panel " role="tabpanel" aria-labelledby="contents"><ul class="rh-layout-simple-list RH-LAYOUT-LEFTPANEL-toc" role="tree" aria-labelledby="contents"><li class="RH-LAYOUT-LEFTPANEL-section RH-LAYOUT-LEFTPANEL-section-level-0" tabindex="-1" role="treeitem" aria-expanded="false" aria-labelledby="Introduction00" aria-selected="false"><div class="RH-LAYOUT-LEFTPANEL-title RH-LAYOUT-LEFTPANEL-collapsed-title RH-LAYOUT-LEFTPANEL-title-level-0" id="Introduction00"><div class="RH-LAYOUT-LEFTPANEL-expand-icon"></div><a class="rh-layout-LEFTPANEL-tree-item-text" title="Introduction" tabindex="-1" href="https://docs.flightsimulator.com/html/Introduction/Introduction.htm">Introduction</a></div></li><li class="RH-LAYOUT-LEFTPANEL-section RH-LAYOUT-LEFTPANEL-section-level-0" tabindex="-1" role="treeitem" aria-expanded="false" aria-labelledby="Developer_Mode01" aria-selected="false"><div class="RH-LAYOUT-LEFTPANEL-title RH-LAYOUT-LEFTPANEL-collapsed-title RH-LAYOUT-LEFTPANEL-title-level-0" id="Developer_Mode01"><div class="RH-LAYOUT-LEFTPANEL-expand-icon"></div><a class="rh-layout-LEFTPANEL-tree-item-text" title="Developer Mode" tabindex="-1" href="https://docs.flightsimulator.com/html/Developer_Mode/Developer_Mode.htm">Developer Mode</a></div></li><li class="RH-LAYOUT-LEFTPANEL-section RH-LAYOUT-LEFTPANEL-section-level-0" tabindex="-1" role="treeitem" aria-expanded="false" aria-labelledby="Asset_Creation02" aria-selected="false"><div class="RH-LAYOUT-LEFTPANEL-title RH-LAYOUT-LEFTPANEL-collapsed-title RH-LAYOUT-LEFTPANEL-title-level-0" id="Asset_Creation02"><div class="RH-LAYOUT-LEFTPANEL-expand-icon"></div><a class="rh-layout-LEFTPANEL-tree-item-text" title="Asset Creation" tabindex="-1" href="https://docs.flightsimulator.com/html/Asset_Creation/Asset_Creation.htm">Asset Creation</a></div></li><li class="RH-LAYOUT-LEFTPANEL-section RH-LAYOUT-LEFTPANEL-section-level-0" tabindex="-1" role="treeitem" aria-expanded="false" aria-labelledby="Content_Configuration03" aria-selected="false"><div class="RH-LAYOUT-LEFTPANEL-title RH-LAYOUT-LEFTPANEL-collapsed-title RH-LAYOUT-LEFTPANEL-title-level-0" id="Content_Configuration03"><div class="RH-LAYOUT-LEFTPANEL-expand-icon"></div><a class="rh-layout-LEFTPANEL-tree-item-text" title="Content Configuration" tabindex="-1" href="https://docs.flightsimulator.com/html/Content_Configuration/Content_Configuration.htm">Content Configuration</a></div></li><li class="RH-LAYOUT-LEFTPANEL-section RH-LAYOUT-LEFTPANEL-section-level-0" tabindex="-1" role="treeitem" aria-expanded="true" aria-labelledby="Programming_Tools04" aria-selected="false"><div class="RH-LAYOUT-LEFTPANEL-title RH-LAYOUT-LEFTPANEL-expanded-title RH-LAYOUT-LEFTPANEL-title-level-0" id="Programming_Tools04"><div class="RH-LAYOUT-LEFTPANEL-collapse-icon"></div><a class="rh-layout-LEFTPANEL-tree-item-text" title="Programming Tools" tabindex="-1" href="https://docs.flightsimulator.com/html/Programming_Tools/Programming_Tools.htm">Programming Tools</a></div><ul class="rh-layout-simple-list" role="group"><li class="RH-LAYOUT-LEFTPANEL-section RH-LAYOUT-LEFTPANEL-section-level-1" tabindex="-1" role="treeitem" aria-expanded="true" aria-labelledby="SimConnect_SDK10" aria-selected="false"><div class="RH-LAYOUT-LEFTPANEL-title RH-LAYOUT-LEFTPANEL-expanded-title RH-LAYOUT-LEFTPANEL-title-level-1" id="SimConnect_SDK10"><div class="RH-LAYOUT-LEFTPANEL-collapse-icon"></div><a class="rh-layout-LEFTPANEL-tree-item-text" title="SimConnect SDK" tabindex="-1" href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/SimConnect_SDK.htm">SimConnect SDK</a></div><ul class="rh-layout-simple-list" role="group"><li class="RH-LAYOUT-LEFTPANEL-section RH-LAYOUT-LEFTPANEL-section-level-2" tabindex="-1" role="treeitem" aria-expanded="false" aria-labelledby="SimConnect_API_Reference20" aria-selected="false"><div class="RH-LAYOUT-LEFTPANEL-title RH-LAYOUT-LEFTPANEL-collapsed-title RH-LAYOUT-LEFTPANEL-title-level-2" id="SimConnect_API_Reference20"><div class="RH-LAYOUT-LEFTPANEL-expand-icon"></div><a class="rh-layout-LEFTPANEL-tree-item-text" title="SimConnect API Reference" tabindex="-1" href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/SimConnect_API_Reference.htm">SimConnect API Reference</a></div></li><li class="RH-LAYOUT-LEFTPANEL-section RH-LAYOUT-LEFTPANEL-section-level-2" tabindex="-1" role="treeitem" aria-expanded="true" aria-labelledby="SimConnect_API_Status21" aria-selected="false"><div class="RH-LAYOUT-LEFTPANEL-title RH-LAYOUT-LEFTPANEL-expanded-title RH-LAYOUT-LEFTPANEL-title-level-2" id="SimConnect_API_Status21"><div class="RH-LAYOUT-LEFTPANEL-collapse-icon"></div><a class="rh-layout-LEFTPANEL-tree-item-text" title="SimConnect API Status" tabindex="-1" href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/SimConnect_API_Status.htm">SimConnect API Status</a></div><ul class="rh-layout-simple-list" role="group"><li class="RH-LAYOUT-LEFTPANEL-title RH-LAYOUT-LEFTPANEL-title-level-3" role="presentation"><a class="rh-layout-LEFTPANEL-tree-item-text" title="Status Of Input Events" role="treeitem" tabindex="-1" aria-selected="false" href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/Status_Of_Input_Events.htm">Status Of Input Events</a></li><li class="RH-LAYOUT-LEFTPANEL-title RH-LAYOUT-LEFTPANEL-title-level-3" role="presentation"><a class="rh-layout-LEFTPANEL-tree-item-text" title="Status Of SimEvents" role="treeitem" aria-selected="false" tabindex="0" href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/Status_Of_SimEvents.htm">Status Of SimEvents</a></li><li class="RH-LAYOUT-LEFTPANEL-title RH-LAYOUT-LEFTPANEL-title-level-3" role="presentation"><a class="rh-layout-LEFTPANEL-tree-item-text" title="Status Of Simulation Variables" role="treeitem" tabindex="-1" aria-selected="false" href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/Status_Of_Simulation_Variables.htm">Status Of Simulation Variables</a></li><li class="RH-LAYOUT-LEFTPANEL-title RH-LAYOUT-LEFTPANEL-title-level-3" role="presentation"><a class="rh-layout-LEFTPANEL-tree-item-text" title="Status Of System Events" role="treeitem" tabindex="-1" aria-selected="false" href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/Status_Of_System_Events.htm">Status Of System Events</a></li></ul></li><li class="RH-LAYOUT-LEFTPANEL-title RH-LAYOUT-LEFTPANEL-title-level-2" role="presentation"><a class="rh-layout-LEFTPANEL-tree-item-text" title="SimConnect INI Definition" role="treeitem" tabindex="-1" href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/SimConnect_INI_Definition.htm" aria-selected="false">SimConnect INI Definition</a></li><li class="RH-LAYOUT-LEFTPANEL-title RH-LAYOUT-LEFTPANEL-title-level-2" role="presentation"><a class="rh-layout-LEFTPANEL-tree-item-text" title="SimConnect CFG Definition" role="treeitem" tabindex="-1" href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/SimConnect_CFG_Definition.htm" aria-selected="false">SimConnect CFG Definition</a></li><li class="RH-LAYOUT-LEFTPANEL-title RH-LAYOUT-LEFTPANEL-title-level-2" role="presentation"><a class="rh-layout-LEFTPANEL-tree-item-text" title="SimConnect XML Definition" role="treeitem" tabindex="-1" href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/SimConnect_XML_Definition.htm" aria-selected="false">SimConnect XML Definition</a></li><li class="RH-LAYOUT-LEFTPANEL-title RH-LAYOUT-LEFTPANEL-title-level-2" role="presentation"><a class="rh-layout-LEFTPANEL-tree-item-text" title="Programming SimConnect Clients using Managed Code" role="treeitem" tabindex="-1" href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Programming_SimConnect_Clients_using_Managed_Code.htm" aria-selected="false">Programming SimConnect Clients using Managed Code</a></li></ul></li><li class="RH-LAYOUT-LEFTPANEL-section RH-LAYOUT-LEFTPANEL-section-level-1" tabindex="-1" role="treeitem" aria-expanded="true" aria-labelledby="Simulation_Variables11" aria-selected="false"><div class="RH-LAYOUT-LEFTPANEL-title RH-LAYOUT-LEFTPANEL-expanded-title RH-LAYOUT-LEFTPANEL-title-level-1" id="Simulation_Variables11"><div class="RH-LAYOUT-LEFTPANEL-collapse-icon"></div><a class="rh-layout-LEFTPANEL-tree-item-text" title="Simulation Variables" tabindex="-1" href="https://docs.flightsimulator.com/html/Programming_Tools/SimVars/Simulation_Variables.htm">Simulation Variables</a></div><ul class="rh-layout-simple-list" role="group"><li class="RH-LAYOUT-LEFTPANEL-title RH-LAYOUT-LEFTPANEL-title-level-2" role="presentation"><a class="rh-layout-LEFTPANEL-tree-item-text" title="General Simulation Variables" role="treeitem" tabindex="-1" aria-selected="false" href="https://docs.flightsimulator.com/html/Programming_Tools/SimVars/General_Simulation_Variables.htm">General Simulation Variables</a></li><li class="RH-LAYOUT-LEFTPANEL-title RH-LAYOUT-LEFTPANEL-title-level-2" role="presentation"><a class="rh-layout-LEFTPANEL-tree-item-text" title="Aircraft Simulation Variables" role="treeitem" tabindex="-1" href="https://docs.flightsimulator.com/html/Programming_Tools/SimVars/Aircraft_Simulation_Variables.htm" aria-selected="false">Aircraft Simulation Variables</a></li><li class="RH-LAYOUT-LEFTPANEL-title RH-LAYOUT-LEFTPANEL-title-level-2" role="presentation"><a class="rh-layout-LEFTPANEL-tree-item-text" title="Audio Simulation Variables" role="treeitem" tabindex="-1" href="https://docs.flightsimulator.com/html/Programming_Tools/SimVars/Audio_Simulation_Variables.htm" aria-selected="false">Audio Simulation Variables</a></li><li class="RH-LAYOUT-LEFTPANEL-title RH-LAYOUT-LEFTPANEL-title-level-2" role="presentation"><a class="rh-layout-LEFTPANEL-tree-item-text" title="Simulation Variable Units" role="treeitem" tabindex="-1" href="https://docs.flightsimulator.com/html/Programming_Tools/SimVars/Simulation_Variable_Units.htm" aria-selected="false">Simulation Variable Units</a></li><li class="RH-LAYOUT-LEFTPANEL-title RH-LAYOUT-LEFTPANEL-selected-title RH-LAYOUT-LEFTPANEL-title-level-2" role="presentation"><div class="RH-LAYOUT-LEFTPANEL-selected-icon "></div><a class="rh-layout-LEFTPANEL-tree-item-text" title="Legacy Event IDs" role="treeitem" tabindex="-1" aria-selected="true" disabled="">Legacy Event IDs</a></li></ul></li><li class="RH-LAYOUT-LEFTPANEL-section RH-LAYOUT-LEFTPANEL-section-level-1" tabindex="-1" role="treeitem" aria-expanded="false" aria-labelledby="WebAssembly12" aria-selected="false"><div class="RH-LAYOUT-LEFTPANEL-title RH-LAYOUT-LEFTPANEL-collapsed-title RH-LAYOUT-LEFTPANEL-title-level-1" id="WebAssembly12"><div class="RH-LAYOUT-LEFTPANEL-expand-icon"></div><a class="rh-layout-LEFTPANEL-tree-item-text" title="WebAssembly" tabindex="-1" href="https://docs.flightsimulator.com/html/Programming_Tools/WASM/WebAssembly.htm">WebAssembly</a></div></li></ul></li><li class="RH-LAYOUT-LEFTPANEL-section RH-LAYOUT-LEFTPANEL-section-level-0" tabindex="-1" role="treeitem" aria-expanded="false" aria-labelledby="Additional_Information05" aria-selected="false"><div class="RH-LAYOUT-LEFTPANEL-title RH-LAYOUT-LEFTPANEL-collapsed-title RH-LAYOUT-LEFTPANEL-title-level-0" id="Additional_Information05"><div class="RH-LAYOUT-LEFTPANEL-expand-icon"></div><a class="rh-layout-LEFTPANEL-tree-item-text" title="Additional Information" tabindex="-1" href="https://docs.flightsimulator.com/html/Additional_Information/Additional_Information.htm">Additional Information</a></div></li></ul></div><div id="index-panel" class="RH-LAYOUT-INDEX-tab-panel frameless-hide" role="tabpanel" aria-labelledby="index"><div class="RH-LAYOUT-INDEX-search-box" role="search" aria-labelledby="index"><div class="RH-LAYOUT-INDEX-search-text-box  "><input type="text" class="RH-LAYOUT-INDEX-search-textfield " placeholder="Search the SDK" aria-controls="index-list" title="Search the SDK" value=""><button class="rh-button RH-LAYOUT-INDEX-search-clear-icon frameless-invisible" title="Clear Search Box" aria-hidden="true"></button></div></div><ul id="index-list" class="rh-layout-simple-list RH-LAYOUT-INDEX-keyword-list rh-layout-LEFTPANEL-tree-item-no-left-margin" role="tree" aria-labelledby="index"><div class="RH-LAYOUT-LEFTPANEL-content-loading"></div></ul></div><div id="glossary-panel" class="RH-LAYOUT-GLOSSARY-tab-panel frameless-hide" role="tabpanel" aria-labelledby="glossary"><div class="RH-LAYOUT-GLOSSARY-search-box" role="search" aria-labelledby="glossary"><div class="RH-LAYOUT-GLOSSARY-search-text-box  "><input type="text" class="RH-LAYOUT-GLOSSARY-search-textfield " placeholder="Search the SDK" aria-controls="glossary-list" title="Search the SDK" value=""><button class="rh-button RH-LAYOUT-GLOSSARY-search-clear-icon frameless-invisible" title="Clear Search Box" aria-hidden="true"></button></div></div><ul id="glossary-list" class="rh-layout-simple-list RH-LAYOUT-GLOSSARY-glossary-list" aria-labelledby="glossary"><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="add-ons" tabindex="0" aria-expanded="false" aria-controls="add-ons"><label>add-ons</label></button><div id="add-ons"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="ADF" tabindex="-1" aria-expanded="false" aria-controls="ADF"><label>ADF</label></button><div id="ADF"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="ADPCM" tabindex="-1" aria-expanded="false" aria-controls="ADPCM"><label>ADPCM</label></button><div id="ADPCM"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="AGL" tabindex="-1" aria-expanded="false" aria-controls="AGL"><label>AGL</label></button><div id="AGL"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="ambisonic" tabindex="-1" aria-expanded="false" aria-controls="ambisonic"><label>ambisonic</label></button><div id="ambisonic"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="AMSL" tabindex="-1" aria-expanded="false" aria-controls="AMSL"><label>AMSL</label></button><div id="AMSL"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="AoA" tabindex="-1" aria-expanded="false" aria-controls="AoA"><label>AoA</label></button><div id="AoA"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="API" tabindex="-1" aria-expanded="false" aria-controls="API"><label>API</label></button><div id="API"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="APU" tabindex="-1" aria-expanded="false" aria-controls="APU"><label>APU</label></button><div id="APU"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="ATC" tabindex="-1" aria-expanded="false" aria-controls="ATC"><label>ATC</label></button><div id="ATC"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="BGL" tabindex="-1" aria-expanded="false" aria-controls="BGL"><label>BGL</label></button><div id="BGL"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="bpp" tabindex="-1" aria-expanded="false" aria-controls="bpp"><label>bpp</label></button><div id="bpp"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="Camber" tabindex="-1" aria-expanded="false" aria-controls="Camber"><label>Camber</label></button><div id="Camber"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="CG" tabindex="-1" aria-expanded="false" aria-controls="CG"><label>CG</label></button><div id="CG"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="CGL" tabindex="-1" aria-expanded="false" aria-controls="CGL"><label>CGL</label></button><div id="CGL"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="Chord" tabindex="-1" aria-expanded="false" aria-controls="Chord"><label>Chord</label></button><div id="Chord"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="CoL" tabindex="-1" aria-expanded="false" aria-controls="CoL"><label>CoL</label></button><div id="CoL"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="dB" tabindex="-1" aria-expanded="false" aria-controls="dB"><label>dB</label></button><div id="dB"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="dBTP" tabindex="-1" aria-expanded="false" aria-controls="dBTP"><label>dBTP</label></button><div id="dBTP"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="DDS" tabindex="-1" aria-expanded="false" aria-controls="DDS"><label>DDS</label></button><div id="DDS"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="de-crab" tabindex="-1" aria-expanded="false" aria-controls="de-crab"><label>de-crab</label></button><div id="de-crab"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="DEM" tabindex="-1" aria-expanded="false" aria-controls="DEM"><label>DEM</label></button><div id="DEM"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="Dihedral" tabindex="-1" aria-expanded="false" aria-controls="Dihedral"><label>Dihedral</label></button><div id="Dihedral"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="DME" tabindex="-1" aria-expanded="false" aria-controls="DME"><label>DME</label></button><div id="DME"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="DoF" tabindex="-1" aria-expanded="false" aria-controls="DoF"><label>DoF</label></button><div id="DoF"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="DRM" tabindex="-1" aria-expanded="false" aria-controls="DRM"><label>DRM</label></button><div id="DRM"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="EGT" tabindex="-1" aria-expanded="false" aria-controls="EGT"><label>EGT</label></button><div id="EGT"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="EPR" tabindex="-1" aria-expanded="false" aria-controls="EPR"><label>EPR</label></button><div id="EPR"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="FAF" tabindex="-1" aria-expanded="false" aria-controls="FAF"><label>FAF</label></button><div id="FAF"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="flaps" tabindex="-1" aria-expanded="false" aria-controls="flaps"><label>flaps</label></button><div id="flaps"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="FOV" tabindex="-1" aria-expanded="false" aria-controls="FOV"><label>FOV</label></button><div id="FOV"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="FSUIPC" tabindex="-1" aria-expanded="false" aria-controls="FSUIPC"><label>FSUIPC</label></button><div id="FSUIPC"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="ft" tabindex="-1" aria-expanded="false" aria-controls="ft"><label>ft</label></button><div id="ft"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="ftlbs" tabindex="-1" aria-expanded="false" aria-controls="ftlbs"><label>ftlbs</label></button><div id="ftlbs"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="GA" tabindex="-1" aria-expanded="false" aria-controls="GA"><label>GA</label></button><div id="GA"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="gallon" tabindex="-1" aria-expanded="false" aria-controls="gallon"><label>gallon</label></button><div id="gallon"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="GDI+" tabindex="-1" aria-expanded="false" aria-controls="GDI+"><label>GDI+</label></button><div id="GDI+"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="glTF" tabindex="-1" aria-expanded="false" aria-controls="glTF"><label>glTF</label></button><div id="glTF"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="GPS" tabindex="-1" aria-expanded="false" aria-controls="GPS"><label>GPS</label></button><div id="GPS"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="GPWS" tabindex="-1" aria-expanded="false" aria-controls="GPWS"><label>GPWS</label></button><div id="GPWS"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="GUID" tabindex="-1" aria-expanded="false" aria-controls="GUID"><label>GUID</label></button><div id="GUID"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="hp" tabindex="-1" aria-expanded="false" aria-controls="hp"><label>hp</label></button><div id="hp"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="IAF" tabindex="-1" aria-expanded="false" aria-controls="IAF"><label>IAF</label></button><div id="IAF"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="ICAO" tabindex="-1" aria-expanded="false" aria-controls="ICAO"><label>ICAO</label></button><div id="ICAO"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="ICAO code" tabindex="-1" aria-expanded="false" aria-controls="ICAO code"><label>ICAO code</label></button><div id="ICAO code"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="ILS" tabindex="-1" aria-expanded="false" aria-controls="ILS"><label>ILS</label></button><div id="ILS"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="Incidence" tabindex="-1" aria-expanded="false" aria-controls="Incidence"><label>Incidence</label></button><div id="Incidence"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="inHg" tabindex="-1" aria-expanded="false" aria-controls="inHg"><label>inHg</label></button><div id="inHg"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="ITT" tabindex="-1" aria-expanded="false" aria-controls="ITT"><label>ITT</label></button><div id="ITT"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="kcas" tabindex="-1" aria-expanded="false" aria-controls="kcas"><label>kcas</label></button><div id="kcas"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="kias" tabindex="-1" aria-expanded="false" aria-controls="kias"><label>kias</label></button><div id="kias"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="knot" tabindex="-1" aria-expanded="false" aria-controls="knot"><label>knot</label></button><div id="knot"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="ktas" tabindex="-1" aria-expanded="false" aria-controls="ktas"><label>ktas</label></button><div id="ktas"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="lbs" tabindex="-1" aria-expanded="false" aria-controls="lbs"><label>lbs</label></button><div id="lbs"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="LDA" tabindex="-1" aria-expanded="false" aria-controls="LDA"><label>LDA</label></button><div id="LDA"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="LKFS" tabindex="-1" aria-expanded="false" aria-controls="LKFS"><label>LKFS</label></button><div id="LKFS"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="LoD" tabindex="-1" aria-expanded="false" aria-controls="LoD"><label>LoD</label></button><div id="LoD"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="LU" tabindex="-1" aria-expanded="false" aria-controls="LU"><label>LU</label></button><div id="LU"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="MAC" tabindex="-1" aria-expanded="false" aria-controls="MAC"><label>MAC</label></button><div id="MAC"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="mach" tabindex="-1" aria-expanded="false" aria-controls="mach"><label>mach</label></button><div id="mach"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="Makefile" tabindex="-1" aria-expanded="false" aria-controls="Makefile"><label>Makefile</label></button><div id="Makefile"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="MOI" tabindex="-1" aria-expanded="false" aria-controls="MOI"><label>MOI</label></button><div id="MOI"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="mph" tabindex="-1" aria-expanded="false" aria-controls="mph"><label>mph</label></button><div id="mph"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="MSL" tabindex="-1" aria-expanded="false" aria-controls="MSL"><label>MSL</label></button><div id="MSL"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="N1" tabindex="-1" aria-expanded="false" aria-controls="N1"><label>N1</label></button><div id="N1"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="N2" tabindex="-1" aria-expanded="false" aria-controls="N2"><label>N2</label></button><div id="N2"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="NDB" tabindex="-1" aria-expanded="false" aria-controls="NDB"><label>NDB</label></button><div id="NDB"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="nm" tabindex="-1" aria-expanded="false" aria-controls="nm"><label>nm</label></button><div id="nm"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="Oswald Efficiency Factor" tabindex="-1" aria-expanded="false" aria-controls="Oswald Efficiency Factor"><label>Oswald Efficiency Factor</label></button><div id="Oswald Efficiency Factor"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="pbh" tabindex="-1" aria-expanded="false" aria-controls="pbh"><label>pbh</label></button><div id="pbh"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="PBR" tabindex="-1" aria-expanded="false" aria-controls="PBR"><label>PBR</label></button><div id="PBR"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="PCM" tabindex="-1" aria-expanded="false" aria-controls="PCM"><label>PCM</label></button><div id="PCM"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="Percent Over 100" tabindex="-1" aria-expanded="false" aria-controls="Percent Over 100"><label>Percent Over 100</label></button><div id="Percent Over 100"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="PID" tabindex="-1" aria-expanded="false" aria-controls="PID"><label>PID</label></button><div id="PID"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="POH" tabindex="-1" aria-expanded="false" aria-controls="POH"><label>POH</label></button><div id="POH"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="POI" tabindex="-1" aria-expanded="false" aria-controls="POI"><label>POI</label></button><div id="POI"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="psf" tabindex="-1" aria-expanded="false" aria-controls="psf"><label>psf</label></button><div id="psf"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="psi" tabindex="-1" aria-expanded="false" aria-controls="psi"><label>psi</label></button><div id="psi"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="quadkey" tabindex="-1" aria-expanded="false" aria-controls="quadkey"><label>quadkey</label></button><div id="quadkey"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="Rankine" tabindex="-1" aria-expanded="false" aria-controls="Rankine"><label>Rankine</label></button><div id="Rankine"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="RNAV" tabindex="-1" aria-expanded="false" aria-controls="RNAV"><label>RNAV</label></button><div id="RNAV"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="RPM" tabindex="-1" aria-expanded="false" aria-controls="RPM"><label>RPM</label></button><div id="RPM"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="RTO" tabindex="-1" aria-expanded="false" aria-controls="RTO"><label>RTO</label></button><div id="RTO"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="RTPC" tabindex="-1" aria-expanded="false" aria-controls="RTPC"><label>RTPC</label></button><div id="RTPC"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="SDF" tabindex="-1" aria-expanded="false" aria-controls="SDF"><label>SDF</label></button><div id="SDF"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="Slug sqft" tabindex="-1" aria-expanded="false" aria-controls="Slug sqft"><label>Slug sqft</label></button><div id="Slug sqft"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="sqft" tabindex="-1" aria-expanded="false" aria-controls="sqft"><label>sqft</label></button><div id="sqft"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="STOL" tabindex="-1" aria-expanded="false" aria-controls="STOL"><label>STOL</label></button><div id="STOL"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="Sweep" tabindex="-1" aria-expanded="false" aria-controls="Sweep"><label>Sweep</label></button><div id="Sweep"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="TIN" tabindex="-1" aria-expanded="false" aria-controls="TIN"><label>TIN</label></button><div id="TIN"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="Twist" tabindex="-1" aria-expanded="false" aria-controls="Twist"><label>Twist</label></button><div id="Twist"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="UI" tabindex="-1" aria-expanded="false" aria-controls="UI"><label>UI</label></button><div id="UI"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="VASI" tabindex="-1" aria-expanded="false" aria-controls="VASI"><label>VASI</label></button><div id="VASI"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="VFR" tabindex="-1" aria-expanded="false" aria-controls="VFR"><label>VFR</label></button><div id="VFR"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="VFS" tabindex="-1" aria-expanded="false" aria-controls="VFS"><label>VFS</label></button><div id="VFS"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="VMO" tabindex="-1" aria-expanded="false" aria-controls="VMO"><label>VMO</label></button><div id="VMO"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="VOR" tabindex="-1" aria-expanded="false" aria-controls="VOR"><label>VOR</label></button><div id="VOR"></div></div></li><li class=""><div class="RH-LAYOUT-GLOSSARY-glossary "><button class="rh-button RH-LAYOUT-GLOSSARY-term  " title="WASM" tabindex="-1" aria-expanded="false" aria-controls="WASM"><label>WASM</label></button><div id="WASM"></div></div></li></ul></div></div></div></div>
    <div class="rh-layout-BODY-main" id="rh-layout-main">
      <div class="RH-LAYOUT-TOOLBAR-toolbar-box" id="rh-toolbar"><div class="RH-LAYOUT-TOOLBAR-toolbar "><div class="rh-layout-TOOLBAR-custom-button-wrapper "><div class="RH-LAYOUT-TOOLBAR-toc-icon-div "><button class="rh-button RH-LAYOUT-TOOLBAR-panel-hide RH-LAYOUT-TOOLBAR-panel-hide  " title="Hide Left Panel"></button></div><div class="rh-layout-TOOLBAR-custom-button-wrapper "><div class="rh-layout-TOOLBAR-tool-list  " role="toolbar"><div class="nav" id="menu"><ul id="menu-close" role="presentation"><li role="presentation"><button class="rh-button RH-LAYOUT-TOOLBAR-button-0  " title="Expand All"><span class="RH-LAYOUT-TOOLBAR-button-icon  "></span></button></li><li role="presentation"><button class="rh-button RH-LAYOUT-TOOLBAR-button-1  " title="Favorites"><span class="RH-LAYOUT-TOOLBAR-button-icon  "></span></button></li><li role="presentation"><a class="RH-LAYOUT-TOOLBAR-menu-close" href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#menu-close">× menu-close</a></li><li role="presentation"><a class="RH-LAYOUT-TOOLBAR-menu" href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#menu">☰ menu</a></li></ul></div></div></div></div></div></div>
      <div class="rh-layout-BODY-body">
        <div class="RH-LAYOUT-CENTERPANEL-container">
          <div class="rh-layout-selectdetails" id="rh-selectdetails"><div class="RH-LAYOUT-CENTERPANEL-top-panel "><div role="region" aria-label="Selected Filters"><ul class="RH-LAYOUT-FILTERS-applied-filters-box  "></ul></div><div><div class="RH-LAYOUT-BCRUMBS-container "><div class="RH-LAYOUT-BCRUMBS-label-div "><span class="RH-LAYOUT-BCRUMBS-label  " title=""></span></div><nav class="rh-layout-BCRUMBS-nav" role="navigation" aria-label="Breadcrumb"><ul class="RH-LAYOUT-BCRUMBS-list-box  "><li class="rh-layout-BCRUMBS-list-item "><a class="RH-LAYOUT-BCRUMBS-brdcrmb-item" title="Programming Tools" href="https://docs.flightsimulator.com/html/Programming_Tools/Programming_Tools.htm">Programming Tools</a></li><li class="rh-layout-BCRUMBS-list-item "><a class="RH-LAYOUT-BCRUMBS-brdcrmb-item" title="Simulation Variables" href="https://docs.flightsimulator.com/html/Programming_Tools/SimVars/Simulation_Variables.htm">Simulation Variables</a></li><li class="rh-layout-BCRUMBS-list-item RH-LAYOUT-BCRUMBS-active-topic"><a class="RH-LAYOUT-BCRUMBS-active-topic" title="Legacy Event IDs">Legacy Event IDs</a></li></ul></nav></div></div></div></div>
          <div class="RH-LAYOUT-CENTERPANEL-topic-box" id="rh-topic"><div>
  <h2 id="legacy_event_id">LEGACY EVENT IDs</h2>
  <p>The EventIds listed here are purely for reference and legacy support.</p>
  <p>&nbsp;</p>
  <h3 id="aircraft-engine">Aircraft Engine</h3>
  <table style="table-layout:auto;">
    <colgroup>
      <col>
      <col>
      <col>
      <col>
    </colgroup>
    <tbody>
      <tr>
        <th>Event ID</th>
        <th>SimConnect Name</th>
        <th>Description</th>
        <th>Multiplayer</th>
      </tr>
      <tr>
        <td><code class="inline">KEY_THROTTLE_FULL</code></td>
        <td>THROTTLE_FULL</td>
        <td>Set throttles max</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_THROTTLE_INCR</code></td>
        <td>THROTTLE_INCR</td>
        <td>Increment throttles</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_THROTTLE_INCR_SMALL</code></td>
        <td>THROTTLE_INCR_SMALL</td>
        <td>Increment throttles small</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_THROTTLE_DECR</code></td>
        <td>THROTTLE_DECR</td>
        <td>Decrement throttles</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_THROTTLE_DECR_SMALL</code></td>
        <td>THROTTLE_DECR_SMALL</td>
        <td>Decrease throttles small</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_THROTTLE_CUT</code></td>
        <td>THROTTLE_CUT</td>
        <td>Set throttles to idle</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_INCREASE_THROTTLE</code></td>
        <td>INCREASE_THROTTLE</td>
        <td>Increment throttles</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_DECREASE_THROTTLE</code></td>
        <td>DECREASE_THROTTLE</td>
        <td>Decrement throttles</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_THROTTLE_SET</code></td>
        <td>THROTTLE_SET</td>
        <td>Set throttles exactly (0- 16383)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AXIS_THROTTLE_SET</code></td>
        <td>AXIS_THROTTLE_SET</td>
        <td>Set throttles (0- 16383)</td>
        <td>Shared Cockpit (Pilot only, transmitted to Co-pilot if in a helicopter, not-transmitted otherwise).</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_THROTTLE1_SET</code></td>
        <td>THROTTLE1_SET</td>
        <td>Set throttle 1 exactly (0 to 16383)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_THROTTLE2_SET</code></td>
        <td>THROTTLE2_SET</td>
        <td>Set throttle 2 exactly (0 to 16383)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_THROTTLE3_SET</code></td>
        <td>THROTTLE3_SET</td>
        <td>Set throttle 3 exactly (0 to 16383)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_THROTTLE4_SET</code></td>
        <td>THROTTLE4_SET</td>
        <td>Set throttle 4 exactly (0 to 16383)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_THROTTLE1_FULL</code></td>
        <td>THROTTLE1_FULL</td>
        <td>Set throttle 1 max</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_THROTTLE1_INCR</code></td>
        <td>THROTTLE1_INCR</td>
        <td>Increment throttle 1</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_THROTTLE1_INCR_SMALL</code></td>
        <td>THROTTLE1_INCR_SMALL</td>
        <td>Increment throttle 1 small</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_THROTTLE1_DECR</code></td>
        <td>THROTTLE1_DECR</td>
        <td>Decrement throttle 1</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_THROTTLE1_CUT</code></td>
        <td>THROTTLE1_CUT</td>
        <td>Set throttle 1 to idle</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_THROTTLE2_FULL</code></td>
        <td>THROTTLE2_FULL</td>
        <td>Set throttle 2 max</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_THROTTLE2_INCR</code></td>
        <td>THROTTLE2_INCR</td>
        <td>Increment throttle 2</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_THROTTLE2_INCR_SMALL</code></td>
        <td>THROTTLE2_INCR_SMALL</td>
        <td>Increment throttle 2 small</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_THROTTLE2_DECR</code></td>
        <td>THROTTLE2_DECR</td>
        <td>Decrement throttle 2</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_THROTTLE2_CUT</code></td>
        <td>THROTTLE2_CUT</td>
        <td>Set throttle 2 to idle</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_THROTTLE3_FULL</code></td>
        <td>THROTTLE3_FULL</td>
        <td>Set throttle 3 max</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_THROTTLE3_INCR</code></td>
        <td>THROTTLE3_INCR</td>
        <td>Increment throttle 3</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_THROTTLE3_INCR_SMALL</code></td>
        <td>THROTTLE3_INCR_SMALL</td>
        <td>Increment throttle 3 small</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_THROTTLE3_DECR</code></td>
        <td>THROTTLE3_DECR</td>
        <td>Decrement throttle 3</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_THROTTLE3_CUT</code></td>
        <td>THROTTLE3_CUT</td>
        <td>Set throttle 3 to idle</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_THROTTLE4_FULL</code></td>
        <td>THROTTLE4_FULL</td>
        <td>Set throttle 1 max</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_THROTTLE4_INCR</code></td>
        <td>THROTTLE4_INCR</td>
        <td>Increment throttle 4</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_THROTTLE4_INCR_SMALL</code></td>
        <td>THROTTLE4_INCR_SMALL</td>
        <td>Increment throttle 4 small</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_THROTTLE4_DECR</code></td>
        <td>THROTTLE4_DECR</td>
        <td>Decrement throttle 4</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_THROTTLE4_CUT</code></td>
        <td>THROTTLE4_CUT</td>
        <td>Set throttle 4 to idle</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_THROTTLE_10</code></td>
        <td>THROTTLE_10</td>
        <td>Set throttles to 10%</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_THROTTLE_20</code></td>
        <td>THROTTLE_20</td>
        <td>Set throttles to 20%</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_THROTTLE_30</code></td>
        <td>THROTTLE_30</td>
        <td>Set throttles to 30%</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_THROTTLE_40</code></td>
        <td>THROTTLE_40</td>
        <td>Set throttles to 40%</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_THROTTLE_50</code></td>
        <td>THROTTLE_50</td>
        <td>Set throttles to 50%</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_THROTTLE_60</code></td>
        <td>THROTTLE_60</td>
        <td>Set throttles to 60%</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_THROTTLE_70</code></td>
        <td>THROTTLE_70</td>
        <td>Set throttles to 70%</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_THROTTLE_80</code></td>
        <td>THROTTLE_80</td>
        <td>Set throttles to 80%</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_THROTTLE_90</code></td>
        <td>THROTTLE_90</td>
        <td>Set throttles to 90%</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AXIS_THROTTLE1_SET</code></td>
        <td>AXIS_THROTTLE1_SET</td>
        <td>Set throttle 1 exactly (-16383 - +16383)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AXIS_THROTTLE2_SET</code></td>
        <td>AXIS_THROTTLE2_SET</td>
        <td>Set throttle 2 exactly (-16383 - +16383)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AXIS_THROTTLE3_SET</code></td>
        <td>AXIS_THROTTLE3_SET</td>
        <td>Set throttle 3 exactly (-16383 - +16383)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AXIS_THROTTLE4_SET</code></td>
        <td>AXIS_THROTTLE4_SET</td>
        <td>Set throttle 4 exactly (-16383 - +16383)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_THROTTLE1_DECR_SMALL</code></td>
        <td>THROTTLE1_DECR_SMALL</td>
        <td>Decrease throttle 1 small</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_THROTTLE2_DECR_SMALL</code></td>
        <td>THROTTLE2_DECR_SMALL</td>
        <td>Decrease throttle 2 small</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_THROTTLE3_DECR_SMALL</code></td>
        <td>THROTTLE3_DECR_SMALL</td>
        <td>Decrease throttle 3 small</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_THROTTLE4_DECR_SMALL</code></td>
        <td>THROTTLE4_DECR_SMALL</td>
        <td>Decrease throttle 4 small</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PROP_PITCH_DECR_SMALL</code></td>
        <td>PROP_PITCH_DECR_SMALL</td>
        <td>Decrease prop levers small</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PROP_PITCH1_DECR_SMALL</code></td>
        <td>PROP_PITCH1_DECR_SMALL</td>
        <td>Decrease prop lever 1 small</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PROP_PITCH2_DECR_SMALL</code></td>
        <td>PROP_PITCH2_DECR_SMALL</td>
        <td>Decrease prop lever 2 small</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PROP_PITCH3_DECR_SMALL</code></td>
        <td>PROP_PITCH3_DECR_SMALL</td>
        <td>Decrease prop lever 3 small</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PROP_PITCH4_DECR_SMALL</code></td>
        <td>PROP_PITCH4_DECR_SMALL</td>
        <td>Decrease prop lever 4 small</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MIXTURE1_RICH</code></td>
        <td>MIXTURE1_RICH</td>
        <td>Set mixture lever 1 to max rich</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MIXTURE1_INCR</code></td>
        <td>MIXTURE1_INCR</td>
        <td>Increment mixture lever 1</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MIXTURE1_INCR_SMALL</code></td>
        <td>MIXTURE1_INCR_SMALL</td>
        <td>Increment mixture lever 1 small</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MIXTURE1_DECR</code></td>
        <td>MIXTURE1_DECR</td>
        <td>Decrement mixture lever 1</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MIXTURE1_LEAN</code></td>
        <td>MIXTURE1_LEAN</td>
        <td>Set mixture lever 1 to max lean</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MIXTURE2_RICH</code></td>
        <td>MIXTURE2_RICH</td>
        <td>Set mixture lever 2 to max rich</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MIXTURE2_INCR</code></td>
        <td>MIXTURE2_INCR</td>
        <td>Increment mixture lever 2</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MIXTURE2_INCR_SMALL</code></td>
        <td>MIXTURE2_INCR_SMALL</td>
        <td>Increment mixture lever 2 small</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MIXTURE2_DECR</code></td>
        <td>MIXTURE2_DECR</td>
        <td>Decrement mixture lever 2</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MIXTURE2_LEAN</code></td>
        <td>MIXTURE2_LEAN</td>
        <td>Set mixture lever 2 to max lean</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MIXTURE3_RICH</code></td>
        <td>MIXTURE3_RICH</td>
        <td>Set mixture lever 3 to max rich</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MIXTURE3_INCR</code></td>
        <td>MIXTURE3_INCR</td>
        <td>Increment mixture lever 3</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MIXTURE3_INCR_SMALL</code></td>
        <td>MIXTURE3_INCR_SMALL</td>
        <td>Increment mixture lever 3 small</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MIXTURE3_DECR</code></td>
        <td>MIXTURE3_DECR</td>
        <td>Decrement mixture lever 3</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MIXTURE3_LEAN</code></td>
        <td>MIXTURE3_LEAN</td>
        <td>Set mixture lever 3 to max lean</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MIXTURE4_RICH</code></td>
        <td>MIXTURE4_RICH</td>
        <td>Set mixture lever 4 to max rich</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MIXTURE4_INCR</code></td>
        <td>MIXTURE4_INCR</td>
        <td>Increment mixture lever 4</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MIXTURE4_INCR_SMALL</code></td>
        <td>MIXTURE4_INCR_SMALL</td>
        <td>Increment mixture lever 4 small</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MIXTURE4_DECR</code></td>
        <td>MIXTURE4_DECR</td>
        <td>Decrement mixture lever 4</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MIXTURE4_LEAN</code></td>
        <td>MIXTURE4_LEAN</td>
        <td>Set mixture lever 4 to max lean</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MIXTURE_SET</code></td>
        <td>MIXTURE_SET</td>
        <td>Set mixture levers to exact value (0 to 16383)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MIXTURE_RICH</code></td>
        <td>MIXTURE_RICH</td>
        <td>Set mixture levers to max rich</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MIXTURE_INCR</code></td>
        <td>MIXTURE_INCR</td>
        <td>Increment mixture levers</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MIXTURE_INCR_SMALL</code></td>
        <td>MIXTURE_INCR_SMALL</td>
        <td>Increment mixture levers small</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MIXTURE_DECR</code></td>
        <td>MIXTURE_DECR</td>
        <td>Decrement mixture levers</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MIXTURE_LEAN</code></td>
        <td>MIXTURE_LEAN</td>
        <td>Set mixture levers to max lean</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MIXTURE1_SET</code></td>
        <td>MIXTURE1_SET</td>
        <td>Set mixture lever 1 exact value (0 to 16383)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MIXTURE2_SET</code></td>
        <td>MIXTURE2_SET</td>
        <td>Set mixture lever 2 exact value (0 to 16383)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MIXTURE3_SET</code></td>
        <td>MIXTURE3_SET</td>
        <td>Set mixture lever 3 exact value (0 to 16383)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MIXTURE4_SET</code></td>
        <td>MIXTURE4_SET</td>
        <td>Set mixture lever 4 exact value (0 to 16383)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AXIS_MIXTURE_SET</code></td>
        <td>AXIS_MIXTURE_SET</td>
        <td>Set mixture lever 1 exact value (-16383 to +16383)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AXIS_MIXTURE1_SET</code></td>
        <td>AXIS_MIXTURE1_SET</td>
        <td>Set mixture lever 1 exact value (-16383 to +16383)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AXIS_MIXTURE2_SET</code></td>
        <td>AXIS_MIXTURE2_SET</td>
        <td>Set mixture lever 2 exact value (-16383 to +16383)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AXIS_MIXTURE3_SET</code></td>
        <td>AXIS_MIXTURE3_SET</td>
        <td>Set mixture lever 3 exact value (-16383 to +16383)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AXIS_MIXTURE4_SET</code></td>
        <td>AXIS_MIXTURE4_SET</td>
        <td>Set mixture lever 4 exact value (-16383 to +16383)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MIXTURE_SET_BEST</code></td>
        <td>MIXTURE_SET_BEST</td>
        <td>Set mixture levers to current best power setting</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MIXTURE_DECR_SMALL</code></td>
        <td>MIXTURE_DECR_SMALL</td>
        <td>Decrement mixture levers small</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MIXTURE1_DECR_SMALL</code></td>
        <td>MIXTURE1_DECR_SMALL</td>
        <td>Decrement mixture lever 1 small</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MIXTURE2_DECR_SMALL</code></td>
        <td>MIXTURE2_DECR_SMALL</td>
        <td>Decrement mixture lever 4 small</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MIXTURE3_DECR_SMALL</code></td>
        <td>MIXTURE3_DECR_SMALL</td>
        <td>Decrement mixture lever 4 small</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MIXTURE4_DECR_SMALL</code></td>
        <td>MIXTURE4_DECR_SMALL</td>
        <td>Decrement mixture lever 4 small</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PROP_PITCH_SET</code></td>
        <td>PROP_PITCH_SET</td>
        <td>Set prop pitch levers (0 to 16383)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PROP_PITCH_LO</code></td>
        <td>PROP_PITCH_LO</td>
        <td>Set prop pitch levers max (lo pitch)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PROP_PITCH_INCR</code></td>
        <td>PROP_PITCH_INCR</td>
        <td>Increment prop pitch levers</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PROP_PITCH_INCR_SMALL</code></td>
        <td>PROP_PITCH_INCR_SMALL</td>
        <td>Increment prop pitch levers small</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PROP_PITCH_DECR</code></td>
        <td>PROP_PITCH_DECR</td>
        <td>Decrement prop pitch levers</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PROP_PITCH_HI</code></td>
        <td>PROP_PITCH_HI</td>
        <td>Set prop pitch levers min (hi pitch)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PROP_PITCH1_SET</code></td>
        <td>PROP_PITCH1_SET</td>
        <td>Set prop pitch lever 1 exact value (0 to 16383)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PROP_PITCH2_SET</code></td>
        <td>PROP_PITCH2_SET</td>
        <td>Set prop pitch lever 2 exact value (0 to 16383)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PROP_PITCH3_SET</code></td>
        <td>PROP_PITCH3_SET</td>
        <td>Set prop pitch lever 3 exact value (0 to 16383)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PROP_PITCH4_SET</code></td>
        <td>PROP_PITCH4_SET</td>
        <td>Set prop pitch lever 4 exact value (0 to 16383)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PROP_PITCH1_LO</code></td>
        <td>PROP_PITCH1_LO</td>
        <td>Set prop pitch lever 1 max (lo pitch)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PROP_PITCH1_INCR</code></td>
        <td>PROP_PITCH1_INCR</td>
        <td>Increment prop pitch lever 1</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PROP_PITCH1_INCR_SMALL</code></td>
        <td>PROP_PITCH1_INCR_SMALL</td>
        <td>Increment prop pitch lever 1 small</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PROP_PITCH1_DECR</code></td>
        <td>PROP_PITCH1_DECR</td>
        <td>Decrement prop pitch lever 1</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PROP_PITCH1_HI</code></td>
        <td>PROP_PITCH1_HI</td>
        <td>Set prop pitch lever 1 min (hi pitch)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PROP_PITCH2_LO</code></td>
        <td>PROP_PITCH2_LO</td>
        <td>Set prop pitch lever 2 max (lo pitch)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PROP_PITCH2_INCR</code></td>
        <td>PROP_PITCH2_INCR</td>
        <td>Increment prop pitch lever 2</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PROP_PITCH2_INCR_SMALL</code></td>
        <td>PROP_PITCH2_INCR_SMALL</td>
        <td>Increment prop pitch lever 2 small</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PROP_PITCH2_DECR</code></td>
        <td>PROP_PITCH2_DECR</td>
        <td>Decrement prop pitch lever 2</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PROP_PITCH2_HI</code></td>
        <td>PROP_PITCH2_HI</td>
        <td>Set prop pitch lever 2 min (hi pitch)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PROP_PITCH3_LO</code></td>
        <td>PROP_PITCH3_LO</td>
        <td>Set prop pitch lever 3 max (lo pitch)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PROP_PITCH3_INCR</code></td>
        <td>PROP_PITCH3_INCR</td>
        <td>Increment prop pitch lever 3</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PROP_PITCH3_INCR_SMALL</code></td>
        <td>PROP_PITCH3_INCR_SMALL</td>
        <td>Increment prop pitch lever 3 small</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PROP_PITCH3_DECR</code></td>
        <td>PROP_PITCH3_DECR</td>
        <td>Decrement prop pitch lever 3</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PROP_PITCH3_HI</code></td>
        <td>PROP_PITCH3_HI</td>
        <td>Set prop pitch lever 3 min (hi pitch)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PROP_PITCH4_LO</code></td>
        <td>PROP_PITCH4_LO</td>
        <td>Set prop pitch lever 4 max (lo pitch)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PROP_PITCH4_INCR</code></td>
        <td>PROP_PITCH4_INCR</td>
        <td>Increment prop pitch lever 4</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PROP_PITCH4_INCR_SMALL</code></td>
        <td>PROP_PITCH4_INCR_SMALL</td>
        <td>Increment prop pitch lever 4 small</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PROP_PITCH4_DECR</code></td>
        <td>PROP_PITCH4_DECR</td>
        <td>Decrement prop pitch lever 4</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PROP_PITCH4_HI</code></td>
        <td>PROP_PITCH4_HI</td>
        <td>Set prop pitch lever 4 min (hi pitch)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AXIS_PROPELLER_SET</code></td>
        <td>AXIS_PROPELLER_SET</td>
        <td>Set propeller levers exact value (-16383 to +16383)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AXIS_PROPELLER1_SET</code></td>
        <td>AXIS_PROPELLER1_SET</td>
        <td>Set propeller lever 1 exact value (-16383 to +16383)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AXIS_PROPELLER2_SET</code></td>
        <td>AXIS_PROPELLER2_SET</td>
        <td>Set propeller lever 2 exact value (-16383 to +16383)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AXIS_PROPELLER3_SET</code></td>
        <td>AXIS_PROPELLER3_SET</td>
        <td>Set propeller lever 3 exact value (-16383 to +16383)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AXIS_PROPELLER4_SET</code></td>
        <td>AXIS_PROPELLER4_SET</td>
        <td>Set propeller lever 4 exact value (-16383 to +16383)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_JET_STARTER</code></td>
        <td>JET_STARTER</td>
        <td>Selects jet engine starter (for +/- sequence)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_STARTER_SET</code></td>
        <td>MAGNETO_SET</td>
        <td>Sets magnetos (0,1)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_STARTER1</code></td>
        <td>TOGGLE_STARTER1</td>
        <td>Toggle starter 1</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_STARTER2</code></td>
        <td>TOGGLE_STARTER2</td>
        <td>Toggle starter 2</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_STARTER3</code></td>
        <td>TOGGLE_STARTER3</td>
        <td>Toggle starter 3</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_STARTER4</code></td>
        <td>TOGGLE_STARTER4</td>
        <td>Toggle starter 4</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_ALL_STARTERS</code></td>
        <td>TOGGLE_ALL_STARTERS</td>
        <td>Toggle starters</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ENGINE_AUTO_START</code></td>
        <td>ENGINE_AUTO_START</td>
        <td>Triggers auto-start</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ENGINE_AUTO_SHUTDOWN</code></td>
        <td>ENGINE_AUTO_SHUTDOWN</td>
        <td>Triggers auto-shutdown</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MAGNETO</code></td>
        <td>MAGNETO</td>
        <td>Selects magnetos (for +/- sequence)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MAGNETO_DECR</code></td>
        <td>MAGNETO_DECR</td>
        <td>Decrease magneto switches positions</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MAGNETO_INCR</code></td>
        <td>MAGNETO_INCR</td>
        <td>Increase magneto switches positions</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MAGNETO1_OFF</code></td>
        <td>MAGNETO1_OFF</td>
        <td>Set engine 1 magnetos off</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MAGNETO1_RIGHT</code></td>
        <td>MAGNETO1_RIGHT</td>
        <td>Toggle engine 1 right magneto</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MAGNETO1_LEFT</code></td>
        <td>MAGNETO1_LEFT</td>
        <td>Toggle engine 1 left magneto</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MAGNETO1_BOTH</code></td>
        <td>MAGNETO1_BOTH</td>
        <td>Set engine 1 magnetos on</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MAGNETO1_START</code></td>
        <td>MAGNETO1_START</td>
        <td>Set engine 1 magnetos on and toggle starter</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MAGNETO2_OFF</code></td>
        <td>MAGNETO2_OFF</td>
        <td>Set engine 2 magnetos off</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MAGNETO2_RIGHT</code></td>
        <td>MAGNETO2_RIGHT</td>
        <td>Toggle engine 2 right magneto</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MAGNETO2_LEFT</code></td>
        <td>MAGNETO2_LEFT</td>
        <td>Toggle engine 2 left magneto</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MAGNETO2_BOTH</code></td>
        <td>MAGNETO2_BOTH</td>
        <td>Set engine 2 magnetos on</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MAGNETO2_START</code></td>
        <td>MAGNETO2_START</td>
        <td>Set engine 2 magnetos on and toggle starter</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MAGNETO3_OFF</code></td>
        <td>MAGNETO3_OFF</td>
        <td>Set engine 3 magnetos off</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MAGNETO3_RIGHT</code></td>
        <td>MAGNETO3_RIGHT</td>
        <td>Toggle engine 3 right magneto</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MAGNETO3_LEFT</code></td>
        <td>MAGNETO3_LEFT</td>
        <td>Toggle engine 3 left magneto</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MAGNETO3_BOTH</code></td>
        <td>MAGNETO3_BOTH</td>
        <td>Set engine 3 magnetos on</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MAGNETO3_START</code></td>
        <td>MAGNETO3_START</td>
        <td>Set engine 3 magnetos on and toggle starter</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MAGNETO4_OFF</code></td>
        <td>MAGNETO4_OFF</td>
        <td>Set engine 4 magnetos off</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MAGNETO4_RIGHT</code></td>
        <td>MAGNETO4_RIGHT</td>
        <td>Toggle engine 4 right magneto</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MAGNETO4_LEFT</code></td>
        <td>MAGNETO4_LEFT</td>
        <td>Toggle engine 4 left magneto</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MAGNETO4_BOTH</code></td>
        <td>MAGNETO4_BOTH</td>
        <td>Set engine 4 magnetos on</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MAGNETO4_START</code></td>
        <td>MAGNETO4_START</td>
        <td>Set engine 4 magnetos on and toggle starter</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MAGNETO_OFF</code></td>
        <td>MAGNETO_OFF</td>
        <td>Set engine magnetos off</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MAGNETO_RIGHT</code></td>
        <td>MAGNETO_RIGHT</td>
        <td>Set engine right magnetos on</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MAGNETO_LEFT</code></td>
        <td>MAGNETO_LEFT</td>
        <td>Set engine left magnetos on</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MAGNETO_BOTH</code></td>
        <td>MAGNETO_BOTH</td>
        <td>Set engine magnetos on</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MAGNETO_START</code></td>
        <td>MAGNETO_START</td>
        <td>Set engine magnetos on and toggle starters</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MAGNETO1_DECR</code></td>
        <td>MAGNETO1_DECR</td>
        <td>Decrease engine 1 magneto switch position</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MAGNETO1_INCR</code></td>
        <td>MAGNETO1_INCR</td>
        <td>Increase engine 1 magneto switch position</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MAGNETO2_DECR</code></td>
        <td>MAGNETO2_DECR</td>
        <td>Decrease engine 2 magneto switch position</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MAGNETO2_INCR</code></td>
        <td>MAGNETO2_INCR</td>
        <td>Increase engine 2 magneto switch position</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MAGNETO3_DECR</code></td>
        <td>MAGNETO3_DECR</td>
        <td>Decrease engine 3 magneto switch position</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MAGNETO3_INCR</code></td>
        <td>MAGNETO3_INCR</td>
        <td>Increase engine 3 magneto switch position</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MAGNETO4_DECR</code></td>
        <td>MAGNETO4_DECR</td>
        <td>Decrease engine 4 magneto switch position</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MAGNETO4_INCR</code></td>
        <td>MAGNETO4_INCR</td>
        <td>Increase engine 4 magneto switch position</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MAGNETO_SET</code></td>
        <td>Not supported</td>
        <td>Set engine magneto switches</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MAGNETO1_SET</code></td>
        <td>MAGNETO1_SET</td>
        <td>Set engine 1 magneto switch</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MAGNETO2_SET</code></td>
        <td>MAGNETO2_SET</td>
        <td>Set engine 2 magneto switch</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MAGNETO3_SET</code></td>
        <td>MAGNETO3_SET</td>
        <td>Set engine 3 magneto switch</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MAGNETO4_SET</code></td>
        <td>MAGNETO4_SET</td>
        <td>Set engine 4 magneto switch</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ANTI_ICE_ON</code></td>
        <td>ANTI_ICE_ON</td>
        <td>Sets anti-ice switches on</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ANTI_ICE_OFF</code></td>
        <td>ANTI_ICE_OFF</td>
        <td>Sets anti-ice switches off</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ANTI_ICE_SET</code></td>
        <td>ANTI_ICE_SET</td>
        <td>Sets anti-ice switches from argument (0,1)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ANTI_ICE_TOGGLE</code></td>
        <td>ANTI_ICE_TOGGLE</td>
        <td>Toggle anti-ice switches</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ANTI_ICE_TOGGLE_ENG1</code></td>
        <td>ANTI_ICE_TOGGLE_ENG1</td>
        <td>Toggle engine 1 anti-ice switch</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ANTI_ICE_TOGGLE_ENG2</code></td>
        <td>ANTI_ICE_TOGGLE_ENG2</td>
        <td>Toggle engine 2 anti-ice switch</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ANTI_ICE_TOGGLE_ENG3</code></td>
        <td>ANTI_ICE_TOGGLE_ENG3</td>
        <td>Toggle engine 3 anti-ice switch</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ANTI_ICE_TOGGLE_ENG4</code></td>
        <td>ANTI_ICE_TOGGLE_ENG4</td>
        <td>Toggle engine 4 anti-ice switch</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ANTI_ICE_SET_ENG1</code></td>
        <td>ANTI_ICE_SET_ENG1</td>
        <td>Sets engine 1 anti-ice switch (0,1)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ANTI_ICE_SET_ENG2</code></td>
        <td>ANTI_ICE_SET_ENG2</td>
        <td>Sets engine 2 anti-ice switch (0,1)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ANTI_ICE_SET_ENG3</code></td>
        <td>ANTI_ICE_SET_ENG3</td>
        <td>Sets engine 3 anti-ice switch (0,1)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ANTI_ICE_SET_ENG4</code></td>
        <td>ANTI_ICE_SET_ENG4</td>
        <td>Sets engine 4 anti-ice switch (0,1)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_FUEL_VALVE_ALL</code></td>
        <td>TOGGLE_FUEL_VALVE_ALL</td>
        <td>Toggle engine fuel valves</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_FUEL_VALVE_ENG1</code></td>
        <td>TOGGLE_FUEL_VALVE_ENG1</td>
        <td>Toggle engine 1 fuel valve</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_FUEL_VALVE_ENG2</code></td>
        <td>TOGGLE_FUEL_VALVE_ENG2</td>
        <td>Toggle engine 2 fuel valve</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_FUEL_VALVE_ENG3</code></td>
        <td>TOGGLE_FUEL_VALVE_ENG3</td>
        <td>Toggle engine 3 fuel valve</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_FUEL_VALVE_ENG4</code></td>
        <td>TOGGLE_FUEL_VALVE_ENG4</td>
        <td>Toggle engine 4 fuel valve</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_COWLFLAP1_SET</code></td>
        <td>COWLFLAP1_SET</td>
        <td>Sets engine 1 cowl flap lever position (0 to 16383)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_COWLFLAP2_SET</code></td>
        <td>COWLFLAP2_SET</td>
        <td>Sets engine 2 cowl flap lever position (0 to 16383)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_COWLFLAP3_SET</code></td>
        <td>COWLFLAP3_SET</td>
        <td>Sets engine 3 cowl flap lever position (0 to 16383)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_COWLFLAP4_SET</code></td>
        <td>COWLFLAP4_SET</td>
        <td>Sets engine 4 cowl flap lever position (0 to 16383)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_INC_COWL_FLAPS</code></td>
        <td>INC_COWL_FLAPS</td>
        <td>Increment cowl flap levers</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_DEC_COWL_FLAPS</code></td>
        <td>DEC_COWL_FLAPS</td>
        <td>Decrement cowl flap levers</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_INC_COWL_FLAPS1</code></td>
        <td>INC_COWL_FLAPS1</td>
        <td>Increment engine 1 cowl flap lever</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_DEC_COWL_FLAPS1</code></td>
        <td>DEC_COWL_FLAPS1</td>
        <td>Decrement engine 1 cowl flap lever</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_INC_COWL_FLAPS2</code></td>
        <td>INC_COWL_FLAPS2</td>
        <td>Increment engine 2 cowl flap lever</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_DEC_COWL_FLAPS2</code></td>
        <td>DEC_COWL_FLAPS2</td>
        <td>Decrement engine 2 cowl flap lever</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_INC_COWL_FLAPS3</code></td>
        <td>INC_COWL_FLAPS3</td>
        <td>Increment engine 3 cowl flap lever</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_DEC_COWL_FLAPS3</code></td>
        <td>DEC_COWL_FLAPS3</td>
        <td>Decrement engine 3 cowl flap lever</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_INC_COWL_FLAPS4</code></td>
        <td>INC_COWL_FLAPS4</td>
        <td>Increment engine 4 cowl flap lever</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_DEC_COWL_FLAPS4</code></td>
        <td>DEC_COWL_FLAPS4</td>
        <td>Decrement engine 4 cowl flap lever</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FUEL_PUMP</code></td>
        <td>FUEL_PUMP</td>
        <td>Toggle electric fuel pumps</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_ELECT_FUEL_PUMP</code></td>
        <td>TOGGLE_ELECT_FUEL_PUMP</td>
        <td>Toggle electric fuel pumps</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_ELECT_FUEL_PUMP1</code></td>
        <td>TOGGLE_ELECT_FUEL_PUMP1</td>
        <td>Toggle engine 1 electric fuel pump</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_ELECT_FUEL_PUMP2</code></td>
        <td>TOGGLE_ELECT_FUEL_PUMP2</td>
        <td>Toggle engine 2 electric fuel pump</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_ELECT_FUEL_PUMP3</code></td>
        <td>TOGGLE_ELECT_FUEL_PUMP3</td>
        <td>Toggle engine 3 electric fuel pump</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_ELECT_FUEL_PUMP4</code></td>
        <td>TOGGLE_ELECT_FUEL_PUMP4</td>
        <td>Toggle engine 4 electric fuel pump</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ENGINE_PRIMER</code></td>
        <td>ENGINE_PRIMER</td>
        <td>Trigger engine primers</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_PRIMER</code></td>
        <td>TOGGLE_PRIMER</td>
        <td>Trigger engine primers</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_PRIMER1</code></td>
        <td>TOGGLE_PRIMER1</td>
        <td>Trigger engine 1 primer</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_PRIMER2</code></td>
        <td>TOGGLE_PRIMER2</td>
        <td>Trigger engine 2 primer</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_PRIMER3</code></td>
        <td>TOGGLE_PRIMER3</td>
        <td>Trigger engine 3 primer</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_PRIMER4</code></td>
        <td>TOGGLE_PRIMER4</td>
        <td>Trigger engine 4 primer</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_FEATHER_SWITCHES</code></td>
        <td>TOGGLE_FEATHER_SWITCHES</td>
        <td>Trigger propeller switches</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_FEATHER_SWITCH_1</code></td>
        <td>TOGGLE_FEATHER_SWITCH_1</td>
        <td>Trigger propeller 1 switch</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_FEATHER_SWITCH_2</code></td>
        <td>TOGGLE_FEATHER_SWITCH_2</td>
        <td>Trigger propeller 2 switch</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_FEATHER_SWITCH_3</code></td>
        <td>TOGGLE_FEATHER_SWITCH_3</td>
        <td>Trigger propeller 3 switch</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_FEATHER_SWITCH_4</code></td>
        <td>TOGGLE_FEATHER_SWITCH_4</td>
        <td>Trigger propeller 4 switch</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_PROP_SYNC</code></td>
        <td>TOGGLE_PROPELLER_SYNC</td>
        <td>Turns propeller synchronization switch on</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_ARM_AUTOFEATHER</code></td>
        <td>TOGGLE_AUTOFEATHER_ARM</td>
        <td>Turns auto-feather arming switch on.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_AFTERBURNER</code></td>
        <td>TOGGLE_AFTERBURNER</td>
        <td>Toggles afterburners</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_AFTERBURNER1</code></td>
        <td>TOGGLE_AFTERBURNER1</td>
        <td>Toggles engine 1 afterburner</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_AFTERBURNER2</code></td>
        <td>TOGGLE_AFTERBURNER2</td>
        <td>Toggles engine 2 afterburner</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_AFTERBURNER3</code></td>
        <td>TOGGLE_AFTERBURNER3</td>
        <td>Toggles engine 3 afterburner</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_AFTERBURNER4</code></td>
        <td>TOGGLE_AFTERBURNER4</td>
        <td>Toggles engine 4 afterburner</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ENGINE</code></td>
        <td>ENGINE</td>
        <td>Sets engines for 1,2,3,4 selection (to be followed by SELECT_n)</td>
        <td>Shared Cockpit</td>
      </tr>
    </tbody>
  </table>
  <p>&nbsp;</p>
  <p>&nbsp;</p>
  <h3 id="aircraft-flight-controls">Aircraft Flight Controls</h3>
  <table style="table-layout:auto;">
    <colgroup>
      <col>
      <col>
      <col>
      <col>
    </colgroup>
    <tbody>
      <tr>
        <th>Event ID</th>
        <th>String Name</th>
        <th>Description</th>
        <th>Multiplayer</th>
      </tr>
      <tr>
        <td><code class="inline">KEY_SPOILERS_TOGGLE</code></td>
        <td>SPOILERS_TOGGLE</td>
        <td>Toggles spoiler handle</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FLAPS_UP</code></td>
        <td>FLAPS_UP</td>
        <td>Sets flap handle to full retract position</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FLAPS_1</code></td>
        <td>FLAPS_1</td>
        <td>Sets flap handle to first extension position</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FLAPS_2</code></td>
        <td>FLAPS_2</td>
        <td>Sets flap handle to second extension position</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FLAPS_3</code></td>
        <td>FLAPS_3</td>
        <td>Sets flap handle to third extension position</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FLAPS_DOWN</code></td>
        <td>FLAPS_DOWN</td>
        <td>Sets flap handle to full extension position</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ELEV_TRIM_DN</code></td>
        <td>ELEV_TRIM_DN</td>
        <td>Increments elevator trim down</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ELEV_DOWN</code></td>
        <td>ELEV_DOWN</td>
        <td>Increments elevator down</td>
        <td>Shared Cockpit (Pilot only).</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AILERONS_LEFT</code></td>
        <td>AILERONS_LEFT</td>
        <td>Increments ailerons left</td>
        <td>Shared Cockpit (Pilot only).</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_CENTER_AILER_RUDDER</code></td>
        <td>CENTER_AILER_RUDDER</td>
        <td>Centers aileron and rudder positions</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AILERONS_RIGHT</code></td>
        <td>AILERONS_RIGHT</td>
        <td>Increments ailerons right</td>
        <td>Shared Cockpit (Pilot only).</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ELEV_TRIM_UP</code></td>
        <td>ELEV_TRIM_UP</td>
        <td>Increment elevator trim up</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ELEV_UP</code></td>
        <td>ELEV_UP</td>
        <td>Increments elevator up</td>
        <td>Shared Cockpit (Pilot only).</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ELEVATOR_DOWN</code></td>
        <td>Unsupported</td>
        <td>Increments elevator down</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ELEVATOR_UP</code></td>
        <td>Unsupported</td>
        <td>Increments elevator up</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AILERON_LEFT</code></td>
        <td>Unsupported</td>
        <td>Increments ailerons left</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AILERON_CENTER</code></td>
        <td>Unsupported</td>
        <td>Centers aileron position</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AILERON_RIGHT</code></td>
        <td>Unsupported</td>
        <td>Increments ailerons right</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_RUDDER_LEFT</code></td>
        <td>RUDDER_LEFT</td>
        <td>Increments rudder left</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_RUDDER_CENTER</code></td>
        <td>RUDDER_CENTER</td>
        <td>Centers rudder position</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_RUDDER_RIGHT</code></td>
        <td>RUDDER_RIGHT</td>
        <td>Increments rudder right</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ELEVATOR_SET</code></td>
        <td>ELEVATOR_SET</td>
        <td>Sets elevator position (-16383 - +16383)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AILERON_SET</code></td>
        <td>AILERON_SET</td>
        <td>Sets aileron position (-16383 - +16383)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_RUDDER_SET</code></td>
        <td>RUDDER_SET</td>
        <td>Sets rudder position (-16383 - +16383)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FLAPS_INCR</code></td>
        <td>FLAPS_INCR</td>
        <td>Increments flap handle position</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FLAPS_DECR</code></td>
        <td>FLAPS_DECR</td>
        <td>Decrements flap handle position</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AXIS_ELEVATOR_SET</code></td>
        <td>AXIS_ELEVATOR_SET</td>
        <td>Sets elevator position (-16383 - +16383)</td>
        <td>Shared Cockpit (Pilot only, and not transmitted to Co-pilot)</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AXIS_AILERONS_SET</code></td>
        <td>AXIS_AILERONS_SET</td>
        <td>Sets aileron position (-16383 - +16383)</td>
        <td>Shared Cockpit (Pilot only, and not transmitted to Co-pilot)</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AXIS_RUDDER_SET</code></td>
        <td>AXIS_RUDDER_SET</td>
        <td>Sets rudder position (-16383 - +16383)</td>
        <td>Shared Cockpit (Pilot only, and not transmitted to Co-pilot)</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AXIS_ELEV_TRIM_SET</code></td>
        <td>AXIS_ELEV_TRIM_SET</td>
        <td>Sets elevator trim position (-16383 - +16383)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SPOILERS_SET</code></td>
        <td>SPOILERS_SET</td>
        <td>Sets spoiler handle position (0 to 16383)</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SPOILERS_ARM_TOGGLE</code></td>
        <td>SPOILERS_ARM_TOGGLE</td>
        <td>Toggles arming of auto-spoilers</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SPOILERS_ON</code></td>
        <td>SPOILERS_ON</td>
        <td>Sets spoiler handle to full extend position</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SPOILERS_OFF</code></td>
        <td>SPOILERS_OFF</td>
        <td>Sets spoiler handle to full retract position</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SPOILERS_ARM_ON</code></td>
        <td>SPOILERS_ARM_ON</td>
        <td>Sets auto-spoiler arming on</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SPOILERS_ARM_OFF</code></td>
        <td>SPOILERS_ARM_OFF</td>
        <td>Sets auto-spoiler arming off</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SPOILERS_ARM_SET</code></td>
        <td>SPOILERS_ARM_SET</td>
        <td>Sets auto-spoiler arming (0,1)</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AILERON_TRIM_LEFT</code></td>
        <td>AILERON_TRIM_LEFT</td>
        <td>Increments aileron trim left</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AILERON_TRIM_RIGHT</code></td>
        <td>AILERON_TRIM_RIGHT</td>
        <td>Increments aileron trim right</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_RUDDER_TRIM_LEFT</code></td>
        <td>RUDDER_TRIM_LEFT</td>
        <td>Increments rudder trim left</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_RUDDER_TRIM_RIGHT</code></td>
        <td>RUDDER_TRIM_RIGHT</td>
        <td>Increments aileron trim right</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AXIS_SPOILER_SET</code></td>
        <td>AXIS_SPOILER_SET</td>
        <td>Sets spoiler handle position (-16383 - +16383)</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FLAPS_SET</code></td>
        <td>FLAPS_SET</td>
        <td>Sets flap handle to closest increment (0 to 16383)</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ELEVATOR_TRIM_SET</code></td>
        <td>ELEVATOR_TRIM_SET</td>
        <td>Sets elevator trim position (0 to 16383)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AXIS_FLAPS_SET</code></td>
        <td>AXIS_FLAPS_SET</td>
        <td>Sets flap handle to closest increment (-16383 - +16383)</td>
        <td>Shared Cockpit</td>
      </tr>
    </tbody>
  </table>
  <p>&nbsp;</p>
  <p>&nbsp;</p>
  <h3 id="aircraft-automatic-flight-systems-autopilot">Aircraft Automatic Flight Systems / Autopilot</h3>
  <table style="table-layout:auto;">
    <colgroup>
      <col>
      <col>
      <col>
      <col>
    </colgroup>
    <tbody>
      <tr>
        <th>Event ID</th>
        <th>String Name</th>
        <th>Description</th>
        <th>Multiplayer</th>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_MASTER</code></td>
        <td>AP_MASTER</td>
        <td>Toggles AP on/off</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AUTOPILOT_OFF</code></td>
        <td>AUTOPILOT_OFF</td>
        <td>Turns AP off</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AUTOPILOT_ON</code></td>
        <td>AUTOPILOT_ON</td>
        <td>Turns AP on</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_YAW_DAMPER_TOGGLE</code></td>
        <td>YAW_DAMPER_TOGGLE</td>
        <td>Toggles yaw damper on/off</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_PANEL_HEADING_HOLD</code></td>
        <td>AP_PANEL_HEADING_HOLD</td>
        <td>Toggles heading hold mode on/off</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_PANEL_ALTITUDE_HOLD</code></td>
        <td>AP_PANEL_ALTITUDE_HOLD</td>
        <td>Toggles altitude hold mode on/off</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_ATT_HOLD_ON</code></td>
        <td>AP_ATT_HOLD_ON</td>
        <td>Turns on AP wing leveler and pitch hold mode</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_LOC_HOLD_ON</code></td>
        <td>AP_LOC_HOLD_ON</td>
        <td>Turns AP localizer hold on/armed and glide-slope hold mode off</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_APR_HOLD_ON</code></td>
        <td>AP_APR_HOLD_ON</td>
        <td>Turns both AP localizer and glide-slope modes on/armed</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_HDG_HOLD_ON</code></td>
        <td>AP_HDG_HOLD_ON</td>
        <td>Turns heading hold mode on</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_ALT_HOLD_ON</code></td>
        <td>AP_ALT_HOLD_ON</td>
        <td>Turns altitude hold mode on</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_WING_LEVELER_ON</code></td>
        <td>AP_WING_LEVELER_ON</td>
        <td>Turns wing leveler mode on</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_BC_HOLD_ON</code></td>
        <td>AP_BC_HOLD_ON</td>
        <td>Turns localizer back course hold mode on/armed</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_NAV1_HOLD_ON</code></td>
        <td>AP_NAV1_HOLD_ON</td>
        <td>Turns lateral hold mode on</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_ATT_HOLD_OFF</code></td>
        <td>AP_ATT_HOLD_OFF</td>
        <td>Turns off attitude hold mode</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_LOC_HOLD_OFF</code></td>
        <td>AP_LOC_HOLD_OFF</td>
        <td>Turns off localizer hold mode</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_APR_HOLD_OFF</code></td>
        <td>AP_APR_HOLD_OFF</td>
        <td>Turns off approach hold mode</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_HDG_HOLD_OFF</code></td>
        <td>AP_HDG_HOLD_OFF</td>
        <td>Turns off heading hold mode</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_ALT_HOLD_OFF</code></td>
        <td>AP_ALT_HOLD_OFF</td>
        <td>Turns off altitude hold mode</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_WING_LEVELER_OFF</code></td>
        <td>AP_WING_LEVELER_OFF</td>
        <td>Turns off wing leveler mode</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_BC_HOLD_OFF</code></td>
        <td>AP_BC_HOLD_OFF</td>
        <td>Turns off backcourse mode for localizer hold</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_NAV1_HOLD_OFF</code></td>
        <td>AP_NAV1_HOLD_OFF</td>
        <td>Turns off nav hold mode</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_AIRSPEED_HOLD</code></td>
        <td>AP_AIRSPEED_HOLD</td>
        <td>Toggles airspeed hold mode</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AUTO_THROTTLE_ARM</code></td>
        <td>AUTO_THROTTLE_ARM</td>
        <td>Toggles autothrottle arming mode</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AUTO_THROTTLE_TO_GA</code></td>
        <td>AUTO_THROTTLE_TO_GA</td>
        <td>Toggles Takeoff/Go Around mode</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_HEADING_BUG_INC</code></td>
        <td>HEADING_BUG_INC</td>
        <td>Increments heading hold reference bug</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_HEADING_BUG_DEC</code></td>
        <td>HEADING_BUG_DEC</td>
        <td>Decrements heading hold reference bug</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_HEADING_BUG_SET</code></td>
        <td>HEADING_BUG_SET</td>
        <td>Set heading hold reference bug (degrees)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_PANEL_SPEED_HOLD</code></td>
        <td>AP_PANEL_SPEED_HOLD</td>
        <td>Toggles airspeed hold mode</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_ALT_VAR_INC</code></td>
        <td>AP_ALT_VAR_INC</td>
        <td>Increments reference altitude</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_ALT_VAR_DEC</code></td>
        <td>AP_ALT_VAR_DEC</td>
        <td>Decrements reference altitude</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_VS_VAR_INC</code></td>
        <td>AP_VS_VAR_INC</td>
        <td>Increments vertical speed reference</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_VS_VAR_DEC</code></td>
        <td>AP_VS_VAR_DEC</td>
        <td>Decrements vertical speed reference</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_SPD_VAR_INC</code></td>
        <td>AP_SPD_VAR_INC</td>
        <td>Increments airspeed hold reference</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_SPD_VAR_DEC</code></td>
        <td>AP_SPD_VAR_DEC</td>
        <td>Decrements airspeed hold reference</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_PANEL_MACH_HOLD</code></td>
        <td>AP_PANEL_MACH_HOLD</td>
        <td>Toggles mach hold</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_MACH_VAR_INC</code></td>
        <td>AP_MACH_VAR_INC</td>
        <td>Increments reference mach</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_MACH_VAR_DEC</code></td>
        <td>AP_MACH_VAR_DEC</td>
        <td>Decrements reference mach</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_MACH_HOLD</code></td>
        <td>AP_MACH_HOLD</td>
        <td>Toggles mach hold</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_ALT_VAR_SET_METRIC</code></td>
        <td>AP_ALT_VAR_SET_METRIC</td>
        <td>Sets reference altitude in meters</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_VS_VAR_SET_ENGLISH</code></td>
        <td>AP_VS_VAR_SET_ENGLISH</td>
        <td>Sets reference vertical speed in feet per minute</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_SPD_VAR_SET</code></td>
        <td>AP_SPD_VAR_SET</td>
        <td>Sets airspeed reference in knots</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_MACH_VAR_SET</code></td>
        <td>AP_MACH_VAR_SET</td>
        <td>Sets mach reference</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_YAW_DAMPER_ON</code></td>
        <td>YAW_DAMPER_ON</td>
        <td>Turns yaw damper on</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_YAW_DAMPER_OFF</code></td>
        <td>YAW_DAMPER_OFF</td>
        <td>Turns yaw damper off</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_YAW_DAMPER_SET</code></td>
        <td>YAW_DAMPER_SET</td>
        <td>Sets yaw damper on/off (1,0)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_AIRSPEED_ON</code></td>
        <td>AP_AIRSPEED_ON</td>
        <td>Turns airspeed hold on</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_AIRSPEED_OFF</code></td>
        <td>AP_AIRSPEED_OFF</td>
        <td>Turns airspeed hold off</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_AIRSPEED_SET</code></td>
        <td>AP_AIRSPEED_SET</td>
        <td>Sets airspeed hold on/off (1,0)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_MACH_ON</code></td>
        <td>AP_MACH_ON</td>
        <td>Turns mach hold on</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_MACH_OFF</code></td>
        <td>AP_MACH_OFF</td>
        <td>Turns mach hold off</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_MACH_SET</code></td>
        <td>AP_MACH_SET</td>
        <td>Sets mach hold on/off (1,0)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_PANEL_ALTITUDE_ON</code></td>
        <td>AP_PANEL_ALTITUDE_ON</td>
        <td>Turns altitude hold mode on (without capturing current altitude)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_PANEL_ALTITUDE_OFF</code></td>
        <td>AP_PANEL_ALTITUDE_OFF</td>
        <td>Turns altitude hold mode off</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_PANEL_ALTITUDE_SET</code></td>
        <td>AP_PANEL_ALTITUDE_SET</td>
        <td>Sets altitude hold mode on/off (1,0)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_PANEL_HEADING_ON</code></td>
        <td>AP_PANEL_HEADING_ON</td>
        <td>Turns heading mode on (without capturing current heading)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_PANEL_HEADING_OFF</code></td>
        <td>AP_PANEL_HEADING_OFF</td>
        <td>Turns heading mode off</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_PANEL_HEADING_SET</code></td>
        <td>AP_PANEL_HEADING_SET</td>
        <td>Set heading mode on/off (1,0)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_PANEL_MACH_ON</code></td>
        <td>AP_PANEL_MACH_ON</td>
        <td>Turns on mach hold</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_PANEL_MACH_OFF</code></td>
        <td>AP_PANEL_MACH_OFF</td>
        <td>Turns off mach hold</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_PANEL_MACH_SET</code></td>
        <td>AP_PANEL_MACH_SET</td>
        <td>Sets mach hold on/off (1,0)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_PANEL_SPEED_ON</code></td>
        <td>AP_PANEL_SPEED_ON</td>
        <td>Turns on speed hold mode</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_PANEL_SPEED_OFF</code></td>
        <td>AP_PANEL_SPEED_OFF</td>
        <td>Turns off speed hold mode</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_PANEL_SPEED_SET</code></td>
        <td>AP_PANEL_SPEED_SET</td>
        <td>Set speed hold mode on/off (1,0)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_ALT_VAR_SET_ENGLISH</code></td>
        <td>AP_ALT_VAR_SET_ENGLISH</td>
        <td>Sets altitude reference in feet</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_VS_VAR_SET_METRIC</code></td>
        <td>AP_VS_VAR_SET_METRIC</td>
        <td>Sets vertical speed reference in meters per minute</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_FLIGHT_DIRECTOR</code></td>
        <td>TOGGLE_FLIGHT_DIRECTOR</td>
        <td>Toggles flight director on/off</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SYNC_FLIGHT_DIRECTOR_PITCH</code></td>
        <td>SYNC_FLIGHT_DIRECTOR_PITCH</td>
        <td>Synchronizes flight director pitch with current aircraft pitch</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_INC_AUTOBRAKE_CONTROL</code></td>
        <td>INCREASE_AUTOBRAKE_CONTROL</td>
        <td>Increments autobrake level</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_DEC_AUTOBRAKE_CONTROL</code></td>
        <td>DECREASE_AUTOBRAKE_CONTROL</td>
        <td>Decrements autobrake level</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AUTOPILOT_AIRSPEED_HOLD_CURRENT</code></td>
        <td>AP_PANEL_SPEED_HOLD_TOGGLE</td>
        <td>Turns airspeed hold mode on with current airspeed</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AUTOPILOT_PANEL_AIRSPEED_SET</code></td>
        <td>Unsupported</td>
        <td>Sets airspeed reference to current airspeed</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AUTOPILOT_MACH_HOLD_CURRENT</code></td>
        <td>AP_PANEL_MACH_HOLD_TOGGLE</td>
        <td>Sets mach hold reference to current mach</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_NAV_SELECT_SET</code></td>
        <td>AP_NAV_SELECT_SET</td>
        <td>Sets the nav (1 or 2) which is used by the Nav hold modes</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_HEADING_BUG_SELECT</code></td>
        <td>HEADING_BUG_SELECT</td>
        <td>Selects the heading bug for use with +/-</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ALTITUDE_BUG_SELECT</code></td>
        <td>ALTITUDE_BUG_SELECT</td>
        <td>Selects the altitude reference for use with +/-</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_VSI_BUG_SELECT</code></td>
        <td>VSI_BUG_SELECT</td>
        <td>Selects the vertical speed reference for use with +/-</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AIRSPEED_BUG_SELECT</code></td>
        <td>AIRSPEED_BUG_SELECT</td>
        <td>Selects the airspeed reference for use with +/-</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_PITCH_REF_INC_UP</code></td>
        <td>AP_PITCH_REF_INC_UP</td>
        <td>Increments the pitch reference for pitch hold mode</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_PITCH_REF_INC_DN</code></td>
        <td>AP_PITCH_REF_INC_DN</td>
        <td>Decrements the pitch reference for pitch hold mode</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_PITCH_REF_SELECT</code></td>
        <td>AP_PITCH_REF_SELECT</td>
        <td>Selects pitch reference for use with +/-</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_ATT_HOLD</code></td>
        <td>AP_ATT_HOLD</td>
        <td>Toggle attitude hold mode</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_LOC_HOLD</code></td>
        <td>AP_LOC_HOLD</td>
        <td>Toggles localizer (only) hold mode</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_APR_HOLD</code></td>
        <td>AP_APR_HOLD</td>
        <td>Toggles approach hold (localizer and glide-slope)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_HDG_HOLD</code></td>
        <td>AP_HDG_HOLD</td>
        <td>Toggles heading hold mode</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_ALT_HOLD</code></td>
        <td>AP_ALT_HOLD</td>
        <td>Toggles altitude hold mode</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_WING_LEVELER</code></td>
        <td>AP_WING_LEVELER</td>
        <td>Toggles wing leveler mode</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_BC_HOLD</code></td>
        <td>AP_BC_HOLD</td>
        <td>Toggles the backcourse mode for the localizer hold</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_NAV1_HOLD</code></td>
        <td>AP_NAV1_HOLD</td>
        <td>Toggles the nav hold mode</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_MAX_BANK_INC</code></td>
        <td>AP_MAX_BANK_INC</td>
        <td>Autopilot max bank angle increment.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_MAX_BANK_DEC</code></td>
        <td>AP_MAX_BANK_DEC</td>
        <td>Autopilot max bank angle decrement.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_N1_HOLD</code></td>
        <td>AP_N1_HOLD</td>
        <td>Autopilot, hold the N1 percentage at its current level.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_N1_REF_INC</code></td>
        <td>AP_N1_REF_INC</td>
        <td>Increment the autopilot N1 reference.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_N1_REF_DEC</code></td>
        <td>AP_N1_REF_DEC</td>
        <td>Decrement the autopilot N1 reference.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AP_N1_REF_SET</code></td>
        <td>AP_N1_REF_SET</td>
        <td>Sets the autopilot N1 reference.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FLY_BY_WIRE_ELAC_TOGGLE</code></td>
        <td>FLY_BY_WIRE_ELAC_TOGGLE</td>
        <td>Turn on or off the fly by wire Elevators and Ailerons computer.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FLY_BY_WIRE_FAC_TOGGLE</code></td>
        <td>FLY_BY_WIRE_FAC_TOGGLE</td>
        <td>Turn on or off the fly by wire Flight Augmentation computer.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FLY_BY_WIRE_SEC_TOGGLE</code></td>
        <td>FLY_BY_WIRE_SEC_TOGGLE</td>
        <td>Turn on or off the fly by wire Spoilers and Elevators computer.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_G1000_PFD_FLIGHTPLAN_BUTTON</code></td>
        <td>G1000_PFD_FLIGHTPLAN_BUTTON</td>
        <td>The primary flight display (PFD) should display its current flight plan.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_G1000_PFD_PROCEDURE_BUTTON</code></td>
        <td>G1000_PFD_PROCEDURE_BUTTON</td>
        <td>Turn to the Procedure page.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_G1000_PFD_ZOOMIN_BUTTON</code></td>
        <td>G1000_PFD_ZOOMIN_BUTTON</td>
        <td>Zoom in on the current map.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_G1000_PFD_ZOOMOUT_BUTTON</code></td>
        <td>G1000_PFD_ZOOMOUT_BUTTON</td>
        <td>Zoom out on the current map.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_G1000_PFD_DIRECTTO_BUTTON</code></td>
        <td>G1000_PFD_DIRECTTO_BUTTON</td>
        <td>Turn to the Direct To page.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_G1000_PFD_MENU_BUTTON</code></td>
        <td>G1000_PFD_MENU_BUTTON</td>
        <td>If a segmented flight plan is highlighted, activates the associated menu.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_G1000_PFD_CLEAR_BUTTON</code></td>
        <td>G1000_PFD_CLEAR_BUTTON</td>
        <td>Clears the current input.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_G1000_PFD_ENTER_BUTTON</code></td>
        <td>G1000_PFD_ENTER_BUTTON</td>
        <td>Enters the current input.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_G1000_PFD_CURSOR_BUTTON</code></td>
        <td>G1000_PFD_CURSOR_BUTTON</td>
        <td>Turns on or off a screen cursor.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_G1000_PFD_GROUP_KNOB_INC</code></td>
        <td>G1000_PFD_GROUP_KNOB_INC</td>
        <td>Step up through the page groups.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_G1000_PFD_GROUP_KNOB_DEC</code></td>
        <td>G1000_PFD_GROUP_KNOB_DEC</td>
        <td>Step down through the page groups.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_G1000_PFD_PAGE_KNOB_INC</code></td>
        <td>G1000_PFD_PAGE_KNOB_INC</td>
        <td>Step up through the individual pages.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_G1000_PFD_PAGE_KNOB_DEC</code></td>
        <td>G1000_PFD_PAGE_KNOB_DEC</td>
        <td>Step down through the individual pages.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_G1000_PFD_SOFTKEY1, to KEY_G1000_PFD_SOFTKEY12</code></td>
        <td>G1000_PFD_SOFTKEY1, to G1000_PFD_SOFTKEY12</td>
        <td>Initiate the action for the icon displayed in the softkey position.</td>
        <td>Shared Cockpit</td>
      </tr>
    </tbody>
  </table>
  <p>&nbsp;</p>
  <h4 id="g1000-multi-function-display">G1000 (Multi-Function Display)</h4>
  <table style="table-layout:auto;">
    <colgroup>
      <col>
      <col>
      <col>
      <col>
    </colgroup>
    <tbody>
      <tr>
        <th>Event ID</th>
        <th>String Name</th>
        <th>Description</th>
        <th>Multiplayer</th>
      </tr>
      <tr>
        <td><code class="inline">KEY_G1000_MFD_FLIGHTPLAN_BUTTON</code></td>
        <td>G1000_MFD_FLIGHTPLAN_BUTTON</td>
        <td>The multi-function display (MFD) should display its current flight plan.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_G1000_MFD_PROCEDURE_BUTTON</code></td>
        <td>G1000_MFD_PROCEDURE_BUTTON</td>
        <td>Turn to the Procedure page.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_G1000_MFD_ZOOMIN_BUTTON</code></td>
        <td>G1000_MFD_ZOOMIN_BUTTON</td>
        <td>Zoom in on the current map.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_G1000_MFD_ZOOMOUT_BUTTON</code></td>
        <td>G1000_MFD_ZOOMOUT_BUTTON</td>
        <td>Zoom out on the current map.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_G1000_MFD_DIRECTTO_BUTTON</code></td>
        <td>G1000_MFD_DIRECTTO_BUTTON</td>
        <td>Turn to the Direct To page.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_G1000_MFD_MENU_BUTTON</code></td>
        <td>G1000_MFD_MENU_BUTTON</td>
        <td>If a segmented flight plan is highlighted, activates the associated menu.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_G1000_MFD_CLEAR_BUTTON</code></td>
        <td>G1000_MFD_CLEAR_BUTTON</td>
        <td>Clears the current input.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_G1000_MFD_ENTER_BUTTON</code></td>
        <td>G1000_MFD_ENTER_BUTTON</td>
        <td>Enters the current input.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_G1000_MFD_CURSOR_BUTTON</code></td>
        <td>G1000_MFD_CURSOR_BUTTON</td>
        <td>Turns on or off a screen cursor.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_G1000_MFD_GROUP_KNOB_INC</code></td>
        <td>G1000_MFD_GROUP_KNOB_INC</td>
        <td>Step up through the page groups.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_G1000_MFD_GROUP_KNOB_DEC</code></td>
        <td>G1000_MFD_GROUP_KNOB_DEC</td>
        <td>Step down through the page groups.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_G1000_MFD_PAGE_KNOB_INC</code></td>
        <td>G1000_MFD_PAGE_KNOB_INC</td>
        <td>Step up through the individual pages.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_G1000_MFD_PAGE_KNOB_DEC</code></td>
        <td>G1000_MFD_PAGE_KNOB_DEC</td>
        <td>Step down through the individual pages.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_G1000_MFD_SOFTKEY1, to KEY_G1000_MFD_SOFTKEY12</code></td>
        <td>G1000_MFD_SOFTKEY1, to G1000_MFD_SOFTKEY2</td>
        <td>Initiate the action for the icon displayed in the softkey position.</td>
        <td>Shared Cockpit</td>
      </tr>
    </tbody>
  </table>
  <p>&nbsp;</p>
  <p>&nbsp;</p>
  <h3 id="aircraft-fuel-system">Aircraft Fuel System</h3>
  <table style="table-layout:auto;">
    <colgroup>
      <col>
      <col>
      <col>
      <col>
    </colgroup>
    <tbody>
      <tr>
        <th>Event ID</th>
        <th>String Name</th>
        <th>Description</th>
        <th>Multiplayer</th>
      </tr>
      <tr>
        <td><code class="inline">KEY_FUEL_SELECTOR_OFF</code></td>
        <td>FUEL_SELECTOR_OFF</td>
        <td>Turns selector 1 to OFF position</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FUEL_SELECTOR_ALL</code></td>
        <td>FUEL_SELECTOR_ALL</td>
        <td>Turns selector 1 to ALL position</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FUEL_SELECTOR_LEFT</code></td>
        <td>FUEL_SELECTOR_LEFT</td>
        <td>Turns selector 1 to LEFT position (burns from tip then aux then main)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FUEL_SELECTOR_RIGHT</code></td>
        <td>FUEL_SELECTOR_RIGHT</td>
        <td>Turns selector 1 to RIGHT position (burns from tip then aux then main)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FUEL_SELECTOR_LEFT_AUX</code></td>
        <td>FUEL_SELECTOR_LEFT_AUX</td>
        <td>Turns selector 1 to LEFT AUX position</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FUEL_SELECTOR_RIGHT_AUX</code></td>
        <td>FUEL_SELECTOR_RIGHT_AUX</td>
        <td>Turns selector 1 to RIGHT AUX position</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FUEL_SELECTOR_CENTER</code></td>
        <td>FUEL_SELECTOR_CENTER</td>
        <td>Turns selector 1 to CENTER position</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FUEL_SELECTOR_SET</code></td>
        <td>FUEL_SELECTOR_SET</td>
        <td>Sets selector 1 position (see code list below)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FUEL_SELECTOR_2_OFF</code></td>
        <td>FUEL_SELECTOR_2_OFF</td>
        <td>Turns selector 2 to OFF position</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FUEL_SELECTOR_2_ALL</code></td>
        <td>FUEL_SELECTOR_2_ALL</td>
        <td>Turns selector 2 to ALL position</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FUEL_SELECTOR_2_LEFT</code></td>
        <td>FUEL_SELECTOR_2_LEFT</td>
        <td>Turns selector 2 to LEFT position (burns from tip then aux then main)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FUEL_SELECTOR_2_RIGHT</code></td>
        <td>FUEL_SELECTOR_2_RIGHT</td>
        <td>Turns selector 2 to RIGHT position (burns from tip then aux then main)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FUEL_SELECTOR_2_LEFT_AUX</code></td>
        <td>FUEL_SELECTOR_2_LEFT_AUX</td>
        <td>Turns selector 2 to LEFT AUX position</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FUEL_SELECTOR_2_RIGHT_AUX</code></td>
        <td>FUEL_SELECTOR_2_RIGHT_AUX</td>
        <td>Turns selector 2 to RIGHT AUX position</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FUEL_SELECTOR_2_CENTER</code></td>
        <td>FUEL_SELECTOR_2_CENTER</td>
        <td>Turns selector 2 to CENTER position</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FUEL_SELECTOR_2_SET</code></td>
        <td>FUEL_SELECTOR_2_SET</td>
        <td>Sets selector 2 position (see code list below)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FUEL_SELECTOR_3_OFF</code></td>
        <td>FUEL_SELECTOR_3_OFF</td>
        <td>Turns selector 3 to OFF position</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FUEL_SELECTOR_3_ALL</code></td>
        <td>FUEL_SELECTOR_3_ALL</td>
        <td>Turns selector 3 to ALL position</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FUEL_SELECTOR_3_LEFT</code></td>
        <td>FUEL_SELECTOR_3_LEFT</td>
        <td>Turns selector 3 to LEFT position (burns from tip then aux then main)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FUEL_SELECTOR_3_RIGHT</code></td>
        <td>FUEL_SELECTOR_3_RIGHT</td>
        <td>Turns selector 3 to RIGHT position (burns from tip then aux then main)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FUEL_SELECTOR_3_LEFT_AUX</code></td>
        <td>FUEL_SELECTOR_3_LEFT_AUX</td>
        <td>Turns selector 3 to LEFT AUX position</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FUEL_SELECTOR_3_RIGHT_AUX</code></td>
        <td>FUEL_SELECTOR_3_RIGHT_AUX</td>
        <td>Turns selector 3 to RIGHT AUX position</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FUEL_SELECTOR_3_CENTER</code></td>
        <td>FUEL_SELECTOR_3_CENTER</td>
        <td>Turns selector 3 to CENTER position</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FUEL_SELECTOR_3_SET</code></td>
        <td>FUEL_SELECTOR_3_SET</td>
        <td>Sets selector 3 position (see code list below)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FUEL_SELECTOR_4_OFF</code></td>
        <td>FUEL_SELECTOR_4_OFF</td>
        <td>Turns selector 4 to OFF position</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FUEL_SELECTOR_4_ALL</code></td>
        <td>FUEL_SELECTOR_4_ALL</td>
        <td>Turns selector 4 to ALL position</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FUEL_SELECTOR_4_LEFT</code></td>
        <td>FUEL_SELECTOR_4_LEFT</td>
        <td>Turns selector 4 to LEFT position (burns from tip then aux then main)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FUEL_SELECTOR_4_RIGHT</code></td>
        <td>FUEL_SELECTOR_4_RIGHT</td>
        <td>Turns selector 4 to RIGHT position (burns from tip then aux then main)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FUEL_SELECTOR_4_LEFT_AUX</code></td>
        <td>FUEL_SELECTOR_4_LEFT_AUX</td>
        <td>Turns selector 4 to LEFT AUX position</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FUEL_SELECTOR_4_RIGHT_AUX</code></td>
        <td>FUEL_SELECTOR_4_RIGHT_AUX</td>
        <td>Turns selector 4 to RIGHT AUX position</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FUEL_SELECTOR_4_CENTER</code></td>
        <td>FUEL_SELECTOR_4_CENTER</td>
        <td>Turns selector 4 to CENTER position</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FUEL_SELECTOR_4_SET</code></td>
        <td>FUEL_SELECTOR_4_SET</td>
        <td>Sets selector 4 position (see code list below)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_CROSS_FEED_OPEN</code></td>
        <td>CROSS_FEED_OPEN</td>
        <td>Opens cross feed valve (when used in conjunction with "isolate" tank)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_CROSS_FEED_TOGGLE</code></td>
        <td>CROSS_FEED_TOGGLE</td>
        <td>Toggles crossfeed valve (when used in conjunction with "isolate" tank)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_CROSS_FEED_OFF</code></td>
        <td>CROSS_FEED_OFF</td>
        <td>Closes crossfeed valve (when used in conjunction with "isolate" tank)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FUEL_DUMP_SWITCH_SET</code></td>
        <td>FUEL_DUMP_SWITCH_SET</td>
        <td>Set to True or False. The switch can only be set to True if <code class="inline">fuel_dump_rate</code> is specified in the aircraft configuration file, which indicates that a fuel dump system exists.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_ANTIDETONATION_TANK_VALVE</code></td>
        <td>ANTIDETONATION_TANK_VALVE_TOGGLE</td>
        <td>Toggle the anti-detonation valve. Pass a value to determine which tank, if there are multiple tanks, to use. Tanks are indexed from 1. Refer to the document Notes on Aircraft Systems.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_NITROUS_TANK_VALVE</code></td>
        <td>NITROUS_TANK_VALVE_TOGGLE</td>
        <td>Toggle the nitrous valve. Pass a value to determine which tank, if there are multiple tanks, to use. Tanks are indexed from 1.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_REPAIR_AND_REFUEL</code></td>
        <td>REPAIR_AND_REFUEL</td>
        <td>Fully repair and refuel the user aircraft. Ignored if flight realism is enforced.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FUEL_DUMP_TOGGLE</code></td>
        <td>FUEL_DUMP_TOGGLE</td>
        <td>Turns on or off the fuel dump switch.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_REQUEST_FUEL</code></td>
        <td>REQUEST_FUEL_KEY</td>
        <td>Request a fuel truck. The aircraft must be in a parking spot for this to be successful.</td>
        <td>Shared Cockpit</td>
      </tr>
    </tbody>
  </table>
  <p>&nbsp;</p>
  <h4 id="fuel-selection-keys">Fuel Selection Keys</h4>
  <table style="table-layout:auto;">
    <colgroup>
      <col>
      <col>
      <col>
      <col>
    </colgroup>
    <tbody>
      <tr>
        <th>Event ID</th>
        <th>String Name</th>
        <th>Description</th>
        <th>Multiplayer</th>
      </tr>
      <tr>
        <td><code class="inline">KEY_FUEL_SELECTOR_LEFT_MAIN</code></td>
        <td>FUEL_SELECTOR_LEFT_MAIN</td>
        <td>Sets the fuel selector. Fuel will be taken in the order left tip, left aux,then main fuel tanks.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FUEL_SELECTOR_2_LEFT_MAIN</code></td>
        <td>FUEL_SELECTOR_2_LEFT_MAIN</td>
        <td>Sets the fuel selector for engine 2.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FUEL_SELECTOR_3_LEFT_MAIN</code></td>
        <td>FUEL_SELECTOR_3_LEFT_MAIN</td>
        <td>Sets the fuel selector for engine 3.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FUEL_SELECTOR_4_LEFT_MAIN</code></td>
        <td>FUEL_SELECTOR_4_LEFT_MAIN</td>
        <td>Sets the fuel selector for engine 4.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FUEL_SELECTOR_RIGHT_MAIN</code></td>
        <td>FUEL_SELECTOR_RIGHT_MAIN</td>
        <td>Sets the fuel selector. Fuel will be taken in the order right tip, right aux,then main fuel tanks.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FUEL_SELECTOR_2_RIGHT_MAIN</code></td>
        <td>FUEL_SELECTOR_2_RIGHT_MAIN</td>
        <td>Sets the fuel selector for engine 2.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FUEL_SELECTOR_3_RIGHT_MAIN</code></td>
        <td>FUEL_SELECTOR_3_RIGHT_MAIN</td>
        <td>Sets the fuel selector for engine 3.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FUEL_SELECTOR_4_RIGHT_MAIN</code></td>
        <td>FUEL_SELECTOR_4_RIGHT_MAIN</td>
        <td>Sets the fuel selector for engine 4.</td>
        <td>Shared Cockpit</td>
      </tr>
    </tbody>
  </table>
  <p>&nbsp;</p>
  <h4 id="fuel-selector-codes">Fuel Selector Codes</h4>
  <table style="table-layout:auto;width:50%;">
    <colgroup>
      <col>
      <col>
    </colgroup>
    <tbody>
      <tr>
        <th>FuelSelector</th>
        <th>Code</th>
      </tr>
      <tr>
        <td><code class="inline">FUEL_TANK_SELECTOR_OFF</code></td>
        <td>0</td>
      </tr>
      <tr>
        <td><code class="inline">FUEL_TANK_SELECTOR_ALL</code></td>
        <td>1</td>
      </tr>
      <tr>
        <td><code class="inline">FUEL_TANK_SELECTOR_LEFT</code></td>
        <td>2</td>
      </tr>
      <tr>
        <td><code class="inline">FUEL_TANK_SELECTOR_RIGHT</code></td>
        <td>3</td>
      </tr>
      <tr>
        <td><code class="inline">FUEL_TANK_SELECTOR_LEFT_AUX</code></td>
        <td>4</td>
      </tr>
      <tr>
        <td><code class="inline">FUEL_TANK_SELECTOR_RIGHT_AUX</code></td>
        <td>5</td>
      </tr>
      <tr>
        <td><code class="inline">FUEL_TANK_SELECTOR_CENTER</code></td>
        <td>6</td>
      </tr>
      <tr>
        <td><code class="inline">FUEL_TANK_SELECTOR_CENTER2</code></td>
        <td>7</td>
      </tr>
      <tr>
        <td><code class="inline">FUEL_TANK_SELECTOR_CENTER3</code></td>
        <td>8</td>
      </tr>
      <tr>
        <td><code class="inline">FUEL_TANK_SELECTOR_EXTERNAL1</code></td>
        <td>9</td>
      </tr>
      <tr>
        <td><code class="inline">FUEL_TANK_SELECTOR_EXTERNAL2</code></td>
        <td>10</td>
      </tr>
      <tr>
        <td><code class="inline">FUEL_TANK_SELECTOR_RIGHT_TIP</code></td>
        <td>11</td>
      </tr>
      <tr>
        <td><code class="inline">FUEL_TANK_SELECTOR_LEFT_TIP</code></td>
        <td>12</td>
      </tr>
      <tr>
        <td><code class="inline">FUEL_TANK_SELECTOR_CROSSFEED</code></td>
        <td>13</td>
      </tr>
      <tr>
        <td><code class="inline">FUEL_TANK_SELECTOR_CROSSFEED_L2R</code></td>
        <td>14</td>
      </tr>
      <tr>
        <td><code class="inline">FUEL_TANK_SELECTOR_CROSSFEED_R2L</code></td>
        <td>15</td>
      </tr>
      <tr>
        <td><code class="inline">FUEL_TANK_SELECTOR_BOTH</code></td>
        <td>16</td>
      </tr>
      <tr>
        <td><code class="inline">FUEL_TANK_SELECTOR_EXTERNAL_ALL</code></td>
        <td>17</td>
      </tr>
      <tr>
        <td><code class="inline">FUEL_TANK_SELECTOR_ISOLATE</code></td>
        <td>18</td>
      </tr>
    </tbody>
  </table>
  <p>&nbsp;</p>
  <p>&nbsp;</p>
  <h3 id="aircraft-avionics">Aircraft Avionics</h3>
  <table style="table-layout:auto;">
    <colgroup>
      <col>
      <col>
      <col>
      <col>
    </colgroup>
    <tbody>
      <tr>
        <th>Event ID</th>
        <th>String Name</th>
        <th>Description</th>
        <th>Multiplayer</th>
      </tr>
      <tr>
        <td><code class="inline">KEY_XPNDR</code></td>
        <td>XPNDR</td>
        <td>Sequentially selects the transponder digits for use with +/-.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ADF</code></td>
        <td>ADF</td>
        <td>Sequentially selects the <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="&#39;Automatic Direction Finding&#39; is an electronic aid to navigation that identifies the relative bearing of an aircraft from a radio beacon transmitting in the MF or LF bandwidth, such as an Non-Directional Beacon or commercial radio broadcast station." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">ADF</a> tuner digits for use with +/-. Follow byKEY_SELECT_2 for ADF 2.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_DME</code></td>
        <td>DME</td>
        <td>Selects the <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="This stands for &#39;Distance Measuring Equipment&#39; and is a type of navigation beacon - usually coupled with VOR or ILS - to enable aircraft to measure their position relative to that beacon.  A &#39;DME arc&#39; would then be a circle defined around the beacon at a given distance." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">DME</a> for use with +/-</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_COM_RADIO</code></td>
        <td>COM_RADIO</td>
        <td>Sequentially selects the COM tuner digits for use with +/-. Follow byKEY_SELECT_2 for COM 2.</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_VOR_OBS</code></td>
        <td>VOR_OBS</td>
        <td>Sequentially selects the <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="This means &#39;Very high frequency Omni-directional Range&#39;, which is a type of short-range radio navigation system for aircraft, enabling aircraft with a receiving unit to determine their position and stay on course by receiving radio signals transmitted by a network of fixed ground radio beacons." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">VOR</a> OBS for use with +/-. Follow by KEY_SELECT_2 for VOR 2.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_NAV_RADIO</code></td>
        <td>NAV_RADIO</td>
        <td>Sequentially selects the NAV tuner digits for use with +/-. Follow byKEY_SELECT_2 for NAV 2.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_COM_RADIO_WHOLE_DEC</code></td>
        <td>COM_RADIO_WHOLE_DEC</td>
        <td>Decrements COM by one MHz</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_COM_RADIO_WHOLE_INC</code></td>
        <td>COM_RADIO_WHOLE_INC</td>
        <td>Increments COM by one MHz</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_COM_RADIO_FRACT_DEC</code></td>
        <td>COM_RADIO_FRACT_DEC</td>
        <td>Decrements COM by 25 KHz</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_COM_RADIO_FRACT_INC</code></td>
        <td>COM_RADIO_FRACT_INC</td>
        <td>Increments COM by 25 KHz</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_NAV1_RADIO_WHOLE_DEC</code></td>
        <td>NAV1_RADIO_WHOLE_DEC</td>
        <td>Decrements Nav 1 by one MHz</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_NAV1_RADIO_WHOLE_INC</code></td>
        <td>NAV1_RADIO_WHOLE_INC</td>
        <td>Increments Nav 1 by one MHz</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_NAV1_RADIO_FRACT_DEC</code></td>
        <td>NAV1_RADIO_FRACT_DEC</td>
        <td>Decrements Nav 1 by 25 KHz</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_NAV1_RADIO_FRACT_INC</code></td>
        <td>NAV1_RADIO_FRACT_INC</td>
        <td>Increments Nav 1 by 25 KHz</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_NAV2_RADIO_WHOLE_DEC</code></td>
        <td>NAV2_RADIO_WHOLE_DEC</td>
        <td>Decrements Nav 2 by one MHz</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_NAV2_RADIO_WHOLE_INC</code></td>
        <td>NAV2_RADIO_WHOLE_INC</td>
        <td>Increments Nav 2 by one MHz</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_NAV2_RADIO_FRACT_DEC</code></td>
        <td>NAV2_RADIO_FRACT_DEC</td>
        <td>Decrements Nav 2 by 25 KHz</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_NAV2_RADIO_FRACT_INC</code></td>
        <td>NAV2_RADIO_FRACT_INC</td>
        <td>Increments Nav 2 by 25 KHz</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ADF_100_INC</code></td>
        <td>ADF_100_INC</td>
        <td>Increments <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="&#39;Automatic Direction Finding&#39; is an electronic aid to navigation that identifies the relative bearing of an aircraft from a radio beacon transmitting in the MF or LF bandwidth, such as an Non-Directional Beacon or commercial radio broadcast station." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">ADF</a> by 100 KHz</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ADF_10_INC</code></td>
        <td>ADF_10_INC</td>
        <td>Increments <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="&#39;Automatic Direction Finding&#39; is an electronic aid to navigation that identifies the relative bearing of an aircraft from a radio beacon transmitting in the MF or LF bandwidth, such as an Non-Directional Beacon or commercial radio broadcast station." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">ADF</a> by 10 KHz</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ADF_1_INC</code></td>
        <td>ADF_1_INC</td>
        <td>Increments <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="&#39;Automatic Direction Finding&#39; is an electronic aid to navigation that identifies the relative bearing of an aircraft from a radio beacon transmitting in the MF or LF bandwidth, such as an Non-Directional Beacon or commercial radio broadcast station." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">ADF</a> by 1 KHz</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_XPNDR_1000_INC</code></td>
        <td>XPNDR_1000_INC</td>
        <td>Increments first digit of transponder</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_XPNDR_100_INC</code></td>
        <td>XPNDR_100_INC</td>
        <td>Increments second digit of transponder</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_XPNDR_10_INC</code></td>
        <td>XPNDR_10_INC</td>
        <td>Increments third digit of transponder</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_XPNDR_1_INC</code></td>
        <td>XPNDR_1_INC</td>
        <td>Increments fourth digit of transponder</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_VOR1_OBI_DEC</code></td>
        <td>VOR1_OBI_DEC</td>
        <td>Decrements the <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="This means &#39;Very high frequency Omni-directional Range&#39;, which is a type of short-range radio navigation system for aircraft, enabling aircraft with a receiving unit to determine their position and stay on course by receiving radio signals transmitted by a network of fixed ground radio beacons." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">VOR</a> 1 OBS setting</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_VOR1_OBI_INC</code></td>
        <td>VOR1_OBI_INC</td>
        <td>Increments the <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="This means &#39;Very high frequency Omni-directional Range&#39;, which is a type of short-range radio navigation system for aircraft, enabling aircraft with a receiving unit to determine their position and stay on course by receiving radio signals transmitted by a network of fixed ground radio beacons." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">VOR</a> 1 OBS setting</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_VOR2_OBI_DEC</code></td>
        <td>VOR2_OBI_DEC</td>
        <td>Decrements the <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="This means &#39;Very high frequency Omni-directional Range&#39;, which is a type of short-range radio navigation system for aircraft, enabling aircraft with a receiving unit to determine their position and stay on course by receiving radio signals transmitted by a network of fixed ground radio beacons." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">VOR</a> 2 OBS setting</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_VOR2_OBI_INC</code></td>
        <td>VOR2_OBI_INC</td>
        <td>Increments the <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="This means &#39;Very high frequency Omni-directional Range&#39;, which is a type of short-range radio navigation system for aircraft, enabling aircraft with a receiving unit to determine their position and stay on course by receiving radio signals transmitted by a network of fixed ground radio beacons." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">VOR</a> 2 OBS setting</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ADF_100_DEC</code></td>
        <td>ADF_100_DEC</td>
        <td>Decrements <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="&#39;Automatic Direction Finding&#39; is an electronic aid to navigation that identifies the relative bearing of an aircraft from a radio beacon transmitting in the MF or LF bandwidth, such as an Non-Directional Beacon or commercial radio broadcast station." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">ADF</a> by 100 KHz</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ADF_10_DEC</code></td>
        <td>ADF_10_DEC</td>
        <td>Decrements <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="&#39;Automatic Direction Finding&#39; is an electronic aid to navigation that identifies the relative bearing of an aircraft from a radio beacon transmitting in the MF or LF bandwidth, such as an Non-Directional Beacon or commercial radio broadcast station." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">ADF</a> by 10 KHz</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ADF_1_DEC</code></td>
        <td>ADF_1_DEC</td>
        <td>Decrements <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="&#39;Automatic Direction Finding&#39; is an electronic aid to navigation that identifies the relative bearing of an aircraft from a radio beacon transmitting in the MF or LF bandwidth, such as an Non-Directional Beacon or commercial radio broadcast station." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">ADF</a> by 1 KHz</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_COM_RADIO_SET</code></td>
        <td>COM_RADIO_SET</td>
        <td>Sets COM frequency (BCD Hz)</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_NAV1_RADIO_SET</code></td>
        <td>NAV1_RADIO_SET</td>
        <td>Sets NAV 1 frequency (BCD Hz)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_NAV2_RADIO_SET</code></td>
        <td>NAV2_RADIO_SET</td>
        <td>Sets NAV 2 frequency (BCD Hz)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ADF_SET</code></td>
        <td>ADF_SET</td>
        <td>Sets <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="&#39;Automatic Direction Finding&#39; is an electronic aid to navigation that identifies the relative bearing of an aircraft from a radio beacon transmitting in the MF or LF bandwidth, such as an Non-Directional Beacon or commercial radio broadcast station." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">ADF</a> frequency (BCD Hz)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_XPNDR_SET</code></td>
        <td>XPNDR_SET</td>
        <td>Sets transponder code (BCD)</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_VOR1_SET</code></td>
        <td>VOR1_SET</td>
        <td>Sets OBS 1 (0 to 360)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_VOR2_SET</code></td>
        <td>VOR2_SET</td>
        <td>Sets OBS 2 (0 to 360)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_DME1_TOGGLE</code></td>
        <td>DME1_TOGGLE</td>
        <td>Sets <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="This stands for &#39;Distance Measuring Equipment&#39; and is a type of navigation beacon - usually coupled with VOR or ILS - to enable aircraft to measure their position relative to that beacon.  A &#39;DME arc&#39; would then be a circle defined around the beacon at a given distance." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">DME</a> display to Nav 1</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_DME2_TOGGLE</code></td>
        <td>DME2_TOGGLE</td>
        <td>Sets <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="This stands for &#39;Distance Measuring Equipment&#39; and is a type of navigation beacon - usually coupled with VOR or ILS - to enable aircraft to measure their position relative to that beacon.  A &#39;DME arc&#39; would then be a circle defined around the beacon at a given distance." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">DME</a> display to Nav 2</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_RADIO_VOR1_IDENT_DISABLE</code></td>
        <td>RADIO_VOR1_IDENT_DISABLE</td>
        <td>Turns NAV 1 ID off</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_RADIO_VOR2_IDENT_DISABLE</code></td>
        <td>RADIO_VOR2_IDENT_DISABLE</td>
        <td>Turns NAV 2 ID off</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_RADIO_DME1_IDENT_DISABLE</code></td>
        <td>RADIO_DME1_IDENT_DISABLE</td>
        <td>Turns <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="This stands for &#39;Distance Measuring Equipment&#39; and is a type of navigation beacon - usually coupled with VOR or ILS - to enable aircraft to measure their position relative to that beacon.  A &#39;DME arc&#39; would then be a circle defined around the beacon at a given distance." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">DME</a> 1 ID off</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_RADIO_DME2_IDENT_DISABLE</code></td>
        <td>RADIO_DME2_IDENT_DISABLE</td>
        <td>Turns <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="This stands for &#39;Distance Measuring Equipment&#39; and is a type of navigation beacon - usually coupled with VOR or ILS - to enable aircraft to measure their position relative to that beacon.  A &#39;DME arc&#39; would then be a circle defined around the beacon at a given distance." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">DME</a> 2 ID off</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_RADIO_ADF_IDENT_DISABLE</code></td>
        <td>RADIO_ADF_IDENT_DISABLE</td>
        <td>Turns <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="&#39;Automatic Direction Finding&#39; is an electronic aid to navigation that identifies the relative bearing of an aircraft from a radio beacon transmitting in the MF or LF bandwidth, such as an Non-Directional Beacon or commercial radio broadcast station." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">ADF</a> 1 ID off</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_RADIO_VOR1_IDENT_ENABLE</code></td>
        <td>RADIO_VOR1_IDENT_ENABLE</td>
        <td>Turns NAV 1 ID on</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_RADIO_VOR2_IDENT_ENABLE</code></td>
        <td>RADIO_VOR2_IDENT_ENABLE</td>
        <td>Turns NAV 2 ID on</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_RADIO_DME1_IDENT_ENABLE</code></td>
        <td>RADIO_DME1_IDENT_ENABLE</td>
        <td>Turns <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="This stands for &#39;Distance Measuring Equipment&#39; and is a type of navigation beacon - usually coupled with VOR or ILS - to enable aircraft to measure their position relative to that beacon.  A &#39;DME arc&#39; would then be a circle defined around the beacon at a given distance." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">DME</a> 1 ID on</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_RADIO_DME2_IDENT_ENABLE</code></td>
        <td>RADIO_DME2_IDENT_ENABLE</td>
        <td>Turns <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="This stands for &#39;Distance Measuring Equipment&#39; and is a type of navigation beacon - usually coupled with VOR or ILS - to enable aircraft to measure their position relative to that beacon.  A &#39;DME arc&#39; would then be a circle defined around the beacon at a given distance." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">DME</a> 2 ID on</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_RADIO_ADF_IDENT_ENABLE</code></td>
        <td>RADIO_ADF_IDENT_ENABLE</td>
        <td>Turns <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="&#39;Automatic Direction Finding&#39; is an electronic aid to navigation that identifies the relative bearing of an aircraft from a radio beacon transmitting in the MF or LF bandwidth, such as an Non-Directional Beacon or commercial radio broadcast station." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">ADF</a> 1 ID on</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_RADIO_VOR1_IDENT_TOGGLE</code></td>
        <td>RADIO_VOR1_IDENT_TOGGLE</td>
        <td>Toggles NAV 1 ID</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_RADIO_VOR2_IDENT_TOGGLE</code></td>
        <td>RADIO_VOR2_IDENT_TOGGLE</td>
        <td>Toggles NAV 2 ID</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_RADIO_DME1_IDENT_TOGGLE</code></td>
        <td>RADIO_DME1_IDENT_TOGGLE</td>
        <td>Toggles <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="This stands for &#39;Distance Measuring Equipment&#39; and is a type of navigation beacon - usually coupled with VOR or ILS - to enable aircraft to measure their position relative to that beacon.  A &#39;DME arc&#39; would then be a circle defined around the beacon at a given distance." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">DME</a> 1 ID</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_RADIO_DME2_IDENT_TOGGLE</code></td>
        <td>RADIO_DME2_IDENT_TOGGLE</td>
        <td>Toggles <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="This stands for &#39;Distance Measuring Equipment&#39; and is a type of navigation beacon - usually coupled with VOR or ILS - to enable aircraft to measure their position relative to that beacon.  A &#39;DME arc&#39; would then be a circle defined around the beacon at a given distance." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">DME</a> 2 ID</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_RADIO_ADF_IDENT_TOGGLE</code></td>
        <td>RADIO_ADF_IDENT_TOGGLE</td>
        <td>Toggles <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="&#39;Automatic Direction Finding&#39; is an electronic aid to navigation that identifies the relative bearing of an aircraft from a radio beacon transmitting in the MF or LF bandwidth, such as an Non-Directional Beacon or commercial radio broadcast station." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">ADF</a> 1 ID</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_RADIO_VOR1_IDENT_SET</code></td>
        <td>RADIO_VOR1_IDENT_SET</td>
        <td>Sets NAV 1 ID (on/off)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_RADIO_VOR2_IDENT_SET</code></td>
        <td>RADIO_VOR2_IDENT_SET</td>
        <td>Sets NAV 2 ID (on/off)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_RADIO_DME1_IDENT_SET</code></td>
        <td>RADIO_DME1_IDENT_SET</td>
        <td>Sets <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="This stands for &#39;Distance Measuring Equipment&#39; and is a type of navigation beacon - usually coupled with VOR or ILS - to enable aircraft to measure their position relative to that beacon.  A &#39;DME arc&#39; would then be a circle defined around the beacon at a given distance." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">DME</a> 1 ID (on/off)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_RADIO_DME2_IDENT_SET</code></td>
        <td>RADIO_DME2_IDENT_SET</td>
        <td>Sets <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="This stands for &#39;Distance Measuring Equipment&#39; and is a type of navigation beacon - usually coupled with VOR or ILS - to enable aircraft to measure their position relative to that beacon.  A &#39;DME arc&#39; would then be a circle defined around the beacon at a given distance." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">DME</a> 2 ID (on/off)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_RADIO_ADF_IDENT_SET</code></td>
        <td>RADIO_ADF_IDENT_SET</td>
        <td>Sets <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="&#39;Automatic Direction Finding&#39; is an electronic aid to navigation that identifies the relative bearing of an aircraft from a radio beacon transmitting in the MF or LF bandwidth, such as an Non-Directional Beacon or commercial radio broadcast station." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">ADF</a> 1 ID (on/off)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ADF_CARD_INC</code></td>
        <td>ADF_CARD_INC</td>
        <td>Increments <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="&#39;Automatic Direction Finding&#39; is an electronic aid to navigation that identifies the relative bearing of an aircraft from a radio beacon transmitting in the MF or LF bandwidth, such as an Non-Directional Beacon or commercial radio broadcast station." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">ADF</a> card</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ADF_CARD_DEC</code></td>
        <td>ADF_CARD_DEC</td>
        <td>Decrements <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="&#39;Automatic Direction Finding&#39; is an electronic aid to navigation that identifies the relative bearing of an aircraft from a radio beacon transmitting in the MF or LF bandwidth, such as an Non-Directional Beacon or commercial radio broadcast station." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">ADF</a> card</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ADF_CARD_SET</code></td>
        <td>ADF_CARD_SET</td>
        <td>Sets <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="&#39;Automatic Direction Finding&#39; is an electronic aid to navigation that identifies the relative bearing of an aircraft from a radio beacon transmitting in the MF or LF bandwidth, such as an Non-Directional Beacon or commercial radio broadcast station." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">ADF</a> card (0-360)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_DME_TOGGLE</code></td>
        <td>TOGGLE_DME</td>
        <td>Toggles between NAV 1 and NAV 2</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AVIONICS_MASTER_SET</code></td>
        <td>AVIONICS_MASTER_SET</td>
        <td>Sets the avionics master switch</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_AVIONICS_MASTER</code></td>
        <td>TOGGLE_AVIONICS_MASTER</td>
        <td>Toggles the avionics master switch</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_COM_STBY_RADIO_SET</code></td>
        <td>COM_STBY_RADIO_SET</td>
        <td>Sets COM 1 standby frequency (BCD Hz)</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_COM_STBY_RADIO_SWITCH_TO</code>, or <code class="inline">KEY_COM_RADIO_SWAP</code></td>
        <td>COM_STBY_RADIO_SWAP</td>
        <td>Swaps COM 1 frequency with standby</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_COM_RADIO_FRACT_DEC_CARRY</code></td>
        <td>COM_RADIO_FRACT_DEC_CARRY</td>
        <td>Decrement COM 1 frequency by 25 KHz, and carry when digit wraps</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_COM_RADIO_FRACT_INC_CARRY</code></td>
        <td>COM_RADIO_FRACT_INC_CARRY</td>
        <td>Increment COM 1 frequency by 25 KHz, and carry when digit wraps</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_COM2_RADIO_WHOLE_DEC</code></td>
        <td>COM2_RADIO_WHOLE_DEC</td>
        <td>Decrement COM 2 frequency by 1 MHz, with no carry when digit wraps</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_COM2_RADIO_WHOLE_INC</code></td>
        <td>COM2_RADIO_WHOLE_INC</td>
        <td>Increment COM 2 frequency by 1 MHz, with no carry when digit wraps</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_COM2_RADIO_FRACT_DEC</code></td>
        <td>COM2_RADIO_FRACT_DEC</td>
        <td>Decrement COM 2 frequency by 25 KHz, with no carry when digit wraps</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_COM2_RADIO_FRACT_DEC_CARRY</code></td>
        <td>COM2_RADIO_FRACT_DEC_CARRY</td>
        <td>Decrement COM 2 frequency by 25 KHz, and carry when digit wraps</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_COM2_RADIO_FRACT_INC</code></td>
        <td>COM2_RADIO_FRACT_INC</td>
        <td>Increment COM 2 frequency by 25 KHz, with no carry when digit wraps</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_COM2_RADIO_FRACT_INC_CARRY</code></td>
        <td>COM2_RADIO_FRACT_INC_CARRY</td>
        <td>Increment COM 2 frequency by 25 KHz, and carry when digit wraps</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_COM2_RADIO_SET</code></td>
        <td>COM2_RADIO_SET</td>
        <td>Sets COM 2 frequency (BCD Hz)</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_COM2_STBY_RADIO_SET</code></td>
        <td>COM2_STBY_RADIO_SET</td>
        <td>Sets COM 2 standby frequency (BCD Hz)</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_COM2_RADIO_SWAP</code></td>
        <td>COM2_RADIO_SWAP</td>
        <td>Swaps COM 2 frequency with standby</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_NAV1_RADIO_FRACT_DEC_CARRY</code></td>
        <td>NAV1_RADIO_FRACT_DEC_CARRY</td>
        <td>Decrement NAV 1 frequency by 50 KHz, and carry when digit wraps</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_NAV1_RADIO_FRACT_INC_CARRY</code></td>
        <td>NAV1_RADIO_FRACT_INC_CARRY</td>
        <td>Increment NAV 1 frequency by 50 KHz, and carry when digit wraps</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_NAV1_STBY_SET</code></td>
        <td>NAV1_STBY_SET</td>
        <td>Sets NAV 1 standby frequency (BCD Hz)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_NAV1_RADIO_SWAP</code></td>
        <td>NAV1_RADIO_SWAP</td>
        <td>Swaps NAV 1 frequency with standby</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_NAV2_RADIO_FRACT_DEC_CARRY</code></td>
        <td>NAV2_RADIO_FRACT_DEC_CARRY</td>
        <td>Decrement NAV 2 frequency by 50 KHz, and carry when digit wraps</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_NAV2_RADIO_FRACT_INC_CARRY</code></td>
        <td>NAV2_RADIO_FRACT_INC_CARRY</td>
        <td>Increment NAV 2 frequency by 50 KHz, and carry when digit wraps</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_NAV2_STBY_SET</code></td>
        <td>NAV2_STBY_SET</td>
        <td>Sets NAV 2 standby frequency (BCD Hz)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_NAV2_RADIO_SWAP</code></td>
        <td>NAV2_RADIO_SWAP</td>
        <td>Swaps NAV 2 frequency with standby</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ADF1_RADIO_TENTHS_DEC</code></td>
        <td>ADF1_RADIO_TENTHS_DEC</td>
        <td>Decrements <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="&#39;Automatic Direction Finding&#39; is an electronic aid to navigation that identifies the relative bearing of an aircraft from a radio beacon transmitting in the MF or LF bandwidth, such as an Non-Directional Beacon or commercial radio broadcast station." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">ADF</a> 1 by 0.1 KHz.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ADF1_RADIO_TENTHS_INC</code></td>
        <td>ADF1_RADIO_TENTHS_INC</td>
        <td>Increments <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="&#39;Automatic Direction Finding&#39; is an electronic aid to navigation that identifies the relative bearing of an aircraft from a radio beacon transmitting in the MF or LF bandwidth, such as an Non-Directional Beacon or commercial radio broadcast station." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">ADF</a> 1 by 0.1 KHz.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_XPNDR_1000_DEC</code></td>
        <td>XPNDR_1000_DEC</td>
        <td>Decrements first digit of transponder</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_XPNDR_100_DEC</code></td>
        <td>XPNDR_100_DEC</td>
        <td>Decrements second digit of transponder</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_XPNDR_10_DEC</code></td>
        <td>XPNDR_10_DEC</td>
        <td>Decrements third digit of transponder</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_XPNDR_1_DEC</code></td>
        <td>XPNDR_1_DEC</td>
        <td>Decrements fourth digit of transponder</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_XPNDR_DEC_CARRY</code></td>
        <td>XPNDR_DEC_CARRY</td>
        <td>Decrements fourth digit of transponder, and with carry.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_XPNDR_INC_CARRY</code></td>
        <td>XPNDR_INC_CARRY</td>
        <td>Increments fourth digit of transponder, and with carry.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ADF_FRACT_DEC_CARRY</code></td>
        <td>ADF_FRACT_DEC_CARRY</td>
        <td>Decrements <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="&#39;Automatic Direction Finding&#39; is an electronic aid to navigation that identifies the relative bearing of an aircraft from a radio beacon transmitting in the MF or LF bandwidth, such as an Non-Directional Beacon or commercial radio broadcast station." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">ADF</a> 1 frequency by 0.1 KHz, with carry</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ADF_FRACT_INC_CARRY</code></td>
        <td>ADF_FRACT_INC_CARRY</td>
        <td>Increments <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="&#39;Automatic Direction Finding&#39; is an electronic aid to navigation that identifies the relative bearing of an aircraft from a radio beacon transmitting in the MF or LF bandwidth, such as an Non-Directional Beacon or commercial radio broadcast station." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">ADF</a> 1 frequency by 0.1 KHz, with carry</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_COM1_TRANSMIT_SELECT</code></td>
        <td>COM1_TRANSMIT_SELECT</td>
        <td>Selects COM 1 to transmit</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_COM2_TRANSMIT_SELECT</code></td>
        <td>COM2_TRANSMIT_SELECT</td>
        <td>Selects COM 2 to transmit</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_COM_RECEIVE_ALL_TOGGLE</code></td>
        <td>COM_RECEIVE_ALL_TOGGLE</td>
        <td>Toggles all COM radios to receive on</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_COM_RECEIVE_ALL_SET</code></td>
        <td>COM_RECEIVE_ALL_SET</td>
        <td>Sets whether to receive on all COM radios (1,0)</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MARKER_SOUND_TOGGLE</code></td>
        <td>MARKER_SOUND_TOGGLE</td>
        <td>Toggles marker beacon sound on/off</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MARKER_SOUND_SET</code></td>
        <td>Unsupported</td>
        <td>Sets marker beacon sound (1, 0)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ADF_COMPLETE_SET</code></td>
        <td>ADF_COMPLETE_SET</td>
        <td>Sets <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="&#39;Automatic Direction Finding&#39; is an electronic aid to navigation that identifies the relative bearing of an aircraft from a radio beacon transmitting in the MF or LF bandwidth, such as an Non-Directional Beacon or commercial radio broadcast station." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">ADF</a> 1 frequency (BCD Hz)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ADF_WHOLE_INC</code></td>
        <td>ADF1_WHOLE_INC</td>
        <td>Increments <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="&#39;Automatic Direction Finding&#39; is an electronic aid to navigation that identifies the relative bearing of an aircraft from a radio beacon transmitting in the MF or LF bandwidth, such as an Non-Directional Beacon or commercial radio broadcast station." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">ADF</a> 1 by 1 KHz, with carry as digits wrap.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ADF_WHOLE_DEC</code></td>
        <td>ADF1_WHOLE_DEC</td>
        <td>Decrements <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="&#39;Automatic Direction Finding&#39; is an electronic aid to navigation that identifies the relative bearing of an aircraft from a radio beacon transmitting in the MF or LF bandwidth, such as an Non-Directional Beacon or commercial radio broadcast station." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">ADF</a> 1 by 1 KHz, with carry as digits wrap.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ADF2_100_INC</code></td>
        <td>ADF2_100_INC</td>
        <td>Increments the <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="&#39;Automatic Direction Finding&#39; is an electronic aid to navigation that identifies the relative bearing of an aircraft from a radio beacon transmitting in the MF or LF bandwidth, such as an Non-Directional Beacon or commercial radio broadcast station." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">ADF</a> 2 frequency 100 digit, with wrapping</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ADF2_10_INC</code></td>
        <td>ADF2_10_INC</td>
        <td>Increments the <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="&#39;Automatic Direction Finding&#39; is an electronic aid to navigation that identifies the relative bearing of an aircraft from a radio beacon transmitting in the MF or LF bandwidth, such as an Non-Directional Beacon or commercial radio broadcast station." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">ADF</a> 2 frequency 10 digit, with wrapping</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ADF2_1_INC</code></td>
        <td>ADF2_1_INC</td>
        <td>Increments the <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="&#39;Automatic Direction Finding&#39; is an electronic aid to navigation that identifies the relative bearing of an aircraft from a radio beacon transmitting in the MF or LF bandwidth, such as an Non-Directional Beacon or commercial radio broadcast station." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">ADF</a> 2 frequency 1 digit, with wrapping</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ADF2_RADIO_TENTHS_INC</code></td>
        <td>ADF2_RADIO_TENTHS_INC</td>
        <td>Increments <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="&#39;Automatic Direction Finding&#39; is an electronic aid to navigation that identifies the relative bearing of an aircraft from a radio beacon transmitting in the MF or LF bandwidth, such as an Non-Directional Beacon or commercial radio broadcast station." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">ADF</a> 2 frequency 1/10 digit, with wrapping</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ADF2_100_DEC</code></td>
        <td>ADF2_100_DEC</td>
        <td>Decrements the <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="&#39;Automatic Direction Finding&#39; is an electronic aid to navigation that identifies the relative bearing of an aircraft from a radio beacon transmitting in the MF or LF bandwidth, such as an Non-Directional Beacon or commercial radio broadcast station." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">ADF</a> 2 frequency 100 digit, with wrapping</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ADF2_10_DEC</code></td>
        <td>ADF2_10_DEC</td>
        <td>Decrements the <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="&#39;Automatic Direction Finding&#39; is an electronic aid to navigation that identifies the relative bearing of an aircraft from a radio beacon transmitting in the MF or LF bandwidth, such as an Non-Directional Beacon or commercial radio broadcast station." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">ADF</a> 2 frequency 10 digit, with wrapping</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ADF2_1_DEC</code></td>
        <td>ADF2_1_DEC</td>
        <td>Decrements the <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="&#39;Automatic Direction Finding&#39; is an electronic aid to navigation that identifies the relative bearing of an aircraft from a radio beacon transmitting in the MF or LF bandwidth, such as an Non-Directional Beacon or commercial radio broadcast station." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">ADF</a> 2 frequency 1 digit, with wrapping</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ADF2_RADIO_TENTHS_DEC</code></td>
        <td>ADF2_RADIO_TENTHS_DEC</td>
        <td>Decrements <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="&#39;Automatic Direction Finding&#39; is an electronic aid to navigation that identifies the relative bearing of an aircraft from a radio beacon transmitting in the MF or LF bandwidth, such as an Non-Directional Beacon or commercial radio broadcast station." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">ADF</a> 2 frequency 1/10 digit, with wrapping</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ADF2_WHOLE_INC</code></td>
        <td>ADF2_WHOLE_INC</td>
        <td>Increments <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="&#39;Automatic Direction Finding&#39; is an electronic aid to navigation that identifies the relative bearing of an aircraft from a radio beacon transmitting in the MF or LF bandwidth, such as an Non-Directional Beacon or commercial radio broadcast station." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">ADF</a> 2 by 1 KHz, with carry as digits wrap.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ADF2_WHOLE_DEC</code></td>
        <td>ADF2_WHOLE_DEC</td>
        <td>Decrements <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="&#39;Automatic Direction Finding&#39; is an electronic aid to navigation that identifies the relative bearing of an aircraft from a radio beacon transmitting in the MF or LF bandwidth, such as an Non-Directional Beacon or commercial radio broadcast station." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">ADF</a> 2 by 1 KHz, with carry as digits wrap.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ADF2_FRACT_INC_CARRY</code></td>
        <td>ADF2_FRACT_DEC_CARRY</td>
        <td>Decrements ADF 2 frequency by 0.1 KHz, with carry</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ADF2_FRACT_DEC_CARRY</code></td>
        <td>ADF2_FRACT_INC_CARRY</td>
        <td>Increments <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="&#39;Automatic Direction Finding&#39; is an electronic aid to navigation that identifies the relative bearing of an aircraft from a radio beacon transmitting in the MF or LF bandwidth, such as an Non-Directional Beacon or commercial radio broadcast station." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">ADF</a> 2 frequency by 0.1 KHz, with carry</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ADF2_COMPLETE_SET</code></td>
        <td>ADF2_COMPLETE_SET</td>
        <td>Sets <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="&#39;Automatic Direction Finding&#39; is an electronic aid to navigation that identifies the relative bearing of an aircraft from a radio beacon transmitting in the MF or LF bandwidth, such as an Non-Directional Beacon or commercial radio broadcast station." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">ADF</a> 1 frequency (BCD Hz)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_RADIO_ADF2_IDENT_DISABLE</code></td>
        <td>RADIO_ADF2_IDENT_DISABLE</td>
        <td>Turns <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="&#39;Automatic Direction Finding&#39; is an electronic aid to navigation that identifies the relative bearing of an aircraft from a radio beacon transmitting in the MF or LF bandwidth, such as an Non-Directional Beacon or commercial radio broadcast station." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">ADF</a> 2 ID off</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_RADIO_ADF2_IDENT_ENABLE</code></td>
        <td>RADIO_ADF2_IDENT_ENABLE</td>
        <td>Turns <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="&#39;Automatic Direction Finding&#39; is an electronic aid to navigation that identifies the relative bearing of an aircraft from a radio beacon transmitting in the MF or LF bandwidth, such as an Non-Directional Beacon or commercial radio broadcast station." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">ADF</a> 2 ID on</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_RADIO_ADF2_IDENT_TOGGLE</code></td>
        <td>RADIO_ADF2_IDENT_TOGGLE</td>
        <td>Toggles <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="&#39;Automatic Direction Finding&#39; is an electronic aid to navigation that identifies the relative bearing of an aircraft from a radio beacon transmitting in the MF or LF bandwidth, such as an Non-Directional Beacon or commercial radio broadcast station." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">ADF</a> 2 ID</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_RADIO_ADF2_IDENT_SET</code></td>
        <td>RADIO_ADF2_IDENT_SET</td>
        <td>Sets <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="&#39;Automatic Direction Finding&#39; is an electronic aid to navigation that identifies the relative bearing of an aircraft from a radio beacon transmitting in the MF or LF bandwidth, such as an Non-Directional Beacon or commercial radio broadcast station." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">ADF</a> 2 ID on/off (1,0)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FREQUENCY_SWAP</code></td>
        <td>FREQUENCY_SWAP</td>
        <td>Swaps frequency with standby on whichever NAV or COM radio is selected.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_GPS_DRIVES_NAV1</code></td>
        <td>TOGGLE_GPS_DRIVES_NAV1</td>
        <td>Toggles between <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="This is a shortened version of &#39;Global Positioning System&#39; which is a satellite-based radionavigation system that provides geolocation and time information to a GPS receiver anywhere on or near the Earth where there is an unobstructed line of sight to four or more GPS satellites." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">GPS</a> and NAV 1 driving NAV 1 OBS display (and AP)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_GPS_POWER_BUTTON</code></td>
        <td>GPS_POWER_BUTTON</td>
        <td>Toggles power button</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_GPS_NEAREST_BUTTON</code></td>
        <td>GPS_NEAREST_BUTTON</td>
        <td>Selects Nearest Airport Page</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_GPS_OBS_BUTTON</code></td>
        <td>GPS_OBS_BUTTON</td>
        <td>Toggles automatic sequencing of waypoints</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_GPS_MSG_BUTTON</code></td>
        <td>GPS_MSG_BUTTON</td>
        <td>Toggles the Message Page</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_GPS_MSG_BUTTON_DOWN</code></td>
        <td>GPS_MSG_BUTTON_DOWN</td>
        <td>Triggers the pressing of the message button.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_GPS_MSG_BUTTON_UP</code></td>
        <td>GPS_MSG_BUTTON_UP</td>
        <td>Triggers the release of the message button</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_GPS_FLIGHTPLAN_BUTTON</code></td>
        <td>GPS_FLIGHTPLAN_BUTTON</td>
        <td>Displays the programmed flightplan.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_GPS_TERRAIN_BUTTON</code></td>
        <td>GPS_TERRAIN_BUTTON</td>
        <td>Displays terrain information on default display</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_GPS_PROCEDURE_BUTTON</code></td>
        <td>GPS_PROCEDURE_BUTTON</td>
        <td>Displays the approach procedure page.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_GPS_ZOOMIN_BUTTON</code></td>
        <td>GPS_ZOOMIN_BUTTON</td>
        <td>Zooms in default display</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_GPS_ZOOMOUT_BUTTON</code></td>
        <td>GPS_ZOOMOUT_BUTTON</td>
        <td>Zooms out default display</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_GPS_DIRECTTO_BUTTON</code></td>
        <td>GPS_DIRECTTO_BUTTON</td>
        <td>Brings up the "Direct To"&nbsp;page</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_GPS_MENU_BUTTON</code></td>
        <td>GPS_MENU_BUTTON</td>
        <td>Brings up page to select active legs in a flightplan.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_GPS_CLEAR_BUTTON</code></td>
        <td>GPS_CLEAR_BUTTON</td>
        <td>Clears entered data on a page</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_GPS_CLEAR_ALL_BUTTON</code></td>
        <td>GPS_CLEAR_ALL_BUTTON</td>
        <td>Clears all data immediately</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_GPS_CLEAR_BUTTON_DOWN</code></td>
        <td>GPS_CLEAR_BUTTON_DOWN</td>
        <td>Triggers the pressing of the Clear button</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_GPS_CLEAR_BUTTON_UP</code></td>
        <td>GPS_CLEAR_BUTTON_UP</td>
        <td>Triggers the release of the Clear button.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_GPS_ENTER_BUTTON</code></td>
        <td>GPS_ENTER_BUTTON</td>
        <td>Approves entered data.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_GPS_CURSOR_BUTTON</code></td>
        <td>GPS_CURSOR_BUTTON</td>
        <td>Selects GPS cursor</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_GPS_GROUP_KNOB_INC</code></td>
        <td>GPS_GROUP_KNOB_INC</td>
        <td>Increments cursor</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_GPS_GROUP_KNOB_DEC</code></td>
        <td>GPS_GROUP_KNOB_DEC</td>
        <td>Decrements cursor</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_GPS_PAGE_KNOB_INC</code></td>
        <td>GPS_PAGE_KNOB_INC</td>
        <td>Increments through pages</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_GPS_PAGE_KNOB_DEC</code></td>
        <td>GPS_PAGE_KNOB_DEC</td>
        <td>Decrements through pages</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_DME_SELECT</code></td>
        <td>DME_SELECT</td>
        <td>Selects one of the two <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="This stands for &#39;Distance Measuring Equipment&#39; and is a type of navigation beacon - usually coupled with VOR or ILS - to enable aircraft to measure their position relative to that beacon.  A &#39;DME arc&#39; would then be a circle defined around the beacon at a given distance." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">DME</a> systems (1,2).</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_RADIO_SELECTED_DME_IDENT_ENABLE</code></td>
        <td>RADIO_SELECTED_DME_IDENT_ENABLE</td>
        <td>Turns on the identification sound for the selected <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="This stands for &#39;Distance Measuring Equipment&#39; and is a type of navigation beacon - usually coupled with VOR or ILS - to enable aircraft to measure their position relative to that beacon.  A &#39;DME arc&#39; would then be a circle defined around the beacon at a given distance." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">DME</a>.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_RADIO_SELECTED_DME_IDENT_DISABLE</code></td>
        <td>RADIO_SELECTED_DME_IDENT_DISABLE</td>
        <td>Turns off the identification sound for the selected <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="This stands for &#39;Distance Measuring Equipment&#39; and is a type of navigation beacon - usually coupled with VOR or ILS - to enable aircraft to measure their position relative to that beacon.  A &#39;DME arc&#39; would then be a circle defined around the beacon at a given distance." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">DME</a>.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_RADIO_SELECTED_DME_IDENT_SET</code></td>
        <td>RADIO_SELECTED_DME_IDENT_SET</td>
        <td>Sets the <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="This stands for &#39;Distance Measuring Equipment&#39; and is a type of navigation beacon - usually coupled with VOR or ILS - to enable aircraft to measure their position relative to that beacon.  A &#39;DME arc&#39; would then be a circle defined around the beacon at a given distance." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">DME</a> identification sound to the given filename.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_RADIO_SELECTED_DME_IDENT_TOGGLE</code></td>
        <td>RADIO_SELECTED_DME_IDENT_TOGGLE</td>
        <td>Turns on or off the identification sound for the selected <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#" data-popovertext="This stands for &#39;Distance Measuring Equipment&#39; and is a type of navigation beacon - usually coupled with VOR or ILS - to enable aircraft to measure their position relative to that beacon.  A &#39;DME arc&#39; would then be a circle defined around the beacon at a given distance." data-rhwidget="TextPopOver" aria-haspopup="true" role="button">DME</a>.</td>
        <td>Shared Cockpit</td>
      </tr>
    </tbody>
  </table>
  <p>&nbsp;</p>
  <p>&nbsp;</p>
  <h3 id="aircraft-instruments">Aircraft Instruments</h3>
  <table style="table-layout:auto;">
    <colgroup>
      <col>
      <col>
      <col>
      <col>
    </colgroup>
    <tbody>
      <tr>
        <th>Event ID</th>
        <th>String Name</th>
        <th>Description</th>
        <th>Multiplayer</th>
      </tr>
      <tr>
        <td><code class="inline">KEY_EGT</code></td>
        <td>EGT</td>
        <td>Selects EGT bug for +/-</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_EGT_INC</code></td>
        <td>EGT_INC</td>
        <td>Increments EGT bugs</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_EGT_DEC</code></td>
        <td>EGT_DEC</td>
        <td>Decrements EGT bugs</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_EGT_SET</code></td>
        <td>EGT_SET</td>
        <td>Sets EGT bugs (0 to 32767)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_BAROMETRIC</code></td>
        <td>BAROMETRIC</td>
        <td>Syncs altimeter setting to sea level pressure, or 29.92 if above 18000 feet</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_GYRO_DRIFT_INC</code></td>
        <td>GYRO_DRIFT_INC</td>
        <td>Increments heading indicator</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_GYRO_DRIFT_DEC</code></td>
        <td>GYRO_DRIFT_DEC</td>
        <td>Decrements heading indicator</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_KOHLSMAN_INC</code></td>
        <td>KOHLSMAN_INC</td>
        <td>Increments altimeter setting</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_KOHLSMAN_DEC</code></td>
        <td>KOHLSMAN_DEC</td>
        <td>Decrements altimeter setting</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_KOHLSMAN_SET</code></td>
        <td>KOHLSMAN_SET</td>
        <td>Sets altimeter setting (Millibars * 16)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TRUE_AIRSPEED_CALIBRATE_INC</code></td>
        <td>TRUE_AIRSPEED_CAL_INC</td>
        <td>Increments airspeed indicators true airspeed reference card</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TRUE_AIRSPEED_CALIBRATE_DEC</code></td>
        <td>TRUE_AIRSPEED_CAL_DEC</td>
        <td>Decrements airspeed indicators true airspeed reference card</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TRUE_AIRSPEED_CAL_SET</code></td>
        <td>TRUE_AIRSPEED_CAL_SET</td>
        <td>Sets airspeed indicators true airspeed reference card (degrees, where 0 is standard sea level conditions)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_EGT1_INC</code></td>
        <td>EGT1_INC</td>
        <td>Increments EGT bug 1</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_EGT1_DEC</code></td>
        <td>EGT1_DEC</td>
        <td>Decrements EGT bug 1</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_EGT1_SET</code></td>
        <td>EGT1_SET</td>
        <td>Sets EGT bug 1 (0 to 32767)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_EGT2_INC</code></td>
        <td>EGT2_INC</td>
        <td>Increments EGT bug 2</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_EGT2_DEC</code></td>
        <td>EGT2_DEC</td>
        <td>Decrements EGT bug 2</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_EGT2_SET</code></td>
        <td>EGT2_SET</td>
        <td>Sets EGT bug 2 (0 to 32767)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_EGT3_INC</code></td>
        <td>EGT3_INC</td>
        <td>Increments EGT bug 3</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_EGT3_DEC</code></td>
        <td>EGT3_DEC</td>
        <td>Decrements EGT bug 3</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_EGT3_SET</code></td>
        <td>EGT3_SET</td>
        <td>Sets EGT bug 3 (0 to 32767)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_EGT4_INC</code></td>
        <td>EGT4_INC</td>
        <td>Increments EGT bug 4</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_EGT4_DEC</code></td>
        <td>EGT4_DEC</td>
        <td>Decrements EGT bug 4</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_EGT4_SET</code></td>
        <td>EGT4_SET</td>
        <td>Sets EGT bug 4 (0 to 32767)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ATTITUDE_BARS_POSITION_INC</code></td>
        <td>ATTITUDE_BARS_POSITION_UP</td>
        <td>Increments attitude indicator pitch reference bars</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ATTITUDE_BARS_POSITION_DEC</code></td>
        <td>ATTITUDE_BARS_POSITION_DOWN</td>
        <td>Decrements attitude indicator pitch reference bars</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_ATTITUDE_CAGE</code></td>
        <td>ATTITUDE_CAGE_BUTTON</td>
        <td>Cages attitude indicator at 0 pitch and bank</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_RESET_G_FORCE_INDICATOR</code></td>
        <td>RESET_G_FORCE_INDICATOR</td>
        <td>Resets max/min indicated G force to 1.0.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_RESET_MAX_RPM_INDICATOR</code></td>
        <td>RESET_MAX_RPM_INDICATOR</td>
        <td>Reset max indicated engine rpm to 0.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_HEADING_GYRO_SET</code></td>
        <td>HEADING_GYRO_SET</td>
        <td>Sets heading indicator to 0 drift error.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_GYRO_DRIFT_SET</code></td>
        <td>GYRO_DRIFT_SET</td>
        <td>Sets heading indicator drift angle (degrees).</td>
        <td>Shared Cockpit</td>
      </tr>
    </tbody>
  </table>
  <p>&nbsp;</p>
  <p>&nbsp;</p>
  <h3 id="aircraft-lights">Aircraft Lights</h3>
  <table style="table-layout:auto;">
    <colgroup>
      <col>
      <col>
      <col>
      <col>
    </colgroup>
    <tbody>
      <tr>
        <th>Event ID</th>
        <th>String Name</th>
        <th>Description</th>
        <th>Multiplayer</th>
      </tr>
      <tr>
        <td><code class="inline">KEY_STROBES_TOGGLE</code></td>
        <td>STROBES_TOGGLE</td>
        <td>Toggle strobe lights</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ALL_LIGHTS_TOGGLE</code></td>
        <td>ALL_LIGHTS_TOGGLE</td>
        <td>Toggle all lights</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PANEL_LIGHTS_TOGGLE</code></td>
        <td>PANEL_LIGHTS_TOGGLE</td>
        <td>Toggle panel lights</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_LANDING_LIGHTS_TOGGLE</code></td>
        <td>LANDING_LIGHTS_TOGGLE</td>
        <td>Toggle landing lights</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_LANDING_LIGHT_UP</code></td>
        <td>LANDING_LIGHT_UP</td>
        <td>Rotate landing light up</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_LANDING_LIGHT_DOWN</code></td>
        <td>LANDING_LIGHT_DOWN</td>
        <td>Rotate landing light down</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_LANDING_LIGHT_LEFT</code></td>
        <td>LANDING_LIGHT_LEFT</td>
        <td>Rotate landing light left</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_LANDING_LIGHT_RIGHT</code></td>
        <td>LANDING_LIGHT_RIGHT</td>
        <td>Rotate landing light right</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_LANDING_LIGHT_HOME</code></td>
        <td>LANDING_LIGHT_HOME</td>
        <td>Return landing light to default position</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_STROBES_ON</code></td>
        <td>STROBES_ON</td>
        <td>Turn strobe lights on</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_STROBES_OFF</code></td>
        <td>STROBES_OFF</td>
        <td>Turn strobe light off</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_STROBES_SET</code></td>
        <td>STROBES_SET</td>
        <td>Set strobe lights on/off (1,0)</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PANEL_LIGHTS_ON</code></td>
        <td>PANEL_LIGHTS_ON</td>
        <td>Turn panel lights on</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PANEL_LIGHTS_OFF</code></td>
        <td>PANEL_LIGHTS_OFF</td>
        <td>Turn panel lights off</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PANEL_LIGHTS_SET</code></td>
        <td>PANEL_LIGHTS_SET</td>
        <td>Set panel lights on/off (1,0)</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_LANDING_LIGHTS_ON</code></td>
        <td>LANDING_LIGHTS_ON</td>
        <td>Turn landing lights on</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_LANDING_LIGHTS_OFF</code></td>
        <td>LANDING_LIGHTS_OFF</td>
        <td>Turn landing lights off</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_LANDING_LIGHTS_SET</code></td>
        <td>LANDING_LIGHTS_SET</td>
        <td>Set landing lights on/off (1,0)</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_BEACON_LIGHTS</code></td>
        <td>TOGGLE_BEACON_LIGHTS</td>
        <td>Toggle beacon lights</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_TAXI_LIGHTS</code></td>
        <td>TOGGLE_TAXI_LIGHTS</td>
        <td>Toggle taxi lights</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_LOGO_LIGHTS</code></td>
        <td>TOGGLE_LOGO_LIGHTS</td>
        <td>Toggle logo lights</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_RECOGNITION_LIGHTS</code></td>
        <td>TOGGLE_RECOGNITION_LIGHTS</td>
        <td>Toggle recognition lights</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_WING_LIGHTS</code></td>
        <td>TOGGLE_WING_LIGHTS</td>
        <td>Toggle wing lights</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_NAV_LIGHTS</code></td>
        <td>TOGGLE_NAV_LIGHTS</td>
        <td>Toggle navigation lights</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_CABIN_LIGHTS</code></td>
        <td>TOGGLE_CABIN_LIGHTS</td>
        <td>Toggle cockpit/cabin lights</td>
        <td>All aircraft</td>
      </tr>
    </tbody>
  </table>
  <p>&nbsp;</p>
  <p>&nbsp;</p>
  <h3 id="aircraft-failures">Aircraft Failures</h3>
  <table style="table-layout:auto;">
    <colgroup>
      <col>
      <col>
      <col>
      <col>
    </colgroup>
    <tbody>
      <tr>
        <th>Event ID</th>
        <th>String Name</th>
        <th>Description</th>
        <th>Multiplayer</th>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_VACUUM_FAILURE</code></td>
        <td>TOGGLE_VACUUM_FAILURE</td>
        <td>Toggle vacuum system failure</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_ELECTRICAL_FAILURE</code></td>
        <td>TOGGLE_ELECTRICAL_FAILURE</td>
        <td>Toggle electrical system failure</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_PITOT_BLOCKAGE</code></td>
        <td>TOGGLE_PITOT_BLOCKAGE</td>
        <td>Toggles blocked pitot tube</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_STATIC_PORT_BLOCKAGE</code></td>
        <td>TOGGLE_STATIC_PORT_BLOCKAGE</td>
        <td>Toggles blocked static port</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_HYDRAULIC_FAILURE</code></td>
        <td>TOGGLE_HYDRAULIC_FAILURE</td>
        <td>Toggles hydraulic system failure</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_TOTAL_BRAKE_FAILURE</code></td>
        <td>TOGGLE_TOTAL_BRAKE_FAILURE</td>
        <td>Toggles brake failure (both)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_LEFT_BRAKE_FAILURE</code></td>
        <td>TOGGLE_LEFT_BRAKE_FAILURE</td>
        <td>Toggles left brake failure</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_RIGHT_BRAKE_FAILURE</code></td>
        <td>TOGGLE_RIGHT_BRAKE_FAILURE</td>
        <td>Toggles right brake failure</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_ENGINE1_FAILURE</code></td>
        <td>TOGGLE_ENGINE1_FAILURE</td>
        <td>Toggle engine 1 failure</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_ENGINE2_FAILURE</code></td>
        <td>TOGGLE_ENGINE2_FAILURE</td>
        <td>Toggle engine 2 failure</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_ENGINE3_FAILURE</code></td>
        <td>TOGGLE_ENGINE3_FAILURE</td>
        <td>Toggle engine 3 failure</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_ENGINE4_FAILURE</code></td>
        <td>TOGGLE_ENGINE4_FAILURE</td>
        <td>Toggle engine 4 failure</td>
        <td>Shared Cockpit</td>
      </tr>
    </tbody>
  </table>
  <p>&nbsp;</p>
  <p>&nbsp;</p>
  <h3 id="aircraft-miscellaneous-systems">Aircraft Miscellaneous Systems</h3>
  <table style="table-layout:auto;">
    <colgroup>
      <col>
      <col>
      <col>
      <col>
    </colgroup>
    <tbody>
      <tr>
        <th>Event ID</th>
        <th>String Name</th>
        <th>Description</th>
        <th>Multiplayer</th>
      </tr>
      <tr>
        <td><code class="inline">KEY_SMOKE_TOGGLE</code></td>
        <td>SMOKE_TOGGLE</td>
        <td>Toggle smoke system switch</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_GEAR_TOGGLE</code></td>
        <td>GEAR_TOGGLE</td>
        <td>Toggle gear handle</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_BRAKES</code></td>
        <td>BRAKES</td>
        <td>Increment brake pressure</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_GEAR_SET</code></td>
        <td>GEAR_SET</td>
        <td>Sets gear handle position up/down (0,1)</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_BRAKES_LEFT</code></td>
        <td>BRAKES_LEFT</td>
        <td>Increments left brake pressure</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_BRAKES_RIGHT</code></td>
        <td>BRAKES_RIGHT</td>
        <td>Increments right brake pressure</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PARKING_BRAKES</code></td>
        <td>PARKING_BRAKES</td>
        <td>Toggles parking brake on/off</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_GEAR_PUMP</code></td>
        <td>GEAR_PUMP</td>
        <td>Increments emergency gear extension</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PITOT_HEAT_TOGGLE</code></td>
        <td>PITOT_HEAT_TOGGLE</td>
        <td>Toggles pitot heat switch</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SMOKE_ON</code></td>
        <td>SMOKE_ON</td>
        <td>Turns smoke system on</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SMOKE_OFF</code></td>
        <td>SMOKE_OFF</td>
        <td>Turns smoke system off</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SMOKE_SET</code></td>
        <td>SMOKE_SET</td>
        <td>Sets smoke system on/off (1,0)</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PITOT_HEAT_ON</code></td>
        <td>PITOT_HEAT_ON</td>
        <td>Turns pitot heat switch on</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PITOT_HEAT_OFF</code></td>
        <td>PITOT_HEAT_OFF</td>
        <td>Turns pitot heat switch off</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PITOT_HEAT_SET</code></td>
        <td>PITOT_HEAT_SET</td>
        <td>Sets pitot heat switch on/off (1,0)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_GEAR_UP</code></td>
        <td>GEAR_UP</td>
        <td>Sets gear handle in UP position</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_GEAR_DOWN</code></td>
        <td>GEAR_DOWN</td>
        <td>Sets gear handle in DOWN position</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_MASTER_BATTERY</code></td>
        <td>TOGGLE_MASTER_BATTERY</td>
        <td>Toggles main battery switch</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_MASTER_ALTERNATOR</code></td>
        <td>TOGGLE_MASTER_ALTERNATOR</td>
        <td>Toggles main alternator/generator switch</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_ELECTRIC_VACUUM_PUMP</code></td>
        <td>TOGGLE_ELECTRIC_VACUUM_PUMP</td>
        <td>Toggles backup electric vacuum pump</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_ALTERNATE_STATIC</code></td>
        <td>TOGGLE_ALTERNATE_STATIC</td>
        <td>Toggles alternate static pressure port</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_DECISION_HEIGHT_DEC</code></td>
        <td>DECREASE_DECISION_HEIGHT</td>
        <td>Decrements decision height reference</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_DECISION_HEIGHT_INC</code></td>
        <td>INCREASE_DECISION_HEIGHT</td>
        <td>Increments decision height reference</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_STRUCTURAL_DEICE</code></td>
        <td>TOGGLE_STRUCTURAL_DEICE</td>
        <td>Toggles structural deice switch</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_PROPELLER_DEICE</code></td>
        <td>TOGGLE_PROPELLER_DEICE</td>
        <td>Toggles propeller deice switch</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_ALTERNATOR1</code></td>
        <td>TOGGLE_ALTERNATOR1</td>
        <td>Toggles alternator/generator 1 switch</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_ALTERNATOR2</code></td>
        <td>TOGGLE_ALTERNATOR2</td>
        <td>Toggles alternator/generator 2 switch</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_ALTERNATOR3</code></td>
        <td>TOGGLE_ALTERNATOR3</td>
        <td>Toggles alternator/generator 3 switch</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_ALTERNATOR4</code></td>
        <td>TOGGLE_ALTERNATOR4</td>
        <td>Toggles alternator/generator 4 switch</td>
        <td>All aircraft</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_MASTER_BATTERY_ALTERNATOR</code></td>
        <td>TOGGLE_MASTER_BATTERY_ALTERNATOR</td>
        <td>Toggles master battery and alternator switch</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AXIS_LEFT_BRAKE_SET</code></td>
        <td>AXIS_LEFT_BRAKE_SET</td>
        <td>Sets left brake position from axis controller (e.g.&nbsp;joystick). -16383 (0brakes) to +16383 (max brakes)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AXIS_RIGHT_BRAKE_SET</code></td>
        <td>AXIS_RIGHT_BRAKE_SET</td>
        <td>Sets right brake position from axis controller (e.g.&nbsp;joystick). -16383 (0brakes) to +16383 (max brakes)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_AIRCRAFT_EXIT</code></td>
        <td>TOGGLE_AIRCRAFT_EXIT</td>
        <td>Toggles primary door open/close. Follow by KEY_SELECT_2, etc for subsequent doors.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_WING_FOLD</code></td>
        <td>TOGGLE_WING_FOLD</td>
        <td>Toggles wing folding</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SET_WING_FOLD</code></td>
        <td>SET_WING_FOLD</td>
        <td>Sets the wings into the folded position suitable for storage, typically on a carrier. Takes a value:1 -fold wings, 0 - unfold wings</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_TAIL_HOOK_HANDLE</code></td>
        <td>TOGGLE_TAIL_HOOK_HANDLE</td>
        <td>Toggles tail hook</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SET_TAIL_HOOK_HANDLE</code></td>
        <td>SET_TAIL_HOOK_HANDLE</td>
        <td>Sets the tail hook handle. Takes a value: 1 - set tail hook, 0 - retract tail hook</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_WATER_RUDDER</code></td>
        <td>TOGGLE_WATER_RUDDER</td>
        <td>Toggles water rudders</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PUSHBACK_SET</code></td>
        <td>TOGGLE_PUSHBACK</td>
        <td>Toggles pushback.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TUG_HEADING</code></td>
        <td>KEY_TUG_HEADING</td>
        <td>Triggers tug and sets the desired heading. The units are a 32 bit integer (0 to4294967295) which represent 0 to 360 degrees. To set a 45 degree angle, for example, set the value to 4294967295 / 8.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TUG_SPEED</code></td>
        <td>KEY_TUG_SPEED</td>
        <td>Triggers tug, and sets desired speed, in feet per second. The speed can be bothpositive (forward movement) and negative (backward movement).</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TUG_DISABLE</code></td>
        <td>TUG_DISABLE</td>
        <td>Disables tug</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_MASTER_IGNITION_SWITCH</code></td>
        <td>TOGGLE_MASTER_IGNITION_SWITCH</td>
        <td>Toggles master ignition switch</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_TAILWHEEL_LOCK</code></td>
        <td>TOGGLE_TAILWHEEL_LOCK</td>
        <td>Toggles tail wheel lock</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ADD_FUEL_QUANTITY</code></td>
        <td>ADD_FUEL_QUANTITY</td>
        <td>Adds fuel to the aircraft, 25% of capacity by default. 0 to 65535 (max fuel) canbe passed.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOW_PLANE_RELEASE</code></td>
        <td>TOW_PLANE_RELEASE</td>
        <td>Release a towed aircraft, usually a glider.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_REQUEST_TOW_PLANE</code></td>
        <td>TOW_PLANE_REQUEST</td>
        <td>Request a tow plane. The user aircraft must be tow-able, stationary, on the ground and not already attached for this to succeed.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_RELEASE_DROPPABLE_OBJECTS</code></td>
        <td>RELEASE_DROPPABLE_OBJECTS</td>
        <td>Release one droppable object. Multiple key events will release multiple objects.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_RETRACT_FLOAT_SWITCH_DEC</code></td>
        <td>RETRACT_FLOAT_SWITCH_DEC</td>
        <td>If the plane has retractable floats, moves the retract position from Extend to Neutral, or Neutral to Retract.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_RETRACT_FLOAT_SWITCH_INC</code></td>
        <td>RETRACT_FLOAT_SWITCH_INC</td>
        <td>If the plane has retractable floats, moves the retract position from Retract to Neutral, or Neutral to Extend.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_WATER_BALLAST_VALVE</code></td>
        <td>TOGGLE_WATER_BALLAST_VALVE</td>
        <td>Turn the water ballast valve on or off.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_VARIOMETER_SWITCH</code></td>
        <td>TOGGLE_VARIOMETER_SWITCH</td>
        <td>Turn the variometer on or off.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_TURN_INDICATOR_SWITCH</code></td>
        <td>TOGGLE_TURN_INDICATOR_SWITCH</td>
        <td>Turn the turn indicator on or off.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_APU_STARTER</code></td>
        <td>APU_STARTER</td>
        <td>Start up the auxiliary power unit (APU).</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_APU_OFF_SWITCH</code></td>
        <td>APU_OFF_SWITCH</td>
        <td>Turn the APU off.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_APU_GENERATOR_SWITCH_TOGGLE</code></td>
        <td>APU_GENERATOR_SWITCH_TOGGLE</td>
        <td>Turn the auxiliary generator on or off.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_APU_GENERATOR_SWITCH_SET</code></td>
        <td>APU_GENERATOR_SWITCH_SET</td>
        <td>Set the auxiliary generator switch (0,1).</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_EXTINGUISH_ENGINE_FIRE</code></td>
        <td>EXTINGUISH_ENGINE_FIRE</td>
        <td>Takes a two digit argument.The first digit represents the fire extinguisher index, and the second represents the engine index.For example,11 would represent using bottle 1 on engine 1.21 would represent using bottle 2 on engine 1.Typical entries for a twin engine aircraft would be 11 and 22.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_HYDRAULIC_SWITCH_TOGGLE</code></td>
        <td>HYDRAULIC_SWITCH_TOGGLE</td>
        <td>Turn the hydraulic switch on or off.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_BLEED_AIR_SOURCE_CONTROL_INC</code></td>
        <td>BLEED_AIR_SOURCE_CONTROL_INC</td>
        <td>Increases the bleed air source control.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_BLEED_AIR_SOURCE_CONTROL_DEC</code></td>
        <td>BLEED_AIR_SOURCE_CONTROL_DEC</td>
        <td>Decreases the bleed air source control.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_BLEED_AIR_SOURCE_CONTROL_SET</code></td>
        <td>BLEED_AIR_SOURCE_CONTROL_SET</td>
        <td>Set to one of: 0: auto1: off2: apu3: engines</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TURBINE_IGNITION_SWITCH_TOGGLE</code></td>
        <td>TURBINE_IGNITION_SWITCH_TOGGLE</td>
        <td>Turn the turbine ignition switch on or off.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_CABIN_NO_SMOKING_ALERT_ SWITCH_TOGGLE</code></td>
        <td>CABIN_NO_SMOKING_ALERT_ SWITCH_TOGGLE</td>
        <td>Turn the "No smoking" alert on or off.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_CABIN_SEATBELTS_ALERT_ SWITCH_TOGGLE</code></td>
        <td>CABIN_SEATBELTS_ALERT_ SWITCH_TOGGLE</td>
        <td>Turn the "Fasten seatbelts" alert on or off.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ANTISKID_BRAKES_TOGGLE</code></td>
        <td>ANTISKID_BRAKES_TOGGLE</td>
        <td>Turn the anti-skid braking system on or off.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_GPWS_SWITCH_TOGGLE</code></td>
        <td>GPWS_SWITCH_TOGGLE</td>
        <td>Turn the g round proximity warning system (GPWS) on or off.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MANUAL_FUEL_PRESSURE_PUMP</code></td>
        <td>MANUAL_FUEL_PRESSURE_PUMP</td>
        <td>Activate the manual fuel pressure pump.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ANNUNCIATOR_SWITCH_TOGGLE</code></td>
        <td>ANNUNCIATOR_SWITCH_TOGGLE</td>
        <td>Togles the annunciator switch.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ANNUNCIATOR_SWITCH_ON</code></td>
        <td>ANNUNCIATOR_SWITCH_ON</td>
        <td>Turns on the annunciator switch.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ANNUNCIATOR_SWITCH_OFF</code></td>
        <td>ANNUNCIATOR_SWITCH_OFF</td>
        <td>Turns off the annunciator switch.</td>
        <td>Shared Cockpit</td>
      </tr>
    </tbody>
  </table>
  <p>&nbsp;</p>
  <h4 id="nose-wheel-steering">Nose Wheel Steering</h4>
  <table style="table-layout:auto;">
    <colgroup>
      <col>
      <col>
      <col>
      <col>
    </colgroup>
    <tbody>
      <tr>
        <th>Event ID</th>
        <th>String Name</th>
        <th>Description</th>
        <th>Multiplayer</th>
      </tr>
      <tr>
        <td><code class="inline">KEY_STEERING_INC</code></td>
        <td>STEERING_INC</td>
        <td>Increments the nose wheel steering position by 5 percent.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_STEERING_DEC</code></td>
        <td>STEERING_DEC</td>
        <td>Decrements the nose wheel steering position by 5 percent.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_STEERING_SET</code></td>
        <td>STEERING_SET</td>
        <td>Sets the value of the nose wheel steering position. Zero is straight ahead(-16383, far left +16383, far right).</td>
        <td>Shared Cockpit</td>
      </tr>
    </tbody>
  </table>
  <p>&nbsp;</p>
  <h4 id="cabin-pressurization">Cabin Pressurization</h4>
  <table style="table-layout:auto;">
    <colgroup>
      <col>
      <col>
      <col>
      <col>
    </colgroup>
    <tbody>
      <tr>
        <th>Event ID</th>
        <th>String Name</th>
        <th>Description</th>
        <th>Multiplayer</th>
      </tr>
      <tr>
        <td><code class="inline">KEY_PRESSURIZATION_PRESSURE_ALT_INC</code></td>
        <td>KEY_PRESSURIZATION_PRESSURE_ALT_INC</td>
        <td>Increases the altitude that the cabin is pressurized to.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PRESSURIZATION_PRESSURE_ALT_DEC</code></td>
        <td>KEY_PRESSURIZATION_PRESSURE_ALT_DEC</td>
        <td>Decreases the altitude that the cabin is pressurized to.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PRESSURIZATION_CLIMB_RATE_INC</code></td>
        <td>PRESSURIZATION_CLIMB_RATE_INC</td>
        <td>Sets the rate at which cabin pressurization is increased.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PRESSURIZATION_CLIMB_RATE_DEC</code></td>
        <td>PRESSURIZATION_CLIMB_RATE_DEC</td>
        <td>Sets the rate at which cabin pressurization is decreased.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PRESSURIZATION_PRESSURE_DUMP _SWTICH</code></td>
        <td>PRESSURIZATION_PRESSURE_DUMP _SWTICH</td>
        <td>Sets the cabin pressure to the outside air pressure.</td>
        <td>Shared Cockpit</td>
      </tr>
    </tbody>
  </table>
  <p>&nbsp;</p>
  <h4 id="catapult-launches">Catapult Launches</h4>
  <table style="table-layout:auto;">
    <colgroup>
      <col>
      <col>
      <col>
      <col>
    </colgroup>
    <tbody>
      <tr>
        <th>Event ID</th>
        <th>String Name</th>
        <th>Description</th>
        <th>Multiplayer</th>
      </tr>
      <tr>
        <td><code class="inline">KEY_TAKEOFF_ASSIST_ARM_TOGGLE</code></td>
        <td>TAKEOFF_ASSIST_ARM_TOGGLE</td>
        <td>Deploy or remove the assist arm. Refer to the document Notes on Aircraft Systems.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TAKEOFF_ASSIST_ARM_SET</code></td>
        <td>TAKEOFF_ASSIST_ARM_SET</td>
        <td>Value: TRUE request set FALSE request unset</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TAKEOFF_ASSIST_FIRE</code></td>
        <td>TAKEOFF_ASSIST_FIRE</td>
        <td>If everything is set up correctly. Launch from the catapult.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_LAUNCH_BAR_SWITCH</code></td>
        <td>TOGGLE_LAUNCH_BAR_SWITCH</td>
        <td>Toggle the request for the launch bar to be installed or removed.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SET_LAUNCHBAR_SWITCH</code></td>
        <td>SET_LAUNCH_BAR_SWITCH</td>
        <td>Value: TRUE request set FALSE request unset</td>
        <td>Shared Cockpit</td>
      </tr>
    </tbody>
  </table>
  <p>&nbsp;</p>
  <p>&nbsp;</p>
  <h3 id="helicopter-specific-systems">Helicopter Specific Systems</h3>
  <table style="table-layout:auto;">
    <colgroup>
      <col>
      <col>
      <col>
      <col>
    </colgroup>
    <tbody>
      <tr>
        <th>Event ID</th>
        <th>String Name</th>
        <th>Description</th>
        <th>Multiplayer</th>
      </tr>
      <tr>
        <td><code class="inline">KEY_ROTOR_BRAKE</code></td>
        <td>ROTOR_BRAKE</td>
        <td>Triggers rotor braking input</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ROTOR_CLUTCH_SWITCH_TOGGLE</code></td>
        <td>ROTOR_CLUTCH_SWITCH_TOGGLE</td>
        <td>Toggles on electric rotor clutch switch</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ROTOR_CLUTCH_SWITCH_SET</code></td>
        <td>ROTOR_CLUTCH_SWITCH_SET</td>
        <td>Sets electric rotor clutch switch on/off (1,0)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ROTOR_GOV_SWITCH_TOGGLE</code></td>
        <td>ROTOR_GOV_SWITCH_TOGGLE</td>
        <td>Toggles the electric rotor governor switch</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ROTOR_GOV_SWITCH_SET</code></td>
        <td>ROTOR_GOV_SWITCH_SET</td>
        <td>Sets the electric rotor governor switch on/off (1,0)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ROTOR_LATERAL_TRIM_INC</code></td>
        <td>ROTOR_LATERAL_TRIM_INC</td>
        <td>Increments the lateral (right) rotor trim</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ROTOR_LATERAL_TRIM_DEC</code></td>
        <td>ROTOR_LATERAL_TRIM_DEC</td>
        <td>Decrements the lateral (right) rotor trim</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ROTOR_LATERAL_TRIM_SET</code></td>
        <td>ROTOR_LATERAL_TRIM_SET</td>
        <td>Sets the lateral (right) rotor trim (0 to 16383)</td>
        <td>Shared Cockpit</td>
      </tr>
    </tbody>
  </table>
  <p>&nbsp;</p>
  <p>&nbsp;</p>
  <h4 id="slings-and-hoists">Slings and Hoists</h4>
  <table style="table-layout:auto;">
    <colgroup>
      <col>
      <col>
      <col>
      <col>
    </colgroup>
    <tbody>
      <tr>
        <th>Event ID</th>
        <th>String Name</th>
        <th>Description</th>
        <th>Multiplayer</th>
      </tr>
      <tr>
        <td><code class="inline">KEY_SLING_PICKUP_RELEASE</code></td>
        <td>SLING_PICKUP_RELEASE</td>
        <td>Toggle between pickup and release mode. Hold mode is automatic and cannot be selected. Refer to the document Notes on Aircraft Systems.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_HOIST_SWITCH_EXTEND</code></td>
        <td>HOIST_SWITCH_EXTEND</td>
        <td>The rate at which a hoist cable extends is set in the Aircraft Configuration File.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_HOIST_SWITCH_RETRACT</code></td>
        <td>HOIST_SWITCH_RETRACT</td>
        <td>The rate at which a hoist cable retracts is set in the Aircraft Configuration File.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_HOIST_SWITCH_SET</code></td>
        <td>HOIST_SWITCH_SET</td>
        <td>The data value should be set to one of: &lt;0 up=0 off &gt;0 down</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_HOIST_DEPLOY_TOGGLE</code></td>
        <td>HOIST_DEPLOY_TOGGLE</td>
        <td>Toggles the hoist arm switch, extend or retract.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_HOIST_DEPLOY_SET</code></td>
        <td>HOIST_DEPLOY_SET</td>
        <td>The data value should be set to: 0 - set hoist switch to retract the arm1 - set hoist switch to extend the arm</td>
        <td>Shared Cockpit</td>
      </tr>
    </tbody>
  </table>
  <p>&nbsp;</p>
  <p>&nbsp;</p>
  <h3 id="aircraft-slew-system">Aircraft Slew System</h3>
  <table style="table-layout:auto;">
    <colgroup>
      <col>
      <col>
      <col>
      <col>
    </colgroup>
    <tbody>
      <tr>
        <th>Event ID</th>
        <th>String Name</th>
        <th>Description</th>
        <th>Multiplayer</th>
      </tr>
      <tr>
        <td><code class="inline">KEY_SLEW_TOGGLE</code></td>
        <td>SLEW_TOGGLE</td>
        <td>Toggles slew on/off</td>
        <td>Shared Cockpit (Pilot only)</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SLEW_OFF</code></td>
        <td>SLEW_OFF</td>
        <td>Turns slew off</td>
        <td>Shared Cockpit (Pilot only)</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SLEW_ON</code></td>
        <td>SLEW_ON</td>
        <td>Turns slew on</td>
        <td>Shared Cockpit (Pilot only)</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SLEW_SET</code></td>
        <td>SLEW_SET</td>
        <td>Sets slew on/off (1,0)</td>
        <td>Shared Cockpit (Pilot only)</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SLEW_RESET</code></td>
        <td>SLEW_RESET</td>
        <td>Stop slew and reset pitch, bank, and heading all to zero.</td>
        <td>Shared Cockpit (Pilot only)</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SLEW_ALTIT_UP_FAST</code></td>
        <td>SLEW_ALTIT_UP_FAST</td>
        <td>Slew upward fast</td>
        <td>Shared Cockpit (Pilot only)</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SLEW_ALTIT_UP_SLOW</code></td>
        <td>SLEW_ALTIT_UP_SLOW</td>
        <td>Slew upward slow</td>
        <td>Shared Cockpit (Pilot only)</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SLEW_ALTIT_FREEZE</code></td>
        <td>SLEW_ALTIT_FREEZE</td>
        <td>Stop vertical slew</td>
        <td>Shared Cockpit (Pilot only)</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SLEW_ALTIT_DN_SLOW</code></td>
        <td>SLEW_ALTIT_DN_SLOW</td>
        <td>Slew downward slow</td>
        <td>Shared Cockpit (Pilot only)</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SLEW_ALTIT_DN_FAST</code></td>
        <td>SLEW_ALTIT_DN_FAST</td>
        <td>Slew downward fast</td>
        <td>Shared Cockpit (Pilot only)</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SLEW_ALTIT_PLUS</code></td>
        <td>SLEW_ALTIT_PLUS</td>
        <td>Increase upward slew</td>
        <td>Shared Cockpit (Pilot only)</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SLEW_ALTIT_MINUS</code></td>
        <td>SLEW_ALTIT_MINUS</td>
        <td>Decrease upward slew</td>
        <td>Shared Cockpit (Pilot only)</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SLEW_PITCH_DN_FAST</code></td>
        <td>SLEW_PITCH_DN_FAST</td>
        <td>Slew pitch downward fast</td>
        <td>Shared Cockpit (Pilot only)</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SLEW_PITCH_DN_SLOW</code></td>
        <td>SLEW_PITCH_DN_SLOW</td>
        <td>Slew pitch downward slow</td>
        <td>Shared Cockpit (Pilot only)</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SLEW_PITCH_FREEZE</code></td>
        <td>SLEW_PITCH_FREEZE</td>
        <td>Stop pitch slew</td>
        <td>Shared Cockpit (Pilot only)</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SLEW_PITCH_UP_SLOW</code></td>
        <td>SLEW_PITCH_UP_SLOW</td>
        <td>Slew pitch up slow</td>
        <td>Shared Cockpit (Pilot only)</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SLEW_PITCH_UP_FAST</code></td>
        <td>SLEW_PITCH_UP_FAST</td>
        <td>Slew pitch upward fast</td>
        <td>Shared Cockpit (Pilot only)</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SLEW_PITCH_PLUS</code></td>
        <td>SLEW_PITCH_PLUS</td>
        <td>Increase pitch up slew</td>
        <td>Shared Cockpit (Pilot only)</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SLEW_PITCH_MINUS</code></td>
        <td>SLEW_PITCH_MINUS</td>
        <td>Decrease pitch up slew</td>
        <td>Shared Cockpit (Pilot only)</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SLEW_BANK_MINUS</code></td>
        <td>SLEW_BANK_MINUS</td>
        <td>Increase left bank slew</td>
        <td>Shared Cockpit (Pilot only)</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SLEW_AHEAD_PLUS</code></td>
        <td>SLEW_AHEAD_PLUS</td>
        <td>Increase forward slew</td>
        <td>Shared Cockpit (Pilot only)</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SLEW_BANK_PLUS</code></td>
        <td>SLEW_BANK_PLUS</td>
        <td>Increase right bank slew</td>
        <td>Shared Cockpit (Pilot only)</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SLEW_LEFT</code></td>
        <td>SLEW_LEFT</td>
        <td>Slew to the left</td>
        <td>Shared Cockpit (Pilot only)</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SLEW_FREEZE</code></td>
        <td>SLEW_FREEZE</td>
        <td>Stop all slew</td>
        <td>Shared Cockpit (Pilot only)</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SLEW_RIGHT</code></td>
        <td>SLEW_RIGHT</td>
        <td>Slew to the right</td>
        <td>Shared Cockpit (Pilot only)</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SLEW_HEADING_MINUS</code></td>
        <td>SLEW_HEADING_MINUS</td>
        <td>Increase slew heading to the left</td>
        <td>Shared Cockpit (Pilot only)</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SLEW_AHEAD_MINUS</code></td>
        <td>SLEW_AHEAD_MINUS</td>
        <td>Decrease forward slew</td>
        <td>Shared Cockpit (Pilot only)</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SLEW_HEADING_PLUS</code></td>
        <td>SLEW_HEADING_PLUS</td>
        <td>Increase slew heading to the right</td>
        <td>Shared Cockpit (Pilot only)</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AXIS_SLEW_AHEAD_SET</code></td>
        <td>AXIS_SLEW_AHEAD_SET</td>
        <td>Sets forward slew (+/- 16383)</td>
        <td>Shared Cockpit (Pilot only)</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AXIS_SLEW_SIDEWAYS_SET</code></td>
        <td>AXIS_SLEW_SIDEWAYS_SET</td>
        <td>Sets sideways slew (+/- 16383)</td>
        <td>Shared Cockpit (Pilot only)</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AXIS_SLEW_HEADING_SET</code></td>
        <td>AXIS_SLEW_HEADING_SET</td>
        <td>Sets heading slew (+/- 16383)</td>
        <td>Shared Cockpit (Pilot only)</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AXIS_SLEW_ALT_SET</code></td>
        <td>AXIS_SLEW_ALT_SET</td>
        <td>Sets vertical slew (+/- 16383)</td>
        <td>Shared Cockpit (Pilot only)</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AXIS_SLEW_BANK_SET</code></td>
        <td>AXIS_SLEW_BANK_SET</td>
        <td>Sets roll slew (+/- 16383)</td>
        <td>Shared Cockpit (Pilot only)</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AXIS_SLEW_PITCH_SET</code></td>
        <td>AXIS_SLEW_PITCH_SET</td>
        <td>Sets pitch slew (+/- 16383)</td>
        <td>Shared Cockpit (Pilot only)</td>
      </tr>
    </tbody>
  </table>
  <p>&nbsp;</p>
  <p>&nbsp;</p>
  <h3 id="view-system">View System</h3>
  <table style="table-layout:auto;">
    <colgroup>
      <col>
      <col>
      <col>
      <col>
    </colgroup>
    <tbody>
      <tr>
        <th>Event ID</th>
        <th>String Name</th>
        <th>Description</th>
        <th>Multiplayer</th>
      </tr>
      <tr>
        <td><code class="inline">KEY_VIEW_MODE</code></td>
        <td>VIEW_MODE</td>
        <td>Selects next view</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_VIEW_WINDOW_TO_FRONT</code></td>
        <td>VIEW_WINDOW_TO_FRONT</td>
        <td>Sets active window to front</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_VIEW_RESET</code></td>
        <td>VIEW_RESET</td>
        <td>Resets the view to the default</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_VIEW_ALWAYS_PAN_UP</code></td>
        <td>VIEW_ALWAYS_PAN_UP</td>
        <td>&nbsp;</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_VIEW_ALWAYS_PAN_DOWN</code></td>
        <td>VIEW_ALWAYS_PAN_DOWN</td>
        <td>&nbsp;</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_NEXT_SUB_VIEW</code></td>
        <td>NEXT_SUB_VIEW</td>
        <td>&nbsp;</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PREV_SUB_VIEW</code></td>
        <td>PREV_SUB_VIEW</td>
        <td>&nbsp;</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_VIEW_TRACK_PAN_TOGGLE</code></td>
        <td>VIEW_TRACK_PAN_TOGGLE</td>
        <td>&nbsp;</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_VIEW_PREVIOUS_TOGGLE</code></td>
        <td>VIEW_PREVIOUS_TOGGLE</td>
        <td>&nbsp;</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_VIEW_CAMERA_SELECT_STARTING</code></td>
        <td>VIEW_CAMERA_SELECT_START</td>
        <td>&nbsp;</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PANEL_HUD_NEXT</code></td>
        <td>PANEL_HUD_NEXT</td>
        <td>&nbsp;</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PANEL_HUD_PREVIOUS</code></td>
        <td>PANEL_HUD_PREVIOUS</td>
        <td>&nbsp;</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ZOOM_IN</code></td>
        <td>ZOOM_IN</td>
        <td>Zooms view in</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ZOOM_OUT</code></td>
        <td>ZOOM_OUT</td>
        <td>Zooms view out</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MAP_ZOOM_FINE_IN</code></td>
        <td>MAP_ZOOM_FINE_IN</td>
        <td>Fine zoom in map view</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PAN_LEFT</code></td>
        <td>PAN_LEFT</td>
        <td>Pans view left</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PAN_RIGHT</code></td>
        <td>PAN_RIGHT</td>
        <td>Pans view right</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MAP_ZOOM_FINE_OUT</code></td>
        <td>MAP_ZOOM_FINE_OUT</td>
        <td>Fine zoom out in map view</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_VIEW_FORWARD</code></td>
        <td>VIEW_FORWARD</td>
        <td>Sets view direction forward</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_VIEW_FORWARD_RIGHT</code></td>
        <td>VIEW_FORWARD_RIGHT</td>
        <td>Sets view direction forward and right</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_VIEW_RIGHT</code></td>
        <td>VIEW_RIGHT</td>
        <td>Sets view direction to the right</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_VIEW_REAR_RIGHT</code></td>
        <td>VIEW_REAR_RIGHT</td>
        <td>Sets view direction to the rear and right</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_VIEW_REAR</code></td>
        <td>VIEW_REAR</td>
        <td>Sets view direction to the rear</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_VIEW_REAR_LEFT</code></td>
        <td>VIEW_REAR_LEFT</td>
        <td>Sets view direction to the rear and left</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_VIEW_LEFT</code></td>
        <td>VIEW_LEFT</td>
        <td>Sets view direction to the left</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_VIEW_FORWARD_LEFT</code></td>
        <td>VIEW_FORWARD_LEFT</td>
        <td>Sets view direction forward and left</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_VIEW_DOWN</code></td>
        <td>VIEW_DOWN</td>
        <td>Sets view direction down</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ZOOM_MINUS</code></td>
        <td>ZOOM_MINUS</td>
        <td>Decreases zoom</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ZOOM_PLUS</code></td>
        <td>ZOOM_PLUS</td>
        <td>Increase zoom</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PAN_UP</code></td>
        <td>PAN_UP</td>
        <td>Pan view up</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PAN_DOWN</code></td>
        <td>PAN_DOWN</td>
        <td>Pan view down</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_VIEW_MODE_REV</code></td>
        <td>VIEW_MODE_REV</td>
        <td>Reverse view cycle</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ZOOM_IN_FINE</code></td>
        <td>ZOOM_IN_FINE</td>
        <td>Zoom in fine</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ZOOM_OUT_FINE</code></td>
        <td>ZOOM_OUT_FINE</td>
        <td>Zoom out fine</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_CLOSE_VIEW</code></td>
        <td>CLOSE_VIEW</td>
        <td>Close current view</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_NEW_VIEW</code></td>
        <td>NEW_VIEW</td>
        <td>Open new view</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_NEXT_VIEW</code></td>
        <td>NEXT_VIEW</td>
        <td>Select next view</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PREV_VIEW</code></td>
        <td>PREV_VIEW</td>
        <td>Select previous view</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PAN_LEFT_UP</code></td>
        <td>PAN_LEFT_UP</td>
        <td>Pan view left</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PAN_LEFT_DOWN</code></td>
        <td>PAN_LEFT_DOWN</td>
        <td>Pan view left and down</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PAN_RIGHT_UP</code></td>
        <td>PAN_RIGHT_UP</td>
        <td>Pan view right and up</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PAN_RIGHT_DOWN</code></td>
        <td>PAN_RIGHT_DOWN</td>
        <td>Pan view right and down</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PAN_TILT_LEFT</code></td>
        <td>PAN_TILT_LEFT</td>
        <td>Tilt view left</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PAN_TILT_RIGHT</code></td>
        <td>PAN_TILT_RIGHT</td>
        <td>Tilt view right</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PAN_RESET</code></td>
        <td>PAN_RESET</td>
        <td>Reset view to forward</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_VIEW_FORWARD_UP</code></td>
        <td>VIEW_FORWARD_UP</td>
        <td>Sets view forward and up</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_VIEW_FORWARD_RIGHT_UP</code></td>
        <td>VIEW_FORWARD_RIGHT_UP</td>
        <td>Sets view forward, right, and up</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_VIEW_RIGHT_UP</code></td>
        <td>VIEW_RIGHT_UP</td>
        <td>Sets view right and up</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_VIEW_REAR_RIGHT_UP</code></td>
        <td>VIEW_REAR_RIGHT_UP</td>
        <td>Sets view rear, right, and up</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_VIEW_REAR_UP</code></td>
        <td>VIEW_REAR_UP</td>
        <td>Sets view rear and up</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_VIEW_REAR_LEFT_UP</code></td>
        <td>VIEW_REAR_LEFT_UP</td>
        <td>Sets view rear left and up</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_VIEW_LEFT_UP</code></td>
        <td>VIEW_LEFT_UP</td>
        <td>Sets view left and up</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_VIEW_FORWARD_LEFT_UP</code></td>
        <td>VIEW_FORWARD_LEFT_UP</td>
        <td>Sets view forward left and up</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_VIEW_UP</code></td>
        <td>VIEW_UP</td>
        <td>Sets view up</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_VIEW_RESET</code></td>
        <td>VIEW_RESET</td>
        <td>Reset view forward</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PAN_RESET_COCKPIT</code></td>
        <td>PAN_RESET_COCKPIT</td>
        <td>Reset panning to forward, if in cockpit view</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_CHASE_VIEW_NEXT</code></td>
        <td>KEY_CHASE_VIEW_NEXT</td>
        <td>Cycle view to next target</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_CHASE_VIEW_PREV</code></td>
        <td>KEY_CHASE_VIEW_PREV</td>
        <td>Cycle view to previous target</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_CHASE_VIEW_TOGGLE</code></td>
        <td>CHASE_VIEW_TOGGLE</td>
        <td>Toggles chase view on/off</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_EYEPOINT_UP</code></td>
        <td>EYEPOINT_UP</td>
        <td>Move eyepoint up</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_EYEPOINT_DOWN</code></td>
        <td>EYEPOINT_DOWN</td>
        <td>Move eyepoint down</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_EYEPOINT_RIGHT</code></td>
        <td>EYEPOINT_RIGHT</td>
        <td>Move eyepoint right</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_EYEPOINT_LEFT</code></td>
        <td>EYEPOINT_LEFT</td>
        <td>Move eyepoint left</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_EYEPOINT_FORWARD</code></td>
        <td>EYEPOINT_FORWARD</td>
        <td>Move eyepoint forward</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_EYEPOINT_BACK</code></td>
        <td>EYEPOINT_BACK</td>
        <td>Move eyepoint backward</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_EYEPOINT_RESET</code></td>
        <td>EYEPOINT_RESET</td>
        <td>Move eyepoint to default position</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_NEW_MAP</code></td>
        <td>NEW_MAP</td>
        <td>Opens new map view</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_VIEW_COCKPIT_FORWARD</code></td>
        <td>VIEW_COCKPIT_FORWARD</td>
        <td>Switch immediately to the forward view, in 2D mode.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_VIEW_VIRTUAL_COCKPIT_FORWARD</code></td>
        <td>VIEW_VIRTUAL_COCKPIT_FORWARD</td>
        <td>Switch immediately to the forward view, in virtual cockpit mode.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_VIEW_PANEL_ALPHA_SET</code></td>
        <td>VIEW_PANEL_ALPHA_SET</td>
        <td>Sets the alpha-blending value for the panel. Takes a parameter in the range 0to 255. The alpha-blending can be changed from the keyboard using Ctrl-Shift-T,and the plus and minus keys.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_VIEW_PANEL_ALPHA_SELECT</code></td>
        <td>VIEW_PANEL_ALPHA_SELECT</td>
        <td>Sets the mode to change the alpha-blending, so the keys KEY_PLUS and KEY_MINUS increment and decrement the value.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_VIEW_PANEL_ALPHA_INC</code></td>
        <td>VIEW_PANEL_ALPHA_INC</td>
        <td>Increment alpha-blending for the panel.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_VIEW_PANEL_ALPHA_DEC</code></td>
        <td>VIEW_PANEL_ALPHA_DEC</td>
        <td>Decrement alpha-blending for the panel.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_VIEW_LINKING_SET</code></td>
        <td>VIEW_LINKING_SET</td>
        <td>Links all the views from one camera together, so that panning the view will change the view of all the linked cameras.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_VIEW_LINKING_TOGGLE</code></td>
        <td>VIEW_LINKING_TOGGLE</td>
        <td>Turns view linking on or off.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_VIEW_CHASE_DISTANCE_ADD</code></td>
        <td>VIEW_CHASE_DISTANCE_ADD</td>
        <td>Increments the distance of the view camera from the chase object (such as in Spot Plane view, or viewing an AI controlled aircraft).</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_VIEW_CHASE_DISTANCE_SUB</code></td>
        <td>VIEW_CHASE_DISTANCE_SUB</td>
        <td>Decrements the distance of the view camera from the chase object.</td>
        <td>Shared Cockpit</td>
      </tr>
    </tbody>
  </table>
  <p>&nbsp;</p>
  <p>&nbsp;</p>
  <h3 id="miscellaneous-events">Miscellaneous Events</h3>
  <table style="table-layout:auto;">
    <colgroup>
      <col>
      <col>
      <col>
      <col>
    </colgroup>
    <tbody>
      <tr>
        <th>Event ID</th>
        <th>String Name</th>
        <th>Description</th>
        <th>Multiplayer</th>
      </tr>
      <tr>
        <td><code class="inline">KEY_PAUSE_TOGGLE</code></td>
        <td>PAUSE_TOGGLE</td>
        <td>Toggles pause on/off</td>
        <td>Disabled</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PAUSE_ON</code></td>
        <td>PAUSE_ON</td>
        <td>Turns pause on</td>
        <td>Disabled</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PAUSE_OFF</code></td>
        <td>PAUSE_OFF</td>
        <td>Turns pause off</td>
        <td>Disabled</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PAUSE_SET</code></td>
        <td>PAUSE_SET</td>
        <td>Sets pause on/off (1,0)</td>
        <td>Disabled</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_DEMO_STOP</code></td>
        <td>DEMO_STOP</td>
        <td>Stops demo system playback</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SELECT_1</code></td>
        <td>SELECT_1</td>
        <td>Sets "selected" index (for other events) to 1</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SELECT_2</code></td>
        <td>SELECT_2</td>
        <td>Sets "selected" index (for other events) to 2</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SELECT_3</code></td>
        <td>SELECT_3</td>
        <td>Sets "selected" index (for other events) to 3</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SELECT_4</code></td>
        <td>SELECT_4</td>
        <td>Sets "selected" index (for other events) to 4</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MINUS</code></td>
        <td>MINUS</td>
        <td>Used in conjunction with "selected" parameters to decrease their value (e.g.,radio frequency)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PLUS</code></td>
        <td>PLUS</td>
        <td>Used in conjunction with "selected" parameters to increase their value (e.g.,radio frequency)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ZOOM_1X</code></td>
        <td>ZOOM_1X</td>
        <td>Sets zoom level to 1</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SOUND_TOGGLE</code></td>
        <td>SOUND_TOGGLE</td>
        <td>Toggles sound on/off</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SIM_RATE</code></td>
        <td>SIM_RATE</td>
        <td>Selects simulation rate (use KEY_MINUS, KEY_PLUS to change)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_JOYSTICK_CALIBRATE</code></td>
        <td>JOYSTICK_CALIBRATE</td>
        <td>Toggles joystick on/off</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SITUATION_SAVE</code></td>
        <td>SITUATION_SAVE</td>
        <td>Saves flight situation</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SITUATION_RESET</code></td>
        <td>SITUATION_RESET</td>
        <td>Resets flight situation</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SOUND_SET</code></td>
        <td>SOUND_SET</td>
        <td>Sets sound on/off (1,0)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_EXIT</code></td>
        <td>EXIT</td>
        <td>Quit ESP with a message</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ABORT</code></td>
        <td>ABORT</td>
        <td>Quit ESP without a message</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_READOUTS_SLEW</code></td>
        <td>READOUTS_SLEW</td>
        <td>Cycle through information readouts while in slew</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_READOUTS_FLIGHT</code></td>
        <td>READOUTS_FLIGHT</td>
        <td>Cycle through information readouts</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MINUS_SHIFT</code></td>
        <td>MINUS_SHIFT</td>
        <td>Used with other events</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PLUS_SHIFT</code></td>
        <td>PLUS_SHIFT</td>
        <td>Used with other events</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SIM_RATE_INCR</code></td>
        <td>SIM_RATE_INCR</td>
        <td>Increase sim rate</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SIM_RATE_DECR</code></td>
        <td>SIM_RATE_DECR</td>
        <td>Decrease sim rate</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_KNEEBOARD</code></td>
        <td>KNEEBOARD_VIEW</td>
        <td>Toggles kneeboard</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PANEL_1</code></td>
        <td>PANEL_1</td>
        <td>Toggles panel 1</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PANEL_2</code></td>
        <td>PANEL_2</td>
        <td>Toggles panel 2</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PANEL_3</code></td>
        <td>PANEL_3</td>
        <td>Toggles panel 3</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PANEL_4</code></td>
        <td>PANEL_4</td>
        <td>Toggles panel 4</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PANEL_5</code></td>
        <td>PANEL_5</td>
        <td>Toggles panel 5</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PANEL_6</code></td>
        <td>PANEL_6</td>
        <td>Toggles panel 6</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PANEL_7</code></td>
        <td>PANEL_7</td>
        <td>Toggles panel 7</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PANEL_8</code></td>
        <td>PANEL_8</td>
        <td>Toggles panel 8</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PANEL_9</code></td>
        <td>PANEL_9</td>
        <td>Toggles panel 9</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SOUND_ON</code></td>
        <td>SOUND_ON</td>
        <td>Turns sound on</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SOUND_OFF</code></td>
        <td>SOUND_OFF</td>
        <td>Turns sound off</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_INVOKE_HELP</code></td>
        <td>INVOKE_HELP</td>
        <td>Brings up Help system</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_AIRCRAFT_LABELS</code></td>
        <td>TOGGLE_AIRCRAFT_LABELS</td>
        <td>Toggles aircraft labels</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FLIGHT_MAP</code></td>
        <td>FLIGHT_MAP</td>
        <td>Brings up flight map</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_RELOAD_PANELS</code></td>
        <td>RELOAD_PANELS</td>
        <td>Reload panel data</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PANEL_ID_TOGGLE</code></td>
        <td>PANEL_ID_TOGGLE</td>
        <td>Toggles indexed panel (1 to 9)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PANEL_ID_OPEN</code></td>
        <td>PANEL_ID_OPEN</td>
        <td>Opens indexed panel (1 to 9)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_PANEL_ID_CLOSE</code></td>
        <td>PANEL_ID_CLOSE</td>
        <td>Closes indexed panel (1 to 9)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_CONTROL_RELOAD_USER_AIRCRAFT</code></td>
        <td>RELOAD_USER_AIRCRAFT</td>
        <td>Reloads the user aircraft data (from cache if same type loaded as an AI,otherwise from disk)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SIM_RESET</code></td>
        <td>SIM_RESET</td>
        <td>Resets aircraft state</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_VIRTUAL_COPILOT_TOGGLE</code></td>
        <td>VIRTUAL_COPILOT_TOGGLE</td>
        <td>Turns Flying Tips on/off</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_VIRTUAL_COPILOT_SET</code></td>
        <td>VIRTUAL_COPILOT_SET</td>
        <td>Sets Flying Tips on/off (1,0)</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_VIRTUAL_COPILOT_ACTION</code></td>
        <td>VIRTUAL_COPILOT_ACTION</td>
        <td>Triggers action noted in Flying Tips</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_REFRESH_SCENERY</code></td>
        <td>REFRESH_SCENERY</td>
        <td>Reloads scenery</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_CLOCK_HOURS_DEC</code></td>
        <td>CLOCK_HOURS_DEC</td>
        <td>Decrements time by hours</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_CLOCK_HOURS_INC</code></td>
        <td>CLOCK_HOURS_INC</td>
        <td>Increments time by hours</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_CLOCK_MINUTES_DEC</code></td>
        <td>CLOCK_MINUTES_DEC</td>
        <td>Decrements time by minutes</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_CLOCK_MINUTES_INC</code></td>
        <td>CLOCK_MINUTES_INC</td>
        <td>Increments time by minutes</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_CLOCK_SECONDS_ZERO</code></td>
        <td>CLOCK_SECONDS_ZERO</td>
        <td>Zeros seconds</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_CLOCK_HOURS_SET</code></td>
        <td>CLOCK_HOURS_SET</td>
        <td>Sets hour of day</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_CLOCK_MINUTES_SET</code></td>
        <td>CLOCK_MINUTES_SET</td>
        <td>Sets minutes of the hour</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ZULU_HOURS_SET</code></td>
        <td>ZULU_HOURS_SET</td>
        <td>Sets hours, zulu time</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ZULU_MINUTES_SET</code></td>
        <td>ZULU_MINUTES_SET</td>
        <td>Sets minutes, in zulu time</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ZULU_DAY_SET</code></td>
        <td>ZULU_DAY_SET</td>
        <td>Sets day, in zulu time</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ZULU_YEAR_SET</code></td>
        <td>ZULU_YEAR_SET</td>
        <td>Sets year, in zulu time</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_GAUGE_KEYSTROKE</code></td>
        <td>GAUGE_KEYSTROKE</td>
        <td>Enables a keystroke to be sent to a gauge that is in focus. The keystrokes can only be in the range 0 to 9, A to Z, and the four keys: plus, minus, comma and period. This is typically used to allow some keyboard entry to a complex device such as a GPS to enter such things as ICAO codes using the keyboard, rather than turning dials.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_SIMUI_WINDOW_HIDESHOW</code></td>
        <td>SIMUI_WINDOW_HIDESHOW</td>
        <td>Display the ATC window.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_WINDOW_TITLES_TOGGLE</code></td>
        <td>VIEW_WINDOW_TITLES_TOGGLE</td>
        <td>Turn window titles on or off.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AXIS_PAN_PITCH</code></td>
        <td>AXIS_PAN_PITCH</td>
        <td>Sets the pitch of the axis. Requires an angle.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AXIS_PAN_HEADING</code></td>
        <td>AXIS_PAN_HEADING</td>
        <td>Sets the heading of the axis. Requires an angle.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AXIS_PAN_TILT</code></td>
        <td>AXIS_PAN_TILT</td>
        <td>Sets the tilt of the axis. Requires an angle.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AXIS_INDICATOR_CYCLE</code></td>
        <td>VIEW_AXIS_INDICATOR_CYCLE</td>
        <td>Step through the view axes.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MAP_ORIENTATION_CYCLE</code></td>
        <td>VIEW_MAP_ORIENTATION_CYCLE</td>
        <td>Step through the map orientations.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_JETWAY</code></td>
        <td>TOGGLE_JETWAY</td>
        <td>Requests a jetway, which will only be answered if the aircraft is at a parking spot.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_VIDEO_RECORD_TOGGLE</code></td>
        <td>VIDEO_RECORD_TOGGLE</td>
        <td>Turn on or off the video recording feature. This records uncompressed AVI format files to: My Documents\Videos\</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_AIRPORT_NAME_DISPLAY</code></td>
        <td>TOGGLE_AIRPORT_NAME_DISPLAY</td>
        <td>Turn on or off the airport name.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_CAPTURE_SCREENSHOT</code></td>
        <td>CAPTURE_SCREENSHOT</td>
        <td>Capture the current view as a screenshot. Which will be saved to a bmp file in: My Documents\Pictures\</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MOUSE_LOOK_TOGGLE</code></td>
        <td>MOUSE_LOOK_TOGGLE</td>
        <td>Switch Mouse Look mode on or off. Mouse Look mode enables a user to control their view using the mouse, and holding down the space bar.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_YAXIS_INVERT_TOGGLE</code></td>
        <td>YAXIS_INVERT_TOGGLE</td>
        <td>Switch inversion of Y axis controls on or off.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_AUTOCOORD_TOGGLE</code></td>
        <td>AUTORUDDER_TOGGLE</td>
        <td>Turn the automatic rudder control feature on or off.</td>
        <td>Shared Cockpit</td>
      </tr>
    </tbody>
  </table>
  <p>&nbsp;</p>
  <h4 id="freezing-position">Freezing Position</h4>
  <table style="table-layout:auto;">
    <colgroup>
      <col>
      <col>
      <col>
      <col>
    </colgroup>
    <tbody>
      <tr>
        <th>Event ID</th>
        <th>String Name</th>
        <th>Description</th>
        <th>Multiplayer</th>
      </tr>
      <tr>
        <td><code class="inline">KEY_FREEZE_LATITUDE_LONGITUDE_TOGGLE</code></td>
        <td>FREEZE_LATITUDE_LONGITUDE_TOGGLE</td>
        <td>Turns the freezing of the lat/lon position of the aircraft (either user or AI controlled) on or off. If this key event is set, it means that the latitude and longitude of the aircraft are not being controlled by ESP, so enabling, for example, a SimConnect client to control the position of the aircraft. This can also apply to altitude and attitude. Refer to the simulation variables: IS LATITUDE LONGITUDE FREEZE ON, IS ALTITUDE FREEZE ON, and IS ATTITUDE FREEZE ON Refer also to the <a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/API_Reference/AI_Object/SimConnect_AIReleaseControl.htm">SimConnect_AIReleaseControl</a> function.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FREEZE_LATITUDE_LONGITUDE_SET</code></td>
        <td>FREEZE_LATITUDE_LONGITUDE_SET</td>
        <td>Freezes the lat/lon position of the aircraft.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FREEZE_ALTITUDE_TOGGLE</code></td>
        <td>FREEZE_ALTITUDE_TOGGLE</td>
        <td>Turns the freezing of the altitude of the aircraft on or off.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FREEZE_ALTITUDE_SET</code></td>
        <td>FREEZE_ALTITUDE_SET</td>
        <td>Freezes the altitude of the aircraft.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FREEZE_ATTITUDE_TOGGLE</code></td>
        <td>FREEZE_ATTITUDE_TOGGLE</td>
        <td>Turns the freezing of the attitude (pitch, bank and heading) of the aircraft on or off.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_FREEZE_ATTITUDE_SET</code></td>
        <td>FREEZE_ATTITUDE_SET</td>
        <td>Freezes the attitude (pitch, bank and heading) of the aircraft.</td>
        <td>Shared Cockpit</td>
      </tr>
    </tbody>
  </table>
  <p>&nbsp;</p>
  <h4 id="mission-keys">Mission Keys</h4>
  <table style="table-layout:auto;">
    <colgroup>
      <col>
      <col>
      <col>
      <col>
    </colgroup>
    <tbody>
      <tr>
        <th>Event ID</th>
        <th>String Name</th>
        <th>Description</th>
        <th>Multiplayer</th>
      </tr>
      <tr>
        <td><code class="inline">KEY_POINT_OF_INTEREST_TOGGLE_POINTER</code></td>
        <td>POINT_OF_INTEREST_TOGGLE_POINTER</td>
        <td>Turn the point-of-interest indicator (often a light beam) on or off. Refer to the Missions system documentation.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_POINT_OF_INTEREST_CYCLE_PREVIOUS</code></td>
        <td>POINT_OF_INTEREST_CYCLE_PREVIOUS</td>
        <td>Change the current point-of-interest to the previous point-of-interest.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_POINT_OF_INTEREST_CYCLE_NEXT</code></td>
        <td>POINT_OF_INTEREST_CYCLE_NEXT</td>
        <td>Change the current point-of-interest to the next point-of-interest.</td>
        <td>Shared Cockpit</td>
      </tr>
    </tbody>
  </table>
  <p>&nbsp;</p>
  <p>&nbsp;</p>
  <h3 id="atc">ATC</h3>
  <table style="table-layout:auto;">
    <tbody>
      <tr>
        <th>Event ID</th>
        <th>String Name</th>
        <th>Description</th>
        <th>Multiplayer</th>
      </tr>
      <tr>
        <td><code class="inline">KEY_ATC</code></td>
        <td>ATC</td>
        <td>Activates ATC window</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ATC_MENU_1</code></td>
        <td>ATC_MENU_1</td>
        <td>Selects ATC option 1</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ATC_MENU_2</code></td>
        <td>ATC_MENU_2</td>
        <td>Selects ATC option 2</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ATC_MENU_3</code></td>
        <td>ATC_MENU_3</td>
        <td>Selects ATC option 3</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ATC_MENU_4</code></td>
        <td>ATC_MENU_4</td>
        <td>Selects ATC option 4</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ATC_MENU_5</code></td>
        <td>ATC_MENU_5</td>
        <td>Selects ATC option 5</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ATC_MENU_6</code></td>
        <td>ATC_MENU_6</td>
        <td>Selects ATC option 6</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ATC_MENU_7</code></td>
        <td>ATC_MENU_7</td>
        <td>Selects ATC option 7</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ATC_MENU_8</code></td>
        <td>ATC_MENU_8</td>
        <td>Selects ATC option 8</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ATC_MENU_9</code></td>
        <td>ATC_MENU_9</td>
        <td>Selects ATC option 9</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_ATC_MENU_0</code></td>
        <td>ATC_MENU_0</td>
        <td>Selects ATC option 10</td>
        <td>Shared Cockpit</td>
      </tr>
    </tbody>
  </table>
  <p>&nbsp;</p>
  <p>&nbsp;</p>
  <h3 id="multiplayer">Multiplayer</h3>
  <table style="table-layout:auto;">
    <colgroup>
      <col>
      <col>
      <col>
      <col>
    </colgroup>
    <tbody>
      <tr>
        <th>Event ID</th>
        <th>String Name</th>
        <th>Description</th>
        <th>Multiplayer</th>
      </tr>
      <tr>
        <td><code class="inline">KEY_MULTIPLAYER_TRANSFER_CONTROL</code></td>
        <td>MP_TRANSFER_CONTROL</td>
        <td>Toggle to the next player to track</td>
        <td>-</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MULTIPLAYER_PLAYER_CYCLE</code></td>
        <td>MP_PLAYER_CYCLE</td>
        <td>Cycle through the current user aircraft.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MULTIPLAYER_PLAYER_FOLLOW</code></td>
        <td>MP_PLAYER_FOLLOW</td>
        <td>Set the view to follow the selected user aircraft.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MULTIPLAYER_CHAT</code></td>
        <td>MP_CHAT</td>
        <td>Toggles chat window visible/invisible</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MULTIPLAYER_ACTIVATE_CHAT</code></td>
        <td>MP_ACTIVATE_CHAT</td>
        <td>Activates chat window</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MULTIPLAYER_VOICE_CAPTURE_START</code></td>
        <td>MP_VOICE_CAPTURE_START</td>
        <td>Start capturing audio from the users computer and transmitting it to all other players in the multiplayer session who are turned to the same radio frequency.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MULTIPLAYER_VOICE_CAPTURE_STOP</code></td>
        <td>MP_VOICE_CAPTURE_STOP</td>
        <td>Stop capturing radio audio.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MULTIPLAYER_BROADCAST_VOICE_ CAPTURE_START</code></td>
        <td>MP_BROADCAST_VOICE_ CAPTURE_START</td>
        <td>Start capturing audio from the users computer and transmitting it to all other players in the multiplayer session.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_MULTIPLAYER_BROADCAST_VOICE_ CAPTURE_STOP</code></td>
        <td>MP_BROADCAST_VOICE_ CAPTURE_STOP</td>
        <td>Stop capturing broadcast audio.</td>
        <td>Shared Cockpit</td>
      </tr>
      <tr>
        <td><code class="inline">KEY_TOGGLE_RACERESULTS_WINDOW</code></td>
        <td>TOGGLE_RACERESULTS_WINDOW</td>
        <td>Show or hide multi-player race results.</td>
        <td>Disabled</td>
      </tr>
    </tbody>
  </table>
  <p>&nbsp;</p>
  <p>&nbsp;</p>
  <p>&nbsp;</p>

</div></div>
          <div class="RH-LAYOUT-BRS-container" id="rh-brs"></div>
        </div>
        <div class="RH-LAYOUT-RIGHTPANEL-container" id="rh-rightpanel"><div tabindex="-1" class="rh-layout-RIGHTPANEL-rightpanel-content "><div class="RH-LAYOUT-RIGHTPANEL-top-buttons "><button class="rh-button RH-LAYOUT-RIGHTPANEL-fav-button  " title="Set as Favorite"></button><button class="rh-button RH-LAYOUT-RIGHTPANEL-print-button  " title="Print"></button><button class="rh-button RH-LAYOUT-RIGHTPANEL-highlight-remove-button  " title="Remove Highlight"></button></div><div class="RH-LAYOUT-MINITOC-container " role="navigation" aria-labelledby="minitoc-caption">      <label data-type="minitoc-caption" href="#" class="RH-LAYOUT-MINITOC-caption" id="minitoc-caption">In this Topic</label> <ol><li class="rh-layout-MINITOC-rightpanel-minitoc-list RH-LAYOUT-MINITOC-item-level-1"><div class="rh-layout-MINITOC-item-wrapper"><div class="rh-layout-MINITOC-item-pointer"></div><a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#legacy_event_id" class="RH-LAYOUT-MINITOC-item">LEGACY EVENT IDs</a></div></li><li class="rh-layout-MINITOC-rightpanel-minitoc-list RH-LAYOUT-MINITOC-item-level-1"><ol><li class="rh-layout-MINITOC-rightpanel-minitoc-list RH-LAYOUT-MINITOC-item-level-2"><div class="rh-layout-MINITOC-item-wrapper"><div class="rh-layout-MINITOC-item-pointer"></div><a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#aircraft-engine" class="RH-LAYOUT-MINITOC-item">Aircraft Engine</a></div></li><li class="rh-layout-MINITOC-rightpanel-minitoc-list RH-LAYOUT-MINITOC-item-level-2"><div class="rh-layout-MINITOC-item-wrapper"><div class="rh-layout-MINITOC-item-pointer"></div><a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#aircraft-flight-controls" class="RH-LAYOUT-MINITOC-item">Aircraft Flight Controls</a></div></li><li class="rh-layout-MINITOC-rightpanel-minitoc-list RH-LAYOUT-MINITOC-item-level-2"><div class="rh-layout-MINITOC-item-wrapper"><div class="rh-layout-MINITOC-item-pointer"></div><a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#aircraft-automatic-flight-systems-autopilot" class="RH-LAYOUT-MINITOC-item">Aircraft Automatic Flight Systems / Autopilot</a></div></li><li class="rh-layout-MINITOC-rightpanel-minitoc-list RH-LAYOUT-MINITOC-item-level-2"><ol><li class="rh-layout-MINITOC-rightpanel-minitoc-list RH-LAYOUT-MINITOC-item-level-3"><div class="rh-layout-MINITOC-item-wrapper"><div class="rh-layout-MINITOC-item-pointer"></div><a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#g1000-multi-function-display" class="RH-LAYOUT-MINITOC-item">G1000 (Multi-Function Display)</a></div></li></ol></li><li class="rh-layout-MINITOC-rightpanel-minitoc-list RH-LAYOUT-MINITOC-item-level-2"><div class="rh-layout-MINITOC-item-wrapper"><div class="rh-layout-MINITOC-item-pointer"></div><a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#aircraft-fuel-system" class="RH-LAYOUT-MINITOC-item">Aircraft Fuel System</a></div></li><li class="rh-layout-MINITOC-rightpanel-minitoc-list RH-LAYOUT-MINITOC-item-level-2"><ol><li class="rh-layout-MINITOC-rightpanel-minitoc-list RH-LAYOUT-MINITOC-item-level-3"><div class="rh-layout-MINITOC-item-wrapper"><div class="rh-layout-MINITOC-item-pointer"></div><a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#fuel-selection-keys" class="RH-LAYOUT-MINITOC-item">Fuel Selection Keys</a></div></li><li class="rh-layout-MINITOC-rightpanel-minitoc-list RH-LAYOUT-MINITOC-item-level-3"><div class="rh-layout-MINITOC-item-wrapper"><div class="rh-layout-MINITOC-item-pointer"></div><a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#fuel-selector-codes" class="RH-LAYOUT-MINITOC-item">Fuel Selector Codes</a></div></li></ol></li><li class="rh-layout-MINITOC-rightpanel-minitoc-list RH-LAYOUT-MINITOC-item-level-2"><div class="rh-layout-MINITOC-item-wrapper"><div class="rh-layout-MINITOC-item-pointer"></div><a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#aircraft-avionics" class="RH-LAYOUT-MINITOC-item">Aircraft Avionics</a></div></li><li class="rh-layout-MINITOC-rightpanel-minitoc-list RH-LAYOUT-MINITOC-item-level-2"><div class="rh-layout-MINITOC-item-wrapper"><div class="rh-layout-MINITOC-item-pointer"></div><a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#aircraft-instruments" class="RH-LAYOUT-MINITOC-item">Aircraft Instruments</a></div></li><li class="rh-layout-MINITOC-rightpanel-minitoc-list RH-LAYOUT-MINITOC-item-level-2"><div class="rh-layout-MINITOC-item-wrapper"><div class="rh-layout-MINITOC-item-pointer"></div><a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#aircraft-lights" class="RH-LAYOUT-MINITOC-item">Aircraft Lights</a></div></li><li class="rh-layout-MINITOC-rightpanel-minitoc-list RH-LAYOUT-MINITOC-item-level-2"><div class="rh-layout-MINITOC-item-wrapper"><div class="rh-layout-MINITOC-item-pointer"></div><a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#aircraft-failures" class="RH-LAYOUT-MINITOC-item">Aircraft Failures</a></div></li><li class="rh-layout-MINITOC-rightpanel-minitoc-list RH-LAYOUT-MINITOC-item-level-2"><div class="rh-layout-MINITOC-item-wrapper"><div class="rh-layout-MINITOC-item-pointer"></div><a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#aircraft-miscellaneous-systems" class="RH-LAYOUT-MINITOC-item">Aircraft Miscellaneous Systems</a></div></li><li class="rh-layout-MINITOC-rightpanel-minitoc-list RH-LAYOUT-MINITOC-item-level-2"><ol><li class="rh-layout-MINITOC-rightpanel-minitoc-list RH-LAYOUT-MINITOC-item-level-3"><div class="rh-layout-MINITOC-item-wrapper"><div class="rh-layout-MINITOC-item-pointer RH-LAYOUT-MINITOC-selected-pointer"></div><a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#nose-wheel-steering" class="RH-LAYOUT-MINITOC-item RH-LAYOUT-MINITOC-selected-item">Nose Wheel Steering</a></div></li><li class="rh-layout-MINITOC-rightpanel-minitoc-list RH-LAYOUT-MINITOC-item-level-3"><div class="rh-layout-MINITOC-item-wrapper"><div class="rh-layout-MINITOC-item-pointer"></div><a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#cabin-pressurization" class="RH-LAYOUT-MINITOC-item">Cabin Pressurization</a></div></li><li class="rh-layout-MINITOC-rightpanel-minitoc-list RH-LAYOUT-MINITOC-item-level-3"><div class="rh-layout-MINITOC-item-wrapper"><div class="rh-layout-MINITOC-item-pointer"></div><a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#catapult-launches" class="RH-LAYOUT-MINITOC-item">Catapult Launches</a></div></li></ol></li><li class="rh-layout-MINITOC-rightpanel-minitoc-list RH-LAYOUT-MINITOC-item-level-2"><div class="rh-layout-MINITOC-item-wrapper"><div class="rh-layout-MINITOC-item-pointer"></div><a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#helicopter-specific-systems" class="RH-LAYOUT-MINITOC-item">Helicopter Specific Systems</a></div></li><li class="rh-layout-MINITOC-rightpanel-minitoc-list RH-LAYOUT-MINITOC-item-level-2"><ol><li class="rh-layout-MINITOC-rightpanel-minitoc-list RH-LAYOUT-MINITOC-item-level-3"><div class="rh-layout-MINITOC-item-wrapper"><div class="rh-layout-MINITOC-item-pointer"></div><a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#slings-and-hoists" class="RH-LAYOUT-MINITOC-item">Slings and Hoists</a></div></li></ol></li><li class="rh-layout-MINITOC-rightpanel-minitoc-list RH-LAYOUT-MINITOC-item-level-2"><div class="rh-layout-MINITOC-item-wrapper"><div class="rh-layout-MINITOC-item-pointer"></div><a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#aircraft-slew-system" class="RH-LAYOUT-MINITOC-item">Aircraft Slew System</a></div></li><li class="rh-layout-MINITOC-rightpanel-minitoc-list RH-LAYOUT-MINITOC-item-level-2"><div class="rh-layout-MINITOC-item-wrapper"><div class="rh-layout-MINITOC-item-pointer"></div><a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#view-system" class="RH-LAYOUT-MINITOC-item">View System</a></div></li><li class="rh-layout-MINITOC-rightpanel-minitoc-list RH-LAYOUT-MINITOC-item-level-2"><div class="rh-layout-MINITOC-item-wrapper"><div class="rh-layout-MINITOC-item-pointer"></div><a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#miscellaneous-events" class="RH-LAYOUT-MINITOC-item">Miscellaneous Events</a></div></li><li class="rh-layout-MINITOC-rightpanel-minitoc-list RH-LAYOUT-MINITOC-item-level-2"><ol><li class="rh-layout-MINITOC-rightpanel-minitoc-list RH-LAYOUT-MINITOC-item-level-3"><div class="rh-layout-MINITOC-item-wrapper"><div class="rh-layout-MINITOC-item-pointer"></div><a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#freezing-position" class="RH-LAYOUT-MINITOC-item">Freezing Position</a></div></li><li class="rh-layout-MINITOC-rightpanel-minitoc-list RH-LAYOUT-MINITOC-item-level-3"><div class="rh-layout-MINITOC-item-wrapper"><div class="rh-layout-MINITOC-item-pointer"></div><a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#mission-keys" class="RH-LAYOUT-MINITOC-item">Mission Keys</a></div></li></ol></li><li class="rh-layout-MINITOC-rightpanel-minitoc-list RH-LAYOUT-MINITOC-item-level-2"><div class="rh-layout-MINITOC-item-wrapper"><div class="rh-layout-MINITOC-item-pointer"></div><a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#atc" class="RH-LAYOUT-MINITOC-item">ATC</a></div></li><li class="rh-layout-MINITOC-rightpanel-minitoc-list RH-LAYOUT-MINITOC-item-level-2"><div class="rh-layout-MINITOC-item-wrapper"><div class="rh-layout-MINITOC-item-pointer"></div><a href="https://docs.flightsimulator.com/html/Programming_Tools/SimConnect/Status/#multiplayer" class="RH-LAYOUT-MINITOC-item">Multiplayer</a></div></li></ol></li></ol></div><div class="rh-layout-RIGHTPANEL-bottom-buttons "><button class="rh-button RH-LAYOUT-RIGHTPANEL-pageup-button  " title="Go to Top"></button><button class="rh-button RH-LAYOUT-RIGHTPANEL-fullscreen-button  " title="Full Screen" aria-pressed="false"></button></div></div></div>
      </div>
    </div>
  </main>
<footer role="contentinfo" class="RH-LAYOUT-FOOTER-container" id="rh-footer">
  <div class="rh-layout-footer-outer-box" style="background-color: #EFEFEF;">
    <!--<div class="rh-layout-footer-inner-left-box" style="background-color: #EFEFEF;"><img alt="World Icon" class="rh-layout-footer-imgTag" src="Group 114.svg" title="World Icon" />
      <div>
        <p style="text-align: center;background-color: #EFEFEF;color: rgb(0,0,0,);line-height: 2;margin: 0;"><span style="color:#ffffff;"></span><span style="color:rgb(0,0,0,);">EN</span></p>
      </div>
    </div>-->
    <div class="rh-layout-footer-inner-right-box" style="background-color: #EFEFEF;">
      <div class="rh-layout-footer-inner-right-box1" style="background-color: #EFEFEF;">
        <p style="text-align: center;background-color:  #EFEFEF;color: rgb(0,0,0,);line-height: 2;margin: 0;"><a href="https://www.microsoft.com/" style="color: black; text-decoration: none;">©2021 Microsoft</a></p>
      </div>
      <div class="rh-layout-footer-inner-right-box2" style="background-color: #EFEFEF;">
        <p style="text-align: center;background-color:  #EFEFEF;color: rgb(0,0,0,);height: 100%;line-height: 2;margin: 0;"><a href="https://flightsimulator.zendesk.com/hc/en-us" style="color: black; text-decoration: none;">Contact Us</a></p>
      </div>
      <div class="rh-layout-footer-inner-right-box3" style="background-color: #EFEFEF;">
        <p style="text-align: center;background-color:  #EFEFEF;color: rgb(0,0,0,);line-height: 2;margin: 0;"><span style="font-size: 11pt;"><a href="https://privacy.microsoft.com/en-us/privacystatement" style="color: black; text-decoration: none;">Privacy Policy</a></span></p>
      </div>
      <!--<div class="rh-layout-footer-inner-right-box4" style="background-color: #EFEFEF;">
        <p style="text-align: center;background-color:  #EFEFEF;color: rgb(0,0,0,);line-height: 2;margin: 0;"><a href="http://www.example.com" style="color: black; text-decoration: none;">Terms and Conditions<a/></p>
      </div>-->
      <div class="rh-layout-footer-inner-right-box3" style="background-color: #EFEFEF;">
        <p style="text-align: center;background-color:  #EFEFEF;color: rgb(0,0,0,);line-height: 2;margin: 0;"><a href="https://forums.flightsimulator.com/" style="color: black; text-decoration: none;">MSFS Forums</a></p>
      </div>
    </div>
  </div>

</footer>
  <script type="text/javascript">//<![CDATA[

    gRootRelPath = "../../.."
    gCommonRootRelPath = "../../.."
    gTopicId = "5.1.2.0_2"
  
//]]></script>


  <script src="./Legacy Event IDs_files/topicpage.js.download"></script><script type="text/javascript" async="" src="./Legacy Event IDs_files/parentdata.js.download"></script>
  <script src="./Legacy Event IDs_files/layoutconfig.js.download"></script>
  <script src="./Legacy Event IDs_files/brsdata.js.download"></script>

  
    

            


<script type="text/javascript" async="" src="./Legacy Event IDs_files/projectdata.js.download"></script><script type="text/javascript" async="" src="./Legacy Event IDs_files/projectsettings.js.download"></script><script type="text/javascript" async="" src="./Legacy Event IDs_files/usersettings.js.download"></script><script type="text/javascript" async="" src="./Legacy Event IDs_files/gdata1.new.js.download"></script><script type="text/javascript" async="" src="./Legacy Event IDs_files/idata1.new.js.download"></script><script type="text/javascript" async="" src="./Legacy Event IDs_files/brsdata.js.download"></script><script type="text/javascript" async="" src="./Legacy Event IDs_files/toc.new.js.download"></script><script type="text/javascript" async="" src="./Legacy Event IDs_files/search_auto_index.js.download"></script><script type="text/javascript" async="" src="./Legacy Event IDs_files/search_auto_map_0.js.download"></script><div id="modal-root-menubar"></div><script type="text/javascript" async="" src="./Legacy Event IDs_files/csh.new.js.download"></script><script type="text/javascript" async="" src="./Legacy Event IDs_files/whtagdata.js.download"></script><script type="text/javascript" async="" src="./Legacy Event IDs_files/toc41.new.js.download"></script><script type="text/javascript" async="" src="./Legacy Event IDs_files/parentdata.js.download"></script><script type="text/javascript" async="" src="./Legacy Event IDs_files/toc42.new.js.download"></script><script type="text/javascript" async="" src="./Legacy Event IDs_files/toc51.new.js.download"></script><script type="text/javascript" async="" src="./Legacy Event IDs_files/toc52.new.js.download"></script></body></html>