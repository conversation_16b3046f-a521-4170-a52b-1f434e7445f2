﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata minClientVersion="2.12">
    <id>System.Runtime.InteropServices.RuntimeInformation</id>
    <version>4.3.0</version>
    <title>System.Runtime.InteropServices.RuntimeInformation</title>
    <authors>Microsoft</authors>
    <owners>microsoft,dotnetframework</owners>
    <requireLicenseAcceptance>true</requireLicenseAcceptance>
    <licenseUrl>http://go.microsoft.com/fwlink/?LinkId=329770</licenseUrl>
    <projectUrl>https://dot.net/</projectUrl>
    <iconUrl>http://go.microsoft.com/fwlink/?LinkID=288859</iconUrl>
    <description>Provides APIs to query about runtime and OS information.

Commonly Used Types:
System.Runtime.InteropServices.RuntimeInformation
System.Runtime.InteropServices.OSPlatform
 
When using NuGet 3.x this package requires at least version 3.4.</description>
    <releaseNotes>https://go.microsoft.com/fwlink/?LinkID=799421</releaseNotes>
    <copyright>© Microsoft Corporation.  All rights reserved.</copyright>
    <serviceable>true</serviceable>
    <dependencies>
      <group targetFramework="MonoAndroid1.0" />
      <group targetFramework="MonoTouch1.0" />
      <group targetFramework=".NETFramework4.5" />
      <group targetFramework=".NETCore5.0">
        <dependency id="Microsoft.NETCore.Platforms" version="1.1.0" />
        <dependency id="System.Reflection" version="4.3.0" exclude="Compile" />
        <dependency id="System.Reflection.Extensions" version="4.3.0" exclude="Compile" />
        <dependency id="System.Resources.ResourceManager" version="4.3.0" exclude="Compile" />
        <dependency id="System.Runtime" version="4.3.0" exclude="Compile" />
        <dependency id="System.Threading" version="4.3.0" exclude="Compile" />
      </group>
      <group targetFramework=".NETStandard1.1">
        <dependency id="runtime.native.System" version="4.3.0" exclude="Compile" />
        <dependency id="System.Reflection" version="4.3.0" exclude="Compile" />
        <dependency id="System.Reflection.Extensions" version="4.3.0" exclude="Compile" />
        <dependency id="System.Resources.ResourceManager" version="4.3.0" exclude="Compile" />
        <dependency id="System.Runtime" version="4.3.0" />
        <dependency id="System.Runtime.InteropServices" version="4.3.0" exclude="Compile" />
        <dependency id="System.Threading" version="4.3.0" exclude="Compile" />
      </group>
      <group targetFramework="Windows8.0" />
      <group targetFramework="WindowsPhoneApp8.1" />
      <group targetFramework="Xamarin.iOS1.0" />
      <group targetFramework="Xamarin.Mac2.0" />
      <group targetFramework="Xamarin.TVOS1.0" />
      <group targetFramework="Xamarin.WatchOS1.0" />
    </dependencies>
  </metadata>
</package>