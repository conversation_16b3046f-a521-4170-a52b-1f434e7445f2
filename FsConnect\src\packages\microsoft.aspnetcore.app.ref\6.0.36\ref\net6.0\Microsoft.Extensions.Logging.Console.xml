<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Extensions.Logging.Console</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Extensions.Logging.Console.AnsiLogConsole">
            <summary>
            For consoles which understand the ANSI escape code sequences to represent color
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.Console.AnsiParser.Parse(System.String)">
            <summary>
            Parses a subset of display attributes
            Set Display Attributes
            Set Attribute Mode [{attr1};...;{attrn}m
            Sets multiple display attribute settings. The following lists standard attributes that are getting parsed:
            1 Bright
            Foreground Colours
            30 Black
            31 Red
            32 Green
            33 Yellow
            34 Blue
            35 Magenta
            36 Cyan
            37 White
            Background Colours
            40 Black
            41 Red
            42 Green
            43 Yellow
            44 Blue
            45 Magenta
            46 Cyan
            47 White
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Logging.Console.ConsoleFormatter">
            <summary>
            Allows custom log messages formatting
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Logging.Console.ConsoleFormatter.Name">
            <summary>
            Gets the name associated with the console log formatter.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.Console.ConsoleFormatter.Write``1(Microsoft.Extensions.Logging.Abstractions.LogEntry{``0}@,Microsoft.Extensions.Logging.IExternalScopeProvider,System.IO.TextWriter)">
            <summary>
            Writes the log message to the specified TextWriter.
            </summary>
            <remarks>
            if the formatter wants to write colors to the console, it can do so by embedding ANSI color codes into the string
            </remarks>
            <param name="logEntry">The log entry.</param>
            <param name="scopeProvider">The provider of scope data.</param>
            <param name="textWriter">The string writer embedding ansi code for colors.</param>
            <typeparam name="TState">The type of the object to be written.</typeparam>
        </member>
        <member name="T:Microsoft.Extensions.Logging.Console.ConsoleFormatterNames">
            <summary>
            Reserved formatter names for the built-in console formatters.
            </summary>
        </member>
        <member name="F:Microsoft.Extensions.Logging.Console.ConsoleFormatterNames.Simple">
            <summary>
            Reserved name for simple console formatter
            </summary>
        </member>
        <member name="F:Microsoft.Extensions.Logging.Console.ConsoleFormatterNames.Json">
            <summary>
            Reserved name for json console formatter
            </summary>
        </member>
        <member name="F:Microsoft.Extensions.Logging.Console.ConsoleFormatterNames.Systemd">
            <summary>
            Reserved name for systemd console formatter
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Logging.Console.ConsoleFormatterOptions">
            <summary>
            Options for the built-in console log formatter.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Logging.Console.ConsoleFormatterOptions.IncludeScopes">
            <summary>
            Includes scopes when <see langword="true" />.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Logging.Console.ConsoleFormatterOptions.TimestampFormat">
            <summary>
            Gets or sets format string used to format timestamp in logging messages. Defaults to <c>null</c>.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Logging.Console.ConsoleFormatterOptions.UseUtcTimestamp">
            <summary>
            Gets or sets indication whether or not UTC timezone should be used to for timestamps in logging messages. Defaults to <c>false</c>.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Logging.Console.ConsoleLoggerFormat">
            <summary>
            Format of <see cref="T:Microsoft.Extensions.Logging.Console.ConsoleLogger" /> messages.
            </summary>
        </member>
        <member name="F:Microsoft.Extensions.Logging.Console.ConsoleLoggerFormat.Default">
            <summary>
            Produces messages in the default console format.
            </summary>
        </member>
        <member name="F:Microsoft.Extensions.Logging.Console.ConsoleLoggerFormat.Systemd">
            <summary>
            Produces messages in a format suitable for console output to the systemd journal.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Logging.Console.ConsoleLoggerOptions">
            <summary>
            Options for a <see cref="T:Microsoft.Extensions.Logging.Console.ConsoleLogger"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Logging.Console.ConsoleLoggerOptions.DisableColors">
            <summary>
            Disables colors when <see langword="true" />.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Logging.Console.ConsoleLoggerOptions.Format">
            <summary>
            Gets or sets log message format. Defaults to <see cref="F:Microsoft.Extensions.Logging.Console.ConsoleLoggerFormat.Default" />.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Logging.Console.ConsoleLoggerOptions.FormatterName">
            <summary>
            Name of the log message formatter to use. Defaults to "simple" />.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Logging.Console.ConsoleLoggerOptions.IncludeScopes">
            <summary>
            Includes scopes when <see langword="true" />.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Logging.Console.ConsoleLoggerOptions.LogToStandardErrorThreshold">
            <summary>
            Gets or sets value indicating the minimum level of messages that would get written to <c>Console.Error</c>.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Logging.Console.ConsoleLoggerOptions.TimestampFormat">
            <summary>
            Gets or sets format string used to format timestamp in logging messages. Defaults to <c>null</c>.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Logging.Console.ConsoleLoggerOptions.UseUtcTimestamp">
            <summary>
            Gets or sets indication whether or not UTC timezone should be used to for timestamps in logging messages. Defaults to <c>false</c>.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Logging.Console.ConsoleLoggerProvider">
            <summary>
            A provider of <see cref="T:Microsoft.Extensions.Logging.Console.ConsoleLogger"/> instances.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.Console.ConsoleLoggerProvider.#ctor(Microsoft.Extensions.Options.IOptionsMonitor{Microsoft.Extensions.Logging.Console.ConsoleLoggerOptions})">
            <summary>
            Creates an instance of <see cref="T:Microsoft.Extensions.Logging.Console.ConsoleLoggerProvider"/>.
            </summary>
            <param name="options">The options to create <see cref="T:Microsoft.Extensions.Logging.Console.ConsoleLogger"/> instances with.</param>
        </member>
        <member name="M:Microsoft.Extensions.Logging.Console.ConsoleLoggerProvider.#ctor(Microsoft.Extensions.Options.IOptionsMonitor{Microsoft.Extensions.Logging.Console.ConsoleLoggerOptions},System.Collections.Generic.IEnumerable{Microsoft.Extensions.Logging.Console.ConsoleFormatter})">
            <summary>
            Creates an instance of <see cref="T:Microsoft.Extensions.Logging.Console.ConsoleLoggerProvider"/>.
            </summary>
            <param name="options">The options to create <see cref="T:Microsoft.Extensions.Logging.Console.ConsoleLogger"/> instances with.</param>
            <param name="formatters">Log formatters added for <see cref="T:Microsoft.Extensions.Logging.Console.ConsoleLogger"/> insteaces.</param>
        </member>
        <member name="M:Microsoft.Extensions.Logging.Console.ConsoleLoggerProvider.CreateLogger(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Extensions.Logging.Console.ConsoleLoggerProvider.Dispose">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Extensions.Logging.Console.ConsoleLoggerProvider.SetScopeProvider(Microsoft.Extensions.Logging.IExternalScopeProvider)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.Extensions.Logging.Console.JsonConsoleFormatterOptions">
            <summary>
            Options for the built-in json console log formatter.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Logging.Console.JsonConsoleFormatterOptions.JsonWriterOptions">
            <summary>
            Gets or sets JsonWriterOptions.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Logging.Console.LoggerColorBehavior">
            <summary>
            Determines when to use color when logging messages.
            </summary>
        </member>
        <member name="F:Microsoft.Extensions.Logging.Console.LoggerColorBehavior.Default">
            <summary>
            Use the default color behavior, enabling color except when the console output is redirected.
            </summary>
            <remarks>
            Enables color except when the console output is redirected.
            </remarks>
        </member>
        <member name="F:Microsoft.Extensions.Logging.Console.LoggerColorBehavior.Enabled">
            <summary>
            Enable color for logging
            </summary>
        </member>
        <member name="F:Microsoft.Extensions.Logging.Console.LoggerColorBehavior.Disabled">
            <summary>
            Disable color for logging
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Logging.Console.SimpleConsoleFormatterOptions">
            <summary>
            Options for the built-in default console log formatter.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Logging.Console.SimpleConsoleFormatterOptions.ColorBehavior">
            <summary>
            Determines when to use color when logging messages.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Logging.Console.SimpleConsoleFormatterOptions.SingleLine">
            <summary>
            When <see langword="true" />, the entire message gets logged in a single line.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.ConsoleLoggerExtensions.AddConsole(Microsoft.Extensions.Logging.ILoggingBuilder)">
            <summary>
            Adds a console logger named 'Console' to the factory.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> to use.</param>
        </member>
        <member name="M:Microsoft.Extensions.Logging.ConsoleLoggerExtensions.AddConsole(Microsoft.Extensions.Logging.ILoggingBuilder,System.Action{Microsoft.Extensions.Logging.Console.ConsoleLoggerOptions})">
            <summary>
            Adds a console logger named 'Console' to the factory.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> to use.</param>
            <param name="configure">A delegate to configure the <see cref="T:Microsoft.Extensions.Logging.Console.ConsoleLogger"/>.</param>
        </member>
        <member name="M:Microsoft.Extensions.Logging.ConsoleLoggerExtensions.AddSimpleConsole(Microsoft.Extensions.Logging.ILoggingBuilder)">
            <summary>
            Add the default console log formatter named 'simple' to the factory with default properties.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> to use.</param>
        </member>
        <member name="M:Microsoft.Extensions.Logging.ConsoleLoggerExtensions.AddSimpleConsole(Microsoft.Extensions.Logging.ILoggingBuilder,System.Action{Microsoft.Extensions.Logging.Console.SimpleConsoleFormatterOptions})">
            <summary>
            Add and configure a console log formatter named 'simple' to the factory.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> to use.</param>
            <param name="configure">A delegate to configure the <see cref="T:Microsoft.Extensions.Logging.Console.ConsoleLogger"/> options for the built-in default log formatter.</param>
        </member>
        <member name="M:Microsoft.Extensions.Logging.ConsoleLoggerExtensions.AddJsonConsole(Microsoft.Extensions.Logging.ILoggingBuilder)">
            <summary>
            Add a console log formatter named 'json' to the factory with default properties.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> to use.</param>
        </member>
        <member name="M:Microsoft.Extensions.Logging.ConsoleLoggerExtensions.AddJsonConsole(Microsoft.Extensions.Logging.ILoggingBuilder,System.Action{Microsoft.Extensions.Logging.Console.JsonConsoleFormatterOptions})">
            <summary>
            Add and configure a console log formatter named 'json' to the factory.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> to use.</param>
            <param name="configure">A delegate to configure the <see cref="T:Microsoft.Extensions.Logging.Console.ConsoleLogger"/> options for the built-in json log formatter.</param>
        </member>
        <member name="M:Microsoft.Extensions.Logging.ConsoleLoggerExtensions.AddSystemdConsole(Microsoft.Extensions.Logging.ILoggingBuilder,System.Action{Microsoft.Extensions.Logging.Console.ConsoleFormatterOptions})">
            <summary>
            Add and configure a console log formatter named 'systemd' to the factory.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> to use.</param>
            <param name="configure">A delegate to configure the <see cref="T:Microsoft.Extensions.Logging.Console.ConsoleLogger"/> options for the built-in systemd log formatter.</param>
        </member>
        <member name="M:Microsoft.Extensions.Logging.ConsoleLoggerExtensions.AddSystemdConsole(Microsoft.Extensions.Logging.ILoggingBuilder)">
            <summary>
            Add a console log formatter named 'systemd' to the factory with default properties.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> to use.</param>
        </member>
        <member name="M:Microsoft.Extensions.Logging.ConsoleLoggerExtensions.AddConsoleFormatter``2(Microsoft.Extensions.Logging.ILoggingBuilder)">
            <summary>
            Adds a custom console logger formatter 'TFormatter' to be configured with options 'TOptions'.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> to use.</param>
        </member>
        <member name="M:Microsoft.Extensions.Logging.ConsoleLoggerExtensions.AddConsoleFormatter``2(Microsoft.Extensions.Logging.ILoggingBuilder,System.Action{``1})">
            <summary>
            Adds a custom console logger formatter 'TFormatter' to be configured with options 'TOptions'.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> to use.</param>
            <param name="configure">A delegate to configure options 'TOptions' for custom formatter 'TFormatter'.</param>
        </member>
        <member name="T:Microsoft.Extensions.Logging.NullExternalScopeProvider">
            <summary>
            Scope provider that does nothing.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Logging.NullExternalScopeProvider.Instance">
            <summary>
            Returns a cached instance of <see cref="T:Microsoft.Extensions.Logging.NullExternalScopeProvider"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.NullExternalScopeProvider.Microsoft#Extensions#Logging#IExternalScopeProvider#ForEachScope``1(System.Action{System.Object,``0},``0)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Extensions.Logging.NullExternalScopeProvider.Microsoft#Extensions#Logging#IExternalScopeProvider#Push(System.Object)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.Extensions.Logging.NullScope">
            <summary>
            An empty scope without any logic
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.NullScope.Dispose">
            <inheritdoc />
        </member>
        <member name="P:System.SR.BufferMaximumSizeExceeded">
            <summary>Cannot allocate a buffer of size {0}.</summary>
        </member>
    </members>
</doc>
