<?xml version="1.0"?>
<doc>
    <assembly>
        <name>System.Windows.Extensions</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Win32.SafeHandles.SafeHandleCache`1">
            <summary>Provides a cache for special instances of SafeHandles.</summary>
            <typeparam name="T">Specifies the type of SafeHandle.</typeparam>
        </member>
        <member name="M:Microsoft.Win32.SafeHandles.SafeHandleCache`1.GetInvalidHandle(System.Func{`0})">
            <summary>
            Gets a cached, invalid handle.  As the instance is cached, it should either never be Disposed
            or it should override <see cref="M:System.Runtime.InteropServices.SafeHandle.Dispose(System.Boolean)"/> to prevent disposal when the
            instance represents an invalid handle: <see cref="P:System.Runtime.InteropServices.SafeHandle.IsInvalid"/> returns <see language="true"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Win32.SafeHandles.SafeHandleCache`1.IsCachedInvalidHandle(System.Runtime.InteropServices.SafeHandle)">
            <summary>Gets whether the specified handle is invalid handle.</summary>
            <param name="handle">The handle to compare.</param>
            <returns>true if <paramref name="handle"/> is invalid handle; otherwise, false.</returns>
        </member>
        <member name="P:System.SR.ConvertInvalidPrimitive">
            <summary>{0} is not a valid value for {1}.</summary>
        </member>
        <member name="P:System.SR.Cryptography_InvalidHandle">
            <summary>{0} is an invalid handle.</summary>
        </member>
        <member name="P:System.SR.Enum_InvalidValue">
            <summary>Enumeration value '{0}' specified in condition mapping is not valid.</summary>
        </member>
        <member name="P:System.SR.PlatformNotSupported_System_Windows_Extensions">
            <summary>System.Windows.Extensions types are not supported on this platform.</summary>
        </member>
        <member name="P:System.SR.SoundAPIBadSoundLocation">
            <summary>Could not determine a universal resource identifier for the sound location.</summary>
        </member>
        <member name="P:System.SR.SoundAPIFileDoesNotExist">
            <summary>Please be sure a sound file exists at the specified location.</summary>
        </member>
        <member name="P:System.SR.SoundAPIFormatNotSupported">
            <summary>Sound API only supports playing PCM wave files.</summary>
        </member>
        <member name="P:System.SR.SoundAPIInvalidWaveFile">
            <summary>The file located at {0} is not a valid wave file.</summary>
        </member>
        <member name="P:System.SR.SoundAPIInvalidWaveHeader">
            <summary>The wave header is corrupt.</summary>
        </member>
        <member name="P:System.SR.SoundAPILoadTimedOut">
            <summary>The request to load the wave file in memory timed out.</summary>
        </member>
        <member name="P:System.SR.SoundAPILoadTimeout">
            <summary>The LoadTimeout property of a SoundPlayer cannot be negative.</summary>
        </member>
        <member name="P:System.SR.SoundAPIReadError">
            <summary>=There was an error reading the file located at {0}. Please make sure that a valid wave file exists at the specified location.</summary>
        </member>
        <member name="P:System.SR.TextParseFailedFormat">
            <summary>Text "{0}" cannot be parsed. The expected text format is "{1}".</summary>
        </member>
        <member name="P:System.SR.PropertyValueInvalidEntry">
            <summary>IDictionary parameter contains at least one entry that is not valid. Ensure all values are consistent with the object's properties.</summary>
        </member>
    </members>
</doc>
