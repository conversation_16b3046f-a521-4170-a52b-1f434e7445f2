<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.AspNetCore.Html.Abstractions</name>
    </assembly>
    <members>
        <member name="T:Microsoft.AspNetCore.Html.HtmlContentBuilder">
            <summary>
            An <see cref="T:Microsoft.AspNetCore.Html.IHtmlContentBuilder"/> implementation using an in memory list.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Html.HtmlContentBuilder.#ctor">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Html.HtmlContentBuilder"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Html.HtmlContentBuilder.#ctor(System.Int32)">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Html.HtmlContentBuilder"/> with the given initial capacity.
            </summary>
            <param name="capacity">The initial capacity of the backing store.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Html.HtmlContentBuilder.Count">
            <summary>
            Gets the number of elements in the <see cref="T:Microsoft.AspNetCore.Html.HtmlContentBuilder"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Html.HtmlContentBuilder.#ctor(System.Collections.Generic.IList{System.Object})">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Html.HtmlContentBuilder"/> with the given list of entries.
            </summary>
            <param name="entries">
            The list of entries. The <see cref="T:Microsoft.AspNetCore.Html.HtmlContentBuilder"/> will use this list without making a copy.
            </param>
        </member>
        <member name="M:Microsoft.AspNetCore.Html.HtmlContentBuilder.Append(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Html.HtmlContentBuilder.AppendHtml(Microsoft.AspNetCore.Html.IHtmlContent)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Html.HtmlContentBuilder.AppendHtml(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Html.HtmlContentBuilder.Clear">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Html.HtmlContentBuilder.CopyTo(Microsoft.AspNetCore.Html.IHtmlContentBuilder)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Html.HtmlContentBuilder.MoveTo(Microsoft.AspNetCore.Html.IHtmlContentBuilder)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Html.HtmlContentBuilder.WriteTo(System.IO.TextWriter,System.Text.Encodings.Web.HtmlEncoder)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Html.HtmlContentBuilderExtensions">
            <summary>
            Extension methods for <see cref="T:Microsoft.AspNetCore.Html.IHtmlContentBuilder"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Html.HtmlContentBuilderExtensions.AppendFormat(Microsoft.AspNetCore.Html.IHtmlContentBuilder,System.String,System.Object[])">
            <summary>
            Appends the specified <paramref name="format"/> to the existing content after replacing each format
            item with the HTML encoded <see cref="T:System.String"/> representation of the corresponding item in the
            <paramref name="args"/> array.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Html.IHtmlContentBuilder"/>.</param>
            <param name="format">
            The composite format <see cref="T:System.String"/> (see http://msdn.microsoft.com/en-us/library/txafckwd.aspx).
            The format string is assumed to be HTML encoded as-provided, and no further encoding will be performed.
            </param>
            <param name="args">
            The object array to format. Each element in the array will be formatted and then HTML encoded.
            </param>
            <returns>A reference to this instance after the append operation has completed.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Html.HtmlContentBuilderExtensions.AppendFormat(Microsoft.AspNetCore.Html.IHtmlContentBuilder,System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Appends the specified <paramref name="format"/> to the existing content with information from the
            <paramref name="formatProvider"/> after replacing each format item with the HTML encoded
            <see cref="T:System.String"/> representation of the corresponding item in the <paramref name="args"/> array.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Html.IHtmlContentBuilder"/>.</param>
            <param name="formatProvider">An object that supplies culture-specific formatting information.</param>
            <param name="format">
            The composite format <see cref="T:System.String"/> (see http://msdn.microsoft.com/en-us/library/txafckwd.aspx).
            The format string is assumed to be HTML encoded as-provided, and no further encoding will be performed.
            </param>
            <param name="args">
            The object array to format. Each element in the array will be formatted and then HTML encoded.
            </param>
            <returns>A reference to this instance after the append operation has completed.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Html.HtmlContentBuilderExtensions.AppendLine(Microsoft.AspNetCore.Html.IHtmlContentBuilder)">
            <summary>
            Appends an <see cref="P:System.Environment.NewLine"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Html.IHtmlContentBuilder"/>.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Html.IHtmlContentBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Html.HtmlContentBuilderExtensions.AppendLine(Microsoft.AspNetCore.Html.IHtmlContentBuilder,System.String)">
            <summary>
            Appends an <see cref="P:System.Environment.NewLine"/> after appending the <see cref="T:System.String"/> value.
            The value is treated as unencoded as-provided, and will be HTML encoded before writing to output.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Html.IHtmlContentBuilder"/>.</param>
            <param name="unencoded">The <see cref="T:System.String"/> to append.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Html.IHtmlContentBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Html.HtmlContentBuilderExtensions.AppendLine(Microsoft.AspNetCore.Html.IHtmlContentBuilder,Microsoft.AspNetCore.Html.IHtmlContent)">
            <summary>
            Appends an <see cref="P:System.Environment.NewLine"/> after appending the <see cref="T:Microsoft.AspNetCore.Html.IHtmlContent"/> value.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Html.IHtmlContentBuilder"/>.</param>
            <param name="content">The <see cref="T:Microsoft.AspNetCore.Html.IHtmlContent"/> to append.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Html.IHtmlContentBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Html.HtmlContentBuilderExtensions.AppendHtmlLine(Microsoft.AspNetCore.Html.IHtmlContentBuilder,System.String)">
            <summary>
            Appends an <see cref="P:System.Environment.NewLine"/> after appending the <see cref="T:System.String"/> value.
            The value is treated as HTML encoded as-provided, and no further encoding will be performed.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Html.IHtmlContentBuilder"/>.</param>
            <param name="encoded">The HTML encoded <see cref="T:System.String"/> to append.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Html.IHtmlContentBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Html.HtmlContentBuilderExtensions.SetContent(Microsoft.AspNetCore.Html.IHtmlContentBuilder,System.String)">
            <summary>
            Sets the content to the <see cref="T:System.String"/> value. The value is treated as unencoded as-provided,
            and will be HTML encoded before writing to output.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Html.IHtmlContentBuilder"/>.</param>
            <param name="unencoded">The <see cref="T:System.String"/> value that replaces the content.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Html.IHtmlContentBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Html.HtmlContentBuilderExtensions.SetHtmlContent(Microsoft.AspNetCore.Html.IHtmlContentBuilder,Microsoft.AspNetCore.Html.IHtmlContent)">
            <summary>
            Sets the content to the <see cref="T:Microsoft.AspNetCore.Html.IHtmlContent"/> value.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Html.IHtmlContentBuilder"/>.</param>
            <param name="content">The <see cref="T:Microsoft.AspNetCore.Html.IHtmlContent"/> value that replaces the content.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Html.IHtmlContentBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Html.HtmlContentBuilderExtensions.SetHtmlContent(Microsoft.AspNetCore.Html.IHtmlContentBuilder,System.String)">
            <summary>
            Sets the content to the <see cref="T:System.String"/> value. The value is treated as HTML encoded as-provided, and
            no further encoding will be performed.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Html.IHtmlContentBuilder"/>.</param>
            <param name="encoded">The HTML encoded <see cref="T:System.String"/> that replaces the content.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Html.IHtmlContentBuilder"/>.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Html.HtmlFormattableString">
            <summary>
            An <see cref="T:Microsoft.AspNetCore.Html.IHtmlContent"/> implementation of composite string formatting
            (see https://msdn.microsoft.com/en-us/library/txafckwd(v=vs.110).aspx) which HTML encodes
            formatted arguments.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Html.HtmlFormattableString.#ctor(System.String,System.Object[])">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Html.HtmlFormattableString"/> with the given <paramref name="format"/> and
            <paramref name="args"/>.
            </summary>
            <param name="format">A composite format string.</param>
            <param name="args">An array that contains objects to format.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Html.HtmlFormattableString.#ctor(System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Html.HtmlFormattableString"/> with the given <paramref name="formatProvider"/>,
            <paramref name="format"/> and <paramref name="args"/>.
            </summary>
            <param name="formatProvider">An object that provides culture-specific formatting information.</param>
            <param name="format">A composite format string.</param>
            <param name="args">An array that contains objects to format.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Html.HtmlFormattableString.WriteTo(System.IO.TextWriter,System.Text.Encodings.Web.HtmlEncoder)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Html.HtmlString">
            <summary>
            An <see cref="T:Microsoft.AspNetCore.Html.IHtmlContent"/> implementation that wraps an HTML encoded <see cref="T:System.String"/>.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Html.HtmlString.NewLine">
            <summary>
            An <see cref="T:Microsoft.AspNetCore.Html.HtmlString"/> instance for <see cref="P:System.Environment.NewLine"/>.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Html.HtmlString.Empty">
            <summary>
            An <see cref="T:Microsoft.AspNetCore.Html.HtmlString"/> instance for <see cref="F:System.String.Empty"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Html.HtmlString.#ctor(System.String)">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Html.HtmlString"/>.
            </summary>
            <param name="value">The HTML encoded value.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Html.HtmlString.Value">
            <summary>
            Gets the HTML encoded value.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Html.HtmlString.WriteTo(System.IO.TextWriter,System.Text.Encodings.Web.HtmlEncoder)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Html.HtmlString.ToString">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Html.IHtmlContent">
            <summary>
            HTML content which can be written to a TextWriter.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Html.IHtmlContent.WriteTo(System.IO.TextWriter,System.Text.Encodings.Web.HtmlEncoder)">
            <summary>
            Writes the content by encoding it with the specified <paramref name="encoder"/>
            to the specified <paramref name="writer"/>.
            </summary>
            <param name="writer">The <see cref="T:System.IO.TextWriter"/> to which the content is written.</param>
            <param name="encoder">The <see cref="T:System.Text.Encodings.Web.HtmlEncoder"/> which encodes the content to be written.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Html.IHtmlContentBuilder">
            <summary>
            A builder for HTML content.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Html.IHtmlContentBuilder.AppendHtml(Microsoft.AspNetCore.Html.IHtmlContent)">
            <summary>
            Appends an <see cref="T:Microsoft.AspNetCore.Html.IHtmlContent"/> instance.
            </summary>
            <param name="content">The <see cref="T:Microsoft.AspNetCore.Html.IHtmlContent"/> to append.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Html.IHtmlContentBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Html.IHtmlContentBuilder.Append(System.String)">
            <summary>
            Appends a <see cref="T:System.String"/> value. The value is treated as unencoded as-provided, and will be HTML
            encoded before writing to output.
            </summary>
            <param name="unencoded">The <see cref="T:System.String"/> to append.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Html.IHtmlContentBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Html.IHtmlContentBuilder.AppendHtml(System.String)">
            <summary>
            Appends an HTML encoded <see cref="T:System.String"/> value. The value is treated as HTML encoded as-provided, and
            no further encoding will be performed.
            </summary>
            <param name="encoded">The HTML encoded <see cref="T:System.String"/> to append.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Html.IHtmlContentBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Html.IHtmlContentBuilder.Clear">
            <summary>
            Clears the content.
            </summary>
            <returns>The <see cref="T:Microsoft.AspNetCore.Html.IHtmlContentBuilder"/>.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Html.IHtmlContentContainer">
            <summary>
            Defines a contract for <see cref="T:Microsoft.AspNetCore.Html.IHtmlContent"/> instances made up of several components which
            can be copied into an <see cref="T:Microsoft.AspNetCore.Html.IHtmlContentBuilder"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Html.IHtmlContentContainer.CopyTo(Microsoft.AspNetCore.Html.IHtmlContentBuilder)">
            <summary>
            Copies the contained content of this <see cref="T:Microsoft.AspNetCore.Html.IHtmlContentContainer"/> into <paramref name="builder"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Html.IHtmlContentBuilder"/>.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Html.IHtmlContentContainer.MoveTo(Microsoft.AspNetCore.Html.IHtmlContentBuilder)">
            <summary>
            <para>
            Moves the contained content of this <see cref="T:Microsoft.AspNetCore.Html.IHtmlContentContainer"/> into <paramref name="builder"/>.
            </para>
            <para>
            After <see cref="M:Microsoft.AspNetCore.Html.IHtmlContentContainer.MoveTo(Microsoft.AspNetCore.Html.IHtmlContentBuilder)"/> is called, this <see cref="T:Microsoft.AspNetCore.Html.IHtmlContentContainer"/> instance should be left
            in an empty state.
            </para>
            </summary>
            <param name="builder">The <see cref="T:Microsoft.AspNetCore.Html.IHtmlContentBuilder"/>.</param>
        </member>
    </members>
</doc>
