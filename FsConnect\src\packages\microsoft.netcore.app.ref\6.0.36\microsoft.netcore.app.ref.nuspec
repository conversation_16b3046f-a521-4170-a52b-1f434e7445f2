﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2011/08/nuspec.xsd">
  <metadata>
    <id>Microsoft.NETCore.App.Ref</id>
    <version>6.0.36</version>
    <authors>Microsoft</authors>
    <license type="expression">MIT</license>
    <licenseUrl>https://licenses.nuget.org/MIT</licenseUrl>
    <icon>Icon.png</icon>
    <projectUrl>https://dot.net/</projectUrl>
    <description>Internal implementation package not meant for direct consumption. Please do not reference directly. 
A set of .NET APIs that are included in the default .NET application model. Contains reference assemblies, documentation, and other design-time assets.</description>
    <releaseNotes>https://go.microsoft.com/fwlink/?LinkID=799421</releaseNotes>
    <copyright>© Microsoft Corporation. All rights reserved.</copyright>
    <serviceable>true</serviceable>
    <packageTypes>
      <packageType name="DotnetPlatform" />
    </packageTypes>
    <repository type="git" url="https://github.com/dotnet/runtime" commit="f1dd57165bfd91875761329ac3a8b17f6606ad18" />
  </metadata>
</package>