<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.AspNetCore.Authorization.Policy</name>
    </assembly>
    <members>
        <member name="T:Microsoft.AspNetCore.Authorization.Policy.AuthenticationFeatures">
            <summary>
            Keeps the User and AuthenticationResult consistent with each other
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler">
            <summary>
            Default implementation for <see cref="T:Microsoft.AspNetCore.Authorization.IAuthorizationMiddlewareResultHandler"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.HandleAsync(Microsoft.AspNetCore.Http.RequestDelegate,Microsoft.AspNetCore.Http.HttpContext,Microsoft.AspNetCore.Authorization.AuthorizationPolicy,Microsoft.AspNetCore.Authorization.Policy.PolicyAuthorizationResult)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Authorization.Policy.IPolicyEvaluator">
            <summary>
            Base class for authorization handlers that need to be called for a specific requirement type.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.Policy.IPolicyEvaluator.AuthenticateAsync(Microsoft.AspNetCore.Authorization.AuthorizationPolicy,Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            Does authentication for <see cref="P:Microsoft.AspNetCore.Authorization.AuthorizationPolicy.AuthenticationSchemes"/> and sets the resulting
            <see cref="T:System.Security.Claims.ClaimsPrincipal"/> to <see cref="P:Microsoft.AspNetCore.Http.HttpContext.User"/>.  If no schemes are set, this is a no-op.
            </summary>
            <param name="policy">The <see cref="T:Microsoft.AspNetCore.Authorization.AuthorizationPolicy"/>.</param>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Http.HttpContext"/>.</param>
            <returns><see cref="M:Microsoft.AspNetCore.Authentication.AuthenticateResult.Success(Microsoft.AspNetCore.Authentication.AuthenticationTicket)"/> unless all schemes specified by <see cref="P:Microsoft.AspNetCore.Authorization.AuthorizationPolicy.AuthenticationSchemes"/> fail to authenticate.  </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.Policy.IPolicyEvaluator.AuthorizeAsync(Microsoft.AspNetCore.Authorization.AuthorizationPolicy,Microsoft.AspNetCore.Authentication.AuthenticateResult,Microsoft.AspNetCore.Http.HttpContext,System.Object)">
            <summary>
            Attempts authorization for a policy using <see cref="T:Microsoft.AspNetCore.Authorization.IAuthorizationService"/>.
            </summary>
            <param name="policy">The <see cref="T:Microsoft.AspNetCore.Authorization.AuthorizationPolicy"/>.</param>
            <param name="authenticationResult">The result of a call to <see cref="M:Microsoft.AspNetCore.Authorization.Policy.IPolicyEvaluator.AuthenticateAsync(Microsoft.AspNetCore.Authorization.AuthorizationPolicy,Microsoft.AspNetCore.Http.HttpContext)"/>.</param>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Http.HttpContext"/>.</param>
            <param name="resource">
            An optional resource the policy should be checked with.
            If a resource is not required for policy evaluation you may pass null as the value.
            </param>
            <returns>Returns <see cref="M:Microsoft.AspNetCore.Authorization.Policy.PolicyAuthorizationResult.Success"/> if authorization succeeds.
            Otherwise returns <see cref="M:Microsoft.AspNetCore.Authorization.Policy.PolicyAuthorizationResult.Forbid(Microsoft.AspNetCore.Authorization.AuthorizationFailure)"/> if <see cref="P:Microsoft.AspNetCore.Authentication.AuthenticateResult.Succeeded"/>, otherwise
            returns  <see cref="M:Microsoft.AspNetCore.Authorization.Policy.PolicyAuthorizationResult.Challenge"/></returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Authorization.Policy.PolicyAuthorizationResult">
            <summary>
            The result of <see cref="M:Microsoft.AspNetCore.Authorization.Policy.IPolicyEvaluator.AuthorizeAsync(Microsoft.AspNetCore.Authorization.AuthorizationPolicy,Microsoft.AspNetCore.Authentication.AuthenticateResult,Microsoft.AspNetCore.Http.HttpContext,System.Object)"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authorization.Policy.PolicyAuthorizationResult.Challenged">
            <summary>
            If true, means the callee should challenge and try again.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authorization.Policy.PolicyAuthorizationResult.Forbidden">
            <summary>
            Authorization was forbidden.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authorization.Policy.PolicyAuthorizationResult.Succeeded">
            <summary>
            Authorization was successful.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Authorization.Policy.PolicyAuthorizationResult.AuthorizationFailure">
            <summary>
            Contains information about why authorization failed.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.Policy.PolicyAuthorizationResult.Challenge">
             <summary>
            Indicates that an unauthenticated user requested access to an endpoint that requires authentication.
             </summary>
             <returns>The <see cref="T:Microsoft.AspNetCore.Authorization.Policy.PolicyAuthorizationResult"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.Policy.PolicyAuthorizationResult.Forbid">
            <summary>
            Indiciates that the access to a resource was forbidden.
            </summary>
            <returns>The <see cref="T:Microsoft.AspNetCore.Authorization.Policy.PolicyAuthorizationResult"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.Policy.PolicyAuthorizationResult.Forbid(Microsoft.AspNetCore.Authorization.AuthorizationFailure)">
            <summary>
            Indiciates that the access to a resource was forbidden.
            </summary>
            <param name="authorizationFailure">Specifies the reason the authorization failed.s</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Authorization.Policy.PolicyAuthorizationResult"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.Policy.PolicyAuthorizationResult.Success">
            <summary>
            Indicates a successful authorization.
            </summary>
            <returns>The <see cref="T:Microsoft.AspNetCore.Authorization.Policy.PolicyAuthorizationResult"/>.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Authorization.Policy.PolicyEvaluator">
            <summary>
            Default implementation for <see cref="T:Microsoft.AspNetCore.Authorization.Policy.IPolicyEvaluator"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.Policy.PolicyEvaluator.#ctor(Microsoft.AspNetCore.Authorization.IAuthorizationService)">
            <summary>
            Constructor
            </summary>
            <param name="authorization">The authorization service.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.Policy.PolicyEvaluator.AuthenticateAsync(Microsoft.AspNetCore.Authorization.AuthorizationPolicy,Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            Does authentication for <see cref="P:Microsoft.AspNetCore.Authorization.AuthorizationPolicy.AuthenticationSchemes"/> and sets the resulting
            <see cref="T:System.Security.Claims.ClaimsPrincipal"/> to <see cref="P:Microsoft.AspNetCore.Http.HttpContext.User"/>.  If no schemes are set, this is a no-op.
            </summary>
            <param name="policy">The <see cref="T:Microsoft.AspNetCore.Authorization.AuthorizationPolicy"/>.</param>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Http.HttpContext"/>.</param>
            <returns><see cref="M:Microsoft.AspNetCore.Authentication.AuthenticateResult.Success(Microsoft.AspNetCore.Authentication.AuthenticationTicket)"/> unless all schemes specified by <see cref="P:Microsoft.AspNetCore.Authorization.AuthorizationPolicy.AuthenticationSchemes"/> failed to authenticate.  </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.Policy.PolicyEvaluator.AuthorizeAsync(Microsoft.AspNetCore.Authorization.AuthorizationPolicy,Microsoft.AspNetCore.Authentication.AuthenticateResult,Microsoft.AspNetCore.Http.HttpContext,System.Object)">
            <summary>
            Attempts authorization for a policy using <see cref="T:Microsoft.AspNetCore.Authorization.IAuthorizationService"/>.
            </summary>
            <param name="policy">The <see cref="T:Microsoft.AspNetCore.Authorization.AuthorizationPolicy"/>.</param>
            <param name="authenticationResult">The result of a call to <see cref="M:Microsoft.AspNetCore.Authorization.Policy.PolicyEvaluator.AuthenticateAsync(Microsoft.AspNetCore.Authorization.AuthorizationPolicy,Microsoft.AspNetCore.Http.HttpContext)"/>.</param>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Http.HttpContext"/>.</param>
            <param name="resource">
            An optional resource the policy should be checked with.
            If a resource is not required for policy evaluation you may pass null as the value.
            </param>
            <returns>Returns <see cref="M:Microsoft.AspNetCore.Authorization.Policy.PolicyAuthorizationResult.Success"/> if authorization succeeds.
            Otherwise returns <see cref="M:Microsoft.AspNetCore.Authorization.Policy.PolicyAuthorizationResult.Forbid(Microsoft.AspNetCore.Authorization.AuthorizationFailure)"/> if <see cref="P:Microsoft.AspNetCore.Authentication.AuthenticateResult.Succeeded"/>, otherwise
            returns  <see cref="M:Microsoft.AspNetCore.Authorization.Policy.PolicyAuthorizationResult.Challenge"/></returns>
        </member>
        <member name="P:Microsoft.AspNetCore.Authorization.Policy.Resources.Exception_UnableToFindServices">
            <summary>Unable to find the required services. Please add all the required services by calling '{0}.{1}' in the application startup code.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.Policy.Resources.FormatException_UnableToFindServices(System.Object,System.Object)">
            <summary>Unable to find the required services. Please add all the required services by calling '{0}.{1}' in the application startup code.</summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Authorization.AuthorizationMiddleware">
            <summary>
            A middleware that enables authorization capabilities.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.#ctor(Microsoft.AspNetCore.Http.RequestDelegate,Microsoft.AspNetCore.Authorization.IAuthorizationPolicyProvider)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Authorization.AuthorizationMiddleware"/>.
            </summary>
            <param name="next">The next middleware in the application middleware pipeline.</param>
            <param name="policyProvider">The <see cref="T:Microsoft.AspNetCore.Authorization.IAuthorizationPolicyProvider"/>.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            Invokes the middleware performing authorization.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Http.HttpContext"/>.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Authorization.IAuthorizationMiddlewareResultHandler">
            <summary>
            Allow custom handling of authorization and handling of the authorization response.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Authorization.IAuthorizationMiddlewareResultHandler.HandleAsync(Microsoft.AspNetCore.Http.RequestDelegate,Microsoft.AspNetCore.Http.HttpContext,Microsoft.AspNetCore.Authorization.AuthorizationPolicy,Microsoft.AspNetCore.Authorization.Policy.PolicyAuthorizationResult)">
            <summary>
            Evaluates the authorization requirement and processes the authorization result.
            </summary>
            <param name="next">
            The next middleware in the application pipeline. Implementations may not invoke this if the authorization did not succeed.
            </param>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Http.HttpContext"/>.</param>
            <param name="policy">The <see cref="T:Microsoft.AspNetCore.Authorization.AuthorizationPolicy"/> for the resource.</param>
            <param name="authorizeResult">The result of authorization.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Builder.AuthorizationAppBuilderExtensions">
            <summary>
            Extension methods to add authorization capabilities to an HTTP application pipeline.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.AuthorizationAppBuilderExtensions.UseAuthorization(Microsoft.AspNetCore.Builder.IApplicationBuilder)">
            <summary>
            Adds the <see cref="T:Microsoft.AspNetCore.Authorization.AuthorizationMiddleware"/> to the specified <see cref="T:Microsoft.AspNetCore.Builder.IApplicationBuilder"/>, which enables authorization capabilities.
            <para>
            When authorizing a resource that is routed using endpoint routing, this call must appear between the calls to
            <c>app.UseRouting()</c> and <c>app.UseEndpoints(...)</c> for the middleware to function correctly.
            </para>
            </summary>
            <param name="app">The <see cref="T:Microsoft.AspNetCore.Builder.IApplicationBuilder"/> to add the middleware to.</param>
            <returns>A reference to <paramref name="app"/> after the operation has completed.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Builder.AuthorizationEndpointConventionBuilderExtensions">
            <summary>
            Authorization extension methods for <see cref="T:Microsoft.AspNetCore.Builder.IEndpointConventionBuilder"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.AuthorizationEndpointConventionBuilderExtensions.RequireAuthorization``1(``0)">
            <summary>
            Adds the default authorization policy to the endpoint(s).
            </summary>
            <param name="builder">The endpoint convention builder.</param>
            <returns>The original convention builder parameter.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.AuthorizationEndpointConventionBuilderExtensions.RequireAuthorization``1(``0,System.String[])">
            <summary>
            Adds authorization policies with the specified names to the endpoint(s).
            </summary>
            <param name="builder">The endpoint convention builder.</param>
            <param name="policyNames">A collection of policy names. If empty, the default authorization policy will be used.</param>
            <returns>The original convention builder parameter.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.AuthorizationEndpointConventionBuilderExtensions.RequireAuthorization``1(``0,Microsoft.AspNetCore.Authorization.IAuthorizeData[])">
            <summary>
            Adds authorization policies with the specified <see cref="T:Microsoft.AspNetCore.Authorization.IAuthorizeData"/> to the endpoint(s).
            </summary>
            <param name="builder">The endpoint convention builder.</param>
            <param name="authorizeData">
            A collection of <paramref name="authorizeData"/>. If empty, the default authorization policy will be used.
            </param>
            <returns>The original convention builder parameter.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.AuthorizationEndpointConventionBuilderExtensions.AllowAnonymous``1(``0)">
            <summary>
            Allows anonymous access to the endpoint by adding <see cref="T:Microsoft.AspNetCore.Authorization.AllowAnonymousAttribute" /> to the endpoint metadata. This will bypass
            all authorization checks for the endpoint including the default authorization policy and fallback authorization policy.
            </summary>
            <param name="builder">The endpoint convention builder.</param>
            <returns>The original convention builder parameter.</returns>
        </member>
        <member name="T:Microsoft.Extensions.DependencyInjection.PolicyServiceCollectionExtensions">
            <summary>
            Extension methods for setting up authorization services in an <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.PolicyServiceCollectionExtensions.AddAuthorizationPolicyEvaluator(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Adds the authorization policy evaluator service to the specified <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.
            </summary>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add services to.</param>
            <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.PolicyServiceCollectionExtensions.AddAuthorization(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Adds authorization policy services to the specified <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.
            </summary>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add services to.</param>
            <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.PolicyServiceCollectionExtensions.AddAuthorization(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action{Microsoft.AspNetCore.Authorization.AuthorizationOptions})">
            <summary>
            Adds authorization policy services to the specified <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.
            </summary>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add services to.</param>
            <param name="configure">An action delegate to configure the provided <see cref="T:Microsoft.AspNetCore.Authorization.AuthorizationOptions"/>.</param>
            <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> so that additional calls can be chained.</returns>
        </member>
        <member name="T:Microsoft.Extensions.Internal.SecurityHelper">
            <summary>
            Helper code used when implementing authentication middleware
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Internal.SecurityHelper.MergeUserPrincipal(System.Security.Claims.ClaimsPrincipal,System.Security.Claims.ClaimsPrincipal)">
            <summary>
            Add all ClaimsIdentities from an additional ClaimPrincipal to the ClaimsPrincipal
            Merges a new claims principal, placing all new identities first, and eliminating
            any empty unauthenticated identities from context.User
            </summary>
            <param name="existingPrincipal">The <see cref="T:System.Security.Claims.ClaimsPrincipal"/> containing existing <see cref="T:System.Security.Claims.ClaimsIdentity"/>.</param>
            <param name="additionalPrincipal">The <see cref="T:System.Security.Claims.ClaimsPrincipal"/> containing <see cref="T:System.Security.Claims.ClaimsIdentity"/> to be added.</param>
        </member>
    </members>
</doc>
