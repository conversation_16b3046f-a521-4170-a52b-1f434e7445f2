﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata>
    <id>Serilog.Sinks.Console</id>
    <version>3.1.1</version>
    <authors>Serilog Contributors</authors>
    <owners>Serilog Contributors</owners>
    <requireLicenseAcceptance>false</requireLicenseAcceptance>
    <licenseUrl>https://www.apache.org/licenses/LICENSE-2.0</licenseUrl>
    <projectUrl>https://github.com/serilog/serilog-sinks-console</projectUrl>
    <iconUrl>http://serilog.net/images/serilog-sink-nuget.png</iconUrl>
    <description>A Serilog sink that writes log events to the console/terminal.</description>
    <tags>serilog console terminal</tags>
    <dependencies>
      <group targetFramework=".NETFramework4.5">
        <dependency id="Serilog" version="2.5.0" exclude="Build,Analyzers" />
      </group>
      <group targetFramework=".NETCoreApp1.1">
        <dependency id="Serilog" version="2.5.0" exclude="Build,Analyzers" />
        <dependency id="System.Console" version="4.3.0" exclude="Build,Analyzers" />
        <dependency id="System.Runtime.InteropServices" version="4.3.0" exclude="Build,Analyzers" />
        <dependency id="System.Runtime.InteropServices.RuntimeInformation" version="4.3.0" exclude="Build,Analyzers" />
      </group>
      <group targetFramework=".NETStandard1.3">
        <dependency id="Serilog" version="2.5.0" exclude="Build,Analyzers" />
        <dependency id="System.Console" version="4.3.0" exclude="Build,Analyzers" />
      </group>
    </dependencies>
    <frameworkAssemblies>
      <frameworkAssembly assemblyName="Microsoft.CSharp" targetFramework=".NETFramework4.5" />
      <frameworkAssembly assemblyName="System.Core" targetFramework=".NETFramework4.5" />
      <frameworkAssembly assemblyName="System" targetFramework=".NETFramework4.5" />
    </frameworkAssemblies>
  </metadata>
</package>