﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata minClientVersion="2.12">
    <id>System.Threading</id>
    <version>4.3.0</version>
    <title>System.Threading</title>
    <authors>Microsoft</authors>
    <owners>microsoft,dotnetframework</owners>
    <requireLicenseAcceptance>true</requireLicenseAcceptance>
    <licenseUrl>http://go.microsoft.com/fwlink/?LinkId=329770</licenseUrl>
    <projectUrl>https://dot.net/</projectUrl>
    <iconUrl>http://go.microsoft.com/fwlink/?LinkID=288859</iconUrl>
    <description>Provides the fundamental synchronization primitives, including System.Threading.Monitor and System.Threading.Mutex, that are required when writing asynchronous code.

Commonly Used Types:
System.Threading.Monitor
System.Threading.SynchronizationContext
System.Threading.ManualResetEvent
System.Threading.AutoResetEvent
System.Threading.ThreadLocal&lt;T&gt;
System.Threading.EventWaitHandle
System.Threading.SemaphoreSlim
System.Threading.Mutex
 
When using NuGet 3.x this package requires at least version 3.4.</description>
    <releaseNotes>https://go.microsoft.com/fwlink/?LinkID=799421</releaseNotes>
    <copyright>© Microsoft Corporation.  All rights reserved.</copyright>
    <serviceable>true</serviceable>
    <dependencies>
      <group targetFramework="MonoAndroid1.0" />
      <group targetFramework="MonoTouch1.0" />
      <group targetFramework=".NETFramework4.5" />
      <group targetFramework=".NETCore5.0">
        <dependency id="System.Runtime" version="4.3.0" />
        <dependency id="System.Threading.Tasks" version="4.3.0" />
      </group>
      <group targetFramework=".NETStandard1.0">
        <dependency id="System.Runtime" version="4.3.0" />
        <dependency id="System.Threading.Tasks" version="4.3.0" />
      </group>
      <group targetFramework=".NETStandard1.3">
        <dependency id="System.Runtime" version="4.3.0" />
        <dependency id="System.Threading.Tasks" version="4.3.0" />
      </group>
      <group targetFramework=".NETPortable0.0-Profile259" />
      <group targetFramework="Windows8.0" />
      <group targetFramework="WindowsPhone8.0" />
      <group targetFramework="WindowsPhoneApp8.1" />
      <group targetFramework="Xamarin.iOS1.0" />
      <group targetFramework="Xamarin.Mac2.0" />
      <group targetFramework="Xamarin.TVOS1.0" />
      <group targetFramework="Xamarin.WatchOS1.0" />
    </dependencies>
    <frameworkAssemblies>
      <frameworkAssembly assemblyName="System" targetFramework=".NETFramework4.5" />
      <frameworkAssembly assemblyName="System.Core" targetFramework=".NETFramework4.5" />
    </frameworkAssemblies>
  </metadata>
</package>