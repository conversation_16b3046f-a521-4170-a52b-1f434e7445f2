<?xml version="1.0"?>
<doc>
    <assembly>
        <name>System.Resources.Extensions</name>
    </assembly>
    <members>
        <member name="M:System.Resources.Extensions.PreserializedResourceWriter.AddResource(System.String,System.String,System.String)">
            <summary>
            Adds a resource of specified type represented by a string value.
            If the type is a primitive type, the value will be converted using TypeConverter by the writer
            to that primitive type and stored in the resources in binary format.
            If the type is not a primitive type, the string value will be stored in the resources as a
            string and converted with a TypeConverter for the type when reading the resource.
            This is done to avoid activating arbitrary types during resource writing.
            </summary>
            <param name="name">Resource name</param>
            <param name="value">Value of the resource in string form understood by the type's TypeConverter</param>
            <param name="typeName">Assembly qualified type name of the resource</param>
        </member>
        <member name="M:System.Resources.Extensions.PreserializedResourceWriter.AddTypeConverterResource(System.String,System.Byte[],System.String)">
            <summary>
            Adds a resource of specified type represented by a byte[] value which will be
            passed to the type's TypeConverter when reading the resource.
            </summary>
            <param name="name">Resource name</param>
            <param name="value">Value of the resource in byte[] form understood by the type's TypeConverter</param>
            <param name="typeName">Assembly qualified type name of the resource</param>
        </member>
        <member name="M:System.Resources.Extensions.PreserializedResourceWriter.AddBinaryFormattedResource(System.String,System.Byte[],System.String)">
            <summary>
            Adds a resource of specified type represented by a byte[] value which will be
            passed to BinaryFormatter when reading the resource.
            </summary>
            <param name="name">Resource name</param>
            <param name="value">Value of the resource in byte[] form understood by BinaryFormatter</param>
            <param name="typeName">Assembly qualified type name of the resource</param>
        </member>
        <member name="M:System.Resources.Extensions.PreserializedResourceWriter.AddActivatorResource(System.String,System.IO.Stream,System.String,System.Boolean)">
            <summary>
            Adds a resource of specified type represented by a Stream value which will be
            passed to the type's constructor when reading the resource.
            </summary>
            <param name="name">Resource name</param>
            <param name="value">Value of the resource in Stream form understood by the types constructor</param>
            <param name="typeName">Assembly qualified type name of the resource</param>
            <param name="closeAfterWrite">Indicates that the stream should be closed after resources have been written</param>
        </member>
        <member name="T:System.Resources.Extensions.TypeNameComparer">
            <summary>
            Compares type names as strings, ignoring version.
            When type names are missing, mscorlib is assumed.
            This comparer is not meant to capture all scenarios (eg: TypeForwards)
            but is meant to serve as a best effort, avoiding false positives, in the
            absense of real type metadata.
            </summary>
        </member>
        <member name="P:System.SR.ArgumentOutOfRange_StreamLength">
            <summary>Stream length must be non-negative and less than 2^31 - 1 - origin.</summary>
        </member>
        <member name="P:System.SR.Argument_StreamNotReadable">
            <summary>Stream was not readable</summary>
        </member>
        <member name="P:System.SR.Argument_StreamNotWritable">
            <summary>Stream was not writable.</summary>
        </member>
        <member name="P:System.SR.Arg_ResourceFileUnsupportedVersion">
            <summary>The ResourceReader class does not know how to read this version of .resources files. Expected version: {0}  This file: {1}</summary>
        </member>
        <member name="P:System.SR.BadImageFormat_InvalidType">
            <summary>Corrupt .resources file.  The specified type doesn't exist.</summary>
        </member>
        <member name="P:System.SR.BadImageFormat_NegativeStringLength">
            <summary>Corrupt .resources file. String length must be non-negative.</summary>
        </member>
        <member name="P:System.SR.BadImageFormat_ResourceDataLengthInvalid">
            <summary>Corrupt .resources file.  The specified data length '{0}' is not a valid position in the stream.</summary>
        </member>
        <member name="P:System.SR.BadImageFormat_ResourceNameCorrupted">
            <summary>Corrupt .resources file. A resource name extends past the end of the stream.</summary>
        </member>
        <member name="P:System.SR.BadImageFormat_ResourceNameCorrupted_NameIndex">
            <summary>Corrupt .resources file. The resource name for name index {0} extends past the end of the stream.</summary>
        </member>
        <member name="P:System.SR.BadImageFormat_ResourcesDataInvalidOffset">
            <summary>Corrupt .resources file. Invalid offset '{0}' into data section.</summary>
        </member>
        <member name="P:System.SR.BadImageFormat_ResourcesHeaderCorrupted">
            <summary>Corrupt .resources file. Unable to read resources from this file because of invalid header information. Try regenerating the .resources file.</summary>
        </member>
        <member name="P:System.SR.BadImageFormat_ResourcesIndexTooLong">
            <summary>Corrupt .resources file. String for name index '{0}' extends past the end of the file.</summary>
        </member>
        <member name="P:System.SR.BadImageFormat_ResourcesNameInvalidOffset">
            <summary>Corrupt .resources file. Invalid offset '{0}' into name section.</summary>
        </member>
        <member name="P:System.SR.BadImageFormat_ResourcesNameTooLong">
            <summary>Corrupt .resources file. Resource name extends past the end of the file.</summary>
        </member>
        <member name="P:System.SR.BadImageFormat_ResType_SerBlobMismatch">
            <summary>The type serialized in the .resources file was not the same type that the .resources file said it contained. Expected '{0}' but read '{1}'.</summary>
        </member>
        <member name="P:System.SR.BadImageFormat_TypeMismatch">
            <summary>Corrupt .resources file.  The specified type doesn't match the available data in the stream.</summary>
        </member>
        <member name="P:System.SR.Format_Bad7BitInt32">
            <summary>Too many bytes in what should have been a 7 bit encoded Int32.</summary>
        </member>
        <member name="P:System.SR.InvalidOperation_EnumEnded">
            <summary>Enumeration already finished.</summary>
        </member>
        <member name="P:System.SR.InvalidOperation_EnumNotStarted">
            <summary>Enumeration has not started. Call MoveNext.</summary>
        </member>
        <member name="P:System.SR.InvalidOperation_ResourceNotString_Type">
            <summary>Resource was of type '{0}' instead of String - call GetObject instead.</summary>
        </member>
        <member name="P:System.SR.InvalidOperation_ResourceWriterSaved">
            <summary>The resource writer has already been closed and cannot be edited.</summary>
        </member>
        <member name="P:System.SR.NotSupported_BinarySerializedResources">
            <summary>This platform does not support binary serialized resources.</summary>
        </member>
        <member name="P:System.SR.NotSupported_ResourceObjectSerialization">
            <summary>Cannot read resources that depend on serialization.</summary>
        </member>
        <member name="P:System.SR.NotSupported_UnseekableStream">
            <summary>Stream does not support seeking.</summary>
        </member>
        <member name="P:System.SR.NotSupported_WrongResourceReader_Type">
            <summary>This .resources file should not be read with this reader. The resource reader type is "{0}".</summary>
        </member>
        <member name="P:System.SR.ObjectDisposed_ResourceSet">
            <summary>Cannot access a closed resource set.</summary>
        </member>
        <member name="P:System.SR.ResourceManager_ReflectionNotAllowed">
            <summary>Use of ResourceManager for custom types is disabled. Set the MSBuild Property CustomResourceTypesSupport to true in order to enable it.</summary>
        </member>
        <member name="P:System.SR.ResourceReaderIsClosed">
            <summary>ResourceReader is closed.</summary>
        </member>
        <member name="P:System.SR.Resources_StreamNotValid">
            <summary>Stream is not a valid resource file.</summary>
        </member>
        <member name="P:System.SR.TypeLoadException_CannotLoadConverter">
            <summary>Could not load a converter for type {0}.</summary>
        </member>
    </members>
</doc>
