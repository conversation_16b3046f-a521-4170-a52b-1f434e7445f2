<?xml version="1.0"?>
<doc>
    <assembly>
        <name>System.Security.Cryptography.ProtectedData</name>
    </assembly>
    <members>
        <member name="P:System.SR.Cryptography_DpApi_ProfileMayNotBeLoaded">
            <summary>The data protection operation was unsuccessful. This may have been caused by not having the user profile loaded for the current thread's user context, which may be the case when the thread is impersonating.</summary>
        </member>
        <member name="P:System.SR.PlatformNotSupported_CryptographyProtectedData">
            <summary>Windows Data Protection API (DPAPI) is not supported on this platform.</summary>
        </member>
        <member name="M:Interop.Kernel32.GetMessage(System.Int32)">
            <summary>
                Returns a string message for the specified Win32 error code.
            </summary>
        </member>
    </members>
</doc>
