<?xml version="1.0"?>
<doc>
    <assembly>
        <name>CTrue.FsConnect</name>
    </assembly>
    <members>
        <member name="T:CTrue.FsConnect.Bcd">
            <summary>
            Handles the BCD format
            </summary>
        </member>
        <member name="M:CTrue.FsConnect.Bcd.Bcd2Dec(System.UInt32)">
            <summary>
            Converts from binary coded decimal to integer
            </summary>
            <param name="num"></param>
            <returns></returns>
        </member>
        <member name="M:CTrue.FsConnect.Bcd.Dec2Bcd(System.UInt32)">
            <summary>
            Converts from integer to binary coded decimal
            </summary>
            <param name="num"></param>
            <returns></returns>
        </member>
        <member name="T:CTrue.FsConnect.FsDataReceivedEventArgs">
            <summary>
            Used to hold data received from Flight Simulator.
            </summary>
        </member>
        <member name="P:CTrue.FsConnect.FsDataReceivedEventArgs.RequestId">
            <summary>
            The request id of the received data.
            </summary>
        </member>
        <member name="P:CTrue.FsConnect.FsDataReceivedEventArgs.ObjectID">
            <summary>
            The ID of the object that the update is for.
            </summary>
        </member>
        <member name="P:CTrue.FsConnect.FsDataReceivedEventArgs.DefineId">
            <summary>
            The ID of the client defined data definition.
            </summary>
        </member>
        <member name="P:CTrue.FsConnect.FsDataReceivedEventArgs.Flags">
            <summary>
            	The flags that were set for this data request, see SimConnect_RequestDataOnSimObject for a description of the flags. This parameter will always be set to zero if the call was SimConnect_RequestDataOnSimObjectType.
            </summary>
        </member>
        <member name="P:CTrue.FsConnect.FsDataReceivedEventArgs.Data">
            <summary>
            The data that was received.
            </summary>
        </member>
        <member name="P:CTrue.FsConnect.FsDataReceivedEventArgs.EntryNumber">
            <summary>
            If multiple objects are being returned, this is the index number of this object out of a total of <see cref="P:CTrue.FsConnect.FsDataReceivedEventArgs.OutOf"/>. This will always be 1 if the call was SimConnect_RequestDataOnSimObject, and can be 0 or more if the call was SimConnect_RequestDataOnSimObjectType.
            </summary>
        </member>
        <member name="P:CTrue.FsConnect.FsDataReceivedEventArgs.OutOf">
            <summary>
            The total number of objects being returned. Note that <see cref="P:CTrue.FsConnect.FsDataReceivedEventArgs.EntryNumber"/> and <see cref="P:CTrue.FsConnect.FsDataReceivedEventArgs.OutOf"/> start with 1 not 0, so if two objects are being  returned <see cref="P:CTrue.FsConnect.FsDataReceivedEventArgs.EntryNumber"/> and <see cref="P:CTrue.FsConnect.FsDataReceivedEventArgs.OutOf"/> pairs will be 1,2 and 2,2 for the two objects. This will always be 1 if the call was SimConnect_RequestDataOnSimObject, and can be 0 or more if the call was SimConnect_RequestDataOnSimObjectType.
            </summary>
        </member>
        <member name="P:CTrue.FsConnect.FsDataReceivedEventArgs.DefineCount">
            <summary>
            The number of items in the data list.
            </summary>
        </member>
        <member name="P:CTrue.FsConnect.FsDataReceivedEventArgs.DataItemCount">
            <summary>
            Number of objects returned.
            </summary>
        </member>
        <member name="T:CTrue.FsConnect.FsErrorEventArgs">
            <summary>
            Describes information about an error reported by SimConnect.
            </summary>
        </member>
        <member name="T:CTrue.FsConnect.ObjectAddRemoveEventReceivedEventArgs">
            <summary>
            Used to hold data received from Flight Simulator.
            </summary>
        </member>
        <member name="P:CTrue.FsConnect.ObjectAddRemoveEventReceivedEventArgs.Added">
            <summary>
            Gets or sets whether the Sim Object was added, or removed.
            </summary>
        </member>
        <member name="P:CTrue.FsConnect.ObjectAddRemoveEventReceivedEventArgs.RequestId">
            <summary>
            The request id of the received data.
            </summary>
        </member>
        <member name="P:CTrue.FsConnect.ObjectAddRemoveEventReceivedEventArgs.Data">
            <summary>
            The data that was received.
            </summary>
        </member>
        <member name="P:CTrue.FsConnect.ObjectAddRemoveEventReceivedEventArgs.ObjectID">
            <summary>
            The Object Id of the added or removed Sim Object.
            </summary>
        </member>
        <member name="P:CTrue.FsConnect.FrequencyBcd.Bcd32Value">
            <summary>
            Gets the BCD encoded as 32 bit.
            </summary>
        </member>
        <member name="P:CTrue.FsConnect.FrequencyBcd.Bcd16Value">
            <summary>
            Gets the BCD encoded as 16 bit.
            </summary>
        </member>
        <member name="T:CTrue.FsConnect.FsConnect">
            <inheritdoc />
        </member>
        <member name="E:CTrue.FsConnect.FsConnect.ConnectionChanged">
            <inheritdoc />
        </member>
        <member name="E:CTrue.FsConnect.FsConnect.FsDataReceived">
            <inheritdoc />
        </member>
        <member name="E:CTrue.FsConnect.FsConnect.ObjectAddRemoveEventReceived">
            <inheritdoc />
        </member>
        <member name="E:CTrue.FsConnect.FsConnect.FsError">
            <inheritdoc />
        </member>
        <member name="E:CTrue.FsConnect.FsConnect.AircraftLoaded">
            <inheritdoc />
        </member>
        <member name="E:CTrue.FsConnect.FsConnect.FlightLoaded">
            <inheritdoc />
        </member>
        <member name="E:CTrue.FsConnect.FsConnect.PauseStateChanged">
            <inheritdoc />
        </member>
        <member name="E:CTrue.FsConnect.FsConnect.SimStateChanged">
            <inheritdoc />
        </member>
        <member name="E:CTrue.FsConnect.FsConnect.Crashed">
            <inheritdoc />
        </member>
        <member name="P:CTrue.FsConnect.FsConnect.Connected">
            <inheritdoc />
        </member>
        <member name="P:CTrue.FsConnect.FsConnect.ConnectionInfo">
            <inheritdoc />
        </member>
        <member name="P:CTrue.FsConnect.FsConnect.SimConnectFileLocation">
            <inheritdoc />
        </member>
        <member name="P:CTrue.FsConnect.FsConnect.Paused">
            <inheritdoc />
        </member>
        <member name="M:CTrue.FsConnect.FsConnect.Connect(System.String,System.UInt32)">
            <inheritdoc />
        </member>
        <member name="M:CTrue.FsConnect.FsConnect.RegisterInputEvent(CTrue.FsConnect.InputEventInfo)">
            <inheritdoc />
        </member>
        <member name="M:CTrue.FsConnect.FsConnect.Connect(System.String,System.String,System.UInt32,CTrue.FsConnect.SimConnectProtocol)">
            <inheritdoc />
        </member>
        <member name="M:CTrue.FsConnect.FsConnect.Disconnect">
            <inheritdoc />
        </member>
        <member name="M:CTrue.FsConnect.FsConnect.RegisterDataDefinition``1(System.Enum,System.Collections.Generic.List{CTrue.FsConnect.SimVar})">
            <inheritdoc />
        </member>
        <member name="M:CTrue.FsConnect.FsConnect.RegisterDataDefinition``1(System.Int32,System.Collections.Generic.List{CTrue.FsConnect.SimVar})">
            <inheritdoc />
        </member>
        <member name="M:CTrue.FsConnect.FsConnect.RegisterDataDefinition``1(System.Collections.Generic.List{CTrue.FsConnect.SimVar})">
            <inheritdoc />
        </member>
        <member name="M:CTrue.FsConnect.FsConnect.RegisterDataDefinition``1(System.Enum)">
            <inheritdoc />
        </member>
        <member name="M:CTrue.FsConnect.FsConnect.RegisterDataDefinition``1(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:CTrue.FsConnect.FsConnect.RegisterDataDefinition``1">
            <inheritdoc />
        </member>
        <member name="M:CTrue.FsConnect.FsConnect.RequestDataOnSimObject(System.Enum,System.Enum,System.UInt32,CTrue.FsConnect.FsConnectPeriod,CTrue.FsConnect.FsConnectDRequestFlag,System.UInt32,System.UInt32,System.UInt32)">
            <inheritdoc />
        </member>
        <member name="M:CTrue.FsConnect.FsConnect.RequestDataOnSimObject(System.Enum,System.Int32,System.UInt32,CTrue.FsConnect.FsConnectPeriod,CTrue.FsConnect.FsConnectDRequestFlag,System.UInt32,System.UInt32,System.UInt32)">
            <inheritdoc />
        </member>
        <member name="M:CTrue.FsConnect.FsConnect.RequestData(System.Enum,System.Enum,System.UInt32,CTrue.FsConnect.FsConnectSimobjectType)">
            <inheritdoc />
        </member>
        <member name="M:CTrue.FsConnect.FsConnect.RequestData(System.Int32,System.Int32,System.UInt32,CTrue.FsConnect.FsConnectSimobjectType)">
            <inheritdoc />
        </member>
        <member name="M:CTrue.FsConnect.FsConnect.UpdateData``1(System.Enum,``0,System.UInt32)">
            <inheritdoc />
        </member>
        <member name="M:CTrue.FsConnect.FsConnect.UpdateData``1(System.Int32,``0,System.UInt32)">
            <inheritdoc />
        </member>
        <member name="M:CTrue.FsConnect.FsConnect.GetNextId">
            <summary>
            Gets the next id, for definitions and other SimConnect artifacts that require it.
            </summary>
            <returns>Returns an int that can be used to identifying SimConnect artifacts, such as definitions and events.</returns>
        </member>
        <member name="M:CTrue.FsConnect.FsConnect.MapClientEventToSimEvent(System.Enum,System.Enum,System.String)">
            <inheritdoc />
        </member>
        <member name="M:CTrue.FsConnect.FsConnect.MapClientEventToSimEvent(System.Enum,System.Enum,CTrue.FsConnect.FsEventNameId)">
            <inheritdoc />
        </member>
        <member name="M:CTrue.FsConnect.FsConnect.MapClientEventToSimEvent(System.Int32,System.Int32,System.String)">
            <inheritdoc />
        </member>
        <member name="M:CTrue.FsConnect.FsConnect.MapClientEventToSimEvent(System.Int32,System.Int32,CTrue.FsConnect.FsEventNameId)">
            <inheritdoc />
        </member>
        <member name="M:CTrue.FsConnect.FsConnect.SetNotificationGroupPriority(System.Enum)">
            <inheritdoc />
        </member>
        <member name="M:CTrue.FsConnect.FsConnect.SetNotificationGroupPriority(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:CTrue.FsConnect.FsConnect.TransmitClientEvent(System.Enum,System.UInt32,System.Enum)">
            <inheritdoc />
        </member>
        <member name="M:CTrue.FsConnect.FsConnect.TransmitClientEvent(System.Int32,System.UInt32,System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:CTrue.FsConnect.FsConnect.SetText(System.String,System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:CTrue.FsConnect.FsConnect.Pause">
            <inheritdoc />
        </member>
        <member name="M:CTrue.FsConnect.FsConnect.Pause(System.Boolean)">
            <inheritdoc />
        </member>
        <member name="M:CTrue.FsConnect.FsConnect.Dispose">
            <summary>
            Disconnects and disposes the client.
            </summary>
        </member>
        <member name="M:CTrue.FsConnect.FsConnect.Dispose(System.Boolean)">
            <summary>
            
            </summary>
            <param name="disposing"></param>
        </member>
        <member name="T:CTrue.FsConnect.FsConnectEnum">
            <summary>
            The <see cref="T:CTrue.FsConnect.FsConnectEnum"/> is a dummy enum used when identifying definitions, events and other SimConnect artifacts that needs to be identified.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsConnectEnum.Base">
            <summary>
            Base enum identifying the first enum id used by FsConnect.
            </summary>
        </member>
        <member name="T:CTrue.FsConnect.FsConnectPeriod">
            <summary>
            Describes how often data should be returned.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsConnectPeriod.Never">
            <summary>
            Specifies that the data is not to be sent.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsConnectPeriod.Once">
            <summary>
            Specifies that the data should be sent once only. Note that this is not an efficient way of receiving data frequently, use one of the other periods if there is a regular frequency to the data request.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsConnectPeriod.VisualFrame">
            <summary>
            Specifies that the data should be sent every visual (rendered) frame.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsConnectPeriod.SimFrame">
            <summary>
            Specifies that the data should be sent every simulated frame, whether that frame is rendered or not.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsConnectPeriod.Second">
            <summary>
            Specifies that the data should be sent once every second.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsConnectDRequestFlag.Default">
            <summary>
            The default, data will be sent strictly according to the defined period.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsConnectDRequestFlag.Changed">
            <summary>
            Data will only be sent to the client when one or more values have changed. If this is the only flag set, then all the variables in a data definition will be returned if just one of the values changes.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsConnectDRequestFlag.Tagged">
            <summary>
            	Requested data will be sent in tagged format (datum ID/value pairs). Tagged format requires that a datum reference ID is returned along with the data value, in order that the client code is able to identify the variable. This flag is usually set in conjunction with the previous flag, but it can be used on its own to return all the values in a data definition in datum ID/value pairs. See the SIMCONNECT_RECV_SIMOBJECT_DATA structure for more details.
            </summary>
        </member>
        <member name="T:CTrue.FsConnect.FsConnectionInfo">
            <summary>
            Contains key information about the connection to Flight Simulator.
            </summary>
        </member>
        <member name="T:CTrue.FsConnect.FsEventNameId">
            <summary>
            The <see cref="T:CTrue.FsConnect.FsEventNameId"/> enum contains all known events.
            </summary>
            <remarks>
            Note: This list is based on known legacy events. Not all events are supported by Microsoft Flight Simulator 2020.
            </remarks>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ThrottleFull">
            <summary>
            Set throttles max
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ThrottleIncr">
            <summary>
            Increment throttles
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ThrottleIncrSmall">
            <summary>
            Increment throttles small
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ThrottleDecr">
            <summary>
            Decrement throttles
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ThrottleDecrSmall">
            <summary>
            Decrease throttles small
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ThrottleCut">
            <summary>
            Set throttles to idle
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.IncreaseThrottle">
            <summary>
            Increment throttles
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.DecreaseThrottle">
            <summary>
            Decrement throttles
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ThrottleSet">
            <summary>
            Set throttles exactly (0- 16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AxisThrottleSet">
            <summary>
            Set throttles (0- 16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Throttle1Set">
            <summary>
            Set throttle 1 exactly (0 to 16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Throttle2Set">
            <summary>
            Set throttle 2 exactly (0 to 16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Throttle3Set">
            <summary>
            Set throttle 3 exactly (0 to 16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Throttle4Set">
            <summary>
            Set throttle 4 exactly (0 to 16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Throttle1Full">
            <summary>
            Set throttle 1 max
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Throttle1Incr">
            <summary>
            Increment throttle 1
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Throttle1IncrSmall">
            <summary>
            Increment throttle 1 small
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Throttle1Decr">
            <summary>
            Decrement throttle 1
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Throttle1Cut">
            <summary>
            Set throttle 1 to idle
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Throttle2Full">
            <summary>
            Set throttle 2 max
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Throttle2Incr">
            <summary>
            Increment throttle 2
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Throttle2IncrSmall">
            <summary>
            Increment throttle 2 small
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Throttle2Decr">
            <summary>
            Decrement throttle 2
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Throttle2Cut">
            <summary>
            Set throttle 2 to idle
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Throttle3Full">
            <summary>
            Set throttle 3 max
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Throttle3Incr">
            <summary>
            Increment throttle 3
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Throttle3IncrSmall">
            <summary>
            Increment throttle 3 small
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Throttle3Decr">
            <summary>
            Decrement throttle 3
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Throttle3Cut">
            <summary>
            Set throttle 3 to idle
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Throttle4Full">
            <summary>
            Set throttle 1 max
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Throttle4Incr">
            <summary>
            Increment throttle 4
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Throttle4IncrSmall">
            <summary>
            Increment throttle 4 small
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Throttle4Decr">
            <summary>
            Decrement throttle 4
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Throttle4Cut">
            <summary>
            Set throttle 4 to idle
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Throttle10">
            <summary>
            Set throttles to 10%
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Throttle20">
            <summary>
            Set throttles to 20%
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Throttle30">
            <summary>
            Set throttles to 30%
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Throttle40">
            <summary>
            Set throttles to 40%
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Throttle50">
            <summary>
            Set throttles to 50%
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Throttle60">
            <summary>
            Set throttles to 60%
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Throttle70">
            <summary>
            Set throttles to 70%
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Throttle80">
            <summary>
            Set throttles to 80%
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Throttle90">
            <summary>
            Set throttles to 90%
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AxisThrottle1Set">
            <summary>
            Set throttle 1 exactly (-16383 - +16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AxisThrottle2Set">
            <summary>
            Set throttle 2 exactly (-16383 - +16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AxisThrottle3Set">
            <summary>
            Set throttle 3 exactly (-16383 - +16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AxisThrottle4Set">
            <summary>
            Set throttle 4 exactly (-16383 - +16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Throttle1DecrSmall">
            <summary>
            Decrease throttle 1 small
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Throttle2DecrSmall">
            <summary>
            Decrease throttle 2 small
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Throttle3DecrSmall">
            <summary>
            Decrease throttle 3 small
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Throttle4DecrSmall">
            <summary>
            Decrease throttle 4 small
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PropPitchDecrSmall">
            <summary>
            Decrease prop levers small
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PropPitch1DecrSmall">
            <summary>
            Decrease prop lever 1 small
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PropPitch2DecrSmall">
            <summary>
            Decrease prop lever 2 small
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PropPitch3DecrSmall">
            <summary>
            Decrease prop lever 3 small
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PropPitch4DecrSmall">
            <summary>
            Decrease prop lever 4 small
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Mixture1Rich">
            <summary>
            Set mixture lever 1 to max rich
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Mixture1Incr">
            <summary>
            Increment mixture lever 1
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Mixture1IncrSmall">
            <summary>
            Increment mixture lever 1 small
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Mixture1Decr">
            <summary>
            Decrement mixture lever 1
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Mixture1Lean">
            <summary>
            Set mixture lever 1 to max lean
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Mixture2Rich">
            <summary>
            Set mixture lever 2 to max rich
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Mixture2Incr">
            <summary>
            Increment mixture lever 2
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Mixture2IncrSmall">
            <summary>
            Increment mixture lever 2 small
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Mixture2Decr">
            <summary>
            Decrement mixture lever 2
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Mixture2Lean">
            <summary>
            Set mixture lever 2 to max lean
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Mixture3Rich">
            <summary>
            Set mixture lever 3 to max rich
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Mixture3Incr">
            <summary>
            Increment mixture lever 3
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Mixture3IncrSmall">
            <summary>
            Increment mixture lever 3 small
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Mixture3Decr">
            <summary>
            Decrement mixture lever 3
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Mixture3Lean">
            <summary>
            Set mixture lever 3 to max lean
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Mixture4Rich">
            <summary>
            Set mixture lever 4 to max rich
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Mixture4Incr">
            <summary>
            Increment mixture lever 4
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Mixture4IncrSmall">
            <summary>
            Increment mixture lever 4 small
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Mixture4Decr">
            <summary>
            Decrement mixture lever 4
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Mixture4Lean">
            <summary>
            Set mixture lever 4 to max lean
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.MixtureSet">
            <summary>
            Set mixture levers to exact value (0 to 16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.MixtureRich">
            <summary>
            Set mixture levers to max rich
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.MixtureIncr">
            <summary>
            Increment mixture levers
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.MixtureIncrSmall">
            <summary>
            Increment mixture levers small
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.MixtureDecr">
            <summary>
            Decrement mixture levers
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.MixtureLean">
            <summary>
            Set mixture levers to max lean
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Mixture1Set">
            <summary>
            Set mixture lever 1 exact value (0 to 16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Mixture2Set">
            <summary>
            Set mixture lever 2 exact value (0 to 16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Mixture3Set">
            <summary>
            Set mixture lever 3 exact value (0 to 16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Mixture4Set">
            <summary>
            Set mixture lever 4 exact value (0 to 16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AxisMixtureSet">
            <summary>
            Set mixture lever 1 exact value (-16383 to +16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AxisMixture1Set">
            <summary>
            Set mixture lever 1 exact value (-16383 to +16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AxisMixture2Set">
            <summary>
            Set mixture lever 2 exact value (-16383 to +16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AxisMixture3Set">
            <summary>
            Set mixture lever 3 exact value (-16383 to +16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AxisMixture4Set">
            <summary>
            Set mixture lever 4 exact value (-16383 to +16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.MixtureSetBest">
            <summary>
            Set mixture levers to current best power setting
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.MixtureDecrSmall">
            <summary>
            Decrement mixture levers small
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Mixture1DecrSmall">
            <summary>
            Decrement mixture lever 1 small
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Mixture2DecrSmall">
            <summary>
            Decrement mixture lever 4 small
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Mixture3DecrSmall">
            <summary>
            Decrement mixture lever 4 small
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Mixture4DecrSmall">
            <summary>
            Decrement mixture lever 4 small
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PropPitchSet">
            <summary>
            Set prop pitch levers (0 to 16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PropPitchLo">
            <summary>
            Set prop pitch levers max (lo pitch)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PropPitchIncr">
            <summary>
            Increment prop pitch levers
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PropPitchIncrSmall">
            <summary>
            Increment prop pitch levers small
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PropPitchDecr">
            <summary>
            Decrement prop pitch levers
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PropPitchHi">
            <summary>
            Set prop pitch levers min (hi pitch)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PropPitch1Set">
            <summary>
            Set prop pitch lever 1 exact value (0 to 16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PropPitch2Set">
            <summary>
            Set prop pitch lever 2 exact value (0 to 16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PropPitch3Set">
            <summary>
            Set prop pitch lever 3 exact value (0 to 16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PropPitch4Set">
            <summary>
            Set prop pitch lever 4 exact value (0 to 16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PropPitch1Lo">
            <summary>
            Set prop pitch lever 1 max (lo pitch)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PropPitch1Incr">
            <summary>
            Increment prop pitch lever 1
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PropPitch1IncrSmall">
            <summary>
            Increment prop pitch lever 1 small
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PropPitch1Decr">
            <summary>
            Decrement prop pitch lever 1
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PropPitch1Hi">
            <summary>
            Set prop pitch lever 1 min (hi pitch)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PropPitch2Lo">
            <summary>
            Set prop pitch lever 2 max (lo pitch)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PropPitch2Incr">
            <summary>
            Increment prop pitch lever 2
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PropPitch2IncrSmall">
            <summary>
            Increment prop pitch lever 2 small
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PropPitch2Decr">
            <summary>
            Decrement prop pitch lever 2
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PropPitch2Hi">
            <summary>
            Set prop pitch lever 2 min (hi pitch)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PropPitch3Lo">
            <summary>
            Set prop pitch lever 3 max (lo pitch)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PropPitch3Incr">
            <summary>
            Increment prop pitch lever 3
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PropPitch3IncrSmall">
            <summary>
            Increment prop pitch lever 3 small
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PropPitch3Decr">
            <summary>
            Decrement prop pitch lever 3
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PropPitch3Hi">
            <summary>
            Set prop pitch lever 3 min (hi pitch)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PropPitch4Lo">
            <summary>
            Set prop pitch lever 4 max (lo pitch)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PropPitch4Incr">
            <summary>
            Increment prop pitch lever 4
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PropPitch4IncrSmall">
            <summary>
            Increment prop pitch lever 4 small
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PropPitch4Decr">
            <summary>
            Decrement prop pitch lever 4
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PropPitch4Hi">
            <summary>
            Set prop pitch lever 4 min (hi pitch)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AxisPropellerSet">
            <summary>
            Set propeller levers exact value (-16383 to +16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AxisPropeller1Set">
            <summary>
            Set propeller lever 1 exact value (-16383 to +16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AxisPropeller2Set">
            <summary>
            Set propeller lever 2 exact value (-16383 to +16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AxisPropeller3Set">
            <summary>
            Set propeller lever 3 exact value (-16383 to +16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AxisPropeller4Set">
            <summary>
            Set propeller lever 4 exact value (-16383 to +16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.JetStarter">
            <summary>
            Selects jet engine starter (for +/- sequence)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.StarterSet">
            <summary>
            Sets magnetos (0,1)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleStarter1">
            <summary>
            Toggle starter 1
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleStarter2">
            <summary>
            Toggle starter 2
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleStarter3">
            <summary>
            Toggle starter 3
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleStarter4">
            <summary>
            Toggle starter 4
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleAllStarters">
            <summary>
            Toggle starters
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.EngineAutoStart">
            <summary>
            Triggers auto-start
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.EngineAutoShutdown">
            <summary>
            Triggers auto-shutdown
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Magneto">
            <summary>
            Selects magnetos (for +/- sequence)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.MagnetoDecr">
            <summary>
            Decrease magneto switches positions
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.MagnetoIncr">
            <summary>
            Increase magneto switches positions
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Magneto1Off">
            <summary>
            Set engine 1 magnetos off
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Magneto1Right">
            <summary>
            Toggle engine 1 right magneto
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Magneto1Left">
            <summary>
            Toggle engine 1 left magneto
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Magneto1Both">
            <summary>
            Set engine 1 magnetos on
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Magneto1Start">
            <summary>
            Set engine 1 magnetos on and toggle starter
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Magneto2Off">
            <summary>
            Set engine 2 magnetos off
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Magneto2Right">
            <summary>
            Toggle engine 2 right magneto
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Magneto2Left">
            <summary>
            Toggle engine 2 left magneto
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Magneto2Both">
            <summary>
            Set engine 2 magnetos on
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Magneto2Start">
            <summary>
            Set engine 2 magnetos on and toggle starter
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Magneto3Off">
            <summary>
            Set engine 3 magnetos off
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Magneto3Right">
            <summary>
            Toggle engine 3 right magneto
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Magneto3Left">
            <summary>
            Toggle engine 3 left magneto
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Magneto3Both">
            <summary>
            Set engine 3 magnetos on
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Magneto3Start">
            <summary>
            Set engine 3 magnetos on and toggle starter
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Magneto4Off">
            <summary>
            Set engine 4 magnetos off
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Magneto4Right">
            <summary>
            Toggle engine 4 right magneto
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Magneto4Left">
            <summary>
            Toggle engine 4 left magneto
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Magneto4Both">
            <summary>
            Set engine 4 magnetos on
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Magneto4Start">
            <summary>
            Set engine 4 magnetos on and toggle starter
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.MagnetoOff">
            <summary>
            Set engine magnetos off
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.MagnetoRight">
            <summary>
            Set engine right magnetos on
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.MagnetoLeft">
            <summary>
            Set engine left magnetos on
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.MagnetoBoth">
            <summary>
            Set engine magnetos on
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.MagnetoStart">
            <summary>
            Set engine magnetos on and toggle starters
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Magneto1Decr">
            <summary>
            Decrease engine 1 magneto switch position
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Magneto1Incr">
            <summary>
            Increase engine 1 magneto switch position
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Magneto2Decr">
            <summary>
            Decrease engine 2 magneto switch position
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Magneto2Incr">
            <summary>
            Increase engine 2 magneto switch position
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Magneto3Decr">
            <summary>
            Decrease engine 3 magneto switch position
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Magneto3Incr">
            <summary>
            Increase engine 3 magneto switch position
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Magneto4Decr">
            <summary>
            Decrease engine 4 magneto switch position
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Magneto4Incr">
            <summary>
            Increase engine 4 magneto switch position
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Magneto1Set">
            <summary>
            Set engine 1 magneto switch
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Magneto2Set">
            <summary>
            Set engine 2 magneto switch
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Magneto3Set">
            <summary>
            Set engine 3 magneto switch
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Magneto4Set">
            <summary>
            Set engine 4 magneto switch
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AntiIceOn">
            <summary>
            Sets anti-ice switches on
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AntiIceOff">
            <summary>
            Sets anti-ice switches off
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AntiIceSet">
            <summary>
            Sets anti-ice switches from argument (0,1)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AntiIceToggle">
            <summary>
            Toggle anti-ice switches
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AntiIceToggleEng1">
            <summary>
            Toggle engine 1 anti-ice switch
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AntiIceToggleEng2">
            <summary>
            Toggle engine 2 anti-ice switch
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AntiIceToggleEng3">
            <summary>
            Toggle engine 3 anti-ice switch
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AntiIceToggleEng4">
            <summary>
            Toggle engine 4 anti-ice switch
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AntiIceSetEng1">
            <summary>
            Sets engine 1 anti-ice switch (0,1)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AntiIceSetEng2">
            <summary>
            Sets engine 2 anti-ice switch (0,1)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AntiIceSetEng3">
            <summary>
            Sets engine 3 anti-ice switch (0,1)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AntiIceSetEng4">
            <summary>
            Sets engine 4 anti-ice switch (0,1)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleFuelValveAll">
            <summary>
            Toggle engine fuel valves
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleFuelValveEng1">
            <summary>
            Toggle engine 1 fuel valve
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleFuelValveEng2">
            <summary>
            Toggle engine 2 fuel valve
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleFuelValveEng3">
            <summary>
            Toggle engine 3 fuel valve
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleFuelValveEng4">
            <summary>
            Toggle engine 4 fuel valve
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Cowlflap1Set">
            <summary>
            Sets engine 1 cowl flap lever position (0 to 16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Cowlflap2Set">
            <summary>
            Sets engine 2 cowl flap lever position (0 to 16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Cowlflap3Set">
            <summary>
            Sets engine 3 cowl flap lever position (0 to 16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Cowlflap4Set">
            <summary>
            Sets engine 4 cowl flap lever position (0 to 16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.IncCowlFlaps">
            <summary>
            Increment cowl flap levers
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.DecCowlFlaps">
            <summary>
            Decrement cowl flap levers
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.IncCowlFlaps1">
            <summary>
            Increment engine 1 cowl flap lever
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.DecCowlFlaps1">
            <summary>
            Decrement engine 1 cowl flap lever
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.IncCowlFlaps2">
            <summary>
            Increment engine 2 cowl flap lever
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.DecCowlFlaps2">
            <summary>
            Decrement engine 2 cowl flap lever
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.IncCowlFlaps3">
            <summary>
            Increment engine 3 cowl flap lever
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.DecCowlFlaps3">
            <summary>
            Decrement engine 3 cowl flap lever
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.IncCowlFlaps4">
            <summary>
            Increment engine 4 cowl flap lever
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.DecCowlFlaps4">
            <summary>
            Decrement engine 4 cowl flap lever
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FuelPump">
            <summary>
            Toggle electric fuel pumps
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleElectFuelPump">
            <summary>
            Toggle electric fuel pumps
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleElectFuelPump1">
            <summary>
            Toggle engine 1 electric fuel pump
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleElectFuelPump2">
            <summary>
            Toggle engine 2 electric fuel pump
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleElectFuelPump3">
            <summary>
            Toggle engine 3 electric fuel pump
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleElectFuelPump4">
            <summary>
            Toggle engine 4 electric fuel pump
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.EnginePrimer">
            <summary>
            Trigger engine primers
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.TogglePrimer">
            <summary>
            Trigger engine primers
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.TogglePrimer1">
            <summary>
            Trigger engine 1 primer
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.TogglePrimer2">
            <summary>
            Trigger engine 2 primer
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.TogglePrimer3">
            <summary>
            Trigger engine 3 primer
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.TogglePrimer4">
            <summary>
            Trigger engine 4 primer
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleFeatherSwitches">
            <summary>
            Trigger propeller switches
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleFeatherSwitch1">
            <summary>
            Trigger propeller 1 switch
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleFeatherSwitch2">
            <summary>
            Trigger propeller 2 switch
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleFeatherSwitch3">
            <summary>
            Trigger propeller 3 switch
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleFeatherSwitch4">
            <summary>
            Trigger propeller 4 switch
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.TogglePropSync">
            <summary>
            Turns propeller synchronization switch on
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleArmAutofeather">
            <summary>
            Turns auto-feather arming switch on.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleAfterburner">
            <summary>
            Toggles afterburners
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleAfterburner1">
            <summary>
            Toggles engine 1 afterburner
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleAfterburner2">
            <summary>
            Toggles engine 2 afterburner
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleAfterburner3">
            <summary>
            Toggles engine 3 afterburner
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleAfterburner4">
            <summary>
            Toggles engine 4 afterburner
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Engine">
            <summary>
            Sets engines for 1,2,3,4 selection (to be followed by SELECT_n)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SpoilersToggle">
            <summary>
            Toggles spoiler handle
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FlapsUp">
            <summary>
            Sets flap handle to full retract position
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Flaps1">
            <summary>
            Sets flap handle to first extension position
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Flaps2">
            <summary>
            Sets flap handle to second extension position
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Flaps3">
            <summary>
            Sets flap handle to third extension position
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FlapsDown">
            <summary>
            Sets flap handle to full extension position
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ElevTrimDn">
            <summary>
            Increments elevator trim down
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ElevDown">
            <summary>
            Increments elevator down
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AileronsLeft">
            <summary>
            Increments ailerons left
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.CenterAilerRudder">
            <summary>
            Centers aileron and rudder positions
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AileronsRight">
            <summary>
            Increments ailerons right
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ElevTrimUp">
            <summary>
            Increment elevator trim up
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ElevUp">
            <summary>
            Increments elevator up
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.RudderLeft">
            <summary>
            Increments rudder left
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.RudderCenter">
            <summary>
            Centers rudder position
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.RudderRight">
            <summary>
            Increments rudder right
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ElevatorSet">
            <summary>
            Sets elevator position (-16383 - +16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AileronSet">
            <summary>
            Sets aileron position (-16383 - +16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.RudderSet">
            <summary>
            Sets rudder position (-16383 - +16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FlapsIncr">
            <summary>
            Increments flap handle position
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FlapsDecr">
            <summary>
            Decrements flap handle position
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AxisElevatorSet">
            <summary>
            Sets elevator position (-16383 - +16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AxisAileronsSet">
            <summary>
            Sets aileron position (-16383 - +16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AxisRudderSet">
            <summary>
            Sets rudder position (-16383 - +16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AxisElevTrimSet">
            <summary>
            Sets elevator trim position (-16383 - +16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SpoilersSet">
            <summary>
            Sets spoiler handle position (0 to 16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SpoilersArmToggle">
            <summary>
            Toggles arming of auto-spoilers
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SpoilersOn">
            <summary>
            Sets spoiler handle to full extend position
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SpoilersOff">
            <summary>
            Sets spoiler handle to full retract position
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SpoilersArmOn">
            <summary>
            Sets auto-spoiler arming on
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SpoilersArmOff">
            <summary>
            Sets auto-spoiler arming off
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SpoilersArmSet">
            <summary>
            Sets auto-spoiler arming (0,1)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AileronTrimLeft">
            <summary>
            Increments aileron trim left
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AileronTrimRight">
            <summary>
            Increments aileron trim right
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.RudderTrimLeft">
            <summary>
            Increments rudder trim left
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.RudderTrimRight">
            <summary>
            Increments aileron trim right
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AxisSpoilerSet">
            <summary>
            Sets spoiler handle position (-16383 - +16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FlapsSet">
            <summary>
            Sets flap handle to closest increment (0 to 16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ElevatorTrimSet">
            <summary>
            Sets elevator trim position (0 to 16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AxisFlapsSet">
            <summary>
            Sets flap handle to closest increment (-16383 - +16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApMaster">
            <summary>
            Toggles AP on/off
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AutopilotOff">
            <summary>
            Turns AP off
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AutopilotOn">
            <summary>
            Turns AP on
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.YawDamperToggle">
            <summary>
            Toggles yaw damper on/off
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApPanelHeadingHold">
            <summary>
            Toggles heading hold mode on/off
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApPanelAltitudeHold">
            <summary>
            Toggles altitude hold mode on/off
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApAttHoldOn">
            <summary>
            Turns on AP wing leveler and pitch hold mode
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApLocHoldOn">
            <summary>
            Turns AP localizer hold on/armed and glide-slope hold mode off
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApAprHoldOn">
            <summary>
            Turns both AP localizer and glide-slope modes on/armed
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApHdgHoldOn">
            <summary>
            Turns heading hold mode on
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApAltHoldOn">
            <summary>
            Turns altitude hold mode on
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApWingLevelerOn">
            <summary>
            Turns wing leveler mode on
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApBcHoldOn">
            <summary>
            Turns localizer back course hold mode on/armed
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApNav1HoldOn">
            <summary>
            Turns lateral hold mode on
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApAttHoldOff">
            <summary>
            Turns off attitude hold mode
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApLocHoldOff">
            <summary>
            Turns off localizer hold mode
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApAprHoldOff">
            <summary>
            Turns off approach hold mode
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApHdgHoldOff">
            <summary>
            Turns off heading hold mode
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApAltHoldOff">
            <summary>
            Turns off altitude hold mode
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApWingLevelerOff">
            <summary>
            Turns off wing leveler mode
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApBcHoldOff">
            <summary>
            Turns off backcourse mode for localizer hold
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApNav1HoldOff">
            <summary>
            Turns off nav hold mode
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApAirspeedHold">
            <summary>
            Toggles airspeed hold mode
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AutoThrottleArm">
            <summary>
            Toggles autothrottle arming mode
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AutoThrottleToGa">
            <summary>
            Toggles Takeoff/Go Around mode
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.HeadingBugInc">
            <summary>
            Increments heading hold reference bug
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.HeadingBugDec">
            <summary>
            Decrements heading hold reference bug
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.HeadingBugSet">
            <summary>
            Set heading hold reference bug (degrees)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApPanelSpeedHold">
            <summary>
            Toggles airspeed hold mode
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApAltVarInc">
            <summary>
            Increments reference altitude
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApAltVarDec">
            <summary>
            Decrements reference altitude
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApVsVarInc">
            <summary>
            Increments vertical speed reference
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApVsVarDec">
            <summary>
            Decrements vertical speed reference
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApSpdVarInc">
            <summary>
            Increments airspeed hold reference
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApSpdVarDec">
            <summary>
            Decrements airspeed hold reference
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApPanelMachHold">
            <summary>
            Toggles mach hold
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApMachVarInc">
            <summary>
            Increments reference mach
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApMachVarDec">
            <summary>
            Decrements reference mach
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApMachHold">
            <summary>
            Toggles mach hold
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApAltVarSetMetric">
            <summary>
            Sets reference altitude in meters
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApVsVarSetEnglish">
            <summary>
            Sets reference vertical speed in feet per minute
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApSpdVarSet">
            <summary>
            Sets airspeed reference in knots
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApMachVarSet">
            <summary>
            Sets mach reference
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.YawDamperOn">
            <summary>
            Turns yaw damper on
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.YawDamperOff">
            <summary>
            Turns yaw damper off
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.YawDamperSet">
            <summary>
            Sets yaw damper on/off (1,0)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApAirspeedOn">
            <summary>
            Turns airspeed hold on
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApAirspeedOff">
            <summary>
            Turns airspeed hold off
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApAirspeedSet">
            <summary>
            Sets airspeed hold on/off (1,0)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApMachOn">
            <summary>
            Turns mach hold on
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApMachOff">
            <summary>
            Turns mach hold off
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApMachSet">
            <summary>
            Sets mach hold on/off (1,0)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApPanelAltitudeOn">
            <summary>
            Turns altitude hold mode on (without capturing current altitude)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApPanelAltitudeOff">
            <summary>
            Turns altitude hold mode off
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApPanelAltitudeSet">
            <summary>
            Sets altitude hold mode on/off (1,0)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApPanelHeadingOn">
            <summary>
            Turns heading mode on (without capturing current heading)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApPanelHeadingOff">
            <summary>
            Turns heading mode off
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApPanelHeadingSet">
            <summary>
            Set heading mode on/off (1,0)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApPanelMachOn">
            <summary>
            Turns on mach hold
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApPanelMachOff">
            <summary>
            Turns off mach hold
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApPanelMachSet">
            <summary>
            Sets mach hold on/off (1,0)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApPanelSpeedOn">
            <summary>
            Turns on speed hold mode
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApPanelSpeedOff">
            <summary>
            Turns off speed hold mode
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApPanelSpeedSet">
            <summary>
            Set speed hold mode on/off (1,0)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApAltVarSetEnglish">
            <summary>
            Sets altitude reference in feet
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApVsVarSetMetric">
            <summary>
            Sets vertical speed reference in meters per minute
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleFlightDirector">
            <summary>
            Toggles flight director on/off
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SyncFlightDirectorPitch">
            <summary>
            Synchronizes flight director pitch with current aircraft pitch
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.IncAutobrakeControl">
            <summary>
            Increments autobrake level
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.DecAutobrakeControl">
            <summary>
            Decrements autobrake level
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AutopilotAirspeedHoldCurrent">
            <summary>
            Turns airspeed hold mode on with current airspeed
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AutopilotMachHoldCurrent">
            <summary>
            Sets mach hold reference to current mach
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApNavSelectSet">
            <summary>
            Sets the nav (1 or 2) which is used by the Nav hold modes
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.HeadingBugSelect">
            <summary>
            Selects the heading bug for use with +/-
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AltitudeBugSelect">
            <summary>
            Selects the altitude reference for use with +/-
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.VsiBugSelect">
            <summary>
            Selects the vertical speed reference for use with +/-
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AirspeedBugSelect">
            <summary>
            Selects the airspeed reference for use with +/-
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApPitchRefIncUp">
            <summary>
            Increments the pitch reference for pitch hold mode
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApPitchRefIncDn">
            <summary>
            Decrements the pitch reference for pitch hold mode
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApPitchRefSelect">
            <summary>
            Selects pitch reference for use with +/-
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApAttHold">
            <summary>
            Toggle attitude hold mode
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApLocHold">
            <summary>
            Toggles localizer (only) hold mode
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApAprHold">
            <summary>
            Toggles approach hold (localizer and glide-slope)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApHdgHold">
            <summary>
            Toggles heading hold mode
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApAltHold">
            <summary>
            Toggles altitude hold mode
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApWingLeveler">
            <summary>
            Toggles wing leveler mode
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApBcHold">
            <summary>
            Toggles the backcourse mode for the localizer hold
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApNav1Hold">
            <summary>
            Toggles the nav hold mode
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApMaxBankInc">
            <summary>
            Autopilot max bank angle increment.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApMaxBankDec">
            <summary>
            Autopilot max bank angle decrement.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApN1Hold">
            <summary>
            Autopilot, hold the N1 percentage at its current level.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApN1RefInc">
            <summary>
            Increment the autopilot N1 reference.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApN1RefDec">
            <summary>
            Decrement the autopilot N1 reference.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApN1RefSet">
            <summary>
            Sets the autopilot N1 reference.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FlyByWireElacToggle">
            <summary>
            Turn on or off the fly by wire Elevators and Ailerons computer.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FlyByWireFacToggle">
            <summary>
            Turn on or off the fly by wire Flight Augmentation computer.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FlyByWireSecToggle">
            <summary>
            Turn on or off the fly by wire Spoilers and Elevators computer.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.G1000PfdFlightplanButton">
            <summary>
            The primary flight display (PFD) should display its current flight plan.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.G1000PfdProcedureButton">
            <summary>
            Turn to the Procedure page.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.G1000PfdZoominButton">
            <summary>
            Zoom in on the current map.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.G1000PfdZoomoutButton">
            <summary>
            Zoom out on the current map.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.G1000PfdDirecttoButton">
            <summary>
            Turn to the Direct To page.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.G1000PfdMenuButton">
            <summary>
            If a segmented flight plan is highlighted, activates the associated menu.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.G1000PfdClearButton">
            <summary>
            Clears the current input.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.G1000PfdEnterButton">
            <summary>
            Enters the current input.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.G1000PfdCursorButton">
            <summary>
            Turns on or off a screen cursor.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.G1000PfdGroupKnobInc">
            <summary>
            Step up through the page groups.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.G1000PfdGroupKnobDec">
            <summary>
            Step down through the page groups.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.G1000PfdPageKnobInc">
            <summary>
            Step up through the individual pages.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.G1000PfdPageKnobDec">
            <summary>
            Step down through the individual pages.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.G1000PfdSoftkey1">
            <summary>
            Initiate the action for the icon displayed in the softkey position.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.G1000MfdFlightplanButton">
            <summary>
            The multi-function display (MFD) should display its current flight plan.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.G1000MfdProcedureButton">
            <summary>
            Turn to the Procedure page.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.G1000MfdZoominButton">
            <summary>
            Zoom in on the current map.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.G1000MfdZoomoutButton">
            <summary>
            Zoom out on the current map.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.G1000MfdDirecttoButton">
            <summary>
            Turn to the Direct To page.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.G1000MfdMenuButton">
            <summary>
            If a segmented flight plan is highlighted, activates the associated menu.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.G1000MfdClearButton">
            <summary>
            Clears the current input.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.G1000MfdEnterButton">
            <summary>
            Enters the current input.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.G1000MfdCursorButton">
            <summary>
            Turns on or off a screen cursor.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.G1000MfdGroupKnobInc">
            <summary>
            Step up through the page groups.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.G1000MfdGroupKnobDec">
            <summary>
            Step down through the page groups.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.G1000MfdPageKnobInc">
            <summary>
            Step up through the individual pages.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.G1000MfdPageKnobDec">
            <summary>
            Step down through the individual pages.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.G1000MfdSoftkey1">
            <summary>
            Initiate the action for the icon displayed in the softkey position.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FuelSelectorOff">
            <summary>
            Turns selector 1 to OFF position
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FuelSelectorAll">
            <summary>
            Turns selector 1 to ALL position
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FuelSelectorLeft">
            <summary>
            Turns selector 1 to LEFT position (burns from tip then aux then main)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FuelSelectorRight">
            <summary>
            Turns selector 1 to RIGHT position (burns from tip then aux then main)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FuelSelectorLeftAux">
            <summary>
            Turns selector 1 to LEFT AUX position
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FuelSelectorRightAux">
            <summary>
            Turns selector 1 to RIGHT AUX position
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FuelSelectorCenter">
            <summary>
            Turns selector 1 to CENTER position
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FuelSelectorSet">
            <summary>
            Sets selector 1 position (see code list below)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FuelSelector2Off">
            <summary>
            Turns selector 2 to OFF position
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FuelSelector2All">
            <summary>
            Turns selector 2 to ALL position
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FuelSelector2Left">
            <summary>
            Turns selector 2 to LEFT position (burns from tip then aux then main)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FuelSelector2Right">
            <summary>
            Turns selector 2 to RIGHT position (burns from tip then aux then main)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FuelSelector2LeftAux">
            <summary>
            Turns selector 2 to LEFT AUX position
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FuelSelector2RightAux">
            <summary>
            Turns selector 2 to RIGHT AUX position
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FuelSelector2Center">
            <summary>
            Turns selector 2 to CENTER position
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FuelSelector2Set">
            <summary>
            Sets selector 2 position (see code list below)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FuelSelector3Off">
            <summary>
            Turns selector 3 to OFF position
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FuelSelector3All">
            <summary>
            Turns selector 3 to ALL position
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FuelSelector3Left">
            <summary>
            Turns selector 3 to LEFT position (burns from tip then aux then main)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FuelSelector3Right">
            <summary>
            Turns selector 3 to RIGHT position (burns from tip then aux then main)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FuelSelector3LeftAux">
            <summary>
            Turns selector 3 to LEFT AUX position
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FuelSelector3RightAux">
            <summary>
            Turns selector 3 to RIGHT AUX position
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FuelSelector3Center">
            <summary>
            Turns selector 3 to CENTER position
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FuelSelector3Set">
            <summary>
            Sets selector 3 position (see code list below)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FuelSelector4Off">
            <summary>
            Turns selector 4 to OFF position
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FuelSelector4All">
            <summary>
            Turns selector 4 to ALL position
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FuelSelector4Left">
            <summary>
            Turns selector 4 to LEFT position (burns from tip then aux then main)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FuelSelector4Right">
            <summary>
            Turns selector 4 to RIGHT position (burns from tip then aux then main)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FuelSelector4LeftAux">
            <summary>
            Turns selector 4 to LEFT AUX position
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FuelSelector4RightAux">
            <summary>
            Turns selector 4 to RIGHT AUX position
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FuelSelector4Center">
            <summary>
            Turns selector 4 to CENTER position
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FuelSelector4Set">
            <summary>
            Sets selector 4 position (see code list below)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.CrossFeedOpen">
            <summary>
            "Opens cross feed valve (when used in conjunction with ""isolate"" tank)"
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.CrossFeedToggle">
            <summary>
            "Toggles crossfeed valve (when used in conjunction with ""isolate"" tank)"
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.CrossFeedOff">
            <summary>
            "Closes crossfeed valve (when used in conjunction with ""isolate"" tank)"
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FuelDumpSwitchSet">
            <summary>
            Set to True or False. The switch can only be set to True if fuel_dump_rate is specified in the aircraft configuration file, which indicates that a fuel dump system exists.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleAntidetonationTankValve">
            <summary>
            Toggle the anti-detonation valve. Pass a value to determine which tank, if there are multiple tanks, to use. Tanks are indexed from 1. Refer to the document Notes on Aircraft Systems.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleNitrousTankValve">
            <summary>
            Toggle the nitrous valve. Pass a value to determine which tank, if there are multiple tanks, to use. Tanks are indexed from 1.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.RepairAndRefuel">
            <summary>
            Fully repair and refuel the user aircraft. Ignored if flight realism is enforced.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FuelDumpToggle">
            <summary>
            Turns on or off the fuel dump switch.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.RequestFuel">
            <summary>
            Request a fuel truck. The aircraft must be in a parking spot for this to be successful.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FuelSelectorLeftMain">
            <summary>
            Sets the fuel selector. Fuel will be taken in the order left tip, left aux,then main fuel tanks.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FuelSelector2LeftMain">
            <summary>
            Sets the fuel selector for engine 2.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FuelSelector3LeftMain">
            <summary>
            Sets the fuel selector for engine 3.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FuelSelector4LeftMain">
            <summary>
            Sets the fuel selector for engine 4.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FuelSelectorRightMain">
            <summary>
            Sets the fuel selector. Fuel will be taken in the order right tip, right aux,then main fuel tanks.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FuelSelector2RightMain">
            <summary>
            Sets the fuel selector for engine 2.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FuelSelector3RightMain">
            <summary>
            Sets the fuel selector for engine 3.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FuelSelector4RightMain">
            <summary>
            Sets the fuel selector for engine 4.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Xpndr">
            <summary>
            Sequentially selects the transponder digits for use with +/-.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Adf">
            <summary>
            Sequentially selects the ADF tuner digits for use with +/-. Follow byKEY_SELECT_2 for ADF 2.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Dme">
            <summary>
            Selects the DME for use with +/-
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ComRadio">
            <summary>
            Sequentially selects the COM tuner digits for use with +/-. Follow byKEY_SELECT_2 for COM 2.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.VorObs">
            <summary>
            Sequentially selects the VOR OBS for use with +/-. Follow by KEY_SELECT_2 for VOR 2.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.NavRadio">
            <summary>
            Sequentially selects the NAV tuner digits for use with +/-. Follow byKEY_SELECT_2 for NAV 2.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ComRadioWholeDec">
            <summary>
            Decrements COM by one MHz
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ComRadioWholeInc">
            <summary>
            Increments COM by one MHz
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ComRadioFractDec">
            <summary>
            Decrements COM by 25 KHz
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ComRadioFractInc">
            <summary>
            Increments COM by 25 KHz
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Nav1RadioWholeDec">
            <summary>
            Decrements Nav 1 by one MHz
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Nav1RadioWholeInc">
            <summary>
            Increments Nav 1 by one MHz
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Nav1RadioFractDec">
            <summary>
            Decrements Nav 1 by 25 KHz
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Nav1RadioFractInc">
            <summary>
            Increments Nav 1 by 25 KHz
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Nav2RadioWholeDec">
            <summary>
            Decrements Nav 2 by one MHz
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Nav2RadioWholeInc">
            <summary>
            Increments Nav 2 by one MHz
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Nav2RadioFractDec">
            <summary>
            Decrements Nav 2 by 25 KHz
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Nav2RadioFractInc">
            <summary>
            Increments Nav 2 by 25 KHz
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Adf100Inc">
            <summary>
            Increments ADF by 100 KHz
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Adf10Inc">
            <summary>
            Increments ADF by 10 KHz
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Adf1Inc">
            <summary>
            Increments ADF by 1 KHz
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Xpndr1000Inc">
            <summary>
            Increments first digit of transponder
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Xpndr100Inc">
            <summary>
            Increments second digit of transponder
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Xpndr10Inc">
            <summary>
            Increments third digit of transponder
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Xpndr1Inc">
            <summary>
            Increments fourth digit of transponder
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Vor1ObiDec">
            <summary>
            Decrements the VOR 1 OBS setting
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Vor1ObiInc">
            <summary>
            Increments the VOR 1 OBS setting
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Vor2ObiDec">
            <summary>
            Decrements the VOR 2 OBS setting
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Vor2ObiInc">
            <summary>
            Increments the VOR 2 OBS setting
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Adf100Dec">
            <summary>
            Decrements ADF by 100 KHz
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Adf10Dec">
            <summary>
            Decrements ADF by 10 KHz
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Adf1Dec">
            <summary>
            Decrements ADF by 1 KHz
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ComRadioSet">
            <summary>
            Sets COM frequency (BCD Hz)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Nav1RadioSet">
            <summary>
            Sets NAV 1 frequency (BCD Hz)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Nav2RadioSet">
            <summary>
            Sets NAV 2 frequency (BCD Hz)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AdfSet">
            <summary>
            Sets ADF frequency (BCD Hz)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.XpndrSet">
            <summary>
            Sets transponder code (BCD)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Vor1Set">
            <summary>
            Sets OBS 1 (0 to 360)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Vor2Set">
            <summary>
            Sets OBS 2 (0 to 360)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Dme1Toggle">
            <summary>
            Sets DME display to Nav 1
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Dme2Toggle">
            <summary>
            Sets DME display to Nav 2
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.RadioVor1IdentDisable">
            <summary>
            Turns NAV 1 ID off
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.RadioVor2IdentDisable">
            <summary>
            Turns NAV 2 ID off
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.RadioDme1IdentDisable">
            <summary>
            Turns DME 1 ID off
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.RadioDme2IdentDisable">
            <summary>
            Turns DME 2 ID off
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.RadioAdfIdentDisable">
            <summary>
            Turns ADF 1 ID off
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.RadioVor1IdentEnable">
            <summary>
            Turns NAV 1 ID on
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.RadioVor2IdentEnable">
            <summary>
            Turns NAV 2 ID on
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.RadioDme1IdentEnable">
            <summary>
            Turns DME 1 ID on
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.RadioDme2IdentEnable">
            <summary>
            Turns DME 2 ID on
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.RadioAdfIdentEnable">
            <summary>
            Turns ADF 1 ID on
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.RadioVor1IdentToggle">
            <summary>
            Toggles NAV 1 ID
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.RadioVor2IdentToggle">
            <summary>
            Toggles NAV 2 ID
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.RadioDme1IdentToggle">
            <summary>
            Toggles DME 1 ID
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.RadioDme2IdentToggle">
            <summary>
            Toggles DME 2 ID
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.RadioAdfIdentToggle">
            <summary>
            Toggles ADF 1 ID
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.RadioVor1IdentSet">
            <summary>
            Sets NAV 1 ID (on/off)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.RadioVor2IdentSet">
            <summary>
            Sets NAV 2 ID (on/off)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.RadioDme1IdentSet">
            <summary>
            Sets DME 1 ID (on/off)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.RadioDme2IdentSet">
            <summary>
            Sets DME 2 ID (on/off)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.RadioAdfIdentSet">
            <summary>
            Sets ADF 1 ID (on/off)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AdfCardInc">
            <summary>
            Increments ADF card
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AdfCardDec">
            <summary>
            Decrements ADF card
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AdfCardSet">
            <summary>
            Sets ADF card (0-360)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.DmeToggle">
            <summary>
            Toggles between NAV 1 and NAV 2
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AvionicsMasterSet">
            <summary>
            Sets the avionics master switch
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleAvionicsMaster">
            <summary>
            Toggles the avionics master switch
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ComStbyRadioSet">
            <summary>
            Sets COM 1 standby frequency (BCD Hz)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ComStbyRadioSwitchTo">
            <summary>
            Swaps COM 1 frequency with standby
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ComRadioFractDecCarry">
            <summary>
            Decrement COM 1 frequency by 25 KHz, and carry when digit wraps
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ComRadioFractIncCarry">
            <summary>
            Increment COM 1 frequency by 25 KHz, and carry when digit wraps
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Com2RadioWholeDec">
            <summary>
            Decrement COM 2 frequency by 1 MHz, with no carry when digit wraps
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Com2RadioWholeInc">
            <summary>
            Increment COM 2 frequency by 1 MHz, with no carry when digit wraps
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Com2RadioFractDec">
            <summary>
            Decrement COM 2 frequency by 25 KHz, with no carry when digit wraps
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Com2RadioFractDecCarry">
            <summary>
            Decrement COM 2 frequency by 25 KHz, and carry when digit wraps
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Com2RadioFractInc">
            <summary>
            Increment COM 2 frequency by 25 KHz, with no carry when digit wraps
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Com2RadioFractIncCarry">
            <summary>
            Increment COM 2 frequency by 25 KHz, and carry when digit wraps
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Com2RadioSet">
            <summary>
            Sets COM 2 frequency (BCD Hz)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Com2StbyRadioSet">
            <summary>
            Sets COM 2 standby frequency (BCD Hz)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Com2RadioSwap">
            <summary>
            Swaps COM 2 frequency with standby
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Nav1RadioFractDecCarry">
            <summary>
            Decrement NAV 1 frequency by 50 KHz, and carry when digit wraps
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Nav1RadioFractIncCarry">
            <summary>
            Increment NAV 1 frequency by 50 KHz, and carry when digit wraps
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Nav1StbySet">
            <summary>
            Sets NAV 1 standby frequency (BCD Hz)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Nav1RadioSwap">
            <summary>
            Swaps NAV 1 frequency with standby
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Nav2RadioFractDecCarry">
            <summary>
            Decrement NAV 2 frequency by 50 KHz, and carry when digit wraps
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Nav2RadioFractIncCarry">
            <summary>
            Increment NAV 2 frequency by 50 KHz, and carry when digit wraps
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Nav2StbySet">
            <summary>
            Sets NAV 2 standby frequency (BCD Hz)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Nav2RadioSwap">
            <summary>
            Swaps NAV 2 frequency with standby
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Adf1RadioTenthsDec">
            <summary>
            Decrements ADF 1 by 0.1 KHz.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Adf1RadioTenthsInc">
            <summary>
            Increments ADF 1 by 0.1 KHz.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Xpndr1000Dec">
            <summary>
            Decrements first digit of transponder
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Xpndr100Dec">
            <summary>
            Decrements second digit of transponder
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Xpndr10Dec">
            <summary>
            Decrements third digit of transponder
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Xpndr1Dec">
            <summary>
            Decrements fourth digit of transponder
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.XpndrDecCarry">
            <summary>
            Decrements fourth digit of transponder, and with carry.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.XpndrIncCarry">
            <summary>
            Increments fourth digit of transponder, and with carry.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AdfFractDecCarry">
            <summary>
            Decrements ADF 1 frequency by 0.1 KHz, with carry
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AdfFractIncCarry">
            <summary>
            Increments ADF 1 frequency by 0.1 KHz, with carry
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Com1TransmitSelect">
            <summary>
            Selects COM 1 to transmit
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Com2TransmitSelect">
            <summary>
            Selects COM 2 to transmit
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ComReceiveAllToggle">
            <summary>
            Toggles all COM radios to receive on
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ComReceiveAllSet">
            <summary>
            Sets whether to receive on all COM radios (1,0)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.MarkerSoundToggle">
            <summary>
            Toggles marker beacon sound on/off
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AdfCompleteSet">
            <summary>
            Sets ADF 1 frequency (BCD Hz)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AdfWholeInc">
            <summary>
            Increments ADF 1 by 1 KHz, with carry as digits wrap.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AdfWholeDec">
            <summary>
            Decrements ADF 1 by 1 KHz, with carry as digits wrap.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Adf2100Inc">
            <summary>
            Increments the ADF 2 frequency 100 digit, with wrapping
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Adf210Inc">
            <summary>
            Increments the ADF 2 frequency 10 digit, with wrapping
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Adf21Inc">
            <summary>
            Increments the ADF 2 frequency 1 digit, with wrapping
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Adf2RadioTenthsInc">
            <summary>
            Increments ADF 2 frequency 1/10 digit, with wrapping
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Adf2100Dec">
            <summary>
            Decrements the ADF 2 frequency 100 digit, with wrapping
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Adf210Dec">
            <summary>
            Decrements the ADF 2 frequency 10 digit, with wrapping
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Adf21Dec">
            <summary>
            Decrements the ADF 2 frequency 1 digit, with wrapping
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Adf2RadioTenthsDec">
            <summary>
            Decrements ADF 2 frequency 1/10 digit, with wrapping
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Adf2WholeInc">
            <summary>
            Increments ADF 2 by 1 KHz, with carry as digits wrap.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Adf2WholeDec">
            <summary>
            Decrements ADF 2 by 1 KHz, with carry as digits wrap.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Adf2FractIncCarry">
            <summary>
            Decrements ADF 2 frequency by 0.1 KHz, with carry
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Adf2FractDecCarry">
            <summary>
            Increments ADF 2 frequency by 0.1 KHz, with carry
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Adf2CompleteSet">
            <summary>
            Sets ADF 1 frequency (BCD Hz)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.RadioAdf2IdentDisable">
            <summary>
            Turns ADF 2 ID off
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.RadioAdf2IdentEnable">
            <summary>
            Turns ADF 2 ID on
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.RadioAdf2IdentToggle">
            <summary>
            Toggles ADF 2 ID
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.RadioAdf2IdentSet">
            <summary>
            Sets ADF 2 ID on/off (1,0)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FrequencySwap">
            <summary>
            Swaps frequency with standby on whichever NAV or COM radio is selected.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleGpsDrivesNav1">
            <summary>
            Toggles between GPS and NAV 1 driving NAV 1 OBS display (and AP)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.GpsPowerButton">
            <summary>
            Toggles power button
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.GpsNearestButton">
            <summary>
            Selects Nearest Airport Page
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.GpsObsButton">
            <summary>
            Toggles automatic sequencing of waypoints
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.GpsMsgButton">
            <summary>
            Toggles the Message Page
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.GpsMsgButtonDown">
            <summary>
            Triggers the pressing of the message button.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.GpsMsgButtonUp">
            <summary>
            Triggers the release of the message button
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.GpsFlightplanButton">
            <summary>
            Displays the programmed flightplan.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.GpsTerrainButton">
            <summary>
            Displays terrain information on default display
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.GpsProcedureButton">
            <summary>
            Displays the approach procedure page.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.GpsZoominButton">
            <summary>
            Zooms in default display
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.GpsZoomoutButton">
            <summary>
            Zooms out default display
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.GpsDirecttoButton">
            <summary>
            "Brings up the ""Direct To"" page"
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.GpsMenuButton">
            <summary>
            Brings up page to select active legs in a flightplan.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.GpsClearButton">
            <summary>
            Clears entered data on a page
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.GpsClearAllButton">
            <summary>
            Clears all data immediately
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.GpsClearButtonDown">
            <summary>
            Triggers the pressing of the Clear button
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.GpsClearButtonUp">
            <summary>
            Triggers the release of the Clear button.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.GpsEnterButton">
            <summary>
            Approves entered data.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.GpsCursorButton">
            <summary>
            Selects GPS cursor
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.GpsGroupKnobInc">
            <summary>
            Increments cursor
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.GpsGroupKnobDec">
            <summary>
            Decrements cursor
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.GpsPageKnobInc">
            <summary>
            Increments through pages
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.GpsPageKnobDec">
            <summary>
            Decrements through pages
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.DmeSelect">
            <summary>
            Selects one of the two DME systems (1,2).
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.RadioSelectedDmeIdentEnable">
            <summary>
            Turns on the identification sound for the selected DME.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.RadioSelectedDmeIdentDisable">
            <summary>
            Turns off the identification sound for the selected DME.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.RadioSelectedDmeIdentSet">
            <summary>
            Sets the DME identification sound to the given filename.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.RadioSelectedDmeIdentToggle">
            <summary>
            Turns on or off the identification sound for the selected DME.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Egt">
            <summary>
            Selects EGT bug for +/-
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.EgtInc">
            <summary>
            Increments EGT bugs
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.EgtDec">
            <summary>
            Decrements EGT bugs
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.EgtSet">
            <summary>
            Sets EGT bugs (0 to 32767)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Barometric">
            <summary>
            Syncs altimeter setting to sea level pressure, or 29.92 if above 18000 feet
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.GyroDriftInc">
            <summary>
            Increments heading indicator
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.GyroDriftDec">
            <summary>
            Decrements heading indicator
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.KohlsmanInc">
            <summary>
            Increments altimeter setting
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.KohlsmanDec">
            <summary>
            Decrements altimeter setting
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.KohlsmanSet">
            <summary>
            Sets altimeter setting (Millibars * 16)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.TrueAirspeedCalibrateInc">
            <summary>
            Increments airspeed indicators true airspeed reference card
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.TrueAirspeedCalibrateDec">
            <summary>
            Decrements airspeed indicators true airspeed reference card
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.TrueAirspeedCalSet">
            <summary>
            Sets airspeed indicators true airspeed reference card (degrees, where 0 is standard sea level conditions)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Egt1Inc">
            <summary>
            Increments EGT bug 1
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Egt1Dec">
            <summary>
            Decrements EGT bug 1
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Egt1Set">
            <summary>
            Sets EGT bug 1 (0 to 32767)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Egt2Inc">
            <summary>
            Increments EGT bug 2
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Egt2Dec">
            <summary>
            Decrements EGT bug 2
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Egt2Set">
            <summary>
            Sets EGT bug 2 (0 to 32767)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Egt3Inc">
            <summary>
            Increments EGT bug 3
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Egt3Dec">
            <summary>
            Decrements EGT bug 3
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Egt3Set">
            <summary>
            Sets EGT bug 3 (0 to 32767)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Egt4Inc">
            <summary>
            Increments EGT bug 4
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Egt4Dec">
            <summary>
            Decrements EGT bug 4
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Egt4Set">
            <summary>
            Sets EGT bug 4 (0 to 32767)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AttitudeBarsPositionInc">
            <summary>
            Increments attitude indicator pitch reference bars
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AttitudeBarsPositionDec">
            <summary>
            Decrements attitude indicator pitch reference bars
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleAttitudeCage">
            <summary>
            Cages attitude indicator at 0 pitch and bank
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ResetGForceIndicator">
            <summary>
            Resets max/min indicated G force to 1.0.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ResetMaxRpmIndicator">
            <summary>
            Reset max indicated engine rpm to 0.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.HeadingGyroSet">
            <summary>
            Sets heading indicator to 0 drift error.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.GyroDriftSet">
            <summary>
            Sets heading indicator drift angle (degrees).
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.StrobesToggle">
            <summary>
            Toggle strobe lights
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AllLightsToggle">
            <summary>
            Toggle all lights
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PanelLightsToggle">
            <summary>
            Toggle panel lights
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.LandingLightsToggle">
            <summary>
            Toggle landing lights
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.LandingLightUp">
            <summary>
            Rotate landing light up
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.LandingLightDown">
            <summary>
            Rotate landing light down
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.LandingLightLeft">
            <summary>
            Rotate landing light left
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.LandingLightRight">
            <summary>
            Rotate landing light right
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.LandingLightHome">
            <summary>
            Return landing light to default position
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.StrobesOn">
            <summary>
            Turn strobe lights on
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.StrobesOff">
            <summary>
            Turn strobe light off
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.StrobesSet">
            <summary>
            Set strobe lights on/off (1,0)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PanelLightsOn">
            <summary>
            Turn panel lights on
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PanelLightsOff">
            <summary>
            Turn panel lights off
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PanelLightsSet">
            <summary>
            Set panel lights on/off (1,0)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.LandingLightsOn">
            <summary>
            Turn landing lights on
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.LandingLightsOff">
            <summary>
            Turn landing lights off
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.LandingLightsSet">
            <summary>
            Set landing lights on/off (1,0)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleBeaconLights">
            <summary>
            Toggle beacon lights
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleTaxiLights">
            <summary>
            Toggle taxi lights
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleLogoLights">
            <summary>
            Toggle logo lights
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleRecognitionLights">
            <summary>
            Toggle recognition lights
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleWingLights">
            <summary>
            Toggle wing lights
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleNavLights">
            <summary>
            Toggle navigation lights
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleCabinLights">
            <summary>
            Toggle cockpit/cabin lights
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleVacuumFailure">
            <summary>
            Toggle vacuum system failure
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleElectricalFailure">
            <summary>
            Toggle electrical system failure
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.TogglePitotBlockage">
            <summary>
            Toggles blocked pitot tube
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleStaticPortBlockage">
            <summary>
            Toggles blocked static port
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleHydraulicFailure">
            <summary>
            Toggles hydraulic system failure
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleTotalBrakeFailure">
            <summary>
            Toggles brake failure (both)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleLeftBrakeFailure">
            <summary>
            Toggles left brake failure
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleRightBrakeFailure">
            <summary>
            Toggles right brake failure
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleEngine1Failure">
            <summary>
            Toggle engine 1 failure
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleEngine2Failure">
            <summary>
            Toggle engine 2 failure
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleEngine3Failure">
            <summary>
            Toggle engine 3 failure
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleEngine4Failure">
            <summary>
            Toggle engine 4 failure
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SmokeToggle">
            <summary>
            Toggle smoke system switch
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.GearToggle">
            <summary>
            Toggle gear handle
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Brakes">
            <summary>
            Increment brake pressure
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.GearSet">
            <summary>
            Sets gear handle position up/down (0,1)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.BrakesLeft">
            <summary>
            Increments left brake pressure
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.BrakesRight">
            <summary>
            Increments right brake pressure
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ParkingBrakes">
            <summary>
            Toggles parking brake on/off
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.GearPump">
            <summary>
            Increments emergency gear extension
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PitotHeatToggle">
            <summary>
            Toggles pitot heat switch
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SmokeOn">
            <summary>
            Turns smoke system on
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SmokeOff">
            <summary>
            Turns smoke system off
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SmokeSet">
            <summary>
            Sets smoke system on/off (1,0)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PitotHeatOn">
            <summary>
            Turns pitot heat switch on
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PitotHeatOff">
            <summary>
            Turns pitot heat switch off
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PitotHeatSet">
            <summary>
            Sets pitot heat switch on/off (1,0)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.GearUp">
            <summary>
            Sets gear handle in UP position
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.GearDown">
            <summary>
            Sets gear handle in DOWN position
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleMasterBattery">
            <summary>
            Toggles main battery switch
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleMasterAlternator">
            <summary>
            Toggles main alternator/generator switch
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleElectricVacuumPump">
            <summary>
            Toggles backup electric vacuum pump
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleAlternateStatic">
            <summary>
            Toggles alternate static pressure port
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.DecisionHeightDec">
            <summary>
            Decrements decision height reference
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.DecisionHeightInc">
            <summary>
            Increments decision height reference
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleStructuralDeice">
            <summary>
            Toggles structural deice switch
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.TogglePropellerDeice">
            <summary>
            Toggles propeller deice switch
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleAlternator1">
            <summary>
            Toggles alternator/generator 1 switch
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleAlternator2">
            <summary>
            Toggles alternator/generator 2 switch
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleAlternator3">
            <summary>
            Toggles alternator/generator 3 switch
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleAlternator4">
            <summary>
            Toggles alternator/generator 4 switch
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleMasterBatteryAlternator">
            <summary>
            Toggles master battery and alternator switch
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AxisLeftBrakeSet">
            <summary>
            Sets left brake position from axis controller (e.g. joystick). -16383 (0brakes) to +16383 (max brakes)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AxisRightBrakeSet">
            <summary>
            Sets right brake position from axis controller (e.g. joystick). -16383 (0brakes) to +16383 (max brakes)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleAircraftExit">
            <summary>
            Toggles primary door open/close. Follow by KEY_SELECT_2, etc for subsequent doors.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleWingFold">
            <summary>
            Toggles wing folding
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SetWingFold">
            <summary>
            Sets the wings into the folded position suitable for storage, typically on a carrier. Takes a value:1 -fold wings, 0 - unfold wings
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleTailHookHandle">
            <summary>
            Toggles tail hook
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SetTailHookHandle">
            <summary>
            Sets the tail hook handle. Takes a value: 1 - set tail hook, 0 - retract tail hook
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleWaterRudder">
            <summary>
            Toggles water rudders
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PushbackSet">
            <summary>
            Toggles pushback.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.TugHeading">
            <summary>
            Triggers tug and sets the desired heading. The units are a 32 bit integer (0 to4294967295) which represent 0 to 360 degrees. To set a 45 degree angle, for example, set the value to 4294967295 / 8.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.TugSpeed">
            <summary>
            Triggers tug, and sets desired speed, in feet per second. The speed can be bothpositive (forward movement) and negative (backward movement).
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.TugDisable">
            <summary>
            Disables tug
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleMasterIgnitionSwitch">
            <summary>
            Toggles master ignition switch
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleTailwheelLock">
            <summary>
            Toggles tail wheel lock
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AddFuelQuantity">
            <summary>
            Adds fuel to the aircraft, 25% of capacity by default. 0 to 65535 (max fuel) canbe passed.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.TowPlaneRelease">
            <summary>
            Release a towed aircraft, usually a glider.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.RequestTowPlane">
            <summary>
            Request a tow plane. The user aircraft must be tow-able, stationary, on the ground and not already attached for this to succeed.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ReleaseDroppableObjects">
            <summary>
            Release one droppable object. Multiple key events will release multiple objects.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.RetractFloatSwitchDec">
            <summary>
            If the plane has retractable floats, moves the retract position from Extend to Neutral, or Neutral to Retract.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.RetractFloatSwitchInc">
            <summary>
            If the plane has retractable floats, moves the retract position from Retract to Neutral, or Neutral to Extend.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleWaterBallastValve">
            <summary>
            Turn the water ballast valve on or off.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleVariometerSwitch">
            <summary>
            Turn the variometer on or off.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleTurnIndicatorSwitch">
            <summary>
            Turn the turn indicator on or off.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApuStarter">
            <summary>
            Start up the auxiliary power unit (APU).
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApuOffSwitch">
            <summary>
            Turn the APU off.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApuGeneratorSwitchToggle">
            <summary>
            Turn the auxiliary generator on or off.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ApuGeneratorSwitchSet">
            <summary>
            Set the auxiliary generator switch (0,1).
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ExtinguishEngineFire">
            <summary>
            Takes a two digit argument.The first digit represents the fire extinguisher index, and the second represents the engine index.For example,11 would represent using bottle 1 on engine 1.21 would represent using bottle 2 on engine 1.Typical entries for a twin engine aircraft would be 11 and 22.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.HydraulicSwitchToggle">
            <summary>
            Turn the hydraulic switch on or off.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.BleedAirSourceControlInc">
            <summary>
            Increases the bleed air source control.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.BleedAirSourceControlDec">
            <summary>
            Decreases the bleed air source control.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.BleedAirSourceControlSet">
            <summary>
            Set to one of: 0: auto1: off2: apu3: engines
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.TurbineIgnitionSwitchToggle">
            <summary>
            Turn the turbine ignition switch on or off.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.CabinNoSmokingAlert">
            <summary>
            "Turn the ""No smoking"" alert on or off."
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.CabinSeatbeltsAlert">
            <summary>
            "Turn the ""Fasten seatbelts"" alert on or off."
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AntiskidBrakesToggle">
            <summary>
            Turn the anti-skid braking system on or off.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.GpwsSwitchToggle">
            <summary>
            Turn the g round proximity warning system (GPWS) on or off.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ManualFuelPressurePump">
            <summary>
            Activate the manual fuel pressure pump.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AnnunciatorSwitchToggle">
            <summary>
            Togles the annunciator switch.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AnnunciatorSwitchOn">
            <summary>
            Turns on the annunciator switch.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AnnunciatorSwitchOff">
            <summary>
            Turns off the annunciator switch.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SteeringInc">
            <summary>
            Increments the nose wheel steering position by 5 percent.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SteeringDec">
            <summary>
            Decrements the nose wheel steering position by 5 percent.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SteeringSet">
            <summary>
            Sets the value of the nose wheel steering position. Zero is straight ahead(-16383, far left +16383, far right).
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PressurizationPressureAltInc">
            <summary>
            Increases the altitude that the cabin is pressurized to.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PressurizationPressureAltDec">
            <summary>
            Decreases the altitude that the cabin is pressurized to.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PressurizationClimbRateInc">
            <summary>
            Sets the rate at which cabin pressurization is increased.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PressurizationClimbRateDec">
            <summary>
            Sets the rate at which cabin pressurization is decreased.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PressurizationPressureDump">
            <summary>
            Sets the cabin pressure to the outside air pressure.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.TakeoffAssistArmToggle">
            <summary>
            Deploy or remove the assist arm. Refer to the document Notes on Aircraft Systems.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.TakeoffAssistArmSet">
            <summary>
            Value: TRUE request set FALSE request unset
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.TakeoffAssistFire">
            <summary>
            If everything is set up correctly. Launch from the catapult.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleLaunchBarSwitch">
            <summary>
            Toggle the request for the launch bar to be installed or removed.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SetLaunchbarSwitch">
            <summary>
            Value: TRUE request set FALSE request unset
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.RotorBrake">
            <summary>
            Triggers rotor braking input
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.RotorClutchSwitchToggle">
            <summary>
            Toggles on electric rotor clutch switch
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.RotorClutchSwitchSet">
            <summary>
            Sets electric rotor clutch switch on/off (1,0)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.RotorGovSwitchToggle">
            <summary>
            Toggles the electric rotor governor switch
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.RotorGovSwitchSet">
            <summary>
            Sets the electric rotor governor switch on/off (1,0)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.RotorLateralTrimInc">
            <summary>
            Increments the lateral (right) rotor trim
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.RotorLateralTrimDec">
            <summary>
            Decrements the lateral (right) rotor trim
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.RotorLateralTrimSet">
            <summary>
            Sets the lateral (right) rotor trim (0 to 16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SlingPickupRelease">
            <summary>
            Toggle between pickup and release mode. Hold mode is automatic and cannot be selected. Refer to the document Notes on Aircraft Systems.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.HoistSwitchExtend">
            <summary>
            The rate at which a hoist cable extends is set in the Aircraft Configuration File.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.HoistSwitchRetract">
            <summary>
            The rate at which a hoist cable retracts is set in the Aircraft Configuration File.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.HoistSwitchSet">
            <summary>
            The data value should be set to one of: &lt;0 up=0 off &gt;0 down
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.HoistDeployToggle">
            <summary>
            Toggles the hoist arm switch, extend or retract.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.HoistDeploySet">
            <summary>
            The data value should be set to: 0 - set hoist switch to retract the arm1 - set hoist switch to extend the arm
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SlewToggle">
            <summary>
            Toggles slew on/off
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SlewOff">
            <summary>
            Turns slew off
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SlewOn">
            <summary>
            Turns slew on
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SlewSet">
            <summary>
            Sets slew on/off (1,0)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SlewReset">
            <summary>
            Stop slew and reset pitch, bank, and heading all to zero.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SlewAltitUpFast">
            <summary>
            Slew upward fast
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SlewAltitUpSlow">
            <summary>
            Slew upward slow
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SlewAltitFreeze">
            <summary>
            Stop vertical slew
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SlewAltitDnSlow">
            <summary>
            Slew downward slow
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SlewAltitDnFast">
            <summary>
            Slew downward fast
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SlewAltitPlus">
            <summary>
            Increase upward slew
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SlewAltitMinus">
            <summary>
            Decrease upward slew
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SlewPitchDnFast">
            <summary>
            Slew pitch downward fast
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SlewPitchDnSlow">
            <summary>
            Slew pitch downward slow
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SlewPitchFreeze">
            <summary>
            Stop pitch slew
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SlewPitchUpSlow">
            <summary>
            Slew pitch up slow
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SlewPitchUpFast">
            <summary>
            Slew pitch upward fast
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SlewPitchPlus">
            <summary>
            Increase pitch up slew
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SlewPitchMinus">
            <summary>
            Decrease pitch up slew
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SlewBankMinus">
            <summary>
            Increase left bank slew
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SlewAheadPlus">
            <summary>
            Increase forward slew
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SlewBankPlus">
            <summary>
            Increase right bank slew
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SlewLeft">
            <summary>
            Slew to the left
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SlewFreeze">
            <summary>
            Stop all slew
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SlewRight">
            <summary>
            Slew to the right
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SlewHeadingMinus">
            <summary>
            Increase slew heading to the left
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SlewAheadMinus">
            <summary>
            Decrease forward slew
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SlewHeadingPlus">
            <summary>
            Increase slew heading to the right
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AxisSlewAheadSet">
            <summary>
            Sets forward slew (+/- 16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AxisSlewSidewaysSet">
            <summary>
            Sets sideways slew (+/- 16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AxisSlewHeadingSet">
            <summary>
            Sets heading slew (+/- 16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AxisSlewAltSet">
            <summary>
            Sets vertical slew (+/- 16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AxisSlewBankSet">
            <summary>
            Sets roll slew (+/- 16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AxisSlewPitchSet">
            <summary>
            Sets pitch slew (+/- 16383)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ViewMode">
            <summary>
            Selects next view
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ViewWindowToFront">
            <summary>
            Sets active window to front
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ViewReset">
            <summary>
            Resets the view to the default
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ViewAlwaysPanUp">
            <summary>
            
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ViewAlwaysPanDown">
            <summary>
            
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.NextSubView">
            <summary>
            
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PrevSubView">
            <summary>
            
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ViewTrackPanToggle">
            <summary>
            
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ViewPreviousToggle">
            <summary>
            
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ViewCameraSelectStarting">
            <summary>
            
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PanelHudNext">
            <summary>
            
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PanelHudPrevious">
            <summary>
            
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ZoomIn">
            <summary>
            Zooms view in
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ZoomOut">
            <summary>
            Zooms view out
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.MapZoomFineIn">
            <summary>
            Fine zoom in map view
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PanLeft">
            <summary>
            Pans view left
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PanRight">
            <summary>
            Pans view right
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.MapZoomFineOut">
            <summary>
            Fine zoom out in map view
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ViewForward">
            <summary>
            Sets view direction forward
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ViewForwardRight">
            <summary>
            Sets view direction forward and right
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ViewRight">
            <summary>
            Sets view direction to the right
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ViewRearRight">
            <summary>
            Sets view direction to the rear and right
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ViewRear">
            <summary>
            Sets view direction to the rear
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ViewRearLeft">
            <summary>
            Sets view direction to the rear and left
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ViewLeft">
            <summary>
            Sets view direction to the left
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ViewForwardLeft">
            <summary>
            Sets view direction forward and left
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ViewDown">
            <summary>
            Sets view direction down
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ZoomMinus">
            <summary>
            Decreases zoom
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ZoomPlus">
            <summary>
            Increase zoom
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PanUp">
            <summary>
            Pan view up
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PanDown">
            <summary>
            Pan view down
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ViewModeRev">
            <summary>
            Reverse view cycle
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ZoomInFine">
            <summary>
            Zoom in fine
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ZoomOutFine">
            <summary>
            Zoom out fine
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.CloseView">
            <summary>
            Close current view
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.NewView">
            <summary>
            Open new view
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.NextView">
            <summary>
            Select next view
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PrevView">
            <summary>
            Select previous view
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PanLeftUp">
            <summary>
            Pan view left
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PanLeftDown">
            <summary>
            Pan view left and down
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PanRightUp">
            <summary>
            Pan view right and up
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PanRightDown">
            <summary>
            Pan view right and down
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PanTiltLeft">
            <summary>
            Tilt view left
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PanTiltRight">
            <summary>
            Tilt view right
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PanReset">
            <summary>
            Reset view to forward
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ViewForwardUp">
            <summary>
            Sets view forward and up
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ViewForwardRightUp">
            <summary>
            Sets view forward, right, and up
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ViewRightUp">
            <summary>
            Sets view right and up
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ViewRearRightUp">
            <summary>
            Sets view rear, right, and up
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ViewRearUp">
            <summary>
            Sets view rear and up
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ViewRearLeftUp">
            <summary>
            Sets view rear left and up
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ViewLeftUp">
            <summary>
            Sets view left and up
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ViewForwardLeftUp">
            <summary>
            Sets view forward left and up
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ViewUp">
            <summary>
            Sets view up
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PanResetCockpit">
            <summary>
            Reset panning to forward, if in cockpit view
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ChaseViewNext">
            <summary>
            Cycle view to next target
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ChaseViewPrev">
            <summary>
            Cycle view to previous target
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ChaseViewToggle">
            <summary>
            Toggles chase view on/off
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.EyepointUp">
            <summary>
            Move eyepoint up
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.EyepointDown">
            <summary>
            Move eyepoint down
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.EyepointRight">
            <summary>
            Move eyepoint right
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.EyepointLeft">
            <summary>
            Move eyepoint left
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.EyepointForward">
            <summary>
            Move eyepoint forward
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.EyepointBack">
            <summary>
            Move eyepoint backward
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.EyepointReset">
            <summary>
            Move eyepoint to default position
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.NewMap">
            <summary>
            Opens new map view
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ViewCockpitForward">
            <summary>
            Switch immediately to the forward view, in 2D mode.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ViewVirtualCockpitForward">
            <summary>
            Switch immediately to the forward view, in virtual cockpit mode.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ViewPanelAlphaSet">
            <summary>
            Sets the alpha-blending value for the panel. Takes a parameter in the range 0to 255. The alpha-blending can be changed from the keyboard using Ctrl-Shift-T,and the plus and minus keys.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ViewPanelAlphaSelect">
            <summary>
            Sets the mode to change the alpha-blending, so the keys KEY_PLUS and KEY_MINUS increment and decrement the value.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ViewPanelAlphaInc">
            <summary>
            Increment alpha-blending for the panel.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ViewPanelAlphaDec">
            <summary>
            Decrement alpha-blending for the panel.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ViewLinkingSet">
            <summary>
            Links all the views from one camera together, so that panning the view will change the view of all the linked cameras.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ViewLinkingToggle">
            <summary>
            Turns view linking on or off.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ViewChaseDistanceAdd">
            <summary>
            Increments the distance of the view camera from the chase object (such as in Spot Plane view, or viewing an AI controlled aircraft).
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ViewChaseDistanceSub">
            <summary>
            Decrements the distance of the view camera from the chase object.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PauseToggle">
            <summary>
            Toggles pause on/off
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PauseOn">
            <summary>
            Turns pause on
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PauseOff">
            <summary>
            Turns pause off
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PauseSet">
            <summary>
            Sets pause on/off (1,0)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.DemoStop">
            <summary>
            Stops demo system playback
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Select1">
            <summary>
            "Sets ""selected"" index (for other events) to 1"
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Select2">
            <summary>
            "Sets ""selected"" index (for other events) to 2"
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Select3">
            <summary>
            "Sets ""selected"" index (for other events) to 3"
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Select4">
            <summary>
            "Sets ""selected"" index (for other events) to 4"
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Minus">
            <summary>
            "Used in conjunction with ""selected"" parameters to decrease their value (e.g.,radio frequency)"
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Plus">
            <summary>
            "Used in conjunction with ""selected"" parameters to increase their value (e.g.,radio frequency)"
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Zoom1x">
            <summary>
            Sets zoom level to 1
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SoundToggle">
            <summary>
            Toggles sound on/off
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SimRate">
            <summary>
            Selects simulation rate (use KEY_MINUS, KEY_PLUS to change)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.JoystickCalibrate">
            <summary>
            Toggles joystick on/off
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SituationSave">
            <summary>
            Saves flight situation
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SituationReset">
            <summary>
            Resets flight situation
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SoundSet">
            <summary>
            Sets sound on/off (1,0)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Exit">
            <summary>
            Quit ESP with a message
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Abort">
            <summary>
            Quit ESP without a message
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ReadoutsSlew">
            <summary>
            Cycle through information readouts while in slew
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ReadoutsFlight">
            <summary>
            Cycle through information readouts
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.MinusShift">
            <summary>
            Used with other events
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PlusShift">
            <summary>
            Used with other events
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SimRateIncr">
            <summary>
            Increase sim rate
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SimRateDecr">
            <summary>
            Decrease sim rate
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Kneeboard">
            <summary>
            Toggles kneeboard
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Panel1">
            <summary>
            Toggles panel 1
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Panel2">
            <summary>
            Toggles panel 2
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Panel3">
            <summary>
            Toggles panel 3
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Panel4">
            <summary>
            Toggles panel 4
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Panel5">
            <summary>
            Toggles panel 5
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Panel6">
            <summary>
            Toggles panel 6
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Panel7">
            <summary>
            Toggles panel 7
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Panel8">
            <summary>
            Toggles panel 8
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Panel9">
            <summary>
            Toggles panel 9
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SoundOn">
            <summary>
            Turns sound on
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SoundOff">
            <summary>
            Turns sound off
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.InvokeHelp">
            <summary>
            Brings up Help system
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleAircraftLabels">
            <summary>
            Toggles aircraft labels
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FlightMap">
            <summary>
            Brings up flight map
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ReloadPanels">
            <summary>
            Reload panel data
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PanelIdToggle">
            <summary>
            Toggles indexed panel (1 to 9)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PanelIdOpen">
            <summary>
            Opens indexed panel (1 to 9)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PanelIdClose">
            <summary>
            Closes indexed panel (1 to 9)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ControlReloadUserAircraft">
            <summary>
            Reloads the user aircraft data (from cache if same type loaded as an AI,otherwise from disk)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SimReset">
            <summary>
            Resets aircraft state
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.VirtualCopilotToggle">
            <summary>
            Turns Flying Tips on/off
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.VirtualCopilotSet">
            <summary>
            Sets Flying Tips on/off (1,0)
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.VirtualCopilotAction">
            <summary>
            Triggers action noted in Flying Tips
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.RefreshScenery">
            <summary>
            Reloads scenery
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ClockHoursDec">
            <summary>
            Decrements time by hours
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ClockHoursInc">
            <summary>
            Increments time by hours
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ClockMinutesDec">
            <summary>
            Decrements time by minutes
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ClockMinutesInc">
            <summary>
            Increments time by minutes
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ClockSecondsZero">
            <summary>
            Zeros seconds
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ClockHoursSet">
            <summary>
            Sets hour of day
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ClockMinutesSet">
            <summary>
            Sets minutes of the hour
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ZuluHoursSet">
            <summary>
            Sets hours, zulu time
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ZuluMinutesSet">
            <summary>
            Sets minutes, in zulu time
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ZuluDaySet">
            <summary>
            Sets day, in zulu time
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ZuluYearSet">
            <summary>
            Sets year, in zulu time
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.GaugeKeystroke">
            <summary>
            Enables a keystroke to be sent to a gauge that is in focus. The keystrokes can only be in the range 0 to 9, A to Z, and the four keys: plus, minus, comma and period. This is typically used to allow some keyboard entry to a complex device such as a GPS to enter such things as ICAO codes using the keyboard, rather than turning dials.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.SimuiWindowHideshow">
            <summary>
            Display the ATC window.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.WindowTitlesToggle">
            <summary>
            Turn window titles on or off.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AxisPanPitch">
            <summary>
            Sets the pitch of the axis. Requires an angle.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AxisPanHeading">
            <summary>
            Sets the heading of the axis. Requires an angle.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AxisPanTilt">
            <summary>
            Sets the tilt of the axis. Requires an angle.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AxisIndicatorCycle">
            <summary>
            Step through the view axes.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.MapOrientationCycle">
            <summary>
            Step through the map orientations.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleJetway">
            <summary>
            Requests a jetway, which will only be answered if the aircraft is at a parking spot.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.VideoRecordToggle">
            <summary>
            Turn on or off the video recording feature. This records uncompressed AVI format files to: My Documents\Videos\
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleAirportNameDisplay">
            <summary>
            Turn on or off the airport name.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.CaptureScreenshot">
            <summary>
            Capture the current view as a screenshot. Which will be saved to a bmp file in: My Documents\Pictures\
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.MouseLookToggle">
            <summary>
            Switch Mouse Look mode on or off. Mouse Look mode enables a user to control their view using the mouse, and holding down the space bar.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.YaxisInvertToggle">
            <summary>
            Switch inversion of Y axis controls on or off.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AutocoordToggle">
            <summary>
            Turn the automatic rudder control feature on or off.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FreezeLatitudeLongitudeToggle">
            <summary>
            Turns the freezing of the lat/lon position of the aircraft (either user or AI controlled) on or off. If this key event is set, it means that the latitude and longitude of the aircraft are not being controlled by ESP, so enabling, for example, a SimConnect client to control the position of the aircraft. This can also apply to altitude and attitude. Refer to the simulation variables: IS LATITUDE LONGITUDE FREEZE ON, IS ALTITUDE FREEZE ON, and IS ATTITUDE FREEZE ON Refer also to the SimConnect_AIReleaseControl function.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FreezeLatitudeLongitudeSet">
            <summary>
            Freezes the lat/lon position of the aircraft.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FreezeAltitudeToggle">
            <summary>
            Turns the freezing of the altitude of the aircraft on or off.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FreezeAltitudeSet">
            <summary>
            Freezes the altitude of the aircraft.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FreezeAttitudeToggle">
            <summary>
            Turns the freezing of the attitude (pitch, bank and heading) of the aircraft on or off.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.FreezeAttitudeSet">
            <summary>
            Freezes the attitude (pitch, bank and heading) of the aircraft.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PointOfInterestTogglePointer">
            <summary>
            Turn the point-of-interest indicator (often a light beam) on or off. Refer to the Missions system documentation.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PointOfInterestCyclePrevious">
            <summary>
            Change the current point-of-interest to the previous point-of-interest.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.PointOfInterestCycleNext">
            <summary>
            Change the current point-of-interest to the next point-of-interest.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Atc">
            <summary>
            Activates ATC window
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AtcMenu1">
            <summary>
            Selects ATC option 1
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AtcMenu2">
            <summary>
            Selects ATC option 2
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AtcMenu3">
            <summary>
            Selects ATC option 3
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AtcMenu4">
            <summary>
            Selects ATC option 4
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AtcMenu5">
            <summary>
            Selects ATC option 5
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AtcMenu6">
            <summary>
            Selects ATC option 6
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AtcMenu7">
            <summary>
            Selects ATC option 7
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AtcMenu8">
            <summary>
            Selects ATC option 8
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AtcMenu9">
            <summary>
            Selects ATC option 9
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.AtcMenu0">
            <summary>
            Selects ATC option 10
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.MultiplayerTransferControl">
            <summary>
            Toggle to the next player to track
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.MultiplayerPlayerCycle">
            <summary>
            Cycle through the current user aircraft.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.MultiplayerPlayerFollow">
            <summary>
            Set the view to follow the selected user aircraft.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.MultiplayerChat">
            <summary>
            Toggles chat window visible/invisible
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.MultiplayerActivateChat">
            <summary>
            Activates chat window
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.MultiplayerVoiceCaptureStart">
            <summary>
            Start capturing audio from the users computer and transmitting it to all other players in the multiplayer session who are turned to the same radio frequency.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.MultiplayerVoiceCaptureStop">
            <summary>
            Stop capturing radio audio.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.MultiplayerBroadcastVoice">
            <summary>
            Start capturing audio from the users computer and transmitting it to all other players in the multiplayer session.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ToggleRaceresultsWindow">
            <summary>
            Show or hide multi-player race results.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ComRadioSetHz">
            <summary>
            Set COM1 Active frequency.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Com2RadioSetHz">
            <summary>
            Set COM2 Active frequency.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Nav1RadioSetHz">
            <summary>
            Set NAV1 Standby frequency.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Nav2RadioSetHz">
            <summary>
            Set NAV2 Standby frequency.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.ComStbyRadioSetHz">
            <summary>
            Set COM1 Standby frequency.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Com2StbyRadioSetHz">
            <summary>
            Set COM2 Standby frequency.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Nav1StbyRadioSetHz">
            <summary>
            Set NAV1 Standby frequency.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsEventNameId.Nav2StbyRadioSetHz">
            <summary>
            Set NAV2 Standby frequency.
            </summary>
        </member>
        <member name="T:CTrue.FsConnect.FsEventNameLookup">
            <summary>
            Provides lookup of FsEventNameId enums to event names that can be registered in MSFS.
            </summary>
        </member>
        <member name="M:CTrue.FsConnect.FsEventNameLookup.GetFsEventName(CTrue.FsConnect.FsEventNameId)">
            <summary>
            Returns an event name for a given event name id.
            </summary>
            <param name="eventNameId">An <see cref="T:CTrue.FsConnect.FsEventNameId"/> representing a MSFS event.</param>
        </member>
        <member name="T:CTrue.FsConnect.FsSimVar">
            <summary>
            The <see cref="T:CTrue.FsConnect.FsSimVar"/> enum contains all known simulation variables.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsSimVar.AutopilotHeadingLock">
            <summary>
            Heading mode active
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.FsSimVar.AutopilotHeadingLockDir">
            <summary>
            Selected heading
            </summary>
        </member>
        <member name="M:CTrue.FsConnect.FsSimVarFactory.GetSimVarName(CTrue.FsConnect.FsSimVar)">
            <summary>
            Gets the corresponding simulation variable name to a simulation variable code.
            </summary>
            <param name="simVarId">A <see cref="T:CTrue.FsConnect.FsSimVar"/> enum.</param>
            <returns>The corresponding sim variable name.</returns>
        </member>
        <member name="M:CTrue.FsConnect.FsSimVarFactory.GetSimVarName(System.String)">
            <summary>
            Looks up a text and tries to resolve the corresponding SimVar name.
            </summary>
            <param name="simVarName">A name containing any case or underscores.</param>
            <returns>An identified SimVar name or null if not recognized.</returns>
        </member>
        <member name="M:CTrue.FsConnect.FsUnitFactory.GetUnitName(CTrue.FsConnect.FsUnit)">
            <summary>
            Gets the unit name used by MSFS.
            </summary>
            <param name="unit"></param>
            <returns></returns>
        </member>
        <member name="T:CTrue.FsConnect.FsUtils">
            <summary>
            Utilities for handling Flight Simulator
            </summary>
        </member>
        <member name="M:CTrue.FsConnect.FsUtils.Deg2Rad(System.Double)">
            <summary>
            Converts degrees to radians.
            </summary>
            <param name="deg"></param>
            <returns></returns>
        </member>
        <member name="M:CTrue.FsConnect.FsUtils.Rad2Deg(System.Double)">
            <summary>
            Converts radians to degrees.
            </summary>
        </member>
        <member name="T:CTrue.FsConnect.IFsConnect">
            <summary>
            A wrapper / helper class for connection to Microsoft Flight Simulator.
            </summary>
            <remarks>
            The <see cref="T:CTrue.FsConnect.IFsConnect"/> wraps the SimConnect.dll and managed 
            </remarks>
        </member>
        <member name="E:CTrue.FsConnect.IFsConnect.ConnectionChanged">
            <summary>
            The <see cref="E:CTrue.FsConnect.IFsConnect.ConnectionChanged"/> event is raised when the connection status to Flight Simulator has changed.
            </summary>
        </member>
        <member name="E:CTrue.FsConnect.IFsConnect.FsDataReceived">
            <summary>
            The <see cref="E:CTrue.FsConnect.IFsConnect.FsDataReceived"/> event is raised when data has been received from Flight Simulator.
            </summary>
        </member>
        <member name="E:CTrue.FsConnect.IFsConnect.ObjectAddRemoveEventReceived">
            <summary>
            The <see cref="E:CTrue.FsConnect.IFsConnect.ObjectAddRemoveEventReceived"/> event is raised when sim objects have been added or removed from Flight Simulator.
            </summary>
        </member>
        <member name="E:CTrue.FsConnect.IFsConnect.FsError">
            <summary>
            The <see cref="E:CTrue.FsConnect.IFsConnect.FsError"/> event is raised when an error has been raised by SimConnect.
            </summary>
        </member>
        <member name="E:CTrue.FsConnect.IFsConnect.AircraftLoaded">
            <summary>
            The <see cref="E:CTrue.FsConnect.IFsConnect.AircraftLoaded"/> event is raised when the aircraft has loaded.
            </summary>
        </member>
        <member name="E:CTrue.FsConnect.IFsConnect.FlightLoaded">
            <summary>
            The <see cref="E:CTrue.FsConnect.IFsConnect.FlightLoaded"/> event is raised when the flight has loaded.
            </summary>
        </member>
        <member name="E:CTrue.FsConnect.IFsConnect.PauseStateChanged">
            <summary>
            The <see cref="E:CTrue.FsConnect.IFsConnect.PauseStateChanged"/> event is raised when the flight simulator has been paused or unpaused.
            </summary>
        </member>
        <member name="E:CTrue.FsConnect.IFsConnect.SimStateChanged">
            <summary>
            The <see cref="E:CTrue.FsConnect.IFsConnect.SimStateChanged"/> event is raised when the flight simulator running state has changed.
            </summary>
        </member>
        <member name="E:CTrue.FsConnect.IFsConnect.Crashed">
            <summary>
            The <see cref="E:CTrue.FsConnect.IFsConnect.Crashed"/> event is raised when the aircraft has crashed.
            </summary>
        </member>
        <member name="P:CTrue.FsConnect.IFsConnect.Connected">
            <summary>
            Gets a boolean value indication whether a connection to Flight Simulator is established.
            </summary>
        </member>
        <member name="P:CTrue.FsConnect.IFsConnect.SimConnectFileLocation">
            <summary>
            Gets or sets where to write the SimConnect.cfg file, that specifies how to connect to Flight Simulator. By default set to local.
            </summary>
            <remarks>
            The default beghavior is to write the file to the same location as the executing assembly, but this may cause problems if the application does not have write access to this location. As an alternative the file can be written to My Document, but this may cause issues with other SimConnect using applications.
            </remarks>
        </member>
        <member name="P:CTrue.FsConnect.IFsConnect.ConnectionInfo">
            <summary>
            The <see cref="P:CTrue.FsConnect.IFsConnect.ConnectionInfo"/> contains key information about the connection to the flight simulator, such as connection status and version information.
            </summary>
        </member>
        <member name="P:CTrue.FsConnect.IFsConnect.Paused">
            <summary>
            Gets whether the Flight Simulator is paused.
            </summary>
        </member>
        <member name="M:CTrue.FsConnect.IFsConnect.Connect(System.String,System.UInt32)">
            <summary>
            Connects to Flight Simulator using an existing SimConnect.cfg.
            </summary>
            <param name="applicationName">A name identifying this client to Flight Simulator.</param>
            <param name="configIndex">The index to a specified configuration in the SimConnect.cfg file. Default is index 0.</param>
        </member>
        <member name="M:CTrue.FsConnect.IFsConnect.Connect(System.String,System.String,System.UInt32,CTrue.FsConnect.SimConnectProtocol)">
            <summary>
            Connects to Flight Simulator on the specified host name and port.
            </summary>
            <param name="applicationName">A name identifying this client to Flight Simulator.</param>
            <param name="hostName">A hostname or IP address.</param>
            <param name="port">A TCP or pipe port number.</param>
            <param name="protocol">The protocol to use to connect to Flight Simulator.</param>
            <remarks>
            A SimConnect.cfg file will be generated containing connection information.
            Flight Simulator must be configured for remote connections by editing the SimConnect.xml file that are part of the installation.
            </remarks>
        </member>
        <member name="M:CTrue.FsConnect.IFsConnect.Disconnect">
            <summary>
            Disconnects from Flight Simulator.
            </summary>
        </member>
        <member name="M:CTrue.FsConnect.IFsConnect.SetText(System.String,System.Int32)">
            <summary>
            Displays a text in Flight Simulator.
            </summary>
            <param name="text">The text to display.</param>
            <param name="duration">Duration to display text, in seconds.</param>
        </member>
        <member name="M:CTrue.FsConnect.IFsConnect.Pause">
            <summary>
            Toggle the pause status of the Flight Simulator.
            </summary>
        </member>
        <member name="M:CTrue.FsConnect.IFsConnect.Pause(System.Boolean)">
            <summary>
            Pauses or unpauses the simulator.
            </summary>
            <param name="pause">true to pause, false to unpause.</param>
        </member>
        <member name="M:CTrue.FsConnect.IFsConnect.RegisterDataDefinition``1(System.Enum,System.Collections.Generic.List{CTrue.FsConnect.SimVar})">
            <summary>
            Registers data structures for requesting data from Flight Simulator.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="defineId">The definition id to associated with the data definition.</param>
            <param name="definition"></param>
            <returns>The definition id used to register the data definition</returns>
            <remarks>
            A connection to Flight Simulator must have been established before registering data definitions.
            </remarks>
        </member>
        <member name="M:CTrue.FsConnect.IFsConnect.RegisterDataDefinition``1(System.Int32,System.Collections.Generic.List{CTrue.FsConnect.SimVar})">
            <summary>
            Registers data structures for requesting data from Flight Simulator.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="defineId">The definition id to associated with the data definition.</param>
            <param name="definition"></param>
            <returns>The definition id used to register the data definition</returns>
            <remarks>
            A connection to Flight Simulator must have been established before registering data definitions.
            </remarks>
        </member>
        <member name="M:CTrue.FsConnect.IFsConnect.RegisterDataDefinition``1(System.Collections.Generic.List{CTrue.FsConnect.SimVar})">
            <summary>
            Registers data structures for requesting data from Flight Simulator.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="definition"></param>
            <returns></returns>
            <remarks>
            A connection to Flight Simulator must have been established before registering data definitions.
            </remarks>
        </member>
        <member name="M:CTrue.FsConnect.IFsConnect.RegisterDataDefinition``1(System.Enum)">
            <summary>
            Registers data structures for requesting data from Flight Simulator.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="defineId">The definition id to associated with the data definition.</param>
            <returns>The definition id used to register the data definition</returns>
            <remarks>
            A connection to Flight Simulator must have been established before registering data definitions.
            The data definition is based on reflection, by analyzing the type. See <see cref="T:CTrue.FsConnect.SimVarReflector"/>.
            </remarks>
        </member>
        <member name="M:CTrue.FsConnect.IFsConnect.RegisterDataDefinition``1(System.Int32)">
            <summary>
            Registers data structures for requesting data from Flight Simulator.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="defineId">The definition id to associated with the data definition.</param>
            <returns>The definition id used to register the data definition</returns>
            <remarks>
            A connection to Flight Simulator must have been established before registering data definitions.
            The data definition is based on reflection, by analyzing the type. See <see cref="T:CTrue.FsConnect.SimVarReflector"/>.
            </remarks>
        </member>
        <member name="M:CTrue.FsConnect.IFsConnect.RegisterDataDefinition``1">
            <summary>
            Registers data structures for requesting data from Flight Simulator.
            </summary>
            <typeparam name="T"></typeparam>
            <returns>The definition id used to register the data definition</returns>
            <remarks>
            A connection to Flight Simulator must have been established before registering data definitions.
            The data definition is based on reflection, by analyzing the type. See <see cref="T:CTrue.FsConnect.SimVarReflector"/>.
            </remarks>
        </member>
        <member name="M:CTrue.FsConnect.IFsConnect.RequestDataOnSimObject(System.Enum,System.Enum,System.UInt32,CTrue.FsConnect.FsConnectPeriod,CTrue.FsConnect.FsConnectDRequestFlag,System.UInt32,System.UInt32,System.UInt32)">
            <summary>
            Requests data on a Sim object, periodically or/and when changed.
            </summary>
            <param name="requestId"></param>
            <param name="defineId">The definition id to associated with the data definition.</param>
            <param name="objectId"></param>
            <param name="period"></param>
            <param name="flags"></param>
            <param name="origin"></param>
            <param name="interval"></param>
            <param name="limit"></param>
        </member>
        <member name="M:CTrue.FsConnect.IFsConnect.RequestDataOnSimObject(System.Enum,System.Int32,System.UInt32,CTrue.FsConnect.FsConnectPeriod,CTrue.FsConnect.FsConnectDRequestFlag,System.UInt32,System.UInt32,System.UInt32)">
            <summary>
            Requests data on a Sim object, periodically or/and when changed.
            </summary>
            <param name="requestId"></param>
            <param name="defineId">The definition id to associated with the data definition.</param>
            <param name="objectId"></param>
            <param name="period"></param>
            <param name="flags"></param>
            <param name="origin"></param>
            <param name="interval"></param>
            <param name="limit"></param>
        </member>
        <member name="M:CTrue.FsConnect.IFsConnect.RequestData(System.Enum,System.Enum,System.UInt32,CTrue.FsConnect.FsConnectSimobjectType)">
            <summary>
            Requests data from Flight Simulator.
            </summary>
            <param name="requestId"></param>
            <param name="defineId">The definition id to associated with the data definition.</param>
            <param name="radius">Radius in meters. Should be less that 2000000 (200km).</param>
            <param name="type"></param>
        </member>
        <member name="M:CTrue.FsConnect.IFsConnect.RequestData(System.Int32,System.Int32,System.UInt32,CTrue.FsConnect.FsConnectSimobjectType)">
            <summary>
            Requests data from Flight Simulator.
            </summary>
            <param name="requestId"></param>
            <param name="defineId">The definition id to associated with the data definition.</param>
            <param name="radius">Radius in meters. Should be less that 2000000 (200km).</param>
            <param name="type"></param>
        </member>
        <member name="M:CTrue.FsConnect.IFsConnect.UpdateData``1(System.Enum,``0,System.UInt32)">
            <summary>
            Updates a sim object in the flight simulator.
            </summary>
            <param name="defineId">The definition id to associated with the data definition.</param>
            <param name="data"></param>
            <param name="objectId"></param>
            <typeparam name="T"></typeparam>
        </member>
        <member name="M:CTrue.FsConnect.IFsConnect.UpdateData``1(System.Int32,``0,System.UInt32)">
            <summary>
            Updates a sim object in the flight simulator.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="defineId">The definition id to associated with the data definition.</param>
            <param name="data"></param>
            <param name="objectId"></param>
        </member>
        <member name="M:CTrue.FsConnect.IFsConnect.SetNotificationGroupPriority(System.Enum)">
            <summary>
            Sets notification group priority to highest level.
            </summary>
            <param name="groupId"></param>
        </member>
        <member name="M:CTrue.FsConnect.IFsConnect.SetNotificationGroupPriority(System.Int32)">
            <summary>
            Sets notification group priority to highest level.
            </summary>
            <param name="groupId"></param>
        </member>
        <member name="M:CTrue.FsConnect.IFsConnect.TransmitClientEvent(System.Enum,System.UInt32,System.Enum)">
            <summary>
            Sends a client event to flight simulator.
            </summary>
            <param name="eventId"></param>
            <param name="dwData"></param>
            <param name="groupId"></param>
        </member>
        <member name="M:CTrue.FsConnect.IFsConnect.TransmitClientEvent(System.Int32,System.UInt32,System.Int32)">
            <summary>
            Sends a client event to flight simulator.
            </summary>
            <param name="eventId"></param>
            <param name="dwData"></param>
            <param name="groupId"></param>
        </member>
        <member name="M:CTrue.FsConnect.IFsConnect.MapClientEventToSimEvent(System.Enum,System.Enum,System.String)">
            <summary>
            Maps a client event to a sim event.
            </summary>
            <param name="groupId"></param>
            <param name="eventId"></param>
            <param name="eventName"></param>
        </member>
        <member name="M:CTrue.FsConnect.IFsConnect.MapClientEventToSimEvent(System.Enum,System.Enum,CTrue.FsConnect.FsEventNameId)">
            <summary>
            Maps a client event to a sim event.
            </summary>
            <param name="groupId"></param>
            <param name="eventId"></param>
            <param name="eventNameId"></param>
        </member>
        <member name="M:CTrue.FsConnect.IFsConnect.MapClientEventToSimEvent(System.Int32,System.Int32,System.String)">
            <summary>
            Maps a client event to a sim event.
            </summary>
            <param name="groupId"></param>
            <param name="eventId"></param>
            <param name="eventName"></param>
        </member>
        <member name="M:CTrue.FsConnect.IFsConnect.MapClientEventToSimEvent(System.Int32,System.Int32,CTrue.FsConnect.FsEventNameId)">
            <summary>
            Maps a client event to a sim event.
            </summary>
            <param name="groupId"></param>
            <param name="eventId"></param>
            <param name="eventNameId"></param>
        </member>
        <member name="M:CTrue.FsConnect.IFsConnect.GetNextId">
            <summary>
            Gets the next id, for definitions and other SimConnect artifacts that require it.
            </summary>
            <returns>Returns an int that can be used to identifying SimConnect artifacts, such as definitions and events.</returns>
        </member>
        <member name="M:CTrue.FsConnect.IFsConnect.RegisterInputEvent(CTrue.FsConnect.InputEventInfo)">
            <summary>
            Registers handling of an input event.
            </summary>
            <param name="inputEventInfo"></param>
            <returns>Returns true if input event was registered, false if already registered.</returns>
        </member>
        <member name="T:CTrue.FsConnect.InputEventInfo">
            <summary>
            The <see cref="T:CTrue.FsConnect.InputEventInfo"/> is used to register a binding between an input event and client event.
            </summary>
        </member>
        <member name="M:CTrue.FsConnect.InputEventInfo.#ctor(System.Enum,System.Enum,System.Enum,System.String,CTrue.FsConnect.InputEventRaised)">
            <summary>
            Creates an <see cref="T:CTrue.FsConnect.InputEventInfo"/> instance.
            </summary>
            <param name="clientEventId"></param>
            <param name="notificationGroupId"></param>
            <param name="inputGroup"></param>
            <param name="inputDefinition"></param>
            <param name="inputEventRaisedDelegate"></param>
        </member>
        <member name="M:CTrue.FsConnect.InputEventInfo.RaiseInputEvent">
            <summary>
            Invokes the registered delegate.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.SimConnectFileLocation.MyDocuments">
            <summary>
            The SimConnect.cfg file will be written to the MyDocuments location.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.SimConnectFileLocation.Local">
            <summary>
            The SimConnect.cfg file will be written to the same location that the application was started from.
            </summary>
            <remarks>
            The application must have write access to this folder.
            </remarks>
        </member>
        <member name="T:CTrue.FsConnect.SimConnectProtocol">
            <summary>
            Specifies the protocol to use to connect to Flight Simulator.
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.SimConnectProtocol.Pipe">
            <summary>
            Named pipes
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.SimConnectProtocol.Ipv4">
            <summary>
            TCP.IP v4
            </summary>
        </member>
        <member name="F:CTrue.FsConnect.SimConnectProtocol.Ipv6">
            <summary>
            TCP.IP v6
            </summary>
        </member>
        <member name="T:CTrue.FsConnect.SimVar">
            <summary>
            Represents a simulation variable, used to define a data definition.
            </summary>
        </member>
        <member name="P:CTrue.FsConnect.SimVar.Name">
            <summary>
            The name of the definition. See the SimVars class in the Simvars example in the SDK.
            </summary>
        </member>
        <member name="P:CTrue.FsConnect.SimVar.Unit">
            <summary>
            The unit of the definition. See the Units class in the Simvars example in the SDK.
            </summary>
        </member>
        <member name="P:CTrue.FsConnect.SimVar.DataType">
            <summary>
            The data type of the definition.
            </summary>
        </member>
        <member name="M:CTrue.FsConnect.SimVar.#ctor">
            <summary>
            Creates an uninitialized instance of the <see cref="T:CTrue.FsConnect.SimVar"/> class.
            </summary>
        </member>
        <member name="M:CTrue.FsConnect.SimVar.#ctor(System.String,System.String,Microsoft.FlightSimulator.SimConnect.SIMCONNECT_DATATYPE)">
            <summary>
            Creates an initialized instance of the <see cref="T:CTrue.FsConnect.SimVar"/> class.
            </summary>
            <param name="simVarName"></param>
            <param name="unitName"></param>
            <param name="dataType"></param>
        </member>
        <member name="M:CTrue.FsConnect.SimVar.#ctor(System.String,CTrue.FsConnect.FsUnit,Microsoft.FlightSimulator.SimConnect.SIMCONNECT_DATATYPE)">
            <summary>
            Creates an initialized instance of the <see cref="T:CTrue.FsConnect.SimVar"/> class.
            </summary>
            <param name="simVarName"></param>
            <param name="unitId"></param>
            <param name="dataType"></param>
        </member>
        <member name="M:CTrue.FsConnect.SimVar.#ctor(CTrue.FsConnect.FsSimVar,System.String,Microsoft.FlightSimulator.SimConnect.SIMCONNECT_DATATYPE)">
            <summary>
            Creates an initialized instance of the <see cref="T:CTrue.FsConnect.SimVar"/> class.
            </summary>
            <param name="simVarId"></param>
            <param name="unitName"></param>
            <param name="dataType"></param>
        </member>
        <member name="M:CTrue.FsConnect.SimVar.#ctor(CTrue.FsConnect.FsSimVar,CTrue.FsConnect.FsUnit,Microsoft.FlightSimulator.SimConnect.SIMCONNECT_DATATYPE)">
            <summary>
            Creates an initialized instance using enums for known values.
            </summary>
            <param name="simVarId"></param>
            <param name="unitId"></param>
            <param name="dataType"></param>
        </member>
        <member name="M:CTrue.FsConnect.SimVar.ToString">
            <summary>
            Gets a textual representation of the SimVar.
            </summary>
            <returns></returns>
        </member>
        <member name="T:CTrue.FsConnect.SimVarAttribute">
            <summary>
            The <see cref="T:CTrue.FsConnect.SimVarAttribute"/> decorates a field in a SimVar structure so that the correct data definition can be generated automatically.
            </summary>
        </member>
        <member name="P:CTrue.FsConnect.SimVarAttribute.Name">
            <summary>
            The name of the SimVar.
            </summary>
        </member>
        <member name="P:CTrue.FsConnect.SimVarAttribute.NameId">
            <summary>
            The id of the SimVar, see the <see cref="T:CTrue.FsConnect.FsSimVar"/> enum for supported ids.
            </summary>
        </member>
        <member name="P:CTrue.FsConnect.SimVarAttribute.Instance">
            <summary>
            The instance id of the SimVar, such as the engine for a SimVar that can return data for individual instances.
            </summary>
        </member>
        <member name="P:CTrue.FsConnect.SimVarAttribute.Unit">
            <summary>
            The unit name of the SimVar.
            </summary>
        </member>
        <member name="P:CTrue.FsConnect.SimVarAttribute.UnitId">
            <summary>
            The unit id of the SimVar, see the <see cref="T:CTrue.FsConnect.FsUnit"/> enum for supported id.
            </summary>
        </member>
        <member name="P:CTrue.FsConnect.SimVarAttribute.DataType">
            <summary>
            The SimConnect data type representing the SimVar data.
            </summary>
        </member>
        <member name="T:CTrue.FsConnect.SimVarReflector">
            <summary>
            The <see cref="T:CTrue.FsConnect.SimVarReflector"/> analyzes a type and determines a SimVar definition based on types, property names and the use of the <see cref="T:CTrue.FsConnect.SimVarAttribute"/>.
            </summary>
        </member>
        <member name="M:CTrue.FsConnect.SimVarReflector.GetSimVars``1">
            <summary>
            Gets a collection of <see cref="T:CTrue.FsConnect.SimVar"/> instances based on reflection of the provided stuct.
            </summary>
            <typeparam name="T">The struct</typeparam>
            <returns>A list of <see cref="T:CTrue.FsConnect.SimVar"/> instances.</returns>
        </member>
    </members>
</doc>
