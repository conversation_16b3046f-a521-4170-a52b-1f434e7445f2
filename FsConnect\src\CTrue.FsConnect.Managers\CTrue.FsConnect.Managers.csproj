<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <LangVersion>8.0</LangVersion>
    <Product>Flight Simulator Connect Managers</Product>
    <Copyright />
    <Description>
      Managers that supports common operations working with Microsoft Flightsimulator 2020, through the use of FsConnect.
      Supports:
        - Aircraft information
        - Retrieving sim objects
        - Setting world time
        - Controlling COM and NAV frequencies
        - Controlling transponder code
        - Initial Autopilot manager
    </Description>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <PackageLicenseFile>LICENSE.txt</PackageLicenseFile>
    <PackageRequireLicenseAcceptance>true</PackageRequireLicenseAcceptance>
    <PackageTags>msfs flight-simulator simconnect</PackageTags>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <PackageReleaseNotes>Updated SDK version to ******** * Upgraded to .NET 6.0 with C# 8.0 support</PackageReleaseNotes>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <PlatformTarget>x64</PlatformTarget>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <PlatformTarget>x64</PlatformTarget>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\CTrue.FsConnect\CTrue.FsConnect.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="Microsoft.FlightSimulator.SimConnect">
      <HintPath>..\Dependencies\SimConnect\lib\net461\Microsoft.FlightSimulator.SimConnect.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <None Include="licenses\LICENSE.txt" Pack="true" PackagePath="$(PackageLicenseFile)" />
  </ItemGroup>

</Project>
