System.Globalization.Native.dylib|Microsoft.NETCore.App.Ref||0.0.0.0
System.Globalization.Native.so|Microsoft.NETCore.App.Ref||0.0.0.0
System.IO.Compression.Native.a|Microsoft.NETCore.App.Ref||0.0.0.0
System.IO.Compression.Native.dylib|Microsoft.NETCore.App.Ref||0.0.0.0
System.IO.Compression.Native.so|Microsoft.NETCore.App.Ref||0.0.0.0
System.Native.a|Microsoft.NETCore.App.Ref||0.0.0.0
System.Native.dylib|Microsoft.NETCore.App.Ref||0.0.0.0
System.Native.so|Microsoft.NETCore.App.Ref||0.0.0.0
System.Net.Http.Native.a|Microsoft.NETCore.App.Ref||0.0.0.0
System.Net.Http.Native.dylib|Microsoft.NETCore.App.Ref||0.0.0.0
System.Net.Http.Native.so|Microsoft.NETCore.App.Ref||0.0.0.0
System.Net.Security.Native.a|Microsoft.NETCore.App.Ref||0.0.0.0
System.Net.Security.Native.dylib|Microsoft.NETCore.App.Ref||0.0.0.0
System.Net.Security.Native.so|Microsoft.NETCore.App.Ref||0.0.0.0
System.Security.Cryptography.Native.Apple.a|Microsoft.NETCore.App.Ref||0.0.0.0
System.Security.Cryptography.Native.Apple.dylib|Microsoft.NETCore.App.Ref||0.0.0.0
System.Security.Cryptography.Native.OpenSsl.a|Microsoft.NETCore.App.Ref||0.0.0.0
System.Security.Cryptography.Native.OpenSsl.dylib|Microsoft.NETCore.App.Ref||0.0.0.0
System.Security.Cryptography.Native.OpenSsl.so|Microsoft.NETCore.App.Ref||0.0.0.0
clrcompression.dll|Microsoft.NETCore.App.Ref||6.0.21.52210
Microsoft.CSharp.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
Microsoft.VisualBasic.Core.dll|Microsoft.NETCore.App.Ref|11.0.0.0|6.0.21.52210
Microsoft.Win32.Primitives.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
Microsoft.Win32.Registry.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.AppContext.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Buffers.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Collections.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Collections.Concurrent.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Collections.Immutable.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Collections.NonGeneric.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Collections.Specialized.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.ComponentModel.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.ComponentModel.Annotations.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.ComponentModel.EventBasedAsync.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.ComponentModel.Primitives.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.ComponentModel.TypeConverter.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Console.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Data.Common.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Data.DataSetExtensions.dll|Microsoft.NETCore.App.Ref|4.0.0.0|6.0.21.52210
System.Diagnostics.Contracts.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Diagnostics.Debug.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Diagnostics.DiagnosticSource.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Diagnostics.FileVersionInfo.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Diagnostics.Process.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Diagnostics.StackTrace.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Diagnostics.TextWriterTraceListener.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Diagnostics.Tools.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Diagnostics.TraceSource.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Diagnostics.Tracing.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Drawing.Primitives.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Dynamic.Runtime.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Formats.Asn1.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Globalization.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Globalization.Calendars.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Globalization.Extensions.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.IO.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.IO.Compression.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.IO.Compression.Brotli.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.IO.Compression.ZipFile.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.IO.FileSystem.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.IO.FileSystem.AccessControl.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.IO.FileSystem.DriveInfo.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.IO.FileSystem.Primitives.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.IO.FileSystem.Watcher.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.IO.IsolatedStorage.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.IO.MemoryMappedFiles.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.IO.Pipes.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.IO.Pipes.AccessControl.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.IO.UnmanagedMemoryStream.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Linq.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Linq.Expressions.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Linq.Parallel.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Linq.Queryable.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Memory.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Net.Http.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Net.Http.Json.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Net.HttpListener.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Net.Mail.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Net.NameResolution.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Net.NetworkInformation.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Net.Ping.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Net.Primitives.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Net.Quic.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Net.Requests.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Net.Security.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Net.ServicePoint.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Net.Sockets.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Net.WebClient.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Net.WebHeaderCollection.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Net.WebProxy.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Net.WebSockets.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Net.WebSockets.Client.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Numerics.Vectors.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.ObjectModel.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Private.DataContractSerialization.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Private.Runtime.InteropServices.JavaScript.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Private.Uri.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Private.Xml.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Private.Xml.Linq.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Reflection.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Reflection.DispatchProxy.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Reflection.Emit.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Reflection.Emit.ILGeneration.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Reflection.Emit.Lightweight.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Reflection.Extensions.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Reflection.Metadata.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Reflection.Primitives.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Reflection.TypeExtensions.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Resources.Reader.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Resources.ResourceManager.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Resources.Writer.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Runtime.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Runtime.CompilerServices.Unsafe.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Runtime.CompilerServices.VisualC.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Runtime.Extensions.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Runtime.Handles.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Runtime.InteropServices.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Runtime.InteropServices.RuntimeInformation.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Runtime.Intrinsics.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Runtime.Loader.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Runtime.Numerics.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Runtime.Serialization.Formatters.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Runtime.Serialization.Json.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Runtime.Serialization.Primitives.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Runtime.Serialization.Xml.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Security.AccessControl.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Security.Claims.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Security.Cryptography.Algorithms.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Security.Cryptography.Cng.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Security.Cryptography.Csp.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Security.Cryptography.Encoding.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Security.Cryptography.OpenSsl.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Security.Cryptography.Primitives.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Security.Cryptography.X509Certificates.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Security.Principal.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Security.Principal.Windows.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Security.SecureString.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Text.Encoding.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Text.Encoding.CodePages.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Text.Encoding.Extensions.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Text.Encodings.Web.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Text.Json.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Text.RegularExpressions.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Threading.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Threading.Channels.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Threading.Overlapped.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Threading.Tasks.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Threading.Tasks.Dataflow.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Threading.Tasks.Extensions.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Threading.Tasks.Parallel.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Threading.Thread.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Threading.ThreadPool.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Threading.Timer.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Transactions.Local.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.ValueTuple.dll|Microsoft.NETCore.App.Ref|4.0.3.0|6.0.21.52210
System.Web.HttpUtility.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Xml.ReaderWriter.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Xml.XDocument.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Xml.XmlDocument.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Xml.XmlSerializer.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Xml.XPath.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Xml.XPath.XDocument.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
mscorlib.dll|Microsoft.NETCore.App.Ref|4.0.0.0|6.0.21.52210
Microsoft.VisualBasic.dll|Microsoft.NETCore.App.Ref|10.0.0.0|6.0.21.52210
System.dll|Microsoft.NETCore.App.Ref|4.0.0.0|6.0.21.52210
System.ComponentModel.DataAnnotations.dll|Microsoft.NETCore.App.Ref|4.0.0.0|6.0.21.52210
System.Configuration.dll|Microsoft.NETCore.App.Ref|4.0.0.0|6.0.21.52210
System.Core.dll|Microsoft.NETCore.App.Ref|4.0.0.0|6.0.21.52210
System.Data.dll|Microsoft.NETCore.App.Ref|4.0.0.0|6.0.21.52210
System.Drawing.dll|Microsoft.NETCore.App.Ref|4.0.0.0|6.0.21.52210
System.IO.Compression.FileSystem.dll|Microsoft.NETCore.App.Ref|4.0.0.0|6.0.21.52210
System.Net.dll|Microsoft.NETCore.App.Ref|4.0.0.0|6.0.21.52210
System.Numerics.dll|Microsoft.NETCore.App.Ref|4.0.0.0|6.0.21.52210
System.Runtime.Serialization.dll|Microsoft.NETCore.App.Ref|4.0.0.0|6.0.21.52210
System.Security.dll|Microsoft.NETCore.App.Ref|4.0.0.0|6.0.21.52210
System.ServiceProcess.dll|Microsoft.NETCore.App.Ref|4.0.0.0|6.0.21.52210
System.ServiceModel.Web.dll|Microsoft.NETCore.App.Ref|4.0.0.0|6.0.21.52210
System.Transactions.dll|Microsoft.NETCore.App.Ref|4.0.0.0|6.0.21.52210
System.Web.dll|Microsoft.NETCore.App.Ref|4.0.0.0|6.0.21.52210
System.Windows.dll|Microsoft.NETCore.App.Ref|4.0.0.0|6.0.21.52210
System.Xml.dll|Microsoft.NETCore.App.Ref|4.0.0.0|6.0.21.52210
System.Xml.Serialization.dll|Microsoft.NETCore.App.Ref|4.0.0.0|6.0.21.52210
System.Xml.Linq.dll|Microsoft.NETCore.App.Ref|4.0.0.0|6.0.21.52210
WindowsBase.dll|Microsoft.NETCore.App.Ref|4.0.0.0|6.0.21.52210
netstandard.dll|Microsoft.NETCore.App.Ref|2.1.0.0|6.0.21.52210
System.Private.CoreLib.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Private.DataContractSerialization.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Private.Uri.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Private.Xml.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
System.Private.Xml.Linq.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
libSystem.Globalization.Native.dylib|Microsoft.NETCore.App.Ref||0.0.0.0
libSystem.Globalization.Native.so|Microsoft.NETCore.App.Ref||0.0.0.0
libSystem.IO.Compression.Native.a|Microsoft.NETCore.App.Ref||0.0.0.0
libSystem.IO.Compression.Native.dylib|Microsoft.NETCore.App.Ref||0.0.0.0
libSystem.IO.Compression.Native.so|Microsoft.NETCore.App.Ref||0.0.0.0
libSystem.Native.a|Microsoft.NETCore.App.Ref||0.0.0.0
libSystem.Native.dylib|Microsoft.NETCore.App.Ref||0.0.0.0
libSystem.Native.so|Microsoft.NETCore.App.Ref||0.0.0.0
libSystem.Net.Http.Native.a|Microsoft.NETCore.App.Ref||0.0.0.0
libSystem.Net.Http.Native.dylib|Microsoft.NETCore.App.Ref||0.0.0.0
libSystem.Net.Http.Native.so|Microsoft.NETCore.App.Ref||0.0.0.0
libSystem.Net.Security.Native.a|Microsoft.NETCore.App.Ref||0.0.0.0
libSystem.Net.Security.Native.dylib|Microsoft.NETCore.App.Ref||0.0.0.0
libSystem.Net.Security.Native.so|Microsoft.NETCore.App.Ref||0.0.0.0
libSystem.Security.Cryptography.Native.Apple.a|Microsoft.NETCore.App.Ref||0.0.0.0
libSystem.Security.Cryptography.Native.Apple.dylib|Microsoft.NETCore.App.Ref||0.0.0.0
libSystem.Security.Cryptography.Native.Android.a|Microsoft.NETCore.App.Ref||0.0.0.0
libSystem.Security.Cryptography.Native.Android.so|Microsoft.NETCore.App.Ref||0.0.0.0
libSystem.Security.Cryptography.Native.OpenSsl.a|Microsoft.NETCore.App.Ref||0.0.0.0
libSystem.Security.Cryptography.Native.OpenSsl.dylib|Microsoft.NETCore.App.Ref||0.0.0.0
libSystem.Security.Cryptography.Native.OpenSsl.so|Microsoft.NETCore.App.Ref||0.0.0.0
coreclr.dll|Microsoft.NETCore.App.Ref||6.0.21.52210
libcoreclr.so|Microsoft.NETCore.App.Ref||0.0.0.0
libcoreclr.dylib|Microsoft.NETCore.App.Ref||0.0.0.0
clretwrc.dll|Microsoft.NETCore.App.Ref||6.0.21.52210
clrjit.dll|Microsoft.NETCore.App.Ref||6.0.21.52210
libclrjit.so|Microsoft.NETCore.App.Ref||0.0.0.0
libclrjit.dylib|Microsoft.NETCore.App.Ref||0.0.0.0
dbgshim.dll|Microsoft.NETCore.App.Ref||6.0.21.52210
libdbgshim.so|Microsoft.NETCore.App.Ref||0.0.0.0
libdbgshim.dylib|Microsoft.NETCore.App.Ref||0.0.0.0
mscordaccore.dll|Microsoft.NETCore.App.Ref||6.0.21.52210
libmscordaccore.so|Microsoft.NETCore.App.Ref||0.0.0.0
libmscordaccore.dylib|Microsoft.NETCore.App.Ref||0.0.0.0
mscordbi.dll|Microsoft.NETCore.App.Ref||6.0.21.52210
libmscordbi.so|Microsoft.NETCore.App.Ref||0.0.0.0
libmscordbi.dylib|Microsoft.NETCore.App.Ref||0.0.0.0
mscorrc.dll|Microsoft.NETCore.App.Ref||6.0.21.52210
api-ms-win-core-console-l1-1-0.dll|Microsoft.NETCore.App.Ref||10.0.19041.1
api-ms-win-core-console-l1-2-0.dll|Microsoft.NETCore.App.Ref||10.0.19041.1
api-ms-win-core-datetime-l1-1-0.dll|Microsoft.NETCore.App.Ref||10.0.19041.1
api-ms-win-core-debug-l1-1-0.dll|Microsoft.NETCore.App.Ref||10.0.19041.1
api-ms-win-core-errorhandling-l1-1-0.dll|Microsoft.NETCore.App.Ref||10.0.19041.1
api-ms-win-core-file-l1-1-0.dll|Microsoft.NETCore.App.Ref||10.0.19041.1
api-ms-win-core-file-l1-2-0.dll|Microsoft.NETCore.App.Ref||10.0.19041.1
api-ms-win-core-file-l2-1-0.dll|Microsoft.NETCore.App.Ref||10.0.19041.1
api-ms-win-core-handle-l1-1-0.dll|Microsoft.NETCore.App.Ref||10.0.19041.1
api-ms-win-core-heap-l1-1-0.dll|Microsoft.NETCore.App.Ref||10.0.19041.1
api-ms-win-core-interlocked-l1-1-0.dll|Microsoft.NETCore.App.Ref||10.0.19041.1
********************************-1-0.dll|Microsoft.NETCore.App.Ref||10.0.19041.1
api-ms-win-core-localization-l1-2-0.dll|Microsoft.NETCore.App.Ref||10.0.19041.1
api-ms-win-core-memory-l1-1-0.dll|Microsoft.NETCore.App.Ref||10.0.19041.1
api-ms-win-core-namedpipe-l1-1-0.dll|Microsoft.NETCore.App.Ref||10.0.19041.1
api-ms-win-core-processenvironment-l1-1-0.dll|Microsoft.NETCore.App.Ref||10.0.19041.1
api-ms-win-core-processthreads-l1-1-0.dll|Microsoft.NETCore.App.Ref||10.0.19041.1
api-ms-win-core-processthreads-l1-1-1.dll|Microsoft.NETCore.App.Ref||10.0.19041.1
api-ms-win-core-profile-l1-1-0.dll|Microsoft.NETCore.App.Ref||10.0.19041.1
api-ms-win-core-rtlsupport-l1-1-0.dll|Microsoft.NETCore.App.Ref||10.0.19041.1
api-ms-win-core-string-l1-1-0.dll|Microsoft.NETCore.App.Ref||10.0.19041.1
api-ms-win-core-synch-l1-1-0.dll|Microsoft.NETCore.App.Ref||10.0.19041.1
api-ms-win-core-synch-l1-2-0.dll|Microsoft.NETCore.App.Ref||10.0.19041.1
api-ms-win-core-sysinfo-l1-1-0.dll|Microsoft.NETCore.App.Ref||10.0.19041.1
api-ms-win-core-timezone-l1-1-0.dll|Microsoft.NETCore.App.Ref||10.0.19041.1
api-ms-win-core-util-l1-1-0.dll|Microsoft.NETCore.App.Ref||10.0.19041.1
api-ms-win-crt-conio-l1-1-0.dll|Microsoft.NETCore.App.Ref||10.0.19041.1
api-ms-win-crt-convert-l1-1-0.dll|Microsoft.NETCore.App.Ref||10.0.19041.1
api-ms-win-crt-environment-l1-1-0.dll|Microsoft.NETCore.App.Ref||10.0.19041.1
api-ms-win-crt-filesystem-l1-1-0.dll|Microsoft.NETCore.App.Ref||10.0.19041.1
api-ms-win-crt-heap-l1-1-0.dll|Microsoft.NETCore.App.Ref||10.0.19041.1
api-ms-win-crt-locale-l1-1-0.dll|Microsoft.NETCore.App.Ref||10.0.19041.1
api-ms-win-crt-math-l1-1-0.dll|Microsoft.NETCore.App.Ref||10.0.19041.1
api-ms-win-crt-multibyte-l1-1-0.dll|Microsoft.NETCore.App.Ref||10.0.19041.1
api-ms-win-crt-private-l1-1-0.dll|Microsoft.NETCore.App.Ref||10.0.19041.1
api-ms-win-crt-process-l1-1-0.dll|Microsoft.NETCore.App.Ref||10.0.19041.1
api-ms-win-crt-runtime-l1-1-0.dll|Microsoft.NETCore.App.Ref||10.0.19041.1
api-ms-win-crt-stdio-l1-1-0.dll|Microsoft.NETCore.App.Ref||10.0.19041.1
api-ms-win-crt-string-l1-1-0.dll|Microsoft.NETCore.App.Ref||10.0.19041.1
api-ms-win-crt-time-l1-1-0.dll|Microsoft.NETCore.App.Ref||10.0.19041.1
api-ms-win-crt-utility-l1-1-0.dll|Microsoft.NETCore.App.Ref||10.0.19041.1
API-MS-Win-core-xstate-l2-1-0.dll|Microsoft.NETCore.App.Ref||10.0.19041.1
ucrtbase.dll|Microsoft.NETCore.App.Ref||6.0.21.52210
msquic.dll|Microsoft.NETCore.App.Ref||1.8.0.0
System.IO.Compression.Native.dll|Microsoft.NETCore.App.Ref||6.0.21.52210
createdump.exe|Microsoft.NETCore.App.Ref||6.0.21.52210
createdump|Microsoft.NETCore.App.Ref||0.0.0.0
libcoreclrtraceptprovider.so|Microsoft.NETCore.App.Ref||0.0.0.0
Microsoft.DiaSymReader.Native.x86.dll|Microsoft.NETCore.App.Ref||14.28.29715.1
Microsoft.DiaSymReader.Native.amd64.dll|Microsoft.NETCore.App.Ref||14.28.29715.1
Microsoft.DiaSymReader.Native.arm.dll|Microsoft.NETCore.App.Ref||14.28.29715.1
Microsoft.DiaSymReader.Native.arm64.dll|Microsoft.NETCore.App.Ref||14.28.29715.1
libmonosgen-2.0.lib|Microsoft.NETCore.App.Ref||0.0.0.0
libmonosgen-2.0.a|Microsoft.NETCore.App.Ref||0.0.0.0
libmonosgen-2.0.so|Microsoft.NETCore.App.Ref||0.0.0.0
libmonosgen-2.0.dylib|Microsoft.NETCore.App.Ref||0.0.0.0
llc|Microsoft.NETCore.App.Ref||0.0.0.0
mono-aot-cross|Microsoft.NETCore.App.Ref||0.0.0.0
mono-aot-cross.exe|Microsoft.NETCore.App.Ref||6.0.21.52210
opt|Microsoft.NETCore.App.Ref||0.0.0.0
libmono-component-diagnostics_tracing.dll|Microsoft.NETCore.App.Ref||6.0.21.52210
libmono-component-diagnostics_tracing.so|Microsoft.NETCore.App.Ref||0.0.0.0
libmono-component-diagnostics_tracing.dylib|Microsoft.NETCore.App.Ref||0.0.0.0
libmono-component-diagnostics_tracing-static.a|Microsoft.NETCore.App.Ref||0.0.0.0
libmono-component-diagnostics_tracing-stub-static.a|Microsoft.NETCore.App.Ref||0.0.0.0
libmono-component-diagnostics_tracing-static.lib|Microsoft.NETCore.App.Ref||0.0.0.0
libmono-component-diagnostics_tracing-stub-static.lib|Microsoft.NETCore.App.Ref||0.0.0.0
libmono-component-hot_reload.dll|Microsoft.NETCore.App.Ref||6.0.21.52210
libmono-component-hot_reload.so|Microsoft.NETCore.App.Ref||0.0.0.0
libmono-component-hot_reload.dylib|Microsoft.NETCore.App.Ref||0.0.0.0
libmono-component-hot_reload-static.a|Microsoft.NETCore.App.Ref||0.0.0.0
libmono-component-hot_reload-stub-static.a|Microsoft.NETCore.App.Ref||0.0.0.0
libmono-component-hot_reload-static.lib|Microsoft.NETCore.App.Ref||0.0.0.0
libmono-component-hot_reload-stub-static.lib|Microsoft.NETCore.App.Ref||0.0.0.0
libmono-component-debugger.dll|Microsoft.NETCore.App.Ref||6.0.21.52210
libmono-component-debugger.so|Microsoft.NETCore.App.Ref||0.0.0.0
libmono-component-debugger.dylib|Microsoft.NETCore.App.Ref||0.0.0.0
libmono-component-debugger-static.a|Microsoft.NETCore.App.Ref||0.0.0.0
libmono-component-debugger-stub-static.a|Microsoft.NETCore.App.Ref||0.0.0.0
libmono-component-debugger-static.lib|Microsoft.NETCore.App.Ref||0.0.0.0
libmono-component-debugger-stub-static.lib|Microsoft.NETCore.App.Ref||0.0.0.0
libmono-ee-interp.a|Microsoft.NETCore.App.Ref||0.0.0.0
libmono-icall-table.a|Microsoft.NETCore.App.Ref||0.0.0.0
libmono-ilgen.a|Microsoft.NETCore.App.Ref||0.0.0.0
libmono-profiler-aot.a|Microsoft.NETCore.App.Ref||0.0.0.0
System.Private.Runtime.InteropServices.Javascript.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
dotnet.js|Microsoft.NETCore.App.Ref||0.0.0.0
dotnet.wasm|Microsoft.NETCore.App.Ref||0.0.0.0
dotnet.timezones.blat|Microsoft.NETCore.App.Ref||0.0.0.0
icudt.dat|Microsoft.NETCore.App.Ref||0.0.0.0
icudt_no_CJK.dat|Microsoft.NETCore.App.Ref||0.0.0.0
icudt_CJK.dat|Microsoft.NETCore.App.Ref||0.0.0.0
icudt_EFIGS.dat|Microsoft.NETCore.App.Ref||0.0.0.0
icudt_optimal.dat|Microsoft.NETCore.App.Ref||0.0.0.0
icudt_optimal_no_CJK.dat|Microsoft.NETCore.App.Ref||0.0.0.0
binding_support.js|Microsoft.NETCore.App.Ref||0.0.0.0
dotnet_support.js|Microsoft.NETCore.App.Ref||0.0.0.0
library_mono.js|Microsoft.NETCore.App.Ref||0.0.0.0
pal_random.js|Microsoft.NETCore.App.Ref||0.0.0.0
corebindings.c|Microsoft.NETCore.App.Ref||0.0.0.0
driver.c|Microsoft.NETCore.App.Ref||0.0.0.0
pinvoke.c|Microsoft.NETCore.App.Ref||0.0.0.0
pinvoke.h|Microsoft.NETCore.App.Ref||0.0.0.0
emcc-default.rsp|Microsoft.NETCore.App.Ref||0.0.0.0
emcc-props.json|Microsoft.NETCore.App.Ref||0.0.0.0
libicudata.a|Microsoft.NETCore.App.Ref||0.0.0.0
libicui18n.a|Microsoft.NETCore.App.Ref||0.0.0.0
libicuuc.a|Microsoft.NETCore.App.Ref||0.0.0.0
mscordaccore_amd64_amd64_6.0.21.52210.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
mscordaccore_amd64_amd64_6.0.21.52210.dll|Microsoft.NETCore.App.Ref|*******|6.0.21.52210
