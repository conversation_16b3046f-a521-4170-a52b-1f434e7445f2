{"version": 2, "dgSpecHash": "UbruZoc30xk=", "success": false, "projectFilePath": "C:\\dev\\Personal\\FlightPig\\FsConnect\\src\\CTrue.FsConnect.Test\\CTrue.FsConnect.Test.csproj", "expectedPackageFiles": [], "logs": [{"code": "NU1301", "level": "Error", "message": "The local source 'C:\\dev\\Personal\\FlightPig\\FsConnect\\artifacts\\packages' doesn't exist.", "libraryId": "NUnit"}, {"code": "NU1301", "level": "Error", "message": "The local source 'C:\\dev\\Personal\\FlightPig\\FsConnect\\artifacts\\packages' doesn't exist.", "libraryId": "Microsoft.NET.Test.Sdk"}, {"code": "NU1301", "level": "Error", "message": "The local source 'C:\\dev\\Personal\\FlightPig\\FsConnect\\artifacts\\packages' doesn't exist.", "libraryId": "NUnit3TestAdapter"}]}