<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.AspNetCore.Mvc.Abstractions</name>
    </assembly>
    <members>
        <member name="T:Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptor">
            <summary>
            Describes an MVC action.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptor.#ctor">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptor"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptor.Id">
            <summary>
            Gets an id which uniquely identifies the action.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptor.RouteValues">
            <summary>
            Gets or sets the collection of route values that must be provided by routing
            for the action to be selected.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptor.AttributeRouteInfo">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Mvc.Routing.AttributeRouteInfo"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptor.ActionConstraints">
            <summary>
            The set of constraints for this action. Must all be satisfied for the action to be selected.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptor.EndpointMetadata">
            <summary>
            Gets or sets the endpoint metadata for this action.
            This API is meant for infrastructure and should not be used by application code.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptor.Parameters">
            <summary>
            The set of parameters associated with this action.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptor.BoundProperties">
            <summary>
            The set of properties which are model bound.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptor.FilterDescriptors">
            <summary>
            The set of filters associated with this action.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptor.DisplayName">
            <summary>
            A friendly name for this action.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptor.Properties">
            <summary>
            Stores arbitrary metadata properties associated with the <see cref="T:Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptor"/>.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptorExtensions">
            <summary>
            Extension methods for <see cref="T:Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptor"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptorExtensions.GetProperty``1(Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptor)">
            <summary>
            Gets the value of a property from the <see cref="P:Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptor.Properties"/> collection
            using the provided value of <typeparamref name="T"/> as the key.
            </summary>
            <typeparam name="T">The type of the property.</typeparam>
            <param name="actionDescriptor">The action descriptor.</param>
            <returns>The property or the default value of <typeparamref name="T"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptorExtensions.SetProperty``1(Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptor,``0)">
            <summary>
            Sets the value of an property in the <see cref="P:Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptor.Properties"/> collection using
            the provided value of <typeparamref name="T"/> as the key.
            </summary>
            <typeparam name="T">The type of the property.</typeparam>
            <param name="actionDescriptor">The action descriptor.</param>
            <param name="value">The value of the property.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptorProviderContext">
            <summary>
            A context for <see cref="T:Microsoft.AspNetCore.Mvc.Abstractions.IActionDescriptorProvider"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptorProviderContext.Results">
            <summary>
            Gets the <see cref="T:System.Collections.Generic.IList`1" /> of <see cref="T:Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptor"/> instances of <see cref="T:Microsoft.AspNetCore.Mvc.Abstractions.IActionDescriptorProvider"/>
            can populate.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Abstractions.ActionInvokerProviderContext">
            <summary>
            A context for <see cref="T:Microsoft.AspNetCore.Mvc.Abstractions.IActionInvokerProvider"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Abstractions.ActionInvokerProviderContext.#ctor(Microsoft.AspNetCore.Mvc.ActionContext)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Mvc.Abstractions.ActionInvokerProviderContext"/>.
            </summary>
            <param name="actionContext">The <see cref="T:Microsoft.AspNetCore.Mvc.ActionContext"/> to invoke.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Abstractions.ActionInvokerProviderContext.ActionContext">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Mvc.ActionContext"/> to invoke.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Abstractions.ActionInvokerProviderContext.Result">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Mvc.Abstractions.IActionInvoker"/> that will be used to invoke <see cref="P:Microsoft.AspNetCore.Mvc.Abstractions.ActionInvokerProviderContext.ActionContext" />
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Abstractions.IActionDescriptorProvider">
            <summary>
            Defines a contract for specifying <see cref="T:Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptor"/> instances.
            </summary>
            <remarks>
            <para>
            On application initialization, MVC invokes all registered instances of <see cref="T:Microsoft.AspNetCore.Mvc.Abstractions.IActionDescriptorProvider"/> to
            perform <see cref="T:Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptor" /> discovery.
            <see cref="T:Microsoft.AspNetCore.Mvc.Abstractions.IActionDescriptorProvider"/> instances are invoked in the ascending sort order of <see cref="P:Microsoft.AspNetCore.Mvc.Abstractions.IActionDescriptorProvider.Order"/>.
            </para>
            <para>
            Each provider has its <see cref="M:Microsoft.AspNetCore.Mvc.Abstractions.IActionDescriptorProvider.OnProvidersExecuting(Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptorProviderContext)"/> method
            called in sequence and given the same instance of <see cref="T:Microsoft.AspNetCore.Mvc.Abstractions.ActionInvokerProviderContext"/>. Then each
            provider has its <see cref="M:Microsoft.AspNetCore.Mvc.Abstractions.IActionDescriptorProvider.OnProvidersExecuted(Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptorProviderContext)"/> method called in the reverse order. Each instance has
            an opportunity to add or modify <see cref="P:Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptorProviderContext.Results"/>.
            </para>
            <para>
            As providers are called in a predefined sequence, each provider has a chance to observe and decorate the
            result of the providers that have already run.
            </para>
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Abstractions.IActionDescriptorProvider.Order">
            <summary>
            Gets the order value for determining the order of execution of providers. Providers execute in
            ascending numeric value of the <see cref="P:Microsoft.AspNetCore.Mvc.Abstractions.IActionDescriptorProvider.Order"/> property.
            </summary>
            <remarks>
            <para>
            Providers are executed in an ordering determined by an ascending sort of the <see cref="P:Microsoft.AspNetCore.Mvc.Abstractions.IActionDescriptorProvider.Order"/> property.
            A provider with a lower numeric value of <see cref="P:Microsoft.AspNetCore.Mvc.Abstractions.IActionDescriptorProvider.Order"/> will have its
            <see cref="M:Microsoft.AspNetCore.Mvc.Abstractions.IActionDescriptorProvider.OnProvidersExecuting(Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptorProviderContext)"/> called before that of a provider with a higher numeric value of
            <see cref="P:Microsoft.AspNetCore.Mvc.Abstractions.IActionDescriptorProvider.Order"/>. The <see cref="M:Microsoft.AspNetCore.Mvc.Abstractions.IActionDescriptorProvider.OnProvidersExecuted(Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptorProviderContext)"/> method is called in the reverse ordering after
            all calls to <see cref="M:Microsoft.AspNetCore.Mvc.Abstractions.IActionDescriptorProvider.OnProvidersExecuting(Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptorProviderContext)"/>. A provider with a lower numeric value of
            <see cref="P:Microsoft.AspNetCore.Mvc.Abstractions.IActionDescriptorProvider.Order"/> will have its <see cref="M:Microsoft.AspNetCore.Mvc.Abstractions.IActionDescriptorProvider.OnProvidersExecuted(Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptorProviderContext)"/> method called after that of a provider
            with a higher numeric value of <see cref="P:Microsoft.AspNetCore.Mvc.Abstractions.IActionDescriptorProvider.Order"/>.
            </para>
            <para>
            If two providers have the same numeric value of <see cref="P:Microsoft.AspNetCore.Mvc.Abstractions.IActionDescriptorProvider.Order"/>, then their relative execution order
            is undefined.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Abstractions.IActionDescriptorProvider.OnProvidersExecuting(Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptorProviderContext)">
            <summary>
            Called to execute the provider.
            <see cref="P:Microsoft.AspNetCore.Mvc.Abstractions.IActionDescriptorProvider.Order"/> for details on the order of execution of <see cref="M:Microsoft.AspNetCore.Mvc.Abstractions.IActionDescriptorProvider.OnProvidersExecuting(Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptorProviderContext)"/>.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptorProviderContext"/>.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Abstractions.IActionDescriptorProvider.OnProvidersExecuted(Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptorProviderContext)">
            <summary>
            Called to execute the provider, after the <see cref="M:Microsoft.AspNetCore.Mvc.Abstractions.IActionDescriptorProvider.OnProvidersExecuting(Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptorProviderContext)"/> methods of all providers,
            have been called.
            <see cref="P:Microsoft.AspNetCore.Mvc.Abstractions.IActionDescriptorProvider.Order"/> for details on the order of execution of <see cref="M:Microsoft.AspNetCore.Mvc.Abstractions.IActionDescriptorProvider.OnProvidersExecuted(Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptorProviderContext)"/>.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptorProviderContext"/>.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Abstractions.IActionInvoker">
            <summary>
            Defines an interface for invoking an MVC action.
            </summary>
            <remarks>
            An <see cref="T:Microsoft.AspNetCore.Mvc.Abstractions.IActionInvoker"/> is created for each request the MVC handles by querying the set of
            <see cref="T:Microsoft.AspNetCore.Mvc.Abstractions.IActionInvokerProvider"/> instances. See <see cref="T:Microsoft.AspNetCore.Mvc.Abstractions.IActionInvokerProvider"/> for more information.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Abstractions.IActionInvoker.InvokeAsync">
            <summary>
            Invokes an MVC action.
            </summary>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> which will complete when action processing has completed.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Abstractions.IActionInvokerProvider">
            <summary>
            Defines an interface for components that can create an <see cref="T:Microsoft.AspNetCore.Mvc.Abstractions.IActionInvoker"/> for the
            current request.
            </summary>
            <remarks>
            <para>
            <see cref="T:Microsoft.AspNetCore.Mvc.Abstractions.IActionInvokerProvider"/> instances form a pipeline that results in the creation of an
            <see cref="T:Microsoft.AspNetCore.Mvc.Abstractions.IActionInvoker"/>. The <see cref="T:Microsoft.AspNetCore.Mvc.Abstractions.IActionInvokerProvider"/> instances are ordered by
            an ascending sort of the <see cref="P:Microsoft.AspNetCore.Mvc.Abstractions.IActionInvokerProvider.Order"/>.
            </para>
            <para>
            To create an <see cref="T:Microsoft.AspNetCore.Mvc.Abstractions.IActionInvoker"/>, each provider has its <see cref="M:Microsoft.AspNetCore.Mvc.Abstractions.IActionInvokerProvider.OnProvidersExecuting(Microsoft.AspNetCore.Mvc.Abstractions.ActionInvokerProviderContext)"/> method
            called in sequence and given the same instance of <see cref="T:Microsoft.AspNetCore.Mvc.Abstractions.ActionInvokerProviderContext"/>. Then each
            provider has its <see cref="M:Microsoft.AspNetCore.Mvc.Abstractions.IActionInvokerProvider.OnProvidersExecuted(Microsoft.AspNetCore.Mvc.Abstractions.ActionInvokerProviderContext)"/> method called in the reverse order. The result is
            the value of <see cref="P:Microsoft.AspNetCore.Mvc.Abstractions.ActionInvokerProviderContext.Result"/>.
            </para>
            <para>
            As providers are called in a predefined sequence, each provider has a chance to observe and decorate the
            result of the providers that have already run.
            </para>
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Abstractions.IActionInvokerProvider.Order">
            <summary>
            Gets the order value for determining the order of execution of providers. Providers execute in
            ascending numeric value of the <see cref="P:Microsoft.AspNetCore.Mvc.Abstractions.IActionInvokerProvider.Order"/> property.
            </summary>
            <remarks>
            <para>
            Providers are executed in an ordering determined by an ascending sort of the <see cref="P:Microsoft.AspNetCore.Mvc.Abstractions.IActionInvokerProvider.Order"/> property.
            A provider with a lower numeric value of <see cref="P:Microsoft.AspNetCore.Mvc.Abstractions.IActionInvokerProvider.Order"/> will have its
            <see cref="M:Microsoft.AspNetCore.Mvc.Abstractions.IActionInvokerProvider.OnProvidersExecuting(Microsoft.AspNetCore.Mvc.Abstractions.ActionInvokerProviderContext)"/> called before that of a provider with a higher numeric value of
            <see cref="P:Microsoft.AspNetCore.Mvc.Abstractions.IActionInvokerProvider.Order"/>. The <see cref="M:Microsoft.AspNetCore.Mvc.Abstractions.IActionInvokerProvider.OnProvidersExecuted(Microsoft.AspNetCore.Mvc.Abstractions.ActionInvokerProviderContext)"/> method is called in the reverse ordering after
            all calls to <see cref="M:Microsoft.AspNetCore.Mvc.Abstractions.IActionInvokerProvider.OnProvidersExecuting(Microsoft.AspNetCore.Mvc.Abstractions.ActionInvokerProviderContext)"/>. A provider with a lower numeric value of
            <see cref="P:Microsoft.AspNetCore.Mvc.Abstractions.IActionInvokerProvider.Order"/> will have its <see cref="M:Microsoft.AspNetCore.Mvc.Abstractions.IActionInvokerProvider.OnProvidersExecuted(Microsoft.AspNetCore.Mvc.Abstractions.ActionInvokerProviderContext)"/> method called after that of a provider
            with a higher numeric value of <see cref="P:Microsoft.AspNetCore.Mvc.Abstractions.IActionInvokerProvider.Order"/>.
            </para>
            <para>
            If two providers have the same numeric value of <see cref="P:Microsoft.AspNetCore.Mvc.Abstractions.IActionInvokerProvider.Order"/>, then their relative execution order
            is undefined.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Abstractions.IActionInvokerProvider.OnProvidersExecuting(Microsoft.AspNetCore.Mvc.Abstractions.ActionInvokerProviderContext)">
            <summary>
            Called to execute the provider.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Mvc.Abstractions.ActionInvokerProviderContext"/>.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Abstractions.IActionInvokerProvider.OnProvidersExecuted(Microsoft.AspNetCore.Mvc.Abstractions.ActionInvokerProviderContext)">
            <summary>
            Called to execute the provider, after the <see cref="M:Microsoft.AspNetCore.Mvc.Abstractions.IActionInvokerProvider.OnProvidersExecuting(Microsoft.AspNetCore.Mvc.Abstractions.ActionInvokerProviderContext)"/> methods of all providers,
            have been called.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Mvc.Abstractions.ActionInvokerProviderContext"/>.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor">
            <summary>
            Describes a parameter in an action.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor.Name">
            <summary>
            Gets or sets the parameter name.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor.ParameterType">
            <summary>
            Gets or sets the type of the parameter.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Abstractions.ParameterDescriptor.BindingInfo">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.BindingInfo"/> for the parameter.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Abstractions.Resources.ArgumentCannotBeNullOrEmpty">
            <summary>Value cannot be null or empty.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Abstractions.Resources.ModelBindingContext_ModelMetadataMustBeSet">
            <summary>The ModelMetadata property must be set before accessing this property.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Abstractions.Resources.Validation_InvalidFieldCannotBeReset">
            <summary>A field previously marked invalid should not be marked valid.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Abstractions.Resources.Validation_InvalidFieldCannotBeReset_ToSkipped">
            <summary>A field previously marked invalid should not be marked skipped.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Abstractions.Resources.ModelStateDictionary_MaxModelStateErrors">
            <summary>The maximum number of allowed model errors has been reached.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Abstractions.Resources.BindingSource_Body">
            <summary>Body</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Abstractions.Resources.BindingSource_Custom">
            <summary>Custom</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Abstractions.Resources.BindingSource_Form">
            <summary>Form</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Abstractions.Resources.BindingSource_Header">
            <summary>Header</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Abstractions.Resources.BindingSource_Services">
            <summary>Services</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Abstractions.Resources.BindingSource_ModelBinding">
            <summary>ModelBinding</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Abstractions.Resources.BindingSource_Path">
            <summary>Path</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Abstractions.Resources.BindingSource_Query">
            <summary>Query</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Abstractions.Resources.BindingSource_CannotBeComposite">
            <summary>The provided binding source '{0}' is a composite. '{1}' requires that the source must represent a single type of input.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Abstractions.Resources.FormatBindingSource_CannotBeComposite(System.Object,System.Object)">
            <summary>The provided binding source '{0}' is a composite. '{1}' requires that the source must represent a single type of input.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Abstractions.Resources.BindingSource_MustBeFromRequest">
            <summary>The provided binding source '{0}' is not a request-based binding source. '{1}' requires that the source must represent data from an HTTP request.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Abstractions.Resources.FormatBindingSource_MustBeFromRequest(System.Object,System.Object)">
            <summary>The provided binding source '{0}' is not a request-based binding source. '{1}' requires that the source must represent data from an HTTP request.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Abstractions.Resources.BindingSource_CannotBeGreedy">
            <summary>The provided binding source '{0}' is a greedy data source. '{1}' does not support greedy data sources.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Abstractions.Resources.FormatBindingSource_CannotBeGreedy(System.Object,System.Object)">
            <summary>The provided binding source '{0}' is a greedy data source. '{1}' does not support greedy data sources.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Abstractions.Resources.BindingSource_MustBeGreedy">
            <summary>The provided binding source '{0}' is not a greedy data source. '{1}' only supports greedy data sources.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Abstractions.Resources.FormatBindingSource_MustBeGreedy(System.Object,System.Object)">
            <summary>The provided binding source '{0}' is not a greedy data source. '{1}' only supports greedy data sources.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Abstractions.Resources.BindingSource_Special">
            <summary>Special</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Abstractions.Resources.BindingSource_FormFile">
            <summary>FormFile</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Abstractions.Resources.BinderType_MustBeIModelBinder">
            <summary>The type '{0}' must implement '{1}' to be used as a model binder.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Abstractions.Resources.FormatBinderType_MustBeIModelBinder(System.Object,System.Object)">
            <summary>The type '{0}' must implement '{1}' to be used as a model binder.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Abstractions.Resources.RecordTypeHasValidationOnProperties">
            <summary>Record type '{0}' has validation metadata defined on property '{1}' that will be ignored. '{1}' is a parameter in the record primary constructor and validation metadata must be associated with the constructor parameter.</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Abstractions.Resources.FormatRecordTypeHasValidationOnProperties(System.Object,System.Object)">
            <summary>Record type '{0}' has validation metadata defined on property '{1}' that will be ignored. '{1}' is a parameter in the record primary constructor and validation metadata must be associated with the constructor parameter.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Abstractions.Resources.ModelStateDictionary_MaxModelStateDepth">
            <summary>The specified key exceeded the maximum ModelState depth: {0}</summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Abstractions.Resources.FormatModelStateDictionary_MaxModelStateDepth(System.Object)">
            <summary>The specified key exceeded the maximum ModelState depth: {0}</summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ActionConstraints.ActionConstraintContext">
            <summary>
            Context for <see cref="T:Microsoft.AspNetCore.Mvc.ActionConstraints.IActionConstraint"/> execution.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ActionConstraints.ActionConstraintContext.Candidates">
            <summary>
            The list of <see cref="T:Microsoft.AspNetCore.Mvc.ActionConstraints.ActionSelectorCandidate"/>. This includes all actions that are valid for the current
            request, as well as their constraints.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ActionConstraints.ActionConstraintContext.CurrentCandidate">
            <summary>
            The current <see cref="T:Microsoft.AspNetCore.Mvc.ActionConstraints.ActionSelectorCandidate"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ActionConstraints.ActionConstraintContext.RouteContext">
            <summary>
            The <see cref="P:Microsoft.AspNetCore.Mvc.ActionConstraints.ActionConstraintContext.RouteContext"/>.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ActionConstraints.ActionConstraintItem">
            <summary>
            Represents an <see cref="T:Microsoft.AspNetCore.Mvc.ActionConstraints.IActionConstraintMetadata"/> with or without a corresponding
            <see cref="T:Microsoft.AspNetCore.Mvc.ActionConstraints.IActionConstraint"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ActionConstraints.ActionConstraintItem.#ctor(Microsoft.AspNetCore.Mvc.ActionConstraints.IActionConstraintMetadata)">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Mvc.ActionConstraints.ActionConstraintItem"/>.
            </summary>
            <param name="metadata">The <see cref="T:Microsoft.AspNetCore.Mvc.ActionConstraints.IActionConstraintMetadata"/> instance.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ActionConstraints.ActionConstraintItem.Constraint">
            <summary>
            The <see cref="T:Microsoft.AspNetCore.Mvc.ActionConstraints.IActionConstraint"/> associated with <see cref="P:Microsoft.AspNetCore.Mvc.ActionConstraints.ActionConstraintItem.Metadata"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ActionConstraints.ActionConstraintItem.Metadata">
            <summary>
            The <see cref="T:Microsoft.AspNetCore.Mvc.ActionConstraints.IActionConstraintMetadata"/> instance.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ActionConstraints.ActionConstraintItem.IsReusable">
            <summary>
            Gets or sets a value indicating whether or not <see cref="P:Microsoft.AspNetCore.Mvc.ActionConstraints.ActionConstraintItem.Constraint"/> can be reused across requests.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ActionConstraints.ActionConstraintProviderContext">
            <summary>
            Context for an action constraint provider.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ActionConstraints.ActionConstraintProviderContext.#ctor(Microsoft.AspNetCore.Http.HttpContext,Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptor,System.Collections.Generic.IList{Microsoft.AspNetCore.Mvc.ActionConstraints.ActionConstraintItem})">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Mvc.ActionConstraints.ActionConstraintProviderContext"/>.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Http.HttpContext"/> associated with the request.</param>
            <param name="action">The <see cref="T:Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptor"/> for which constraints are being created.</param>
            <param name="items">The list of <see cref="T:Microsoft.AspNetCore.Mvc.ActionConstraints.ActionConstraintItem"/> objects.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ActionConstraints.ActionConstraintProviderContext.HttpContext">
            <summary>
            The <see cref="T:Microsoft.AspNetCore.Http.HttpContext"/> associated with the request.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ActionConstraints.ActionConstraintProviderContext.Action">
            <summary>
            The <see cref="T:Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptor"/> for which constraints are being created.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ActionConstraints.ActionConstraintProviderContext.Results">
            <summary>
            The list of <see cref="T:Microsoft.AspNetCore.Mvc.ActionConstraints.ActionConstraintItem"/> objects.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ActionConstraints.ActionSelectorCandidate">
            <summary>
            A candidate action for action selection.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ActionConstraints.ActionSelectorCandidate.#ctor(Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptor,System.Collections.Generic.IReadOnlyList{Microsoft.AspNetCore.Mvc.ActionConstraints.IActionConstraint})">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Mvc.ActionConstraints.ActionSelectorCandidate"/>.
            </summary>
            <param name="action">The <see cref="T:Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptor"/> representing a candidate for selection.</param>
            <param name="constraints">
            The list of <see cref="T:Microsoft.AspNetCore.Mvc.ActionConstraints.IActionConstraint"/> instances associated with <paramref name="action"/>.
            </param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ActionConstraints.ActionSelectorCandidate.Action">
            <summary>
            The <see cref="T:Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptor"/> representing a candidate for selection.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ActionConstraints.ActionSelectorCandidate.Constraints">
            <summary>
            The list of <see cref="T:Microsoft.AspNetCore.Mvc.ActionConstraints.IActionConstraint"/> instances associated with <see name="Action"/>.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ActionConstraints.IActionConstraint">
             <summary>
             Supports conditional logic to determine whether or not an associated action is valid to be selected
             for the given request.
             </summary>
             <remarks>
             Action constraints have the secondary effect of making an action with a constraint applied a better
             match than one without.
            
             Consider two actions, 'A' and 'B' with the same action and controller name. Action 'A' only allows the
             HTTP POST method (via a constraint) and action 'B' has no constraints.
            
             If an incoming request is a POST, then 'A' is considered the best match because it both matches and
             has a constraint. If an incoming request uses any other verb, 'A' will not be valid for selection
             due to it's constraint, so 'B' is the best match.
            
            
             Action constraints are also grouped according to their order value. Any constraints with the same
             group value are considered to be part of the same application policy, and will be executed in the
             same stage.
            
             Stages run in ascending order based on the value of <see cref="P:Microsoft.AspNetCore.Mvc.ActionConstraints.IActionConstraint.Order"/>. Given a set of actions which
             are candidates for selection, the next stage to run is the lowest value of <see cref="P:Microsoft.AspNetCore.Mvc.ActionConstraints.IActionConstraint.Order"/> for any
             constraint of any candidate which is greater than the order of the last stage.
            
             Once the stage order is identified, each action has all of its constraints in that stage executed.
             If any constraint does not match, then that action is not a candidate for selection. If any actions
             with constraints in the current state are still candidates, then those are the 'best' actions and this
             process will repeat with the next stage on the set of 'best' actions. If after processing the
             subsequent stages of the 'best' actions no candidates remain, this process will repeat on the set of
             'other' candidate actions from this stage (those without a constraint).
             </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ActionConstraints.IActionConstraint.Order">
            <summary>
            The constraint order.
            </summary>
            <remarks>
            Constraints are grouped into stages by the value of <see cref="P:Microsoft.AspNetCore.Mvc.ActionConstraints.IActionConstraint.Order"/>. See remarks on
            <see cref="T:Microsoft.AspNetCore.Mvc.ActionConstraints.IActionConstraint"/>.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ActionConstraints.IActionConstraint.Accept(Microsoft.AspNetCore.Mvc.ActionConstraints.ActionConstraintContext)">
            <summary>
            Determines whether an action is a valid candidate for selection.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Mvc.ActionConstraints.ActionConstraintContext"/>.</param>
            <returns>True if the action is valid for selection, otherwise false.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ActionConstraints.IActionConstraintFactory">
             <summary>
             A factory for <see cref="T:Microsoft.AspNetCore.Mvc.ActionConstraints.IActionConstraint"/>.
             </summary>
             <remarks>
             <see cref="T:Microsoft.AspNetCore.Mvc.ActionConstraints.IActionConstraintFactory"/> will be invoked during action selection
             to create constraint instances for an action.
            
             Place an attribute implementing this interface on a controller or action to insert an action
             constraint created by a factory.
             </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ActionConstraints.IActionConstraintFactory.IsReusable">
            <summary>
            Gets a value that indicates if the result of <see cref="M:Microsoft.AspNetCore.Mvc.ActionConstraints.IActionConstraintFactory.CreateInstance(System.IServiceProvider)"/>
            can be reused across requests.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ActionConstraints.IActionConstraintFactory.CreateInstance(System.IServiceProvider)">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Mvc.ActionConstraints.IActionConstraint"/>.
            </summary>
            <param name="services">The per-request services.</param>
            <returns>An <see cref="T:Microsoft.AspNetCore.Mvc.ActionConstraints.IActionConstraint"/>.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ActionConstraints.IActionConstraintMetadata">
            <summary>
            A marker interface that identifies a type as metadata for an <see cref="T:Microsoft.AspNetCore.Mvc.ActionConstraints.IActionConstraint"/>.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ActionConstraints.IActionConstraintProvider">
            <summary>
            Provider for <see cref="T:Microsoft.AspNetCore.Mvc.ActionConstraints.IActionConstraint"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ActionConstraints.IActionConstraintProvider.Order">
            <summary>
            Gets the order value for determining the order of execution of providers. Providers execute in
            ascending numeric value of the <see cref="P:Microsoft.AspNetCore.Mvc.ActionConstraints.IActionConstraintProvider.Order"/> property.
            </summary>
            <remarks>
            <para>
            Providers are executed in an ordering determined by an ascending sort of the <see cref="P:Microsoft.AspNetCore.Mvc.ActionConstraints.IActionConstraintProvider.Order"/> property.
            A provider with a lower numeric value of <see cref="P:Microsoft.AspNetCore.Mvc.ActionConstraints.IActionConstraintProvider.Order"/> will have its
            <see cref="M:Microsoft.AspNetCore.Mvc.ActionConstraints.IActionConstraintProvider.OnProvidersExecuting(Microsoft.AspNetCore.Mvc.ActionConstraints.ActionConstraintProviderContext)"/> called before that of a provider with a higher numeric value of
            <see cref="P:Microsoft.AspNetCore.Mvc.ActionConstraints.IActionConstraintProvider.Order"/>. The <see cref="M:Microsoft.AspNetCore.Mvc.ActionConstraints.IActionConstraintProvider.OnProvidersExecuted(Microsoft.AspNetCore.Mvc.ActionConstraints.ActionConstraintProviderContext)"/> method is called in the reverse ordering after
            all calls to <see cref="M:Microsoft.AspNetCore.Mvc.ActionConstraints.IActionConstraintProvider.OnProvidersExecuting(Microsoft.AspNetCore.Mvc.ActionConstraints.ActionConstraintProviderContext)"/>. A provider with a lower numeric value of
            <see cref="P:Microsoft.AspNetCore.Mvc.ActionConstraints.IActionConstraintProvider.Order"/> will have its <see cref="M:Microsoft.AspNetCore.Mvc.ActionConstraints.IActionConstraintProvider.OnProvidersExecuted(Microsoft.AspNetCore.Mvc.ActionConstraints.ActionConstraintProviderContext)"/> method called after that of a provider
            with a higher numeric value of <see cref="P:Microsoft.AspNetCore.Mvc.ActionConstraints.IActionConstraintProvider.Order"/>.
            </para>
            <para>
            If two providers have the same numeric value of <see cref="P:Microsoft.AspNetCore.Mvc.ActionConstraints.IActionConstraintProvider.Order"/>, then their relative execution order
            is undefined.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ActionConstraints.IActionConstraintProvider.OnProvidersExecuting(Microsoft.AspNetCore.Mvc.ActionConstraints.ActionConstraintProviderContext)">
            <summary>
            Called to execute the provider.
            <see cref="P:Microsoft.AspNetCore.Mvc.ActionConstraints.IActionConstraintProvider.Order"/> for details on the order of execution of <see cref="M:Microsoft.AspNetCore.Mvc.ActionConstraints.IActionConstraintProvider.OnProvidersExecuting(Microsoft.AspNetCore.Mvc.ActionConstraints.ActionConstraintProviderContext)"/>.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Mvc.ActionConstraints.ActionConstraintProviderContext"/>.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ActionConstraints.IActionConstraintProvider.OnProvidersExecuted(Microsoft.AspNetCore.Mvc.ActionConstraints.ActionConstraintProviderContext)">
            <summary>
            Called to execute the provider, after the <see cref="M:Microsoft.AspNetCore.Mvc.ActionConstraints.IActionConstraintProvider.OnProvidersExecuting(Microsoft.AspNetCore.Mvc.ActionConstraints.ActionConstraintProviderContext)"/> methods of all providers,
            have been called.
            <see cref="P:Microsoft.AspNetCore.Mvc.ActionConstraints.IActionConstraintProvider.Order"/> for details on the order of execution of <see cref="M:Microsoft.AspNetCore.Mvc.ActionConstraints.IActionConstraintProvider.OnProvidersExecuted(Microsoft.AspNetCore.Mvc.ActionConstraints.ActionConstraintProviderContext)"/>.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Mvc.ActionConstraints.ActionConstraintProviderContext"/>.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ActionContext">
            <summary>
            Context object for execution of action which has been selected as part of an HTTP request.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ActionContext.#ctor">
            <summary>
            Creates an empty <see cref="T:Microsoft.AspNetCore.Mvc.ActionContext"/>.
            </summary>
            <remarks>
            The default constructor is provided for unit test purposes only.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ActionContext.#ctor(Microsoft.AspNetCore.Mvc.ActionContext)">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Mvc.ActionContext"/>.
            </summary>
            <param name="actionContext">The <see cref="T:Microsoft.AspNetCore.Mvc.ActionContext"/> to copy.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ActionContext.#ctor(Microsoft.AspNetCore.Http.HttpContext,Microsoft.AspNetCore.Routing.RouteData,Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptor)">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Mvc.ActionContext"/>.
            </summary>
            <param name="httpContext">The <see cref="T:Microsoft.AspNetCore.Http.HttpContext"/> for the current request.</param>
            <param name="routeData">The <see cref="T:Microsoft.AspNetCore.Routing.RouteData"/> for the current request.</param>
            <param name="actionDescriptor">The <see cref="T:Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptor"/> for the selected action.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ActionContext.#ctor(Microsoft.AspNetCore.Http.HttpContext,Microsoft.AspNetCore.Routing.RouteData,Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptor,Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary)">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Mvc.ActionContext"/>.
            </summary>
            <param name="httpContext">The <see cref="T:Microsoft.AspNetCore.Http.HttpContext"/> for the current request.</param>
            <param name="routeData">The <see cref="T:Microsoft.AspNetCore.Routing.RouteData"/> for the current request.</param>
            <param name="actionDescriptor">The <see cref="T:Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptor"/> for the selected action.</param>
            <param name="modelState">The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary"/>.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ActionContext.ActionDescriptor">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptor"/> for the selected action.
            </summary>
            <remarks>
            The property setter is provided for unit test purposes only.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ActionContext.HttpContext">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Http.HttpContext"/> for the current request.
            </summary>
            <remarks>
            The property setter is provided for unit test purposes only.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ActionContext.ModelState">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ActionContext.RouteData">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Routing.RouteData"/> for the current request.
            </summary>
            <remarks>
            The property setter is provided for unit test purposes only.
            </remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiDescription">
            <summary>
            Represents an API exposed by this application.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiDescription.ActionDescriptor">
            <summary>
            Gets or sets <see cref="P:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiDescription.ActionDescriptor"/> for this api.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiDescription.GroupName">
            <summary>
            Gets or sets group name for this api.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiDescription.HttpMethod">
            <summary>
            Gets or sets the supported HTTP method for this api, or null if all HTTP methods are supported.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiDescription.ParameterDescriptions">
            <summary>
            Gets a list of <see cref="T:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiParameterDescription"/> for this api.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiDescription.Properties">
            <summary>
            Gets arbitrary metadata properties associated with the <see cref="T:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiDescription"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiDescription.RelativePath">
            <summary>
            Gets or sets relative url path template (relative to application root) for this api.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiDescription.SupportedRequestFormats">
            <summary>
            Gets the list of possible formats for a request.
            </summary>
            <remarks>
            Will be empty if the action does not accept a parameter decorated with the <c>[FromBody]</c> attribute.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiDescription.SupportedResponseTypes">
            <summary>
            Gets the list of possible formats for a response.
            </summary>
            <remarks>
            Will be empty if the action returns no response, or if the response type is unclear. Use
            <c>ProducesAttribute</c> on an action method to specify a response type.
            </remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiDescriptionProviderContext">
            <summary>
            A context object for <see cref="T:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiDescription"/> providers.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiDescriptionProviderContext.#ctor(System.Collections.Generic.IReadOnlyList{Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptor})">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiDescriptionProviderContext"/>.
            </summary>
            <param name="actions">The list of actions.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiDescriptionProviderContext.Actions">
            <summary>
            The list of actions.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiDescriptionProviderContext.Results">
            <summary>
            The list of resulting <see cref="T:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiDescription"/>.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiParameterDescription">
            <summary>
            A metadata description of an input to an API.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiParameterDescription.ModelMetadata">
            <summary>
            Gets or sets the <see cref="P:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiParameterDescription.ModelMetadata"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiParameterDescription.Name">
            <summary>
            Gets or sets the name.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiParameterDescription.RouteInfo">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiParameterRouteInfo"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiParameterDescription.Source">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiParameterDescription.BindingInfo">
            <summary>
            Gets or sets the <see cref="P:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiParameterDescription.BindingInfo"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiParameterDescription.Type">
            <summary>
            Gets or sets the parameter type.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiParameterDescription.ParameterDescriptor">
            <summary>
            Gets or sets the parameter descriptor.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiParameterDescription.IsRequired">
            <summary>
            Gets or sets a value that determines if the parameter is required.
            </summary>
            <remarks>
            A parameter is considered required if
            <list type="bullet">
            <item>it's bound from the request body (<see cref="F:Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource.Body"/>).</item>
            <item>it's a required route value.</item>
            <item>it has annotations (e.g. BindRequiredAttribute) that indicate it's required.</item>
            </list>
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiParameterDescription.DefaultValue">
            <summary>
            Gets or sets the default value for a parameter.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiParameterRouteInfo">
            <summary>
            A metadata description of routing information for an <see cref="T:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiParameterDescription"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiParameterRouteInfo.Constraints">
            <summary>
            Gets or sets the set of <see cref="T:Microsoft.AspNetCore.Routing.IRouteConstraint"/> objects for the parameter.
            </summary>
            <remarks>
            Route constraints are only applied when a value is bound from a URL's path. See
            <see cref="P:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiParameterDescription.Source"/> for the data source considered.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiParameterRouteInfo.DefaultValue">
            <summary>
            Gets or sets the default value for the parameter.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiParameterRouteInfo.IsOptional">
             <summary>
             Gets a value indicating whether not a parameter is considered optional by routing.
             </summary>
             <remarks>
             An optional parameter is considered optional by the routing system. This does not imply
             that the parameter is considered optional by the action.
            
             If the parameter uses <see cref="F:Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource.ModelBinding"/> for the value of
             <see cref="P:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiParameterDescription.Source"/> then the value may also come from the
             URL query string or form data.
             </remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiRequestFormat">
            <summary>
            A possible format for the body of a request.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiRequestFormat.Formatter">
            <summary>
            The formatter used to read this request.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiRequestFormat.MediaType">
            <summary>
            The media type of the request.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiResponseFormat">
            <summary>
            Possible format for an <see cref="T:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiResponseType"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiResponseFormat.Formatter">
            <summary>
            Gets or sets the formatter used to output this response.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiResponseFormat.MediaType">
            <summary>
            Gets or sets the media type of the response.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiResponseType">
            <summary>
            Possible type of the response body which is formatted by <see cref="P:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiResponseType.ApiResponseFormats"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiResponseType.ApiResponseFormats">
            <summary>
            Gets or sets the response formats supported by this type.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiResponseType.ModelMetadata">
            <summary>
            Gets or sets <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/> for the <see cref="P:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiResponseType.Type"/> or null.
            </summary>
            <remarks>
            Will be null if <see cref="P:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiResponseType.Type"/> is null or void.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiResponseType.Type">
            <summary>
            Gets or sets the CLR data type of the response or null.
            </summary>
            <remarks>
            Will be null if the action returns no response, or if the response type is unclear. Use
            <c>Microsoft.AspNetCore.Mvc.ProducesAttribute</c> or <c>Microsoft.AspNetCore.Mvc.ProducesResponseTypeAttribute</c> on an action method
            to specify a response type.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiResponseType.StatusCode">
            <summary>
            Gets or sets the HTTP response status code.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiResponseType.IsDefaultResponse">
            <summary>
            Gets or sets a value indicating whether the response type represents a default response.
            </summary>
            <remarks>
            If an <see cref="T:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiDescription"/> has a default response, then the <see cref="P:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiResponseType.StatusCode"/> property should be ignored. This response
            will be used when a more specific response format does not apply. The common use of a default response is to specify the format
            for communicating error conditions.
            </remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ApiExplorer.IApiDescriptionProvider">
            <summary>
            Defines a contract for specifying <see cref="T:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiDescription"/> instances.
            </summary>
            <remarks>
            <para>
            On the first query for <see cref="T:Microsoft.AspNetCore.Mvc.Abstractions.ActionDescriptor"/>, MVC invokes all registered instances of <see cref="T:Microsoft.AspNetCore.Mvc.ApiExplorer.IApiDescriptionProvider"/>
            in the ascending sort order of <see cref="P:Microsoft.AspNetCore.Mvc.ApiExplorer.IApiDescriptionProvider.Order"/>.
            </para>
            <para>
            Each provider has its <see cref="M:Microsoft.AspNetCore.Mvc.ApiExplorer.IApiDescriptionProvider.OnProvidersExecuting(Microsoft.AspNetCore.Mvc.ApiExplorer.ApiDescriptionProviderContext)"/> method
            called in sequence and given the same instance of <see cref="T:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiDescriptionProviderContext"/>. Then each
            provider has its <see cref="M:Microsoft.AspNetCore.Mvc.ApiExplorer.IApiDescriptionProvider.OnProvidersExecuted(Microsoft.AspNetCore.Mvc.ApiExplorer.ApiDescriptionProviderContext)"/> method called in the reverse order. Each instance has
            an opportunity to add or modify <see cref="P:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiDescriptionProviderContext.Results"/>.
            </para>
            <para>
            As providers are called in a predefined sequence, each provider has a chance to observe and decorate the
            result of the providers that have already run.
            </para>
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ApiExplorer.IApiDescriptionProvider.Order">
            <summary>
            Gets the order value for determining the order of execution of providers. Providers execute in
            ascending numeric value of the <see cref="P:Microsoft.AspNetCore.Mvc.ApiExplorer.IApiDescriptionProvider.Order"/> property.
            </summary>
            <remarks>
            <para>
            Providers are executed in an ordering determined by an ascending sort of the <see cref="P:Microsoft.AspNetCore.Mvc.ApiExplorer.IApiDescriptionProvider.Order"/> property.
            A provider with a lower numeric value of <see cref="P:Microsoft.AspNetCore.Mvc.ApiExplorer.IApiDescriptionProvider.Order"/> will have its
            <see cref="M:Microsoft.AspNetCore.Mvc.ApiExplorer.IApiDescriptionProvider.OnProvidersExecuting(Microsoft.AspNetCore.Mvc.ApiExplorer.ApiDescriptionProviderContext)"/> called before that of a provider with a higher numeric value of
            <see cref="P:Microsoft.AspNetCore.Mvc.ApiExplorer.IApiDescriptionProvider.Order"/>. The <see cref="M:Microsoft.AspNetCore.Mvc.ApiExplorer.IApiDescriptionProvider.OnProvidersExecuted(Microsoft.AspNetCore.Mvc.ApiExplorer.ApiDescriptionProviderContext)"/> method is called in the reverse ordering after
            all calls to <see cref="M:Microsoft.AspNetCore.Mvc.ApiExplorer.IApiDescriptionProvider.OnProvidersExecuting(Microsoft.AspNetCore.Mvc.ApiExplorer.ApiDescriptionProviderContext)"/>. A provider with a lower numeric value of
            <see cref="P:Microsoft.AspNetCore.Mvc.ApiExplorer.IApiDescriptionProvider.Order"/> will have its <see cref="M:Microsoft.AspNetCore.Mvc.ApiExplorer.IApiDescriptionProvider.OnProvidersExecuted(Microsoft.AspNetCore.Mvc.ApiExplorer.ApiDescriptionProviderContext)"/> method called after that of a provider
            with a higher numeric value of <see cref="P:Microsoft.AspNetCore.Mvc.ApiExplorer.IApiDescriptionProvider.Order"/>.
            </para>
            <para>
            If two providers have the same numeric value of <see cref="P:Microsoft.AspNetCore.Mvc.ApiExplorer.IApiDescriptionProvider.Order"/>, then their relative execution order
            is undefined.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ApiExplorer.IApiDescriptionProvider.OnProvidersExecuting(Microsoft.AspNetCore.Mvc.ApiExplorer.ApiDescriptionProviderContext)">
            <summary>
            Creates or modifies <see cref="T:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiDescription"/>s.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiDescriptionProviderContext"/>.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ApiExplorer.IApiDescriptionProvider.OnProvidersExecuted(Microsoft.AspNetCore.Mvc.ApiExplorer.ApiDescriptionProviderContext)">
            <summary>
            Called after <see cref="T:Microsoft.AspNetCore.Mvc.ApiExplorer.IApiDescriptionProvider"/> implementations with higher <see cref="P:Microsoft.AspNetCore.Mvc.ApiExplorer.IApiDescriptionProvider.Order"/> values have been called.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Mvc.ApiExplorer.ApiDescriptionProviderContext"/>.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Authorization.IAllowAnonymousFilter">
            <summary>
            A filter that allows anonymous requests, disabling some <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IAuthorizationFilter"/>s.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Filters.ActionExecutedContext">
            <summary>
            A context for action filters, specifically <see cref="M:Microsoft.AspNetCore.Mvc.Filters.IActionFilter.OnActionExecuted(Microsoft.AspNetCore.Mvc.Filters.ActionExecutedContext)"/> calls.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Filters.ActionExecutedContext.#ctor(Microsoft.AspNetCore.Mvc.ActionContext,System.Collections.Generic.IList{Microsoft.AspNetCore.Mvc.Filters.IFilterMetadata},System.Object)">
            <summary>
            Instantiates a new <see cref="T:Microsoft.AspNetCore.Mvc.Filters.ActionExecutingContext"/> instance.
            </summary>
            <param name="actionContext">The <see cref="T:Microsoft.AspNetCore.Mvc.ActionContext"/>.</param>
            <param name="filters">All applicable <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IFilterMetadata"/> implementations.</param>
            <param name="controller">The controller instance containing the action.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.ActionExecutedContext.Canceled">
            <summary>
            Gets or sets an indication that an action filter short-circuited the action and the action filter pipeline.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.ActionExecutedContext.Controller">
            <summary>
            Gets the controller instance containing the action.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.ActionExecutedContext.Exception">
            <summary>
            Gets or sets the <see cref="T:System.Exception"/> caught while executing the action or action filters, if
            any.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.ActionExecutedContext.ExceptionDispatchInfo">
            <summary>
            Gets or sets the <see cref="T:System.Runtime.ExceptionServices.ExceptionDispatchInfo"/> for the
            <see cref="P:Microsoft.AspNetCore.Mvc.Filters.ActionExecutedContext.Exception"/>, if an <see cref="T:System.Exception"/> was caught and this information captured.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.ActionExecutedContext.ExceptionHandled">
            <summary>
            Gets or sets an indication that the <see cref="P:Microsoft.AspNetCore.Mvc.Filters.ActionExecutedContext.Exception"/> has been handled.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.ActionExecutedContext.Result">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Mvc.IActionResult"/>.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Filters.ActionExecutingContext">
            <summary>
            A context for action filters, specifically <see cref="M:Microsoft.AspNetCore.Mvc.Filters.IActionFilter.OnActionExecuted(Microsoft.AspNetCore.Mvc.Filters.ActionExecutedContext)"/> and
            <see cref="M:Microsoft.AspNetCore.Mvc.Filters.IAsyncActionFilter.OnActionExecutionAsync(Microsoft.AspNetCore.Mvc.Filters.ActionExecutingContext,Microsoft.AspNetCore.Mvc.Filters.ActionExecutionDelegate)"/> calls.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Filters.ActionExecutingContext.#ctor(Microsoft.AspNetCore.Mvc.ActionContext,System.Collections.Generic.IList{Microsoft.AspNetCore.Mvc.Filters.IFilterMetadata},System.Collections.Generic.IDictionary{System.String,System.Object},System.Object)">
            <summary>
            Instantiates a new <see cref="T:Microsoft.AspNetCore.Mvc.Filters.ActionExecutingContext"/> instance.
            </summary>
            <param name="actionContext">The <see cref="T:Microsoft.AspNetCore.Mvc.ActionContext"/>.</param>
            <param name="filters">All applicable <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IFilterMetadata"/> implementations.</param>
            <param name="actionArguments">
            The arguments to pass when invoking the action. Keys are parameter names.
            </param>
            <param name="controller">The controller instance containing the action.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.ActionExecutingContext.Result">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Mvc.IActionResult"/> to execute. Setting <see cref="P:Microsoft.AspNetCore.Mvc.Filters.ActionExecutingContext.Result"/> to a non-<c>null</c>
            value inside an action filter will short-circuit the action and any remaining action filters.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.ActionExecutingContext.ActionArguments">
            <summary>
            Gets the arguments to pass when invoking the action. Keys are parameter names.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.ActionExecutingContext.Controller">
            <summary>
            Gets the controller instance containing the action.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Filters.ActionExecutionDelegate">
            <summary>
            A delegate that asynchronously returns an <see cref="T:Microsoft.AspNetCore.Mvc.Filters.ActionExecutedContext"/> indicating the action or the next
            action filter has executed.
            </summary>
            <returns>
            A <see cref="T:System.Threading.Tasks.Task"/> that on completion returns an <see cref="T:Microsoft.AspNetCore.Mvc.Filters.ActionExecutedContext"/>.
            </returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Filters.AuthorizationFilterContext">
            <summary>
            A context for authorization filters i.e. <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IAuthorizationFilter"/> and
            <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IAsyncAuthorizationFilter"/> implementations.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Filters.AuthorizationFilterContext.#ctor(Microsoft.AspNetCore.Mvc.ActionContext,System.Collections.Generic.IList{Microsoft.AspNetCore.Mvc.Filters.IFilterMetadata})">
            <summary>
            Instantiates a new <see cref="T:Microsoft.AspNetCore.Mvc.Filters.AuthorizationFilterContext"/> instance.
            </summary>
            <param name="actionContext">The <see cref="T:Microsoft.AspNetCore.Mvc.ActionContext"/>.</param>
            <param name="filters">All applicable <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IFilterMetadata"/> implementations.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.AuthorizationFilterContext.Result">
            <summary>
            Gets or sets the result of the request. Setting <see cref="P:Microsoft.AspNetCore.Mvc.Filters.AuthorizationFilterContext.Result"/> to a non-<c>null</c> value inside
            an authorization filter will short-circuit the remainder of the filter pipeline.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Filters.ExceptionContext">
            <summary>
            A context for exception filters i.e. <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IExceptionFilter"/> and
            <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IAsyncExceptionFilter"/> implementations.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Filters.ExceptionContext.#ctor(Microsoft.AspNetCore.Mvc.ActionContext,System.Collections.Generic.IList{Microsoft.AspNetCore.Mvc.Filters.IFilterMetadata})">
            <summary>
            Instantiates a new <see cref="T:Microsoft.AspNetCore.Mvc.Filters.ExceptionContext"/> instance.
            </summary>
            <param name="actionContext">The <see cref="T:Microsoft.AspNetCore.Mvc.ActionContext"/>.</param>
            <param name="filters">All applicable <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IFilterMetadata"/> implementations.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.ExceptionContext.Exception">
            <summary>
            Gets or sets the <see cref="T:System.Exception"/> caught while executing the action.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.ExceptionContext.ExceptionDispatchInfo">
            <summary>
            Gets or sets the <see cref="T:System.Runtime.ExceptionServices.ExceptionDispatchInfo"/> for the
            <see cref="P:Microsoft.AspNetCore.Mvc.Filters.ExceptionContext.Exception"/>, if this information was captured.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.ExceptionContext.ExceptionHandled">
            <summary>
            Gets or sets an indication that the <see cref="P:Microsoft.AspNetCore.Mvc.Filters.ExceptionContext.Exception"/> has been handled.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.ExceptionContext.Result">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Mvc.IActionResult"/>.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Filters.FilterContext">
            <summary>
            An abstract context for filters.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Filters.FilterContext.#ctor(Microsoft.AspNetCore.Mvc.ActionContext,System.Collections.Generic.IList{Microsoft.AspNetCore.Mvc.Filters.IFilterMetadata})">
            <summary>
            Instantiates a new <see cref="T:Microsoft.AspNetCore.Mvc.Filters.FilterContext"/> instance.
            </summary>
            <param name="actionContext">The <see cref="T:Microsoft.AspNetCore.Mvc.ActionContext"/>.</param>
            <param name="filters">All applicable <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IFilterMetadata"/> implementations.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.FilterContext.Filters">
            <summary>
            Gets all applicable <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IFilterMetadata"/> implementations.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Filters.FilterContext.IsEffectivePolicy``1(``0)">
            <summary>
            Returns a value indicating whether the provided <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IFilterMetadata"/> is the most effective
            policy (most specific) applied to the action associated with the <see cref="T:Microsoft.AspNetCore.Mvc.Filters.FilterContext"/>.
            </summary>
            <typeparam name="TMetadata">The type of the filter policy.</typeparam>
            <param name="policy">The filter policy instance.</param>
            <returns>
            <c>true</c> if the provided <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IFilterMetadata"/> is the most effective policy, otherwise <c>false</c>.
            </returns>
            <remarks>
            <para>
            The <see cref="M:Microsoft.AspNetCore.Mvc.Filters.FilterContext.IsEffectivePolicy``1(``0)"/> method is used to implement a common convention
            for filters that define an overriding behavior. When multiple filters may apply to the same
            cross-cutting concern, define a common interface for the filters (<typeparamref name="TMetadata"/>) and
            implement the filters such that all of the implementations call this method to determine if they should
            take action.
            </para>
            <para>
            For instance, a global filter might be overridden by placing a filter attribute on an action method.
            The policy applied directly to the action method could be considered more specific.
            </para>
            <para>
            This mechanism for overriding relies on the rules of order and scope that the filter system
            provides to control ordering of filters. It is up to the implementor of filters to implement this
            protocol cooperatively. The filter system has no innate notion of overrides, this is a recommended
            convention.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Filters.FilterContext.FindEffectivePolicy``1">
            <summary>
            Returns the most effective (most specific) policy of type <typeparamref name="TMetadata"/> applied to
            the action associated with the <see cref="T:Microsoft.AspNetCore.Mvc.Filters.FilterContext"/>.
            </summary>
            <typeparam name="TMetadata">The type of the filter policy.</typeparam>
            <returns>The implementation of <typeparamref name="TMetadata"/> applied to the action associated with
            the <see cref="T:Microsoft.AspNetCore.Mvc.Filters.FilterContext"/>
            </returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Filters.FilterDescriptor">
             <summary>
             Descriptor for an <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IFilterMetadata"/>.
             </summary>
             <remarks>
             <see cref="T:Microsoft.AspNetCore.Mvc.Filters.FilterDescriptor"/> describes an <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IFilterMetadata"/> with an order and scope.
            
             Order and scope control the execution order of filters. Filters with a higher value of Order execute
             later in the pipeline.
            
             When filters have the same Order, the Scope value is used to determine the order of execution. Filters
             with a higher value of Scope execute later in the pipeline. See <c>Microsoft.AspNetCore.Mvc.FilterScope</c>
             for commonly used scopes.
            
             For <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IExceptionFilter"/> implementations, the filter runs only after an exception has occurred,
             and so the observed order of execution will be opposite that of other filters.
             </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Filters.FilterDescriptor.#ctor(Microsoft.AspNetCore.Mvc.Filters.IFilterMetadata,System.Int32)">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Mvc.Filters.FilterDescriptor"/>.
            </summary>
            <param name="filter">The <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IFilterMetadata"/>.</param>
            <param name="filterScope">The filter scope.</param>
            <remarks>
            If the <paramref name="filter"/> implements <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IOrderedFilter"/>, then the value of
            <see cref="P:Microsoft.AspNetCore.Mvc.Filters.FilterDescriptor.Order"/> will be taken from <see cref="P:Microsoft.AspNetCore.Mvc.Filters.IOrderedFilter.Order"/>. Otherwise the value
            of <see cref="P:Microsoft.AspNetCore.Mvc.Filters.FilterDescriptor.Order"/> will default to <c>0</c>.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.FilterDescriptor.Filter">
            <summary>
            The <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IFilterMetadata"/> instance.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.FilterDescriptor.Order">
            <summary>
            The filter order.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.FilterDescriptor.Scope">
            <summary>
            The filter scope.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Filters.FilterItem">
            <summary>
            Used to associate executable filters with <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IFilterMetadata"/> instances
            as part of <see cref="T:Microsoft.AspNetCore.Mvc.Filters.FilterProviderContext"/>. An <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IFilterProvider"/> should
            inspect <see cref="P:Microsoft.AspNetCore.Mvc.Filters.FilterProviderContext.Results"/> and set <see cref="P:Microsoft.AspNetCore.Mvc.Filters.FilterItem.Filter"/> and
            <see cref="P:Microsoft.AspNetCore.Mvc.Filters.FilterItem.IsReusable"/> as appropriate.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Filters.FilterItem.#ctor(Microsoft.AspNetCore.Mvc.Filters.FilterDescriptor)">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Mvc.Filters.FilterItem"/>.
            </summary>
            <param name="descriptor">The <see cref="T:Microsoft.AspNetCore.Mvc.Filters.FilterDescriptor"/>.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Filters.FilterItem.#ctor(Microsoft.AspNetCore.Mvc.Filters.FilterDescriptor,Microsoft.AspNetCore.Mvc.Filters.IFilterMetadata)">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Mvc.Filters.FilterItem"/>.
            </summary>
            <param name="descriptor">The <see cref="T:Microsoft.AspNetCore.Mvc.Filters.FilterDescriptor"/>.</param>
            <param name="filter"></param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.FilterItem.Descriptor">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Mvc.Filters.FilterDescriptor"/> containing the filter metadata.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.FilterItem.Filter">
            <summary>
            Gets or sets the executable <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IFilterMetadata"/> associated with <see cref="P:Microsoft.AspNetCore.Mvc.Filters.FilterItem.Descriptor"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.FilterItem.IsReusable">
            <summary>
            Gets or sets a value indicating whether or not <see cref="P:Microsoft.AspNetCore.Mvc.Filters.FilterItem.Filter"/> can be reused across requests.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Filters.FilterProviderContext">
            <summary>
            A context for filter providers i.e. <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IFilterProvider"/> implementations.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Filters.FilterProviderContext.#ctor(Microsoft.AspNetCore.Mvc.ActionContext,System.Collections.Generic.IList{Microsoft.AspNetCore.Mvc.Filters.FilterItem})">
            <summary>
            Instantiates a new <see cref="T:Microsoft.AspNetCore.Mvc.Filters.FilterProviderContext"/> instance.
            </summary>
            <param name="actionContext">The <see cref="P:Microsoft.AspNetCore.Mvc.Filters.FilterProviderContext.ActionContext"/>.</param>
            <param name="items">
            The <see cref="T:Microsoft.AspNetCore.Mvc.Filters.FilterItem"/>s, initially created from <see cref="T:Microsoft.AspNetCore.Mvc.Filters.FilterDescriptor"/>s or a cache entry.
            </param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.FilterProviderContext.ActionContext">
            <summary>
            Gets or sets the <see cref="P:Microsoft.AspNetCore.Mvc.Filters.FilterProviderContext.ActionContext"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.FilterProviderContext.Results">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Mvc.Filters.FilterItem"/>s, initially created from <see cref="T:Microsoft.AspNetCore.Mvc.Filters.FilterDescriptor"/>s or a
            cache entry. <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IFilterProvider"/>s should set <see cref="P:Microsoft.AspNetCore.Mvc.Filters.FilterItem.Filter"/> on existing items or
            add new <see cref="T:Microsoft.AspNetCore.Mvc.Filters.FilterItem"/>s to make executable filters available.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Filters.IActionFilter">
            <summary>
            A filter that surrounds execution of the action.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Filters.IActionFilter.OnActionExecuting(Microsoft.AspNetCore.Mvc.Filters.ActionExecutingContext)">
            <summary>
            Called before the action executes, after model binding is complete.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Mvc.Filters.ActionExecutingContext"/>.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Filters.IActionFilter.OnActionExecuted(Microsoft.AspNetCore.Mvc.Filters.ActionExecutedContext)">
            <summary>
            Called after the action executes, before the action result.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Mvc.Filters.ActionExecutedContext"/>.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Filters.IAlwaysRunResultFilter">
            <summary>
            A filter that surrounds execution of all action results.
            </summary>
            <remarks>
            <para>
            The <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IAlwaysRunResultFilter"/> interface declares an <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IResultFilter"/> implementation
            that should run for all action results. <seealso cref="T:Microsoft.AspNetCore.Mvc.Filters.IAsyncAlwaysRunResultFilter"/>.
            </para>
            <para>
            <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IResultFilter"/> and <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IAsyncResultFilter"/> instances are not executed in cases where
            an authorization filter or resource filter short-circuits the request to prevent execution of the action.
            <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IResultFilter"/> and <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IAsyncResultFilter"/> implementations
            are also not executed in cases where an exception filter handles an exception by producing an action result.
            </para>
            </remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Filters.IAsyncActionFilter">
            <summary>
            A filter that asynchronously surrounds execution of the action, after model binding is complete.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Filters.IAsyncActionFilter.OnActionExecutionAsync(Microsoft.AspNetCore.Mvc.Filters.ActionExecutingContext,Microsoft.AspNetCore.Mvc.Filters.ActionExecutionDelegate)">
            <summary>
            Called asynchronously before the action, after model binding is complete.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Mvc.Filters.ActionExecutingContext"/>.</param>
            <param name="next">
            The <see cref="T:Microsoft.AspNetCore.Mvc.Filters.ActionExecutionDelegate"/>. Invoked to execute the next action filter or the action itself.
            </param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that on completion indicates the filter has executed.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Filters.IAsyncAlwaysRunResultFilter">
            <summary>
            A filter that asynchronously surrounds execution of all action results.
            </summary>
            <remarks>
            <para>
            The <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IAsyncAlwaysRunResultFilter"/> interface declares an <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IAsyncResultFilter"/> implementation
            that should run for all action results. <seealso cref="T:Microsoft.AspNetCore.Mvc.Filters.IAsyncAlwaysRunResultFilter"/>.
            </para>
            <para>
            <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IResultFilter"/> and <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IAsyncResultFilter"/> instances are not executed in cases where
            an authorization filter or resource filter short-circuits the request to prevent execution of the action.
            <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IResultFilter"/> and <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IAsyncResultFilter"/> implementations
            are also not executed in cases where an exception filter handles an exception by producing an action result.
            </para>
            </remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Filters.IAsyncAuthorizationFilter">
            <summary>
            A filter that asynchronously confirms request authorization.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Filters.IAsyncAuthorizationFilter.OnAuthorizationAsync(Microsoft.AspNetCore.Mvc.Filters.AuthorizationFilterContext)">
            <summary>
            Called early in the filter pipeline to confirm request is authorized.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Mvc.Filters.AuthorizationFilterContext"/>.</param>
            <returns>
            A <see cref="T:System.Threading.Tasks.Task"/> that on completion indicates the filter has executed.
            </returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Filters.IAsyncExceptionFilter">
            <summary>
            A filter that runs asynchronously after an action has thrown an <see cref="T:System.Exception"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Filters.IAsyncExceptionFilter.OnExceptionAsync(Microsoft.AspNetCore.Mvc.Filters.ExceptionContext)">
            <summary>
            Called after an action has thrown an <see cref="T:System.Exception"/>.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Mvc.Filters.ExceptionContext"/>.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that on completion indicates the filter has executed.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Filters.IAsyncResourceFilter">
            <summary>
            A filter that asynchronously surrounds execution of model binding, the action (and filters) and the action
            result (and filters).
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Filters.IAsyncResourceFilter.OnResourceExecutionAsync(Microsoft.AspNetCore.Mvc.Filters.ResourceExecutingContext,Microsoft.AspNetCore.Mvc.Filters.ResourceExecutionDelegate)">
            <summary>
            Called asynchronously before the rest of the pipeline.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Mvc.Filters.ResourceExecutingContext"/>.</param>
            <param name="next">
            The <see cref="T:Microsoft.AspNetCore.Mvc.Filters.ResourceExecutionDelegate"/>. Invoked to execute the next resource filter or the remainder
            of the pipeline.
            </param>
            <returns>
            A <see cref="T:System.Threading.Tasks.Task"/> which will complete when the remainder of the pipeline completes.
            </returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Filters.IAsyncResultFilter">
            <summary>
            A filter that asynchronously surrounds execution of action results successfully returned from an action.
            </summary>
            <remarks>
            <para>
            <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IResultFilter"/> and <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IAsyncResultFilter"/> implementations are executed around the action
            result only when the action method (or action filters) complete successfully.
            </para>
            <para>
            <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IResultFilter"/> and <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IAsyncResultFilter"/> instances are not executed in cases where
            an authorization filter or resource filter short-circuits the request to prevent execution of the action.
            <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IResultFilter"/>. <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IResultFilter"/> and <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IAsyncResultFilter"/> implementations
            are also not executed in cases where an exception filter handles an exception by producing an action result.
            </para>
            <para>
            To create a result filter that surrounds the execution of all action results, implement
            either the <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IAlwaysRunResultFilter"/> or the <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IAsyncAlwaysRunResultFilter"/> interface.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Filters.IAsyncResultFilter.OnResultExecutionAsync(Microsoft.AspNetCore.Mvc.Filters.ResultExecutingContext,Microsoft.AspNetCore.Mvc.Filters.ResultExecutionDelegate)">
            <summary>
            Called asynchronously before the action result.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Mvc.Filters.ResultExecutingContext"/>.</param>
            <param name="next">
            The <see cref="T:Microsoft.AspNetCore.Mvc.Filters.ResultExecutionDelegate"/>. Invoked to execute the next result filter or the result itself.
            </param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that on completion indicates the filter has executed.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Filters.IAuthorizationFilter">
            <summary>
            A filter that confirms request authorization.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Filters.IAuthorizationFilter.OnAuthorization(Microsoft.AspNetCore.Mvc.Filters.AuthorizationFilterContext)">
            <summary>
            Called early in the filter pipeline to confirm request is authorized.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Mvc.Filters.AuthorizationFilterContext"/>.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Filters.IExceptionFilter">
            <summary>
            A filter that runs after an action has thrown an <see cref="T:System.Exception"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Filters.IExceptionFilter.OnException(Microsoft.AspNetCore.Mvc.Filters.ExceptionContext)">
            <summary>
            Called after an action has thrown an <see cref="T:System.Exception"/>.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Mvc.Filters.ExceptionContext"/>.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Filters.IFilterContainer">
            <summary>
            A filter that requires a reference back to the <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IFilterFactory"/> that created it.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.IFilterContainer.FilterDefinition">
            <summary>
            The <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IFilterFactory"/> that created this filter instance.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Filters.IFilterFactory">
            <summary>
            An interface for filter metadata which can create an instance of an executable filter.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.IFilterFactory.IsReusable">
            <summary>
            Gets a value that indicates if the result of <see cref="M:Microsoft.AspNetCore.Mvc.Filters.IFilterFactory.CreateInstance(System.IServiceProvider)"/>
            can be reused across requests.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Filters.IFilterFactory.CreateInstance(System.IServiceProvider)">
            <summary>
            Creates an instance of the executable filter.
            </summary>
            <param name="serviceProvider">The request <see cref="T:System.IServiceProvider"/>.</param>
            <returns>An instance of the executable filter.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Filters.IFilterMetadata">
            <summary>
            Marker interface for filters handled in the MVC request pipeline.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Filters.IFilterProvider">
            <summary>
            A <see cref="T:Microsoft.AspNetCore.Mvc.Filters.FilterItem"/> provider. Implementations should update <see cref="P:Microsoft.AspNetCore.Mvc.Filters.FilterProviderContext.Results"/>
            to make executable filters available.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.IFilterProvider.Order">
            <summary>
            Gets the order value for determining the order of execution of providers. Providers execute in
            ascending numeric value of the <see cref="P:Microsoft.AspNetCore.Mvc.Filters.IFilterProvider.Order"/> property.
            </summary>
            <remarks>
            <para>
            Providers are executed in an ordering determined by an ascending sort of the <see cref="P:Microsoft.AspNetCore.Mvc.Filters.IFilterProvider.Order"/> property.
            A provider with a lower numeric value of <see cref="P:Microsoft.AspNetCore.Mvc.Filters.IFilterProvider.Order"/> will have its
            <see cref="M:Microsoft.AspNetCore.Mvc.Filters.IFilterProvider.OnProvidersExecuting(Microsoft.AspNetCore.Mvc.Filters.FilterProviderContext)"/> called before that of a provider with a higher numeric value of
            <see cref="P:Microsoft.AspNetCore.Mvc.Filters.IFilterProvider.Order"/>. The <see cref="M:Microsoft.AspNetCore.Mvc.Filters.IFilterProvider.OnProvidersExecuted(Microsoft.AspNetCore.Mvc.Filters.FilterProviderContext)"/> method is called in the reverse ordering after
            all calls to <see cref="M:Microsoft.AspNetCore.Mvc.Filters.IFilterProvider.OnProvidersExecuting(Microsoft.AspNetCore.Mvc.Filters.FilterProviderContext)"/>. A provider with a lower numeric value of
            <see cref="P:Microsoft.AspNetCore.Mvc.Filters.IFilterProvider.Order"/> will have its <see cref="M:Microsoft.AspNetCore.Mvc.Filters.IFilterProvider.OnProvidersExecuted(Microsoft.AspNetCore.Mvc.Filters.FilterProviderContext)"/> method called after that of a provider
            with a higher numeric value of <see cref="P:Microsoft.AspNetCore.Mvc.Filters.IFilterProvider.Order"/>.
            </para>
            <para>
            If two providers have the same numeric value of <see cref="P:Microsoft.AspNetCore.Mvc.Filters.IFilterProvider.Order"/>, then their relative execution order
            is undefined.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Filters.IFilterProvider.OnProvidersExecuting(Microsoft.AspNetCore.Mvc.Filters.FilterProviderContext)">
            <summary>
            Called in increasing <see cref="P:Microsoft.AspNetCore.Mvc.Filters.IFilterProvider.Order"/>.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Mvc.Filters.FilterProviderContext"/>.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Filters.IFilterProvider.OnProvidersExecuted(Microsoft.AspNetCore.Mvc.Filters.FilterProviderContext)">
            <summary>
            Called in decreasing <see cref="P:Microsoft.AspNetCore.Mvc.Filters.IFilterProvider.Order"/>, after all <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IFilterProvider"/>s have executed once.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Mvc.Filters.FilterProviderContext"/>.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Filters.IOrderedFilter">
            <summary>
            A filter that specifies the relative order it should run.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.IOrderedFilter.Order">
            <summary>
            Gets the order value for determining the order of execution of filters. Filters execute in
            ascending numeric value of the <see cref="P:Microsoft.AspNetCore.Mvc.Filters.IOrderedFilter.Order"/> property.
            </summary>
            <remarks>
            <para>
            Filters are executed in an ordering determined by an ascending sort of the <see cref="P:Microsoft.AspNetCore.Mvc.Filters.IOrderedFilter.Order"/> property.
            </para>
            <para>
            Asynchronous filters, such as <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IAsyncActionFilter"/>, surround the execution of subsequent
            filters of the same filter kind. An asynchronous filter with a lower numeric <see cref="P:Microsoft.AspNetCore.Mvc.Filters.IOrderedFilter.Order"/>
            value will have its filter method, such as <see cref="M:Microsoft.AspNetCore.Mvc.Filters.IAsyncActionFilter.OnActionExecutionAsync(Microsoft.AspNetCore.Mvc.Filters.ActionExecutingContext,Microsoft.AspNetCore.Mvc.Filters.ActionExecutionDelegate)"/>,
            executed before that of a filter with a higher value of <see cref="P:Microsoft.AspNetCore.Mvc.Filters.IOrderedFilter.Order"/>.
            </para>
            <para>
            Synchronous filters, such as <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IActionFilter"/>, have a before-method, such as
            <see cref="M:Microsoft.AspNetCore.Mvc.Filters.IActionFilter.OnActionExecuting(Microsoft.AspNetCore.Mvc.Filters.ActionExecutingContext)"/>, and an after-method, such as
            <see cref="M:Microsoft.AspNetCore.Mvc.Filters.IActionFilter.OnActionExecuted(Microsoft.AspNetCore.Mvc.Filters.ActionExecutedContext)"/>. A synchronous filter with a lower numeric <see cref="P:Microsoft.AspNetCore.Mvc.Filters.IOrderedFilter.Order"/>
            value will have its before-method executed before that of a filter with a higher value of
            <see cref="P:Microsoft.AspNetCore.Mvc.Filters.IOrderedFilter.Order"/>. During the after-stage of the filter, a synchronous filter with a lower
            numeric <see cref="P:Microsoft.AspNetCore.Mvc.Filters.IOrderedFilter.Order"/> value will have its after-method executed after that of a filter with a higher
            value of <see cref="P:Microsoft.AspNetCore.Mvc.Filters.IOrderedFilter.Order"/>.
            </para>
            <para>
            If two filters have the same numeric value of <see cref="P:Microsoft.AspNetCore.Mvc.Filters.IOrderedFilter.Order"/>, then their relative execution order
            is determined by the filter scope.
            </para>
            </remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Filters.IResourceFilter">
            <summary>
            A filter that surrounds execution of model binding, the action (and filters) and the action result
            (and filters).
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Filters.IResourceFilter.OnResourceExecuting(Microsoft.AspNetCore.Mvc.Filters.ResourceExecutingContext)">
            <summary>
            Executes the resource filter. Called before execution of the remainder of the pipeline.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Mvc.Filters.ResourceExecutingContext"/>.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Filters.IResourceFilter.OnResourceExecuted(Microsoft.AspNetCore.Mvc.Filters.ResourceExecutedContext)">
            <summary>
            Executes the resource filter. Called after execution of the remainder of the pipeline.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Mvc.Filters.ResourceExecutedContext"/>.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Filters.IResultFilter">
            <summary>
            A filter that surrounds execution of action results successfully returned from an action.
            </summary>
            <remarks>
            <para>
            <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IResultFilter"/> and <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IAsyncResultFilter"/> implementations are executed around the action
            result only when the action method (or action filters) complete successfully.
            </para>
            <para>
            <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IResultFilter"/> and <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IAsyncResultFilter"/> instances are not executed in cases where
            an authorization filter or resource filter short-circuits the request to prevent execution of the action.
            <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IResultFilter"/>. <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IResultFilter"/> and <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IAsyncResultFilter"/> implementations
            are also not executed in cases where an exception filter handles an exception by producing an action result.
            </para>
            <para>
            To create a result filter that surrounds the execution of all action results, implement
            either the <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IAlwaysRunResultFilter"/> or the <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IAsyncAlwaysRunResultFilter"/> interface.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Filters.IResultFilter.OnResultExecuting(Microsoft.AspNetCore.Mvc.Filters.ResultExecutingContext)">
            <summary>
            Called before the action result executes.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Mvc.Filters.ResultExecutingContext"/>.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Filters.IResultFilter.OnResultExecuted(Microsoft.AspNetCore.Mvc.Filters.ResultExecutedContext)">
            <summary>
            Called after the action result executes.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Mvc.Filters.ResultExecutedContext"/>.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Filters.ResourceExecutedContext">
            <summary>
            A context for resource filters, specifically <see cref="M:Microsoft.AspNetCore.Mvc.Filters.IResourceFilter.OnResourceExecuted(Microsoft.AspNetCore.Mvc.Filters.ResourceExecutedContext)"/> calls.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Filters.ResourceExecutedContext.#ctor(Microsoft.AspNetCore.Mvc.ActionContext,System.Collections.Generic.IList{Microsoft.AspNetCore.Mvc.Filters.IFilterMetadata})">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Mvc.Filters.ResourceExecutedContext"/>.
            </summary>
            <param name="actionContext">The <see cref="T:Microsoft.AspNetCore.Mvc.ActionContext"/>.</param>
            <param name="filters">The list of <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IFilterMetadata"/> instances.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.ResourceExecutedContext.Canceled">
            <summary>
            Gets or sets a value which indicates whether or not execution was canceled by a resource filter.
            If true, then a resource filter short-circuited execution by setting
            <see cref="P:Microsoft.AspNetCore.Mvc.Filters.ResourceExecutingContext.Result"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.ResourceExecutedContext.Exception">
            <summary>
            Gets or set the current <see cref="P:Microsoft.AspNetCore.Mvc.Filters.ResourceExecutedContext.Exception"/>.
            </summary>
            <remarks>
            <para>
            Setting <see cref="P:Microsoft.AspNetCore.Mvc.Filters.ResourceExecutedContext.Exception"/> or <see cref="P:Microsoft.AspNetCore.Mvc.Filters.ResourceExecutedContext.ExceptionDispatchInfo"/> to <c>null</c> will treat
            the exception as handled, and it will not be rethrown by the runtime.
            </para>
            <para>
            Setting <see cref="P:Microsoft.AspNetCore.Mvc.Filters.ResourceExecutedContext.ExceptionHandled"/> to <c>true</c> will also mark the exception as handled.
            </para>
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.ResourceExecutedContext.ExceptionDispatchInfo">
            <summary>
            Gets or set the current <see cref="P:Microsoft.AspNetCore.Mvc.Filters.ResourceExecutedContext.Exception"/>.
            </summary>
            <remarks>
            <para>
            Setting <see cref="P:Microsoft.AspNetCore.Mvc.Filters.ResourceExecutedContext.Exception"/> or <see cref="P:Microsoft.AspNetCore.Mvc.Filters.ResourceExecutedContext.ExceptionDispatchInfo"/> to <c>null</c> will treat
            the exception as handled, and it will not be rethrown by the runtime.
            </para>
            <para>
            Setting <see cref="P:Microsoft.AspNetCore.Mvc.Filters.ResourceExecutedContext.ExceptionHandled"/> to <c>true</c> will also mark the exception as handled.
            </para>
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.ResourceExecutedContext.ExceptionHandled">
            <summary>
            <para>
            Gets or sets a value indicating whether or not the current <see cref="P:Microsoft.AspNetCore.Mvc.Filters.ResourceExecutedContext.Exception"/> has been handled.
            </para>
            <para>
            If <c>false</c> the <see cref="P:Microsoft.AspNetCore.Mvc.Filters.ResourceExecutedContext.Exception"/> will be rethrown by the runtime after resource filters
            have executed.
            </para>
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.ResourceExecutedContext.Result">
            <summary>
            Gets or sets the result.
            </summary>
            <remarks>
            <para>
            The <see cref="P:Microsoft.AspNetCore.Mvc.Filters.ResourceExecutedContext.Result"/> may be provided by execution of the action itself or by another
            filter.
            </para>
            <para>
            The <see cref="P:Microsoft.AspNetCore.Mvc.Filters.ResourceExecutedContext.Result"/> has already been written to the response before being made available
            to resource filters.
            </para>
            </remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Filters.ResourceExecutingContext">
            <summary>
            A context for resource filters, specifically <see cref="M:Microsoft.AspNetCore.Mvc.Filters.IResourceFilter.OnResourceExecuting(Microsoft.AspNetCore.Mvc.Filters.ResourceExecutingContext)"/> and
            <see cref="M:Microsoft.AspNetCore.Mvc.Filters.IAsyncResourceFilter.OnResourceExecutionAsync(Microsoft.AspNetCore.Mvc.Filters.ResourceExecutingContext,Microsoft.AspNetCore.Mvc.Filters.ResourceExecutionDelegate)"/> calls.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Filters.ResourceExecutingContext.#ctor(Microsoft.AspNetCore.Mvc.ActionContext,System.Collections.Generic.IList{Microsoft.AspNetCore.Mvc.Filters.IFilterMetadata},System.Collections.Generic.IList{Microsoft.AspNetCore.Mvc.ModelBinding.IValueProviderFactory})">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Mvc.Filters.ResourceExecutingContext"/>.
            </summary>
            <param name="actionContext">The <see cref="T:Microsoft.AspNetCore.Mvc.ActionContext"/>.</param>
            <param name="filters">The list of <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IFilterMetadata"/> instances.</param>
            <param name="valueProviderFactories">The list of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IValueProviderFactory"/> instances.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.ResourceExecutingContext.Result">
            <summary>
            Gets or sets the result of the action to be executed.
            </summary>
            <remarks>
            Setting <see cref="P:Microsoft.AspNetCore.Mvc.Filters.ResourceExecutingContext.Result"/> to a non-<c>null</c> value inside a resource filter will
            short-circuit execution of additional resource filters and the action itself.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.ResourceExecutingContext.ValueProviderFactories">
            <summary>
            Gets the list of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IValueProviderFactory"/> instances used by model binding.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Filters.ResourceExecutionDelegate">
            <summary>
            A delegate that asynchronously returns a <see cref="T:Microsoft.AspNetCore.Mvc.Filters.ResourceExecutedContext"/> indicating model binding, the
            action, the action's result, result filters, and exception filters have executed.
            </summary>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that on completion returns a <see cref="T:Microsoft.AspNetCore.Mvc.Filters.ResourceExecutedContext"/>.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Filters.ResultExecutedContext">
            <summary>
            A context for result filters, specifically <see cref="M:Microsoft.AspNetCore.Mvc.Filters.IResultFilter.OnResultExecuted(Microsoft.AspNetCore.Mvc.Filters.ResultExecutedContext)"/> calls.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Filters.ResultExecutedContext.#ctor(Microsoft.AspNetCore.Mvc.ActionContext,System.Collections.Generic.IList{Microsoft.AspNetCore.Mvc.Filters.IFilterMetadata},Microsoft.AspNetCore.Mvc.IActionResult,System.Object)">
            <summary>
            Instantiates a new <see cref="T:Microsoft.AspNetCore.Mvc.Filters.ResultExecutedContext"/> instance.
            </summary>
            <param name="actionContext">The <see cref="T:Microsoft.AspNetCore.Mvc.ActionContext"/>.</param>
            <param name="filters">All applicable <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IFilterMetadata"/> implementations.</param>
            <param name="result">
            The <see cref="T:Microsoft.AspNetCore.Mvc.IActionResult"/> copied from <see cref="P:Microsoft.AspNetCore.Mvc.Filters.ResultExecutingContext.Result"/>.
            </param>
            <param name="controller">The controller instance containing the action.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.ResultExecutedContext.Canceled">
            <summary>
            Gets or sets an indication that a result filter set <see cref="P:Microsoft.AspNetCore.Mvc.Filters.ResultExecutingContext.Cancel"/> to
            <c>true</c> and short-circuited the filter pipeline.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.ResultExecutedContext.Controller">
            <summary>
            Gets the controller instance containing the action.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.ResultExecutedContext.Exception">
            <summary>
            Gets or sets the <see cref="T:System.Exception"/> caught while executing the result or result filters, if
            any.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.ResultExecutedContext.ExceptionDispatchInfo">
            <summary>
            Gets or sets the <see cref="T:System.Runtime.ExceptionServices.ExceptionDispatchInfo"/> for the
            <see cref="P:Microsoft.AspNetCore.Mvc.Filters.ResultExecutedContext.Exception"/>, if an <see cref="T:System.Exception"/> was caught and this information captured.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.ResultExecutedContext.ExceptionHandled">
            <summary>
            Gets or sets an indication that the <see cref="P:Microsoft.AspNetCore.Mvc.Filters.ResultExecutedContext.Exception"/> has been handled.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.ResultExecutedContext.Result">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Mvc.IActionResult"/> copied from <see cref="P:Microsoft.AspNetCore.Mvc.Filters.ResultExecutingContext.Result"/>.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Filters.ResultExecutingContext">
            <summary>
            A context for result filters, specifically <see cref="M:Microsoft.AspNetCore.Mvc.Filters.IResultFilter.OnResultExecuting(Microsoft.AspNetCore.Mvc.Filters.ResultExecutingContext)"/> and
            <see cref="M:Microsoft.AspNetCore.Mvc.Filters.IAsyncResultFilter.OnResultExecutionAsync(Microsoft.AspNetCore.Mvc.Filters.ResultExecutingContext,Microsoft.AspNetCore.Mvc.Filters.ResultExecutionDelegate)"/> calls.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Filters.ResultExecutingContext.#ctor(Microsoft.AspNetCore.Mvc.ActionContext,System.Collections.Generic.IList{Microsoft.AspNetCore.Mvc.Filters.IFilterMetadata},Microsoft.AspNetCore.Mvc.IActionResult,System.Object)">
            <summary>
            Instantiates a new <see cref="T:Microsoft.AspNetCore.Mvc.Filters.ResultExecutingContext"/> instance.
            </summary>
            <param name="actionContext">The <see cref="T:Microsoft.AspNetCore.Mvc.ActionContext"/>.</param>
            <param name="filters">All applicable <see cref="T:Microsoft.AspNetCore.Mvc.Filters.IFilterMetadata"/> implementations.</param>
            <param name="result">The <see cref="T:Microsoft.AspNetCore.Mvc.IActionResult"/> of the action and action filters.</param>
            <param name="controller">The controller instance containing the action.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.ResultExecutingContext.Controller">
            <summary>
            Gets the controller instance containing the action.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.ResultExecutingContext.Result">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Mvc.IActionResult"/> to execute. Setting <see cref="P:Microsoft.AspNetCore.Mvc.Filters.ResultExecutingContext.Result"/> to a non-<c>null</c>
            value inside a result filter will short-circuit the result and any remaining result filters.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Filters.ResultExecutingContext.Cancel">
            <summary>
            Gets or sets an indication the result filter pipeline should be short-circuited.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Filters.ResultExecutionDelegate">
            <summary>
            A delegate that asynchronously returns an <see cref="T:Microsoft.AspNetCore.Mvc.Filters.ResultExecutedContext"/> indicating the action result or
            the next result filter has executed.
            </summary>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that on completion returns an <see cref="T:Microsoft.AspNetCore.Mvc.Filters.ResultExecutedContext"/>.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Formatters.FormatterCollection`1">
            <summary>
            Represents a collection of formatters.
            </summary>
            <typeparam name="TFormatter">The type of formatters in the collection.</typeparam>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Formatters.FormatterCollection`1.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Mvc.Formatters.FormatterCollection`1"/> class that is empty.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Formatters.FormatterCollection`1.#ctor(System.Collections.Generic.IList{`0})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Mvc.Formatters.FormatterCollection`1"/> class
            as a wrapper for the specified list.
            </summary>
            <param name="list">The list that is wrapped by the new collection.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Formatters.FormatterCollection`1.RemoveType``1">
            <summary>
            Removes all formatters of the specified type.
            </summary>
            <typeparam name="T">The type to remove.</typeparam>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Formatters.FormatterCollection`1.RemoveType(System.Type)">
            <summary>
            Removes all formatters of the specified type.
            </summary>
            <param name="formatterType">The type to remove.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Formatters.IInputFormatter">
            <summary>
            Reads an object from the request body.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Formatters.IInputFormatter.CanRead(Microsoft.AspNetCore.Mvc.Formatters.InputFormatterContext)">
            <summary>
            Determines whether this <see cref="T:Microsoft.AspNetCore.Mvc.Formatters.IInputFormatter"/> can deserialize an object of the
            <paramref name="context"/>'s <see cref="P:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterContext.ModelType"/>.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterContext"/>.</param>
            <returns>
            <c>true</c> if this <see cref="T:Microsoft.AspNetCore.Mvc.Formatters.IInputFormatter"/> can deserialize an object of the
            <paramref name="context"/>'s <see cref="P:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterContext.ModelType"/>. <c>false</c> otherwise.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Formatters.IInputFormatter.ReadAsync(Microsoft.AspNetCore.Mvc.Formatters.InputFormatterContext)">
            <summary>
            Reads an object from the request body.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterContext"/>.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that on completion deserializes the request body.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Formatters.IInputFormatterExceptionPolicy">
            <summary>
            A policy which <see cref="T:Microsoft.AspNetCore.Mvc.Formatters.IInputFormatter"/>s can implement to indicate if they want the body model binder
            to handle all exceptions. By default, all default <see cref="T:Microsoft.AspNetCore.Mvc.Formatters.IInputFormatter"/>s implement this interface and
            have a default value of <see cref="F:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterExceptionPolicy.MalformedInputExceptions"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Formatters.IInputFormatterExceptionPolicy.ExceptionPolicy">
            <summary>
            Gets the flag to indicate if the body model binder should handle all exceptions. If an exception is handled,
            the body model binder converts the exception into model state errors, else the exception is allowed to propagate.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterContext">
            <summary>
            A context object used by an input formatter for deserializing the request body into an object.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterContext.#ctor(Microsoft.AspNetCore.Http.HttpContext,System.String,Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary,Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata,System.Func{System.IO.Stream,System.Text.Encoding,System.IO.TextReader})">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterContext"/>.
            </summary>
            <param name="httpContext">
            The <see cref="T:Microsoft.AspNetCore.Http.HttpContext"/> for the current operation.
            </param>
            <param name="modelName">The name of the model.</param>
            <param name="modelState">
            The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary"/> for recording errors.
            </param>
            <param name="metadata">
            The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/> of the model to deserialize.
            </param>
            <param name="readerFactory">
            A delegate which can create a <see cref="T:System.IO.TextReader"/> for the request body.
            </param>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterContext.#ctor(Microsoft.AspNetCore.Http.HttpContext,System.String,Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary,Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata,System.Func{System.IO.Stream,System.Text.Encoding,System.IO.TextReader},System.Boolean)">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterContext"/>.
            </summary>
            <param name="httpContext">
            The <see cref="T:Microsoft.AspNetCore.Http.HttpContext"/> for the current operation.
            </param>
            <param name="modelName">The name of the model.</param>
            <param name="modelState">
            The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary"/> for recording errors.
            </param>
            <param name="metadata">
            The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/> of the model to deserialize.
            </param>
            <param name="readerFactory">
            A delegate which can create a <see cref="T:System.IO.TextReader"/> for the request body.
            </param>
            <param name="treatEmptyInputAsDefaultValue">
            A value for the <see cref="P:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterContext.TreatEmptyInputAsDefaultValue"/> property.
            </param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterContext.TreatEmptyInputAsDefaultValue">
            <summary>
            Gets a flag to indicate whether the input formatter should allow no value to be provided.
            If <see langword="false"/>, the input formatter should handle empty input by returning
            <see cref="M:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterResult.NoValueAsync"/>. If <see langword="true"/>, the input
            formatter should handle empty input by returning the default value for the type
            <see cref="P:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterContext.ModelType"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterContext.HttpContext">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Http.HttpContext"/> associated with the current operation.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterContext.ModelName">
            <summary>
            Gets the name of the model. Used as the key or key prefix for errors added to <see cref="P:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterContext.ModelState"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterContext.ModelState">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary"/> associated with the current operation.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterContext.Metadata">
            <summary>
            Gets the requested <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/> of the request body deserialization.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterContext.ModelType">
            <summary>
            Gets the requested <see cref="T:System.Type"/> of the request body deserialization.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterContext.ReaderFactory">
            <summary>
            Gets a delegate which can create a <see cref="T:System.IO.TextReader"/> for the request body.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterException">
            <summary>
            Exception thrown by <see cref="T:Microsoft.AspNetCore.Mvc.Formatters.IInputFormatter"/> when the input is not in an expected format.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterException.#ctor">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterException"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterException.#ctor(System.String)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterException"/> with the specified <paramref name="message"/>.
            </summary>
            <param name="message">The exception message.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterException"/> with the specified <paramref name="message"/> and
            inner exception that is the cause of this exception.
            </summary>
            <param name="message">The exception message.</param>
            <param name="innerException">The exception that is the cause of the current exception.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterExceptionPolicy">
            <summary>
            Defines the set of policies that determine how the model binding system interprets exceptions
            thrown by an <see cref="T:Microsoft.AspNetCore.Mvc.Formatters.IInputFormatter"/>. <seealso cref="T:Microsoft.AspNetCore.Mvc.Formatters.IInputFormatterExceptionPolicy"/>
            </summary>
            <remarks>
            <para>
            An <see cref="T:Microsoft.AspNetCore.Mvc.Formatters.IInputFormatter"/> could throw an exception for several reasons, including:
            <list type="bullet">
            <item><description>malformed input</description></item>
            <item><description>client disconnect or other I/O problem</description></item>
            <item><description>
            application configuration problems such as <see cref="T:System.TypeLoadException"/>
            </description></item>
            </list>
            </para>
            <para>
            The policy associated with <see cref="F:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterExceptionPolicy.AllExceptions"/> treats
            all such categories of problems as model state errors, and usually will be reported to the client as
            an HTTP 400. This was the only policy supported by model binding in ASP.NET Core MVC 1.0, 1.1, and 2.0
            and is still the default for historical reasons.
            </para>
            <para>
            The policy associated with <see cref="F:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterExceptionPolicy.MalformedInputExceptions"/>
            treats only <see cref="T:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterException"/> and its subclasses as model state errors. This means that
            exceptions that are not related to the content of the HTTP request (such as a disconnect) will be re-thrown,
            which by default would cause an HTTP 500 response, unless there is exception-handling middleware enabled.
            </para>
            </remarks>
        </member>
        <member name="F:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterExceptionPolicy.AllExceptions">
            <summary>
            This value indicates that all exceptions thrown by an <see cref="T:Microsoft.AspNetCore.Mvc.Formatters.IInputFormatter"/> will be treated
            as model state errors.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterExceptionPolicy.MalformedInputExceptions">
            <summary>
            This value indicates that only <see cref="T:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterException"/> and subclasses will be treated
            as model state errors. All other exceptions types will be re-thrown and can be handled by a higher
            level exception handler, such as exception-handling middleware.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterResult">
            <summary>
            Result of a <see cref="M:Microsoft.AspNetCore.Mvc.Formatters.IInputFormatter.ReadAsync(Microsoft.AspNetCore.Mvc.Formatters.InputFormatterContext)"/> operation.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterResult.HasError">
            <summary>
            Gets an indication whether the <see cref="M:Microsoft.AspNetCore.Mvc.Formatters.IInputFormatter.ReadAsync(Microsoft.AspNetCore.Mvc.Formatters.InputFormatterContext)"/> operation had an error.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterResult.IsModelSet">
            <summary>
            Gets an indication whether a value for the <see cref="P:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterResult.Model"/> property was supplied.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterResult.Model">
            <summary>
            Gets the deserialized <see cref="T:System.Object"/>.
            </summary>
            <value>
            <c>null</c> if <see cref="P:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterResult.HasError"/> is <c>true</c>.
            </value>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterResult.Failure">
            <summary>
            Returns an <see cref="T:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterResult"/> indicating the <see cref="M:Microsoft.AspNetCore.Mvc.Formatters.IInputFormatter.ReadAsync(Microsoft.AspNetCore.Mvc.Formatters.InputFormatterContext)"/>
            operation failed.
            </summary>
            <returns>
            An <see cref="T:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterResult"/> indicating the <see cref="M:Microsoft.AspNetCore.Mvc.Formatters.IInputFormatter.ReadAsync(Microsoft.AspNetCore.Mvc.Formatters.InputFormatterContext)"/>
            operation failed i.e. with <see cref="P:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterResult.HasError"/> <c>true</c>.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterResult.FailureAsync">
            <summary>
            Returns a <see cref="T:System.Threading.Tasks.Task"/> that on completion provides an <see cref="T:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterResult"/> indicating
            the <see cref="M:Microsoft.AspNetCore.Mvc.Formatters.IInputFormatter.ReadAsync(Microsoft.AspNetCore.Mvc.Formatters.InputFormatterContext)"/> operation failed.
            </summary>
            <returns>
            A <see cref="T:System.Threading.Tasks.Task"/> that on completion provides an <see cref="T:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterResult"/> indicating the
            <see cref="M:Microsoft.AspNetCore.Mvc.Formatters.IInputFormatter.ReadAsync(Microsoft.AspNetCore.Mvc.Formatters.InputFormatterContext)"/> operation failed i.e. with <see cref="P:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterResult.HasError"/> <c>true</c>.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterResult.Success(System.Object)">
            <summary>
            Returns an <see cref="T:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterResult"/> indicating the <see cref="M:Microsoft.AspNetCore.Mvc.Formatters.IInputFormatter.ReadAsync(Microsoft.AspNetCore.Mvc.Formatters.InputFormatterContext)"/>
            operation was successful.
            </summary>
            <param name="model">The deserialized <see cref="T:System.Object"/>.</param>
            <returns>
            An <see cref="T:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterResult"/> indicating the <see cref="M:Microsoft.AspNetCore.Mvc.Formatters.IInputFormatter.ReadAsync(Microsoft.AspNetCore.Mvc.Formatters.InputFormatterContext)"/>
            operation succeeded i.e. with <see cref="P:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterResult.HasError"/> <c>false</c>.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterResult.SuccessAsync(System.Object)">
            <summary>
            Returns a <see cref="T:System.Threading.Tasks.Task"/> that on completion provides an <see cref="T:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterResult"/> indicating
            the <see cref="M:Microsoft.AspNetCore.Mvc.Formatters.IInputFormatter.ReadAsync(Microsoft.AspNetCore.Mvc.Formatters.InputFormatterContext)"/> operation was successful.
            </summary>
            <param name="model">The deserialized <see cref="T:System.Object"/>.</param>
            <returns>
            A <see cref="T:System.Threading.Tasks.Task"/> that on completion provides an <see cref="T:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterResult"/> indicating the
            <see cref="M:Microsoft.AspNetCore.Mvc.Formatters.IInputFormatter.ReadAsync(Microsoft.AspNetCore.Mvc.Formatters.InputFormatterContext)"/> operation succeeded i.e. with <see cref="P:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterResult.HasError"/> <c>false</c>.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterResult.NoValue">
            <summary>
            Returns an <see cref="T:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterResult"/> indicating the <see cref="M:Microsoft.AspNetCore.Mvc.Formatters.IInputFormatter.ReadAsync(Microsoft.AspNetCore.Mvc.Formatters.InputFormatterContext)"/>
            operation produced no value.
            </summary>
            <returns>
            An <see cref="T:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterResult"/> indicating the <see cref="M:Microsoft.AspNetCore.Mvc.Formatters.IInputFormatter.ReadAsync(Microsoft.AspNetCore.Mvc.Formatters.InputFormatterContext)"/>
            operation produced no value.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterResult.NoValueAsync">
            <summary>
            Returns a <see cref="T:System.Threading.Tasks.Task"/> that on completion provides an <see cref="T:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterResult"/> indicating
            the <see cref="M:Microsoft.AspNetCore.Mvc.Formatters.IInputFormatter.ReadAsync(Microsoft.AspNetCore.Mvc.Formatters.InputFormatterContext)"/> operation produced no value.
            </summary>
            <returns>
            A <see cref="T:System.Threading.Tasks.Task"/> that on completion provides an <see cref="T:Microsoft.AspNetCore.Mvc.Formatters.InputFormatterResult"/> indicating the
            <see cref="M:Microsoft.AspNetCore.Mvc.Formatters.IInputFormatter.ReadAsync(Microsoft.AspNetCore.Mvc.Formatters.InputFormatterContext)"/> operation produced no value.
            </returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Formatters.IOutputFormatter">
            <summary>
            Writes an object to the output stream.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Formatters.IOutputFormatter.CanWriteResult(Microsoft.AspNetCore.Mvc.Formatters.OutputFormatterCanWriteContext)">
            <summary>
            Determines whether this <see cref="T:Microsoft.AspNetCore.Mvc.Formatters.IOutputFormatter"/> can serialize
            an object of the specified type.
            </summary>
            <param name="context">The formatter context associated with the call.</param>
            <returns>Returns <c>true</c> if the formatter can write the response; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Formatters.IOutputFormatter.WriteAsync(Microsoft.AspNetCore.Mvc.Formatters.OutputFormatterWriteContext)">
            <summary>
            Writes the object represented by <paramref name="context"/>'s Object property.
            </summary>
            <param name="context">The formatter context associated with the call.</param>
            <returns>A Task that serializes the value to the <paramref name="context"/>'s response message.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Formatters.OutputFormatterCanWriteContext">
            <summary>
            A context object for <see cref="M:Microsoft.AspNetCore.Mvc.Formatters.IOutputFormatter.CanWriteResult(Microsoft.AspNetCore.Mvc.Formatters.OutputFormatterCanWriteContext)"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Formatters.OutputFormatterCanWriteContext.#ctor(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Mvc.Formatters.OutputFormatterCanWriteContext"/>.
            </summary>
            <param name="httpContext">The <see cref="P:Microsoft.AspNetCore.Mvc.Formatters.OutputFormatterCanWriteContext.HttpContext"/> for the current request.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Formatters.OutputFormatterCanWriteContext.HttpContext">
            <summary>
            Gets or sets the <see cref="P:Microsoft.AspNetCore.Mvc.Formatters.OutputFormatterCanWriteContext.HttpContext"/> context associated with the current operation.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Formatters.OutputFormatterCanWriteContext.ContentType">
            <summary>
            Gets or sets the content type to write to the response.
            </summary>
            <remarks>
            An <see cref="T:Microsoft.AspNetCore.Mvc.Formatters.IOutputFormatter"/> can set this value when its
            <see cref="M:Microsoft.AspNetCore.Mvc.Formatters.IOutputFormatter.CanWriteResult(Microsoft.AspNetCore.Mvc.Formatters.OutputFormatterCanWriteContext)"/> method is called,
            and expect to see the same value provided in
            <see cref="M:Microsoft.AspNetCore.Mvc.Formatters.IOutputFormatter.WriteAsync(Microsoft.AspNetCore.Mvc.Formatters.OutputFormatterWriteContext)"/>
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Formatters.OutputFormatterCanWriteContext.ContentTypeIsServerDefined">
            <summary>
            Gets or sets a value to indicate whether the content type was specified by server-side code.
            This allows <see cref="M:Microsoft.AspNetCore.Mvc.Formatters.IOutputFormatter.CanWriteResult(Microsoft.AspNetCore.Mvc.Formatters.OutputFormatterCanWriteContext)"/> to
            implement stricter filtering on content types that, for example, are being considered purely
            because of an incoming Accept header.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Formatters.OutputFormatterCanWriteContext.Object">
            <summary>
            Gets or sets the object to write to the response.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Formatters.OutputFormatterCanWriteContext.ObjectType">
            <summary>
            Gets or sets the <see cref="T:System.Type"/> of the object to write to the response.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Formatters.OutputFormatterWriteContext">
            <summary>
            A context object for <see cref="M:Microsoft.AspNetCore.Mvc.Formatters.IOutputFormatter.WriteAsync(Microsoft.AspNetCore.Mvc.Formatters.OutputFormatterWriteContext)"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.Formatters.OutputFormatterWriteContext.#ctor(Microsoft.AspNetCore.Http.HttpContext,System.Func{System.IO.Stream,System.Text.Encoding,System.IO.TextWriter},System.Type,System.Object)">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Mvc.Formatters.OutputFormatterWriteContext"/>.
            </summary>
            <param name="httpContext">The <see cref="T:Microsoft.AspNetCore.Http.HttpContext"/> for the current request.</param>
            <param name="writerFactory">The delegate used to create a <see cref="T:System.IO.TextWriter"/> for writing the response.</param>
            <param name="objectType">The <see cref="T:System.Type"/> of the object to write to the response.</param>
            <param name="object">The object to write to the response.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Formatters.OutputFormatterWriteContext.WriterFactory">
            <summary>
            <para>
            Gets or sets a delegate used to create a <see cref="T:System.IO.TextWriter"/> for writing text to the response.
            </para>
            <para>
            Write to <see cref="P:Microsoft.AspNetCore.Http.HttpResponse.Body"/> directly to write binary data to the response.
            </para>
            </summary>
            <remarks>
            <para>
            The <see cref="T:System.IO.TextWriter"/> created by this delegate will encode text and write to the
            <see cref="P:Microsoft.AspNetCore.Http.HttpResponse.Body"/> stream. Call this delegate to create a <see cref="T:System.IO.TextWriter"/>
            for writing text output to the response stream.
            </para>
            <para>
            To implement a formatter that writes binary data to the response stream, do not use the
            <see cref="P:Microsoft.AspNetCore.Mvc.Formatters.OutputFormatterWriteContext.WriterFactory"/> delegate, and use <see cref="P:Microsoft.AspNetCore.Http.HttpResponse.Body"/> instead.
            </para>
            </remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.IActionResult">
            <summary>
            Defines a contract that represents the result of an action method.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.IActionResult.ExecuteResultAsync(Microsoft.AspNetCore.Mvc.ActionContext)">
            <summary>
            Executes the result operation of the action method asynchronously. This method is called by MVC to process
            the result of an action method.
            </summary>
            <param name="context">The context in which the result is executed. The context information includes
            information about the action that was executed and request information.</param>
            <returns>A task that represents the asynchronous execute operation.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.IUrlHelper">
            <summary>
            Defines the contract for the helper to build URLs for ASP.NET MVC within an application.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.IUrlHelper.ActionContext">
            <summary>
            Gets the <see cref="P:Microsoft.AspNetCore.Mvc.IUrlHelper.ActionContext"/> for the current request.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.IUrlHelper.Action(Microsoft.AspNetCore.Mvc.Routing.UrlActionContext)">
            <summary>
            Generates a URL with an absolute path for an action method, which contains the action
            name, controller name, route values, protocol to use, host name, and fragment specified by
            <see cref="T:Microsoft.AspNetCore.Mvc.Routing.UrlActionContext"/>. Generates an absolute URL if <see cref="P:Microsoft.AspNetCore.Mvc.Routing.UrlActionContext.Protocol"/> and
            <see cref="P:Microsoft.AspNetCore.Mvc.Routing.UrlActionContext.Host"/> are non-<c>null</c>. See the remarks section for important security information.
            </summary>
            <param name="actionContext">The context object for the generated URLs for an action method.</param>
            <returns>The generated URL.</returns>
            <remarks>
            <para>
            The value of <see cref="P:Microsoft.AspNetCore.Mvc.Routing.UrlActionContext.Host" /> should be a trusted value. Relying on the value of the current request
            can allow untrusted input to influence the resulting URI unless the <c>Host</c> header has been validated.
            See the deployment documentation for instructions on how to properly validate the <c>Host</c> header in
            your deployment environment.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.IUrlHelper.Content(System.String)">
            <summary>
            Converts a virtual (relative, starting with ~/) path to an application absolute path.
            </summary>
            <remarks>
            If the specified content path does not start with the tilde (~) character,
            this method returns <paramref name="contentPath"/> unchanged.
            </remarks>
            <param name="contentPath">The virtual path of the content.</param>
            <returns>The application absolute path.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.IUrlHelper.IsLocalUrl(System.String)">
            <summary>
            Returns a value that indicates whether the URL is local. A URL is considered local if it does not have a
            host / authority part and it has an absolute path. URLs using virtual paths ('~/') are also local.
            </summary>
            <param name="url">The URL.</param>
            <returns><c>true</c> if the URL is local; otherwise, <c>false</c>.</returns>
            <example>
            <para>
            For example, the following URLs are considered local:
            <code>
            /Views/Default/Index.html
            ~/Index.html
            </code>
            </para>
            <para>
            The following URLs are non-local:
            <code>
            ../Index.html
            http://www.contoso.com/
            http://localhost/Index.html
            </code>
            </para>
            </example>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.IUrlHelper.RouteUrl(Microsoft.AspNetCore.Mvc.Routing.UrlRouteContext)">
            <summary>
            Generates a URL with an absolute path, which contains the route name, route values, protocol to use, host
            name, and fragment specified by <see cref="T:Microsoft.AspNetCore.Mvc.Routing.UrlRouteContext"/>. Generates an absolute URL if
            <see cref="P:Microsoft.AspNetCore.Mvc.Routing.UrlActionContext.Protocol"/> and <see cref="P:Microsoft.AspNetCore.Mvc.Routing.UrlActionContext.Host"/> are non-<c>null</c>.
            See the remarks section for important security information.
            </summary>
            <param name="routeContext">The context object for the generated URLs for a route.</param>
            <returns>The generated URL.</returns>
            <remarks>
            <para>
            The value of <see cref="P:Microsoft.AspNetCore.Mvc.Routing.UrlRouteContext.Host" /> should be a trusted value. Relying on the value of the current request
            can allow untrusted input to influence the resulting URI unless the <c>Host</c> header has been validated.
            See the deployment documentation for instructions on how to properly validate the <c>Host</c> header in
            your deployment environment.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.IUrlHelper.Link(System.String,System.Object)">
            <summary>
            Generates an absolute URL for the specified <paramref name="routeName"/> and route
            <paramref name="values"/>, which contains the protocol (such as "http" or "https") and host name from the
            current request. See the remarks section for important security information.
            </summary>
            <param name="routeName">The name of the route that is used to generate URL.</param>
            <param name="values">An object that contains route values.</param>
            <returns>The generated absolute URL.</returns>
            <remarks>
            <para>
            This method uses the value of <see cref="P:Microsoft.AspNetCore.Http.HttpRequest.Host"/> to populate the host section of the generated URI.
            Relying on the value of the current request can allow untrusted input to influence the resulting URI unless
            the <c>Host</c> header has been validated. See the deployment documentation for instructions on how to properly
            validate the <c>Host</c> header in your deployment environment.
            </para>
            </remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.BindingInfo">
            <summary>
            Binding info which represents metadata associated to an action parameter.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.BindingInfo.#ctor">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.BindingInfo"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.BindingInfo.#ctor(Microsoft.AspNetCore.Mvc.ModelBinding.BindingInfo)">
            <summary>
            Creates a copy of a <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.BindingInfo"/>.
            </summary>
            <param name="other">The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.BindingInfo"/> to copy.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.BindingInfo.BindingSource">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.BindingInfo.BinderModelName">
            <summary>
            Gets or sets the binder model name.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.BindingInfo.BinderType">
            <summary>
            Gets or sets the <see cref="T:System.Type"/> of the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IModelBinder"/> implementation used to bind the
            model.
            </summary>
            <remarks>
            Also set <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.BindingInfo.BindingSource"/> if the specified <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IModelBinder"/> implementation does not
            use values from form data, route values or the query string.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.BindingInfo.PropertyFilterProvider">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IPropertyFilterProvider"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.BindingInfo.RequestPredicate">
            <summary>
            Gets or sets a predicate which determines whether or not the model should be bound based on state
            from the current request.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.BindingInfo.EmptyBodyBehavior">
            <summary>
            Gets or sets the value which decides if empty bodies are treated as valid inputs.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.BindingInfo.GetBindingInfo(System.Collections.Generic.IEnumerable{System.Object})">
            <summary>
            Constructs a new instance of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.BindingInfo"/> from the given <paramref name="attributes"/>.
            <para>
            This overload does not account for <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.BindingInfo"/> specified via <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/>. Consider using
            <see cref="M:Microsoft.AspNetCore.Mvc.ModelBinding.BindingInfo.GetBindingInfo(System.Collections.Generic.IEnumerable{System.Object},Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata)"/> overload, or <see cref="M:Microsoft.AspNetCore.Mvc.ModelBinding.BindingInfo.TryApplyBindingInfo(Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata)"/>
            on the result of this method to get a more accurate <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.BindingInfo"/> instance.
            </para>
            </summary>
            <param name="attributes">A collection of attributes which are used to construct <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.BindingInfo"/>
            </param>
            <returns>A new instance of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.BindingInfo"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.BindingInfo.GetBindingInfo(System.Collections.Generic.IEnumerable{System.Object},Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata)">
            <summary>
            Constructs a new instance of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.BindingInfo"/> from the given <paramref name="attributes"/> and <paramref name="modelMetadata"/>.
            </summary>
            <param name="attributes">A collection of attributes which are used to construct <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.BindingInfo"/>.</param>
            <param name="modelMetadata">The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/>.</param>
            <returns>A new instance of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.BindingInfo"/> if any binding metadata was discovered; otherwise or <see langword="null"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.BindingInfo.TryApplyBindingInfo(Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata)">
            <summary>
            Applies binding metadata from the specified <paramref name="modelMetadata"/>.
            <para>
            Uses values from <paramref name="modelMetadata"/> if no value is already available.
            </para>
            </summary>
            <param name="modelMetadata">The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/>.</param>
            <returns><see langword="true"/> if any binding metadata from <paramref name="modelMetadata"/> was applied;
            <see langword="false"/> otherwise.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource">
            <summary>
            A metadata object representing a source of data for model binding.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource.Body">
            <summary>
            A <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource"/> for the request body.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource.Custom">
            <summary>
            A <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource"/> for a custom model binder (unknown data source).
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource.Form">
            <summary>
            A <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource"/> for the request form-data.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource.Header">
            <summary>
            A <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource"/> for the request headers.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource.ModelBinding">
            <summary>
            A <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource"/> for model binding. Includes form-data, query-string
            and route data from the request.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource.Path">
            <summary>
            A <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource"/> for the request url path.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource.Query">
            <summary>
            A <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource"/> for the request query-string.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource.Services">
            <summary>
            A <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource"/> for request services.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource.Special">
            <summary>
            A <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource"/> for special parameter types that are not user input.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource.FormFile">
            <summary>
            A <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource"/> for <see cref="T:Microsoft.AspNetCore.Http.IFormFile"/>, <see cref="T:Microsoft.AspNetCore.Http.IFormCollection"/>, and <see cref="T:Microsoft.AspNetCore.Http.IFormFileCollection"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource.#ctor(System.String,System.String,System.Boolean,System.Boolean)">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource"/>.
            </summary>
            <param name="id">The id, a unique identifier.</param>
            <param name="displayName">The display name.</param>
            <param name="isGreedy">A value indicating whether or not the source is greedy.</param>
            <param name="isFromRequest">
            A value indicating whether or not the data comes from the HTTP request.
            </param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource.DisplayName">
            <summary>
            Gets the display name for the source.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource.Id">
            <summary>
            Gets the unique identifier for the source. Sources are compared based on their Id.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource.IsGreedy">
            <summary>
            Gets a value indicating whether or not a source is greedy. A greedy source will bind a model in
            a single operation, and will not decompose the model into sub-properties.
            </summary>
            <remarks>
            <para>
            For sources based on a <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IValueProvider"/>, setting <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource.IsGreedy"/> to <c>false</c>
            will most closely describe the behavior. This value is used inside the default model binders to
            determine whether or not to attempt to bind properties of a model.
            </para>
            <para>
            Set <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource.IsGreedy"/> to <c>true</c> for most custom <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IModelBinder"/> implementations.
            </para>
            <para>
            If a source represents an <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IModelBinder"/> which will recursively traverse a model's properties
            and bind them individually using <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IValueProvider"/>, then set <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource.IsGreedy"/> to
            <c>true</c>.
            </para>
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource.IsFromRequest">
            <summary>
            Gets a value indicating whether or not the binding source uses input from the current HTTP request.
            </summary>
            <remarks>
            Some sources (like <see cref="F:Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource.Services"/>) are based on application state and not user
            input. These are excluded by default from ApiExplorer diagnostics.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource.CanAcceptDataFrom(Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource)">
             <summary>
             Gets a value indicating whether or not the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource"/> can accept
             data from <paramref name="bindingSource"/>.
             </summary>
             <param name="bindingSource">The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource"/> to consider as input.</param>
             <returns><c>True</c> if the source is compatible, otherwise <c>false</c>.</returns>
             <remarks>
             When using this method, it is expected that the left-hand-side is metadata specified
             on a property or parameter for model binding, and the right hand side is a source of
             data used by a model binder or value provider.
            
             This distinction is important as the left-hand-side may be a composite, but the right
             may not.
             </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource.Equals(Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource.op_Equality(Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource,Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource.op_Inequality(Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource,Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.CompositeBindingSource">
            <summary>
            A <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.CompositeBindingSource.BindingSources"/> which can represent multiple value-provider data sources.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.CompositeBindingSource.Create(System.Collections.Generic.IEnumerable{Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource},System.String)">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.CompositeBindingSource"/>.
            </summary>
            <param name="bindingSources">
            The set of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource"/> entries.
            Must be value-provider sources and user input.
            </param>
            <param name="displayName">The display name for the composite source.</param>
            <returns>A <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.CompositeBindingSource"/>.</returns>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.CompositeBindingSource.BindingSources">
            <summary>
            Gets the set of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource"/> entries.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.CompositeBindingSource.CanAcceptDataFrom(Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.EmptyBodyBehavior">
            <summary>
            Determines the behavior for processing empty bodies during input formatting.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Mvc.ModelBinding.EmptyBodyBehavior.Default">
            <summary>
            Uses the framework default behavior for processing empty bodies.
            This is typically configured using <c>MvcOptions.AllowEmptyInputInBodyModelBinding</c>.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Mvc.ModelBinding.EmptyBodyBehavior.Allow">
            <summary>
            Empty bodies are treated as valid inputs.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Mvc.ModelBinding.EmptyBodyBehavior.Disallow">
            <summary>
            Empty bodies are treated as invalid inputs.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.EnumGroupAndName">
            <summary>
            An abstraction used when grouping enum values for <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.EnumGroupedDisplayNamesAndValues"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.EnumGroupAndName.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.EnumGroupAndName"/> structure. This constructor should
            not be used in any site where localization is important.
            </summary>
            <param name="group">The group name.</param>
            <param name="name">The name.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.EnumGroupAndName.#ctor(System.String,System.Func{System.String})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.EnumGroupAndName"/> structure.
            </summary>
            <param name="group">The group name.</param>
            <param name="name">A <see cref="T:System.Func`1"/> which will return the name.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.EnumGroupAndName.Group">
            <summary>
            Gets the Group name.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.EnumGroupAndName.Name">
            <summary>
            Gets the name.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.IBinderTypeProviderMetadata">
            <summary>
            Provides a <see cref="T:System.Type"/> which implements <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IModelBinder"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.IBinderTypeProviderMetadata.BinderType">
            <summary>
            A <see cref="T:System.Type"/> which implements either <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IModelBinder"/>.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.IBindingSourceMetadata">
            <summary>
            Metadata which specifies the data source for model binding.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.IBindingSourceMetadata.BindingSource">
            <summary>
            Gets the <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.IBindingSourceMetadata.BindingSource"/>.
            </summary>
            <remarks>
            The <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.IBindingSourceMetadata.BindingSource"/> is metadata which can be used to determine which data
            sources are valid for model binding of a property or parameter.
            </remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.IModelBinder">
            <summary>
            Defines an interface for model binders.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.IModelBinder.BindModelAsync(Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingContext)">
            <summary>
            Attempts to bind a model.
            </summary>
            <param name="bindingContext">The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingContext"/>.</param>
            <returns>
            <para>
            A <see cref="T:System.Threading.Tasks.Task"/> which will complete when the model binding process completes.
            </para>
            <para>
            If model binding was successful, the <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingContext.Result"/> should have
            <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingResult.IsModelSet"/> set to <c>true</c>.
            </para>
            <para>
            A model binder that completes successfully should set <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingContext.Result"/> to
            a value returned from <see cref="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingResult.Success(System.Object)"/>.
            </para>
            </returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.IModelBinderProvider">
            <summary>
            Creates <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IModelBinder"/> instances. Register <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IModelBinderProvider"/>
            instances in <c>MvcOptions</c>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.IModelBinderProvider.GetBinder(Microsoft.AspNetCore.Mvc.ModelBinding.ModelBinderProviderContext)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IModelBinder"/> based on <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBinderProviderContext"/>.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBinderProviderContext"/>.</param>
            <returns>An <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IModelBinder"/>.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.IModelMetadataProvider">
            <summary>
            A provider that can supply instances of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/>.
            </summary>
            <remarks>
            While not obsolete, implementing or using <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadataProvider" /> is preferred over <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IModelMetadataProvider"/>.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.IModelMetadataProvider.GetMetadataForType(System.Type)">
            <summary>
            Supplies metadata describing a <see cref="T:System.Type"/>.
            </summary>
            <param name="modelType">The <see cref="T:System.Type"/>.</param>
            <returns>A <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/> instance describing the <see cref="T:System.Type"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.IModelMetadataProvider.GetMetadataForProperties(System.Type)">
            <summary>
            Supplies metadata describing the properties of a <see cref="T:System.Type"/>.
            </summary>
            <param name="modelType">The <see cref="T:System.Type"/>.</param>
            <returns>A set of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/> instances describing properties of the <see cref="T:System.Type"/>.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.IModelNameProvider">
            <summary>
            Represents an entity which can provide model name as metadata.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.IModelNameProvider.Name">
            <summary>
            Model name.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.IPropertyFilterProvider">
            <summary>
            Provides a predicate which can determines which model properties or parameters should be bound by model binding.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.IPropertyFilterProvider.PropertyFilter">
            <summary>
            <para>
            Gets a predicate which can determines which model properties should be bound by model binding.
            </para>
            <para>
            This predicate is also used to determine which parameters are bound when a model's constructor is bound.
            </para>
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.IRequestPredicateProvider">
            <summary>
            An interface that allows a top-level model to be bound or not bound based on state associated
            with the current request.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.IRequestPredicateProvider.RequestPredicate">
            <summary>
            Gets a function which determines whether or not the model object should be bound based
            on the current request.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.IValueProvider">
            <summary>
            Defines the methods that are required for a value provider.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.IValueProvider.ContainsPrefix(System.String)">
            <summary>
            Determines whether the collection contains the specified prefix.
            </summary>
            <param name="prefix">The prefix to search for.</param>
            <returns>true if the collection contains the specified prefix; otherwise, false.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.IValueProvider.GetValue(System.String)">
            <summary>
            Retrieves a value object using the specified key.
            </summary>
            <param name="key">The key of the value object to retrieve.</param>
            <returns>The value object for the specified key. If the exact key is not found, <see cref="F:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderResult.None" />.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.IValueProviderFactory">
            <summary>
            A factory for creating <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IValueProvider"/> instances.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.IValueProviderFactory.CreateValueProviderAsync(Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderFactoryContext)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IValueProvider"/> with values from the current request
            and adds it to <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderFactoryContext.ValueProviders"/> list.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderFactoryContext"/>.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that when completed will add an <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IValueProvider"/> instance
            to <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderFactoryContext.ValueProviders"/> list if applicable.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelBindingMessageProvider">
            <summary>
            Provider for error messages the model binding system detects.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelBindingMessageProvider.MissingBindRequiredValueAccessor">
            <summary>
            Error message the model binding system adds when a property with an associated
            <c>BindRequiredAttribute</c> is not bound.
            </summary>
            <value>
            Default <see cref="T:System.String"/> is "A value for the '{0}' parameter or property was not provided.".
            </value>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelBindingMessageProvider.MissingKeyOrValueAccessor">
            <summary>
            Error message the model binding system adds when either the key or the value of a
            <see cref="T:System.Collections.Generic.KeyValuePair`2"/> is bound but not both.
            </summary>
            <value>Default <see cref="T:System.String"/> is "A value is required.".</value>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelBindingMessageProvider.MissingRequestBodyRequiredValueAccessor">
            <summary>
            Error message the model binding system adds when no value is provided for the request body,
            but a value is required.
            </summary>
            <value>Default <see cref="T:System.String"/> is "A non-empty request body is required.".</value>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelBindingMessageProvider.ValueMustNotBeNullAccessor">
            <summary>
            Error message the model binding system adds when a <c>null</c> value is bound to a
            non-<see cref="T:System.Nullable"/> property.
            </summary>
            <value>Default <see cref="T:System.String"/> is "The value '{0}' is invalid.".</value>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelBindingMessageProvider.AttemptedValueIsInvalidAccessor">
            <summary>
            Error message the model binding system adds when <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelError.Exception"/> is of type
            <see cref="T:System.FormatException"/> or <see cref="T:System.OverflowException"/>, value is known, and error is associated
            with a property.
            </summary>
            <value>Default <see cref="T:System.String"/> is "The value '{0}' is not valid for {1}.".</value>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelBindingMessageProvider.NonPropertyAttemptedValueIsInvalidAccessor">
            <summary>
            Error message the model binding system adds when <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelError.Exception"/> is of type
            <see cref="T:System.FormatException"/> or <see cref="T:System.OverflowException"/>, value is known, and error is associated
            with a collection element or parameter.
            </summary>
            <value>Default <see cref="T:System.String"/> is "The value '{0}' is not valid.".</value>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelBindingMessageProvider.UnknownValueIsInvalidAccessor">
            <summary>
            Error message the model binding system adds when <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelError.Exception"/> is of type
            <see cref="T:System.FormatException"/> or <see cref="T:System.OverflowException"/>, value is unknown, and error is associated
            with a property.
            </summary>
            <value>Default <see cref="T:System.String"/> is "The supplied value is invalid for {0}.".</value>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelBindingMessageProvider.NonPropertyUnknownValueIsInvalidAccessor">
            <summary>
            Error message the model binding system adds when <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelError.Exception"/> is of type
            <see cref="T:System.FormatException"/> or <see cref="T:System.OverflowException"/>, value is unknown, and error is associated
            with a collection element or parameter.
            </summary>
            <value>Default <see cref="T:System.String"/> is "The supplied value is invalid.".</value>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelBindingMessageProvider.ValueIsInvalidAccessor">
            <summary>
            Fallback error message HTML and tag helpers display when a property is invalid but the
            <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelError"/>s have <c>null</c> <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelError.ErrorMessage"/>s.
            </summary>
            <value>Default <see cref="T:System.String"/> is "The value '{0}' is invalid.".</value>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelBindingMessageProvider.ValueMustBeANumberAccessor">
            <summary>
            Error message HTML and tag helpers add for client-side validation of numeric formats. Visible in the
            browser if the field for a <c>float</c> (for example) property does not have a correctly-formatted value.
            </summary>
            <value>Default <see cref="T:System.String"/> is "The field {0} must be a number.".</value>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelBindingMessageProvider.NonPropertyValueMustBeANumberAccessor">
            <summary>
            Error message HTML and tag helpers add for client-side validation of numeric formats. Visible in the
            browser if the field for a <c>float</c> (for example) collection element or action parameter does not have a
            correctly-formatted value.
            </summary>
            <value>Default <see cref="T:System.String"/> is "The field must be a number.".</value>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelMetadataIdentity">
            <summary>
            A key type which identifies a <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelMetadataIdentity.ForType(System.Type)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelMetadataIdentity"/> for the provided model <see cref="T:System.Type"/>.
            </summary>
            <param name="modelType">The model <see cref="T:System.Type"/>.</param>
            <returns>A <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelMetadataIdentity"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelMetadataIdentity.ForProperty(System.Type,System.String,System.Type)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelMetadataIdentity"/> for the provided property.
            </summary>
            <param name="modelType">The model type.</param>
            <param name="name">The name of the property.</param>
            <param name="containerType">The container type of the model property.</param>
            <returns>A <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelMetadataIdentity"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelMetadataIdentity.ForProperty(System.Reflection.PropertyInfo,System.Type,System.Type)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelMetadataIdentity"/> for the provided property.
            </summary>
            <param name="modelType">The model type.</param>
            <param name="propertyInfo">The property.</param>
            <param name="containerType">The container type of the model property.</param>
            <returns>A <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelMetadataIdentity"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelMetadataIdentity.ForParameter(System.Reflection.ParameterInfo)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelMetadataIdentity"/> for the provided parameter.
            </summary>
            <param name="parameter">The <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelMetadataIdentity.ParameterInfo" />.</param>
            <returns>A <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelMetadataIdentity"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelMetadataIdentity.ForParameter(System.Reflection.ParameterInfo,System.Type)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelMetadataIdentity"/> for the provided parameter with the specified
            model type.
            </summary>
            <param name="parameter">The <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelMetadataIdentity.ParameterInfo" />.</param>
            <param name="modelType">The model type.</param>
            <returns>A <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelMetadataIdentity"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelMetadataIdentity.ForConstructor(System.Reflection.ConstructorInfo,System.Type)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelMetadataIdentity"/> for the provided parameter with the specified
            model type.
            </summary>
            <param name="constructor">The <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelMetadataIdentity.ConstructorInfo" />.</param>
            <param name="modelType">The model type.</param>
            <returns>A <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelMetadataIdentity"/>.</returns>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelMetadataIdentity.ContainerType">
            <summary>
            Gets the <see cref="T:System.Type"/> defining the model property represented by the current
            instance, or <c>null</c> if the current instance does not represent a property.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelMetadataIdentity.ModelType">
            <summary>
            Gets the <see cref="T:System.Type"/> represented by the current instance.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelMetadataIdentity.MetadataKind">
            <summary>
            Gets a value indicating the kind of metadata represented by the current instance.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelMetadataIdentity.Name">
            <summary>
            Gets the name of the current instance if it represents a parameter or property, or <c>null</c> if
            the current instance represents a type.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelMetadataIdentity.ParameterInfo">
            <summary>
            Gets a descriptor for the parameter, or <c>null</c> if this instance
            does not represent a parameter.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelMetadataIdentity.PropertyInfo">
            <summary>
            Gets a descriptor for the property, or <c>null</c> if this instance
            does not represent a property.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelMetadataIdentity.ConstructorInfo">
            <summary>
            Gets a descriptor for the constructor, or <c>null</c> if this instance
            does not represent a constructor.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelMetadataIdentity.Equals(Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelMetadataIdentity)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelMetadataIdentity.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelMetadataIdentity.GetHashCode">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelMetadataKind">
            <summary>
            Enumeration for the kinds of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/>
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelMetadataKind.Type">
            <summary>
            Used for <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/> for a <see cref="T:System.Type"/>.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelMetadataKind.Property">
            <summary>
            Used for <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/> for a property.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelMetadataKind.Parameter">
            <summary>
            Used for <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/> for a parameter.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelMetadataKind.Constructor">
            <summary>
            <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/> for a constructor.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBinderProviderContext">
            <summary>
            A context object for <see cref="M:Microsoft.AspNetCore.Mvc.ModelBinding.IModelBinderProvider.GetBinder(Microsoft.AspNetCore.Mvc.ModelBinding.ModelBinderProviderContext)"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBinderProviderContext.CreateBinder(Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata)">
            <summary>
            Creates an <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IModelBinder"/> for the given <paramref name="metadata"/>.
            </summary>
            <param name="metadata">The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/> for the model.</param>
            <returns>An <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IModelBinder"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBinderProviderContext.CreateBinder(Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata,Microsoft.AspNetCore.Mvc.ModelBinding.BindingInfo)">
            <summary>
            Creates an <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IModelBinder"/> for the given <paramref name="metadata"/>
            and <paramref name="bindingInfo"/>.
            </summary>
            <param name="metadata">The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/> for the model.</param>
            <param name="bindingInfo">The <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBinderProviderContext.BindingInfo"/> that should be used
            for creating the binder.</param>
            <returns>An <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IModelBinder"/>.</returns>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBinderProviderContext.BindingInfo">
            <summary>
            Gets the <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBinderProviderContext.BindingInfo"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBinderProviderContext.Metadata">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBinderProviderContext.MetadataProvider">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IModelMetadataProvider"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBinderProviderContext.Services">
            <summary>
            Gets the <see cref="T:System.IServiceProvider"/>.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingContext">
            <summary>
            A context that contains operating information for model binding and validation.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingContext.ActionContext">
            <summary>
            Represents the <see cref="T:Microsoft.AspNetCore.Mvc.ActionContext"/> associated with this context.
            </summary>
            <remarks>
            The property setter is provided for unit testing purposes only.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingContext.BinderModelName">
            <summary>
            Gets or sets a model name which is explicitly set using an <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IModelNameProvider"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingContext.BindingSource">
            <summary>
            Gets or sets a value which represents the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.BindingSource"/> associated with the
            <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingContext.Model"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingContext.FieldName">
            <summary>
            Gets or sets the name of the current field being bound.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingContext.HttpContext">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Http.HttpContext"/> associated with this context.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingContext.IsTopLevelObject">
            <summary>
            Gets or sets an indication that the current binder is handling the top-level object.
            </summary>
            <remarks>Passed into the model binding system.</remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingContext.Model">
            <summary>
            Gets or sets the model value for the current operation.
            </summary>
            <remarks>
            The <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingContext.Model"/> will typically be set for a binding operation that works
            against a pre-existing model object to update certain properties.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingContext.ModelMetadata">
            <summary>
            Gets or sets the metadata for the model associated with this context.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingContext.ModelName">
            <summary>
            Gets or sets the name of the model. This property is used as a key for looking up values in
            <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IValueProvider"/> during model binding.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingContext.OriginalModelName">
            <summary>
            Gets or sets the name of the top-level model. This is not reset to <see cref="F:System.String.Empty"/> when value
            providers have no match for that model.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingContext.ModelState">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary"/> used to capture <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry"/> values
            for properties in the object graph of the model when binding.
            </summary>
            <remarks>
            The property setter is provided for unit testing purposes only.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingContext.ModelType">
            <summary>
            Gets the type of the model.
            </summary>
            <remarks>
            The <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingContext.ModelMetadata"/> property must be set to access this property.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingContext.PropertyFilter">
            <summary>
            Gets or sets a predicate which will be evaluated for each property to determine if the property
            is eligible for model binding.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingContext.ValidationState">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationStateDictionary"/>. Used for tracking validation state to
            customize validation behavior for a model object.
            </summary>
            <remarks>
            The property setter is provided for unit testing purposes only.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingContext.ValueProvider">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IValueProvider"/> associated with this context.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingContext.Result">
            <summary>
            <para>
            Gets or sets a <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingResult"/> which represents the result of the model binding process.
            </para>
            <para>
            Before an <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IModelBinder"/> is called, <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingContext.Result"/> will be set to a value indicating
            failure. The binder should set <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingContext.Result"/> to a value created with
            <see cref="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingResult.Success(System.Object)"/> if model binding succeeded.
            </para>
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingContext.EnterNestedScope(Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata,System.String,System.String,System.Object)">
            <summary>
            Pushes a layer of state onto this context. <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IModelBinder"/> implementations will call this as
            part of recursion when binding properties or collection items.
            </summary>
            <param name="modelMetadata">
            <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/> to assign to the <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingContext.ModelMetadata"/> property.
            </param>
            <param name="fieldName">Name to assign to the <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingContext.FieldName"/> property.</param>
            <param name="modelName">Name to assign to the <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingContext.ModelName"/> property.</param>
            <param name="model">Instance to assign to the <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingContext.Model"/> property.</param>
            <returns>
            A <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingContext.NestedScope"/> scope object which should be used in a <c>using</c> statement where
            <see cref="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingContext.EnterNestedScope(Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata,System.String,System.String,System.Object)"/> is called.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingContext.EnterNestedScope">
            <summary>
            Pushes a layer of state onto this context. <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IModelBinder"/> implementations will call this as
            part of recursion when binding properties or collection items.
            </summary>
            <returns>
            A <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingContext.NestedScope"/> scope object which should be used in a <c>using</c> statement where
            <see cref="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingContext.EnterNestedScope"/> is called.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingContext.ExitNestedScope">
            <summary>
            Removes a layer of state pushed by calling <see cref="M:EnterNestedScope"/>.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingContext.NestedScope">
            <summary>
            Return value of <see cref="M:EnterNestedScope"/>. Should be disposed
            by caller when child binding context state should be popped off of
            the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingContext"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingContext.NestedScope.#ctor(Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingContext)">
            <summary>
            Initializes the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingContext.NestedScope"/> for a <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingContext"/>.
            </summary>
            <param name="context"></param>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingContext.NestedScope.Dispose">
            <summary>
            Exits the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingContext.NestedScope"/> created by calling <see cref="M:EnterNestedScope"/>.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingResult">
            <summary>
            Contains the result of model binding.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingResult.Failed">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingResult"/> representing a failed model binding operation.
            </summary>
            <returns>A <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingResult"/> representing a failed model binding operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingResult.Success(System.Object)">
            <summary>
            Creates a <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingResult"/> representing a successful model binding operation.
            </summary>
            <param name="model">The model value. May be <c>null.</c></param>
            <returns>A <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingResult"/> representing a successful model bind.</returns>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingResult.Model">
            <summary>
            Gets the model associated with this context.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingResult.IsModelSet">
            <summary>
            <para>
            Gets a value indicating whether or not the <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingResult.Model"/> value has been set.
            </para>
            <para>
            This property can be used to distinguish between a model binder which does not find a value and
            the case where a model binder sets the <c>null</c> value.
            </para>
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingResult.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingResult.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingResult.Equals(Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingResult)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingResult.ToString">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingResult.op_Equality(Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingResult,Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingResult)">
            <summary>
            Compares <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingResult"/> objects for equality.
            </summary>
            <param name="x">A <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingResult"/>.</param>
            <param name="y">A <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingResult"/>.</param>
            <returns><c>true</c> if the objects are equal, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingResult.op_Inequality(Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingResult,Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingResult)">
            <summary>
            Compares <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingResult"/> objects for inequality.
            </summary>
            <param name="x">A <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingResult"/>.</param>
            <param name="y">A <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelBindingResult"/>.</param>
            <returns><c>true</c> if the objects are not equal, otherwise <c>false</c>.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelError">
            <summary>
            An error that occurred during model binding and validation.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelError.#ctor(System.Exception)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelError"/> with the specified <paramref name="exception"/>.
            </summary>
            <param name="exception">The <see cref="T:System.Exception"/>.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelError.#ctor(System.Exception,System.String)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelError"/> with the specified <paramref name="exception"/>
            and specified <paramref name="errorMessage"/>.
            </summary>
            <param name="exception">The <see cref="T:System.Exception"/>.</param>
            <param name="errorMessage">The error message.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelError.#ctor(System.String)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelError"/> with the specified <paramref name="errorMessage"/>.
            </summary>
            <param name="errorMessage">The error message.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelError.Exception">
            <summary>
            Gets the <see cref="T:System.Exception"/> associated with this <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelError"/> instance.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelError.ErrorMessage">
            <summary>
            Gets the error message associated with this <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelError"/> instance.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelErrorCollection">
            <summary>
            A collection of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelError"/> instances.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelErrorCollection.Add(System.Exception)">
            <summary>
            Adds the specified <paramref name="exception"/> instance.
            </summary>
            <param name="exception">The <see cref="T:System.Exception"/></param>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelErrorCollection.Add(System.String)">
            <summary>
            Adds the specified error message.
            </summary>
            <param name="errorMessage">The error message.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata">
            <summary>
            A metadata representation of a model type, property or parameter.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.DefaultOrder">
            <summary>
            The default value of <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.Order"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.#ctor(Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelMetadataIdentity)">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/>.
            </summary>
            <param name="identity">The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelMetadataIdentity"/>.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.ContainerType">
            <summary>
            Gets the type containing the property if this metadata is for a property; <see langword="null"/> otherwise.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.ContainerMetadata">
            <summary>
            Gets the metadata for <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.ContainerType"/> if this metadata is for a property;
            <see langword="null"/> otherwise.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.MetadataKind">
            <summary>
            Gets a value indicating the kind of metadata element represented by the current instance.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.ModelType">
            <summary>
            Gets the model type represented by the current instance.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.Name">
            <summary>
            Gets the name of the parameter or property if this metadata is for a parameter or property;
            <see langword="null"/> otherwise i.e. if this is the metadata for a type.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.ParameterName">
            <summary>
            Gets the name of the parameter if this metadata is for a parameter; <see langword="null"/> otherwise.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.PropertyName">
            <summary>
            Gets the name of the property if this metadata is for a property; <see langword="null"/> otherwise.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.Identity">
            <summary>
            Gets the key for the current instance.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.AdditionalValues">
            <summary>
            Gets a collection of additional information about the model.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.Properties">
            <summary>
            Gets the collection of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/> instances for the model's properties.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.BoundConstructorParameterMapping">
            <summary>
            A mapping from parameters to their corresponding properties on a record type.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.BoundConstructorPropertyMapping">
            <summary>
            A mapping from properties to their corresponding constructor parameter on a record type.
            This is the inverse mapping of <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.BoundConstructorParameterMapping"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.BoundConstructor">
            <summary>
            Gets <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/> instance for a constructor of a record type that is used during binding and validation.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.BoundConstructorParameters">
            <summary>
            Gets the collection of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/> instances for parameters on a <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.BoundConstructor"/>.
            This is only available when <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.MetadataKind"/> is <see cref="F:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelMetadataKind.Constructor"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.BinderModelName">
            <summary>
            Gets the name of a model if specified explicitly using <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IModelNameProvider"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.BinderType">
            <summary>
            Gets the <see cref="T:System.Type"/> of an <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IModelBinder"/> of a model if specified explicitly using
            <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IBinderTypeProviderMetadata"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.BindingSource">
            <summary>
            Gets a binder metadata for this model.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.ConvertEmptyStringToNull">
            <summary>
            Gets a value indicating whether or not to convert an empty string value or one containing only whitespace
            characters to <c>null</c> when representing a model as text.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.DataTypeName">
            <summary>
            Gets the name of the model's datatype.  Overrides <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.ModelType"/> in some
            display scenarios.
            </summary>
            <value><c>null</c> unless set manually or through additional metadata e.g. attributes.</value>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.Description">
            <summary>
            Gets the description of the model.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.DisplayFormatString">
            <summary>
            Gets the format string (see https://msdn.microsoft.com/en-us/library/txafckwd.aspx) used to display the
            model.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.DisplayName">
            <summary>
            Gets the display name of the model.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.EditFormatString">
            <summary>
            Gets the format string (see https://msdn.microsoft.com/en-us/library/txafckwd.aspx) used to edit the model.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.ElementMetadata">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/> for elements of <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.ModelType"/> if that <see cref="T:System.Type"/>
            implements <see cref="T:System.Collections.IEnumerable"/>.
            </summary>
            <value>
            <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/> for <c>T</c> if <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.ModelType"/> implements
            <see cref="T:System.Collections.Generic.IEnumerable`1"/>. <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/> for <c>object</c> if <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.ModelType"/>
            implements <see cref="T:System.Collections.IEnumerable"/> but not <see cref="T:System.Collections.Generic.IEnumerable`1"/>. <c>null</c> otherwise i.e. when
            <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.IsEnumerableType"/> is <c>false</c>.
            </value>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.EnumGroupedDisplayNamesAndValues">
            <summary>
            Gets the ordered and grouped display names and values of all <see cref="T:System.Enum"/> values in
            <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.UnderlyingOrModelType"/>.
            </summary>
            <value>
            An <see cref="T:System.Collections.Generic.IEnumerable`1"/> of <see cref="T:System.Collections.Generic.KeyValuePair`2"/> of mappings between
            <see cref="T:System.Enum"/> field groups, names and values. <c>null</c> if <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.IsEnum"/> is <c>false</c>.
            </value>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.EnumNamesAndValues">
            <summary>
            Gets the names and values of all <see cref="T:System.Enum"/> values in <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.UnderlyingOrModelType"/>.
            </summary>
            <value>
            An <see cref="T:System.Collections.Generic.IReadOnlyDictionary`2"/> of mappings between <see cref="T:System.Enum"/> field names
            and values. <c>null</c> if <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.IsEnum"/> is <c>false</c>.
            </value>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.HasNonDefaultEditFormat">
            <summary>
            Gets a value indicating whether <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.EditFormatString"/> has a non-<c>null</c>, non-empty
            value different from the default for the datatype.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.HtmlEncode">
            <summary>
            Gets a value indicating whether the value should be HTML-encoded.
            </summary>
            <value>If <c>true</c>, value should be HTML-encoded. Default is <c>true</c>.</value>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.HideSurroundingHtml">
            <summary>
            Gets a value indicating whether the "HiddenInput" display template should return
            <c>string.Empty</c> (not the expression value) and whether the "HiddenInput" editor template should not
            also return the expression value (together with the hidden &lt;input&gt; element).
            </summary>
            <remarks>
            If <c>true</c>, also causes the default <see cref="T:System.Object"/> display and editor templates to return HTML
            lacking the usual per-property &lt;div&gt; wrapper around the associated property. Thus the default
            <see cref="T:System.Object"/> display template effectively skips the property and the default <see cref="T:System.Object"/>
            editor template returns only the hidden &lt;input&gt; element for the property.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.IsBindingAllowed">
            <summary>
            Gets a value indicating whether or not the model value can be bound by model binding. This is only
            applicable when the current instance represents a property.
            </summary>
            <remarks>
            If <c>true</c> then the model value is considered supported by model binding and can be set
            based on provided input in the request.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.IsBindingRequired">
            <summary>
            Gets a value indicating whether or not the model value is required by model binding. This is only
            applicable when the current instance represents a property.
            </summary>
            <remarks>
            If <c>true</c> then the model value is considered required by model binding and must have a value
            supplied in the request to be considered valid.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.IsEnum">
            <summary>
            Gets a value indicating whether <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.UnderlyingOrModelType"/> is for an <see cref="T:System.Enum"/>.
            </summary>
            <value>
            <c>true</c> if <c>type.IsEnum</c> (<c>type.GetTypeInfo().IsEnum</c> for DNX Core 5.0) is <c>true</c> for
            <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.UnderlyingOrModelType"/>; <c>false</c> otherwise.
            </value>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.IsFlagsEnum">
            <summary>
            Gets a value indicating whether <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.UnderlyingOrModelType"/> is for an <see cref="T:System.Enum"/> with an
            associated <see cref="T:System.FlagsAttribute"/>.
            </summary>
            <value>
            <c>true</c> if <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.IsEnum"/> is <c>true</c> and <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.UnderlyingOrModelType"/> has an
            associated <see cref="T:System.FlagsAttribute"/>; <c>false</c> otherwise.
            </value>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.IsReadOnly">
            <summary>
            Gets a value indicating whether or not the model value is read-only. This is only applicable when
            the current instance represents a property.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.IsRequired">
            <summary>
            Gets a value indicating whether or not the model value is required. This is only applicable when
            the current instance represents a property.
            </summary>
            <remarks>
            <para>
            If <c>true</c> then the model value is considered required by validators.
            </para>
            <para>
            By default an implicit <c>System.ComponentModel.DataAnnotations.RequiredAttribute</c> will be added
            if not present when <c>true.</c>.
            </para>
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.ModelBindingMessageProvider">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.ModelBindingMessageProvider"/> instance.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.Order">
            <summary>
            Gets a value indicating where the current metadata should be ordered relative to other properties
            in its containing type.
            </summary>
            <value>The order value of the current metadata.</value>
            <remarks>
            <para>For example this property is used to order items in <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.Properties"/>.</para>
            <para>The default order is <c>10000</c>.</para>
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.Placeholder">
            <summary>
            Gets the text to display as a placeholder value for an editor.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.NullDisplayText">
            <summary>
            Gets the text to display when the model is <c>null</c>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.PropertyFilterProvider">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IPropertyFilterProvider"/>, which can determine which properties
            should be model bound.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.ShowForDisplay">
            <summary>
            Gets a value that indicates whether the property should be displayed in read-only views.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.ShowForEdit">
            <summary>
            Gets a value that indicates whether the property should be displayed in editable views.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.SimpleDisplayProperty">
            <summary>
            Gets  a value which is the name of the property used to display the model.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.TemplateHint">
            <summary>
            Gets a string used by the templating system to discover display-templates and editor-templates.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.PropertyValidationFilter">
            <summary>
            Gets an <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.IPropertyValidationFilter"/> implementation that indicates whether this model should be
            validated. If <c>null</c>, properties with this <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/> are validated.
            </summary>
            <value>Defaults to <c>null</c>.</value>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.ValidateChildren">
            <summary>
            Gets a value that indicates whether properties or elements of the model should be validated.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.HasValidators">
            <summary>
            Gets a value that indicates if the model, or one of it's properties, or elements has associated validators.
            </summary>
            <remarks>
            When <see langword="false"/>, validation can be assume that the model is valid (<see cref="F:Microsoft.AspNetCore.Mvc.ModelBinding.ModelValidationState.Valid"/>) without
            inspecting the object graph.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.ValidatorMetadata">
            <summary>
            Gets a collection of metadata items for validators.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.ElementType">
            <summary>
            Gets the <see cref="T:System.Type"/> for elements of <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.ModelType"/> if that <see cref="T:System.Type"/>
            implements <see cref="T:System.Collections.IEnumerable"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.IsComplexType">
            <summary>
            Gets a value indicating whether <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.ModelType"/> is a complex type.
            </summary>
            <remarks>
            A complex type is defined as a <see cref="T:System.Type"/> without a <see cref="T:System.ComponentModel.TypeConverter"/> that can convert
            from <see cref="T:System.String"/>. Most POCO and <see cref="T:System.Collections.IEnumerable"/> types are therefore complex. Most, if
            not all, BCL value types are simple types.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.IsNullableValueType">
            <summary>
            Gets a value indicating whether or not <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.ModelType"/> is a <see cref="T:System.Nullable`1"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.IsCollectionType">
            <summary>
            Gets a value indicating whether or not <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.ModelType"/> is a collection type.
            </summary>
            <remarks>
            A collection type is defined as a <see cref="T:System.Type"/> which is assignable to <see cref="T:System.Collections.Generic.ICollection`1"/>.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.IsEnumerableType">
            <summary>
            Gets a value indicating whether or not <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.ModelType"/> is an enumerable type.
            </summary>
            <remarks>
            An enumerable type is defined as a <see cref="T:System.Type"/> which is assignable to
            <see cref="T:System.Collections.IEnumerable"/>, and is not a <see cref="T:System.String"/>.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.IsReferenceOrNullableType">
            <summary>
            Gets a value indicating whether or not <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.ModelType"/> allows <c>null</c> values.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.UnderlyingOrModelType">
            <summary>
            Gets the underlying type argument if <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.ModelType"/> inherits from <see cref="T:System.Nullable`1"/>.
            Otherwise gets <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.ModelType"/>.
            </summary>
            <remarks>
            Identical to <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.ModelType"/> unless <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.IsNullableValueType"/> is <c>true</c>.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.PropertyGetter">
            <summary>
            Gets a property getter delegate to get the property value from a model object.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.PropertySetter">
            <summary>
            Gets a property setter delegate to set the property value on a model object.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.BoundConstructorInvoker">
            <summary>
            Gets a delegate that invokes the bound constructor <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.BoundConstructor" /> if non-<see langword="null" />.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.PropertyHasValidators">
            <summary>
            Gets a value that determines if validators can be constructed using metadata exclusively defined on the property.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.ThrowIfRecordTypeHasValidationOnProperties">
            <summary>
            Throws if the ModelMetadata is for a record type with validation on properties.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.GetDisplayName">
            <summary>
            Gets a display name for the model.
            </summary>
            <remarks>
            <see cref="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.GetDisplayName"/> will return the first of the following expressions which has a
            non-<see langword="null"/> value: <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.DisplayName"/>, <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.Name"/>, or <c>ModelType.Name</c>.
            </remarks>
            <returns>The display name.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.Equals(Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.GetMetadataForType(System.Type)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.GetMetadataForProperties(System.Type)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadataProvider">
            <summary>
            A provider that can supply instances of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadataProvider.GetMetadataForProperties(System.Type)">
            <summary>
            Supplies metadata describing the properties of a <see cref="T:System.Type"/>.
            </summary>
            <param name="modelType">The <see cref="T:System.Type"/>.</param>
            <returns>A set of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/> instances describing properties of the <see cref="T:System.Type"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadataProvider.GetMetadataForType(System.Type)">
            <summary>
            Supplies metadata describing a <see cref="T:System.Type"/>.
            </summary>
            <param name="modelType">The <see cref="T:System.Type"/>.</param>
            <returns>A <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/> instance describing the <see cref="T:System.Type"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadataProvider.GetMetadataForParameter(System.Reflection.ParameterInfo)">
            <summary>
            Supplies metadata describing a parameter.
            </summary>
            <param name="parameter">The <see cref="T:System.Reflection.ParameterInfo"/>.</param>
            <returns>A <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/> instance describing the <paramref name="parameter"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadataProvider.GetMetadataForParameter(System.Reflection.ParameterInfo,System.Type)">
            <summary>
            Supplies metadata describing a parameter.
            </summary>
            <param name="parameter">The <see cref="T:System.Reflection.ParameterInfo"/></param>
            <param name="modelType">The actual model type.</param>
            <returns>A <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/> instance describing the <paramref name="parameter"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadataProvider.GetMetadataForProperty(System.Reflection.PropertyInfo,System.Type)">
            <summary>
            Supplies metadata describing a property.
            </summary>
            <param name="propertyInfo">The <see cref="T:System.Reflection.PropertyInfo"/>.</param>
            <param name="modelType">The actual model type.</param>
            <returns>A <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/> instance describing the <paramref name="propertyInfo"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadataProvider.GetMetadataForConstructor(System.Reflection.ConstructorInfo,System.Type)">
            <summary>
            Supplies metadata describing a constructor.
            </summary>
            <param name="constructor">The <see cref="T:System.Reflection.ConstructorInfo"/>.</param>
            <param name="modelType">The type declaring the constructor.</param>
            <returns>A <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/> instance describing the <paramref name="constructor"/>.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelPropertyCollection">
            <summary>
            A read-only collection of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/> objects which represent model properties.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelPropertyCollection.#ctor(System.Collections.Generic.IEnumerable{Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata})">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelPropertyCollection"/>.
            </summary>
            <param name="properties">The properties.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelPropertyCollection.Item(System.String)">
            <summary>
            Gets a <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/> instance for the property corresponding to <paramref name="propertyName"/>.
            </summary>
            <param name="propertyName">
            The property name. Property names are compared using <see cref="F:System.StringComparison.Ordinal"/>.
            </param>
            <returns>
            The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/> instance for the property specified by <paramref name="propertyName"/>, or
            <c>null</c> if no match can be found.
            </returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary">
            <summary>
            Represents the state of an attempt to bind values from an HTTP Request to an action method, which includes
            validation information.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.DefaultMaxAllowedErrors">
            <summary>
            The default value for <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.MaxAllowedErrors"/> of <c>200</c>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.#ctor(System.Int32,System.Int32,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.#ctor(Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary"/> class by using values that are copied
            from the specified <paramref name="dictionary"/>.
            </summary>
            <param name="dictionary">The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary"/> to copy values from.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.Root">
            <summary>
            Root entry for the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.MaxAllowedErrors">
            <summary>
            Gets or sets the maximum allowed model state errors in this instance of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary"/>.
            Defaults to <c>200</c>.
            </summary>
            <remarks>
            <para>
            <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary"/> tracks the number of model errors added by calls to
            <see cref="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.AddModelError(System.String,System.Exception,Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata)"/> or
            <see cref="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.TryAddModelError(System.String,System.Exception,Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata)"/>.
            Once the value of <c>MaxAllowedErrors - 1</c> is reached, if another attempt is made to add an error,
            the error message will be ignored and a <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.TooManyModelErrorsException"/> will be added.
            </para>
            <para>
            Errors added via modifying <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry"/> directly do not count towards this limit.
            </para>
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.HasReachedMaxErrors">
            <summary>
            Gets a value indicating whether or not the maximum number of errors have been
            recorded.
            </summary>
            <remarks>
            Returns <c>true</c> if a <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.TooManyModelErrorsException"/> has been recorded;
            otherwise <c>false</c>.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.ErrorCount">
            <summary>
            Gets the number of errors added to this instance of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary"/> via
            <see cref="M:AddModelError"/> or <see cref="M:TryAddModelError"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.Count">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.Keys">
            <summary>
            Gets the key sequence.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.System#Collections#Generic#IReadOnlyDictionary{System#String,Microsoft#AspNetCore#Mvc#ModelBinding#ModelStateEntry}#Keys">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.Values">
            <summary>
            Gets the value sequence.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.System#Collections#Generic#IReadOnlyDictionary{System#String,Microsoft#AspNetCore#Mvc#ModelBinding#ModelStateEntry}#Values">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.IsValid">
            <summary>
            Gets a value that indicates whether any model state values in this model state dictionary is invalid or not validated.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.ValidationState">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.Item(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.TryAddModelException(System.String,System.Exception)">
            <summary>
            Adds the specified <paramref name="exception"/> to the <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry.Errors"/> instance
            that is associated with the specified <paramref name="key"/>. If the maximum number of allowed
            errors has already been recorded, ensures that a <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.TooManyModelErrorsException"/> exception is
            recorded instead.
            </summary>
            <remarks>
            This method allows adding the <paramref name="exception"/> to the current <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary"/>
            when <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/> is not available or the exact <paramref name="exception"/>
            must be maintained for later use (even if it is for example a <see cref="T:System.FormatException"/>).
            Where <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/> is available, use <see cref="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.AddModelError(System.String,System.Exception,Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata)"/> instead.
            </remarks>
            <param name="key">The key of the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry"/> to add errors to.</param>
            <param name="exception">The <see cref="T:System.Exception"/> to add.</param>
            <returns>
            <c>True</c> if the given error was added, <c>false</c> if the error was ignored.
            See <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.MaxAllowedErrors"/>.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.AddModelError(System.String,System.Exception,Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata)">
            <summary>
            Adds the specified <paramref name="exception"/> to the <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry.Errors"/> instance
            that is associated with the specified <paramref name="key"/>. If the maximum number of allowed
            errors has already been recorded, ensures that a <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.TooManyModelErrorsException"/> exception is
            recorded instead.
            </summary>
            <param name="key">The key of the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry"/> to add errors to.</param>
            <param name="exception">The <see cref="T:System.Exception"/> to add. Some exception types will be replaced with
            a descriptive error message.</param>
            <param name="metadata">The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/> associated with the model.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.TryAddModelError(System.String,System.Exception,Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata)">
            <summary>
            Attempts to add the specified <paramref name="exception"/> to the <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry.Errors"/>
            instance that is associated with the specified <paramref name="key"/>. If the maximum number of allowed
            errors has already been recorded, ensures that a <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.TooManyModelErrorsException"/> exception is
            recorded instead.
            </summary>
            <param name="key">The key of the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry"/> to add errors to.</param>
            <param name="exception">The <see cref="T:System.Exception"/> to add. Some exception types will be replaced with
            a descriptive error message.</param>
            <param name="metadata">The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/> associated with the model.</param>
            <returns>
            <c>True</c> if the given error was added, <c>false</c> if the error was ignored.
            See <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.MaxAllowedErrors"/>.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.AddModelError(System.String,System.String)">
            <summary>
            Adds the specified <paramref name="errorMessage"/> to the <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry.Errors"/> instance
            that is associated with the specified <paramref name="key"/>. If the maximum number of allowed
            errors has already been recorded, ensures that a <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.TooManyModelErrorsException"/> exception is
            recorded instead.
            </summary>
            <param name="key">The key of the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry"/> to add errors to.</param>
            <param name="errorMessage">The error message to add.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.TryAddModelError(System.String,System.String)">
            <summary>
            Attempts to add the specified <paramref name="errorMessage"/> to the <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry.Errors"/>
            instance that is associated with the specified <paramref name="key"/>. If the maximum number of allowed
            errors has already been recorded, ensures that a <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.TooManyModelErrorsException"/> exception is
            recorded instead.
            </summary>
            <param name="key">The key of the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry"/> to add errors to.</param>
            <param name="errorMessage">The error message to add.</param>
            <returns>
            <c>True</c> if the given error was added, <c>false</c> if the error was ignored.
            See <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.MaxAllowedErrors"/>.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.GetFieldValidationState(System.String)">
            <summary>
            Returns the aggregate <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelValidationState"/> for items starting with the
            specified <paramref name="key"/>.
            </summary>
            <param name="key">The key to look up model state errors for.</param>
            <returns>Returns <see cref="F:Microsoft.AspNetCore.Mvc.ModelBinding.ModelValidationState.Unvalidated"/> if no entries are found for the specified
            key, <see cref="F:Microsoft.AspNetCore.Mvc.ModelBinding.ModelValidationState.Invalid"/> if at least one instance is found with one or more model
            state errors; <see cref="F:Microsoft.AspNetCore.Mvc.ModelBinding.ModelValidationState.Valid"/> otherwise.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.GetValidationState(System.String)">
            <summary>
            Returns <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelValidationState"/> for the <paramref name="key"/>.
            </summary>
            <param name="key">The key to look up model state errors for.</param>
            <returns>Returns <see cref="F:Microsoft.AspNetCore.Mvc.ModelBinding.ModelValidationState.Unvalidated"/> if no entry is found for the specified
            key, <see cref="F:Microsoft.AspNetCore.Mvc.ModelBinding.ModelValidationState.Invalid"/> if an instance is found with one or more model
            state errors; <see cref="F:Microsoft.AspNetCore.Mvc.ModelBinding.ModelValidationState.Valid"/> otherwise.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.MarkFieldValid(System.String)">
            <summary>
            Marks the <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry.ValidationState"/> for the entry with the specified
            <paramref name="key"/> as <see cref="F:Microsoft.AspNetCore.Mvc.ModelBinding.ModelValidationState.Valid"/>.
            </summary>
            <param name="key">The key of the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry"/> to mark as valid.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.MarkFieldSkipped(System.String)">
            <summary>
            Marks the <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry.ValidationState"/> for the entry with the specified <paramref name="key"/>
            as <see cref="F:Microsoft.AspNetCore.Mvc.ModelBinding.ModelValidationState.Skipped"/>.
            </summary>
            <param name="key">The key of the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry"/> to mark as skipped.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.Merge(Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary)">
            <summary>
            Copies the values from the specified <paramref name="dictionary"/> into this instance, overwriting
            existing values if keys are the same.
            </summary>
            <param name="dictionary">The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary"/> to copy values from.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.SetModelValue(System.String,System.Object,System.String)">
            <summary>
            Sets the of <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry.RawValue"/> and <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry.AttemptedValue"/> for
            the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry"/> with the specified <paramref name="key"/>.
            </summary>
            <param name="key">The key for the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry"/> entry.</param>
            <param name="rawValue">The raw value for the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry"/> entry.</param>
            <param name="attemptedValue">
            The values of <paramref name="rawValue"/> in a comma-separated <see cref="T:System.String"/>.
            </param>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.SetModelValue(System.String,Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderResult)">
            <summary>
            Sets the value for the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry"/> with the specified <paramref name="key"/>.
            </summary>
            <param name="key">The key for the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry"/> entry</param>
            <param name="valueProviderResult">
            A <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderResult"/> with data for the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry"/> entry.
            </param>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.ClearValidationState(System.String)">
            <summary>
            Clears <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary"/> entries that match the key that is passed as parameter.
            </summary>
            <param name="key">The key of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary"/> to clear.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.Clear">
            <summary>
            Removes all keys and values from this instance of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.ContainsKey(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.Remove(System.String)">
            <summary>
            Removes the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry"/> with the specified <paramref name="key"/>.
            </summary>
            <param name="key">The key.</param>
            <returns><c>true</c> if the element is successfully removed; otherwise <c>false</c>. This method also
            returns <c>false</c> if key was not found.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.TryGetValue(System.String,Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry@)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through this instance of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary"/>.
            </summary>
            <returns>An <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.Enumerator"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.System#Collections#Generic#IEnumerable{System#Collections#Generic#KeyValuePair{System#String,Microsoft#AspNetCore#Mvc#ModelBinding#ModelStateEntry}}#GetEnumerator">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.System#Collections#IEnumerable#GetEnumerator">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.StartsWithPrefix(System.String,System.String)">
            <summary>
            <para>
            This API supports the MVC's infrastructure and is not intended to be used
            directly from your code. This API may change or be removed in future releases.
            </para>
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.FindKeysWithPrefix(System.String)">
            <summary>
            Gets a <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.PrefixEnumerable"/> that iterates over this instance of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary"/>
            using the specified <paramref name="prefix"/>.
            </summary>
            <param name="prefix">The prefix.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.PrefixEnumerable"/>.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.PrefixEnumerable">
            <summary>
            Enumerates over <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary"/> to provide entries that start with the
            specified prefix.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.PrefixEnumerable.#ctor(Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary,System.String)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.PrefixEnumerable"/>.
            </summary>
            <param name="dictionary">The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary"/>.</param>
            <param name="prefix">The prefix.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.PrefixEnumerable.GetEnumerator">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.Enumerator">
            <summary>
            An <see cref="T:System.Collections.Generic.IEnumerator`1"/> for <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.PrefixEnumerable"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.Enumerator.#ctor(Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary,System.String)">
            <summary>
            Intializes a new instance of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.Enumerator"/>.
            </summary>
            <param name="dictionary">The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary"/>.</param>
            <param name="prefix">The prefix.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.Enumerator.Current">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.Enumerator.Dispose">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.Enumerator.MoveNext">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.Enumerator.Reset">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.KeyEnumerable">
            <summary>
            A <see cref="T:System.Collections.Generic.IEnumerable`1"/> for keys in <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.KeyEnumerable.#ctor(Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.KeyEnumerable"/>.
            </summary>
            <param name="dictionary">The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary"/>.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.KeyEnumerable.GetEnumerator">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.KeyEnumerator">
            <summary>
            An <see cref="T:System.Collections.Generic.IEnumerator`1"/> for keys in <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.KeyEnumerator.#ctor(Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary,System.String)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.KeyEnumerable"/>.
            </summary>
            <param name="dictionary">The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary"/>.</param>
            <param name="prefix">The prefix.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.KeyEnumerator.Current">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.KeyEnumerator.Dispose">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.KeyEnumerator.MoveNext">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.KeyEnumerator.Reset">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.ValueEnumerable">
            <summary>
            An <see cref="T:System.Collections.IEnumerable"/> for <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.ValueEnumerable.#ctor(Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.ValueEnumerable"/>.
            </summary>
            <param name="dictionary">The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary"/>.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.ValueEnumerable.GetEnumerator">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.ValueEnumerator">
            <summary>
            An enumerator for <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.ValueEnumerator.#ctor(Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary,System.String)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.ValueEnumerator"/>.
            </summary>
            <param name="dictionary">The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary"/>.</param>
            <param name="prefix">The prefix to enumerate.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.ValueEnumerator.Current">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.ValueEnumerator.Dispose">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.ValueEnumerator.MoveNext">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.ValueEnumerator.Reset">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry">
            <summary>
            An entry in a <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry.RawValue">
            <summary>
            Gets the raw value from the request associated with this entry.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry.AttemptedValue">
            <summary>
            Gets the set of values contained in <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry.RawValue"/>, joined into a comma-separated string.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry.Errors">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelErrorCollection"/> for this entry.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry.ValidationState">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelValidationState"/> for this entry.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry.IsContainerNode">
            <summary>
            Gets a value that determines if the current instance of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry"/> is a container node.
            Container nodes represent prefix nodes that aren't explicitly added to the
            <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry.GetModelStateForProperty(System.String)">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry"/> for a sub-property with the specified
            <paramref name="propertyName"/>.
            </summary>
            <param name="propertyName">The property name to lookup.</param>
            <returns>
            The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry"/> if a sub-property was found; otherwise <see langword="null"/>.
            </returns>
            <remarks>
            This method returns any existing entry, even those with <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry.IsContainerNode"/> with value
            <see langword="true"/>.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry.Children">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry"/> values for sub-properties.
            </summary>
            <remarks>
            This property returns all existing entries, even those with <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry.IsContainerNode"/> with value
            <see langword="true"/>.
            </remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelValidationState">
            <summary>
            The validation state of a <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry"/> instance.
            <para>
            <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelValidationState"/> of <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.Root"/> is used to determine the validity
            of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary"/>. <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.IsValid"/> is <see langword="true" />, when
            the aggregate validity (<see cref="M:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary.GetFieldValidationState(System.String)"/>)
            of the root node is <see cref="F:Microsoft.AspNetCore.Mvc.ModelBinding.ModelValidationState.Valid"/>.
            </para>
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Mvc.ModelBinding.ModelValidationState.Unvalidated">
            <summary>
            Validation has not been performed on the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry"/>.
            <para>
            For aggregate validity, the validation of a <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry"/> is <see cref="F:Microsoft.AspNetCore.Mvc.ModelBinding.ModelValidationState.Unvalidated"/>
            if either the entry or one of thedescendants is <see cref="F:Microsoft.AspNetCore.Mvc.ModelBinding.ModelValidationState.Unvalidated"/>.
            </para>
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Mvc.ModelBinding.ModelValidationState.Invalid">
            <summary>
            Validation was performed on the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry"/> and was found to be invalid.
            <para>
            For aggregate validity, the validation of a <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry"/> is <see cref="F:Microsoft.AspNetCore.Mvc.ModelBinding.ModelValidationState.Invalid"/>
            if either the entry or one of the descendants is <see cref="F:Microsoft.AspNetCore.Mvc.ModelBinding.ModelValidationState.Invalid"/> and none are <see cref="F:Microsoft.AspNetCore.Mvc.ModelBinding.ModelValidationState.Unvalidated"/>.
            </para>
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Mvc.ModelBinding.ModelValidationState.Valid">
            <summary>
            Validation was performed on the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry"/>
            <para>
            For aggregate validity, the validation of a <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry"/> is <see cref="F:Microsoft.AspNetCore.Mvc.ModelBinding.ModelValidationState.Valid"/>
            if the validity of the entry and all descendants is either <see cref="F:Microsoft.AspNetCore.Mvc.ModelBinding.ModelValidationState.Valid"/> or <see cref="F:Microsoft.AspNetCore.Mvc.ModelBinding.ModelValidationState.Skipped"/>.
            </para>
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Mvc.ModelBinding.ModelValidationState.Skipped">
            <summary>
            Validation was skipped for the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateEntry"/>.
            <para>
            The aggregate validity of an entry is never <see cref="F:Microsoft.AspNetCore.Mvc.ModelBinding.ModelValidationState.Skipped"/>.
            </para>
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.TooManyModelErrorsException">
            <summary>
            The <see cref="T:System.Exception"/> that is thrown when too many model errors are encountered.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.TooManyModelErrorsException.#ctor(System.String)">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.TooManyModelErrorsException"/> with the specified
            exception <paramref name="message"/>.
            </summary>
            <param name="message">The message that describes the error.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ClientModelValidationContext">
            <summary>
            The context for client-side model validation.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ClientModelValidationContext.#ctor(Microsoft.AspNetCore.Mvc.ActionContext,Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata,Microsoft.AspNetCore.Mvc.ModelBinding.IModelMetadataProvider,System.Collections.Generic.IDictionary{System.String,System.String})">
            <summary>
            Create a new instance of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ClientModelValidationContext"/>.
            </summary>
            <param name="actionContext">The <see cref="T:Microsoft.AspNetCore.Mvc.ActionContext"/> for validation.</param>
            <param name="metadata">The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/> for validation.</param>
            <param name="metadataProvider">The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IModelMetadataProvider"/> to be used in validation.</param>
            <param name="attributes">The attributes dictionary for the HTML tag being rendered.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ClientModelValidationContext.Attributes">
            <summary>
            Gets the attributes dictionary for the HTML tag being rendered.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ClientValidatorItem">
            <summary>
            Used to associate validators with <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ClientValidatorItem.ValidatorMetadata"/> instances
            as part of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ClientValidatorProviderContext"/>. An <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.IClientModelValidator"/> should
            inspect <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ClientValidatorProviderContext.Results"/> and set <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ClientValidatorItem.Validator"/> and
            <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ClientValidatorItem.IsReusable"/> as appropriate.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ClientValidatorItem.#ctor">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ClientValidatorItem"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ClientValidatorItem.#ctor(System.Object)">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ClientValidatorItem"/>.
            </summary>
            <param name="validatorMetadata">The <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ClientValidatorItem.ValidatorMetadata"/>.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ClientValidatorItem.ValidatorMetadata">
            <summary>
            Gets the metadata associated with the <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ClientValidatorItem.Validator"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ClientValidatorItem.Validator">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.IClientModelValidator"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ClientValidatorItem.IsReusable">
            <summary>
            Gets or sets a value indicating whether or not <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ClientValidatorItem.Validator"/> can be reused across requests.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ClientValidatorProviderContext">
            <summary>
            A context for <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.IClientModelValidatorProvider"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ClientValidatorProviderContext.#ctor(Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata,System.Collections.Generic.IList{Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ClientValidatorItem})">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ClientValidatorProviderContext"/>.
            </summary>
            <param name="modelMetadata">The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/> for the model being validated.
            </param>
            <param name="items">The list of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ClientValidatorItem"/>s.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ClientValidatorProviderContext.ModelMetadata">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ClientValidatorProviderContext.ValidatorMetadata">
            <summary>
            Gets the validator metadata.
            </summary>
            <remarks>
            This property provides convenience access to <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.ValidatorMetadata"/>.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ClientValidatorProviderContext.Results">
            <summary>
            Gets the list of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ClientValidatorItem"/> instances. <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.IClientModelValidatorProvider"/>
            instances should add the appropriate <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ClientValidatorItem.Validator"/> properties when
            <see cref="M:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.IClientModelValidatorProvider.CreateValidators(Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ClientValidatorProviderContext)"/>
            is called.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.IClientModelValidator">
            <summary>
            Specifies the contract for performing validation in the browser.
            <para>
            MVC's validation system invokes <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.IClientModelValidator"/> to gather attributes that apply to the
            rendered HTML. The rendered view may have to reference JavaScript libraries, such as jQuery Unobtrusive Validation,
            to provide client validation based on the presence of these attributes.
            </para>
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.IClientModelValidator.AddValidation(Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ClientModelValidationContext)">
            <summary>
            Called to add client-side model validation.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ClientModelValidationContext"/>.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.IClientModelValidatorProvider">
            <summary>
            Provides a collection of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.IClientModelValidator"/>s.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.IClientModelValidatorProvider.CreateValidators(Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ClientValidatorProviderContext)">
            <summary>
            Creates set of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.IClientModelValidator"/>s by updating
            <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ClientValidatorItem.Validator"/> in <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ClientValidatorProviderContext.Results"/>.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ClientModelValidationContext"/> associated with this call.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.IModelValidator">
            <summary>
            Validates a model value.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.IModelValidator.Validate(Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ModelValidationContext)">
            <summary>
            Validates the model value.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ModelValidationContext"/>.</param>
            <returns>
            A list of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ModelValidationResult"/> indicating the results of validating the model value.
            </returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.IModelValidatorProvider">
            <summary>
            Provides validators for a model value.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.IModelValidatorProvider.CreateValidators(Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ModelValidatorProviderContext)">
            <summary>
            Creates the validators for <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ModelValidatorProviderContext.ModelMetadata"/>.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ModelValidatorProviderContext"/>.</param>
            <remarks>
            Implementations should add the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.IModelValidator"/> instances to the appropriate
            <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidatorItem"/> instance which should be added to
            <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ModelValidatorProviderContext.Results"/>.
            </remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.IPropertyValidationFilter">
            <summary>
            Contract for attributes that determine whether associated properties should be validated. When the attribute is
            applied to a property, the validation system calls <see cref="M:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.IPropertyValidationFilter.ShouldValidateEntry(Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationEntry,Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationEntry)"/> to determine whether to
            validate that property. When applied to a type, the validation system calls <see cref="M:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.IPropertyValidationFilter.ShouldValidateEntry(Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationEntry,Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationEntry)"/>
            for each property that type defines to determine whether to validate it.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.IPropertyValidationFilter.ShouldValidateEntry(Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationEntry,Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationEntry)">
            <summary>
            Gets an indication whether the <paramref name="entry"/> should be validated.
            </summary>
            <param name="entry"><see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationEntry"/> to check.</param>
            <param name="parentEntry"><see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationEntry"/> containing <paramref name="entry"/>.</param>
            <returns><c>true</c> if <paramref name="entry"/> should be validated; <c>false</c> otherwise.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.IValidationStrategy">
            <summary>
            Defines a strategy for enumerating the child entries of a model object which should be validated.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.IValidationStrategy.GetChildren(Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata,System.String,System.Object)">
            <summary>
            Gets an <see cref="T:System.Collections.Generic.IEnumerator`1"/> containing a <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationEntry"/> for
            each child entry of the model object to be validated.
            </summary>
            <param name="metadata">The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/> associated with <paramref name="model"/>.</param>
            <param name="key">The model prefix associated with <paramref name="model"/>.</param>
            <param name="model">The model object.</param>
            <returns>An <see cref="T:System.Collections.Generic.IEnumerator`1"/>.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ModelValidationContext">
            <summary>
            A context object for <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.IModelValidator"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ModelValidationContext.#ctor(Microsoft.AspNetCore.Mvc.ActionContext,Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata,Microsoft.AspNetCore.Mvc.ModelBinding.IModelMetadataProvider,System.Object,System.Object)">
            <summary>
            Create a new instance of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ModelValidationContext"/>.
            </summary>
            <param name="actionContext">The <see cref="T:Microsoft.AspNetCore.Mvc.ActionContext"/> for validation.</param>
            <param name="modelMetadata">The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/> for validation.</param>
            <param name="metadataProvider">The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IModelMetadataProvider"/> to be used in validation.</param>
            <param name="container">The model container.</param>
            <param name="model">The model to be validated.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ModelValidationContext.Model">
            <summary>
            Gets the model object.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ModelValidationContext.Container">
            <summary>
            Gets the model container object.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ModelValidationContextBase">
            <summary>
            A common base class for <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ModelValidationContext"/> and <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ClientModelValidationContext"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ModelValidationContextBase.#ctor(Microsoft.AspNetCore.Mvc.ActionContext,Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata,Microsoft.AspNetCore.Mvc.ModelBinding.IModelMetadataProvider)">
            <summary>
            Instantiates a new <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ModelValidationContextBase"/>.
            </summary>
            <param name="actionContext">The <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ModelValidationContextBase.ActionContext"/> for this context.</param>
            <param name="modelMetadata">The <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ModelValidationContextBase.ModelMetadata"/> for this model.</param>
            <param name="metadataProvider">The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IModelMetadataProvider"/> to be used by this context.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ModelValidationContextBase.ActionContext">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Mvc.ActionContext"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ModelValidationContextBase.ModelMetadata">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ModelValidationContextBase.MetadataProvider">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IModelMetadataProvider"/>.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ModelValidationResult">
            <summary>
            The result of model validation.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ModelValidationResult.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ModelValidationResult"/>.
            </summary>
            <param name="memberName">The name of the entry on which validation was performed.</param>
            <param name="message">The validation message.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ModelValidationResult.MemberName">
            <summary>
            Gets the name of the entry on which validation was performed.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ModelValidationResult.Message">
            <summary>
            Gets the validation message.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ModelValidatorProviderContext">
            <summary>
            A context for <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.IModelValidatorProvider"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ModelValidatorProviderContext.#ctor(Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata,System.Collections.Generic.IList{Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidatorItem})">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ModelValidatorProviderContext"/>.
            </summary>
            <param name="modelMetadata">The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/>.</param>
            <param name="items">The list of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidatorItem"/>s.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ModelValidatorProviderContext.ModelMetadata">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ModelValidatorProviderContext.ValidatorMetadata">
            <summary>
            Gets the validator metadata.
            </summary>
            <remarks>
            This property provides convenience access to <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata.ValidatorMetadata"/>.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ModelValidatorProviderContext.Results">
            <summary>
            Gets the list of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidatorItem"/> instances. <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.IModelValidatorProvider"/> instances
            should add the appropriate <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidatorItem.Validator"/> properties when
            <see cref="M:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.IModelValidatorProvider.CreateValidators(Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ModelValidatorProviderContext)"/>
            is called.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationEntry">
            <summary>
            Contains data needed for validating a child entry of a model object. See <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.IValidationStrategy"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationEntry.#ctor(Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata,System.String,System.Object)">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationEntry"/>.
            </summary>
            <param name="metadata">The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/> associated with <paramref name="model"/>.</param>
            <param name="key">The model prefix associated with <paramref name="model"/>.</param>
            <param name="model">The model object.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationEntry.#ctor(Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata,System.String,System.Func{System.Object})">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationEntry"/>.
            </summary>
            <param name="metadata">The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/> associated with the <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationEntry.Model"/>.</param>
            <param name="key">The model prefix associated with the <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationEntry.Model"/>.</param>
            <param name="modelAccessor">A delegate that will return the <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationEntry.Model"/>.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationEntry.Key">
            <summary>
            The model prefix associated with <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationEntry.Model"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationEntry.Metadata">
            <summary>
            The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/> associated with <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationEntry.Model"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationEntry.Model">
            <summary>
            The model object.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationStateDictionary">
            <summary>
            Used for tracking validation state to customize validation behavior for a model object.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationStateDictionary.#ctor">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationStateDictionary"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationStateDictionary.Item(System.Object)">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationStateDictionary.System#Collections#Generic#IDictionary{System#Object,Microsoft#AspNetCore#Mvc#ModelBinding#Validation#ValidationStateEntry}#Item(System.Object)">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationStateDictionary.Count">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationStateDictionary.IsReadOnly">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationStateDictionary.Keys">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationStateDictionary.Values">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationStateDictionary.System#Collections#Generic#IReadOnlyDictionary{System#Object,Microsoft#AspNetCore#Mvc#ModelBinding#Validation#ValidationStateEntry}#Keys">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationStateDictionary.System#Collections#Generic#IReadOnlyDictionary{System#Object,Microsoft#AspNetCore#Mvc#ModelBinding#Validation#ValidationStateEntry}#Values">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationStateDictionary.Add(System.Collections.Generic.KeyValuePair{System.Object,Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationStateEntry})">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationStateDictionary.Add(System.Object,Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationStateEntry)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationStateDictionary.Clear">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationStateDictionary.Contains(System.Collections.Generic.KeyValuePair{System.Object,Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationStateEntry})">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationStateDictionary.ContainsKey(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationStateDictionary.CopyTo(System.Collections.Generic.KeyValuePair{System.Object,Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationStateEntry}[],System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationStateDictionary.GetEnumerator">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationStateDictionary.Remove(System.Collections.Generic.KeyValuePair{System.Object,Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationStateEntry})">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationStateDictionary.Remove(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationStateDictionary.TryGetValue(System.Object,Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationStateEntry@)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationStateDictionary.System#Collections#IEnumerable#GetEnumerator">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationStateEntry">
            <summary>
            An entry in a <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationStateDictionary"/>. Records state information to override the default
            behavior of validation for an object.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationStateEntry.Key">
            <summary>
            Gets or sets the model prefix associated with the entry.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationStateEntry.Metadata">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ModelMetadata"/> associated with the entry.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationStateEntry.SuppressValidation">
            <summary>
            Gets or sets a value indicating whether the associated model object should be validated.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationStateEntry.Strategy">
            <summary>
            Gets or sets an <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.IValidationStrategy"/> for enumerating child entries of the associated
            model object.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidatorItem">
            <summary>
            Used to associate validators with <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidatorItem.ValidatorMetadata"/> instances
            as part of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ModelValidatorProviderContext"/>. An <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.IModelValidator"/> should
            inspect <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ModelValidatorProviderContext.Results"/> and set <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidatorItem.Validator"/> and
            <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidatorItem.IsReusable"/> as appropriate.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidatorItem.#ctor">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidatorItem"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidatorItem.#ctor(System.Object)">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidatorItem"/>.
            </summary>
            <param name="validatorMetadata">The <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidatorItem.ValidatorMetadata"/>.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidatorItem.ValidatorMetadata">
            <summary>
            Gets the metadata associated with the <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidatorItem.Validator"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidatorItem.Validator">
            <summary>
            Gets or sets the <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.IModelValidator"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidatorItem.IsReusable">
            <summary>
            Gets or sets a value indicating whether or not <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidatorItem.Validator"/> can be reused across requests.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderException">
            <summary>
            Exception thrown by <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IValueProviderFactory"/> when the input is unable to be read.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderException.#ctor(System.String)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderException"/> with the specified <paramref name="message"/>.
            </summary>
            <param name="message">The exception message.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderException"/> with the specified <paramref name="message"/> and
            inner exception that is the cause of this exception.
            </summary>
            <param name="message">The exception message.</param>
            <param name="innerException">The exception that is the cause of the current exception.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderFactoryContext">
            <summary>
            A context for <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IValueProviderFactory"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderFactoryContext.#ctor(Microsoft.AspNetCore.Mvc.ActionContext)">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderFactoryContext"/>.
            </summary>
            <param name="context">The <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderFactoryContext.ActionContext"/>.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderFactoryContext.ActionContext">
            <summary>
            Gets the <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderFactoryContext.ActionContext"/> associated with this context.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderFactoryContext.ValueProviders">
            <summary>
            Gets the list of <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IValueProvider"/> instances.
            <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IValueProviderFactory"/> instances should add the appropriate
            <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.IValueProvider"/> instances to this list.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderResult">
            <summary>
            Result of an <see cref="M:Microsoft.AspNetCore.Mvc.ModelBinding.IValueProvider.GetValue(System.String)"/> operation.
            </summary>
            <remarks>
            <para>
            <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderResult"/> can represent a single submitted value or multiple submitted values.
            </para>
            <para>
            Use <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderResult.FirstValue"/> to consume only a single value, regardless of whether a single value or
            multiple values were submitted.
            </para>
            <para>
            Treat <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderResult"/> as an <see cref="T:System.Collections.Generic.IEnumerable`1"/> to consume all values,
            regardless of whether a single value or multiple values were submitted.
            </para>
            </remarks>
        </member>
        <member name="F:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderResult.None">
            <summary>
            A <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderResult"/> that represents a lack of data.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderResult.#ctor(Microsoft.Extensions.Primitives.StringValues)">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderResult"/> using <see cref="P:System.Globalization.CultureInfo.InvariantCulture"/>.
            </summary>
            <param name="values">The submitted values.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderResult.#ctor(Microsoft.Extensions.Primitives.StringValues,System.Globalization.CultureInfo)">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderResult"/>.
            </summary>
            <param name="values">The submitted values.</param>
            <param name="culture">The <see cref="T:System.Globalization.CultureInfo"/> associated with this value.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderResult.Culture">
            <summary>
            Gets or sets the <see cref="T:System.Globalization.CultureInfo"/> associated with the values.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderResult.Values">
            <summary>
            Gets or sets the values.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderResult.FirstValue">
            <summary>
            Gets the first value based on the order values were provided in the request. Use <see cref="P:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderResult.FirstValue"/>
            to get a single value for processing regardless of whether a single or multiple values were provided
            in the request.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderResult.Length">
            <summary>
            Gets the number of submitted values.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderResult.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderResult.Equals(Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderResult)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderResult.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderResult.ToString">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderResult.GetEnumerator">
            <summary>
            Gets an <see cref="T:System.Collections.Generic.IEnumerator`1"/> for this <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderResult"/>.
            </summary>
            <returns>An <see cref="T:System.Collections.Generic.IEnumerator`1"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderResult.System#Collections#IEnumerable#GetEnumerator">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderResult.op_Explicit(Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderResult)~System.String">
            <summary>
            Converts the provided <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderResult"/> into a comma-separated string containing all
            submitted values.
            </summary>
            <param name="result">The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderResult"/>.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderResult.op_Explicit(Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderResult)~System.String[]">
            <summary>
            Converts the provided <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderResult"/> into a an array of <see cref="T:System.String"/> containing
            all submitted values.
            </summary>
            <param name="result">The <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderResult"/>.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderResult.op_Equality(Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderResult,Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderResult)">
            <summary>
            Compares two <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderResult"/> objects for equality.
            </summary>
            <param name="x">A <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderResult"/>.</param>
            <param name="y">A <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderResult"/>.</param>
            <returns><c>true</c> if the values are equal, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderResult.op_Inequality(Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderResult,Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderResult)">
            <summary>
            Compares two <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderResult"/> objects for inequality.
            </summary>
            <param name="x">A <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderResult"/>.</param>
            <param name="y">A <see cref="T:Microsoft.AspNetCore.Mvc.ModelBinding.ValueProviderResult"/>.</param>
            <returns><c>false</c> if the values are equal, otherwise <c>true</c>.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Routing.AttributeRouteInfo">
            <summary>
            Represents the routing information for an action that is attribute routed.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Routing.AttributeRouteInfo.Template">
            <summary>
            The route template. May be <see langword="null" /> if the action has no attribute routes.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Routing.AttributeRouteInfo.Order">
            <summary>
            Gets the order of the route associated with a given action. This property determines
            the order in which routes get executed. Routes with a lower order value are tried first. In case a route
            doesn't specify a value, it gets a default order of 0.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Routing.AttributeRouteInfo.Name">
            <summary>
            Gets the name of the route associated with a given action. This property can be used
            to generate a link by referring to the route by name instead of attempting to match a
            route by provided route data.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Routing.AttributeRouteInfo.SuppressLinkGeneration">
            <summary>
            Gets or sets a value that determines if the route entry associated with this model participates in link generation.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Routing.AttributeRouteInfo.SuppressPathMatching">
            <summary>
            Gets or sets a value that determines if the route entry associated with this model participates in path matching (inbound routing).
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Routing.UrlActionContext">
            <summary>
            Context object to be used for the URLs that <see cref="M:Microsoft.AspNetCore.Mvc.IUrlHelper.Action(Microsoft.AspNetCore.Mvc.Routing.UrlActionContext)"/> generates.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Routing.UrlActionContext.Action">
            <summary>
            The name of the action method that <see cref="M:Microsoft.AspNetCore.Mvc.IUrlHelper.Action(Microsoft.AspNetCore.Mvc.Routing.UrlActionContext)"/> uses to generate URLs.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Routing.UrlActionContext.Controller">
            <summary>
            The name of the controller that <see cref="M:Microsoft.AspNetCore.Mvc.IUrlHelper.Action(Microsoft.AspNetCore.Mvc.Routing.UrlActionContext)"/> uses to generate URLs.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Routing.UrlActionContext.Values">
            <summary>
            The object that contains the route values that <see cref="M:Microsoft.AspNetCore.Mvc.IUrlHelper.Action(Microsoft.AspNetCore.Mvc.Routing.UrlActionContext)"/>
            uses to generate URLs.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Routing.UrlActionContext.Protocol">
            <summary>
            The protocol for the URLs that <see cref="M:Microsoft.AspNetCore.Mvc.IUrlHelper.Action(Microsoft.AspNetCore.Mvc.Routing.UrlActionContext)"/> generates,
            such as "http" or "https"
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Routing.UrlActionContext.Host">
            <summary>
            The host name for the URLs that <see cref="M:Microsoft.AspNetCore.Mvc.IUrlHelper.Action(Microsoft.AspNetCore.Mvc.Routing.UrlActionContext)"/> generates.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Routing.UrlActionContext.Fragment">
            <summary>
            The fragment for the URLs that <see cref="M:Microsoft.AspNetCore.Mvc.IUrlHelper.Action(Microsoft.AspNetCore.Mvc.Routing.UrlActionContext)"/> generates.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Mvc.Routing.UrlRouteContext">
            <summary>
            Context object to be used for the URLs that <see cref="M:Microsoft.AspNetCore.Mvc.IUrlHelper.RouteUrl(Microsoft.AspNetCore.Mvc.Routing.UrlRouteContext)"/> generates.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Routing.UrlRouteContext.RouteName">
            <summary>
            The name of the route that <see cref="M:Microsoft.AspNetCore.Mvc.IUrlHelper.RouteUrl(Microsoft.AspNetCore.Mvc.Routing.UrlRouteContext)"/> uses to generate URLs.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Routing.UrlRouteContext.Values">
            <summary>
            The object that contains the route values that <see cref="M:Microsoft.AspNetCore.Mvc.IUrlHelper.RouteUrl(Microsoft.AspNetCore.Mvc.Routing.UrlRouteContext)"/>
            uses to generate URLs.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Routing.UrlRouteContext.Protocol">
            <summary>
            The protocol for the URLs that <see cref="M:Microsoft.AspNetCore.Mvc.IUrlHelper.RouteUrl(Microsoft.AspNetCore.Mvc.Routing.UrlRouteContext)"/> generates,
            such as "http" or "https"
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Routing.UrlRouteContext.Host">
            <summary>
            The host name for the URLs that <see cref="M:Microsoft.AspNetCore.Mvc.IUrlHelper.RouteUrl(Microsoft.AspNetCore.Mvc.Routing.UrlRouteContext)"/> generates.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Mvc.Routing.UrlRouteContext.Fragment">
            <summary>
            The fragment for the URLs that <see cref="M:Microsoft.AspNetCore.Mvc.IUrlHelper.RouteUrl(Microsoft.AspNetCore.Mvc.Routing.UrlRouteContext)"/> generates.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Internal.ClosedGenericMatcher">
            <summary>
            Helper related to generic interface definitions and implementing classes.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Internal.ClosedGenericMatcher.ExtractGenericInterface(System.Type,System.Type)">
            <summary>
            Determine whether <paramref name="queryType"/> is or implements a closed generic <see cref="T:System.Type"/>
            created from <paramref name="interfaceType"/>.
            </summary>
            <param name="queryType">The <see cref="T:System.Type"/> of interest.</param>
            <param name="interfaceType">The open generic <see cref="T:System.Type"/> to match. Usually an interface.</param>
            <returns>
            The closed generic <see cref="T:System.Type"/> created from <paramref name="interfaceType"/> that
            <paramref name="queryType"/> is or implements. <c>null</c> if the two <see cref="T:System.Type"/>s have no such
            relationship.
            </returns>
            <remarks>
            This method will return <paramref name="queryType"/> if <paramref name="interfaceType"/> is
            <c>typeof(KeyValuePair{,})</c>, and <paramref name="queryType"/> is
            <c>typeof(KeyValuePair{string, object})</c>.
            </remarks>
        </member>
    </members>
</doc>
