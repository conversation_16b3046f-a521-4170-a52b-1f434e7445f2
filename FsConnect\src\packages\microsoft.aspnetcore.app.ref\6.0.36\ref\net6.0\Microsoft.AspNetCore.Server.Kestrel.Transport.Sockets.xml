<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets</name>
    </assembly>
    <members>
        <member name="T:Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionContextFactory">
            <summary>
            A factory for socket based connections contexts.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionContextFactory.#ctor(Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionFactoryOptions,Microsoft.Extensions.Logging.ILogger)">
            <summary>
            Creates the <see cref="T:Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionContextFactory"/>.
            </summary>
            <param name="options">The options.</param>
            <param name="logger">The logger.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionContextFactory.Create(System.Net.Sockets.Socket)">
            <summary>
            Create a <see cref="T:Microsoft.AspNetCore.Connections.ConnectionContext"/> for a socket.
            </summary>
            <param name="socket">The socket for the connection.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionContextFactory.Dispose">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionFactoryOptions">
            <summary>
            Options for <see cref="T:Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionContextFactory"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionFactoryOptions.#ctor">
            <summary>
            Create a new instance.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionFactoryOptions.IOQueueCount">
            <summary>
            The number of I/O queues used to process requests. Set to 0 to directly schedule I/O to the ThreadPool.
            </summary>
            <remarks>
            Defaults to <see cref="P:System.Environment.ProcessorCount" /> rounded down and clamped between 1 and 16.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionFactoryOptions.WaitForDataBeforeAllocatingBuffer">
            <summary>
            Wait until there is data available to allocate a buffer. Setting this to false can increase throughput at the cost of increased memory usage.
            </summary>
            <remarks>
            Defaults to true.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionFactoryOptions.MaxReadBufferSize">
            <summary>
            Gets or sets the maximum unconsumed incoming bytes the transport will buffer.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionFactoryOptions.MaxWriteBufferSize">
            <summary>
            Gets or sets the maximum outgoing bytes the transport will buffer before applying write backpressure.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionFactoryOptions.UnsafePreferInlineScheduling">
            <summary>
            Inline application and transport continuations instead of dispatching to the threadpool.
            </summary>
            <remarks>
            This will run application code on the IO thread which is why this is unsafe.
            It is recommended to set the DOTNET_SYSTEM_NET_SOCKETS_INLINE_COMPLETIONS environment variable to '1' when using this setting to also inline the completions
            at the runtime layer as well.
            This setting can make performance worse if there is expensive work that will end up holding onto the IO thread for longer than needed.
            Test to make sure this setting helps performance.
            </remarks>
        </member>
        <member name="T:Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory">
            <summary>
            A factory for socket based connections.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions">
            <summary>
            Options for socket based transports.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.IOQueueCount">
            <summary>
            The number of I/O queues used to process requests. Set to 0 to directly schedule I/O to the ThreadPool.
            </summary>
            <remarks>
            Defaults to <see cref="P:System.Environment.ProcessorCount" /> rounded down and clamped between 1 and 16.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.WaitForDataBeforeAllocatingBuffer">
            <summary>
            Wait until there is data available to allocate a buffer. Setting this to false can increase throughput at the cost of increased memory usage.
            </summary>
            <remarks>
            Defaults to true.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.NoDelay">
            <summary>
            Set to false to enable Nagle's algorithm for all connections.
            </summary>
            <remarks>
            Defaults to true.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.Backlog">
            <summary>
            The maximum length of the pending connection queue.
            </summary>
            <remarks>
            Defaults to 512.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.MaxReadBufferSize">
            <summary>
            Gets or sets the maximum unconsumed incoming bytes the transport will buffer.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.MaxWriteBufferSize">
            <summary>
            Gets or sets the maximum outgoing bytes the transport will buffer before applying write backpressure.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.UnsafePreferInlineScheduling">
            <summary>
            Inline application and transport continuations instead of dispatching to the threadpool.
            </summary>
            <remarks>
            This will run application code on the IO thread which is why this is unsafe.
            It is recommended to set the DOTNET_SYSTEM_NET_SOCKETS_INLINE_COMPLETIONS environment variable to '1' when using this setting to also inline the completions
            at the runtime layer as well.
            This setting can make performance worse if there is expensive work that will end up holding onto the IO thread for longer than needed.
            Test to make sure this setting helps performance.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateBoundListenSocket">
            <summary>
            A function used to create a new <see cref="T:System.Net.Sockets.Socket"/> to listen with. If
            not set, <see cref="M:Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(System.Net.EndPoint)" /> is used.
            </summary>
            <remarks>
            Implementors are expected to call <see cref="M:System.Net.Sockets.Socket.Bind(System.Net.EndPoint)"/> on the
            <see cref="T:System.Net.Sockets.Socket"/>. Please note that <see cref="M:Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(System.Net.EndPoint)"/>
            calls <see cref="M:System.Net.Sockets.Socket.Bind(System.Net.EndPoint)"/> as part of its implementation, so implementors
            using this method do not need to call it again.
            </remarks>
        </member>
        <member name="M:Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(System.Net.EndPoint)">
            <summary>
            Creates a default instance of <see cref="T:System.Net.Sockets.Socket"/> for the given <see cref="T:System.Net.EndPoint"/>
            that can be used by a connection listener to listen for inbound requests. <see cref="M:System.Net.Sockets.Socket.Bind(System.Net.EndPoint)"/>
            is called by this method.
            </summary>
            <param name="endpoint">
            An <see cref="T:System.Net.EndPoint"/>.
            </param>
            <returns>
            A <see cref="T:System.Net.Sockets.Socket"/> instance.
            </returns>
        </member>
        <member name="P:Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketsStrings.FileHandleEndPointNotSupported">
            <summary>The Socket transport does not support binding to file handles. Consider using the libuv transport instead.</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketsStrings.OnlyIPEndPointsSupported">
            <summary>Only ListenType.IPEndPoint is supported by the Socket Transport. https://go.microsoft.com/fwlink/?linkid=874850</summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketsStrings.TransportAlreadyBound">
            <summary>Transport is already bound.</summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Hosting.WebHostBuilderSocketExtensions">
            <summary>
            <see cref="T:Microsoft.AspNetCore.Hosting.IWebHostBuilder" /> extension methods to configure the Socket transport to be used by Kestrel.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Hosting.WebHostBuilderSocketExtensions.UseSockets(Microsoft.AspNetCore.Hosting.IWebHostBuilder)">
            <summary>
            Specify Sockets as the transport to be used by Kestrel.
            </summary>
            <param name="hostBuilder">
            The Microsoft.AspNetCore.Hosting.IWebHostBuilder to configure.
            </param>
            <returns>
            The Microsoft.AspNetCore.Hosting.IWebHostBuilder.
            </returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Hosting.WebHostBuilderSocketExtensions.UseSockets(Microsoft.AspNetCore.Hosting.IWebHostBuilder,System.Action{Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions})">
            <summary>
            Specify Sockets as the transport to be used by Kestrel.
            </summary>
            <param name="hostBuilder">
            The Microsoft.AspNetCore.Hosting.IWebHostBuilder to configure.
            </param>
            <param name="configureOptions">
            A callback to configure Libuv options.
            </param>
            <returns>
            The Microsoft.AspNetCore.Hosting.IWebHostBuilder.
            </returns>
        </member>
        <member name="T:System.Buffers.DiagnosticMemoryPool">
            <summary>
            Used to allocate and distribute re-usable blocks of memory.
            </summary>
        </member>
        <member name="F:System.Buffers.DiagnosticMemoryPool.AnySize">
            <summary>
            This default value passed in to Rent to use the default value for the pool.
            </summary>
        </member>
        <member name="T:System.Buffers.DiagnosticPoolBlock">
            <summary>
            Block tracking object used by the byte buffer memory pool. A slab is a large allocation which is divided into smaller blocks. The
            individual blocks are then treated as independent array segments.
            </summary>
        </member>
        <member name="F:System.Buffers.DiagnosticPoolBlock._pool">
            <summary>
            Back-reference to the memory pool which this block was allocated from. It may only be returned to this pool.
            </summary>
        </member>
        <member name="M:System.Buffers.DiagnosticPoolBlock.#ctor(System.Buffers.DiagnosticMemoryPool,System.Buffers.IMemoryOwner{System.Byte})">
            <summary>
            This object cannot be instantiated outside of the static Create method
            </summary>
        </member>
        <member name="T:System.Buffers.MemoryPoolBlock">
            <summary>
            Wraps an array allocated in the pinned object heap in a reusable block of managed memory
            </summary>
        </member>
        <member name="P:System.Buffers.MemoryPoolBlock.Pool">
            <summary>
            Back-reference to the memory pool which this block was allocated from. It may only be returned to this pool.
            </summary>
        </member>
        <member name="T:System.Buffers.PinnedBlockMemoryPool">
            <summary>
            Used to allocate and distribute re-usable blocks of memory.
            </summary>
        </member>
        <member name="F:System.Buffers.PinnedBlockMemoryPool._blockSize">
            <summary>
            The size of a block. 4096 is chosen because most operating systems use 4k pages.
            </summary>
        </member>
        <member name="P:System.Buffers.PinnedBlockMemoryPool.MaxBufferSize">
            <summary>
            Max allocation block size for pooled blocks,
            larger values can be leased but they will be disposed after use rather than returned to the pool.
            </summary>
        </member>
        <member name="P:System.Buffers.PinnedBlockMemoryPool.BlockSize">
            <summary>
            The size of a block. 4096 is chosen because most operating systems use 4k pages.
            </summary>
        </member>
        <member name="F:System.Buffers.PinnedBlockMemoryPool._blocks">
            <summary>
            Thread-safe collection of blocks which are currently in the pool. A slab will pre-allocate all of the block tracking objects
            and add them to this collection. When memory is requested it is taken from here first, and when it is returned it is re-added.
            </summary>
        </member>
        <member name="F:System.Buffers.PinnedBlockMemoryPool._isDisposed">
            <summary>
            This is part of implementing the IDisposable pattern.
            </summary>
        </member>
        <member name="F:System.Buffers.PinnedBlockMemoryPool.AnySize">
            <summary>
            This default value passed in to Rent to use the default value for the pool.
            </summary>
        </member>
        <member name="M:System.Buffers.PinnedBlockMemoryPool.Return(System.Buffers.MemoryPoolBlock)">
            <summary>
            Called to return a block to the pool. Once Return has been called the memory no longer belongs to the caller, and
            Very Bad Things will happen if the memory is read of modified subsequently. If a caller fails to call Return and the
            block tracking object is garbage collected, the block tracking object's finalizer will automatically re-create and return
            a new tracking object into the pool. This will only happen if there is a bug in the server, however it is necessary to avoid
            leaving "dead zones" in the slab due to lost block tracking objects.
            </summary>
            <param name="block">The block to return. It must have been acquired by calling Lease on the same memory pool instance.</param>
        </member>
    </members>
</doc>
