﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>ReachFramework</name>
  </assembly>
  <members>
    <member name="T:System.Printing.Collation">
      <summary>Specifies whether a printer collates output when it prints multiple copies of a multi-page print job.</summary>
    </member>
    <member name="F:System.Printing.Collation.Collated">
      <summary>Collated output.</summary>
    </member>
    <member name="F:System.Printing.Collation.Uncollated">
      <summary>Uncollated output.</summary>
    </member>
    <member name="F:System.Printing.Collation.Unknown">
      <summary>The feature (whose options are represented by this enumeration) is set to an option not defined in the Print Schema.</summary>
    </member>
    <member name="T:System.Printing.ConflictStatus">
      <summary>Specifies whether any changes were made to a merged <see cref="T:System.Printing.PrintTicket" /> to ensure a viable <see cref="T:System.Printing.PrintTicket" />.</summary>
    </member>
    <member name="F:System.Printing.ConflictStatus.ConflictResolved">
      <summary>One or more conflicts were found and all conflicts were resolved.</summary>
    </member>
    <member name="F:System.Printing.ConflictStatus.NoConflict">
      <summary>No conflicts were found between the initial merged print ticket and the supported printer functions.</summary>
    </member>
    <member name="T:System.Printing.DeviceFontSubstitution">
      <summary>Specifies whether device font substitution is enabled on a printer.</summary>
    </member>
    <member name="F:System.Printing.DeviceFontSubstitution.Off">
      <summary>Device font substitution is disabled.</summary>
    </member>
    <member name="F:System.Printing.DeviceFontSubstitution.On">
      <summary>Device font substitution is enabled.</summary>
    </member>
    <member name="F:System.Printing.DeviceFontSubstitution.Unknown">
      <summary>The feature (whose options are represented by this enumeration) is set to an option not defined in the Print Schema.</summary>
    </member>
    <member name="T:System.Printing.Duplexing">
      <summary>Specifies whether a printer uses one-sided printing or some type of two-sided (duplex) printing.</summary>
    </member>
    <member name="F:System.Printing.Duplexing.OneSided">
      <summary>Output prints on only one side of each sheet.</summary>
    </member>
    <member name="F:System.Printing.Duplexing.TwoSidedLongEdge">
      <summary>Output prints on both sides of each sheet, which flips along the edge parallel to the <see cref="P:System.Printing.PrintDocumentImageableArea.MediaSizeHeight" />.</summary>
    </member>
    <member name="F:System.Printing.Duplexing.TwoSidedShortEdge">
      <summary>Output prints on both sides of each sheet, which flips along the edge parallel to <see cref="P:System.Printing.PrintDocumentImageableArea.MediaSizeWidth" />.</summary>
    </member>
    <member name="F:System.Printing.Duplexing.Unknown">
      <summary>The feature (whose options are represented by this enumeration) is set to an option not defined in the Print Schema.</summary>
    </member>
    <member name="T:System.Printing.InputBin">
      <summary>Specifies the input bin that is used as the source of blank paper or other print media.</summary>
    </member>
    <member name="F:System.Printing.InputBin.AutoSelect">
      <summary>The automatic selection of an input bin according to the page size and media output type.</summary>
    </member>
    <member name="F:System.Printing.InputBin.AutoSheetFeeder">
      <summary>The automatic sheet feeder is used.</summary>
    </member>
    <member name="F:System.Printing.InputBin.Cassette">
      <summary>A removable paper bin is used.</summary>
    </member>
    <member name="F:System.Printing.InputBin.Manual">
      <summary>The manual input bin is used.</summary>
    </member>
    <member name="F:System.Printing.InputBin.Tractor">
      <summary>A tractor feed (also called a pin feed) of continuous-feed paper is used.</summary>
    </member>
    <member name="F:System.Printing.InputBin.Unknown">
      <summary>The feature (whose options are represented by this enumeration) is set to an option not defined in the Print Schema.</summary>
    </member>
    <member name="T:System.Printing.Interop.BaseDevModeType">
      <summary>Specifies the type of default DEVMODE structure to use as the base DEVMODE for conversions of managed <see cref="T:System.Printing.PrintTicket" /> objects to unmanaged DEVMODE structures.</summary>
    </member>
    <member name="F:System.Printing.Interop.BaseDevModeType.PrinterDefault">
      <summary>The printer's default DEVMODE structure.</summary>
    </member>
    <member name="F:System.Printing.Interop.BaseDevModeType.UserDefault">
      <summary>The user's default DEVMODE structure.</summary>
    </member>
    <member name="T:System.Printing.Interop.PrintTicketConverter">
      <summary>Converts managed <see cref="T:System.Printing.PrintTicket" /> objects to unmanaged Graphics Device Interface (GDI) DEVMODE structures, and vice versa.</summary>
    </member>
    <member name="M:System.Printing.Interop.PrintTicketConverter.#ctor(System.String,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.Interop.PrintTicketConverter" /> class for the specified printer.</summary>
      <param name="deviceName">The name of the printer that is bound to the new <see cref="T:System.Printing.Interop.PrintTicketConverter" /> instance.</param>
      <param name="clientPrintSchemaVersion">The Print Schema version to use in the <see cref="T:System.Printing.PrintTicket" /> and DEVMODE conversion.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="deviceName" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="clientPrintSchemaVersion" /> is 0 or less.</exception>
      <exception cref="T:System.Printing.PrintQueueException">The converter was unable to bind to <paramref name="deviceName" />.</exception>
    </member>
    <member name="M:System.Printing.Interop.PrintTicketConverter.ConvertDevModeToPrintTicket(System.Byte[])">
      <summary>Converts the specified DEVMODE structure to a managed code <see cref="T:System.Printing.PrintTicket" />.</summary>
      <param name="devMode">A <see cref="T:System.Byte" /> array that contains the DEVMODE structure.</param>
      <exception cref="T:System.ObjectDisposedException">This <see cref="T:System.Printing.Interop.PrintTicketConverter" /> is already disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The value of <paramref name="devMode" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">The content of <paramref name="devMode" /> is not well-formed.</exception>
      <exception cref="T:System.Printing.PrintQueueException">The conversion failed.</exception>
      <returns>The new managed <see cref="T:System.Printing.PrintTicket" />.</returns>
    </member>
    <member name="M:System.Printing.Interop.PrintTicketConverter.ConvertDevModeToPrintTicket(System.Byte[],System.Printing.PrintTicketScope)">
      <summary>Converts the specified DEVMODE structure to a managed code <see cref="T:System.Printing.PrintTicket" /> that has the specified scope.</summary>
      <param name="devMode">A <see cref="T:System.Byte" /> buffer containing the DEVMODE structure to convert.</param>
      <param name="scope">A <see cref="T:System.Printing.PrintTicketScope" /> value that specifies whether the new <see cref="T:System.Printing.PrintTicket" /> applies to a page, a document, or an entire print job.</param>
      <exception cref="T:System.ObjectDisposedException">This <see cref="T:System.Printing.Interop.PrintTicketConverter" /> is already disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The value of <paramref name="devMode" /> is null (<see langword="Nothing" /> in Visual Basic).</exception>
      <exception cref="T:System.ArgumentException">The content of <paramref name="devMode" /> is not well-formed.</exception>
      <exception cref="T:System.Printing.PrintQueueException">The conversion failed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="scope" /> is not a valid <see cref="T:System.Printing.PrintTicketScope" /> value.</exception>
      <returns>The new <see cref="T:System.Printing.PrintTicket" />.</returns>
    </member>
    <member name="M:System.Printing.Interop.PrintTicketConverter.ConvertPrintTicketToDevMode(System.Printing.PrintTicket,System.Printing.Interop.BaseDevModeType)">
      <summary>Converts the specified managed <see cref="T:System.Printing.PrintTicket" /> to an unmanaged DEVMODE structure that is based on the DEVMODE structure identified by the <see cref="T:System.Printing.Interop.BaseDevModeType" />.</summary>
      <param name="printTicket">The <see cref="T:System.Printing.PrintTicket" /> to convert.</param>
      <param name="baseType">A value that identifies whether to use the user default or printer default DEVMODE as the base DEVMODE.</param>
      <exception cref="T:System.ObjectDisposedException">This <see cref="T:System.Printing.Interop.PrintTicketConverter" /> is already disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The value of <paramref name="printTicket" /> is null (<see langword="Nothing" /> in Visual Basic).</exception>
      <exception cref="T:System.ArgumentException">The content of <paramref name="printTicket" /> is not well-formed.</exception>
      <exception cref="T:System.Printing.PrintQueueException">The conversion failed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="baseType" /> is not a valid <see cref="T:System.Printing.Interop.BaseDevModeType" /> value.</exception>
      <returns>A <see cref="T:System.Byte" /> array that contains the new DEVMODE structure.</returns>
    </member>
    <member name="M:System.Printing.Interop.PrintTicketConverter.ConvertPrintTicketToDevMode(System.Printing.PrintTicket,System.Printing.Interop.BaseDevModeType,System.Printing.PrintTicketScope)">
      <summary>Converts the specified managed code <see cref="T:System.Printing.PrintTicket" /> with the specified scope, into an unmanaged DEVMODE structure that is based on the DEVMODE structure identified by the <see cref="T:System.Printing.Interop.BaseDevModeType" />.</summary>
      <param name="printTicket">The <see cref="T:System.Printing.PrintTicket" /> to convert.</param>
      <param name="baseType">A value that identifies whether to use the user default or printer default DEVMODE as the base DEVMODE.</param>
      <param name="scope">A <see cref="T:System.Printing.PrintTicketScope" /> value that specifies whether the conversion of the <see cref="T:System.Printing.PrintTicket" /> should be done at the scope of a page, a document, or an entire print job.</param>
      <exception cref="T:System.ObjectDisposedException">This <see cref="T:System.Printing.Interop.PrintTicketConverter" /> is already disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The value of <paramref name="printTicket" /> is null (<see langword="Nothing" /> in Visual Basic).</exception>
      <exception cref="T:System.ArgumentException">The content of <paramref name="printTicket" /> is not well-formed.</exception>
      <exception cref="T:System.Printing.PrintQueueException">The conversion failed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="baseType" /> is not a valid <see cref="T:System.Printing.Interop.BaseDevModeType" /> value.  
  
 -or-  
  
 The <paramref name="scope" /> is not a valid <see cref="T:System.Printing.PrintTicketScope" /> value.</exception>
      <returns>A <see cref="T:System.Byte" /> buffer that represents the new DEVMODE structure.</returns>
    </member>
    <member name="M:System.Printing.Interop.PrintTicketConverter.Dispose">
      <summary>Releases the resources used by the <see cref="T:System.Printing.Interop.PrintTicketConverter" />.</summary>
    </member>
    <member name="M:System.Printing.Interop.PrintTicketConverter.System#IDisposable#Dispose">
      <summary>Releases all resources used by the current instance of the <see cref="T:System.Printing.Interop.PrintTicketConverter" /> class.</summary>
    </member>
    <member name="P:System.Printing.Interop.PrintTicketConverter.MaxPrintSchemaVersion">
      <summary>Gets the maximum Print Schema version that <see cref="T:System.Printing.Interop.PrintTicketConverter" /> can support.</summary>
      <returns>The maximum Print Schema version that <see cref="T:System.Printing.Interop.PrintTicketConverter" /> can support.</returns>
    </member>
    <member name="T:System.Printing.OutputColor">
      <summary>Specifies how to print content that contains color or shades of gray.</summary>
    </member>
    <member name="F:System.Printing.OutputColor.Color">
      <summary>Output that prints in color.</summary>
    </member>
    <member name="F:System.Printing.OutputColor.Grayscale">
      <summary>Output that prints in a grayscale.</summary>
    </member>
    <member name="F:System.Printing.OutputColor.Monochrome">
      <summary>Output that prints in a single color and with the same degree of intensity.</summary>
    </member>
    <member name="F:System.Printing.OutputColor.Unknown">
      <summary>The feature (whose options are represented by this enumeration) is set to an option not defined in the Print Schema.</summary>
    </member>
    <member name="T:System.Printing.OutputQuality">
      <summary>Specifies the types of output quality for a print device.</summary>
    </member>
    <member name="F:System.Printing.OutputQuality.Automatic">
      <summary>Automatically selects a quality type that is based on the contents of a print job.</summary>
    </member>
    <member name="F:System.Printing.OutputQuality.Draft">
      <summary>Draft quality.</summary>
    </member>
    <member name="F:System.Printing.OutputQuality.Fax">
      <summary>Fax quality.</summary>
    </member>
    <member name="F:System.Printing.OutputQuality.High">
      <summary>Higher than normal quality.</summary>
    </member>
    <member name="F:System.Printing.OutputQuality.Normal">
      <summary>Normal quality.</summary>
    </member>
    <member name="F:System.Printing.OutputQuality.Photographic">
      <summary>Photographic quality. For more information, see Notes on OutputQuality.Photographic in the Remarks section.</summary>
    </member>
    <member name="F:System.Printing.OutputQuality.Text">
      <summary>Text quality.</summary>
    </member>
    <member name="F:System.Printing.OutputQuality.Unknown">
      <summary>The feature (whose options are represented by this enumeration) is set to an option not defined in the Print Schema.</summary>
    </member>
    <member name="T:System.Printing.PageBorderless">
      <summary>Specifies whether a print device prints to the edge of the media or provides an unprinted margin around the edge.</summary>
    </member>
    <member name="F:System.Printing.PageBorderless.Borderless">
      <summary>Borderless printing, that is, the device prints to the edge of the print media.</summary>
    </member>
    <member name="F:System.Printing.PageBorderless.None">
      <summary>Printing with a border, that is, the device provides an unprinted margin around the edge of the print media.</summary>
    </member>
    <member name="F:System.Printing.PageBorderless.Unknown">
      <summary>The feature (whose options are represented by this enumeration) is set to an option not defined.</summary>
    </member>
    <member name="T:System.Printing.PageImageableArea">
      <summary>Represents the area of a page that can be printed.</summary>
    </member>
    <member name="M:System.Printing.PageImageableArea.ToString">
      <summary>Returns the <see cref="T:System.String" /> representation of <see cref="T:System.Printing.PageImageableArea" />.</summary>
      <returns>A <see cref="T:System.String" /> that represents the property values of the <see cref="T:System.Printing.PageImageableArea" />.</returns>
    </member>
    <member name="P:System.Printing.PageImageableArea.ExtentHeight">
      <summary>Gets the height of the imageable area.</summary>
      <returns>A <see cref="T:System.Double" /> that represents the height of the imageable area in pixels (1/96 of an inch).</returns>
    </member>
    <member name="P:System.Printing.PageImageableArea.ExtentWidth">
      <summary>Gets the width of the imageable area.</summary>
      <returns>A <see cref="T:System.Double" /> that represents the width of the imageable area in pixels (1/96 of an inch).</returns>
    </member>
    <member name="P:System.Printing.PageImageableArea.OriginHeight">
      <summary>Gets the origin height, which is the distance from the upper-left corner of the imageable area (also called the "origin" of the imageable area) to the nearest point on the top edge of the page.</summary>
      <returns>A <see cref="T:System.Double" /> that represents the distance from the top edge of the page to the top of the imageable area in pixels (1/96 of an inch).</returns>
    </member>
    <member name="P:System.Printing.PageImageableArea.OriginWidth">
      <summary>Gets the origin width, which is the distance from the left edge of the page to the upper-left corner of the imageable area (also called the "origin" of the imageable area).</summary>
      <returns>A <see cref="T:System.Double" /> that represents the distance from the left edge of the page to the left edge of the imageable area in pixels (1/96 of an inch).</returns>
    </member>
    <member name="T:System.Printing.PageMediaSize">
      <summary>Describes the page size for paper or other media.</summary>
    </member>
    <member name="M:System.Printing.PageMediaSize.#ctor(System.Double,System.Double)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PageMediaSize" /> class by using the width and height.</summary>
      <param name="width">The width, in pixels, which are 1/96 inch units.</param>
      <param name="height">The height, in pixels, which are 1/96 inch units.</param>
    </member>
    <member name="M:System.Printing.PageMediaSize.#ctor(System.Printing.PageMediaSizeName)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PageMediaSize" /> class by using the specified <see cref="T:System.Printing.PageMediaSizeName" />.</summary>
      <param name="mediaSizeName">The name of the page size for paper (or other media), for example, <see cref="F:System.Printing.PageMediaSizeName.NorthAmericaLetter" /> or <see cref="F:System.Printing.PageMediaSizeName.ISOA4" />.</param>
    </member>
    <member name="M:System.Printing.PageMediaSize.#ctor(System.Printing.PageMediaSizeName,System.Double,System.Double)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PageMediaSize" /> class by using the specified <see cref="T:System.Printing.PageMediaSizeName" /> and the width and height.</summary>
      <param name="mediaSizeName">The name of the page size for paper (or other media), for example, <see cref="F:System.Printing.PageMediaSizeName.NorthAmericaLetter" /> or <see cref="F:System.Printing.PageMediaSizeName.ISOA4" />.</param>
      <param name="width">The width, in pixels, which are 1/96 inch units.</param>
      <param name="height">The height, in pixels, which are 1/96 inch units.</param>
    </member>
    <member name="M:System.Printing.PageMediaSize.ToString">
      <summary>Displays the page size for paper or other media.</summary>
      <returns>A <see cref="T:System.String" /> that represents the page size for paper or other media.</returns>
    </member>
    <member name="P:System.Printing.PageMediaSize.Height">
      <summary>Gets the page height.</summary>
      <returns>A <see cref="T:System.Double" /> that represents the page height, in pixels, which are 1/96 inch units.</returns>
    </member>
    <member name="P:System.Printing.PageMediaSize.PageMediaSizeName">
      <summary>Gets the name of the page size for paper or other media.</summary>
      <returns>A <see cref="T:System.Printing.PageMediaSizeName" /> value that names the page size.</returns>
    </member>
    <member name="P:System.Printing.PageMediaSize.Width">
      <summary>Gets the page width.</summary>
      <returns>A <see cref="T:System.Double" /> that represents the page width, in pixels, which are 1/96 inch units.</returns>
    </member>
    <member name="T:System.Printing.PageMediaSizeName">
      <summary>Specifies the page size or roll width of the paper or other print media.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.BusinessCard">
      <summary>Business card.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.CreditCard">
      <summary>Credit card.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.ISOA0">
      <summary>A0</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.ISOA1">
      <summary>A1</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.ISOA10">
      <summary>A10</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.ISOA2">
      <summary>A2</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.ISOA3">
      <summary>A3</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.ISOA3Extra">
      <summary>A3 Extra.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.ISOA3Rotated">
      <summary>A3 Rotated.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.ISOA4">
      <summary>A4</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.ISOA4Extra">
      <summary>A4 Extra.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.ISOA4Rotated">
      <summary>A4 Rotated.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.ISOA5">
      <summary>A5</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.ISOA5Extra">
      <summary>A5 Extra.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.ISOA5Rotated">
      <summary>A5 Rotated.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.ISOA6">
      <summary>A6</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.ISOA6Rotated">
      <summary>A6 Rotated.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.ISOA7">
      <summary>A7</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.ISOA8">
      <summary>A8</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.ISOA9">
      <summary>A9</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.ISOB0">
      <summary>B0</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.ISOB1">
      <summary>B1</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.ISOB10">
      <summary>B10</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.ISOB2">
      <summary>B2</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.ISOB3">
      <summary>B3</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.ISOB4">
      <summary>B4</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.ISOB4Envelope">
      <summary>B4 Envelope.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.ISOB5Envelope">
      <summary>B5 Envelope.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.ISOB5Extra">
      <summary>B5 Extra.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.ISOB7">
      <summary>B7</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.ISOB8">
      <summary>B8</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.ISOB9">
      <summary>B9</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.ISOC0">
      <summary>C0</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.ISOC1">
      <summary>C1</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.ISOC10">
      <summary>C10</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.ISOC2">
      <summary>C2</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.ISOC3">
      <summary>C3</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.ISOC3Envelope">
      <summary>C3 Envelope.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.ISOC4">
      <summary>C4</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.ISOC4Envelope">
      <summary>C4 Envelope.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.ISOC5">
      <summary>C5</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.ISOC5Envelope">
      <summary>C5 Envelope.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.ISOC6">
      <summary>C6</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.ISOC6C5Envelope">
      <summary>C6C5 Envelope.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.ISOC6Envelope">
      <summary>C6 Envelope.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.ISOC7">
      <summary>C7</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.ISOC8">
      <summary>C8</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.ISOC9">
      <summary>C9</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.ISODLEnvelope">
      <summary>DL Envelope.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.ISODLEnvelopeRotated">
      <summary>DL Envelope Rotated.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.ISOSRA3">
      <summary>SRA 3</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.Japan2LPhoto">
      <summary>2L Photo.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.JapanChou3Envelope">
      <summary>Chou 3 Envelope.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.JapanChou3EnvelopeRotated">
      <summary>Chou 3 Envelope Rotated.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.JapanChou4Envelope">
      <summary>Chou 4 Envelope.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.JapanChou4EnvelopeRotated">
      <summary>Chou 4 Envelope Rotated.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.JapanDoubleHagakiPostcard">
      <summary>Double Hagaki Postcard.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.JapanDoubleHagakiPostcardRotated">
      <summary>Double Hagaki Postcard Rotated.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.JapanHagakiPostcard">
      <summary>Hagaki Postcard.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.JapanHagakiPostcardRotated">
      <summary>Hagaki Postcard Rotated.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.JapanKaku2Envelope">
      <summary>Kaku 2 Envelope.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.JapanKaku2EnvelopeRotated">
      <summary>Kaku 2 Envelope Rotated.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.JapanKaku3Envelope">
      <summary>Kaku 3 Envelope.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.JapanKaku3EnvelopeRotated">
      <summary>Kaku 3 Envelope Rotated.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.JapanLPhoto">
      <summary>L Photo.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.JapanQuadrupleHagakiPostcard">
      <summary>Quadruple Hagaki Postcard.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.JapanYou1Envelope">
      <summary>You 1 Envelope.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.JapanYou2Envelope">
      <summary>You 2 Envelope.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.JapanYou3Envelope">
      <summary>You 3 Envelope.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.JapanYou4Envelope">
      <summary>You 4 Envelope.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.JapanYou4EnvelopeRotated">
      <summary>You 4 Envelope Rotated.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.JapanYou6Envelope">
      <summary>You 6 Envelope.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.JapanYou6EnvelopeRotated">
      <summary>You 6 Envelope Rotated.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.JISB0">
      <summary>Japanese Industrial Standard B0</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.JISB1">
      <summary>Japanese Industrial Standard B1</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.JISB10">
      <summary>Japanese Industrial Standard B10</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.JISB2">
      <summary>Japanese Industrial Standard B2</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.JISB3">
      <summary>Japanese Industrial Standard B3</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.JISB4">
      <summary>Japanese Industrial Standard B4</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.JISB4Rotated">
      <summary>Japanese Industrial Standard B4 Rotated.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.JISB5">
      <summary>Japanese Industrial Standard B5</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.JISB5Rotated">
      <summary>Japanese Industrial Standard B5 Rotated.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.JISB6">
      <summary>Japanese Industrial Standard B6</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.JISB6Rotated">
      <summary>Japanese Industrial Standard B6 Rotated.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.JISB7">
      <summary>Japanese Industrial Standard B7</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.JISB8">
      <summary>Japanese Industrial Standard B8</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.JISB9">
      <summary>Japanese Industrial Standard B9</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.NorthAmerica10x11">
      <summary>10 x 11</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.NorthAmerica10x12">
      <summary>10 x 12</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.NorthAmerica10x14">
      <summary>10 x 14</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.NorthAmerica11x17">
      <summary>11 x 17</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.NorthAmerica14x17">
      <summary>14 x 17</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.NorthAmerica4x6">
      <summary>4 x 6</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.NorthAmerica4x8">
      <summary>4 x 8</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.NorthAmerica5x7">
      <summary>5 x 7</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.NorthAmerica8x10">
      <summary>8 x 10</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.NorthAmerica9x11">
      <summary>9 x 11</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.NorthAmericaArchitectureASheet">
      <summary>Architecture A Sheet.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.NorthAmericaArchitectureBSheet">
      <summary>Architecture B Sheet.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.NorthAmericaArchitectureCSheet">
      <summary>Architecture C Sheet.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.NorthAmericaArchitectureDSheet">
      <summary>Architecture D Sheet.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.NorthAmericaArchitectureESheet">
      <summary>Architecture E Sheet.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.NorthAmericaCSheet">
      <summary>C Sheet.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.NorthAmericaDSheet">
      <summary>D Sheet.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.NorthAmericaESheet">
      <summary>E Sheet.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.NorthAmericaExecutive">
      <summary>Executive.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.NorthAmericaGermanLegalFanfold">
      <summary>German Legal Fanfold.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.NorthAmericaGermanStandardFanfold">
      <summary>German Standard Fanfold.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.NorthAmericaLegal">
      <summary>Legal.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.NorthAmericaLegalExtra">
      <summary>Legal Extra.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.NorthAmericaLetter">
      <summary>Letter.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.NorthAmericaLetterExtra">
      <summary>Letter Extra.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.NorthAmericaLetterPlus">
      <summary>Letter Plus.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.NorthAmericaLetterRotated">
      <summary>Letter Rotated.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.NorthAmericaMonarchEnvelope">
      <summary>Monarch Envelope.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.NorthAmericaNote">
      <summary>Note.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.NorthAmericaNumber10Envelope">
      <summary>#10 Envelope.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.NorthAmericaNumber10EnvelopeRotated">
      <summary>#10 Envelope Rotated.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.NorthAmericaNumber11Envelope">
      <summary>#11 Envelope.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.NorthAmericaNumber12Envelope">
      <summary>#12 Envelope.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.NorthAmericaNumber14Envelope">
      <summary>#14 Envelope.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.NorthAmericaNumber9Envelope">
      <summary>#9 Envelope.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.NorthAmericaPersonalEnvelope">
      <summary>Personal Envelope.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.NorthAmericaQuarto">
      <summary>Quarto.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.NorthAmericaStatement">
      <summary>Statement.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.NorthAmericaSuperA">
      <summary>Super A</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.NorthAmericaSuperB">
      <summary>Super B</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.NorthAmericaTabloid">
      <summary>Tabloid.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.NorthAmericaTabloidExtra">
      <summary>Tabloid Extra.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.OtherMetricA3Plus">
      <summary>A3 Plus.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.OtherMetricA4Plus">
      <summary>A4 Plus.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.OtherMetricFolio">
      <summary>Folio.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.OtherMetricInviteEnvelope">
      <summary>Invite Envelope.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.OtherMetricItalianEnvelope">
      <summary>Italian Envelope.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.PRC10Envelope">
      <summary>People's Republic of China #10 Envelope.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.PRC10EnvelopeRotated">
      <summary>People's Republic of China #10 Envelope Rotated.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.PRC16K">
      <summary>People's Republic of China 16K.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.PRC16KRotated">
      <summary>People's Republic of China 16K Rotated.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.PRC1Envelope">
      <summary>People's Republic of China #1 Envelope.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.PRC1EnvelopeRotated">
      <summary>People's Republic of China #1 Envelope Rotated.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.PRC2Envelope">
      <summary>People's Republic of China #2 Envelope.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.PRC2EnvelopeRotated">
      <summary>People's Republic of China #2 Envelope Rotated.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.PRC32K">
      <summary>People's Republic of China 32K.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.PRC32KBig">
      <summary>People's Republic of China 32K Big.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.PRC32KRotated">
      <summary>People's Republic of China 32K Rotated.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.PRC3Envelope">
      <summary>People's Republic of China #3 Envelope.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.PRC3EnvelopeRotated">
      <summary>People's Republic of China #3 Envelope Rotated.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.PRC4Envelope">
      <summary>People's Republic of China #4 Envelope.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.PRC4EnvelopeRotated">
      <summary>People's Republic of China #4 Envelope Rotated.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.PRC5Envelope">
      <summary>People's Republic of China #5 Envelope.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.PRC5EnvelopeRotated">
      <summary>People's Republic of China #5 Envelope Rotated.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.PRC6Envelope">
      <summary>People's Republic of China #6 Envelope.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.PRC6EnvelopeRotated">
      <summary>People's Republic of China #6 Envelope Rotated.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.PRC7Envelope">
      <summary>People's Republic of China #7 Envelope.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.PRC7EnvelopeRotated">
      <summary>People's Republic of China #7 Envelope Rotated.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.PRC8Envelope">
      <summary>People's Republic of China #8 Envelope.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.PRC8EnvelopeRotated">
      <summary>People's Republic of China #8 Envelope Rotated.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.PRC9Envelope">
      <summary>People's Republic of China #9 Envelope.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.PRC9EnvelopeRotated">
      <summary>People's Republic of China #9 Envelope Rotated.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.Roll04Inch">
      <summary>4-inch wide roll.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.Roll06Inch">
      <summary>6-inch wide roll.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.Roll08Inch">
      <summary>8-inch wide roll.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.Roll12Inch">
      <summary>12-inch wide roll.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.Roll15Inch">
      <summary>15-inch wide roll.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.Roll18Inch">
      <summary>18-inch wide roll.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.Roll22Inch">
      <summary>22-inch wide roll.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.Roll24Inch">
      <summary>24-inch wide roll.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.Roll30Inch">
      <summary>30-inch wide roll.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.Roll36Inch">
      <summary>36-inch wide roll.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.Roll54Inch">
      <summary>54-inch wide roll.</summary>
    </member>
    <member name="F:System.Printing.PageMediaSizeName.Unknown">
      <summary>Unknown paper size.</summary>
    </member>
    <member name="T:System.Printing.PageMediaType">
      <summary>Specifies types of printing paper or other media.</summary>
    </member>
    <member name="F:System.Printing.PageMediaType.Archival">
      <summary>Archive-quality media.</summary>
    </member>
    <member name="F:System.Printing.PageMediaType.AutoSelect">
      <summary>The print device selects the media.</summary>
    </member>
    <member name="F:System.Printing.PageMediaType.BackPrintFilm">
      <summary>Specialty back-printing film.</summary>
    </member>
    <member name="F:System.Printing.PageMediaType.Bond">
      <summary>Standard bond media.</summary>
    </member>
    <member name="F:System.Printing.PageMediaType.CardStock">
      <summary>Standard card stock.</summary>
    </member>
    <member name="F:System.Printing.PageMediaType.Continuous">
      <summary>Continuous-feed media.</summary>
    </member>
    <member name="F:System.Printing.PageMediaType.EnvelopePlain">
      <summary>Standard envelope.</summary>
    </member>
    <member name="F:System.Printing.PageMediaType.EnvelopeWindow">
      <summary>Window envelope.</summary>
    </member>
    <member name="F:System.Printing.PageMediaType.Fabric">
      <summary>Fabric media.</summary>
    </member>
    <member name="F:System.Printing.PageMediaType.HighResolution">
      <summary>Specialty high-resolution media.</summary>
    </member>
    <member name="F:System.Printing.PageMediaType.Label">
      <summary>Label media.</summary>
    </member>
    <member name="F:System.Printing.PageMediaType.MultiLayerForm">
      <summary>Attached multipart forms.</summary>
    </member>
    <member name="F:System.Printing.PageMediaType.MultiPartForm">
      <summary>Individual multipart forms.</summary>
    </member>
    <member name="F:System.Printing.PageMediaType.None">
      <summary>Unknown or unlisted media.</summary>
    </member>
    <member name="F:System.Printing.PageMediaType.Photographic">
      <summary>Standard photographic media.</summary>
    </member>
    <member name="F:System.Printing.PageMediaType.PhotographicFilm">
      <summary>Film photographic media.</summary>
    </member>
    <member name="F:System.Printing.PageMediaType.PhotographicGlossy">
      <summary>Glossy photographic media.</summary>
    </member>
    <member name="F:System.Printing.PageMediaType.PhotographicHighGloss">
      <summary>High-gloss photographic media.</summary>
    </member>
    <member name="F:System.Printing.PageMediaType.PhotographicMatte">
      <summary>Matte photographic media.</summary>
    </member>
    <member name="F:System.Printing.PageMediaType.PhotographicSatin">
      <summary>Satin photographic media.</summary>
    </member>
    <member name="F:System.Printing.PageMediaType.PhotographicSemiGloss">
      <summary>Semi-gloss photographic media.</summary>
    </member>
    <member name="F:System.Printing.PageMediaType.Plain">
      <summary>Plain paper.</summary>
    </member>
    <member name="F:System.Printing.PageMediaType.Screen">
      <summary>Output to a display in continuous form.</summary>
    </member>
    <member name="F:System.Printing.PageMediaType.ScreenPaged">
      <summary>Output to a display in paged form.</summary>
    </member>
    <member name="F:System.Printing.PageMediaType.Stationery">
      <summary>Specialty stationary.</summary>
    </member>
    <member name="F:System.Printing.PageMediaType.TabStockFull">
      <summary>Tab stock, not precut (single tabs).</summary>
    </member>
    <member name="F:System.Printing.PageMediaType.TabStockPreCut">
      <summary>Tab stock, precut (multiple tabs).</summary>
    </member>
    <member name="F:System.Printing.PageMediaType.Transparency">
      <summary>Transparent sheet.</summary>
    </member>
    <member name="F:System.Printing.PageMediaType.TShirtTransfer">
      <summary>Media that is used to transfer an image to a T-shirt.</summary>
    </member>
    <member name="F:System.Printing.PageMediaType.Unknown">
      <summary>The feature (whose options are represented by this enumeration) is set to an option not defined in the Print Schema.</summary>
    </member>
    <member name="T:System.Printing.PageOrder">
      <summary>Specifies whether a print device prints multi-page documents from front-to-back or back-to-front.</summary>
    </member>
    <member name="F:System.Printing.PageOrder.Reverse">
      <summary>Output prints in back-to-front order.</summary>
    </member>
    <member name="F:System.Printing.PageOrder.Standard">
      <summary>Output prints in front-to-back order.</summary>
    </member>
    <member name="F:System.Printing.PageOrder.Unknown">
      <summary>The feature (whose options are represented by this enumeration) is set to an option not defined in the Print Schema.</summary>
    </member>
    <member name="T:System.Printing.PageOrientation">
      <summary>Specifies how pages of content are oriented on print media.</summary>
    </member>
    <member name="F:System.Printing.PageOrientation.Landscape">
      <summary>Content of the imageable area is rotated on the page 90 degrees counterclockwise from standard (portrait) orientation.</summary>
    </member>
    <member name="F:System.Printing.PageOrientation.Portrait">
      <summary>Standard orientation.</summary>
    </member>
    <member name="F:System.Printing.PageOrientation.ReverseLandscape">
      <summary>Content of the imageable area is rotated on the page 90 degrees clockwise from standard (portrait) orientation.</summary>
    </member>
    <member name="F:System.Printing.PageOrientation.ReversePortrait">
      <summary>Content of the imageable area is upside down relative to standard (portrait) orientation.</summary>
    </member>
    <member name="F:System.Printing.PageOrientation.Unknown">
      <summary>The feature (whose options are represented by this enumeration) is set to an option not defined in the Print Schema.</summary>
    </member>
    <member name="T:System.Printing.PageQualitativeResolution">
      <summary>Specifies the page resolution as a qualitative, non-numerical, value.</summary>
    </member>
    <member name="F:System.Printing.PageQualitativeResolution.Default">
      <summary>The default qualitative resolution for the printer.</summary>
    </member>
    <member name="F:System.Printing.PageQualitativeResolution.Draft">
      <summary>Draft-level quality, which is 300 dpi for most printers.</summary>
    </member>
    <member name="F:System.Printing.PageQualitativeResolution.High">
      <summary>High quality, which is 1200 dpi or greater for most printers.</summary>
    </member>
    <member name="F:System.Printing.PageQualitativeResolution.Normal">
      <summary>Normal quality, which is 600 dpi for most printers.</summary>
    </member>
    <member name="F:System.Printing.PageQualitativeResolution.Other">
      <summary>Other quality.</summary>
    </member>
    <member name="F:System.Printing.PageQualitativeResolution.Unknown">
      <summary>The feature (whose options are represented by this enumeration) is set to an option not defined in the Print Schema.</summary>
    </member>
    <member name="T:System.Printing.PageResolution">
      <summary>Defines the page resolution of printed output as either a qualitative value or as dots per inch, or both.</summary>
    </member>
    <member name="M:System.Printing.PageResolution.#ctor(System.Int32,System.Int32)">
      <summary>Initiates a new instance of the <see cref="T:System.Printing.PageResolution" /> class that has the specified vertical and horizontal dots per inch.</summary>
      <param name="resolutionX">The horizontal resolution in dots per inch.</param>
      <param name="resolutionY">The vertical resolution in dots per inch.</param>
    </member>
    <member name="M:System.Printing.PageResolution.#ctor(System.Int32,System.Int32,System.Printing.PageQualitativeResolution)">
      <summary>Initiates a new instance of the <see cref="T:System.Printing.PageResolution" /> class that has the specified <see cref="T:System.Printing.PageQualitativeResolution" /> and the specified vertical and horizontal dots per inch.</summary>
      <param name="resolutionX">The horizontal resolution in dots per inch.</param>
      <param name="resolutionY">The vertical resolution in dots per inch.</param>
      <param name="qualitative">A value representing the resolution.</param>
    </member>
    <member name="M:System.Printing.PageResolution.#ctor(System.Printing.PageQualitativeResolution)">
      <summary>Initiates a new instance of the <see cref="T:System.Printing.PageResolution" /> class that has the specified <see cref="T:System.Printing.PageQualitativeResolution" />.</summary>
      <param name="qualitative">A value representing the resolution.</param>
    </member>
    <member name="M:System.Printing.PageResolution.ToString">
      <summary>Returns the page resolution as a <see cref="T:System.String" />.</summary>
      <returns>A <see cref="T:System.String" /> that contains the property values of the <see cref="T:System.Printing.PageResolution" /> object.</returns>
    </member>
    <member name="P:System.Printing.PageResolution.QualitativeResolution">
      <summary>Gets the qualitative expression, if any, of the page resolution.</summary>
      <returns>A <see cref="T:System.Printing.PageQualitativeResolution" /> value that represents the level of page resolution.</returns>
    </member>
    <member name="P:System.Printing.PageResolution.X">
      <summary>Gets the dots-per-inch measure of the horizontal page resolution.</summary>
      <returns>An <see cref="T:System.Int32" /> value that represents a horizontal page resolution.</returns>
    </member>
    <member name="P:System.Printing.PageResolution.Y">
      <summary>Gets the dots-per-inch measure of the vertical page resolution.</summary>
      <returns>A nullable <see cref="T:System.Int32" /> value that represents a vertical page resolution.</returns>
    </member>
    <member name="T:System.Printing.PageScalingFactorRange">
      <summary>Specifies a range of percentages by which a printer can enlarge or reduce the print image on a page.</summary>
    </member>
    <member name="M:System.Printing.PageScalingFactorRange.ToString">
      <summary>Returns the <see cref="T:System.String" /> representation of the range.</summary>
      <returns>A <see cref="T:System.String" /> representation of the scaling range.</returns>
    </member>
    <member name="P:System.Printing.PageScalingFactorRange.MaximumScale">
      <summary>Gets the maximum percentage of the range.</summary>
      <returns>An <see cref="T:System.Int32" /> value that represents a percentage.</returns>
    </member>
    <member name="P:System.Printing.PageScalingFactorRange.MinimumScale">
      <summary>Gets the minimum percentage of the range.</summary>
      <returns>An <see cref="T:System.Int32" /> value that represents a percentage.</returns>
    </member>
    <member name="T:System.Printing.PagesPerSheetDirection">
      <summary>Specifies the arrangement of pages when more than one page of content appears on a single side of print media.</summary>
    </member>
    <member name="F:System.Printing.PagesPerSheetDirection.BottomLeft">
      <summary>Pages appear in columns, from top to bottom and right to left relative to page orientation.</summary>
    </member>
    <member name="F:System.Printing.PagesPerSheetDirection.BottomRight">
      <summary>Pages appear in columns, from top to bottom and left to right relative to page orientation.</summary>
    </member>
    <member name="F:System.Printing.PagesPerSheetDirection.LeftBottom">
      <summary>Pages appear in rows, from right to left and top to bottom relative to page orientation.</summary>
    </member>
    <member name="F:System.Printing.PagesPerSheetDirection.LeftTop">
      <summary>Pages appear in rows, from right to left and bottom to top relative to page orientation.</summary>
    </member>
    <member name="F:System.Printing.PagesPerSheetDirection.RightBottom">
      <summary>Pages appear in rows, from left to right and top to bottom relative to page orientation.</summary>
    </member>
    <member name="F:System.Printing.PagesPerSheetDirection.RightTop">
      <summary>Pages appear in rows, from left to right and bottom to top relative to page orientation.</summary>
    </member>
    <member name="F:System.Printing.PagesPerSheetDirection.TopLeft">
      <summary>Pages appear in columns, from bottom to top and right to left relative to page orientation.</summary>
    </member>
    <member name="F:System.Printing.PagesPerSheetDirection.TopRight">
      <summary>Pages appear in columns, from bottom to top and left to right relative to page orientation.</summary>
    </member>
    <member name="F:System.Printing.PagesPerSheetDirection.Unknown">
      <summary>The feature (whose options are represented by this enumeration) is set to an option not defined in the Print Schema.</summary>
    </member>
    <member name="T:System.Printing.PhotoPrintingIntent">
      <summary>Specifies the quality of output when a photograph is printed. The printer driver translates the <see cref="T:System.Printing.PhotoPrintingIntent" /> into quantitative values for resolution and other quality factors.</summary>
    </member>
    <member name="F:System.Printing.PhotoPrintingIntent.None">
      <summary>No photo-printing intent. The user sets specific quantitative properties.</summary>
    </member>
    <member name="F:System.Printing.PhotoPrintingIntent.PhotoBest">
      <summary>Best quality photo printing.</summary>
    </member>
    <member name="F:System.Printing.PhotoPrintingIntent.PhotoDraft">
      <summary>Draft quality photo printing.</summary>
    </member>
    <member name="F:System.Printing.PhotoPrintingIntent.PhotoStandard">
      <summary>Standard quality photo printing.</summary>
    </member>
    <member name="F:System.Printing.PhotoPrintingIntent.Unknown">
      <summary>The feature (whose options are represented by this enumeration) is set to an option not defined in the Print Schema.</summary>
    </member>
    <member name="T:System.Printing.PrintCapabilities">
      <summary>Defines the capabilities of a printer.</summary>
    </member>
    <member name="M:System.Printing.PrintCapabilities.#ctor(System.IO.Stream)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintCapabilities" /> class by using an XML stream (that contains a PrintCapabilities document) that specifies printer capabilities and complies with the XML Print Schema.</summary>
      <param name="xmlStream">An XML <see cref="T:System.IO.Stream" /> that describes printer capabilities and conforms to the Print Schema.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="xmlStream" /> is <see langword="null" />.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="xmlStream" /> is not valid XML.</exception>
    </member>
    <member name="P:System.Printing.PrintCapabilities.CollationCapability">
      <summary>Gets a collection of values that identify the collation capabilities of a printer.</summary>
      <returns>A <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" /> of <see cref="T:System.Printing.Collation" /> values.</returns>
    </member>
    <member name="P:System.Printing.PrintCapabilities.DeviceFontSubstitutionCapability">
      <summary>Gets a collection of values that identify whether and how a printer can substitute device-based fonts for computer-based fonts.</summary>
      <returns>A <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" /> of <see cref="T:System.Printing.DeviceFontSubstitution" /> values.</returns>
    </member>
    <member name="P:System.Printing.PrintCapabilities.DuplexingCapability">
      <summary>Gets a collection of values that identify whether and how a printer can perform two-sided printing.</summary>
      <returns>A <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" /> of <see cref="T:System.Printing.Duplexing" /> values.</returns>
    </member>
    <member name="P:System.Printing.PrintCapabilities.InputBinCapability">
      <summary>Gets a collection of values that indicate what input bin (paper tray) is used.</summary>
      <returns>A <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" /> of <see cref="T:System.Printing.InputBin" /> values.</returns>
    </member>
    <member name="P:System.Printing.PrintCapabilities.MaxCopyCount">
      <summary>Gets a value indicating the maximum number of copies that the device can print in a single print job.</summary>
      <returns>A nullable <see cref="T:System.Int32" /> value that specifies the maximum number of copies that a printer can print. Returns <see langword="null" /> if the device driver does not report a maximum.</returns>
    </member>
    <member name="P:System.Printing.PrintCapabilities.OrientedPageMediaHeight">
      <summary>Gets a value indicating the height of the imageable area on a page, where height means the vertical dimension relative to the page's orientation.</summary>
      <returns>A nullable <see cref="T:System.Double" /> value indicating the height, in pixels, which are 1/96 inch increments, of the area on a page that the printer is capable of printing.</returns>
    </member>
    <member name="P:System.Printing.PrintCapabilities.OrientedPageMediaWidth">
      <summary>Gets a value indicating the width of the imageable area on a page, where width means the horizontal dimension relative to the page's orientation.</summary>
      <returns>A nullable <see cref="T:System.Double" /> value indicating the width, in pixels, which are 1/96 inch increments, of the area on a page that the printer is capable of printing.</returns>
    </member>
    <member name="P:System.Printing.PrintCapabilities.OutputColorCapability">
      <summary>Gets a collection of values that specify the ways in which a printer can print content with color and shades of gray.</summary>
      <returns>A <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" /> of <see cref="T:System.Printing.OutputColor" /> values.</returns>
    </member>
    <member name="P:System.Printing.PrintCapabilities.OutputQualityCapability">
      <summary>Gets a collection of values that indicate the types of output quality the printer supports.</summary>
      <returns>A <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" /> of <see cref="T:System.Printing.OutputQuality" /> values.</returns>
    </member>
    <member name="P:System.Printing.PrintCapabilities.PageBorderlessCapability">
      <summary>Gets a collection of values that indicate whether the printer can print up to the edge of the media.</summary>
      <returns>A <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" /> of <see cref="T:System.Printing.PageBorderless" /> values.</returns>
    </member>
    <member name="P:System.Printing.PrintCapabilities.PageImageableArea">
      <summary>Gets an object that represents the area of a page that the printer can use.</summary>
      <returns>A <see cref="T:System.Printing.PageImageableArea" /> object that specifies the distance, in pixels (units of 1/96 inch), of the upper-left corner of the imageable area. The vertical distance is measured from the top edge of the paper and the horizontal distance is measured from the left edge. The return value also specifies the width and height of the imageable area. If the printer driver does not report an imageable area, then this property is null.</returns>
    </member>
    <member name="P:System.Printing.PrintCapabilities.PageMediaSizeCapability">
      <summary>Gets a collection of <see cref="T:System.Printing.PageMediaSize" /> objects that identify the paper and media sizes that a printer supports.</summary>
      <returns>A <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" /> of <see cref="T:System.Printing.PageMediaSize" /> objects.</returns>
    </member>
    <member name="P:System.Printing.PrintCapabilities.PageMediaTypeCapability">
      <summary>Gets a collection of values that identify what types of paper and other media a printer supports.</summary>
      <returns>A <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" /> of <see cref="T:System.Printing.PageMediaType" /> values specifying the print media, such as card stock, label, plain, or photographic.</returns>
    </member>
    <member name="P:System.Printing.PrintCapabilities.PageOrderCapability">
      <summary>Gets a collection of values that indicate whether a printer is capable of printing multiple-page documents from front-to-back, back-to-front, or both ways.</summary>
      <returns>A <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" /> of <see cref="T:System.Printing.PageOrder" /> values.</returns>
    </member>
    <member name="P:System.Printing.PrintCapabilities.PageOrientationCapability">
      <summary>Gets a collection of values that identify what types of page orientation a printer supports.</summary>
      <returns>A <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" /> of <see cref="T:System.Printing.PageOrientation" /> values.</returns>
    </member>
    <member name="P:System.Printing.PrintCapabilities.PageResolutionCapability">
      <summary>Gets a collection of <see cref="T:System.Printing.PageResolution" /> objects that identify what levels of page resolution the printer supports.</summary>
      <returns>A <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" /> of <see cref="T:System.Printing.PageResolution" /> objects. Each value represents a page resolution as a qualitative value, a dots-per-inch value, or both.</returns>
    </member>
    <member name="P:System.Printing.PrintCapabilities.PageScalingFactorRange">
      <summary>Gets the maximum and minimum percentages by which a printer can enlarge or reduce the print image on a page.</summary>
      <returns>A <see cref="T:System.Printing.PageScalingFactorRange" /> object with <see cref="P:System.Printing.PageScalingFactorRange.MaximumScale" /> and <see cref="P:System.Printing.PageScalingFactorRange.MinimumScale" /> properties holding <see cref="T:System.Int32" /> values that represent percentages.</returns>
    </member>
    <member name="P:System.Printing.PrintCapabilities.PagesPerSheetCapability">
      <summary>Gets a collection of integers, each identifying the number of pages that a user can choose to print on a single side of a sheet of paper.</summary>
      <returns>A <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" /> of <see cref="T:System.Int32" /> values representing the options that a printer supports for printing more than one page per side.</returns>
    </member>
    <member name="P:System.Printing.PrintCapabilities.PagesPerSheetDirectionCapability">
      <summary>Gets a collection of values that identify what patterns a printer supports for presenting multiple pages on a single side of a sheet of paper.</summary>
      <returns>A <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" /> of <see cref="T:System.Printing.PagesPerSheetDirection" /> values.</returns>
    </member>
    <member name="P:System.Printing.PrintCapabilities.PhotoPrintingIntentCapability">
      <summary>Gets a collection of values that identify the quality options the printer supports for printing photographs.</summary>
      <returns>A <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" /> of <see cref="T:System.Printing.PhotoPrintingIntent" /> values.</returns>
    </member>
    <member name="P:System.Printing.PrintCapabilities.StaplingCapability">
      <summary>Gets a collection of values that identify the types of automatic stapling that a printer supports.</summary>
      <returns>A <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" /> of <see cref="T:System.Printing.Stapling" /> values.</returns>
    </member>
    <member name="P:System.Printing.PrintCapabilities.TrueTypeFontModeCapability">
      <summary>Gets a collection of values that identify the methods that a printer supports for handling TrueType fonts.</summary>
      <returns>A <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" /> of <see cref="T:System.Printing.TrueTypeFontMode" /> values.</returns>
    </member>
    <member name="T:System.Printing.PrintCommitAttributesException">
      <summary>The exception that is thrown when an error condition prevents some attributes from being committed by a <see cref="T:System.Printing.PrintSystemObject" /> to the actual computer, printer, or device that the object represents.</summary>
    </member>
    <member name="M:System.Printing.PrintCommitAttributesException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintCommitAttributesException" /> class.</summary>
    </member>
    <member name="M:System.Printing.PrintCommitAttributesException.#ctor(System.Int32,System.Collections.ObjectModel.Collection{System.String},System.Collections.ObjectModel.Collection{System.String})">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintCommitAttributesException" /> class by using the specified error code, and the success and failure collections.</summary>
      <param name="errorCode">An <see cref="T:System.Int32" /> that is interpreted as an HRESULT, a coded numerical value that is assigned to a specific exception.</param>
      <param name="attributesSuccessList">A collection of strings that name the attributes that your program successfully committed.</param>
      <param name="attributesFailList">A collection of strings that name the attributes that your program was unable to commit.</param>
    </member>
    <member name="M:System.Printing.PrintCommitAttributesException.#ctor(System.Int32,System.String,System.Collections.ObjectModel.Collection{System.String},System.Collections.ObjectModel.Collection{System.String},System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintCommitAttributesException" /> class by using the specified error code, message, and object name, and the success and failure collections.</summary>
      <param name="errorCode">An <see cref="T:System.Int32" /> that is interpreted as an HRESULT, a coded numerical value that is assigned to a specific exception.</param>
      <param name="message">A description of the error condition.</param>
      <param name="attributesSuccessList">A collection of strings that name the attributes that your program successfully committed.</param>
      <param name="attributesFailList">A collection of strings that name the attributes that your program was unable to commit.</param>
      <param name="objectName">The object that generated the exception.</param>
    </member>
    <member name="M:System.Printing.PrintCommitAttributesException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintCommitAttributesException" /> class by using the specified <see cref="T:System.Runtime.Serialization.SerializationInfo" /> and <see cref="T:System.Runtime.Serialization.StreamingContext" />.</summary>
      <param name="info">Stores all the data that is used to serialize the object.</param>
      <param name="context">Describes the context of the serialized stream, including the source and the destination.</param>
    </member>
    <member name="M:System.Printing.PrintCommitAttributesException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintCommitAttributesException" /> class and provides it with the specified message.</summary>
      <param name="message">A description of the error condition.</param>
    </member>
    <member name="M:System.Printing.PrintCommitAttributesException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintCommitAttributesException" /> class by using the specified message and the inner <see cref="T:System.Exception" />.</summary>
      <param name="message">A description of the error condition.</param>
      <param name="innerException">The underlying condition that triggered the <see cref="T:System.Printing.PrintCommitAttributesException" />.</param>
    </member>
    <member name="M:System.Printing.PrintCommitAttributesException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Gets information about the serialization of the object and the context of the serialized stream.</summary>
      <param name="info">Stores all the data that is used to serialize the object.</param>
      <param name="context">Describes the context of the serialized stream, including the source and the destination.</param>
    </member>
    <member name="P:System.Printing.PrintCommitAttributesException.CommittedAttributesCollection">
      <summary>Gets a <see cref="T:System.Collections.ObjectModel.Collection`1" /> of the names of the attributes that were successfully committed.</summary>
      <returns>A <see cref="T:System.Collections.ObjectModel.Collection`1" /> of strings that identify the <see cref="T:System.Printing.PrintSystemObject" /> attributes that were successfully committed.</returns>
    </member>
    <member name="P:System.Printing.PrintCommitAttributesException.FailedAttributesCollection">
      <summary>Gets a <see cref="T:System.Collections.ObjectModel.Collection`1" /> of the names of the attributes that were not committed.</summary>
      <returns>A <see cref="T:System.Collections.ObjectModel.Collection`1" /> of strings that identify the <see cref="T:System.Printing.PrintSystemObject" /> attributes that your program was unable to commit.</returns>
    </member>
    <member name="P:System.Printing.PrintCommitAttributesException.PrintObjectName">
      <summary>Gets the name of the object that threw the exception.</summary>
      <returns>A <see cref="T:System.String" /> reference to the <see cref="P:System.Printing.PrintSystemObject.Name" /> property of the <see cref="T:System.Printing.PrintSystemObject" /> that threw the exception.</returns>
    </member>
    <member name="T:System.Printing.PrintingCanceledException">
      <summary>The exception that occurs when code attempts to access a canceled print job.</summary>
    </member>
    <member name="M:System.Printing.PrintingCanceledException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintingCanceledException" /> class.</summary>
    </member>
    <member name="M:System.Printing.PrintingCanceledException.#ctor(System.Int32,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintingCanceledException" /> class that provides a specific error code and error condition.</summary>
      <param name="errorCode">An <see cref="T:System.Int32" /> that is interpreted as an HRESULT, a coded numerical value that is assigned to a specific exception.</param>
      <param name="message">A <see cref="T:System.String" /> that describes the error condition.</param>
    </member>
    <member name="M:System.Printing.PrintingCanceledException.#ctor(System.Int32,System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintingCanceledException" /> class that provides a specific error code and error condition, including the underlying cause of the exception.</summary>
      <param name="errorCode">An <see cref="T:System.Int32" /> that is interpreted as an HRESULT, a coded numerical value that is assigned to a specific exception.</param>
      <param name="message">A <see cref="T:System.String" /> that describes the error condition.</param>
      <param name="innerException">The underlying error condition that caused the <see cref="T:System.Printing.PrintingCanceledException" />.</param>
    </member>
    <member name="M:System.Printing.PrintingCanceledException.#ctor(System.Int32,System.String,System.String,System.String,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintingCanceledException" /> class that provides a specific error code, error condition, print queue name, job name, and job ID.</summary>
      <param name="errorCode">An <see cref="T:System.Int32" /> that is interpreted as an HRESULT, a coded numerical value that is assigned to a specific exception.</param>
      <param name="message">A <see cref="T:System.String" /> that describes the error condition.</param>
      <param name="printQueueName">The name of the print queue hosting the print job that caused the exception.</param>
      <param name="jobName">The name of the print job that caused the exception.</param>
      <param name="jobId">The ID number of the print job that caused the exception.</param>
    </member>
    <member name="M:System.Printing.PrintingCanceledException.#ctor(System.Int32,System.String,System.String,System.String,System.Int32,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintingCanceledException" /> class with a specific error code, error condition, print queue name, job name, job ID, and underlying cause of the exception.</summary>
      <param name="errorCode">An <see cref="T:System.Int32" /> that is interpreted as an HRESULT, a coded numerical value that is assigned to a specific exception.</param>
      <param name="message">A <see cref="T:System.String" /> that describes the error condition.</param>
      <param name="printQueueName">The name of the print queue hosting the print job that caused the exception.</param>
      <param name="jobName">The name of the print job that caused the exception.</param>
      <param name="jobId">The ID number of the print job that caused the exception.</param>
      <param name="innerException">The underlying cause of the exception.</param>
    </member>
    <member name="M:System.Printing.PrintingCanceledException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintingCanceledException" /> class that provides specific <see cref="T:System.Runtime.Serialization.SerializationInfo" /> and <see cref="T:System.Runtime.Serialization.StreamingContext" />. This constructor is protected.</summary>
      <param name="info">The data that is required to serialize or deserialize an object.</param>
      <param name="context">The context, including source and destination, of the serialized stream.</param>
    </member>
    <member name="M:System.Printing.PrintingCanceledException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintingCanceledException" /> class that provides a specific error condition.</summary>
      <param name="message">A <see cref="T:System.String" /> that describes the error condition.</param>
    </member>
    <member name="M:System.Printing.PrintingCanceledException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintingCanceledException" /> class that provides a specific error condition, including the underlying cause of the exception.</summary>
      <param name="message">A <see cref="T:System.String" /> that describes the error condition.</param>
      <param name="innerException">The underlying error condition that caused the <see cref="T:System.Printing.PrintingCanceledException" />.</param>
    </member>
    <member name="T:System.Printing.PrintingNotSupportedException">
      <summary>The exception that is thrown when a printing operation is not supported.</summary>
    </member>
    <member name="M:System.Printing.PrintingNotSupportedException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintingNotSupportedException" /> class with a system-supplied message that describes the error.</summary>
    </member>
    <member name="M:System.Printing.PrintingNotSupportedException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintingNotSupportedException" /> class with serialized data.</summary>
      <param name="info">The object that holds the serialized object data.</param>
      <param name="context">The contextual information about the source or destination.</param>
    </member>
    <member name="M:System.Printing.PrintingNotSupportedException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintingNotSupportedException" /> class with a specified message that describes the error.</summary>
      <param name="message">The message that describes the exception. The caller of this constructor is required to ensure that this string has been localized for the current system culture.</param>
    </member>
    <member name="M:System.Printing.PrintingNotSupportedException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintingNotSupportedException" /> class with a specified error message and a reference to the inner exception that is the cause of this exception.</summary>
      <param name="message">The message that describes the exception. The caller of this constructor is required to ensure that this string has been localized for the current system culture.</param>
      <param name="innerException">The exception that is the cause of the current exception. If the <paramref name="innerException" /> parameter is not <see langword="null" />, the current exception is raised in a <see langword="catch" /> block that handles the inner exception.</param>
    </member>
    <member name="M:System.Printing.PrintingNotSupportedException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintingNotSupportedException" /> class with serialized data.</summary>
      <param name="info">The object that holds the serialized object data.</param>
      <param name="context">The contextual information about the source or destination.</param>
    </member>
    <member name="T:System.Printing.PrintJobException">
      <summary>The exception that occurs when the print job does not run correctly.</summary>
    </member>
    <member name="M:System.Printing.PrintJobException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintJobException" /> class.</summary>
    </member>
    <member name="M:System.Printing.PrintJobException.#ctor(System.Int32,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintJobException" /> class that provides a specific error code and error condition.</summary>
      <param name="errorCode">An <see cref="T:System.Int32" /> that is interpreted as an HRESULT, a coded numerical value that is assigned to a specific exception.</param>
      <param name="message">A <see cref="T:System.String" /> that describes the error condition.</param>
    </member>
    <member name="M:System.Printing.PrintJobException.#ctor(System.Int32,System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintJobException" /> class that provides a specific error code and error condition, including the underlying cause of the exception.</summary>
      <param name="errorCode">An <see cref="T:System.Int32" /> that is interpreted as an HRESULT, a coded numerical value that is assigned to a specific exception.</param>
      <param name="message">A <see cref="T:System.String" /> that describes the error condition.</param>
      <param name="innerException">The underlying error that caused the <see cref="T:System.Printing.PrintJobException" />.</param>
    </member>
    <member name="M:System.Printing.PrintJobException.#ctor(System.Int32,System.String,System.String,System.String,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintJobException" /> class that provides a specific error code, error condition, print queue name, job name, and job ID.</summary>
      <param name="errorCode">An <see cref="T:System.Int32" /> that is interpreted as an HRESULT, a coded numerical value that is assigned to a specific exception.</param>
      <param name="message">A <see cref="T:System.String" /> that describes the error condition.</param>
      <param name="printQueueName">The name of the print queue hosting the print job that caused the exception.</param>
      <param name="jobName">The name of the print job that caused the exception.</param>
      <param name="jobId">The ID number of the print job that caused the exception.</param>
    </member>
    <member name="M:System.Printing.PrintJobException.#ctor(System.Int32,System.String,System.String,System.String,System.Int32,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintJobException" /> class that provides a specific message, error code, error condition, print queue name, job name, and job ID.</summary>
      <param name="errorCode">An <see cref="T:System.Int32" /> that is interpreted as an HRESULT, a coded numerical value that is assigned to a specific exception.</param>
      <param name="message">A <see cref="T:System.String" /> that describes the error condition.</param>
      <param name="printQueueName">The name of the print queue hosting the print job that caused the exception.</param>
      <param name="jobName">The name of the print job that caused the exception.</param>
      <param name="jobId">The ID number of the print job that caused the exception.</param>
      <param name="innerException">The underlying cause of the exception.</param>
    </member>
    <member name="M:System.Printing.PrintJobException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintJobException" /> class that provides specific <see cref="T:System.Runtime.Serialization.SerializationInfo" /> and <see cref="T:System.Runtime.Serialization.StreamingContext" />. This constructor is protected.</summary>
      <param name="info">The data that is required to serialize or deserialize an object.</param>
      <param name="context">The context, including source and destination, of the serialized stream.</param>
    </member>
    <member name="M:System.Printing.PrintJobException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintJobException" /> class that provides a specific error condition.</summary>
      <param name="message">A <see cref="T:System.String" /> that describes the error condition.</param>
    </member>
    <member name="M:System.Printing.PrintJobException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintJobException" /> class that provides a specific error condition, including the cause of the exception.</summary>
      <param name="message">A <see cref="T:System.String" /> that describes the error condition.</param>
      <param name="innerException">The underlying error that caused the <see cref="T:System.Printing.PrintJobException" />.</param>
    </member>
    <member name="M:System.Printing.PrintJobException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Gets the data that is used to serialize the object and gets the context of the serialized stream.</summary>
      <param name="info">The data that is required to serialize or deserialize an object.</param>
      <param name="context">The context, including source and destination, of the serialized stream.</param>
    </member>
    <member name="P:System.Printing.PrintJobException.JobId">
      <summary>Gets the ID number of the print job that caused the exception.</summary>
      <returns>An <see cref="T:System.Int32" /> that identifies the print job.</returns>
    </member>
    <member name="P:System.Printing.PrintJobException.JobName">
      <summary>Gets the name of the print job that caused the exception.</summary>
      <returns>A <see cref="T:System.String" /> that names the print job.</returns>
    </member>
    <member name="P:System.Printing.PrintJobException.PrintQueueName">
      <summary>Gets the name of the <see cref="T:System.Printing.PrintQueue" /> that was hosting the print job when the exception was thrown.</summary>
      <returns>A <see cref="T:System.String" /> that specifies the name of the <see cref="T:System.Printing.PrintQueue" />.</returns>
    </member>
    <member name="T:System.Printing.PrintQueueException">
      <summary>The exception that is thrown when an error condition prevents the accessing or creation of a <see cref="T:System.Printing.PrintQueue" />.</summary>
    </member>
    <member name="M:System.Printing.PrintQueueException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintQueueException" /> class.</summary>
    </member>
    <member name="M:System.Printing.PrintQueueException.#ctor(System.Int32,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintQueueException" /> class that provides a specific error code, error condition, and printer name.</summary>
      <param name="errorCode">An <see cref="T:System.Int32" /> that is interpreted as an HRESULT, a coded numerical value that is assigned to a specific exception.</param>
      <param name="message">A <see cref="T:System.String" /> that describes the error condition.</param>
      <param name="printerName">The name of the printer that was being accessed when the error occurred.</param>
    </member>
    <member name="M:System.Printing.PrintQueueException.#ctor(System.Int32,System.String,System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintQueueException" /> class that provides a specific printer name, error code, and error condition, including the underlying cause of the exception.</summary>
      <param name="errorCode">An <see cref="T:System.Int32" /> that is interpreted as an HRESULT, a coded numerical value that is assigned to a specific exception.</param>
      <param name="message">A <see cref="T:System.String" /> that describes the error condition.</param>
      <param name="printerName">The name of the printer that was being accessed when the error occurred.</param>
      <param name="innerException">The underlying error condition that caused the <see cref="T:System.Printing.PrintQueueException" />.</param>
    </member>
    <member name="M:System.Printing.PrintQueueException.#ctor(System.Int32,System.String,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintQueueException" /> class that provides a specific error code, error condition, printer name, and printer message.</summary>
      <param name="errorCode">An <see cref="T:System.Int32" /> that is interpreted as an HRESULT, a coded numerical value that is assigned to a specific exception.</param>
      <param name="message">A <see cref="T:System.String" /> that describes the error condition.</param>
      <param name="printerName">The name of the printer that was being accessed when the error occurred.</param>
      <param name="printerMessage">The exception message that was sent by the printer driver or unmanaged print system component that triggered the exception.</param>
    </member>
    <member name="M:System.Printing.PrintQueueException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintQueueException" /> class that provides specific <see cref="T:System.Runtime.Serialization.SerializationInfo" /> and <see cref="T:System.Runtime.Serialization.StreamingContext" />. This constructor is protected.</summary>
      <param name="info">The data that is required to serialize or deserialize an object.</param>
      <param name="context">The context, including source and destination, of the serialized stream.</param>
    </member>
    <member name="M:System.Printing.PrintQueueException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintQueueException" /> class that provides a specific error condition.</summary>
      <param name="message">A <see cref="T:System.String" /> that describes the error condition.</param>
    </member>
    <member name="M:System.Printing.PrintQueueException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintQueueException" /> class that provides a specific error condition, including the underlying cause of the exception.</summary>
      <param name="message">A <see cref="T:System.String" /> that describes the error condition.</param>
      <param name="innerException">The underlying error condition that caused the <see cref="T:System.Printing.PrintQueueException" />.</param>
    </member>
    <member name="M:System.Printing.PrintQueueException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Gets the data that is used to serialize the object and gets the context of the serialized stream.</summary>
      <param name="info">The data that is required to serialize or deserialize an object.</param>
      <param name="context">The context, including source and destination, of the serialized stream.</param>
    </member>
    <member name="P:System.Printing.PrintQueueException.PrinterName">
      <summary>Gets the name of the printer that was being accessed when the exception was thrown.</summary>
      <returns>A <see cref="T:System.String" /> that names the printer.</returns>
    </member>
    <member name="T:System.Printing.PrintServerException">
      <summary>The exception that occurs when an error condition prevents the accessing or creation of a <see cref="T:System.Printing.PrintServer" />.</summary>
    </member>
    <member name="M:System.Printing.PrintServerException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintServerException" /> class.</summary>
    </member>
    <member name="M:System.Printing.PrintServerException.#ctor(System.Int32,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintServerException" /> class that provides a specific error code, error condition, and print server name.</summary>
      <param name="errorCode">An <see cref="T:System.Int32" /> that is interpreted as an HRESULT, a coded numerical value that is assigned to a specific exception.</param>
      <param name="message">A <see cref="T:System.String" /> that describes the error condition.</param>
      <param name="serverName">The name of the print server that was being accessed when the error condition occurred.</param>
    </member>
    <member name="M:System.Printing.PrintServerException.#ctor(System.Int32,System.String,System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintServerException" /> class that provides a specific print server name, error code, and error condition, including the underlying cause of the exception.</summary>
      <param name="errorCode">An <see cref="T:System.Int32" /> that is interpreted as an HRESULT, a coded numerical value that is assigned to a specific exception.</param>
      <param name="message">A <see cref="T:System.String" /> that describes the error condition.</param>
      <param name="serverName">The name of the print server that was being accessed when the error condition occurred.</param>
      <param name="innerException">The underlying condition that caused the <see cref="T:System.Printing.PrintServerException" />.</param>
    </member>
    <member name="M:System.Printing.PrintServerException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintServerException" /> class that provides specific <see cref="T:System.Runtime.Serialization.SerializationInfo" /> and <see cref="T:System.Runtime.Serialization.StreamingContext" />. This constructor is protected.</summary>
      <param name="info">The data that is required to serialize or deserialize an object.</param>
      <param name="context">The context, including source and destination, of the serialized stream.</param>
    </member>
    <member name="M:System.Printing.PrintServerException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintServerException" /> class that provides a specific error condition.</summary>
      <param name="message">A <see cref="T:System.String" /> that describes the error condition.</param>
    </member>
    <member name="M:System.Printing.PrintServerException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintServerException" /> class that provides a specific error condition, including the underlying cause of the exception.</summary>
      <param name="message">A <see cref="T:System.String" /> that describes the error condition.</param>
      <param name="innerException">The underlying error condition that caused the <see cref="T:System.Printing.PrintServerException" />.</param>
    </member>
    <member name="M:System.Printing.PrintServerException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Gets the data that is used to serialize the object and gets the context of the serialized stream.</summary>
      <param name="info">The data that is used to serialize the object.</param>
      <param name="context">The context, including source and destination, of the serialized stream.</param>
    </member>
    <member name="P:System.Printing.PrintServerException.ServerName">
      <summary>Gets the name of the print server that was being accessed when the exception was thrown.</summary>
      <returns>A <see cref="T:System.String" /> that names the print server.</returns>
    </member>
    <member name="T:System.Printing.PrintSystemException">
      <summary>The exception that occurs when an error condition prevents accessing or creating a <see cref="T:System.Printing.PrintSystemObject" />.</summary>
    </member>
    <member name="M:System.Printing.PrintSystemException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintSystemException" /> class.</summary>
    </member>
    <member name="M:System.Printing.PrintSystemException.#ctor(System.Int32,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintSystemException" /> class that provides a specific error code and error message.</summary>
      <param name="errorCode">An <see cref="T:System.Int32" /> that is interpreted as an HRESULT, a coded numerical value that is assigned to a specific exception.</param>
      <param name="message">A string that describes the error condition.</param>
    </member>
    <member name="M:System.Printing.PrintSystemException.#ctor(System.Int32,System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintSystemException" /> class that provides a specific error code and error message, including the underlying cause of the exception.</summary>
      <param name="errorCode">An <see cref="T:System.Int32" /> that is interpreted as an HRESULT, a coded numerical value that is assigned to a specific exception.</param>
      <param name="message">A string that describes the error condition.</param>
      <param name="innerException">The underlying error condition that caused the exception.</param>
    </member>
    <member name="M:System.Printing.PrintSystemException.#ctor(System.Int32,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintSystemException" /> class that provides a specific error code, error message, and printer message.</summary>
      <param name="errorCode">An <see cref="T:System.Int32" /> that is interpreted as an HRESULT, a coded numerical value that is assigned to a specific exception.</param>
      <param name="message">A string that describes the error condition.</param>
      <param name="printerMessage">The exception message sent by the printer driver or unmanaged print system component that caused the exception.</param>
    </member>
    <member name="M:System.Printing.PrintSystemException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintSystemException" /> class that provides specific serialization information and streaming context.</summary>
      <param name="info">The data that is required to serialize or deserialize an object.</param>
      <param name="context">The context, including source and destination, of the serialized stream.</param>
    </member>
    <member name="M:System.Printing.PrintSystemException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintSystemException" /> class that provides a specific error message.</summary>
      <param name="message">A string that describes the error condition.</param>
    </member>
    <member name="M:System.Printing.PrintSystemException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintSystemException" /> class that provides a specific error message, including the underlying cause of the exception.</summary>
      <param name="message">A string that describes the error condition.</param>
      <param name="innerException">The underlying error condition that caused the exception.</param>
    </member>
    <member name="M:System.Printing.PrintSystemException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Gets the data that is used to serialize the object and gets the context of the serialized stream.</summary>
      <param name="info">The data that is required to serialize or deserialize an object.</param>
      <param name="context">The context, including source and destination, of the serialized stream.</param>
    </member>
    <member name="T:System.Printing.PrintTicket">
      <summary>Defines the settings of a print job.</summary>
    </member>
    <member name="E:System.Printing.PrintTicket.PropertyChanged">
      <summary>Occurs when any property of the <see cref="T:System.Printing.PrintTicket" /> changes.</summary>
    </member>
    <member name="M:System.Printing.PrintTicket.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintTicket" /> class.</summary>
    </member>
    <member name="M:System.Printing.PrintTicket.#ctor(System.IO.Stream)">
      <summary>Initializes a new instance of the <see cref="T:System.Printing.PrintTicket" /> class by using an XML stream (that contains a PrintTicket document) that complies with the XML Print Schema.</summary>
      <param name="xmlStream">An XML stream that describes a print job and conforms to the Print Schema.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="xmlStream" /> is <see langword="null" />.</exception>
      <exception cref="T:System.FormatException">
        <paramref name="xmlStream" /> is not valid XML.</exception>
    </member>
    <member name="M:System.Printing.PrintTicket.Clone">
      <summary>Creates a modifiable clone of this <see cref="T:System.Printing.PrintTicket" />, making deep copies of this object's values.</summary>
      <returns>A modifiable clone of the current object.</returns>
    </member>
    <member name="M:System.Printing.PrintTicket.GetXmlStream">
      <summary>Returns a <see cref="T:System.IO.MemoryStream" /> object that represents the property values of a <see cref="T:System.Printing.PrintTicket" /> as an XML stream that conforms to the Print Schema.</summary>
      <returns>A <see cref="T:System.IO.MemoryStream" /> object that describes the print ticket with XML that conforms to the Print Schema.</returns>
    </member>
    <member name="M:System.Printing.PrintTicket.SaveTo(System.IO.Stream)">
      <summary>Saves the <see cref="T:System.Printing.PrintTicket" /> settings to a <see cref="T:System.IO.Stream" /> object by using an XML format that conforms to the Print Schema.</summary>
      <param name="outStream">The <see cref="T:System.IO.Stream" /> that holds the saved <see cref="T:System.Printing.PrintTicket" />.</param>
    </member>
    <member name="P:System.Printing.PrintTicket.Collation">
      <summary>Gets or sets a value indicating whether the printer collates its output.</summary>
      <exception cref="T:System.ArgumentOutOfRangeException">Calling code has attempted to set the property to a value that is not in the <see cref="T:System.Printing.Collation" /> enumeration.</exception>
      <returns>A <see cref="T:System.Printing.Collation" /> value indicating whether the printer collates its output.</returns>
    </member>
    <member name="P:System.Printing.PrintTicket.CopyCount">
      <summary>Gets or sets the number of copies for the print job.</summary>
      <exception cref="T:System.ArgumentOutOfRangeException">Calling code has attempted to set the property to a value less than 1.</exception>
      <returns>A nullable <see cref="T:System.Int32" /> value that specifies how many copies to print.</returns>
    </member>
    <member name="P:System.Printing.PrintTicket.DeviceFontSubstitution">
      <summary>Gets or sets a value indicating whether the printer substitutes device-based fonts for computer-based fonts on the print job.</summary>
      <exception cref="T:System.ArgumentOutOfRangeException">Calling code has attempted to set the property to a value that is not in the <see cref="T:System.Printing.DeviceFontSubstitution" /> enumeration.</exception>
      <returns>A <see cref="T:System.Printing.DeviceFontSubstitution" /> value indicating whether the device substitutes device-based fonts for computer-based fonts for the current print job.</returns>
    </member>
    <member name="P:System.Printing.PrintTicket.Duplexing">
      <summary>Gets or sets a value indicating what kind of two-sided printing, if any, the printer uses for the print job.</summary>
      <exception cref="T:System.ArgumentOutOfRangeException">Calling code has attempted to set the property to a value that is not in the <see cref="T:System.Printing.Duplexing" /> enumeration.</exception>
      <returns>A <see cref="T:System.Printing.Duplexing" /> value indicating what sort of two-sided printing, if any, the printer uses for the print job.</returns>
    </member>
    <member name="P:System.Printing.PrintTicket.InputBin">
      <summary>Gets or sets a value indicating what input bin (paper tray) to use.</summary>
      <exception cref="T:System.ArgumentOutOfRangeException">Calling code has attempted to set the property to a value that is not in the <see cref="T:System.Printing.InputBin" /> enumeration.</exception>
      <returns>An <see cref="T:System.Printing.InputBin" /> value indicating what input tray is used and whether it is chosen manually or automatically.</returns>
    </member>
    <member name="P:System.Printing.PrintTicket.OutputColor">
      <summary>Gets or sets a value indicating how the printer handles content that has color or shades of gray.</summary>
      <exception cref="T:System.ArgumentOutOfRangeException">Calling code has attempted to set the property to a value that is not in the <see cref="T:System.Printing.OutputColor" /> enumeration.</exception>
      <returns>An <see cref="T:System.Printing.OutputColor" /> value indicating how the printer handles content that has color or shades of gray.</returns>
    </member>
    <member name="P:System.Printing.PrintTicket.OutputQuality">
      <summary>Gets or sets a value indicating the quality of output for the print job.</summary>
      <exception cref="T:System.ArgumentOutOfRangeException">Calling code has attempted to set the property to a value that is not in the <see cref="T:System.Printing.OutputQuality" /> enumeration.</exception>
      <returns>An <see cref="T:System.Printing.OutputQuality" /> value that specifies the needed level of quality.</returns>
    </member>
    <member name="P:System.Printing.PrintTicket.PageBorderless">
      <summary>Gets or sets a value indicating whether the device prints content to the edge of the media or leaves an unprinted margin around the edge.</summary>
      <exception cref="T:System.ArgumentOutOfRangeException">Calling code has attempted to set the property to a value that is not in the <see cref="T:System.Printing.PageBorderless" /> enumeration.</exception>
      <returns>A <see cref="T:System.Printing.PageBorderless" /> value that specifies whether the printer uses borderless printing.</returns>
    </member>
    <member name="P:System.Printing.PrintTicket.PageMediaSize">
      <summary>Gets or sets the page size for the paper (or other media) that a printer uses for a print job.</summary>
      <exception cref="T:System.ArgumentOutOfRangeException">Calling code has attempted to set the property with a <see cref="T:System.Printing.PageMediaSize" /> object that has at least one <see langword="null" /> property.  
  
-or-
  
 Calling code has attempted to set the property with a <see cref="T:System.Printing.PageMediaSize" /> object whose <see cref="P:System.Printing.PageMediaSize.PageMediaSizeName" /> property is set to a value that is not in the <see cref="T:System.Printing.PageMediaSizeName" /> enumeration.  
  
-or-
  
 Calling code has attempted to set the property with a <see cref="T:System.Printing.PageMediaSize" /> object whose <see cref="P:System.Printing.PageMediaSize.Width" /> or <see cref="P:System.Printing.PageMediaSize.Height" /> property is set to a value less than 1.</exception>
      <returns>A <see cref="T:System.Printing.PageMediaSize" /> object that represents the page size by using a name, dimensions, or both.</returns>
    </member>
    <member name="P:System.Printing.PrintTicket.PageMediaType">
      <summary>Gets or sets a value indicating what sort of paper or media the printer uses for the print job.</summary>
      <exception cref="T:System.ArgumentOutOfRangeException">Calling code has attempted to set the property to a value that is not in the <see cref="T:System.Printing.PageMediaType" /> enumeration.</exception>
      <returns>A <see cref="T:System.Printing.PageMediaType" /> value specifying the print media, such as card stock, label, plain, or photographic.</returns>
    </member>
    <member name="P:System.Printing.PrintTicket.PageOrder">
      <summary>Gets or sets a value indicating whether the printer prints multiple pages back-to-front or front-to-back.</summary>
      <exception cref="T:System.ArgumentOutOfRangeException">Calling code has attempted to set the property to a value that is not in the <see cref="T:System.Printing.PageOrder" /> enumeration.</exception>
      <returns>A <see cref="T:System.Printing.PageOrder" /> value specifying last-page-first printing or first-page-first printing.</returns>
    </member>
    <member name="P:System.Printing.PrintTicket.PageOrientation">
      <summary>Gets or sets a value indicating how the page content is oriented for printing.</summary>
      <exception cref="T:System.ArgumentOutOfRangeException">Calling code has attempted to set the property to a value that is not in the <see cref="T:System.Printing.PageOrientation" /> enumeration.</exception>
      <returns>A <see cref="T:System.Printing.PageOrientation" /> value specifying how page content is oriented, for example, <see cref="F:System.Printing.PageOrientation.Landscape" /> or <see cref="F:System.Printing.PageOrientation.ReversePortrait" />.</returns>
    </member>
    <member name="P:System.Printing.PrintTicket.PageResolution">
      <summary>Gets or sets the level of page resolution that the printer uses for a print job.</summary>
      <exception cref="T:System.ArgumentOutOfRangeException">Calling code has attempted to set the property with a <see cref="T:System.Printing.PageResolution" /> object that has at least one <see langword="null" /> property.  
  
-or-
  
 Calling code has attempted to set the property with a <see cref="T:System.Printing.PageMediaSize" /> object whose <see cref="P:System.Printing.PageResolution.QualitativeResolution" /> property is set to a value that is not in the <see cref="T:System.Printing.PageQualitativeResolution" /> enumeration.  
  
-or-
  
 Calling code has attempted to set the property with a <see cref="T:System.Printing.PageResolution" /> object whose <see cref="P:System.Printing.PageResolution.X" /> or <see cref="P:System.Printing.PageResolution.Y" /> property is set to a value less than 1.</exception>
      <returns>A <see cref="T:System.Printing.PageResolution" /> value that represents the resolution as a qualitative value, a dots-per-inch value, or both.</returns>
    </member>
    <member name="P:System.Printing.PrintTicket.PageScalingFactor">
      <summary>Gets or sets the percentage by which the printer enlarges or reduces the print image on a page.</summary>
      <exception cref="T:System.ArgumentOutOfRangeException">Calling code has attempted to set the property to a value that is less than 1.</exception>
      <returns>An <see cref="T:System.Int32" /> value that represents a percentage.</returns>
    </member>
    <member name="P:System.Printing.PrintTicket.PagesPerSheet">
      <summary>Gets or sets the number of pages that print on each printed side of a sheet of paper.</summary>
      <exception cref="T:System.ArgumentOutOfRangeException">Calling code has attempted to set the property to a value that is less than 1.</exception>
      <returns>A nullable <see cref="T:System.Int32" /> value that represents the number of pages that print on each printed side of a sheet of paper.</returns>
    </member>
    <member name="P:System.Printing.PrintTicket.PagesPerSheetDirection">
      <summary>Gets or sets a value indicating how a printer arranges multiple pages that print on each side of a sheet of paper.</summary>
      <exception cref="T:System.ArgumentOutOfRangeException">Calling code has attempted to set the property to a value that is not in the <see cref="T:System.Printing.PagesPerSheetDirection" /> enumeration.</exception>
      <returns>A <see cref="T:System.Printing.PagesPerSheetDirection" /> value indicating how a printer presents multiple pages per sheet.</returns>
    </member>
    <member name="P:System.Printing.PrintTicket.PhotoPrintingIntent">
      <summary>Gets or sets a value indicating in qualitative terms the level of quality the printer uses to print a photograph.</summary>
      <exception cref="T:System.ArgumentOutOfRangeException">Calling code has attempted to set the property to a value that is not in the <see cref="T:System.Printing.PhotoPrintingIntent" /> enumeration.</exception>
      <returns>A <see cref="T:System.Printing.PhotoPrintingIntent" /> value indicating the level of photo quality.</returns>
    </member>
    <member name="P:System.Printing.PrintTicket.Stapling">
      <summary>Gets or sets a value indicating whether, and where, a printer staples multiple pages.</summary>
      <exception cref="T:System.ArgumentOutOfRangeException">Calling code has attempted to set the property to a value that is not in the <see cref="T:System.Printing.Stapling" /> enumeration.</exception>
      <returns>A <see cref="T:System.Printing.Stapling" /> value specifying how a printer staples the output.</returns>
    </member>
    <member name="P:System.Printing.PrintTicket.TrueTypeFontMode">
      <summary>Gets or sets a value indicating how the printer handles text that uses TrueType fonts.</summary>
      <exception cref="T:System.ArgumentOutOfRangeException">Calling code has attempted to set the property to a value that is not in the <see cref="T:System.Printing.TrueTypeFontMode" /> enumeration.</exception>
      <returns>A <see cref="T:System.Printing.TrueTypeFontMode" /> value specifying how a printer handles TrueType fonts.</returns>
    </member>
    <member name="T:System.Printing.PrintTicketScope">
      <summary>Specifies whether a <see cref="T:System.Printing.PrintTicket" /> applies to an entire print job, one document within the print job, or just a page within the print job.</summary>
    </member>
    <member name="F:System.Printing.PrintTicketScope.DocumentScope">
      <summary>A single document.</summary>
    </member>
    <member name="F:System.Printing.PrintTicketScope.JobScope">
      <summary>An entire print job.</summary>
    </member>
    <member name="F:System.Printing.PrintTicketScope.PageScope">
      <summary>A single page.</summary>
    </member>
    <member name="T:System.Printing.Stapling">
      <summary>Specifies whether, and where, a printer staples a multi-page document.</summary>
    </member>
    <member name="F:System.Printing.Stapling.None">
      <summary>The document is not stapled.</summary>
    </member>
    <member name="F:System.Printing.Stapling.SaddleStitch">
      <summary>Multiple staples along the fold line. Also called saddle-stitch stapling.</summary>
    </member>
    <member name="F:System.Printing.Stapling.StapleBottomLeft">
      <summary>A single staple in the lower-left corner.</summary>
    </member>
    <member name="F:System.Printing.Stapling.StapleBottomRight">
      <summary>A single staple in the lower-right corner.</summary>
    </member>
    <member name="F:System.Printing.Stapling.StapleDualBottom">
      <summary>Two staples along the lower edge.</summary>
    </member>
    <member name="F:System.Printing.Stapling.StapleDualLeft">
      <summary>Two staples along the left edge.</summary>
    </member>
    <member name="F:System.Printing.Stapling.StapleDualRight">
      <summary>Two staples along the right edge.</summary>
    </member>
    <member name="F:System.Printing.Stapling.StapleDualTop">
      <summary>Two staples along the upper edge.</summary>
    </member>
    <member name="F:System.Printing.Stapling.StapleTopLeft">
      <summary>A single staple in the upper-left corner.</summary>
    </member>
    <member name="F:System.Printing.Stapling.StapleTopRight">
      <summary>A single staple in the upper-right corner.</summary>
    </member>
    <member name="F:System.Printing.Stapling.Unknown">
      <summary>The feature (whose options are represented by this enumeration) is set to an option not defined in the Print Schema.</summary>
    </member>
    <member name="T:System.Printing.TrueTypeFontMode">
      <summary>Specifies how a printer handles text that is formatted with a TrueType font.</summary>
    </member>
    <member name="F:System.Printing.TrueTypeFontMode.Automatic">
      <summary>The printer driver decides the best method for handling TrueType fonts.</summary>
    </member>
    <member name="F:System.Printing.TrueTypeFontMode.DownloadAsNativeTrueTypeFont">
      <summary>The printer driver downloads the TrueType font.</summary>
    </member>
    <member name="F:System.Printing.TrueTypeFontMode.DownloadAsOutlineFont">
      <summary>The printer driver downloads the TrueType font as an outline font.</summary>
    </member>
    <member name="F:System.Printing.TrueTypeFontMode.DownloadAsRasterFont">
      <summary>The printer driver creates a raster font for each size of the TrueType font that it needs and downloads them all.</summary>
    </member>
    <member name="F:System.Printing.TrueTypeFontMode.RenderAsBitmap">
      <summary>The printer driver downloads each area of text as a graphic.</summary>
    </member>
    <member name="F:System.Printing.TrueTypeFontMode.Unknown">
      <summary>The feature (whose options are represented by this enumeration) is set to an option not defined. in the Print Schema.</summary>
    </member>
    <member name="T:System.Printing.ValidationResult">
      <summary>Represents a merged <see cref="T:System.Printing.PrintTicket" /> that is guaranteed to be viable, with a report of any settings that were changed to make it viable.</summary>
    </member>
    <member name="M:System.Printing.ValidationResult.Equals(System.Object)">
      <summary>Determines whether the specified <see cref="T:System.Printing.ValidationResult" /> is equal to the current <see cref="T:System.Printing.ValidationResult" />.</summary>
      <param name="o">The <see cref="T:System.Printing.ValidationResult" /> to compare.</param>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Printing.ValidationResult" /> objects are equal; otherwise, <see langword="false" />. <see langword="false" /> is also returned if the object passed is not a <see cref="T:System.Printing.ValidationResult" />.</returns>
    </member>
    <member name="M:System.Printing.ValidationResult.GetHashCode">
      <summary>Gets the hash code associated with the <see cref="T:System.Printing.ValidationResult" /> and its <see cref="T:System.Printing.PrintTicket" /> and print stream.</summary>
      <returns>A hash code for the current <see cref="T:System.Printing.ValidationResult" />.</returns>
    </member>
    <member name="M:System.Printing.ValidationResult.op_Equality(System.Printing.ValidationResult,System.Printing.ValidationResult)">
      <summary>Determines if the specified <see cref="T:System.Printing.ValidationResult" /> objects are equal.</summary>
      <param name="a">The first <see cref="T:System.Printing.ValidationResult" /> to compare.</param>
      <param name="b">The second <see cref="T:System.Printing.ValidationResult" /> to compare.</param>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Printing.ValidationResult" /> objects are equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Printing.ValidationResult.op_Inequality(System.Printing.ValidationResult,System.Printing.ValidationResult)">
      <summary>Determines if the specified <see cref="T:System.Printing.ValidationResult" /> objects are not equal.</summary>
      <param name="a">The first <see cref="T:System.Printing.ValidationResult" /> to compare.</param>
      <param name="b">The second <see cref="T:System.Printing.ValidationResult" /> to compare.</param>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Printing.ValidationResult" /> objects are not equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Printing.ValidationResult.ConflictStatus">
      <summary>Gets a value indicating whether a conflict occurred between the functionality supported by the printer and the functionality requested in the initial merger of two source <see cref="T:System.Printing.PrintTicket" />s.</summary>
      <returns>One of the <see cref="T:System.Printing.ConflictStatus" /> values that indicates either that no conflict occurred or that at least one conflict occurred but was resolved. The default is <see cref="F:System.Printing.ConflictStatus.NoConflict" />.</returns>
    </member>
    <member name="P:System.Printing.ValidationResult.ValidatedPrintTicket">
      <summary>Gets a <see cref="T:System.Printing.PrintTicket" /> object that results from the merging of two <see cref="T:System.Printing.PrintTicket" /> objects, which might have been adjusted to ensure its viability.</summary>
      <returns>A valid and viable <see cref="T:System.Printing.PrintTicket" />. The default is <see langword="null" />.</returns>
    </member>
    <member name="T:System.Windows.Xps.Packaging.IDocumentStructureProvider">
      <summary>Defines a method for adding the <see langword="DocumentStructure" /> part of XML Paper Specification (XPS) to an XPS package.</summary>
    </member>
    <member name="M:System.Windows.Xps.Packaging.IDocumentStructureProvider.AddDocumentStructure">
      <summary>Adds the <see langword="DocumentStructure" /> part of XML Paper Specification (XPS) to an XPS package.</summary>
      <returns>An <see cref="T:System.Windows.Xps.Packaging.XpsStructure" /> that represents the <see langword="DocumentStructure" /> part of an XPS package.</returns>
    </member>
    <member name="T:System.Windows.Xps.Packaging.IStoryFragmentProvider">
      <summary>Defines a method for adding the <see cref="T:System.Windows.Documents.DocumentStructures.StoryFragments" /> part of the markup to an XPS package.</summary>
    </member>
    <member name="M:System.Windows.Xps.Packaging.IStoryFragmentProvider.AddStoryFragment">
      <summary>Adds a <see cref="T:System.Windows.Documents.DocumentStructures.StoryFragments" /> element to an XPS package.</summary>
      <returns>An <see cref="T:System.Windows.Xps.Packaging.XpsStructure" /> that represents a <see cref="T:System.Windows.Documents.DocumentStructures.StoryFragments" /> element in an XPS package.</returns>
    </member>
    <member name="T:System.Windows.Xps.Packaging.IXpsFixedDocumentReader">
      <summary>Defines methods for reading the parts of a <see cref="T:System.Windows.Documents.FixedDocument" /> and also for limited types of writing to the document.</summary>
    </member>
    <member name="M:System.Windows.Xps.Packaging.IXpsFixedDocumentReader.AddSignatureDefinition(System.Windows.Xps.Packaging.XpsSignatureDefinition)">
      <summary>Adds the specified <see cref="T:System.Windows.Xps.Packaging.XpsSignatureDefinition" /> to the <see cref="T:System.Windows.Documents.FixedDocument" />.</summary>
      <param name="signatureDefinition">The definition that is added.</param>
    </member>
    <member name="M:System.Windows.Xps.Packaging.IXpsFixedDocumentReader.CommitSignatureDefinition">
      <summary>Flushes the package <see cref="T:System.IO.Stream" /> and also commits an <see cref="T:System.Windows.Xps.Packaging.XpsSignatureDefinition" /> to the package.</summary>
    </member>
    <member name="M:System.Windows.Xps.Packaging.IXpsFixedDocumentReader.GetFixedPage(System.Uri)">
      <summary>Gets a reader for the <see cref="T:System.Windows.Documents.FixedPage" /> with the specified uniform resource identifier (URI).</summary>
      <param name="pageSource">The URI of the page.</param>
      <returns>An <see cref="T:System.Windows.Xps.Packaging.IXpsFixedPageReader" /> for the page.</returns>
    </member>
    <member name="M:System.Windows.Xps.Packaging.IXpsFixedDocumentReader.RemoveSignatureDefinition(System.Windows.Xps.Packaging.XpsSignatureDefinition)">
      <summary>Removes the specified <see cref="T:System.Windows.Xps.Packaging.XpsSignatureDefinition" /> from the <see cref="T:System.Windows.Documents.FixedDocument" />.</summary>
      <param name="signatureDefinition">The definition that is removed.</param>
    </member>
    <member name="P:System.Windows.Xps.Packaging.IXpsFixedDocumentReader.DocumentNumber">
      <summary>Gets the zero-based position of the document in the <see cref="T:System.Windows.Documents.FixedDocumentSequence" />.</summary>
      <returns>An <see cref="T:System.Int32" /> that represents the document location in the sequence.</returns>
    </member>
    <member name="P:System.Windows.Xps.Packaging.IXpsFixedDocumentReader.DocumentStructure">
      <summary>Gets the <see langword="DocumentStructure" /> part, if one exists, of the <see cref="T:System.Windows.Documents.FixedDocument" />.</summary>
      <returns>An <see cref="T:System.Windows.Xps.Packaging.XpsStructure" /> that represents the <see langword="DocumentStructure" /> element, if one exists, and its child elements.</returns>
    </member>
    <member name="P:System.Windows.Xps.Packaging.IXpsFixedDocumentReader.FixedPages">
      <summary>Gets a collection of <see cref="T:System.Windows.Documents.FixedPage" /> readers, one reader for each page in the <see cref="T:System.Windows.Documents.FixedDocument" />.</summary>
      <returns>A collection of fixed page readers, one reader for each page in the <see cref="T:System.Windows.Documents.FixedDocument" />.</returns>
    </member>
    <member name="P:System.Windows.Xps.Packaging.IXpsFixedDocumentReader.PrintTicket">
      <summary>Gets the <see cref="T:System.Printing.PrintTicket" />, if one exists, that is associated with the <see cref="T:System.Windows.Documents.FixedDocument" />.</summary>
      <returns>A <see cref="T:System.Printing.PrintTicket" /> that contains the default printing options for the document; or <see langword="null" /> if no print ticket exists for the document.</returns>
    </member>
    <member name="P:System.Windows.Xps.Packaging.IXpsFixedDocumentReader.SignatureDefinitions">
      <summary>Gets a collection of all the signature definitions that are associated with the <see cref="T:System.Windows.Documents.FixedDocument" />.</summary>
      <returns>An <see cref="T:System.Collections.Generic.ICollection`1" /> of signature definitions, typically one for every person who signed or will sign the document.</returns>
    </member>
    <member name="P:System.Windows.Xps.Packaging.IXpsFixedDocumentReader.Thumbnail">
      <summary>Gets the thumbnail image, if a thumbnail exists, that is associated with the <see cref="T:System.Windows.Documents.FixedDocument" />.</summary>
      <returns>An <see cref="T:System.Windows.Xps.Packaging.XpsThumbnail" /> that represents the image.</returns>
    </member>
    <member name="P:System.Windows.Xps.Packaging.IXpsFixedDocumentReader.Uri">
      <summary>Gets the uniform resource identifier (URI) of the <see cref="T:System.Windows.Documents.FixedDocument" />.</summary>
      <returns>A <see cref="T:System.Uri" /> that represents the URI for the document.</returns>
    </member>
    <member name="T:System.Windows.Xps.Packaging.IXpsFixedDocumentSequenceReader">
      <summary>Defines methods for reading the parts of a <see cref="T:System.Windows.Documents.FixedDocumentSequence" />.</summary>
    </member>
    <member name="M:System.Windows.Xps.Packaging.IXpsFixedDocumentSequenceReader.GetFixedDocument(System.Uri)">
      <summary>Gets a reader for the <see cref="T:System.Windows.Documents.FixedDocument" /> with the specified uniform resource identifier (URI).</summary>
      <param name="documentSource">The URI of the fixed document.</param>
    </member>
    <member name="P:System.Windows.Xps.Packaging.IXpsFixedDocumentSequenceReader.FixedDocuments">
      <summary>Gets a collection of <see cref="T:System.Windows.Documents.FixedDocument" /> readers for each document in a <see cref="T:System.Windows.Documents.FixedDocumentSequence" />.</summary>
      <returns>A collection of <see cref="T:System.Windows.Documents.FixedDocument" /> readers, one for each document in a <see cref="T:System.Windows.Documents.FixedDocumentSequence" />.</returns>
    </member>
    <member name="P:System.Windows.Xps.Packaging.IXpsFixedDocumentSequenceReader.PrintTicket">
      <summary>Gets the <see cref="T:System.Printing.PrintTicket" />, if one exists, that is associated with the <see cref="T:System.Windows.Documents.FixedDocument" />.</summary>
      <returns>A <see cref="T:System.Printing.PrintTicket" /> that contains the default printing options for the sequence; or <see langword="null" />, if no print ticket exists for the document.</returns>
    </member>
    <member name="P:System.Windows.Xps.Packaging.IXpsFixedDocumentSequenceReader.Thumbnail">
      <summary>Gets the thumbnail image, if one exists, that is associated with the <see cref="T:System.Windows.Documents.FixedDocumentSequence" />.</summary>
      <returns>An <see cref="T:System.Windows.Xps.Packaging.XpsThumbnail" /> that represents the image.</returns>
    </member>
    <member name="P:System.Windows.Xps.Packaging.IXpsFixedDocumentSequenceReader.Uri">
      <summary>Gets the uniform resource identifier (URI) of the <see cref="T:System.Windows.Documents.FixedDocumentSequence" />.</summary>
      <returns>A <see cref="T:System.Uri" /> that represents the URI for the sequence.</returns>
    </member>
    <member name="T:System.Windows.Xps.Packaging.IXpsFixedDocumentSequenceWriter">
      <summary>Defines methods and properties for writing a <see cref="T:System.Windows.Documents.FixedDocumentSequence" />.</summary>
    </member>
    <member name="M:System.Windows.Xps.Packaging.IXpsFixedDocumentSequenceWriter.AddFixedDocument">
      <summary>Adds a new <see cref="T:System.Windows.Documents.FixedDocument" /> to the <see cref="T:System.Windows.Documents.FixedDocumentSequence" />.</summary>
      <returns>A writer for outputting the new <see cref="T:System.Windows.Documents.FixedDocument" />.</returns>
    </member>
    <member name="M:System.Windows.Xps.Packaging.IXpsFixedDocumentSequenceWriter.AddThumbnail(System.Windows.Xps.Packaging.XpsImageType)">
      <summary>Adds an <see cref="T:System.Windows.Xps.Packaging.XpsThumbnail" /> image for the <see cref="T:System.Windows.Documents.FixedDocumentSequence" />.</summary>
      <param name="imageType">The image type to write.</param>
      <returns>The new <see cref="T:System.Windows.Xps.Packaging.XpsThumbnail" /> for the <see cref="T:System.Windows.Documents.FixedDocumentSequence" />.</returns>
    </member>
    <member name="M:System.Windows.Xps.Packaging.IXpsFixedDocumentSequenceWriter.Commit">
      <summary>Flushes and closes the <see cref="T:System.Windows.Documents.FixedDocumentSequence" /> writer.</summary>
    </member>
    <member name="P:System.Windows.Xps.Packaging.IXpsFixedDocumentSequenceWriter.PrintTicket">
      <summary>Sets a <see cref="T:System.Printing.PrintTicket" /> for the <see cref="T:System.Windows.Documents.FixedDocumentSequence" /> that is being written.</summary>
      <returns>A <see cref="T:System.Printing.PrintTicket" /> that represents a default printing configuration for the sequence.</returns>
    </member>
    <member name="P:System.Windows.Xps.Packaging.IXpsFixedDocumentSequenceWriter.Uri">
      <summary>Gets the URI of the <see cref="T:System.Windows.Documents.FixedDocumentSequence" /> that is being written.</summary>
      <returns>The URI of the <see cref="T:System.Windows.Documents.FixedDocumentSequence" /> that is being written.</returns>
    </member>
    <member name="T:System.Windows.Xps.Packaging.IXpsFixedDocumentWriter">
      <summary>Defines methods and properties for writing a <see cref="T:System.Windows.Documents.FixedDocument" />.</summary>
    </member>
    <member name="M:System.Windows.Xps.Packaging.IXpsFixedDocumentWriter.AddFixedPage">
      <summary>Adds a new <see cref="T:System.Windows.Documents.FixedPage" /> to the <see cref="T:System.Windows.Documents.FixedDocument" />.</summary>
      <returns>A writer for outputting the new <see cref="T:System.Windows.Documents.FixedPage" />.</returns>
    </member>
    <member name="M:System.Windows.Xps.Packaging.IXpsFixedDocumentWriter.AddThumbnail(System.Windows.Xps.Packaging.XpsImageType)">
      <summary>Adds an <see cref="T:System.Windows.Xps.Packaging.XpsThumbnail" /> image for the <see cref="T:System.Windows.Documents.FixedDocument" />.</summary>
      <param name="imageType">The image type of the thumbnail to add.</param>
      <returns>The new <see cref="T:System.Windows.Xps.Packaging.XpsThumbnail" /> for the <see cref="T:System.Windows.Documents.FixedDocument" />.</returns>
    </member>
    <member name="M:System.Windows.Xps.Packaging.IXpsFixedDocumentWriter.Commit">
      <summary>Flushes and closes the <see cref="T:System.Windows.Documents.FixedDocument" /> writer.</summary>
    </member>
    <member name="P:System.Windows.Xps.Packaging.IXpsFixedDocumentWriter.DocumentNumber">
      <summary>Gets the zero-based position of the <see cref="T:System.Windows.Documents.FixedDocument" /> in the <see cref="T:System.Windows.Documents.FixedDocumentSequence" />.</summary>
      <returns>The zero-based position of the <see cref="T:System.Windows.Documents.FixedDocument" /> in the <see cref="T:System.Windows.Documents.FixedDocumentSequence" />.</returns>
    </member>
    <member name="P:System.Windows.Xps.Packaging.IXpsFixedDocumentWriter.PrintTicket">
      <summary>Sets default print options for the <see cref="T:System.Windows.Documents.FixedDocument" />.</summary>
    </member>
    <member name="P:System.Windows.Xps.Packaging.IXpsFixedDocumentWriter.Uri">
      <summary>Gets the URI of the <see cref="T:System.Windows.Documents.FixedDocument" />.</summary>
      <returns>The URI of the <see cref="T:System.Windows.Documents.FixedDocument" />.</returns>
    </member>
    <member name="T:System.Windows.Xps.Packaging.IXpsFixedPageReader">
      <summary>Defines methods for reading the parts of a <see cref="T:System.Windows.Documents.FixedPage" />.</summary>
    </member>
    <member name="M:System.Windows.Xps.Packaging.IXpsFixedPageReader.GetColorContext(System.Uri)">
      <summary>Gets the color context for the resource that has the specified uniform resource identifier (URI).</summary>
      <param name="uri">The URI of the resource.</param>
      <returns>An <see cref="T:System.Windows.Xps.Packaging.XpsColorContext" />.</returns>
    </member>
    <member name="M:System.Windows.Xps.Packaging.IXpsFixedPageReader.GetFont(System.Uri)">
      <summary>Gets the font that has the specified uniform resource identifier (URI).</summary>
      <param name="uri">The URI of the font.</param>
      <returns>The font that has the specified Uri.</returns>
    </member>
    <member name="M:System.Windows.Xps.Packaging.IXpsFixedPageReader.GetImage(System.Uri)">
      <summary>Gets the image that has the specified uniform resource identifier (URI).</summary>
      <param name="uri">The URI of the image.</param>
      <returns>The image that has the specified Uri.</returns>
    </member>
    <member name="M:System.Windows.Xps.Packaging.IXpsFixedPageReader.GetResource(System.Uri)">
      <summary>Gets the resource that has the specified uniform resource identifier (URI).</summary>
      <param name="resourceUri">The URI of the resource.</param>
      <returns>The resource that has the specified Uri.</returns>
    </member>
    <member name="M:System.Windows.Xps.Packaging.IXpsFixedPageReader.GetResourceDictionary(System.Uri)">
      <summary>Gets the resource dictionary that has the specified uniform resource identifier (URI).</summary>
      <param name="uri">The URI of the resource dictionary.</param>
      <returns>The resource dictionary that has the specified Uri.</returns>
    </member>
    <member name="P:System.Windows.Xps.Packaging.IXpsFixedPageReader.ColorContexts">
      <summary>Gets a collection of all the color contexts on the page.</summary>
      <returns>An <see cref="T:System.Collections.Generic.ICollection`1" /> that includes every <see cref="T:System.Windows.Xps.Packaging.XpsColorContext" /> that is on the page.</returns>
    </member>
    <member name="P:System.Windows.Xps.Packaging.IXpsFixedPageReader.Fonts">
      <summary>Gets a collection of all the fonts that are used on the page.</summary>
      <returns>An <see cref="T:System.Collections.Generic.ICollection`1" /> that includes every <see cref="T:System.Windows.Xps.Packaging.XpsFont" /> that is on the page.</returns>
    </member>
    <member name="P:System.Windows.Xps.Packaging.IXpsFixedPageReader.Images">
      <summary>Gets a collection of all the images on the page.</summary>
      <returns>An <see cref="T:System.Collections.Generic.ICollection`1" /> that includes every <see cref="T:System.Windows.Xps.Packaging.XpsImage" /> that is on the page.</returns>
    </member>
    <member name="P:System.Windows.Xps.Packaging.IXpsFixedPageReader.PageNumber">
      <summary>Gets the page number.</summary>
      <returns>An <see cref="T:System.Int32" /> that represents the page number.</returns>
    </member>
    <member name="P:System.Windows.Xps.Packaging.IXpsFixedPageReader.PrintTicket">
      <summary>Gets the <see cref="T:System.Printing.PrintTicket" />, if a ticket exists, that is associated with the <see cref="T:System.Windows.Documents.FixedPage" />.</summary>
      <returns>A <see cref="T:System.Printing.PrintTicket" /> that contains the default printing options for the page; or <see langword="null" /> if no <see cref="T:System.Printing.PrintTicket" /> exists for the page.</returns>
    </member>
    <member name="P:System.Windows.Xps.Packaging.IXpsFixedPageReader.ResourceDictionaries">
      <summary>Gets a collection of all the resource dictionaries for the page.</summary>
      <returns>An <see cref="T:System.Collections.Generic.ICollection`1" /> of <see cref="T:System.Windows.Xps.Packaging.XpsResourceDictionary" /> objects.</returns>
    </member>
    <member name="P:System.Windows.Xps.Packaging.IXpsFixedPageReader.StoryFragment">
      <summary>Gets the <see cref="T:System.Windows.Documents.DocumentStructures.StoryFragments" /> part of the markup of an XPS package.</summary>
      <returns>An <see cref="T:System.Windows.Xps.Packaging.XpsStructure" /> that represents a <see cref="T:System.Windows.Documents.DocumentStructures.StoryFragments" /> element in an XPS package.</returns>
    </member>
    <member name="P:System.Windows.Xps.Packaging.IXpsFixedPageReader.Thumbnail">
      <summary>Gets the thumbnail image, if a thumbnail exists, that is associated with the <see cref="T:System.Windows.Documents.FixedPage" />.</summary>
      <returns>An <see cref="T:System.Windows.Xps.Packaging.XpsThumbnail" /> that represents the image.</returns>
    </member>
    <member name="P:System.Windows.Xps.Packaging.IXpsFixedPageReader.Uri">
      <summary>Gets the uniform resource identifier (URI) of the <see cref="T:System.Windows.Documents.FixedPage" />.</summary>
      <returns>A <see cref="T:System.Uri" /> that represents the URI for the page.</returns>
    </member>
    <member name="P:System.Windows.Xps.Packaging.IXpsFixedPageReader.XmlReader">
      <summary>Gets an <see cref="T:System.Xml.XmlReader" /> for the page.</summary>
      <returns>An <see cref="T:System.Xml.XmlReader" /> for the page.</returns>
    </member>
    <member name="T:System.Windows.Xps.Packaging.IXpsFixedPageWriter">
      <summary>Defines methods for writing <see cref="T:System.Windows.Documents.FixedPage" /> parts to an XPS document.</summary>
    </member>
    <member name="M:System.Windows.Xps.Packaging.IXpsFixedPageWriter.AddColorContext">
      <summary>Adds a new <see cref="T:System.Windows.Xps.Packaging.XpsColorContext" /> to the current page.</summary>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="M:System.IDisposable.Dispose" /> has been called.</exception>
      <returns>The new color context resource that was added.</returns>
    </member>
    <member name="M:System.Windows.Xps.Packaging.IXpsFixedPageWriter.AddFont">
      <summary>Adds a new <see cref="T:System.Windows.Xps.Packaging.XpsFont" /> to the current page.</summary>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="M:System.IDisposable.Dispose" /> has been called.</exception>
      <returns>The new font resource that was added.</returns>
    </member>
    <member name="M:System.Windows.Xps.Packaging.IXpsFixedPageWriter.AddFont(System.Boolean)">
      <summary>Adds a new obfuscated <see cref="T:System.Windows.Xps.Packaging.XpsFont" /> to the current page.</summary>
      <param name="obfuscate">
        <see langword="true" /> to obfuscate the font; otherwise, <see langword="false" />.</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="M:System.IDisposable.Dispose" /> has been called.</exception>
      <returns>The new font resource that was added.</returns>
    </member>
    <member name="M:System.Windows.Xps.Packaging.IXpsFixedPageWriter.AddFont(System.Boolean,System.Boolean)">
      <summary>Adds a new obfuscated or restricted <see cref="T:System.Windows.Xps.Packaging.XpsFont" /> to the current page.</summary>
      <param name="obfuscate">
        <see langword="true" /> to obfuscate the font; otherwise, <see langword="false" />.</param>
      <param name="addRestrictedRelationship">
        <see langword="true" /> to prevent modification of any text that uses the font; otherwise, <see langword="false" />.</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="M:System.IDisposable.Dispose" /> has been called.</exception>
      <returns>The new font resource that was added.</returns>
    </member>
    <member name="M:System.Windows.Xps.Packaging.IXpsFixedPageWriter.AddImage(System.String)">
      <summary>Adds a new <see cref="T:System.Windows.Xps.Packaging.XpsImage" /> with a specified MIME type to the current page.</summary>
      <param name="mimeType">The MIME type of the image to add.</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="M:System.IDisposable.Dispose" /> has been called.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="mimeType" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="mimeType" /> is an empty string.</exception>
      <returns>The new image resource that was added to the page.</returns>
    </member>
    <member name="M:System.Windows.Xps.Packaging.IXpsFixedPageWriter.AddImage(System.Windows.Xps.Packaging.XpsImageType)">
      <summary>Adds a new <see cref="T:System.Windows.Xps.Packaging.XpsImage" /> with a specified <see cref="T:System.Windows.Xps.Packaging.XpsImageType" /> to the current page.</summary>
      <param name="imageType">The type of image to add to the page.</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="M:System.IDisposable.Dispose" /> has been called.</exception>
      <returns>The new image resource that was added to the page.</returns>
    </member>
    <member name="M:System.Windows.Xps.Packaging.IXpsFixedPageWriter.AddResource(System.Type,System.Uri)">
      <summary>Adds a new <see cref="T:System.Windows.Xps.Packaging.XpsResource" /> to the current page.</summary>
      <param name="resourceType">The type of resource to add.</param>
      <param name="resourceUri">The absolute path of the resource to add, or <see langword="null" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="resourceType" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="M:System.IDisposable.Dispose" /> has been called.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="resourceType" /> is an empty string.</exception>
      <returns>The new XML Paper Specification (XPS) resource that was added.</returns>
    </member>
    <member name="M:System.Windows.Xps.Packaging.IXpsFixedPageWriter.AddResourceDictionary">
      <summary>Adds a <see cref="T:System.Windows.Xps.Packaging.XpsResourceDictionary" /> to the page.</summary>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="M:System.IDisposable.Dispose" /> has been called.</exception>
      <returns>The <see cref="T:System.Windows.Xps.Packaging.XpsResourceDictionary" /> that was added to the page.</returns>
    </member>
    <member name="M:System.Windows.Xps.Packaging.IXpsFixedPageWriter.AddThumbnail(System.Windows.Xps.Packaging.XpsImageType)">
      <summary>Adds a thumbnail image of a specified <see cref="T:System.Windows.Xps.Packaging.XpsImageType" /> to the current page.</summary>
      <param name="imageType">The image type of the thumbnail image to add to the page.</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Windows.Xps.Packaging.IXpsFixedPageWriter.AddThumbnail(System.Windows.Xps.Packaging.XpsImageType)" /> has already been called to add an image thumbnail for this page.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="M:System.IDisposable.Dispose" /> has been called.</exception>
      <returns>The new thumbnail image resource that was added.</returns>
    </member>
    <member name="M:System.Windows.Xps.Packaging.IXpsFixedPageWriter.Commit">
      <summary>Flushes and closes the fixed-page writer.</summary>
    </member>
    <member name="P:System.Windows.Xps.Packaging.IXpsFixedPageWriter.LinkTargetStream">
      <summary>Gets a list of strings that identify the <see cref="T:System.Windows.Documents.LinkTarget" /> hyperlink points that are contained in the current page.</summary>
      <returns>A list of strings that identify the <see cref="T:System.Windows.Documents.LinkTarget" /> hyperlink points that are contained in the page.</returns>
    </member>
    <member name="P:System.Windows.Xps.Packaging.IXpsFixedPageWriter.PageNumber">
      <summary>Gets the zero-based page number of this page.</summary>
      <returns>The zero-based page number of this page.</returns>
    </member>
    <member name="P:System.Windows.Xps.Packaging.IXpsFixedPageWriter.PrintTicket">
      <summary>Sets the default printing options for the page.</summary>
    </member>
    <member name="P:System.Windows.Xps.Packaging.IXpsFixedPageWriter.Uri">
      <summary>Gets the URI of the fixed page.</summary>
      <returns>The URI of the fixed page.</returns>
    </member>
    <member name="P:System.Windows.Xps.Packaging.IXpsFixedPageWriter.XmlWriter">
      <summary>Gets an <see cref="T:System.Xml.XmlWriter" /> for writing XML markup to the page.</summary>
      <returns>The <see cref="T:System.Xml.XmlWriter" /> for writing XML markup to the page.</returns>
    </member>
    <member name="T:System.Windows.Xps.Packaging.PackageInterleavingOrder">
      <summary>Provides values that specify the order in which the major parts of a package are streamed.</summary>
    </member>
    <member name="F:System.Windows.Xps.Packaging.PackageInterleavingOrder.ImagesLast">
      <summary>The streaming order is: resource (other than images), page, document, document sequence, and images.</summary>
    </member>
    <member name="F:System.Windows.Xps.Packaging.PackageInterleavingOrder.None">
      <summary>The streaming order is specified by the packaging system.</summary>
    </member>
    <member name="F:System.Windows.Xps.Packaging.PackageInterleavingOrder.ResourceFirst">
      <summary>The streaming order is: resource, page, document, and document sequence.</summary>
    </member>
    <member name="F:System.Windows.Xps.Packaging.PackageInterleavingOrder.ResourceLast">
      <summary>The streaming order is: document sequence, document, page, and resource.</summary>
    </member>
    <member name="T:System.Windows.Xps.Packaging.PackagingAction">
      <summary>Identifies the types of events that occur during the serialization of a package.</summary>
    </member>
    <member name="F:System.Windows.Xps.Packaging.PackagingAction.AddingDocumentSequence">
      <summary>A document sequence is being added.</summary>
    </member>
    <member name="F:System.Windows.Xps.Packaging.PackagingAction.AddingFixedDocument">
      <summary>A document is being added.</summary>
    </member>
    <member name="F:System.Windows.Xps.Packaging.PackagingAction.AddingFixedPage">
      <summary>A page is being added.</summary>
    </member>
    <member name="F:System.Windows.Xps.Packaging.PackagingAction.DocumentSequenceCompleted">
      <summary>A document sequence has been added, but not necessarily committed.</summary>
    </member>
    <member name="F:System.Windows.Xps.Packaging.PackagingAction.FixedDocumentCompleted">
      <summary>A document has been added, but not necessarily committed.</summary>
    </member>
    <member name="F:System.Windows.Xps.Packaging.PackagingAction.FixedPageCompleted">
      <summary>A page has been added, but not necessarily committed.</summary>
    </member>
    <member name="F:System.Windows.Xps.Packaging.PackagingAction.FontAdded">
      <summary>A font has been added, but not necessarily committed.</summary>
    </member>
    <member name="F:System.Windows.Xps.Packaging.PackagingAction.ImageAdded">
      <summary>An image has been added, but not necessarily committed.</summary>
    </member>
    <member name="F:System.Windows.Xps.Packaging.PackagingAction.None">
      <summary>No action has been taken.</summary>
    </member>
    <member name="F:System.Windows.Xps.Packaging.PackagingAction.ResourceAdded">
      <summary>A resource has been added, but not necessarily committed.</summary>
    </member>
    <member name="F:System.Windows.Xps.Packaging.PackagingAction.XpsDocumentCommitted">
      <summary>The document is committed.</summary>
    </member>
    <member name="T:System.Windows.Xps.Packaging.PackagingProgressEventArgs">
      <summary>Provides data for the <see cref="E:System.Windows.Xps.Serialization.XpsPackagingPolicy.PackagingProgressEvent" /> event.</summary>
    </member>
    <member name="M:System.Windows.Xps.Packaging.PackagingProgressEventArgs.#ctor(System.Windows.Xps.Packaging.PackagingAction,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Xps.Packaging.PackagingProgressEventArgs" /> class.</summary>
      <param name="action">The action that the packaging process is currently performing.</param>
      <param name="numberCompleted">The number of simultaneous times that the specified <paramref name="action" /> occurred.</param>
    </member>
    <member name="P:System.Windows.Xps.Packaging.PackagingProgressEventArgs.Action">
      <summary>Gets a value that indicates what action is currently occurring in the packaging process.</summary>
      <returns>A <see cref="T:System.Windows.Xps.Packaging.PackagingAction" /> that represents the part of the packaging process that is currently occurring.</returns>
    </member>
    <member name="P:System.Windows.Xps.Packaging.PackagingProgressEventArgs.NumberCompleted">
      <summary>Gets the number of simultaneous times that the action that is identified in <see cref="P:System.Windows.Xps.Packaging.PackagingProgressEventArgs.Action" /> has occurred.</summary>
      <returns>An <see cref="T:System.Int32" /> that represents the number of simultaneous times that the action that is identified in <see cref="P:System.Windows.Xps.Packaging.PackagingProgressEventArgs.Action" /> has occurred.</returns>
    </member>
    <member name="T:System.Windows.Xps.Packaging.PackagingProgressEventHandler">
      <summary>Represents the method that handles the <see cref="E:System.Windows.Xps.Serialization.XpsPackagingPolicy.PackagingProgressEvent" />.</summary>
      <param name="sender">The source of the event.</param>
      <param name="e">The event data.</param>
    </member>
    <member name="T:System.Windows.Xps.Packaging.SpotLocation">
      <summary>Represents the location to display a digital signature on an <see cref="T:System.Windows.Xps.Packaging.XpsDocument" />.</summary>
    </member>
    <member name="M:System.Windows.Xps.Packaging.SpotLocation.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Xps.Packaging.SpotLocation" /> class.</summary>
    </member>
    <member name="P:System.Windows.Xps.Packaging.SpotLocation.PageUri">
      <summary>Gets or sets the URI of the page on which to display the digital signature.</summary>
      <returns>The URI of the page on which to display the signature.</returns>
    </member>
    <member name="P:System.Windows.Xps.Packaging.SpotLocation.StartX">
      <summary>Gets or sets the X page coordinate for the digital signature.</summary>
      <returns>The X page coordinate for the digital signature.</returns>
    </member>
    <member name="P:System.Windows.Xps.Packaging.SpotLocation.StartY">
      <summary>Gets or sets the Y page coordinate for the digital signature.</summary>
      <returns>The Y page coordinate for the digital signature.</returns>
    </member>
    <member name="T:System.Windows.Xps.Packaging.XpsColorContext">
      <summary>Represents the color context for a bitmap image.</summary>
    </member>
    <member name="T:System.Windows.Xps.Packaging.XpsDigitalSignature">
      <summary>Represents a digital signature for an <see cref="T:System.Windows.Xps.Packaging.XpsDocument" />.</summary>
    </member>
    <member name="M:System.Windows.Xps.Packaging.XpsDigitalSignature.#ctor(System.IO.Packaging.PackageDigitalSignature,System.Windows.Xps.Packaging.XpsDocument)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Xps.Packaging.XpsDigitalSignature" /> class.</summary>
      <param name="packageSignature">The associated digital signature for the package.</param>
      <param name="package">The associated package.</param>
    </member>
    <member name="M:System.Windows.Xps.Packaging.XpsDigitalSignature.Verify">
      <summary>Verifies the document digital signature against the X.509 certificate embedded in the XPS <see cref="T:System.IO.Packaging.Package" />.</summary>
      <returns>One of the <see cref="T:System.IO.Packaging.VerifyResult" /> enumeration values that specify the result of the verification attempt.</returns>
    </member>
    <member name="M:System.Windows.Xps.Packaging.XpsDigitalSignature.Verify(System.Security.Cryptography.X509Certificates.X509Certificate)">
      <summary>Verifies the document digital signature against a specified X.509 certificate.</summary>
      <param name="certificate">The certificate of authenticity for the signer.</param>
      <returns>One of the <see cref="T:System.IO.Packaging.VerifyResult" /> enumeration values that specify the result of the verification attempt.</returns>
    </member>
    <member name="M:System.Windows.Xps.Packaging.XpsDigitalSignature.VerifyCertificate">
      <summary>Verifies the X.509 certificate embedded in the package.</summary>
      <returns>A bitwise combination of the enumeration values that specify the result of the verification attempt.</returns>
    </member>
    <member name="M:System.Windows.Xps.Packaging.XpsDigitalSignature.VerifyCertificate(System.Security.Cryptography.X509Certificates.X509Certificate)">
      <summary>Verifies the specified X.509 certificate.</summary>
      <param name="certificate">A certificate of authenticity.</param>
      <returns>A bitwise combination of the enumeration values that specify the result of the verification attempt.</returns>
    </member>
    <member name="P:System.Windows.Xps.Packaging.XpsDigitalSignature.DocumentPropertiesRestricted">
      <summary>Gets a value that indicates whether changing the properties of the document invalidates the digital signature.</summary>
      <returns>
        <see langword="true" /> if changing the properties of the document invalidates the signature; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Windows.Xps.Packaging.XpsDigitalSignature.Id">
      <summary>Gets the globally unique identifier (GUID) of the signature.</summary>
      <returns>The GUID of the signature.</returns>
    </member>
    <member name="P:System.Windows.Xps.Packaging.XpsDigitalSignature.IsCertificateAvailable">
      <summary>Gets a value that indicates whether the signer's X.509 certificate is contained in the document <see cref="T:System.IO.Packaging.Package" />.</summary>
      <returns>
        <see langword="true" /> if the X.509 certificate of the signer is embedded in the document <see cref="T:System.IO.Packaging.Package" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Windows.Xps.Packaging.XpsDigitalSignature.SignatureOriginRestricted">
      <summary>Gets a value that indicates whether the digital signature is invalidated if someone else signs the package.</summary>
      <returns>
        <see langword="true" /> if the signature is invalidated if an additional digital signature is applied; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Windows.Xps.Packaging.XpsDigitalSignature.SignatureType">
      <summary>Gets a URL string that identifies the signature type.</summary>
      <returns>A URL string that identifies the signature type. The default signature type is <see cref="F:System.Security.Cryptography.Xml.SignedXml.XmlDsigC14NTransformUrl" />.</returns>
    </member>
    <member name="P:System.Windows.Xps.Packaging.XpsDigitalSignature.SignatureValue">
      <summary>Gets the encrypted hash value of the signature.</summary>
      <returns>A <see cref="T:System.Byte" /> array that contains the encrypted hash value of the signature.</returns>
    </member>
    <member name="P:System.Windows.Xps.Packaging.XpsDigitalSignature.SignedDocumentSequence">
      <summary>Gets the document sequence reader for the signed document sequence.</summary>
      <returns>An <see cref="T:System.Windows.Xps.Packaging.IXpsFixedDocumentSequenceReader" />; or <see langword="null" /> if not all valid XPS parts have been signed in the document.</returns>
    </member>
    <member name="P:System.Windows.Xps.Packaging.XpsDigitalSignature.SignerCertificate">
      <summary>Gets the X.509 certificate of the signer.</summary>
      <returns>The X.509 certificate of the signer; or <see langword="null" /> when there is no certificate embedded in the document package.</returns>
    </member>
    <member name="P:System.Windows.Xps.Packaging.XpsDigitalSignature.SigningTime">
      <summary>Gets the date and time when the signature was created.</summary>
      <returns>The date and time when the pages, document, or document sequence was signed.</returns>
    </member>
    <member name="T:System.Windows.Xps.Packaging.XpsDigSigPartAlteringRestrictions">
      <summary>Specifies the parts of the XPS <see cref="T:System.IO.Packaging.Package" /> that are excluded from the range of a digital signature.</summary>
    </member>
    <member name="F:System.Windows.Xps.Packaging.XpsDigSigPartAlteringRestrictions.Annotations">
      <summary>The Annotations part is excluded.</summary>
    </member>
    <member name="F:System.Windows.Xps.Packaging.XpsDigSigPartAlteringRestrictions.CoreMetadata">
      <summary>The Core Properties part is excluded.</summary>
    </member>
    <member name="F:System.Windows.Xps.Packaging.XpsDigSigPartAlteringRestrictions.None">
      <summary>No parts are excluded.</summary>
    </member>
    <member name="F:System.Windows.Xps.Packaging.XpsDigSigPartAlteringRestrictions.SignatureOrigin">
      <summary>The Signature Origin part is excluded.</summary>
    </member>
    <member name="T:System.Windows.Xps.Packaging.XpsDocument">
      <summary>Provides a <see cref="T:System.IO.Packaging.Package" /> that holds the content of an XPS document.</summary>
    </member>
    <member name="M:System.Windows.Xps.Packaging.XpsDocument.#ctor(System.IO.Packaging.Package)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /> class with access to a specified XML Paper Specification (XPS) <see cref="T:System.IO.Packaging.Package" /> and default interleaving, resource, and compression options.</summary>
      <param name="package">The target XPS package for the <see cref="T:System.Windows.Xps.Packaging.XpsDocument" />.</param>
    </member>
    <member name="M:System.Windows.Xps.Packaging.XpsDocument.#ctor(System.IO.Packaging.Package,System.IO.Packaging.CompressionOption)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /> class that is contained in a specified <see cref="T:System.IO.Packaging.Package" /> with specified default interleaving, resource, and compression options.</summary>
      <param name="package">The target package for the <see cref="T:System.Windows.Xps.Packaging.XpsDocument" />.</param>
      <param name="compressionOption">The package compression option.</param>
    </member>
    <member name="M:System.Windows.Xps.Packaging.XpsDocument.#ctor(System.IO.Packaging.Package,System.IO.Packaging.CompressionOption,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /> class that is contained in a specified <see cref="T:System.IO.Packaging.Package" /> with the specified default interleaving, resource, and compression options.</summary>
      <param name="package">The target package for the <see cref="T:System.Windows.Xps.Packaging.XpsDocument" />.</param>
      <param name="compressionOption">The package compression option.</param>
      <param name="path">The uniform resource identifier (URI) for the document as a string.</param>
    </member>
    <member name="M:System.Windows.Xps.Packaging.XpsDocument.#ctor(System.String,System.IO.FileAccess)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /> class that is contained in a specified <see cref="T:System.IO.Packaging.Package" /> file with default interleaving, resource, and compression options.</summary>
      <param name="path">The path and file name of the target <see cref="T:System.IO.Packaging.Package" /> for the <see cref="T:System.Windows.Xps.Packaging.XpsDocument" />.</param>
      <param name="packageAccess">The file I/O mode in which to open the <see cref="T:System.Windows.Xps.Packaging.XpsDocument" />.</param>
    </member>
    <member name="M:System.Windows.Xps.Packaging.XpsDocument.#ctor(System.String,System.IO.FileAccess,System.IO.Packaging.CompressionOption)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /> class that is contained in a specified <see cref="T:System.IO.Packaging.Package" /> file with default interleaving, resource, and compression options.</summary>
      <param name="path">The path and file name of the target <see cref="T:System.IO.Packaging.Package" /> for the <see cref="T:System.Windows.Xps.Packaging.XpsDocument" />.</param>
      <param name="packageAccess">The file I/O mode in which to open the <see cref="T:System.Windows.Xps.Packaging.XpsDocument" />.</param>
      <param name="compressionOption">The package compression option.</param>
    </member>
    <member name="M:System.Windows.Xps.Packaging.XpsDocument.AddFixedDocumentSequence">
      <summary>Adds a root <see cref="T:System.Windows.Documents.FixedDocumentSequence" /> to the package and returns a writer.</summary>
      <returns>The XML Paper Specification (XPS) fixed-document sequence writer for this <see cref="T:System.Windows.Xps.Packaging.XpsDocument" />.</returns>
    </member>
    <member name="M:System.Windows.Xps.Packaging.XpsDocument.AddThumbnail(System.Windows.Xps.Packaging.XpsImageType)">
      <summary>Adds a thumbnail image to the package.</summary>
      <param name="imageType">The format of the image.</param>
      <returns>The newly added <see cref="T:System.Windows.Xps.Packaging.XpsThumbnail" />.</returns>
    </member>
    <member name="M:System.Windows.Xps.Packaging.XpsDocument.Close">
      <summary>Closes the XPS document <see cref="T:System.IO.Packaging.Package" />.</summary>
    </member>
    <member name="M:System.Windows.Xps.Packaging.XpsDocument.CreateXpsDocumentWriter(System.Windows.Xps.Packaging.XpsDocument)">
      <summary>Creates an <see cref="T:System.Windows.Xps.XpsDocumentWriter" /> for writing an <see cref="T:System.Windows.Xps.Packaging.XpsDocument" />.</summary>
      <param name="xpsDocument">The XPS document to write.</param>
      <returns>The <see cref="T:System.Windows.Xps.XpsDocumentWriter" /> to use for writing the XML Paper Specification (XPS) document.</returns>
    </member>
    <member name="M:System.Windows.Xps.Packaging.XpsDocument.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources that are used by the <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /> and optionally, releases the managed resources.</summary>
      <param name="disposing">
        <see langword="true" /> to release both managed and unmanaged resources; <see langword="false" /> to release only unmanaged resources.</param>
    </member>
    <member name="M:System.Windows.Xps.Packaging.XpsDocument.GetFixedDocumentSequence">
      <summary>Returns the fixed-document sequence at the root of the package.</summary>
      <returns>The <see cref="T:System.Windows.Documents.FixedDocumentSequence" /> of the package.</returns>
    </member>
    <member name="M:System.Windows.Xps.Packaging.XpsDocument.RemoveSignature(System.Windows.Xps.Packaging.XpsDigitalSignature)">
      <summary>Deletes a signature from the package.</summary>
      <param name="signature">The signature that is deleted.</param>
    </member>
    <member name="M:System.Windows.Xps.Packaging.XpsDocument.SignDigitally(System.Security.Cryptography.X509Certificates.X509Certificate,System.Boolean,System.Windows.Xps.Packaging.XpsDigSigPartAlteringRestrictions)">
      <summary>Signs a collection of package parts with a specified X.509 certificate.</summary>
      <param name="certificate">The X.509 certificate to use in signing each part; or <see langword="null" /> to prompt the user to select an installed certificate.</param>
      <param name="embedCertificate">
        <see langword="true" /> to store the certificate in the package; otherwise, <see langword="false" />.</param>
      <param name="restrictions">Flags that indicate what dependent parts must be excluded from the signing.</param>
      <returns>An <see cref="T:System.Windows.Xps.Packaging.XpsDigitalSignature" /> that contains information about the signature.</returns>
    </member>
    <member name="M:System.Windows.Xps.Packaging.XpsDocument.SignDigitally(System.Security.Cryptography.X509Certificates.X509Certificate,System.Boolean,System.Windows.Xps.Packaging.XpsDigSigPartAlteringRestrictions,System.Guid)">
      <summary>Signs a collection of package parts by using a specified X.509 certificate.</summary>
      <param name="certificate">The X.509 certificate to use in signing each part; or <see langword="null" /> to prompt the user to select an installed certificate.</param>
      <param name="embedCertificate">
        <see langword="true" /> to store the certificate in the package; otherwise, <see langword="false" />.</param>
      <param name="restrictions">Flags that indicate what dependent parts are excluded from the signing.</param>
      <param name="id">The ID to assign to the signature.</param>
      <returns>An <see cref="T:System.Windows.Xps.Packaging.XpsDigitalSignature" /> that contains information about the signature.</returns>
    </member>
    <member name="M:System.Windows.Xps.Packaging.XpsDocument.SignDigitally(System.Security.Cryptography.X509Certificates.X509Certificate,System.Boolean,System.Windows.Xps.Packaging.XpsDigSigPartAlteringRestrictions,System.Guid,System.Boolean)">
      <summary>Signs a collection of package parts with a specified X.509 certificate.</summary>
      <param name="certificate">The X.509 certificate to use in signing each part; or <see langword="null" /> to prompt the user to select an installed certificate.</param>
      <param name="embedCertificate">
        <see langword="true" /> to store the certificate in the package; otherwise, <see langword="false" />.</param>
      <param name="restrictions">Flags that indicate what dependent parts to exclude from the signing.</param>
      <param name="id">The ID to assign to the signature.</param>
      <param name="testIsSignable">
        <see langword="true" /> to verify that <see cref="P:System.Windows.Xps.Packaging.XpsDocument.IsSignable" /> is <see langword="true" /> before signing; otherwise, <see langword="false" />.</param>
      <returns>An <see cref="T:System.Windows.Xps.Packaging.XpsDigitalSignature" /> that contains information about the signature.</returns>
    </member>
    <member name="M:System.Windows.Xps.Packaging.XpsDocument.System#IDisposable#Dispose">
      <summary>This member supports the Windows Presentation Foundation (WPF) infrastructure and is not intended to be used directly from your code. Use the type-safe <see cref="M:System.Windows.Xps.Packaging.XpsDocument.Dispose(System.Boolean)" /> method instead.</summary>
    </member>
    <member name="P:System.Windows.Xps.Packaging.XpsDocument.CoreDocumentProperties">
      <summary>Gets the core <see cref="T:System.IO.Packaging.PackageProperties" /> of the XPS document.</summary>
      <returns>The core properties of the <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /><see cref="T:System.IO.Packaging.Package" />.</returns>
    </member>
    <member name="P:System.Windows.Xps.Packaging.XpsDocument.FixedDocumentSequenceReader">
      <summary>Gets an <see cref="T:System.Windows.Xps.Packaging.IXpsFixedDocumentSequenceReader" /> for reading the document.</summary>
      <returns>An <see cref="T:System.Windows.Xps.Packaging.IXpsFixedDocumentSequenceReader" /> for reading the document.</returns>
    </member>
    <member name="P:System.Windows.Xps.Packaging.XpsDocument.IsReader">
      <summary>Gets a value that indicates whether the package is readable.</summary>
      <returns>
        <see langword="true" /> if the package is readable; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Windows.Xps.Packaging.XpsDocument.IsSignable">
      <summary>Gets a value that indicates whether the package can be digitally signed.</summary>
      <returns>
        <see langword="true" /> if the package can be signed; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Windows.Xps.Packaging.XpsDocument.IsWriter">
      <summary>Gets a value that indicates whether the package is writable.</summary>
      <returns>
        <see langword="true" /> if the package is writable; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Windows.Xps.Packaging.XpsDocument.Signatures">
      <summary>Gets a collection of XML Paper Specification (XPS) signatures that are associated with the package.</summary>
      <returns>A <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" /> of <see cref="T:System.Windows.Xps.Packaging.XpsDigitalSignature" /> objects that represent the signatures that are associated with the package.</returns>
    </member>
    <member name="P:System.Windows.Xps.Packaging.XpsDocument.Thumbnail">
      <summary>Gets or sets the XML Paper Specification (XPS) thumbnail image that is associated with the document.</summary>
      <returns>An <see cref="T:System.Windows.Xps.Packaging.XpsThumbnail" /> that represents the thumbnail image that is associated with the document.</returns>
    </member>
    <member name="T:System.Windows.Xps.Packaging.XpsFont">
      <summary>Represents a font in an <see cref="T:System.Windows.Xps.Packaging.XpsDocument" />.</summary>
    </member>
    <member name="M:System.Windows.Xps.Packaging.XpsFont.ObfuscateFontData(System.Byte[],System.Guid)">
      <summary>Obfuscates the font typeface data.</summary>
      <param name="fontData">The typeface data to obfuscate.</param>
      <param name="guid">The globally unique identifier (GUID) to use to obfuscate the <paramref name="fontData" />.</param>
    </member>
    <member name="P:System.Windows.Xps.Packaging.XpsFont.IsObfuscated">
      <summary>Gets a value that indicates whether the font is obfuscated.</summary>
      <returns>
        <see langword="true" /> if the font is obfuscated; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Windows.Xps.Packaging.XpsFont.IsRestricted">
      <summary>Gets or sets a value that indicates whether text that uses this font can be modified.</summary>
      <returns>
        <see langword="true" /> if text that uses this font can be changed or edited; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Windows.Xps.Packaging.XpsImage">
      <summary>Represents an image in an <see cref="T:System.Windows.Xps.Packaging.XpsDocument" />.</summary>
    </member>
    <member name="T:System.Windows.Xps.Packaging.XpsImageType">
      <summary>Specifies graphical formats for images that can be included in an <see cref="T:System.Windows.Xps.Packaging.XpsDocument" />.</summary>
    </member>
    <member name="F:System.Windows.Xps.Packaging.XpsImageType.JpegImageType">
      <summary>JPEG.</summary>
    </member>
    <member name="F:System.Windows.Xps.Packaging.XpsImageType.PngImageType">
      <summary>PNG.</summary>
    </member>
    <member name="F:System.Windows.Xps.Packaging.XpsImageType.TiffImageType">
      <summary>TIFF</summary>
    </member>
    <member name="F:System.Windows.Xps.Packaging.XpsImageType.WdpImageType">
      <summary>WDP.</summary>
    </member>
    <member name="T:System.Windows.Xps.Packaging.XpsPartBase">
      <summary>Defines the abstract class that is the parent for all part classes that can be contained in an XPS package.</summary>
    </member>
    <member name="P:System.Windows.Xps.Packaging.XpsPartBase.Uri">
      <summary>Gets or sets the uniform resource identifier (URI) of the part.</summary>
      <returns>The <see cref="T:System.Uri" /> for the part.</returns>
    </member>
    <member name="T:System.Windows.Xps.Packaging.XpsResource">
      <summary>Defines the base class for resources that can be added to an <see cref="T:System.Windows.Xps.Packaging.XpsDocument" />.</summary>
    </member>
    <member name="M:System.Windows.Xps.Packaging.XpsResource.Commit">
      <summary>Commits all changes and flushes the resource to the document package.</summary>
    </member>
    <member name="M:System.Windows.Xps.Packaging.XpsResource.GetStream">
      <summary>When overridden in a derived class, returns the I/O stream for reading or writing the resource.</summary>
      <returns>The <see cref="T:System.IO.Stream" /> for reading or writing the resource.</returns>
    </member>
    <member name="M:System.Windows.Xps.Packaging.XpsResource.RelativeUri(System.Uri)">
      <summary>Returns the URI of the resource that is relative to a specified absolute URI.</summary>
      <param name="inUri">A starting absolute URI.</param>
      <returns>The <see cref="T:System.Uri" /> of the resource that is relative to the specified absolute <paramref name="inUri" />.</returns>
    </member>
    <member name="M:System.Windows.Xps.Packaging.XpsResource.System#IDisposable#Dispose">
      <summary>This member supports the Windows Presentation Foundation infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Windows.Xps.Packaging.XpsResourceDictionary">
      <summary>Represents a dictionary of <see cref="T:System.Windows.Xps.Packaging.XpsResource" /> elements that are usable across pages of the <see cref="T:System.Windows.Xps.Packaging.XpsDocument" />.</summary>
    </member>
    <member name="T:System.Windows.Xps.Packaging.XpsResourceSharing">
      <summary>Specifies whether resources can be shared between pages and documents in an <see cref="T:System.Windows.Xps.Packaging.XpsDocument" />.</summary>
    </member>
    <member name="F:System.Windows.Xps.Packaging.XpsResourceSharing.NoResourceSharing">
      <summary>The resources can not be shared.</summary>
    </member>
    <member name="F:System.Windows.Xps.Packaging.XpsResourceSharing.ShareResources">
      <summary>The resources can be shared.</summary>
    </member>
    <member name="T:System.Windows.Xps.Packaging.XpsSignatureDefinition">
      <summary>Represents an <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /> digital signature.</summary>
    </member>
    <member name="M:System.Windows.Xps.Packaging.XpsSignatureDefinition.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Xps.Packaging.XpsSignatureDefinition" /> class.</summary>
    </member>
    <member name="P:System.Windows.Xps.Packaging.XpsSignatureDefinition.Culture">
      <summary>Gets or sets the <see cref="T:System.Globalization.CultureInfo" /> of the signature.</summary>
      <returns>The cultural information of the signature.</returns>
    </member>
    <member name="P:System.Windows.Xps.Packaging.XpsSignatureDefinition.HasBeenModified">
      <summary>Gets or sets a value that indicates whether unwritten property changes exist for the <see cref="T:System.Windows.Xps.Packaging.XpsSignatureDefinition" /> class and must be written to the package's stream.</summary>
      <returns>
        <see langword="true" /> if uncommitted changes exist; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Windows.Xps.Packaging.XpsSignatureDefinition.Intent">
      <summary>Gets or sets the string value of the signature intention agreement that the signer is signing against.</summary>
      <returns>A <see cref="T:System.String" /> that represents the intention agreement; for example, "I have read and agree." The default value is <see langword="null" />.</returns>
    </member>
    <member name="P:System.Windows.Xps.Packaging.XpsSignatureDefinition.RequestedSigner">
      <summary>Gets or sets the identity of the person who is requested to sign (or has signed) the package.</summary>
      <returns>A <see cref="T:System.String" /> that represents the signer, for example, "Mary Jones, Marketing Dept." The default value is <see langword="null" />.</returns>
    </member>
    <member name="P:System.Windows.Xps.Packaging.XpsSignatureDefinition.SignBy">
      <summary>Gets or sets the date and time by which the requested signer must sign the parts of the specified document.</summary>
      <returns>A (<see cref="T:System.Nullable" />) <see cref="T:System.DateTime" /> that represents the deadline by which the signer must sign the package. The default value is <see langword="null" />.</returns>
    </member>
    <member name="P:System.Windows.Xps.Packaging.XpsSignatureDefinition.SigningLocale">
      <summary>Gets or sets the legal jurisdiction where the package is signed.</summary>
      <returns>A <see cref="T:System.String" /> that represents the jurisdiction, for example, "State of Utah, United States." The default value is <see langword="null" />.</returns>
    </member>
    <member name="P:System.Windows.Xps.Packaging.XpsSignatureDefinition.SpotId">
      <summary>Gets or sets a unique identifier for this <see cref="T:System.Windows.Xps.Packaging.XpsSignatureDefinition" />.</summary>
      <returns>A (<see cref="T:System.Nullable" />) <see cref="T:System.Guid" /> that represents the ID of the signature definition. The default value is <see langword="null" />.</returns>
    </member>
    <member name="P:System.Windows.Xps.Packaging.XpsSignatureDefinition.SpotLocation">
      <summary>Gets or sets the location that specifies where to display the visible digital signature in an XML Paper Specification (XPS) document.</summary>
      <returns>A <see cref="T:System.Windows.Xps.Packaging.SpotLocation" /> that represents the location in the package where a visible request for a signature appears. The default value is <see langword="null" />.</returns>
    </member>
    <member name="T:System.Windows.Xps.Packaging.XpsStructure">
      <summary>Represents the <see cref="N:System.Windows.Documents.DocumentStructures" /> or <see cref="T:System.Windows.Documents.DocumentStructures.StoryFragments" /> element of an <see cref="T:System.Windows.Xps.Packaging.XpsDocument" />.</summary>
    </member>
    <member name="T:System.Windows.Xps.Packaging.XpsThumbnail">
      <summary>Represents a thumbnail image of a document sequence, single document, or single page.</summary>
    </member>
    <member name="T:System.Windows.Xps.Serialization.BasePackagingPolicy">
      <summary>Defines the base class for XPS package serialization policies. This class is abstract.</summary>
    </member>
    <member name="M:System.Windows.Xps.Serialization.BasePackagingPolicy.#ctor">
      <summary>When overridden in a derived class, initializes a new instance of the <see cref="T:System.Windows.Xps.Serialization.BasePackagingPolicy" /> class.</summary>
    </member>
    <member name="M:System.Windows.Xps.Serialization.BasePackagingPolicy.AcquireResourceStreamForXpsColorContext(System.String)">
      <summary>When overridden in a derived class, acquires a resource stream for an <see cref="T:System.Windows.Xps.Packaging.XpsColorContext" /> object.</summary>
      <param name="resourceId">The ID of the object.</param>
      <returns>The <see cref="T:System.Windows.Xps.Serialization.XpsResourceStream" /> for the <see cref="T:System.Windows.Xps.Packaging.XpsColorContext" />.</returns>
    </member>
    <member name="M:System.Windows.Xps.Serialization.BasePackagingPolicy.AcquireResourceStreamForXpsFont">
      <summary>When overridden in a derived class, acquires a resource stream for an <see cref="T:System.Windows.Xps.Packaging.XpsFont" /> object.</summary>
      <returns>An <see cref="T:System.Windows.Xps.Serialization.XpsResourceStream" /> for the <see cref="T:System.Windows.Xps.Packaging.XpsFont" />.</returns>
    </member>
    <member name="M:System.Windows.Xps.Serialization.BasePackagingPolicy.AcquireResourceStreamForXpsFont(System.String)">
      <summary>When overridden in a derived class, acquires an <see cref="T:System.Windows.Xps.Serialization.XpsResourceStream" /> for an <see cref="T:System.Windows.Xps.Packaging.XpsFont" /> with a specified resource ID.</summary>
      <param name="resourceId">The resource ID of the <see cref="T:System.Windows.Xps.Packaging.XpsFont" /> resource stream to acquire.</param>
      <returns>The XML Paper Specification (XPS) resource stream for the <see cref="T:System.Windows.Xps.Packaging.XpsFont" /> with the specified <paramref name="resourceID" />.</returns>
    </member>
    <member name="M:System.Windows.Xps.Serialization.BasePackagingPolicy.AcquireResourceStreamForXpsImage(System.String)">
      <summary>When overridden in a derived class, acquires a resource stream for an <see cref="T:System.Windows.Xps.Packaging.XpsImage" /> object.</summary>
      <param name="resourceId">The ID of the object.</param>
      <returns>The <see cref="T:System.Windows.Xps.Serialization.XpsResourceStream" /> for the <see cref="T:System.Windows.Xps.Packaging.XpsImage" />.</returns>
    </member>
    <member name="M:System.Windows.Xps.Serialization.BasePackagingPolicy.AcquireResourceStreamForXpsResourceDictionary(System.String)">
      <summary>When overridden in a derived class, acquires a resource stream for an <see cref="T:System.Windows.Xps.Packaging.XpsResourceDictionary" /> object.</summary>
      <param name="resourceId">The ID of the object.</param>
      <returns>The <see cref="T:System.Windows.Xps.Serialization.XpsResourceStream" /> for the <see cref="T:System.Windows.Xps.Packaging.XpsResourceDictionary" />.</returns>
    </member>
    <member name="M:System.Windows.Xps.Serialization.BasePackagingPolicy.AcquireStreamForLinkTargets">
      <summary>When overridden in a derived class, gets a list of strings, each expressing a <see cref="T:System.Windows.Documents.LinkTarget" /> element.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IList`1" /> of <see cref="T:System.String" /> objects that represent the linkable targets for a page.</returns>
    </member>
    <member name="M:System.Windows.Xps.Serialization.BasePackagingPolicy.AcquireXmlWriterForFixedDocument">
      <summary>When overridden in a derived class, returns an <see cref="T:System.Xml.XmlWriter" /> for a <see cref="T:System.Windows.Documents.FixedDocument" />.</summary>
      <returns>The <see cref="T:System.Xml.XmlWriter" /> for the <see cref="T:System.Windows.Documents.FixedDocument" />.</returns>
    </member>
    <member name="M:System.Windows.Xps.Serialization.BasePackagingPolicy.AcquireXmlWriterForFixedDocumentSequence">
      <summary>When overridden in a derived class, returns an <see cref="T:System.Xml.XmlWriter" /> for a <see cref="T:System.Windows.Documents.FixedDocumentSequence" />.</summary>
      <returns>The <see cref="T:System.Xml.XmlWriter" /> for a <see cref="T:System.Windows.Documents.FixedDocumentSequence" />.</returns>
    </member>
    <member name="M:System.Windows.Xps.Serialization.BasePackagingPolicy.AcquireXmlWriterForFixedPage">
      <summary>When overridden in a derived class, returns an <see cref="T:System.Xml.XmlWriter" /> for a <see cref="T:System.Windows.Documents.FixedPage" />.</summary>
      <returns>The <see cref="T:System.Xml.XmlWriter" /> for a <see cref="T:System.Windows.Documents.FixedPage" />.</returns>
    </member>
    <member name="M:System.Windows.Xps.Serialization.BasePackagingPolicy.AcquireXmlWriterForPage">
      <summary>When overridden in a derived class, returns an <see cref="T:System.Xml.XmlWriter" /> for the current page.</summary>
      <returns>The <see cref="T:System.Xml.XmlWriter" /> for the current page.</returns>
    </member>
    <member name="M:System.Windows.Xps.Serialization.BasePackagingPolicy.AcquireXmlWriterForResourceDictionary">
      <summary>When overridden in a derived class, returns an <see cref="T:System.Xml.XmlWriter" /> for an <see cref="T:System.Windows.Xps.Packaging.XpsResourceDictionary" />.</summary>
      <returns>The <see cref="T:System.Xml.XmlWriter" /> for an <see cref="T:System.Windows.Xps.Packaging.XpsResourceDictionary" />.</returns>
    </member>
    <member name="M:System.Windows.Xps.Serialization.BasePackagingPolicy.PersistPrintTicket(System.Printing.PrintTicket)">
      <summary>When overridden in a derived class, writes a print ticket to a package or to a part of the package.</summary>
      <param name="printTicket">An object that provides information about how a print job prints.</param>
    </member>
    <member name="M:System.Windows.Xps.Serialization.BasePackagingPolicy.PreCommitCurrentPage">
      <summary>When overridden in a derived class, performs any required maintenance tasks before the page is committed, for example, flushes streams.</summary>
    </member>
    <member name="M:System.Windows.Xps.Serialization.BasePackagingPolicy.RelateResourceToCurrentPage(System.Uri,System.String)">
      <summary>When overridden in a derived class, adds a relationship from the current page to some internal or external resource.</summary>
      <param name="targetUri">The uniform resource identifier (URI) of the resource.</param>
      <param name="relationshipName">A name for the relationship.</param>
    </member>
    <member name="M:System.Windows.Xps.Serialization.BasePackagingPolicy.RelateRestrictedFontToCurrentDocument(System.Uri)">
      <summary>When overridden in a derived class, adds a relationship from the current page to a font.</summary>
      <param name="targetUri">The uniform resource identifier (URI) of the font.</param>
    </member>
    <member name="M:System.Windows.Xps.Serialization.BasePackagingPolicy.ReleaseResourceStreamForXpsColorContext">
      <summary>When overridden in a derived class, releases the <see cref="T:System.Windows.Xps.Serialization.XpsResourceStream" /> for the current <see cref="T:System.Windows.Xps.Packaging.XpsColorContext" /> object.</summary>
    </member>
    <member name="M:System.Windows.Xps.Serialization.BasePackagingPolicy.ReleaseResourceStreamForXpsFont">
      <summary>When overridden in a derived class, releases the <see cref="T:System.Windows.Xps.Serialization.XpsResourceStream" /> for the current <see cref="T:System.Windows.Xps.Packaging.XpsResourceDictionary" /> object.</summary>
    </member>
    <member name="M:System.Windows.Xps.Serialization.BasePackagingPolicy.ReleaseResourceStreamForXpsFont(System.String)">
      <summary>When overridden in a derived class, releases the <see cref="T:System.Windows.Xps.Serialization.XpsResourceStream" /> for the current <see cref="T:System.Windows.Xps.Packaging.XpsResourceDictionary" /> object.</summary>
      <param name="resourceId">The ID of the resource that is released.</param>
    </member>
    <member name="M:System.Windows.Xps.Serialization.BasePackagingPolicy.ReleaseResourceStreamForXpsImage">
      <summary>When overridden in a derived class, releases the <see cref="T:System.Windows.Xps.Serialization.XpsResourceStream" /> for the current <see cref="T:System.Windows.Xps.Packaging.XpsImage" /> object.</summary>
    </member>
    <member name="M:System.Windows.Xps.Serialization.BasePackagingPolicy.ReleaseResourceStreamForXpsResourceDictionary">
      <summary>When overridden in a derived class, releases the <see cref="T:System.Windows.Xps.Serialization.XpsResourceStream" /> for the current <see cref="T:System.Windows.Xps.Packaging.XpsResourceDictionary" /> object.</summary>
    </member>
    <member name="M:System.Windows.Xps.Serialization.BasePackagingPolicy.ReleaseXmlWriterForFixedDocument">
      <summary>When overridden in a derived class, releases the <see cref="T:System.Xml.XmlWriter" /> for the current <see cref="T:System.Windows.Documents.FixedDocument" />.</summary>
    </member>
    <member name="M:System.Windows.Xps.Serialization.BasePackagingPolicy.ReleaseXmlWriterForFixedDocumentSequence">
      <summary>When overridden in a derived class, releases the <see cref="T:System.Xml.XmlWriter" /> for the current <see cref="T:System.Windows.Documents.FixedDocumentSequence" />.</summary>
    </member>
    <member name="M:System.Windows.Xps.Serialization.BasePackagingPolicy.ReleaseXmlWriterForFixedPage">
      <summary>When overridden in a derived class, releases the <see cref="T:System.Xml.XmlWriter" /> for the current <see cref="T:System.Windows.Documents.FixedPage" />.</summary>
    </member>
    <member name="M:System.Windows.Xps.Serialization.BasePackagingPolicy.System#IDisposable#Dispose">
      <summary>This member supports the Windows Presentation Foundation (WPF) infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Windows.Xps.Serialization.BasePackagingPolicy.CurrentFixedDocumentUri">
      <summary>When overridden in a derived class, gets the uniform resource identifier (URI) of the fixed document that is processing.</summary>
      <returns>The <see cref="T:System.Uri" /> of the current document.</returns>
    </member>
    <member name="P:System.Windows.Xps.Serialization.BasePackagingPolicy.CurrentFixedPageUri">
      <summary>When overridden in a derived class, gets the uniform resource identifier (URI) of the fixed page that is processing.</summary>
      <returns>The <see cref="T:System.Uri" /> of the current page.</returns>
    </member>
    <member name="T:System.Windows.Xps.Serialization.ColorTypeConverter">
      <summary>Provides type converters for converting <see cref="T:System.Windows.Media.Color" /> objects to and from objects of other types.</summary>
    </member>
    <member name="M:System.Windows.Xps.Serialization.ColorTypeConverter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Xps.Serialization.ColorTypeConverter" /> class.</summary>
    </member>
    <member name="M:System.Windows.Xps.Serialization.ColorTypeConverter.CanConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Type)">
      <summary>Returns a value that indicates whether this converter can convert an instance of a specified type to a <see cref="T:System.Windows.Media.Color" />.</summary>
      <param name="context">An object that provides contextual information.</param>
      <param name="sourceType">The type of object that is a candidate for conversion.</param>
      <returns>
        <see langword="true" /> if objects of the specified type can be converted; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Windows.Xps.Serialization.ColorTypeConverter.CanConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Type)">
      <summary>Returns a value that indicates whether this <see cref="T:System.Windows.Xps.Serialization.ColorTypeConverter" /> can convert a <see cref="T:System.Windows.Media.Color" /> to an instance of a specified type.</summary>
      <param name="context">An object that provides contextual information.</param>
      <param name="destinationType">The type of object that you want to convert a color to.</param>
      <returns>
        <see langword="true" /> if conversion is possible; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Windows.Xps.Serialization.ColorTypeConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)">
      <summary>Converts a specified object to a <see cref="T:System.Windows.Media.Color" />.</summary>
      <param name="context">An object that provides contextual information.</param>
      <param name="culture">The language and culture that are used during the conversion.</param>
      <param name="value">The object that is converted.</param>
      <exception cref="T:System.NotImplementedException">This method is called from this class instead of from a derived class.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> is <see langword="null" />.</exception>
      <exception cref="T:System.NotSupportedException">The <paramref name="value" /> is not a type that can be converted.</exception>
      <returns>The new <see cref="T:System.Windows.Media.Color" /> object.</returns>
    </member>
    <member name="M:System.Windows.Xps.Serialization.ColorTypeConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)">
      <summary>Converts a <see cref="T:System.Windows.Media.Color" /> to an object of the specified type.</summary>
      <param name="context">An object that provides contextual information.</param>
      <param name="culture">The language and culture that are used during the conversion.</param>
      <param name="value">The object you want to convert.</param>
      <param name="destinationType">The type that <paramref name="value" /> is converted to.</param>
      <exception cref="T:System.NotSupportedException">The <paramref name="destinationType" /> is not a type that <paramref name="value" /> can be converted to.  
  
 -or-  
  
 The <paramref name="culture" /> is a neutral culture.</exception>
      <returns>The new <see cref="T:System.Object" /> of the designated type.</returns>
    </member>
    <member name="M:System.Windows.Xps.Serialization.ColorTypeConverter.GetProperties(System.ComponentModel.ITypeDescriptorContext,System.Object,System.Attribute[])">
      <summary>Gets a collection of property descriptions for the specified type.</summary>
      <param name="context">An object that provides contextual information.</param>
      <param name="value">An object of the type for which you need property descriptions.</param>
      <param name="attributes">An array of attributes that filter the returned collection to exclude irrelevant properties.</param>
      <exception cref="T:System.NotImplementedException">This method is called from this class instead of from a derived class.</exception>
      <returns>A <see cref="T:System.ComponentModel.PropertyDescriptorCollection" /> that contains the property descriptions that are exposed for the component; or <see langword="null" /> if no property descriptions are returned.</returns>
    </member>
    <member name="M:System.Windows.Xps.Serialization.ColorTypeConverter.SerializeColorContext(System.IServiceProvider,System.Windows.Media.ColorContext)">
      <summary>Serializes a <see cref="T:System.Windows.Media.ColorContext" /> to the XML Paper Specification (XPS) package and returns its uniform resource identifier (URI) as a string.</summary>
      <param name="context">An object that provides contextual information.</param>
      <param name="colorContext">The object to be serialized.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="colorContext" /> is <see langword="null" />.</exception>
      <returns>A <see cref="T:System.String" /> that represents the URI of the color context.</returns>
    </member>
    <member name="T:System.Windows.Xps.Serialization.FontSubsetterCommitPolicies">
      <summary>Specifies the granularity at which font glyph subsets are saved in an XPS document.</summary>
    </member>
    <member name="F:System.Windows.Xps.Serialization.FontSubsetterCommitPolicies.CommitEntireSequence">
      <summary>Store all glyphs that are used in the text by each document sequence.</summary>
    </member>
    <member name="F:System.Windows.Xps.Serialization.FontSubsetterCommitPolicies.CommitPerDocument">
      <summary>Store all glyphs that are used in the text by each document.</summary>
    </member>
    <member name="F:System.Windows.Xps.Serialization.FontSubsetterCommitPolicies.CommitPerPage">
      <summary>Store all glyphs that are used in the text by each page.</summary>
    </member>
    <member name="F:System.Windows.Xps.Serialization.FontSubsetterCommitPolicies.None">
      <summary>No subsetting.  Store all glyphs for all fonts used in the document.</summary>
    </member>
    <member name="T:System.Windows.Xps.Serialization.FontTypeConverter">
      <summary>Provides type converters for converting <see cref="T:System.Windows.Media.GlyphRun" /> objects to and from objects of other types.</summary>
    </member>
    <member name="M:System.Windows.Xps.Serialization.FontTypeConverter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Xps.Serialization.FontTypeConverter" /> class.</summary>
    </member>
    <member name="M:System.Windows.Xps.Serialization.FontTypeConverter.CanConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Type)">
      <summary>Returns a value that indicates whether this converter can convert an instance of a specified type to a <see cref="T:System.Windows.Media.GlyphRun" />.</summary>
      <param name="context">An object that provides contextual information.</param>
      <param name="sourceType">The type of object to convert.</param>
      <returns>
        <see langword="true" /> if objects of the specified type can be converted; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Windows.Xps.Serialization.FontTypeConverter.CanConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Type)">
      <summary>Returns a value that indicates whether this <see cref="T:System.Windows.Xps.Serialization.FontTypeConverter" /> can convert a <see cref="T:System.Windows.Media.GlyphRun" /> to an instance of a specified type.</summary>
      <param name="context">An object that provides contextual information.</param>
      <param name="destinationType">The type of object that you want to convert a glyph run to.</param>
      <returns>
        <see langword="true" /> if conversion is possible; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Windows.Xps.Serialization.FontTypeConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)">
      <summary>Converts a specified object to a <see cref="T:System.Windows.Media.GlyphRun" />.</summary>
      <param name="context">An object that provides contextual information.</param>
      <param name="culture">The language and culture that are used during the conversion.</param>
      <param name="value">The object that is converted.</param>
      <exception cref="T:System.NotImplementedException">This method is called from this class instead of from a derived class.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> is <see langword="null" />.</exception>
      <exception cref="T:System.NotSupportedException">The <paramref name="value" /> is not a type that can be converted.</exception>
      <returns>The new <see cref="T:System.Windows.Media.GlyphRun" /> object.</returns>
    </member>
    <member name="M:System.Windows.Xps.Serialization.FontTypeConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)">
      <summary>Converts a <see cref="T:System.Windows.Media.GlyphRun" /> to an object of the specified type.</summary>
      <param name="context">An object that provides contextual information.</param>
      <param name="culture">The language and culture that are used during the conversion.</param>
      <param name="value">The object you want to convert.</param>
      <param name="destinationType">The type that you want <paramref name="value" /> converted to.</param>
      <exception cref="T:System.NotSupportedException">The <paramref name="destinationType" /> is not a type that <paramref name="value" /> can be converted to.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="context" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="value" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Windows.Xps.XpsSerializationException">An error occurs when serializing the glyph run.</exception>
      <returns>The new <see cref="T:System.Object" /> of the designated type. As implemented in this class, this must be a <see cref="T:System.Uri" />. It expresses the uniform resource identifier (URI) of the font subset that is used by the <paramref name="value" /> parameter.</returns>
    </member>
    <member name="M:System.Windows.Xps.Serialization.FontTypeConverter.GetProperties(System.ComponentModel.ITypeDescriptorContext,System.Object,System.Attribute[])">
      <summary>Gets a collection of property descriptions for the specified type.</summary>
      <param name="context">An object that provides contextual information.</param>
      <param name="value">An object that you need property descriptions for.</param>
      <param name="attributes">An array of attributes that filter the returned collection in order to exclude irrelevant properties.</param>
      <exception cref="T:System.NotImplementedException">This method is called from this class instead of from a derived class.</exception>
      <returns>A <see cref="T:System.ComponentModel.PropertyDescriptorCollection" /> that contains the property descriptions that are exposed for the component; or <see langword="null" /> if no property descriptions are returned.</returns>
    </member>
    <member name="T:System.Windows.Xps.Serialization.ImageSourceTypeConverter">
      <summary>Provides type converters for converting <see cref="T:System.Windows.Media.Imaging.BitmapSource" /> objects to and from objects of other types.</summary>
    </member>
    <member name="M:System.Windows.Xps.Serialization.ImageSourceTypeConverter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Xps.Serialization.ImageSourceTypeConverter" /> class.</summary>
    </member>
    <member name="M:System.Windows.Xps.Serialization.ImageSourceTypeConverter.CanConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Type)">
      <summary>Returns a value that indicates whether this converter can convert an instance of a specified type to a <see cref="T:System.Windows.Media.Imaging.BitmapSource" />.</summary>
      <param name="context">An object that provides contextual information.</param>
      <param name="sourceType">The type of object to convert.</param>
      <returns>
        <see langword="true" /> if objects of the specified type can be converted; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Windows.Xps.Serialization.ImageSourceTypeConverter.CanConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Type)">
      <summary>Returns a value that indicates whether this <see cref="T:System.Windows.Xps.Serialization.ImageSourceTypeConverter" /> can convert a <see cref="T:System.Windows.Media.Imaging.BitmapSource" /> to an instance of a specified type.</summary>
      <param name="context">An object that provides contextual information.</param>
      <param name="destinationType">The type of object that you want to convert to a <see cref="T:System.Windows.Media.Imaging.BitmapSource" />.</param>
      <returns>
        <see langword="true" /> if the conversion is possible; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Windows.Xps.Serialization.ImageSourceTypeConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)">
      <summary>Converts a specified object to a <see cref="T:System.Windows.Media.Imaging.BitmapSource" />.</summary>
      <param name="context">An object that provides contextual information.</param>
      <param name="culture">The language and culture that are used during the conversion.</param>
      <param name="value">The object that is converted.</param>
      <exception cref="T:System.NotImplementedException">This method is called from this class instead of from a derived class.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> is <see langword="null" />.</exception>
      <exception cref="T:System.NotSupportedException">The <paramref name="value" /> is not a type that can be converted.</exception>
      <returns>The new <see cref="T:System.Windows.Media.Imaging.BitmapSource" /> object.</returns>
    </member>
    <member name="M:System.Windows.Xps.Serialization.ImageSourceTypeConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)">
      <summary>Converts a <see cref="T:System.Windows.Media.Imaging.BitmapSource" /> to an object of the specified type.</summary>
      <param name="context">An object that provides contextual information.</param>
      <param name="culture">The language and culture that are used during the conversion.</param>
      <param name="value">The object that you want to convert.</param>
      <param name="destinationType">The type that you want <paramref name="value" /> converted to.</param>
      <exception cref="T:System.NotSupportedException">The <paramref name="destinationType" /> is not a type that <paramref name="value" /> can be converted to.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="context" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="value" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Windows.Xps.XpsSerializationException">An error occurs when serializing the image.</exception>
      <returns>The new <see cref="T:System.Object" /> of the designated type. As implemented in this class, the object must be a <see cref="T:System.Uri" />. The object expresses the uniform resource identifier (URI) of the serialized image.</returns>
    </member>
    <member name="M:System.Windows.Xps.Serialization.ImageSourceTypeConverter.GetProperties(System.ComponentModel.ITypeDescriptorContext,System.Object,System.Attribute[])">
      <summary>Gets a collection of property descriptions for the specified type.</summary>
      <param name="context">An object that provides contextual information.</param>
      <param name="value">An object that you need property descriptions for.</param>
      <param name="attributes">An array of attributes that are used to filter the returned collection in order to exclude irrelevant properties.</param>
      <exception cref="T:System.NotImplementedException">This method is called from this class instead of from a derived class.</exception>
      <returns>A <see cref="T:System.ComponentModel.PropertyDescriptorCollection" /> that contains the property descriptions that are exposed for the component; or <see langword="null" /> if no property descriptions are returned.</returns>
    </member>
    <member name="T:System.Windows.Xps.Serialization.PackageSerializationManager">
      <summary>Provides a base class to manage the serializers and type converters that insert Windows Presentation Foundation (WPF) root objects into an XML Paper Specification (XPS) package.</summary>
    </member>
    <member name="M:System.Windows.Xps.Serialization.PackageSerializationManager.#ctor">
      <summary>When overridden in a derived class, initializes a new instance of the <see cref="T:System.Windows.Xps.Serialization.PackageSerializationManager" /> class.</summary>
    </member>
    <member name="M:System.Windows.Xps.Serialization.PackageSerializationManager.SaveAsXaml(System.Object)">
      <summary>When overridden in a derived class, saves the specified serialized object to an XML Paper Specification (XPS) package.</summary>
      <param name="serializedObject">The object to save.</param>
    </member>
    <member name="M:System.Windows.Xps.Serialization.PackageSerializationManager.System#IDisposable#Dispose">
      <summary>This member supports the Windows Presentation Foundation infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Windows.Xps.Serialization.PrintTicketLevel">
      <summary>Specifies whether a <see cref="T:System.Printing.PrintTicket" /> applies to an entire sequence of documents, to just one document, or to just one page.</summary>
    </member>
    <member name="F:System.Windows.Xps.Serialization.PrintTicketLevel.FixedDocumentPrintTicket">
      <summary>A document.</summary>
    </member>
    <member name="F:System.Windows.Xps.Serialization.PrintTicketLevel.FixedDocumentSequencePrintTicket">
      <summary>A sequence of documents.</summary>
    </member>
    <member name="F:System.Windows.Xps.Serialization.PrintTicketLevel.FixedPagePrintTicket">
      <summary>A page.</summary>
    </member>
    <member name="F:System.Windows.Xps.Serialization.PrintTicketLevel.None">
      <summary>An unknown or unspecified level.</summary>
    </member>
    <member name="T:System.Windows.Xps.Serialization.SerializationState">
      <summary>Do not use.</summary>
    </member>
    <member name="F:System.Windows.Xps.Serialization.SerializationState.Normal">
      <summary>Do not use.</summary>
    </member>
    <member name="F:System.Windows.Xps.Serialization.SerializationState.Stop">
      <summary>Do not use.</summary>
    </member>
    <member name="T:System.Windows.Xps.Serialization.XpsPackagingPolicy">
      <summary>Defines the writer, resource, print ticket, and package relationship settings that are associated with a specified <see cref="T:System.Windows.Xps.Packaging.XpsDocument" />.</summary>
    </member>
    <member name="E:System.Windows.Xps.Serialization.XpsPackagingPolicy.PackagingProgressEvent">
      <summary>Occurs when a <see cref="T:System.Windows.Documents.FixedPage" />, <see cref="T:System.Windows.Documents.FixedDocument" />, or <see cref="T:System.Windows.Documents.FixedDocumentSequence" /> is added to the <see cref="T:System.Windows.Xps.Packaging.XpsDocument" />.</summary>
    </member>
    <member name="M:System.Windows.Xps.Serialization.XpsPackagingPolicy.#ctor(System.Windows.Xps.Packaging.XpsDocument)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Xps.Serialization.XpsPackagingPolicy" /> class for a specified <see cref="T:System.Windows.Xps.Packaging.XpsDocument" />.</summary>
      <param name="xpsPackage">The <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /> that is associated with this <see cref="T:System.Windows.Xps.Serialization.XpsPackagingPolicy" />.</param>
    </member>
    <member name="M:System.Windows.Xps.Serialization.XpsPackagingPolicy.#ctor(System.Windows.Xps.Packaging.XpsDocument,System.Windows.Xps.Packaging.PackageInterleavingOrder)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Xps.Serialization.XpsPackagingPolicy" /> class with a specified <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /> and specified interleave order.</summary>
      <param name="xpsPackage">The XML Paper Specification (XPS) document that the packaging policy applies to.</param>
      <param name="interleavingType">The order in which to interleave document elements.</param>
    </member>
    <member name="M:System.Windows.Xps.Serialization.XpsPackagingPolicy.AcquireResourceStreamForXpsColorContext(System.String)">
      <summary>Acquires the resource stream for a specified <see cref="T:System.Windows.Xps.Packaging.XpsColorContext" />.</summary>
      <param name="resourceId">The resource identifier of the color context.</param>
      <returns>The resource stream for the <see cref="T:System.Windows.Xps.Packaging.XpsColorContext" /> that has the specified <paramref name="resourceId" />.</returns>
    </member>
    <member name="M:System.Windows.Xps.Serialization.XpsPackagingPolicy.AcquireResourceStreamForXpsFont">
      <summary>Acquires the <see cref="T:System.Windows.Xps.Serialization.XpsResourceStream" /> for the current <see cref="T:System.Windows.Xps.Packaging.XpsFont" />.</summary>
      <returns>The resource stream for the current <see cref="T:System.Windows.Xps.Packaging.XpsFont" />.</returns>
    </member>
    <member name="M:System.Windows.Xps.Serialization.XpsPackagingPolicy.AcquireResourceStreamForXpsFont(System.String)">
      <summary>Acquires the <see cref="T:System.Windows.Xps.Serialization.XpsResourceStream" /> for a specified <see cref="T:System.Windows.Xps.Packaging.XpsFont" />.</summary>
      <param name="resourceId">The identifier of the XML Paper Specification (XPS) font.</param>
      <returns>The resource stream for the XML Paper Specification (XPS) font that has the specified <paramref name="resourceId" />.</returns>
    </member>
    <member name="M:System.Windows.Xps.Serialization.XpsPackagingPolicy.AcquireResourceStreamForXpsImage(System.String)">
      <summary>Acquires the resource stream for a specified <see cref="T:System.Windows.Xps.Packaging.XpsImage" />.</summary>
      <param name="resourceId">The resource identifier of the XML Paper Specification (XPS) image.</param>
      <returns>The resource stream for the <see cref="T:System.Windows.Xps.Packaging.XpsImage" /> that has the specified <paramref name="resourceId" />.</returns>
    </member>
    <member name="M:System.Windows.Xps.Serialization.XpsPackagingPolicy.AcquireResourceStreamForXpsResourceDictionary(System.String)">
      <summary>Acquires the resource stream for a specified <see cref="T:System.Windows.Xps.Packaging.XpsResourceDictionary" />.</summary>
      <param name="resourceId">The identifier of the resource dictionary.</param>
      <returns>The XML Paper Specification (XPS) resource stream for the <see cref="T:System.Windows.Xps.Packaging.XpsResourceDictionary" /> that has the specified <paramref name="resourceId" />.</returns>
    </member>
    <member name="M:System.Windows.Xps.Serialization.XpsPackagingPolicy.AcquireStreamForLinkTargets">
      <summary>Returns a list of <see cref="P:System.Windows.Documents.PageContent.LinkTargets" /> for the current page content.</summary>
      <returns>The list of <see cref="P:System.Windows.Documents.PageContent.LinkTargets" /> for the current page content.</returns>
    </member>
    <member name="M:System.Windows.Xps.Serialization.XpsPackagingPolicy.AcquireXmlWriterForFixedDocument">
      <summary>Acquires the <see cref="T:System.Xml.XmlWriter" /> for the current <see cref="T:System.Windows.Documents.FixedDocument" />.</summary>
      <returns>The XML writer for the current <see cref="T:System.Windows.Documents.FixedDocument" />.</returns>
    </member>
    <member name="M:System.Windows.Xps.Serialization.XpsPackagingPolicy.AcquireXmlWriterForFixedDocumentSequence">
      <summary>Acquires the <see cref="T:System.Xml.XmlWriter" /> for the current <see cref="T:System.Windows.Documents.FixedDocumentSequence" />.</summary>
      <returns>The XML writer for the current <see cref="T:System.Windows.Documents.FixedDocumentSequence" />.</returns>
    </member>
    <member name="M:System.Windows.Xps.Serialization.XpsPackagingPolicy.AcquireXmlWriterForFixedPage">
      <summary>Acquires the <see cref="T:System.Xml.XmlWriter" /> for the current <see cref="T:System.Windows.Documents.FixedPage" />.</summary>
      <returns>The XML writer for the current <see cref="T:System.Windows.Documents.FixedPage" />.</returns>
    </member>
    <member name="M:System.Windows.Xps.Serialization.XpsPackagingPolicy.AcquireXmlWriterForPage">
      <summary>Acquires the <see cref="T:System.Xml.XmlWriter" /> for the current page.</summary>
      <returns>The XML writer for the current <see cref="T:System.Windows.Documents.FixedPage" />.</returns>
    </member>
    <member name="M:System.Windows.Xps.Serialization.XpsPackagingPolicy.AcquireXmlWriterForResourceDictionary">
      <summary>Acquires the <see cref="T:System.Xml.XmlWriter" /> for the current <see cref="T:System.Windows.Xps.Packaging.XpsResourceDictionary" />.</summary>
      <returns>The XML writer for the current <see cref="T:System.Windows.Xps.Packaging.XpsResourceDictionary" />.</returns>
    </member>
    <member name="M:System.Windows.Xps.Serialization.XpsPackagingPolicy.PersistPrintTicket(System.Printing.PrintTicket)">
      <summary>Stores a specified <see cref="T:System.Printing.PrintTicket" /> as part of the <see cref="T:System.Windows.Xps.Packaging.XpsDocument" />.</summary>
      <param name="printTicket">The print ticket to store as part of the XML Paper Specification (XPS) document.</param>
    </member>
    <member name="M:System.Windows.Xps.Serialization.XpsPackagingPolicy.PreCommitCurrentPage">
      <summary>Prepares to commit the current page to the output store.</summary>
    </member>
    <member name="M:System.Windows.Xps.Serialization.XpsPackagingPolicy.RelateResourceToCurrentPage(System.Uri,System.String)">
      <summary>Adds a <see cref="T:System.IO.Packaging.PackageRelationship" /> with a specified name that associates a specified resource with the current page.</summary>
      <param name="targetUri">The uniform resource identifier (URI) of the resource to associate with the current page.</param>
      <param name="relationshipName">The identifying name of the <see cref="T:System.IO.Packaging.PackageRelationship" /> that associates the current page with the specified resource.</param>
    </member>
    <member name="M:System.Windows.Xps.Serialization.XpsPackagingPolicy.RelateRestrictedFontToCurrentDocument(System.Uri)">
      <summary>Adds a <see cref="T:System.IO.Packaging.PackageRelationship" /> that associates a restricted font with the current document.</summary>
      <param name="targetUri">The uniform resource identifier (URI) of the restricted font to associate with the current document.</param>
    </member>
    <member name="M:System.Windows.Xps.Serialization.XpsPackagingPolicy.ReleaseResourceStreamForXpsColorContext">
      <summary>Releases the <see cref="T:System.Windows.Xps.Serialization.XpsResourceStream" /> for the current <see cref="T:System.Windows.Xps.Packaging.XpsColorContext" />.</summary>
    </member>
    <member name="M:System.Windows.Xps.Serialization.XpsPackagingPolicy.ReleaseResourceStreamForXpsFont">
      <summary>Releases the <see cref="T:System.Windows.Xps.Serialization.XpsResourceStream" /> for the current <see cref="T:System.Windows.Xps.Packaging.XpsFont" />.</summary>
    </member>
    <member name="M:System.Windows.Xps.Serialization.XpsPackagingPolicy.ReleaseResourceStreamForXpsFont(System.String)">
      <summary>Releases the <see cref="T:System.Windows.Xps.Serialization.XpsResourceStream" /> for a specified <see cref="T:System.Windows.Xps.Packaging.XpsFont" />.</summary>
      <param name="resourceId">The identifier of the XML Paper Specification (XPS) font that you want to release.</param>
    </member>
    <member name="M:System.Windows.Xps.Serialization.XpsPackagingPolicy.ReleaseResourceStreamForXpsImage">
      <summary>Releases the <see cref="T:System.Windows.Xps.Serialization.XpsResourceStream" /> for the current <see cref="T:System.Windows.Xps.Packaging.XpsImage" />.</summary>
    </member>
    <member name="M:System.Windows.Xps.Serialization.XpsPackagingPolicy.ReleaseResourceStreamForXpsResourceDictionary">
      <summary>Releases the <see cref="T:System.Windows.Xps.Serialization.XpsResourceStream" /> for the current <see cref="T:System.Windows.Xps.Packaging.XpsResourceDictionary" />.</summary>
    </member>
    <member name="M:System.Windows.Xps.Serialization.XpsPackagingPolicy.ReleaseXmlWriterForFixedDocument">
      <summary>Releases the <see cref="T:System.Xml.XmlWriter" /> for the current <see cref="T:System.Windows.Documents.FixedDocument" />.</summary>
    </member>
    <member name="M:System.Windows.Xps.Serialization.XpsPackagingPolicy.ReleaseXmlWriterForFixedDocumentSequence">
      <summary>Releases the <see cref="T:System.Xml.XmlWriter" /> for the current <see cref="T:System.Windows.Documents.FixedDocumentSequence" />.</summary>
    </member>
    <member name="M:System.Windows.Xps.Serialization.XpsPackagingPolicy.ReleaseXmlWriterForFixedPage">
      <summary>Releases the <see cref="T:System.Xml.XmlWriter" /> for the current <see cref="T:System.Windows.Documents.FixedPage" />.</summary>
    </member>
    <member name="P:System.Windows.Xps.Serialization.XpsPackagingPolicy.CurrentFixedDocumentUri">
      <summary>Gets the uniform resource identifier (URI) of the current fixed document.</summary>
      <returns>The URI of the current fixed document.</returns>
    </member>
    <member name="P:System.Windows.Xps.Serialization.XpsPackagingPolicy.CurrentFixedPageUri">
      <summary>Gets the uniform resource identifier (URI) of the current fixed page.</summary>
      <returns>The URI of the current fixed page.</returns>
    </member>
    <member name="T:System.Windows.Xps.Serialization.XpsResourceStream">
      <summary>Represents the stream and uniform resource identifier (URI) of an XML Paper Specification (XPS) resource.</summary>
    </member>
    <member name="M:System.Windows.Xps.Serialization.XpsResourceStream.#ctor(System.IO.Stream,System.Uri)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Xps.Serialization.XpsResourceStream" /> class by using the specified stream and uniform resource identifier (URI).</summary>
      <param name="stream">The stream that contains the resource.</param>
      <param name="uri">The URI of the resource.</param>
    </member>
    <member name="M:System.Windows.Xps.Serialization.XpsResourceStream.Initialize">
      <summary>Initializes the <see cref="T:System.IO.Stream" /> in the <see cref="T:System.Windows.Xps.Serialization.XpsResourceStream" />.</summary>
    </member>
    <member name="P:System.Windows.Xps.Serialization.XpsResourceStream.Stream">
      <summary>Gets the <see cref="T:System.IO.Stream" /> of the resource.</summary>
      <returns>A <see cref="T:System.IO.Stream" /> that contains the XML Paper Specification (XPS) resource.</returns>
    </member>
    <member name="P:System.Windows.Xps.Serialization.XpsResourceStream.Uri">
      <summary>Gets the <see cref="T:System.Uri" /> of the resource.</summary>
      <returns>A <see cref="T:System.Uri" /> that contains the XML Paper Specification (XPS) resource.</returns>
    </member>
    <member name="T:System.Windows.Xps.Serialization.XpsSerializationCompletedEventArgs">
      <summary>Provides data for the <see cref="E:System.Windows.Xps.Serialization.XpsSerializationManagerAsync.XpsSerializationCompleted" /> event of an <see cref="T:System.Windows.Xps.Serialization.XpsSerializationManagerAsync" />.</summary>
    </member>
    <member name="M:System.Windows.Xps.Serialization.XpsSerializationCompletedEventArgs.#ctor(System.Boolean,System.Object,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Xps.Serialization.XpsSerializationCompletedEventArgs" /> class.</summary>
      <param name="canceled">
        <see langword="true" /> to indicate that the serialization was canceled before completion; otherwise, <see langword="false" />.</param>
      <param name="state">A user-supplied object that provides additional data to the event handler.</param>
      <param name="exception">An exception, if any, that interrupted the serialization operation.</param>
    </member>
    <member name="T:System.Windows.Xps.Serialization.XpsSerializationCompletedEventHandler">
      <summary>Represents the method that handles the <see cref="E:System.Windows.Xps.Serialization.XpsSerializationManagerAsync.XpsSerializationCompleted" /> event of an <see cref="T:System.Windows.Xps.Serialization.XpsSerializationManagerAsync" />.</summary>
      <param name="sender">The source of the event.</param>
      <param name="e">The event data.</param>
    </member>
    <member name="T:System.Windows.Xps.Serialization.XpsSerializationManager">
      <summary>Manages synchronous XML Paper Specification (XPS) serializers and type converters.</summary>
    </member>
    <member name="E:System.Windows.Xps.Serialization.XpsSerializationManager.XpsSerializationPrintTicketRequired">
      <summary>Occurs when an XPS serializer requests a <see cref="T:System.Printing.PrintTicket" />.</summary>
    </member>
    <member name="E:System.Windows.Xps.Serialization.XpsSerializationManager.XpsSerializationProgressChanged">
      <summary>Occurs when a page or document finishes serialization.</summary>
    </member>
    <member name="M:System.Windows.Xps.Serialization.XpsSerializationManager.#ctor(System.Windows.Xps.Serialization.BasePackagingPolicy,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Xps.Serialization.XpsSerializationManager" /> class.</summary>
      <param name="packagingPolicy">An object that provides methods for acquiring serialization readers and writers for different parts of an XML Paper Specification (XPS) document.</param>
      <param name="batchMode">
        <see langword="true" /> to specify batch mode; otherwise, <see langword="false" />.</param>
    </member>
    <member name="M:System.Windows.Xps.Serialization.XpsSerializationManager.Commit">
      <summary>Commits all changes and writes all buffered data.</summary>
    </member>
    <member name="M:System.Windows.Xps.Serialization.XpsSerializationManager.SaveAsXaml(System.Object)">
      <summary>Saves a specified XAML serialized object to the document package.</summary>
      <param name="serializedObject">The XAML serialized object to save.</param>
    </member>
    <member name="M:System.Windows.Xps.Serialization.XpsSerializationManager.SetFontSubsettingCountPolicy(System.Int32)">
      <summary>Sets the number of pages or documents to process for font-subsetting.</summary>
      <param name="countPolicy">The number of pages or documents to process at a time for font subsetting.</param>
    </member>
    <member name="M:System.Windows.Xps.Serialization.XpsSerializationManager.SetFontSubsettingPolicy(System.Windows.Xps.Serialization.FontSubsetterCommitPolicies)">
      <summary>Sets the granularity at which font-subsetting is performed.</summary>
      <param name="policy">One of the enumeration values that specifies the font-subsetting policy.</param>
    </member>
    <member name="P:System.Windows.Xps.Serialization.XpsSerializationManager.IsBatchMode">
      <summary>Gets a value that indicates whether the serialization manager is in batch mode.</summary>
      <returns>
        <see langword="true" /> if the manager is in batch mode; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Windows.Xps.Serialization.XpsSerializationManagerAsync">
      <summary>Manages asynchronous XML Paper Specification (XPS) serializers and type converters.</summary>
    </member>
    <member name="E:System.Windows.Xps.Serialization.XpsSerializationManagerAsync.XpsSerializationCompleted">
      <summary>Occurs when a serialization operation finishes.</summary>
    </member>
    <member name="M:System.Windows.Xps.Serialization.XpsSerializationManagerAsync.#ctor(System.Windows.Xps.Serialization.BasePackagingPolicy,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Xps.Serialization.XpsSerializationManagerAsync" /> class.</summary>
      <param name="packagingPolicy">An object that provides methods for acquiring serialization readers and writers for different parts of an XML Paper Specification (XPS) document.</param>
      <param name="batchMode">
        <see langword="true" /> to specify batch mode; otherwise, <see langword="false" />.</param>
    </member>
    <member name="M:System.Windows.Xps.Serialization.XpsSerializationManagerAsync.CancelAsync">
      <summary>Cancels an asynchronous serialization operation.</summary>
    </member>
    <member name="M:System.Windows.Xps.Serialization.XpsSerializationManagerAsync.Commit">
      <summary>Commits all changes and writes all buffered data as output.</summary>
    </member>
    <member name="M:System.Windows.Xps.Serialization.XpsSerializationManagerAsync.SaveAsXaml(System.Object)">
      <summary>Saves a specified serialized object to an XML Paper Specification (XPS) package.</summary>
      <param name="serializedObject">The object that is saved.</param>
    </member>
    <member name="T:System.Windows.Xps.Serialization.XpsSerializationPrintTicketRequiredEventArgs">
      <summary>Provides data for the <see cref="E:System.Windows.Xps.Serialization.XpsSerializationManager.XpsSerializationPrintTicketRequired" /> event.</summary>
    </member>
    <member name="M:System.Windows.Xps.Serialization.XpsSerializationPrintTicketRequiredEventArgs.#ctor(System.Windows.Xps.Serialization.PrintTicketLevel,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Xps.Serialization.XpsSerializationPrintTicketRequiredEventArgs" /> class.</summary>
      <param name="printTicketLevel">An object that specifies the scope of the <see cref="T:System.Printing.PrintTicket" />.</param>
      <param name="sequence">Either the total number of pages or the number of documents in the print job. Which applies depends on the scope of the <see cref="T:System.Printing.PrintTicket" />.</param>
    </member>
    <member name="P:System.Windows.Xps.Serialization.XpsSerializationPrintTicketRequiredEventArgs.PrintTicket">
      <summary>Gets or sets the <see cref="T:System.Printing.PrintTicket" /> that is used in the writing operation.</summary>
      <returns>A <see cref="T:System.Printing.PrintTicket" /> that defines how the printer handles a print job.</returns>
    </member>
    <member name="P:System.Windows.Xps.Serialization.XpsSerializationPrintTicketRequiredEventArgs.PrintTicketLevel">
      <summary>Gets a value that indicates the scope of the <see cref="T:System.Printing.PrintTicket" />.</summary>
      <returns>A <see cref="T:System.Windows.Xps.Serialization.PrintTicketLevel" /> value that indicates the scope of the <see cref="T:System.Printing.PrintTicket" />.</returns>
    </member>
    <member name="P:System.Windows.Xps.Serialization.XpsSerializationPrintTicketRequiredEventArgs.Sequence">
      <summary>Gets either the total number of pages or the number of documents in the print job.</summary>
      <returns>An <see cref="T:System.Int32" /> that represents the total number of pages or the number of documents.</returns>
    </member>
    <member name="T:System.Windows.Xps.Serialization.XpsSerializationPrintTicketRequiredEventHandler">
      <summary>Represents the method that handles the <see cref="E:System.Windows.Xps.Serialization.XpsSerializationManager.XpsSerializationPrintTicketRequired" /> event of an <see cref="T:System.Windows.Xps.Serialization.XpsSerializationManager" />.</summary>
      <param name="sender">The source of the event.</param>
      <param name="e">The event data.</param>
    </member>
    <member name="T:System.Windows.Xps.Serialization.XpsSerializationProgressChangedEventArgs">
      <summary>Provides data for the <see cref="E:System.Windows.Xps.Serialization.XpsSerializationManager.XpsSerializationProgressChanged" /> event.</summary>
    </member>
    <member name="M:System.Windows.Xps.Serialization.XpsSerializationProgressChangedEventArgs.#ctor(System.Windows.Xps.Serialization.XpsWritingProgressChangeLevel,System.Int32,System.Int32,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Xps.Serialization.XpsSerializationProgressChangedEventArgs" /> class.</summary>
      <param name="writingLevel">A value that specifies whether the change in progress is an additional page completed or an additional document completed.</param>
      <param name="pageNumber">A value that specifies the total number of pages or the number of documents that are serialized at the time of the event.</param>
      <param name="progressPercentage">A value that specifies the percentage of the total serialization job that is complete.</param>
      <param name="userToken">A user-supplied object that provides additional information for the event handler.</param>
    </member>
    <member name="P:System.Windows.Xps.Serialization.XpsSerializationProgressChangedEventArgs.PageNumber">
      <summary>Gets the number of pages or documents that have been serialized.</summary>
      <returns>The total pages or documents have been serialized at the point when the event occurred.</returns>
    </member>
    <member name="P:System.Windows.Xps.Serialization.XpsSerializationProgressChangedEventArgs.WritingLevel">
      <summary>Gets a value that indicates the scope of the progress indicator.</summary>
      <returns>The scope of the progress indicator.</returns>
    </member>
    <member name="T:System.Windows.Xps.Serialization.XpsSerializationProgressChangedEventHandler">
      <summary>Represents the method that handles the <see cref="E:System.Windows.Xps.Serialization.XpsSerializationManager.XpsSerializationProgressChanged" /> event of an <see cref="T:System.Windows.Xps.Serialization.XpsSerializationManager" />.</summary>
      <param name="sender">The source of the event.</param>
      <param name="e">The event data.</param>
    </member>
    <member name="T:System.Windows.Xps.Serialization.XpsSerializerFactory">
      <summary>Creates and provides information about XML Paper Specification (XPS) serializers.</summary>
    </member>
    <member name="M:System.Windows.Xps.Serialization.XpsSerializerFactory.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Xps.Serialization.XpsSerializerFactory" /> class.</summary>
    </member>
    <member name="M:System.Windows.Xps.Serialization.XpsSerializerFactory.CreateSerializerWriter(System.IO.Stream)">
      <summary>Creates a <see cref="T:System.Windows.Documents.Serialization.SerializerWriter" /> that outputs XPS content to a specified <see cref="T:System.IO.Stream" />.</summary>
      <param name="stream">The output stream that the returned serializer is to write to.</param>
      <returns>An output writer that serializes XPS content to the specified <paramref name="stream" />.</returns>
    </member>
    <member name="P:System.Windows.Xps.Serialization.XpsSerializerFactory.DefaultFileExtension">
      <summary>Gets the standard file name extension for XPS documents.</summary>
      <returns>The standard file name extension (including the leading period) for XPS documents.</returns>
    </member>
    <member name="P:System.Windows.Xps.Serialization.XpsSerializerFactory.DisplayName">
      <summary>Gets the public name for the serializers that the factory produces.</summary>
      <returns>The public name for serializers that the factory produces.</returns>
    </member>
    <member name="P:System.Windows.Xps.Serialization.XpsSerializerFactory.ManufacturerName">
      <summary>Gets the manufacturer's name for serializers that the factory produces.</summary>
      <returns>The manufacturer's name.</returns>
    </member>
    <member name="P:System.Windows.Xps.Serialization.XpsSerializerFactory.ManufacturerWebsite">
      <summary>Gets the manufacturer's Web address for serializers that the factory produces.</summary>
      <returns>The manufacturer's Web site.</returns>
    </member>
    <member name="T:System.Windows.Xps.Serialization.XpsWritingProgressChangeLevel">
      <summary>Specifies the scope of a writing progress indicator for XML Paper Specification (XPS) content.</summary>
    </member>
    <member name="F:System.Windows.Xps.Serialization.XpsWritingProgressChangeLevel.FixedDocumentSequenceWritingProgress">
      <summary>An entire sequence of one or more documents.</summary>
    </member>
    <member name="F:System.Windows.Xps.Serialization.XpsWritingProgressChangeLevel.FixedDocumentWritingProgress">
      <summary>A single document.</summary>
    </member>
    <member name="F:System.Windows.Xps.Serialization.XpsWritingProgressChangeLevel.FixedPageWritingProgress">
      <summary>A single page.</summary>
    </member>
    <member name="F:System.Windows.Xps.Serialization.XpsWritingProgressChangeLevel.None">
      <summary>There is no interpretation for the progress value.</summary>
    </member>
    <member name="T:System.Windows.Xps.XpsException">
      <summary>Serves as the base class for exceptions that are thrown by the XML Paper Specification (XPS) packaging and serialization APIs.</summary>
    </member>
    <member name="M:System.Windows.Xps.XpsException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Xps.XpsException" /> class.</summary>
    </member>
    <member name="M:System.Windows.Xps.XpsException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Xps.XpsException" /> class that provides specific <see cref="T:System.Runtime.Serialization.SerializationInfo" /> and <see cref="T:System.Runtime.Serialization.StreamingContext" />. This constructor is protected.</summary>
      <param name="info">The data that is required to serialize or deserialize an object.</param>
      <param name="context">The context, which includes source and destination, of the serialized stream.</param>
    </member>
    <member name="M:System.Windows.Xps.XpsException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Xps.XpsException" /> class that provides a specific error condition.</summary>
      <param name="message">A <see cref="T:System.String" /> that describes the error condition.</param>
    </member>
    <member name="M:System.Windows.Xps.XpsException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Xps.XpsException" /> class that provides a specific error condition and includes the cause of the exception.</summary>
      <param name="message">A <see cref="T:System.String" /> that describes the error condition.</param>
      <param name="innerException">The underlying error that caused the <see cref="T:System.Windows.Xps.XpsException" />.</param>
    </member>
    <member name="T:System.Windows.Xps.XpsPackagingException">
      <summary>The exception that is thrown when reading, writing to, registering, or accessing in some other way an <see cref="T:System.Windows.Xps.Packaging.XpsDocument" />.</summary>
    </member>
    <member name="M:System.Windows.Xps.XpsPackagingException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Xps.XpsPackagingException" /> class.</summary>
    </member>
    <member name="M:System.Windows.Xps.XpsPackagingException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Xps.XpsPackagingException" /> class that provides specific <see cref="T:System.Runtime.Serialization.SerializationInfo" /> and <see cref="T:System.Runtime.Serialization.StreamingContext" />. This constructor is protected.</summary>
      <param name="info">The data that is required to serialize or deserialize an object.</param>
      <param name="context">The context, which includes source and destination, of the serialized stream.</param>
    </member>
    <member name="M:System.Windows.Xps.XpsPackagingException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Xps.XpsPackagingException" /> class that provides a specific error condition.</summary>
      <param name="message">A <see cref="T:System.String" /> that describes the error condition.</param>
    </member>
    <member name="M:System.Windows.Xps.XpsPackagingException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Xps.XpsPackagingException" /> class that provides a specific error condition and includes the cause of the exception.</summary>
      <param name="message">A <see cref="T:System.String" /> that describes the error condition.</param>
      <param name="innerException">The underlying error that caused the <see cref="T:System.Windows.Xps.XpsPackagingException" />.</param>
    </member>
    <member name="T:System.Windows.Xps.XpsSerializationException">
      <summary>The exception that is thrown for XML Paper Specification (XPS) document serialization errors.</summary>
    </member>
    <member name="M:System.Windows.Xps.XpsSerializationException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Xps.XpsSerializationException" /> class.</summary>
    </member>
    <member name="M:System.Windows.Xps.XpsSerializationException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Xps.XpsSerializationException" /> class that provides specific <see cref="T:System.Runtime.Serialization.SerializationInfo" /> and <see cref="T:System.Runtime.Serialization.StreamingContext" />. This constructor is protected.</summary>
      <param name="info">The data that is required to serialize or deserialize an object.</param>
      <param name="context">The context, which includes the source and destination, of the serialized stream.</param>
    </member>
    <member name="M:System.Windows.Xps.XpsSerializationException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Xps.XpsSerializationException" /> class that provides a specific error condition.</summary>
      <param name="message">A <see cref="T:System.String" /> that describes the error condition.</param>
    </member>
    <member name="M:System.Windows.Xps.XpsSerializationException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Windows.Xps.XpsSerializationException" /> class that provides a specific error condition and includes the cause of the exception.</summary>
      <param name="message">A <see cref="T:System.String" /> that describes the error condition.</param>
      <param name="innerException">The underlying error that caused the exception.</param>
    </member>
  </members>
</doc>