using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using NAudio.Wave;
using Whisper.net;
using FlightPig.Models;

namespace FlightPig.Services
{
    /// <summary>
    /// Speech recognition service using Whisper
    /// </summary>
    public class SpeechRecognitionService : IDisposable
    {
        private readonly AppSettings _settings;
        private WhisperFactory _whisperFactory;
        private WhisperProcessor _whisperProcessor;
        private WaveInEvent _waveIn;
        private MemoryStream _audioStream;
        private bool _isRecording;
        private bool _isListening;
        private readonly object _lockObject = new object();

        public event EventHandler<string> SpeechRecognized;
        public event EventHandler RecordingStarted;
        public event EventHandler RecordingStopped;

        public SpeechRecognitionService(AppSettings settings)
        {
            _settings = settings;
            InitializeWhisper();
            InitializeAudio();
        }

        private void InitializeWhisper()
        {
            try
            {
                Console.WriteLine("Initializing Whisper speech recognition...");
                
                // Download model if it doesn't exist
                var modelPath = Path.Combine("models", _settings.Whisper.ModelName);
                if (!File.Exists(modelPath))
                {
                    Console.WriteLine($"Whisper model not found at {modelPath}");
                    Console.WriteLine("Please download the Whisper model manually or the service will use a default model.");
                    // For now, we'll try to use the built-in model
                }

                _whisperFactory = WhisperFactory.FromPath("ggml-tiny.bin");
                _whisperProcessor = _whisperFactory.CreateBuilder()
                    .WithLanguage(_settings.Whisper.Language)
                    .Build();

                Console.WriteLine("Whisper initialized successfully.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error initializing Whisper: {ex.Message}");
                Console.WriteLine("Speech recognition will be disabled.");
            }
        }

        private void InitializeAudio()
        {
            try
            {
                _waveIn = new WaveInEvent
                {
                    WaveFormat = new WaveFormat(16000, 1), // 16kHz mono for Whisper
                    BufferMilliseconds = 100
                };
                _waveIn.DataAvailable += OnDataAvailable;
                _waveIn.RecordingStopped += OnRecordingStopped;

                Console.WriteLine("Audio input initialized successfully.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error initializing audio: {ex.Message}");
            }
        }

        /// <summary>
        /// Start listening for voice commands
        /// </summary>
        public void StartListening()
        {
            if (!_settings.Whisper.Enabled || _whisperProcessor == null)
            {
                Console.WriteLine("Speech recognition is disabled or not available.");
                return;
            }

            lock (_lockObject)
            {
                if (_isListening) return;
                _isListening = true;
            }

            Console.WriteLine("Voice recognition started. Say a command...");
            StartRecording();
        }

        /// <summary>
        /// Stop listening for voice commands
        /// </summary>
        public void StopListening()
        {
            lock (_lockObject)
            {
                if (!_isListening) return;
                _isListening = false;
            }

            StopRecording();
            Console.WriteLine("Voice recognition stopped.");
        }

        /// <summary>
        /// Record a single voice command
        /// </summary>
        public async Task<string> RecordSingleCommandAsync()
        {
            if (!_settings.Whisper.Enabled || _whisperProcessor == null)
            {
                return null;
            }

            var tcs = new TaskCompletionSource<string>();
            
            EventHandler<string> handler = (sender, text) =>
            {
                tcs.TrySetResult(text);
            };

            try
            {
                SpeechRecognized += handler;
                StartRecording();

                // Wait for speech or timeout
                var timeoutTask = Task.Delay(_settings.Whisper.RecordingTimeoutSeconds * 1000);
                var completedTask = await Task.WhenAny(tcs.Task, timeoutTask);

                if (completedTask == timeoutTask)
                {
                    Console.WriteLine("Voice recording timed out.");
                    return null;
                }

                return await tcs.Task;
            }
            finally
            {
                SpeechRecognized -= handler;
                StopRecording();
            }
        }

        private void StartRecording()
        {
            if (_isRecording || _waveIn == null) return;

            try
            {
                _audioStream = new MemoryStream();
                _isRecording = true;
                _waveIn.StartRecording();
                RecordingStarted?.Invoke(this, EventArgs.Empty);
                Console.WriteLine("🎤 Recording started...");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error starting recording: {ex.Message}");
            }
        }

        private void StopRecording()
        {
            if (!_isRecording || _waveIn == null) return;

            try
            {
                _waveIn.StopRecording();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error stopping recording: {ex.Message}");
            }
        }

        private void OnDataAvailable(object sender, WaveInEventArgs e)
        {
            if (_audioStream != null && _isRecording)
            {
                _audioStream.Write(e.Buffer, 0, e.BytesRecorded);
            }
        }

        private async void OnRecordingStopped(object sender, StoppedEventArgs e)
        {
            _isRecording = false;
            RecordingStopped?.Invoke(this, EventArgs.Empty);
            Console.WriteLine("🎤 Recording stopped.");

            if (_audioStream != null && _audioStream.Length > 0)
            {
                await ProcessAudioAsync();
            }
        }

        private async Task ProcessAudioAsync()
        {
            if (_whisperProcessor == null || _audioStream == null) return;

            try
            {
                Console.WriteLine("Processing speech...");
                
                _audioStream.Position = 0;
                var audioData = _audioStream.ToArray();

                // Convert audio data to float array for Whisper
                var floatData = ConvertBytesToFloat(audioData);

                await foreach (var result in _whisperProcessor.ProcessAsync(floatData))
                {
                    if (!string.IsNullOrWhiteSpace(result.Text))
                    {
                        Console.WriteLine($"Recognized: \"{result.Text}\"");
                        SpeechRecognized?.Invoke(this, result.Text.Trim());
                        break; // Take the first result
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error processing audio: {ex.Message}");
            }
            finally
            {
                _audioStream?.Dispose();
                _audioStream = null;
            }
        }

        private float[] ConvertBytesToFloat(byte[] audioData)
        {
            var floatData = new float[audioData.Length / 2];
            for (int i = 0; i < floatData.Length; i++)
            {
                var sample = BitConverter.ToInt16(audioData, i * 2);
                floatData[i] = sample / 32768f; // Convert to float range [-1, 1]
            }
            return floatData;
        }

        public void Dispose()
        {
            StopListening();
            _waveIn?.Dispose();
            _audioStream?.Dispose();
            _whisperProcessor?.Dispose();
            _whisperFactory?.Dispose();
        }
    }
}
