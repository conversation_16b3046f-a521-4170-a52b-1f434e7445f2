﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Threading.Tasks</name>
  </assembly>
  <members>
    <member name="T:System.AggregateException">
      <summary>表示應用程式執行期間所發生的一個或多個錯誤。</summary>
    </member>
    <member name="M:System.AggregateException.#ctor">
      <summary>使用系統提供的錯誤說明訊息，初始化 <see cref="T:System.AggregateException" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.AggregateException.#ctor(System.Collections.Generic.IEnumerable{System.Exception})">
      <summary>使用造成這個例外狀況原因的內部例外狀況參考，初始化 <see cref="T:System.AggregateException" /> 類別的新執行個體。</summary>
      <param name="innerExceptions">導致目前例外狀況的例外。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="innerExceptions" /> 引數為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="innerExceptions" /> 的項目為 Null。</exception>
    </member>
    <member name="M:System.AggregateException.#ctor(System.Exception[])">
      <summary>使用造成這個例外狀況原因的內部例外狀況參考，初始化 <see cref="T:System.AggregateException" /> 類別的新執行個體。</summary>
      <param name="innerExceptions">導致目前例外狀況的例外。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="innerExceptions" /> 引數為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="innerExceptions" /> 的項目為 Null。</exception>
    </member>
    <member name="M:System.AggregateException.#ctor(System.String)">
      <summary>使用指定的錯誤說明訊息，初始化 <see cref="T:System.AggregateException" /> 類別的新執行個體。</summary>
      <param name="message">說明例外狀況的訊息。這個建構函式的呼叫端必須確保這個字串已經為目前系統的文化特性當地語系化。</param>
    </member>
    <member name="M:System.AggregateException.#ctor(System.String,System.Collections.Generic.IEnumerable{System.Exception})">
      <summary>使用指定的錯誤訊息和造成這個例外狀況原因的內部例外狀況參考，初始化 <see cref="T:System.AggregateException" /> 類別的新執行個體。</summary>
      <param name="message">解釋例外狀況原因的錯誤訊息。</param>
      <param name="innerExceptions">導致目前例外狀況的例外。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="innerExceptions" /> 引數為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="innerExceptions" /> 的項目為 Null。</exception>
    </member>
    <member name="M:System.AggregateException.#ctor(System.String,System.Exception)">
      <summary>使用指定的錯誤訊息和造成這個例外狀況原因的內部例外狀況參考，初始化 <see cref="T:System.AggregateException" /> 類別的新執行個體。</summary>
      <param name="message">說明例外狀況的訊息。這個建構函式的呼叫端必須確保這個字串已經為目前系統的文化特性當地語系化。</param>
      <param name="innerException">導致目前例外狀況的例外。如果 <paramref name="innerException" /> 參數不是 null，則目前的例外狀況會在處理內部例外的 catch 區塊中引發。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="innerException" /> 引數為 null。</exception>
    </member>
    <member name="M:System.AggregateException.#ctor(System.String,System.Exception[])">
      <summary>使用指定的錯誤訊息和造成這個例外狀況原因的內部例外狀況參考，初始化 <see cref="T:System.AggregateException" /> 類別的新執行個體。</summary>
      <param name="message">解釋例外狀況原因的錯誤訊息。</param>
      <param name="innerExceptions">導致目前例外狀況的例外。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="innerExceptions" /> 引數為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="innerExceptions" /> 的項目為 Null。</exception>
    </member>
    <member name="M:System.AggregateException.Flatten">
      <summary>將 <see cref="T:System.AggregateException" /> 執行個體簡維成單一新執行個體。</summary>
      <returns>簡維的新 <see cref="T:System.AggregateException" />。</returns>
    </member>
    <member name="M:System.AggregateException.GetBaseException">
      <summary>傳回這個例外狀況之根本原因的 <see cref="T:System.AggregateException" />。</summary>
      <returns>傳回這個例外狀況之根本原因的 <see cref="T:System.AggregateException" />。</returns>
    </member>
    <member name="M:System.AggregateException.Handle(System.Func{System.Exception,System.Boolean})">
      <summary>對這個 <see cref="T:System.AggregateException" /> 包含的每個 <see cref="T:System.Exception" /> 叫用處理常式。</summary>
      <param name="predicate">要對每個例外狀況執行的述詞。述詞會接受要處理的 <see cref="T:System.Exception" /> 做為引數，並傳回表示是否已處理例外狀況的布林值。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="predicate" /> 引數為 null。</exception>
      <exception cref="T:System.AggregateException">未處理此 <see cref="T:System.AggregateException" /> 所包含的例外狀況。</exception>
    </member>
    <member name="P:System.AggregateException.InnerExceptions">
      <summary>取得造成目前例外狀況之 <see cref="T:System.Exception" /> 執行個體的唯讀集合。</summary>
      <returns>傳回造成目前例外狀況之 <see cref="T:System.Exception" /> 執行個體的唯讀集合。</returns>
    </member>
    <member name="M:System.AggregateException.ToString">
      <summary>建立並傳回目前 <see cref="T:System.AggregateException" /> 的字串表示。</summary>
      <returns>目前例外狀況的字串表示。</returns>
    </member>
    <member name="T:System.OperationCanceledException">
      <summary>在取消執行緒執行的作業之後，在執行緒中擲回的例外狀況。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.OperationCanceledException.#ctor">
      <summary>使用系統提供的錯誤訊息，初始化 <see cref="T:System.OperationCanceledException" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.OperationCanceledException.#ctor(System.String)">
      <summary>使用指定的錯誤訊息，初始化 <see cref="T:System.OperationCanceledException" /> 類別的新執行個體。</summary>
      <param name="message">描述錯誤的 <see cref="T:System.String" />。</param>
    </member>
    <member name="M:System.OperationCanceledException.#ctor(System.String,System.Exception)">
      <summary>使用指定的錯誤訊息和造成這個例外狀況原因的內部例外狀況參考，初始化 <see cref="T:System.OperationCanceledException" /> 類別的新執行個體。</summary>
      <param name="message">解釋例外狀況原因的錯誤訊息。</param>
      <param name="innerException">導致目前例外狀況的例外。如果 <paramref name="innerException" /> 參數不是 null，則目前的例外狀況會在處理內部例外的 catch 區塊中引發。</param>
    </member>
    <member name="M:System.OperationCanceledException.#ctor(System.String,System.Exception,System.Threading.CancellationToken)">
      <summary>使用指定的錯誤訊息、造成這個例外狀況原因的內部例外狀況參考和取消語彙基元，初始化 <see cref="T:System.OperationCanceledException" /> 類別的新執行個體。</summary>
      <param name="message">解釋例外狀況原因的錯誤訊息。</param>
      <param name="innerException">導致目前例外狀況的例外。如果 <paramref name="innerException" /> 參數不是 null，則目前的例外狀況會在處理內部例外的 catch 區塊中引發。</param>
      <param name="token">與已取消作業關聯的取消語彙基元。</param>
    </member>
    <member name="M:System.OperationCanceledException.#ctor(System.String,System.Threading.CancellationToken)">
      <summary>使用指定的錯誤訊息和取消語彙基元，初始化 <see cref="T:System.OperationCanceledException" /> 類別的新執行個體。</summary>
      <param name="message">解釋例外狀況原因的錯誤訊息。</param>
      <param name="token">與已取消作業關聯的取消語彙基元。</param>
    </member>
    <member name="M:System.OperationCanceledException.#ctor(System.Threading.CancellationToken)">
      <summary>使用取消語彙基元，初始化 <see cref="T:System.OperationCanceledException" /> 類別的新執行個體。</summary>
      <param name="token">與已取消作業關聯的取消語彙基元。</param>
    </member>
    <member name="P:System.OperationCanceledException.CancellationToken">
      <summary>取得與已取消之作業相關聯的語彙基元。</summary>
      <returns>與已取消之作業相關聯的語彙基元，或是預設語彙基元。</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.AsyncTaskMethodBuilder">
      <summary>表示非同步方法產生器，會傳回工作。</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.AwaitOnCompleted``2(``0@,``1@)">
      <summary>排程狀態機器以在指定的 awaiter 完成時繼續下一個動作。</summary>
      <param name="awaiter">awaiter。</param>
      <param name="stateMachine">狀態機器。</param>
      <typeparam name="TAwaiter">awaiter 的型別。</typeparam>
      <typeparam name="TStateMachine">狀態機器的類型。</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.AwaitUnsafeOnCompleted``2(``0@,``1@)">
      <summary>排程狀態機器以在指定的 awaiter 完成時繼續下一個動作。可以從部分信任程式碼呼叫這個方法。</summary>
      <param name="awaiter">awaiter。</param>
      <param name="stateMachine">狀態機器。</param>
      <typeparam name="TAwaiter">awaiter 的型別。</typeparam>
      <typeparam name="TStateMachine">狀態機器的類型。</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.Create">
      <summary>建立 <see cref="T:System.Runtime.CompilerServices.AsyncTaskMethodBuilder" /> 類別的執行個體。</summary>
      <returns>產生器的新執行個體。</returns>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetException(System.Exception)">
      <summary>將工作標示為失敗，並將指定的例外狀況繫結至工作。</summary>
      <param name="exception">要繫結至工作的例外狀況。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="exception" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">工作已完成。-或-產生器未初始化。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetResult">
      <summary>將工作標記為已成功完成。</summary>
      <exception cref="T:System.InvalidOperationException">工作已完成。-或-產生器未初始化。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetStateMachine(System.Runtime.CompilerServices.IAsyncStateMachine)">
      <summary>將產生器與指定的狀態機器產生關聯。</summary>
      <param name="stateMachine">要和產生器產生關聯的狀態機器執行個體。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">狀態電腦先前已經設定。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.Start``1(``0@)">
      <summary>開始執行具有相關聯狀態機器的產生器。</summary>
      <param name="stateMachine">狀態機器執行個體，由參考傳遞。</param>
      <typeparam name="TStateMachine">狀態機器的類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" /> 為 null。</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.Task">
      <summary>取得此產生器的工作。</summary>
      <returns>此產生器的工作。</returns>
      <exception cref="T:System.InvalidOperationException">產生器未初始化。</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1">
      <summary>表示非同步方法的產生器，會傳回一項工作並提供結果參數。</summary>
      <typeparam name="TResult">要用來完成工作的結果。</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AwaitOnCompleted``2(``0@,``1@)">
      <summary>排程狀態機器以在指定的 awaiter 完成時繼續下一個動作。</summary>
      <param name="awaiter">awaiter。</param>
      <param name="stateMachine">狀態機器。</param>
      <typeparam name="TAwaiter">awaiter 的型別。</typeparam>
      <typeparam name="TStateMachine">狀態機器的類型。</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AwaitUnsafeOnCompleted``2(``0@,``1@)">
      <summary>排程狀態機器以在指定的 awaiter 完成時繼續下一個動作。可以從部分信任程式碼呼叫這個方法。</summary>
      <param name="awaiter">awaiter。</param>
      <param name="stateMachine">狀態機器。</param>
      <typeparam name="TAwaiter">awaiter 的型別。</typeparam>
      <typeparam name="TStateMachine">狀態機器的類型。</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.Create">
      <summary>建立 <see cref="T:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1" /> 類別的執行個體。</summary>
      <returns>產生器的新執行個體。</returns>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetException(System.Exception)">
      <summary>將工作標示為失敗，並將指定的例外狀況繫結至工作。</summary>
      <param name="exception">要繫結至工作的例外狀況。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="exception" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">工作已完成。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetResult(`0)">
      <summary>將工作標記為已成功完成。</summary>
      <param name="result">要用來完成工作的結果。</param>
      <exception cref="T:System.InvalidOperationException">工作已完成。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetStateMachine(System.Runtime.CompilerServices.IAsyncStateMachine)">
      <summary>將產生器與指定的狀態機器產生關聯。</summary>
      <param name="stateMachine">要和產生器產生關聯的狀態機器執行個體。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">狀態電腦先前已經設定。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.Start``1(``0@)">
      <summary>開始執行具有相關聯狀態機器的產生器。</summary>
      <param name="stateMachine">狀態機器執行個體，由參考傳遞。</param>
      <typeparam name="TStateMachine">狀態機器的類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" /> 為 null。</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.Task">
      <summary>取得此產生器的工作。</summary>
      <returns>此產生器的工作。</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.AsyncVoidMethodBuilder">
      <summary>表示非同步方法產生器，不會傳回值。</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitOnCompleted``2(``0@,``1@)">
      <summary>排程狀態機器以在指定的 awaiter 完成時繼續下一個動作。</summary>
      <param name="awaiter">awaiter。</param>
      <param name="stateMachine">狀態機器。</param>
      <typeparam name="TAwaiter">awaiter 的型別。</typeparam>
      <typeparam name="TStateMachine">狀態機器的類型。</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted``2(``0@,``1@)">
      <summary>排程狀態機器以在指定的 awaiter 完成時繼續下一個動作。可以從部分信任程式碼呼叫這個方法。</summary>
      <param name="awaiter">awaiter。</param>
      <param name="stateMachine">狀態機器。</param>
      <typeparam name="TAwaiter">awaiter 的型別。</typeparam>
      <typeparam name="TStateMachine">狀態機器的類型。</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Create">
      <summary>建立 <see cref="T:System.Runtime.CompilerServices.AsyncVoidMethodBuilder" /> 類別的執行個體。</summary>
      <returns>產生器的新執行個體。</returns>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.SetException(System.Exception)">
      <summary>將例外狀況繫結至方法建立幫手。</summary>
      <param name="exception">要繫結的例外狀況。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="exception" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">產生器未初始化。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.SetResult">
      <summary>將方法產生器標記為成功完成。</summary>
      <exception cref="T:System.InvalidOperationException">產生器未初始化。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.SetStateMachine(System.Runtime.CompilerServices.IAsyncStateMachine)">
      <summary>將產生器與指定的狀態機器產生關聯。</summary>
      <param name="stateMachine">要和產生器產生關聯的狀態機器執行個體。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">狀態電腦先前已經設定。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start``1(``0@)">
      <summary>開始執行具有相關聯狀態機器的產生器。</summary>
      <param name="stateMachine">狀態機器執行個體，由參考傳遞。</param>
      <typeparam name="TStateMachine">狀態機器的類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" /> 為 null。</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable">
      <summary>提供 awaitable 物件，啟用工作上之設定的等候。</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.GetAwaiter">
      <summary>傳回這個 awaitable 物件的 awaiter。</summary>
      <returns>awaiter。</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1">
      <summary>提供 awaitable 物件，啟用工作上之設定的等候。</summary>
      <typeparam name="TResult">這個 <see cref="T:System.Threading.Tasks.Task`1" /> 所產生之結果的型別。</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.GetAwaiter">
      <summary>傳回這個 awaitable 物件的 awaiter。</summary>
      <returns>awaiter。</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter">
      <summary>提供 awaitable 物件 (<see cref="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1" />) 的 awaiter。</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter.GetResult">
      <summary>結束等候已完成的工作。</summary>
      <returns>已完成的工作的結果。</returns>
      <exception cref="T:System.NullReferenceException">awaiter 未正確初始化。</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">工作已取消。</exception>
      <exception cref="T:System.Exception">以出錯狀態完成的工作。</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter.IsCompleted">
      <summary>取得值，指出等候的工作是否已完成。</summary>
      <returns>如果等候的工作已完成，則為 true，否則為 false。</returns>
      <exception cref="T:System.NullReferenceException">awaiter 未正確初始化。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter.OnCompleted(System.Action)">
      <summary>排程此 awaiter 相關工作的接續動作。</summary>
      <param name="continuation">等候作業完成時要叫用的動作。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> 引數為 null。</exception>
      <exception cref="T:System.NullReferenceException">awaiter 未正確初始化。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter.UnsafeOnCompleted(System.Action)">
      <summary>排程此 awaiter 相關工作的接續動作。</summary>
      <param name="continuation">等候作業完成時要叫用的動作。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> 引數為 null。</exception>
      <exception cref="T:System.NullReferenceException">awaiter 未正確初始化。</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter">
      <summary>提供 awaitable (<see cref="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable" />) 物件的 awaiter。</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter.GetResult">
      <summary>結束等候已完成的工作。</summary>
      <exception cref="T:System.NullReferenceException">awaiter 未正確初始化。</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">工作已取消。</exception>
      <exception cref="T:System.Exception">以出錯狀態完成的工作。</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter.IsCompleted">
      <summary>取得值，指出等候的工作是否已完成。</summary>
      <returns>如果等候的工作已完成，則為 true，否則為 false。</returns>
      <exception cref="T:System.NullReferenceException">awaiter 未正確初始化。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter.OnCompleted(System.Action)">
      <summary>排程此 awaiter 相關工作的接續動作。</summary>
      <param name="continuation">等候作業完成時要叫用的動作。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> 引數為 null。</exception>
      <exception cref="T:System.NullReferenceException">awaiter 未正確初始化。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter.UnsafeOnCompleted(System.Action)">
      <summary>排程此 awaiter 相關工作的接續動作。</summary>
      <param name="continuation">等候作業完成時要叫用的動作。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> 引數為 null。</exception>
      <exception cref="T:System.NullReferenceException">awaiter 未正確初始化。</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.IAsyncStateMachine">
      <summary>表示針對非同步方法所產生的狀態機器。這個型別僅供編譯器使用。</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.IAsyncStateMachine.MoveNext">
      <summary>將狀態機器移到下一個狀態。</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.IAsyncStateMachine.SetStateMachine(System.Runtime.CompilerServices.IAsyncStateMachine)">
      <summary>使用堆積配置的複本設定狀態機器。</summary>
      <param name="stateMachine">堆積配置的複本。</param>
    </member>
    <member name="T:System.Runtime.CompilerServices.ICriticalNotifyCompletion">
      <summary>表示在 await 作業完成時排程接續符號的 awaiter。</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.ICriticalNotifyCompletion.UnsafeOnCompleted(System.Action)">
      <summary>排程執行個體完成時所叫用的接續動作。</summary>
      <param name="continuation">作業完成時要叫用的動作。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> 引數是 null (在 Visual Basic 中為 Nothing)。</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.INotifyCompletion">
      <summary>表示在作業完成時排程接續符號的作業。</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.INotifyCompletion.OnCompleted(System.Action)">
      <summary>排程執行個體完成時所叫用的接續動作。</summary>
      <param name="continuation">作業完成時要叫用的動作。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> 引數是 null (在 Visual Basic 中為 Nothing)。</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.TaskAwaiter">
      <summary>提供等候非同步工作完成的物件。</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter.GetResult">
      <summary>結束對非同步工作完成的等候。</summary>
      <exception cref="T:System.NullReferenceException">
        <see cref="T:System.Runtime.CompilerServices.TaskAwaiter" />物件未正確初始化。</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">工作已取消。</exception>
      <exception cref="T:System.Exception">完成的工作處於 <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> 狀態。</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.TaskAwaiter.IsCompleted">
      <summary>取得值，指出非同步工作是否已經完成。</summary>
      <returns>如果工作已完成，則為 true，否則為 false。</returns>
      <exception cref="T:System.NullReferenceException">
        <see cref="T:System.Runtime.CompilerServices.TaskAwaiter" />物件未正確初始化。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter.OnCompleted(System.Action)">
      <summary>設定 <see cref="T:System.Runtime.CompilerServices.TaskAwaiter" /> 物件停止等待非同步工作完成時要執行時的動作。</summary>
      <param name="continuation">等候作業完成時要執行的動作。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> 為 null。</exception>
      <exception cref="T:System.NullReferenceException">
        <see cref="T:System.Runtime.CompilerServices.TaskAwaiter" />物件未正確初始化。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter.UnsafeOnCompleted(System.Action)">
      <summary>排程此 awaiter 相關聯的非同步工作的接續動作。</summary>
      <param name="continuation">等候作業完成時要叫用的動作。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">awaiter 未正確初始化。</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.TaskAwaiter`1">
      <summary>表示等候非同步工作完成，並提供結果參數的物件。</summary>
      <typeparam name="TResult">工作的結果。</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter`1.GetResult">
      <summary>結束對非同步工作完成的等候。</summary>
      <returns>已完成的工作的結果。</returns>
      <exception cref="T:System.NullReferenceException">
        <see cref="T:System.Runtime.CompilerServices.TaskAwaiter`1" />物件未正確初始化。</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">工作已取消。</exception>
      <exception cref="T:System.Exception">完成的工作處於 <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> 狀態。</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.TaskAwaiter`1.IsCompleted">
      <summary>取得值，指出非同步工作是否已經完成。</summary>
      <returns>如果工作已完成，則為 true，否則為 false。</returns>
      <exception cref="T:System.NullReferenceException">
        <see cref="T:System.Runtime.CompilerServices.TaskAwaiter`1" />物件未正確初始化。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter`1.OnCompleted(System.Action)">
      <summary>設定 <see cref="T:System.Runtime.CompilerServices.TaskAwaiter`1" /> 物件停止等待非同步工作完成時要執行時的動作。</summary>
      <param name="continuation">等候作業完成時要執行的動作。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> 為 null。</exception>
      <exception cref="T:System.NullReferenceException">
        <see cref="T:System.Runtime.CompilerServices.TaskAwaiter`1" />物件未正確初始化。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter`1.UnsafeOnCompleted(System.Action)">
      <summary>排程此 awaiter 相關聯的非同步工作的接續動作。</summary>
      <param name="continuation">等候作業完成時要叫用的動作。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">awaiter 未正確初始化。</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.YieldAwaitable">
      <summary>以非同步方式切換到目標環境時，提供等候的內容。</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.YieldAwaitable.GetAwaiter">
      <summary>擷取此類別執行個體的 <see cref="T:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter" /> 物件。</summary>
      <returns>物件，用來監視非同步作業的完成。</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter">
      <summary>提供要切換至目標環境的 awaiter。</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter.GetResult">
      <summary>結束等候作業。</summary>
    </member>
    <member name="P:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter.IsCompleted">
      <summary>取得值，指出是否不需要 yield。</summary>
      <returns>永遠為 false，這表示 <see cref="T:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter" /> 永遠都要有 yield。</returns>
    </member>
    <member name="M:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter.OnCompleted(System.Action)">
      <summary>設定要叫用的接續符號。</summary>
      <param name="continuation">以非同步方式叫用的動作。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> 為 null。</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter.UnsafeOnCompleted(System.Action)">
      <summary>將 <paramref name="continuation" /> 回傳至目前的內容。</summary>
      <param name="continuation">以非同步方式叫用的動作。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> 引數為 null。</exception>
    </member>
    <member name="T:System.Threading.CancellationToken">
      <summary>傳播通知，表示應該取消作業。</summary>
    </member>
    <member name="M:System.Threading.CancellationToken.#ctor(System.Boolean)">
      <summary>初始化 <see cref="T:System.Threading.CancellationToken" />。</summary>
      <param name="canceled">語彙基元的已取消狀態。</param>
    </member>
    <member name="P:System.Threading.CancellationToken.CanBeCanceled">
      <summary>取得這個語彙基元是否能夠處於已取消狀態。</summary>
      <returns>如果這個語彙基元能夠處於已取消狀態，則為 true；否則為 false。</returns>
    </member>
    <member name="M:System.Threading.CancellationToken.Equals(System.Object)">
      <summary>判斷目前的 <see cref="T:System.Threading.CancellationToken" /> 執行個體與指定的 <see cref="T:System.Object" /> 是否相等。</summary>
      <returns>如果 <paramref name="other" /> 是 <see cref="T:System.Threading.CancellationToken" />，且兩個執行個體相等，則為 true；否則為 false。如果兩個語彙基元與同一個 <see cref="T:System.Threading.CancellationTokenSource" /> 相關聯，或兩者都是從公用 CancellationToken 建構函式所建構且其 <see cref="P:System.Threading.CancellationToken.IsCancellationRequested" /> 值相等，則這兩個語彙基元是相等的。</returns>
      <param name="other">要與這個執行個體比較的其他物件。</param>
      <exception cref="T:System.ObjectDisposedException">An associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.Equals(System.Threading.CancellationToken)">
      <summary>判斷目前的 <see cref="T:System.Threading.CancellationToken" /> 執行個體與指定的語彙基元是否相等。</summary>
      <returns>如果執行個體相等，則為 true；否則為 false。如果兩個語彙基元與同一個 <see cref="T:System.Threading.CancellationTokenSource" /> 相關聯，或兩者都是從公用 CancellationToken 建構函式所建構且其 <see cref="P:System.Threading.CancellationToken.IsCancellationRequested" /> 值相等，則這兩個語彙基元是相等的。</returns>
      <param name="other">要與這個執行個體比較的其他 <see cref="T:System.Threading.CancellationToken" />。</param>
    </member>
    <member name="M:System.Threading.CancellationToken.GetHashCode">
      <summary>做為 <see cref="T:System.Threading.CancellationToken" /> 的雜湊函式。</summary>
      <returns>目前 <see cref="T:System.Threading.CancellationToken" /> 執行個體的雜湊碼。</returns>
    </member>
    <member name="P:System.Threading.CancellationToken.IsCancellationRequested">
      <summary>取得是否已要求取消這個語彙基元。</summary>
      <returns>如果已要求取消這個語彙基元，則為 true；否則為 false。</returns>
    </member>
    <member name="P:System.Threading.CancellationToken.None">
      <summary>傳回空的 <see cref="T:System.Threading.CancellationToken" /> 值。</summary>
      <returns>空的取消語彙基元。</returns>
    </member>
    <member name="M:System.Threading.CancellationToken.op_Equality(System.Threading.CancellationToken,System.Threading.CancellationToken)">
      <summary>判斷兩個 <see cref="T:System.Threading.CancellationToken" /> 執行個體是否相等。</summary>
      <returns>如果執行個體相等，則為 true；否則為 false。</returns>
      <param name="left">第一個執行個體。</param>
      <param name="right">第二個執行個體。</param>
      <exception cref="T:System.ObjectDisposedException">An associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.op_Inequality(System.Threading.CancellationToken,System.Threading.CancellationToken)">
      <summary>判斷兩個 <see cref="T:System.Threading.CancellationToken" /> 執行個體是否不相等。</summary>
      <returns>如果執行個體不相等，則為 true；否則為 false。</returns>
      <param name="left">第一個執行個體。</param>
      <param name="right">第二個執行個體。</param>
      <exception cref="T:System.ObjectDisposedException">An associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.Register(System.Action)">
      <summary>註冊委派，當取消這個 <see cref="T:System.Threading.CancellationToken" /> 時將會呼叫該委派。</summary>
      <returns>可用來取消註冊回呼的 <see cref="T:System.Threading.CancellationTokenRegistration" /> 執行個體。</returns>
      <param name="callback">取消 <see cref="T:System.Threading.CancellationToken" /> 時所要執行的委派。</param>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="callback" /> is null.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.Register(System.Action,System.Boolean)">
      <summary>註冊委派，當取消這個 <see cref="T:System.Threading.CancellationToken" /> 時將會呼叫該委派。</summary>
      <returns>可用來取消註冊回呼的 <see cref="T:System.Threading.CancellationTokenRegistration" /> 執行個體。</returns>
      <param name="callback">取消 <see cref="T:System.Threading.CancellationToken" /> 時所要執行的委派。</param>
      <param name="useSynchronizationContext">布林值，表示是否要擷取目前的 <see cref="T:System.Threading.SynchronizationContext" /> 並在叫用 <paramref name="callback" /> 時使用它。</param>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="callback" /> is null.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.Register(System.Action{System.Object},System.Object)">
      <summary>註冊委派，當取消這個 <see cref="T:System.Threading.CancellationToken" /> 時將會呼叫該委派。</summary>
      <returns>可用來取消註冊回呼的 <see cref="T:System.Threading.CancellationTokenRegistration" /> 執行個體。</returns>
      <param name="callback">取消 <see cref="T:System.Threading.CancellationToken" /> 時所要執行的委派。</param>
      <param name="state">當叫用委派時，要傳遞給 <paramref name="callback" /> 的狀態。這可能是 null。</param>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="callback" /> is null.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.Register(System.Action{System.Object},System.Object,System.Boolean)">
      <summary>註冊委派，當取消這個 <see cref="T:System.Threading.CancellationToken" /> 時將會呼叫該委派。</summary>
      <returns>可用來取消註冊回呼的 <see cref="T:System.Threading.CancellationTokenRegistration" /> 執行個體。</returns>
      <param name="callback">取消 <see cref="T:System.Threading.CancellationToken" /> 時所要執行的委派。</param>
      <param name="state">當叫用委派時，要傳遞給 <paramref name="callback" /> 的狀態。這可能是 null。</param>
      <param name="useSynchronizationContext">布林值，表示是否要擷取目前的 <see cref="T:System.Threading.SynchronizationContext" /> 並在叫用 <paramref name="callback" /> 時使用它。</param>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="callback" /> is null.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.ThrowIfCancellationRequested">
      <summary>如果已要求取消這個語彙基元，則擲回 <see cref="T:System.OperationCanceledException" />。</summary>
      <exception cref="T:System.OperationCanceledException">The token has had cancellation requested.</exception>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
    </member>
    <member name="P:System.Threading.CancellationToken.WaitHandle">
      <summary>取得取消語彙基元時所發出的 <see cref="T:System.Threading.WaitHandle" /> 信號。</summary>
      <returns>取消語彙基元時所發出的 <see cref="T:System.Threading.WaitHandle" /> 信號。</returns>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
    </member>
    <member name="T:System.Threading.CancellationTokenRegistration">
      <summary>表示已向 <see cref="T:System.Threading.CancellationToken" /> 登錄的回呼委派。</summary>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.Dispose">
      <summary>將 <see cref="T:System.Threading.CancellationTokenRegistration" /> 類別目前的執行個體所使用的資源全部釋出。</summary>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.Equals(System.Object)">
      <summary>判斷目前的 <see cref="T:System.Threading.CancellationTokenRegistration" /> 執行個體與指定的 <see cref="T:System.Threading.CancellationTokenRegistration" /> 是否相等。</summary>
      <returns>如果這個執行個體和 <paramref name="obj" /> 相等則為 True，否則為 False。如果兩個 <see cref="T:System.Threading.CancellationTokenRegistration" /> 執行個體都參考 <see cref="T:System.Threading.CancellationToken" /> 的相同 Register 方法之單一呼叫的輸出，則這兩個執行個體相等。</returns>
      <param name="obj">要與這個執行個體比較的其他物件。</param>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.Equals(System.Threading.CancellationTokenRegistration)">
      <summary>判斷目前的 <see cref="T:System.Threading.CancellationTokenRegistration" /> 執行個體與指定的 <see cref="T:System.Threading.CancellationTokenRegistration" /> 是否相等。</summary>
      <returns>如果這個執行個體和 <paramref name="other" /> 相等則為 True，否則為 False。 如果兩個 <see cref="T:System.Threading.CancellationTokenRegistration" /> 執行個體都參考 <see cref="T:System.Threading.CancellationToken" /> 的相同 Register 方法之單一呼叫的輸出，則這兩個執行個體相等。</returns>
      <param name="other">要與這個執行個體比較的其他 <see cref="T:System.Threading.CancellationTokenRegistration" />。</param>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.GetHashCode">
      <summary>做為 <see cref="T:System.Threading.CancellationTokenRegistration" /> 的雜湊函式。</summary>
      <returns>目前 <see cref="T:System.Threading.CancellationTokenRegistration" /> 執行個體的雜湊碼。</returns>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.op_Equality(System.Threading.CancellationTokenRegistration,System.Threading.CancellationTokenRegistration)">
      <summary>判斷兩個 <see cref="T:System.Threading.CancellationTokenRegistration" /> 執行個體是否相等。</summary>
      <returns>如果執行個體相等，則為 true，否則為 false。</returns>
      <param name="left">第一個執行個體。</param>
      <param name="right">第二個執行個體。</param>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.op_Inequality(System.Threading.CancellationTokenRegistration,System.Threading.CancellationTokenRegistration)">
      <summary>判斷兩個 <see cref="T:System.Threading.CancellationTokenRegistration" /> 執行個體是否不相等。</summary>
      <returns>如果執行個體不相等則為 true，否則為 false。</returns>
      <param name="left">第一個執行個體。</param>
      <param name="right">第二個執行個體。</param>
    </member>
    <member name="T:System.Threading.CancellationTokenSource">
      <summary>向 <see cref="T:System.Threading.CancellationToken" /> 發出訊號，表示應該將它取消。</summary>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.#ctor">
      <summary>初始化 <see cref="T:System.Threading.CancellationTokenSource" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.#ctor(System.Int32)">
      <summary>初始化 <see cref="T:System.Threading.CancellationTokenSource" /> 類別的新執行個體，過了指定的延遲時間 (以毫秒為單位) 之後，將會取消這個執行個體。</summary>
      <param name="millisecondsDelay">取消此 <see cref="T:System.Threading.CancellationTokenSource" /> 之前要等候的時間間隔 (毫秒)。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsDelay" /> is less than -1. </exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.#ctor(System.TimeSpan)">
      <summary>初始化 <see cref="T:System.Threading.CancellationTokenSource" /> 類別的新執行個體，在指定的時間範圍之後，將會取消這個執行個體。</summary>
      <param name="delay">取消此 <see cref="T:System.Threading.CancellationTokenSource" /> 之前要等候的時間間隔 (毫秒)。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="delay" />.<see cref="P:System.TimeSpan.TotalMilliseconds" /> is less than -1 or greater than <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.Cancel">
      <summary>傳遞取消要求。</summary>
      <exception cref="T:System.ObjectDisposedException">This <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.AggregateException">An aggregate exception containing all the exceptions thrown by the registered callbacks on the associated <see cref="T:System.Threading.CancellationToken" />.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.Cancel(System.Boolean)">
      <summary>傳遞取消要求，並指定是否應處理其餘回呼和可取消的作業。</summary>
      <param name="throwOnFirstException">如果應該立即傳播例外狀況，則為 true；否則為 false。</param>
      <exception cref="T:System.ObjectDisposedException">This <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.AggregateException">An aggregate exception containing all the exceptions thrown by the registered callbacks on the associated <see cref="T:System.Threading.CancellationToken" />.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.CancelAfter(System.Int32)">
      <summary>針對這個 <see cref="T:System.Threading.CancellationTokenSource" />，排定在指定毫秒數之後的取消作業。</summary>
      <param name="millisecondsDelay">在取消此 <see cref="T:System.Threading.CancellationTokenSource" /> 之前，所要等待的時間範圍。</param>
      <exception cref="T:System.ObjectDisposedException">The exception thrown when this <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The exception thrown when <paramref name="millisecondsDelay" /> is less than -1.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.CancelAfter(System.TimeSpan)">
      <summary>針對這個 <see cref="T:System.Threading.CancellationTokenSource" />，排定在指定時間範圍之後的取消作業。</summary>
      <param name="delay">在取消此 <see cref="T:System.Threading.CancellationTokenSource" /> 之前，所要等待的時間範圍。</param>
      <exception cref="T:System.ObjectDisposedException">The exception thrown when this <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The exception that is thrown when <paramref name="delay" /> is less than -1 or greater than Int32.MaxValue.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.CreateLinkedTokenSource(System.Threading.CancellationToken,System.Threading.CancellationToken)">
      <summary>建立 <see cref="T:System.Threading.CancellationTokenSource" />，且設定只要有任何來源語彙基元處於已取消狀態，它就會處於已取消狀態。</summary>
      <returns>已連結至來源語彙基元的 <see cref="T:System.Threading.CancellationTokenSource" />。</returns>
      <param name="token1">要觀察的第一個取消語彙基元。</param>
      <param name="token2">要觀察的第二個取消語彙基元。</param>
      <exception cref="T:System.ObjectDisposedException">A <see cref="T:System.Threading.CancellationTokenSource" /> associated with one of the source tokens has been disposed.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.CreateLinkedTokenSource(System.Threading.CancellationToken[])">
      <summary>建立 <see cref="T:System.Threading.CancellationTokenSource" />，且設定只要指定的陣列中有任何來源語彙基元處於已取消狀態，它就會處於已取消狀態。</summary>
      <returns>已連結至來源語彙基元的 <see cref="T:System.Threading.CancellationTokenSource" />。</returns>
      <param name="tokens">包含要觀察之取消語彙基元執行個體的陣列。</param>
      <exception cref="T:System.ObjectDisposedException">A <see cref="T:System.Threading.CancellationTokenSource" /> associated with one of the source tokens has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tokens" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tokens" /> is empty.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.Dispose">
      <summary>將 <see cref="T:System.Threading.CancellationTokenSource" /> 類別目前的執行個體所使用的資源全部釋出。</summary>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.Dispose(System.Boolean)">
      <summary>釋放 <see cref="T:System.Threading.CancellationTokenSource" /> 類別所使用的 Unmanaged 資源，並選擇性地釋放 Managed 資源。</summary>
      <param name="disposing">true 表示釋放 Managed 和 Unmanaged 資源，false 則表示只釋放 Unmanaged 資源。</param>
    </member>
    <member name="P:System.Threading.CancellationTokenSource.IsCancellationRequested">
      <summary>取得是否已為這個 <see cref="T:System.Threading.CancellationTokenSource" /> 要求取消。</summary>
      <returns>如果已為這個 <see cref="T:System.Threading.CancellationTokenSource" /> 要求取消，則為 true；否則為 false。</returns>
    </member>
    <member name="P:System.Threading.CancellationTokenSource.Token">
      <summary>取得與這個 <see cref="T:System.Threading.CancellationToken" /> 關聯的 <see cref="T:System.Threading.CancellationTokenSource" />。</summary>
      <returns>與這個 <see cref="T:System.Threading.CancellationToken" /> 相關聯的 <see cref="T:System.Threading.CancellationTokenSource" />。</returns>
      <exception cref="T:System.ObjectDisposedException">The token source has been disposed.</exception>
    </member>
    <member name="T:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair">
      <summary>提供工作排程器，以協調執行工作，並確保並行工作可以同時執行，而獨佔式工作永遠不可能。</summary>
    </member>
    <member name="M:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.#ctor">
      <summary>初始化 <see cref="T:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.#ctor(System.Threading.Tasks.TaskScheduler)">
      <summary>初始化 <see cref="T:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair" /> 類別的新執行個體，這個執行個體會以指定的排程器為目標。</summary>
      <param name="taskScheduler">應該執行這一組的目標排程器。</param>
    </member>
    <member name="M:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.#ctor(System.Threading.Tasks.TaskScheduler,System.Int32)">
      <summary>初始化 <see cref="T:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair" /> 類別的新執行個體，這個執行個體以具有最大並行層級的指定排程器為目標。</summary>
      <param name="taskScheduler">應該執行這一組的目標排程器。</param>
      <param name="maxConcurrencyLevel">要同時執行的工作的最大數目。</param>
    </member>
    <member name="M:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.#ctor(System.Threading.Tasks.TaskScheduler,System.Int32,System.Int32)">
      <summary>初始化 <see cref="T:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair" /> 類別的新執行個體，這個執行個體以指定的排程器為目標，其具有最大並行層級與可當做一個單位來進行處理的排定工作最大數目。</summary>
      <param name="taskScheduler">應該執行這一組的目標排程器。</param>
      <param name="maxConcurrencyLevel">要同時執行的工作的最大數目。</param>
      <param name="maxItemsPerTask">要針對此配對所使用的每個基礎排定工作進行處理的工作的最大數目。</param>
    </member>
    <member name="M:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.Complete">
      <summary>通知排程器配對，它不應該接受任何更多的工作。</summary>
    </member>
    <member name="P:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.Completion">
      <summary>取得 <see cref="T:System.Threading.Tasks.Task" />，當排程器已完成處理時，它就會完成。</summary>
      <returns>排程器完成處理時將完成的非同步作業。</returns>
    </member>
    <member name="P:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.ConcurrentScheduler">
      <summary>取得 <see cref="T:System.Threading.Tasks.TaskScheduler" />，這個物件可以用來排程工作至此組，而這些工作可能會與其他工作同時在此組上執行。</summary>
      <returns>可用來同時排程工作的物件。</returns>
    </member>
    <member name="P:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.ExclusiveScheduler">
      <summary>取得 <see cref="T:System.Threading.Tasks.TaskScheduler" />，這個物件可以用來排程工作至此組，相對於其他工作，這些工作必須以獨佔方式在此組上執行。</summary>
      <returns>可用來排定無法與其他工作同時執行之工作的物件。</returns>
    </member>
    <member name="T:System.Threading.Tasks.Task">
      <summary>表示非同步作業。若要瀏覽此類型的 .NET Framework 原始程式碼，請參閱 參考來源。</summary>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action)">
      <summary>使用指定的動作，初始化新的 <see cref="T:System.Threading.Tasks.Task" />。</summary>
      <param name="action">委派，表示工作中要執行的程式碼。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action,System.Threading.CancellationToken)">
      <summary>使用指定的動作和 <see cref="T:System.Threading.Tasks.Task" />，初始化新的 <see cref="T:System.Threading.CancellationToken" />。</summary>
      <param name="action">委派，表示工作中要執行的程式碼。</param>
      <param name="cancellationToken">新工作將會觀察的 <see cref="T:System.Threading.CancellationToken" />。</param>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions)">
      <summary>使用指定的動作和建立選項，初始化新的 <see cref="T:System.Threading.Tasks.Task" />。</summary>
      <param name="action">委派，表示工作中要執行的程式碼。</param>
      <param name="cancellationToken">新工作將會觀察的 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />。</param>
      <param name="creationOptions">
        <see cref="T:System.Threading.Tasks.TaskCreationOptions" />，用於自訂工作的行為。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action,System.Threading.Tasks.TaskCreationOptions)">
      <summary>使用指定的動作和建立選項，初始化新的 <see cref="T:System.Threading.Tasks.Task" />。</summary>
      <param name="action">委派，表示工作中要執行的程式碼。</param>
      <param name="creationOptions">
        <see cref="T:System.Threading.Tasks.TaskCreationOptions" />，用於自訂工作的行為。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action{System.Object},System.Object)">
      <summary>使用指定的動作和狀態，初始化新的 <see cref="T:System.Threading.Tasks.Task" />。</summary>
      <param name="action">委派，表示工作中要執行的程式碼。</param>
      <param name="state">物件，表示動作所需的資料。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action{System.Object},System.Object,System.Threading.CancellationToken)">
      <summary>使用指定的動作、狀態和選項，初始化新的 <see cref="T:System.Threading.Tasks.Task" />。</summary>
      <param name="action">委派，表示工作中要執行的程式碼。</param>
      <param name="state">物件，表示動作所需的資料。</param>
      <param name="cancellationToken">新工作將會觀察的 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action{System.Object},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions)">
      <summary>使用指定的動作、狀態和選項，初始化新的 <see cref="T:System.Threading.Tasks.Task" />。</summary>
      <param name="action">委派，表示工作中要執行的程式碼。</param>
      <param name="state">物件，表示動作所需的資料。</param>
      <param name="cancellationToken">新工作將會觀察的 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />。</param>
      <param name="creationOptions">
        <see cref="T:System.Threading.Tasks.TaskCreationOptions" />，用於自訂工作的行為。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action{System.Object},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>使用指定的動作、狀態和選項，初始化新的 <see cref="T:System.Threading.Tasks.Task" />。</summary>
      <param name="action">委派，表示工作中要執行的程式碼。</param>
      <param name="state">物件，表示動作所需的資料。</param>
      <param name="creationOptions">
        <see cref="T:System.Threading.Tasks.TaskCreationOptions" />，用於自訂工作的行為。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
    </member>
    <member name="P:System.Threading.Tasks.Task.AsyncState">
      <summary>取得建立 <see cref="T:System.Threading.Tasks.Task" /> 時提供的狀態物件，如果未提供則為 null。</summary>
      <returns>
        <see cref="T:System.Object" />，代表工作建立時傳入工作的狀態資料。</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.CompletedTask">
      <summary>取得已成功完成的工作。</summary>
      <returns>成功完成的工作。</returns>
    </member>
    <member name="M:System.Threading.Tasks.Task.ConfigureAwait(System.Boolean)">
      <summary>設定用來等候這個 <see cref="T:System.Threading.Tasks.Task" /> 的 awaiter。</summary>
      <returns>等候這項工作所用的物件。</returns>
      <param name="continueOnCapturedContext">true 表示嘗試將接續封送處理回原始擷取的內容，否則為 false。</param>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task})">
      <summary>建立當目標 <see cref="T:System.Threading.Tasks.Task" /> 完成時非同步執行的接續。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">當 <see cref="T:System.Threading.Tasks.Task" /> 完成時要執行的動作。執行時，會將已完成的工作當做引數傳遞給委派。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task},System.Threading.CancellationToken)">
      <summary>建立當目標 <see cref="T:System.Threading.Tasks.Task" /> 完成時，會接收取消語彙基元並非同步執行的接續。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">當 <see cref="T:System.Threading.Tasks.Task" /> 完成時要執行的動作。執行時，會將已完成的工作當做引數傳遞給委派。</param>
      <param name="cancellationToken">要指派給新接續工作的 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created the token has already been disposed. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>建立當目標工作完成時，會根據指定之 <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> 執行的接續。接續會接收取消語彙基元，並使用指定的排程器。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">要根據指定的 <paramref name="continuationOptions" /> 執行的動作。執行時，會將已完成的工作當做引數傳遞給委派。</param>
      <param name="cancellationToken">要指派給新接續工作的 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />。</param>
      <param name="continuationOptions">接續工作的排程時間及其行為方式的適用選項。這包含準則 (如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />) 和執行選項 (如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />)。</param>
      <param name="scheduler">要與接續工作產生關聯且於執行時使用的 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created the token has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>建立當目標工作完成時，會根據指定之 <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> 執行的接續。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">要根據指定的 <paramref name="continuationOptions" /> 執行的動作。執行時，會將已完成的工作當做引數傳遞給委派。</param>
      <param name="continuationOptions">接續工作的排程時間及其行為方式的適用選項。這包含準則 (如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />) 和執行選項 (如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />)。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task},System.Threading.Tasks.TaskScheduler)">
      <summary>建立當目標 <see cref="T:System.Threading.Tasks.Task" /> 完成時非同步執行的接續。接續會使用指定的排程器。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">當 <see cref="T:System.Threading.Tasks.Task" /> 完成時要執行的動作。執行時，會將已完成的工作當做引數傳遞給委派。</param>
      <param name="scheduler">要與接續工作產生關聯且於執行時使用的 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null. -or-The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task,System.Object},System.Object)">
      <summary>建立當目標 <see cref="T:System.Threading.Tasks.Task" /> 完成時，會接收呼叫端提供的狀態資訊及執行的接續。</summary>
      <returns>新的接續工作。</returns>
      <param name="continuationAction">當工作完成時要執行的動作。執行時，會將已完成的工作和呼叫端提供的狀態物件當做引數傳遞給委派。</param>
      <param name="state">物件，表示接續動作所使用的資料。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task,System.Object},System.Object,System.Threading.CancellationToken)">
      <summary>建立當目標 <see cref="T:System.Threading.Tasks.Task" /> 完成時，會接收呼叫端提供的狀態資訊和取消語彙基元，並非同步執行的接續。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">當 <see cref="T:System.Threading.Tasks.Task" /> 完成時要執行的動作。當執行時，完成的工作會傳遞至委派，並以呼叫端提供狀態的物件做為引數。</param>
      <param name="state">物件，表示接續動作所使用的資料。</param>
      <param name="cancellationToken">要指派給新接續工作的 <see cref="T:System.Threading.CancellationToken" />。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task,System.Object},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>建立當目標 <see cref="T:System.Threading.Tasks.Task" /> 完成時，會接收呼叫端提供的狀態資訊和取消語彙基元，並執行的接續。接續會根據一組指定的條件執行，並使用指定的排程器。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">當 <see cref="T:System.Threading.Tasks.Task" /> 完成時要執行的動作。當執行時，完成的工作會傳遞至委派，並以呼叫端提供狀態的物件做為引數。</param>
      <param name="state">物件，表示接續動作所使用的資料。</param>
      <param name="cancellationToken">要指派給新接續工作的 <see cref="T:System.Threading.CancellationToken" />。</param>
      <param name="continuationOptions">接續工作的排程時間及其行為方式的適用選項。這包含準則 (如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />) 和執行選項 (如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />)。</param>
      <param name="scheduler">要與接續工作產生關聯且於執行時使用的 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task,System.Object},System.Object,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>建立當目標 <see cref="T:System.Threading.Tasks.Task" /> 完成時，會接收呼叫端提供的狀態資訊並執行的接續。接續會根據一組指定的條件執行。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">當 <see cref="T:System.Threading.Tasks.Task" /> 完成時要執行的動作。當執行時，完成的工作會傳遞至委派，並以呼叫端提供狀態的物件做為引數。</param>
      <param name="state">物件，表示接續動作所使用的資料。</param>
      <param name="continuationOptions">接續工作的排程時間及其行為方式的適用選項。這包含準則 (如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />) 和執行選項 (如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />)。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task,System.Object},System.Object,System.Threading.Tasks.TaskScheduler)">
      <summary>建立當目標 <see cref="T:System.Threading.Tasks.Task" /> 完成時，會接收呼叫端提供的狀態資訊並非同步執行的接續。接續會使用指定的排程器。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">當 <see cref="T:System.Threading.Tasks.Task" /> 完成時要執行的動作。當執行時，完成的工作會傳遞至委派，並以呼叫端提供狀態的物件做為引數。</param>
      <param name="state">物件，表示接續動作所使用的資料。</param>
      <param name="scheduler">要與接續工作產生關聯且於執行時使用的 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,``0})">
      <summary>建立當目標 <see cref="T:System.Threading.Tasks.Task`1" /> 完成並傳回值時非同步執行的接續。</summary>
      <returns>新的接續工作。</returns>
      <param name="continuationFunction">當 <see cref="T:System.Threading.Tasks.Task`1" /> 完成時要執行的函式。執行時，會將已完成的工作當做引數傳遞給委派。</param>
      <typeparam name="TResult"> 接續作業所產生的結果類型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,``0},System.Threading.CancellationToken)">
      <summary>建立當目標 <see cref="T:System.Threading.Tasks.Task" /> 完成並傳回值時非同步執行的接續。接續會接收取消語彙基元。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">當 <see cref="T:System.Threading.Tasks.Task" /> 完成時要執行的函式。執行時，會將已完成的工作當做引數傳遞給委派。</param>
      <param name="cancellationToken">要指派給新接續工作的 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />。</param>
      <typeparam name="TResult"> 接續作業所產生的結果類型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created the token has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,``0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>建立會根據指定之接續選項執行並傳回值的接續。接續會接收取消語彙基元，並使用指定的排程器。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">要根據指定之 <paramref name="continuationOptions." /> 執行的函式。執行時，會將完成的工作當做引數傳遞給委派。</param>
      <param name="cancellationToken">要指派給新接續工作的 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />。</param>
      <param name="continuationOptions">接續工作的排程時間及其行為方式的適用選項。這包含準則 (如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />) 和執行選項 (如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />)。</param>
      <param name="scheduler">要與接續工作產生關聯且於執行時使用的 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <typeparam name="TResult"> 接續作業所產生的結果類型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created the token has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,``0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>建立會根據指定之接續選項執行並傳回值的接續。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">要根據 <paramref name="continuationOptions" /> 中指定之條件執行的函式。執行時，會將已完成的工作當做引數傳遞給委派。</param>
      <param name="continuationOptions">接續工作的排程時間及其行為方式的適用選項。這包含準則 (如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />) 和執行選項 (如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />)。</param>
      <typeparam name="TResult"> 接續作業所產生的結果類型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,``0},System.Threading.Tasks.TaskScheduler)">
      <summary>建立當目標 <see cref="T:System.Threading.Tasks.Task" /> 完成並傳回值時非同步執行的接續。接續會使用指定的排程器。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">當 <see cref="T:System.Threading.Tasks.Task" /> 完成時要執行的函式。執行時，會將已完成的工作當做引數傳遞給委派。</param>
      <param name="scheduler">要與接續工作產生關聯且於執行時使用的 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <typeparam name="TResult"> 接續作業所產生的結果類型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,System.Object,``0},System.Object)">
      <summary>建立當目標 <see cref="T:System.Threading.Tasks.Task" /> 完成並傳回值時，會接收呼叫端提供的狀態資訊並非同步執行的接續。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">當 <see cref="T:System.Threading.Tasks.Task" /> 完成時要執行的函式。當執行時，完成的工作會傳遞至委派，並以呼叫端提供狀態的物件做為引數。</param>
      <param name="state">物件，表示接續函式所使用的資料。</param>
      <typeparam name="TResult">接續作業所產生的結果類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,System.Object,``0},System.Object,System.Threading.CancellationToken)">
      <summary>建立當目標 <see cref="T:System.Threading.Tasks.Task" /> 完成並傳回值時，會非同步執行的接續。接續會接收呼叫端提供的狀態資訊和取消語彙基元。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">當 <see cref="T:System.Threading.Tasks.Task" /> 完成時要執行的函式。當執行時，完成的工作會傳遞至委派，並以呼叫端提供狀態的物件做為引數。</param>
      <param name="state">物件，表示接續函式所使用的資料。</param>
      <param name="cancellationToken">要指派給新接續工作的 <see cref="T:System.Threading.CancellationToken" />。</param>
      <typeparam name="TResult">接續作業所產生的結果類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,System.Object,``0},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>建立當目標 <see cref="T:System.Threading.Tasks.Task" /> 完成並傳回值時，會根據指定的工作接續選項來執行的接續。接續會接收呼叫端提供的狀態資訊和取消語彙基元，並使用指定的排程器。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">當 <see cref="T:System.Threading.Tasks.Task" /> 完成時要執行的函式。當執行時，完成的工作會傳遞至委派，並以呼叫端提供狀態的物件做為引數。</param>
      <param name="state">物件，表示接續函式所使用的資料。</param>
      <param name="cancellationToken">要指派給新接續工作的 <see cref="T:System.Threading.CancellationToken" />。</param>
      <param name="continuationOptions">接續工作的排程時間及其行為方式的適用選項。這包含準則 (如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />) 和執行選項 (如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />)。</param>
      <param name="scheduler">要與接續工作產生關聯且於執行時使用的 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <typeparam name="TResult">接續作業所產生的結果類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,System.Object,``0},System.Object,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>建立當目標 <see cref="T:System.Threading.Tasks.Task" /> 完成時，會根據指定的工作接續選項來執行的接續。接續會接收呼叫端提供的狀態資訊。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">當 <see cref="T:System.Threading.Tasks.Task" /> 完成時要執行的函式。當執行時，完成的工作會傳遞至委派，並以呼叫端提供狀態的物件做為引數。</param>
      <param name="state">物件，表示接續函式所使用的資料。</param>
      <param name="continuationOptions">接續工作的排程時間及其行為方式的適用選項。這包含準則 (如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />) 和執行選項 (如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />)。</param>
      <typeparam name="TResult">接續作業所產生的結果類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,System.Object,``0},System.Object,System.Threading.Tasks.TaskScheduler)">
      <summary>建立當目標 <see cref="T:System.Threading.Tasks.Task" /> 完成時非同步執行的接續。接續會接收呼叫端提供的狀態資訊，並使用指定的排程器。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">當 <see cref="T:System.Threading.Tasks.Task" /> 完成時要執行的函式。當執行時，完成的工作會傳遞至委派，並以呼叫端提供狀態的物件做為引數。</param>
      <param name="state">物件，表示接續函式所使用的資料。</param>
      <param name="scheduler">要與接續工作產生關聯且於執行時使用的 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <typeparam name="TResult">接續作業所產生的結果類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="P:System.Threading.Tasks.Task.CreationOptions">
      <summary>取得用來建立這個工作的 <see cref="T:System.Threading.Tasks.TaskCreationOptions" />。</summary>
      <returns>用來建立這個工作的 <see cref="T:System.Threading.Tasks.TaskCreationOptions" />。</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.CurrentId">
      <summary>傳回目前執行之 <see cref="T:System.Threading.Tasks.Task" /> 的唯一 ID。</summary>
      <returns>系統指派給目前執行中工作的整數。</returns>
    </member>
    <member name="M:System.Threading.Tasks.Task.Delay(System.Int32)">
      <summary>建立在時間延遲之後才會完成的工作。</summary>
      <returns>代表時間延遲的工作。</returns>
      <param name="millisecondsDelay">完成所傳回工作之前等待的毫秒數；-1 為無限期地等待。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="millisecondsDelay" /> argument is less than -1.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Delay(System.Int32,System.Threading.CancellationToken)">
      <summary>建立在時間延遲之後才會完成的可取消工作。</summary>
      <returns>代表時間延遲的工作。</returns>
      <param name="millisecondsDelay">完成所傳回工作之前等待的毫秒數；-1 為無限期地等待。</param>
      <param name="cancellationToken">將在完成傳回的工作之前檢查的取消語彙基元。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="millisecondsDelay" /> argument is less than -1. </exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled. </exception>
      <exception cref="T:System.ObjectDisposedException">The provided <paramref name="cancellationToken" /> has already been disposed. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Delay(System.TimeSpan)">
      <summary>建立在指定時間間隔之後完成的工作。</summary>
      <returns>代表時間延遲的工作。</returns>
      <param name="delay">完成所傳回工作之前要等待的時間範圍；TimeSpan.FromMilliseconds(-1) 表示無限期地等待。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="delay" /> represents a negative time interval other than TimeSpan.FromMillseconds(-1). -or-The <paramref name="delay" /> argument's <see cref="P:System.TimeSpan.TotalMilliseconds" /> property is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Delay(System.TimeSpan,System.Threading.CancellationToken)">
      <summary>建立在指定時間間隔之後完成的可取消工作。</summary>
      <returns>代表時間延遲的工作。</returns>
      <param name="delay">完成所傳回工作之前要等待的時間範圍；TimeSpan.FromMilliseconds(-1) 表示無限期地等待。</param>
      <param name="cancellationToken">將在完成傳回的工作之前檢查的取消語彙基元。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="delay" /> represents a negative time interval other than TimeSpan.FromMillseconds(-1). -or-The <paramref name="delay" /> argument's <see cref="P:System.TimeSpan.TotalMilliseconds" /> property is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <paramref name="cancellationToken" /> has already been disposed. </exception>
    </member>
    <member name="P:System.Threading.Tasks.Task.Exception">
      <summary>取得導致 <see cref="T:System.AggregateException" /> 不當結束的 <see cref="T:System.Threading.Tasks.Task" />。如果 <see cref="T:System.Threading.Tasks.Task" /> 順利完成，或未擲回任何例外狀況，則這會傳回 null。</summary>
      <returns>導致 <see cref="T:System.AggregateException" /> 不當結束的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.Factory">
      <summary>提供 Factory 方法的存取，這些 Factory 方法用於建立及設定 <see cref="T:System.Threading.Tasks.Task" /> 和 <see cref="T:System.Threading.Tasks.Task`1" /> 執行個體。</summary>
      <returns>Factory 物件，可以建立各種不同的 <see cref="T:System.Threading.Tasks.Task" /> 和 <see cref="T:System.Threading.Tasks.Task`1" /> 物件。</returns>
    </member>
    <member name="M:System.Threading.Tasks.Task.FromCanceled(System.Threading.CancellationToken)">
      <summary>建立以指定的取消語彙基元取消而完成的 <see cref="T:System.Threading.Tasks.Task" />。</summary>
      <returns>取消的工作。</returns>
      <param name="cancellationToken">可用來完成工作的取消語彙基元。</param>
    </member>
    <member name="M:System.Threading.Tasks.Task.FromCanceled``1(System.Threading.CancellationToken)">
      <summary>建立以指定的取消語彙基元取消而完成的 <see cref="T:System.Threading.Tasks.Task`1" />。</summary>
      <returns>取消的工作。</returns>
      <param name="cancellationToken">可用來完成工作的取消語彙基元。</param>
      <typeparam name="TResult">工作傳回的結果的類型。</typeparam>
    </member>
    <member name="M:System.Threading.Tasks.Task.FromException``1(System.Exception)">
      <summary>建立以指定例外狀況完成的 <see cref="T:System.Threading.Tasks.Task`1" />。</summary>
      <returns>發生錯誤的工作。</returns>
      <param name="exception">可用來完成工作的例外狀況。</param>
      <typeparam name="TResult">工作傳回的結果的類型。</typeparam>
    </member>
    <member name="M:System.Threading.Tasks.Task.FromException(System.Exception)">
      <summary>建立以指定例外狀況完成的 <see cref="T:System.Threading.Tasks.Task" />。</summary>
      <returns>發生錯誤的工作。</returns>
      <param name="exception">可用來完成工作的例外狀況。</param>
    </member>
    <member name="M:System.Threading.Tasks.Task.FromResult``1(``0)">
      <summary>建立已成功完成且具有指定之結果的 <see cref="T:System.Threading.Tasks.Task`1" />。</summary>
      <returns>成功完成的工作。</returns>
      <param name="result">要儲存到完成的工作的結果。</param>
      <typeparam name="TResult">工作傳回的結果的類型。</typeparam>
    </member>
    <member name="M:System.Threading.Tasks.Task.GetAwaiter">
      <summary>取得用來等候這個 <see cref="T:System.Threading.Tasks.Task" /> 的 awaiter。</summary>
      <returns>awaiter 執行個體。</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.Id">
      <summary>取得這個 <see cref="T:System.Threading.Tasks.Task" /> 執行個體的唯一 ID。</summary>
      <returns>系統指派給這個工作執行個體的整數。</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.IsCanceled">
      <summary>取得這個 <see cref="T:System.Threading.Tasks.Task" /> 執行個體是否因取消才完成執行。</summary>
      <returns>如果工作是由於取消而完成，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.IsCompleted">
      <summary>取得這個 <see cref="T:System.Threading.Tasks.Task" /> 是否已完成。</summary>
      <returns>如果工作已完成，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.IsFaulted">
      <summary>取得 <see cref="T:System.Threading.Tasks.Task" /> 是否因未處理的例外狀況才完成。</summary>
      <returns>如果工作擲回未處理的例外狀況，則為 true，否則為 false。</returns>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run(System.Action)">
      <summary>將指定在 ThreadPool 執行工作排入佇列，並傳回該工作的工作控制代碼。</summary>
      <returns>表示已進入佇列要在 ThreadPool 中執行之工作 (work) 的工作 (task)。</returns>
      <param name="action">要以非同步方式執行的工作</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> parameter was null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run(System.Action,System.Threading.CancellationToken)">
      <summary>將指定在 ThreadPool 執行工作排入佇列，並傳回該工作的工作控制代碼。</summary>
      <returns>表示已進入佇列要在 ThreadPool 中執行之工作 (work) 的工作 (task)。</returns>
      <param name="action">要以非同步方式執行的工作</param>
      <param name="cancellationToken">應該用來取消工作的取消語彙基元</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> parameter was null.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with <paramref name="cancellationToken" /> was disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run``1(System.Func{System.Threading.Tasks.Task{``0}})">
      <summary>將指定在 ThreadPool 執行的工作排入佇列，並傳回 Task(TResult) 所傳回之 <paramref name="function" /> 的 Proxy。</summary>
      <returns>Task(TResult)，代表 Task(TResult) 所傳回之 <paramref name="function" /> 的 Proxy。</returns>
      <param name="function">要以非同步方式執行的工作</param>
      <typeparam name="TResult">Proxy 工作傳回的結果類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter was null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run``1(System.Func{System.Threading.Tasks.Task{``0}},System.Threading.CancellationToken)">
      <summary>將指定在 ThreadPool 執行的工作排入佇列，並傳回 Task(TResult) 所傳回之 <paramref name="function" /> 的 Proxy。</summary>
      <returns>Task(TResult)，代表 Task(TResult) 所傳回之 <paramref name="function" /> 的 Proxy。</returns>
      <param name="function">要以非同步方式執行的工作</param>
      <param name="cancellationToken">應該用來取消工作的取消語彙基元</param>
      <typeparam name="TResult">Proxy 工作傳回的結果類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter was null.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with <paramref name="cancellationToken" /> was disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run(System.Func{System.Threading.Tasks.Task})">
      <summary>將指定在 ThreadPool 執行的工作排入佇列，並傳回 <paramref name="function" /> 所傳回之工作的 Proxy。</summary>
      <returns>表示 <paramref name="function" /> 所傳回工作之 Proxy 的工作。</returns>
      <param name="function">要以非同步方式執行的工作</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter was null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run(System.Func{System.Threading.Tasks.Task},System.Threading.CancellationToken)">
      <summary>將指定在 ThreadPool 執行的工作排入佇列，並傳回 <paramref name="function" /> 所傳回之工作的 Proxy。</summary>
      <returns>表示 <paramref name="function" /> 所傳回工作之 Proxy 的工作。</returns>
      <param name="function">要以非同步方式執行的工作。</param>
      <param name="cancellationToken">應該用來取消工作的取消語彙基元。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter was null.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with <paramref name="cancellationToken" /> was disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run``1(System.Func{``0})">
      <summary>將指定在執行緒集區執行工作排入佇列，並傳回代表該工作的 <see cref="T:System.Threading.Tasks.Task`1" /> 物件。</summary>
      <returns>代表已排入佇列要在執行緒集區中執行之工作 (work) 的工作 (task) 物件。</returns>
      <param name="function">要以非同步方式執行的工作。</param>
      <typeparam name="TResult">工作的傳回類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter is null. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run``1(System.Func{``0},System.Threading.CancellationToken)">
      <summary>將指定在執行緒集區執行工作排入佇列，並傳回該工作的 Task(TResult) 控制代碼。</summary>
      <returns>Task(TResult)，代表已排入佇列要在 ThreadPool 中執行的工作。</returns>
      <param name="function">要以非同步方式執行的工作</param>
      <param name="cancellationToken">應該用來取消工作的取消語彙基元</param>
      <typeparam name="TResult">工作的結果類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter was null.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with <paramref name="cancellationToken" /> was disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.RunSynchronously">
      <summary>在目前的 <see cref="T:System.Threading.Tasks.Task" /> 上同步執行 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</summary>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> instance has been disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Threading.Tasks.Task" /> is not in a valid state to be started.It may have already been started, executed, or canceled, or it may have been created in a manner that doesn't support direct scheduling.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.RunSynchronously(System.Threading.Tasks.TaskScheduler)">
      <summary>在提供的 <see cref="T:System.Threading.Tasks.Task" /> 上同步執行 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</summary>
      <param name="scheduler">排程器，要嘗試在其上以內嵌方式執行這個工作。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> instance has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Threading.Tasks.Task" /> is not in a valid state to be started.It may have already been started, executed, or canceled, or it may have been created in a manner that doesn't support direct scheduling.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Start">
      <summary>啟動 <see cref="T:System.Threading.Tasks.Task" />，並排定它在目前的 <see cref="T:System.Threading.Tasks.TaskScheduler" /> 執行。</summary>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> instance has been disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Threading.Tasks.Task" /> is not in a valid state to be started.It may have already been started, executed, or canceled, or it may have been created in a manner that doesn't support direct scheduling.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Start(System.Threading.Tasks.TaskScheduler)">
      <summary>啟動 <see cref="T:System.Threading.Tasks.Task" />，並排定它在指定的 <see cref="T:System.Threading.Tasks.TaskScheduler" /> 執行。</summary>
      <param name="scheduler">要與這個工作產生關聯，並用於執行這個工作的 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> instance has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Threading.Tasks.Task" /> is not in a valid state to be started.It may have already been started, executed, or canceled, or it may have been created in a manner that doesn't support direct scheduling.</exception>
    </member>
    <member name="P:System.Threading.Tasks.Task.Status">
      <summary>取得這個工作的 <see cref="T:System.Threading.Tasks.TaskStatus" />。</summary>
      <returns>這個工作執行個體目前的 <see cref="T:System.Threading.Tasks.TaskStatus" />。</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.System#IAsyncResult#AsyncWaitHandle">
      <summary>取得 <see cref="T:System.Threading.WaitHandle" />，這個項目可用來等候工作完成。</summary>
      <returns>
        <see cref="T:System.Threading.WaitHandle" />，可用來等候工作完成。</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
    </member>
    <member name="P:System.Threading.Tasks.Task.System#IAsyncResult#CompletedSynchronously">
      <summary>取得作業是否已同步完成的指示。</summary>
      <returns>如果作業同步完成，則為 true，否則為 false。</returns>
    </member>
    <member name="M:System.Threading.Tasks.Task.Wait">
      <summary>等候 <see cref="T:System.Threading.Tasks.Task" /> 完成執行。</summary>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.AggregateException">The task was canceled.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object.-or-An exception was thrown during the execution of the task.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Wait(System.Int32)">
      <summary>等待 <see cref="T:System.Threading.Tasks.Task" /> 在指定的毫秒數內完成執行。</summary>
      <returns>如果 true 在指定的時間內執行完成，則為 <see cref="T:System.Threading.Tasks.Task" />，否則為 false。</returns>
      <param name="millisecondsTimeout">要等候的毫秒數，如果要無限期等候，則為 <see cref="F:System.Threading.Timeout.Infinite" /> (-1)。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.AggregateException">The task was canceled.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object.-or-An exception was thrown during the execution of the task.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Wait(System.Int32,System.Threading.CancellationToken)">
      <summary>等候 <see cref="T:System.Threading.Tasks.Task" /> 完成執行。如果在工作完成之前經過逾時間隔或取消語彙基元已取消，則等候會終止。</summary>
      <returns>如果 true 在指定的時間內執行完成，則為 <see cref="T:System.Threading.Tasks.Task" />，否則為 false。</returns>
      <param name="millisecondsTimeout">要等候的毫秒數，如果要無限期等候，則為 <see cref="F:System.Threading.Timeout.Infinite" /> (-1)。</param>
      <param name="cancellationToken">等候工作完成時要觀察的取消語彙基元。</param>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.AggregateException">The task was canceled.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object.-or-An exception was thrown during the execution of the task.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Wait(System.Threading.CancellationToken)">
      <summary>等候 <see cref="T:System.Threading.Tasks.Task" /> 完成執行。如果在工作完成之前取消語彙基元已取消，則等候會終止。</summary>
      <param name="cancellationToken">等候工作完成時要觀察的取消語彙基元。</param>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The task has been disposed.</exception>
      <exception cref="T:System.AggregateException">The task was canceled.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object.-or-An exception was thrown during the execution of the task.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Wait(System.TimeSpan)">
      <summary>等待 <see cref="T:System.Threading.Tasks.Task" /> 在指定的時間間隔內完成執行。</summary>
      <returns>如果 true 在指定的時間內執行完成，則為 <see cref="T:System.Threading.Tasks.Task" />，否則為 false。</returns>
      <param name="timeout">
        <see cref="T:System.TimeSpan" />，代表等候毫秒數；或是 <see cref="T:System.TimeSpan" />，代表無限期等候的 -1 毫秒。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> is a negative number other than -1 milliseconds, which represents an infinite time-out. -or-<paramref name="timeout" /> is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
      <exception cref="T:System.AggregateException">The task was canceled.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object.-or-An exception was thrown during the execution of the task.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAll(System.Threading.Tasks.Task[])">
      <summary>等候所有提供的 <see cref="T:System.Threading.Tasks.Task" /> 物件完成執行。</summary>
      <param name="tasks">要等待之 <see cref="T:System.Threading.Tasks.Task" /> 執行個體的陣列。</param>
      <exception cref="T:System.ObjectDisposedException">One or more of the <see cref="T:System.Threading.Tasks.Task" /> objects in <paramref name="tasks" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.-or-The <paramref name="tasks" /> argument contains a null element.</exception>
      <exception cref="T:System.AggregateException">At least one of the <see cref="T:System.Threading.Tasks.Task" /> instances was canceled.If a task was canceled, the <see cref="T:System.AggregateException" /> exception contains an <see cref="T:System.OperationCanceledException" /> exception in its <see cref="P:System.AggregateException.InnerExceptions" /> collection.-or-An exception was thrown during the execution of at least one of the <see cref="T:System.Threading.Tasks.Task" /> instances. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAll(System.Threading.Tasks.Task[],System.Int32)">
      <summary>等待所有提供的 <see cref="T:System.Threading.Tasks.Task" /> 物件在指定的毫秒數內完成執行。</summary>
      <returns>如果所有 true 執行個體在指定的時間內執行完成，則為 <see cref="T:System.Threading.Tasks.Task" />，否則為 false。</returns>
      <param name="tasks">要等待之 <see cref="T:System.Threading.Tasks.Task" /> 執行個體的陣列。</param>
      <param name="millisecondsTimeout">要等候的毫秒數，如果要無限期等候，則為 <see cref="F:System.Threading.Timeout.Infinite" /> (-1)。</param>
      <exception cref="T:System.ObjectDisposedException">One or more of the <see cref="T:System.Threading.Tasks.Task" /> objects in <paramref name="tasks" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">At least one of the <see cref="T:System.Threading.Tasks.Task" /> instances was canceled.If a task was canceled, the <see cref="T:System.AggregateException" /> contains an <see cref="T:System.OperationCanceledException" /> in its <see cref="P:System.AggregateException.InnerExceptions" /> collection.-or-An exception was thrown during the execution of at least one of the <see cref="T:System.Threading.Tasks.Task" /> instances. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAll(System.Threading.Tasks.Task[],System.Int32,System.Threading.CancellationToken)">
      <summary>等待所有提供的 <see cref="T:System.Threading.Tasks.Task" /> 物件在指定的毫秒數內完成執行。</summary>
      <returns>如果所有 true 執行個體在指定的時間內執行完成，則為 <see cref="T:System.Threading.Tasks.Task" />，否則為 false。</returns>
      <param name="tasks">要等待之 <see cref="T:System.Threading.Tasks.Task" /> 執行個體的陣列。</param>
      <param name="millisecondsTimeout">要等候的毫秒數，如果要無限期等候，則為 <see cref="F:System.Threading.Timeout.Infinite" /> (-1)。</param>
      <param name="cancellationToken">等候工作完成時要觀察的 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />。</param>
      <exception cref="T:System.ObjectDisposedException">One or more of the <see cref="T:System.Threading.Tasks.Task" /> objects in <paramref name="tasks" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">At least one of the <see cref="T:System.Threading.Tasks.Task" /> instances was canceled.If a task was canceled, the <see cref="T:System.AggregateException" /> contains an <see cref="T:System.OperationCanceledException" /> in its <see cref="P:System.AggregateException.InnerExceptions" /> collection.-or-An exception was thrown during the execution of at least one of the <see cref="T:System.Threading.Tasks.Task" /> instances. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAll(System.Threading.Tasks.Task[],System.Threading.CancellationToken)">
      <summary>等候所有提供的 <see cref="T:System.Threading.Tasks.Task" /> 物件完成執行。</summary>
      <param name="tasks">要等待之 <see cref="T:System.Threading.Tasks.Task" /> 執行個體的陣列。</param>
      <param name="cancellationToken">等候工作完成時要觀察的 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />。</param>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">At least one of the <see cref="T:System.Threading.Tasks.Task" /> instances was canceled.If a task was canceled, the <see cref="T:System.AggregateException" /> contains an <see cref="T:System.OperationCanceledException" /> in its <see cref="P:System.AggregateException.InnerExceptions" /> collection.-or-An exception was thrown during the execution of at least one of the <see cref="T:System.Threading.Tasks.Task" /> instances. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
      <exception cref="T:System.ObjectDisposedException">One or more of the <see cref="T:System.Threading.Tasks.Task" /> objects in <paramref name="tasks" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAll(System.Threading.Tasks.Task[],System.TimeSpan)">
      <summary>等候所有提供的可取消 <see cref="T:System.Threading.Tasks.Task" /> 物件在指定的時間間隔內完成執行。</summary>
      <returns>如果所有 true 執行個體在指定的時間內執行完成，則為 <see cref="T:System.Threading.Tasks.Task" />，否則為 false。</returns>
      <param name="tasks">要等待之 <see cref="T:System.Threading.Tasks.Task" /> 執行個體的陣列。</param>
      <param name="timeout">
        <see cref="T:System.TimeSpan" />，代表等候毫秒數；或是 <see cref="T:System.TimeSpan" />，代表無限期等候的 -1 毫秒。</param>
      <exception cref="T:System.ObjectDisposedException">One or more of the <see cref="T:System.Threading.Tasks.Task" /> objects in <paramref name="tasks" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null. </exception>
      <exception cref="T:System.AggregateException">At least one of the <see cref="T:System.Threading.Tasks.Task" /> instances was canceled.If a task was canceled, the <see cref="T:System.AggregateException" /> contains an <see cref="T:System.OperationCanceledException" /> in its <see cref="P:System.AggregateException.InnerExceptions" /> collection.-or-An exception was thrown during the execution of at least one of the <see cref="T:System.Threading.Tasks.Task" /> instances. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> is a negative number other than -1 milliseconds, which represents an infinite time-out. -or-<paramref name="timeout" /> is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAny(System.Threading.Tasks.Task[])">
      <summary>等候任一提供的 <see cref="T:System.Threading.Tasks.Task" /> 物件完成執行。</summary>
      <returns>
        <paramref name="tasks" /> 陣列引數中已完成工作的索引。</returns>
      <param name="tasks">要等待之 <see cref="T:System.Threading.Tasks.Task" /> 執行個體的陣列。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAny(System.Threading.Tasks.Task[],System.Int32)">
      <summary>等候所有提供的 <see cref="T:System.Threading.Tasks.Task" /> 物件在指定的毫秒數內完成執行。</summary>
      <returns>
        <paramref name="tasks" /> 陣列引數中已完成工作的索引；如果發生逾時，則為 -1。</returns>
      <param name="tasks">要等待之 <see cref="T:System.Threading.Tasks.Task" /> 執行個體的陣列。</param>
      <param name="millisecondsTimeout">要等候的毫秒數，如果要無限期等候，則為 <see cref="F:System.Threading.Timeout.Infinite" /> (-1)。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAny(System.Threading.Tasks.Task[],System.Int32,System.Threading.CancellationToken)">
      <summary>等待所有提供的 <see cref="T:System.Threading.Tasks.Task" /> 物件在指定的毫秒數內完成執行。</summary>
      <returns>
        <paramref name="tasks" /> 陣列引數中已完成工作的索引；如果發生逾時，則為 -1。</returns>
      <param name="tasks">要等待之 <see cref="T:System.Threading.Tasks.Task" /> 執行個體的陣列。</param>
      <param name="millisecondsTimeout">要等候的毫秒數，如果要無限期等候，則為 <see cref="F:System.Threading.Timeout.Infinite" /> (-1)。</param>
      <param name="cancellationToken">等候工作完成時要觀察的 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAny(System.Threading.Tasks.Task[],System.Threading.CancellationToken)">
      <summary>等候所有提供的 <see cref="T:System.Threading.Tasks.Task" /> 物件完成執行。</summary>
      <returns>
        <paramref name="tasks" /> 陣列引數中已完成工作的索引。</returns>
      <param name="tasks">要等待之 <see cref="T:System.Threading.Tasks.Task" /> 執行個體的陣列。</param>
      <param name="cancellationToken">等候工作完成時要觀察的 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAny(System.Threading.Tasks.Task[],System.TimeSpan)">
      <summary>等候其中任何一個提供的 <see cref="T:System.Threading.Tasks.Task" /> 物件在指定的時間間隔內完成執行。</summary>
      <returns>
        <paramref name="tasks" /> 陣列引數中已完成工作的索引；如果發生逾時，則為 -1。</returns>
      <param name="tasks">要等待之 <see cref="T:System.Threading.Tasks.Task" /> 執行個體的陣列。</param>
      <param name="timeout">
        <see cref="T:System.TimeSpan" />，代表等候毫秒數；或是 <see cref="T:System.TimeSpan" />，代表無限期等候的 -1 毫秒。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> is a negative number other than -1 milliseconds, which represents an infinite time-out. -or-<paramref name="timeout" /> is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAll``1(System.Collections.Generic.IEnumerable{System.Threading.Tasks.Task{``0}})">
      <summary>建立一個工作，當可列舉集合中的所有 <see cref="T:System.Threading.Tasks.Task`1" /> 物件完成時，會完成此工作。</summary>
      <returns>工作，代表所有提供的工作已完成。</returns>
      <param name="tasks">要等待完成的工作。</param>
      <typeparam name="TResult">完成的工作類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> collection contained a null task. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAll(System.Collections.Generic.IEnumerable{System.Threading.Tasks.Task})">
      <summary>建立一個工作，當可列舉集合中的所有 <see cref="T:System.Threading.Tasks.Task" /> 物件完成時，會完成此工作。</summary>
      <returns>工作，代表所有提供的工作已完成。</returns>
      <param name="tasks">要等待完成的工作。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> collection contained a null task.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAll(System.Threading.Tasks.Task[])">
      <summary>建立一個工作，當陣列中的所有 <see cref="T:System.Threading.Tasks.Task" /> 物件完成時，會完成此工作。</summary>
      <returns>工作，代表所有提供的工作已完成。</returns>
      <param name="tasks">要等待完成的工作。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAll``1(System.Threading.Tasks.Task{``0}[])">
      <summary>建立一個工作，當陣列中的所有 <see cref="T:System.Threading.Tasks.Task`1" /> 物件完成時，會完成此工作。</summary>
      <returns>工作，代表所有提供的工作已完成。</returns>
      <param name="tasks">要等待完成的工作。</param>
      <typeparam name="TResult">完成的工作類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAny``1(System.Collections.Generic.IEnumerable{System.Threading.Tasks.Task{``0}})">
      <summary>建立當任何一個提供的工作完成時才會完成的工作。</summary>
      <returns>工作，代表其中一個提供的工作已完成。傳回的工作結果為已完成的工作。</returns>
      <param name="tasks">要等待完成的工作。</param>
      <typeparam name="TResult">完成的工作類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task, or was empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAny(System.Collections.Generic.IEnumerable{System.Threading.Tasks.Task})">
      <summary>建立當任何一個提供的工作完成時才會完成的工作。</summary>
      <returns>工作，代表其中一個提供的工作已完成。傳回的工作結果為已完成的工作。</returns>
      <param name="tasks">要等待完成的工作。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task, or was empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAny(System.Threading.Tasks.Task[])">
      <summary>建立當任何一個提供的工作完成時才會完成的工作。</summary>
      <returns>工作，代表其中一個提供的工作已完成。傳回的工作結果為已完成的工作。</returns>
      <param name="tasks">要等待完成的工作。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task, or was empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAny``1(System.Threading.Tasks.Task{``0}[])">
      <summary>建立當任何一個提供的工作完成時才會完成的工作。</summary>
      <returns>工作，代表其中一個提供的工作已完成。傳回的工作結果為已完成的工作。</returns>
      <param name="tasks">要等待完成的工作。</param>
      <typeparam name="TResult">完成的工作類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task, or was empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Yield">
      <summary>建立會在等候時以非同步方式遞交 (Yield) 給目前內容的 awaitable 工具。</summary>
      <returns>內容，這個內容會在受到等候時非同步轉換回等候當時的最新內容。如果目前的 <see cref="T:System.Threading.SynchronizationContext" /> 非 null，即視為目前的內容。否則，將與目前執行之工作相關聯的工作排程器視為目前的內容。</returns>
    </member>
    <member name="T:System.Threading.Tasks.Task`1">
      <summary>代表可傳回值的非同步作業。</summary>
      <typeparam name="TResult">這個 <see cref="T:System.Threading.Tasks.Task`1" /> 所產生之結果的類型。</typeparam>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{`0})">
      <summary>使用指定的函式，初始化新的 <see cref="T:System.Threading.Tasks.Task`1" />。</summary>
      <param name="function">委派，表示工作中要執行的程式碼。當函式完成時，工作的 <see cref="P:System.Threading.Tasks.Task`1.Result" /> 屬性會設定成傳回函式的結果值。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{`0},System.Threading.CancellationToken)">
      <summary>使用指定的函式，初始化新的 <see cref="T:System.Threading.Tasks.Task`1" />。</summary>
      <param name="function">委派，表示工作中要執行的程式碼。當函式完成時，工作的 <see cref="P:System.Threading.Tasks.Task`1.Result" /> 屬性會設定成傳回函式的結果值。</param>
      <param name="cancellationToken">要指派給這個工作的 <see cref="T:System.Threading.CancellationToken" />。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions)">
      <summary>使用指定的函式和建立選項，初始化新的 <see cref="T:System.Threading.Tasks.Task`1" />。</summary>
      <param name="function">委派，表示工作中要執行的程式碼。當函式完成時，工作的 <see cref="P:System.Threading.Tasks.Task`1.Result" /> 屬性會設定成傳回函式的結果值。</param>
      <param name="cancellationToken">要指派給新工作的 <see cref="T:System.Threading.CancellationToken" />。</param>
      <param name="creationOptions">
        <see cref="T:System.Threading.Tasks.TaskCreationOptions" />，用於自訂工作的行為。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{`0},System.Threading.Tasks.TaskCreationOptions)">
      <summary>使用指定的函式和建立選項，初始化新的 <see cref="T:System.Threading.Tasks.Task`1" />。</summary>
      <param name="function">委派，表示工作中要執行的程式碼。當函式完成時，工作的 <see cref="P:System.Threading.Tasks.Task`1.Result" /> 屬性會設定成傳回函式的結果值。</param>
      <param name="creationOptions">
        <see cref="T:System.Threading.Tasks.TaskCreationOptions" />，用於自訂工作的行為。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{System.Object,`0},System.Object)">
      <summary>使用指定的函式和狀態，初始化新的 <see cref="T:System.Threading.Tasks.Task`1" />。</summary>
      <param name="function">委派，表示工作中要執行的程式碼。當函式完成時，工作的 <see cref="P:System.Threading.Tasks.Task`1.Result" /> 屬性會設定成傳回函式的結果值。</param>
      <param name="state">物件，表示動作所需的資料。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{System.Object,`0},System.Object,System.Threading.CancellationToken)">
      <summary>使用指定的動作、狀態和選項，初始化新的 <see cref="T:System.Threading.Tasks.Task`1" />。</summary>
      <param name="function">委派，表示工作中要執行的程式碼。當函式完成時，工作的 <see cref="P:System.Threading.Tasks.Task`1.Result" /> 屬性會設定成傳回函式的結果值。</param>
      <param name="state">物件，代表示函式所要使用的資料。</param>
      <param name="cancellationToken">要指派給新工作的 <see cref="T:System.Threading.CancellationToken" />。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{System.Object,`0},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions)">
      <summary>使用指定的動作、狀態和選項，初始化新的 <see cref="T:System.Threading.Tasks.Task`1" />。</summary>
      <param name="function">委派，表示工作中要執行的程式碼。當函式完成時，工作的 <see cref="P:System.Threading.Tasks.Task`1.Result" /> 屬性會設定成傳回函式的結果值。</param>
      <param name="state">物件，代表示函式所要使用的資料。</param>
      <param name="cancellationToken">要指派給新工作的 <see cref="T:System.Threading.CancellationToken" />。</param>
      <param name="creationOptions">
        <see cref="T:System.Threading.Tasks.TaskCreationOptions" />，用於自訂工作的行為。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{System.Object,`0},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>使用指定的動作、狀態和選項，初始化新的 <see cref="T:System.Threading.Tasks.Task`1" />。</summary>
      <param name="function">委派，表示工作中要執行的程式碼。當函式完成時，工作的 <see cref="P:System.Threading.Tasks.Task`1.Result" /> 屬性會設定成傳回函式的結果值。</param>
      <param name="state">物件，代表示函式所要使用的資料。</param>
      <param name="creationOptions">
        <see cref="T:System.Threading.Tasks.TaskCreationOptions" />，用於自訂工作的行為。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ConfigureAwait(System.Boolean)">
      <summary>設定用來等候這個 <see cref="T:System.Threading.Tasks.Task`1" /> 的 awaiter。</summary>
      <returns>等候這項工作所用的物件。</returns>
      <param name="continueOnCapturedContext">true 表示嘗試將接續封送處理回擷取的原始內容，否則為 false。</param>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}})">
      <summary>建立當目標工作完成時非同步執行的接續。</summary>
      <returns>新的接續工作。</returns>
      <param name="continuationAction">當前項 <see cref="T:System.Threading.Tasks.Task`1" /> 完成時要執行的動作。執行時，會將已完成的工作當做引數傳遞給委派。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}},System.Threading.CancellationToken)">
      <summary>建立當目標 <see cref="T:System.Threading.Tasks.Task`1" /> 完成時非同步執行的可取消接續。</summary>
      <returns>新的接續工作。</returns>
      <param name="continuationAction">當 <see cref="T:System.Threading.Tasks.Task`1" /> 完成時要執行的動作。執行時，會將已完成的工作當做引數傳遞給委派。</param>
      <param name="cancellationToken">要傳遞給新接續工作的取消語彙基元。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has been disposed. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>建立會根據 <paramref name="continuationOptions" /> 中指定的條件執行的接續。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">要根據 <paramref name="continuationOptions" /> 中指定之條件執行的動作。執行時，會將已完成的工作當做引數傳遞給委派。</param>
      <param name="cancellationToken">要指派給新接續工作的 <see cref="T:System.Threading.CancellationToken" />。</param>
      <param name="continuationOptions">接續工作的排程時間及其行為方式的適用選項。這包含準則 (如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />) 和執行選項 (如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />)。</param>
      <param name="scheduler">要與接續工作產生關聯且於執行時使用的 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>建立會根據 <paramref name="continuationOptions" /> 中指定的條件執行的接續。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">要根據 <paramref name="continuationOptions" /> 中指定之條件執行的動作。執行時，會將已完成的工作當做引數傳遞給委派。</param>
      <param name="continuationOptions">接續工作的排程時間及其行為方式的適用選項。這包含準則 (如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />) 和執行選項 (如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />)。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}},System.Threading.Tasks.TaskScheduler)">
      <summary>建立當目標 <see cref="T:System.Threading.Tasks.Task`1" /> 完成時非同步執行的接續。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">當 <see cref="T:System.Threading.Tasks.Task`1" /> 完成時要執行的動作。執行時，會將已完成的工作當做引數傳遞給委派。</param>
      <param name="scheduler">要與接續工作產生關聯且於執行時使用的 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0},System.Object},System.Object)">
      <summary>建立當目標 <see cref="T:System.Threading.Tasks.Task`1" /> 完成時，會接收狀態資訊並執行的接續。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">當 <see cref="T:System.Threading.Tasks.Task`1" /> 完成時要執行的動作。執行時，會將已完成的工作和呼叫端提供的狀態物件當做引數傳遞給委派。</param>
      <param name="state">物件，表示接續動作所使用的資料。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0},System.Object},System.Object,System.Threading.CancellationToken)">
      <summary>建立目標 <see cref="T:System.Threading.Tasks.Task`1" /> 完成時執行的接續工作。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">當 <see cref="T:System.Threading.Tasks.Task`1" /> 完成時要執行的動作。當執行時，完成的工作會傳遞至委派，並以呼叫端提供狀態的物件做為引數。</param>
      <param name="state">物件，表示接續動作所使用的資料。</param>
      <param name="cancellationToken">要指派給新接續工作的 <see cref="T:System.Threading.CancellationToken" />。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0},System.Object},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>建立目標 <see cref="T:System.Threading.Tasks.Task`1" /> 完成時執行的接續工作。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">當 <see cref="T:System.Threading.Tasks.Task`1" /> 完成時要執行的動作。當執行時，完成的工作會傳遞至委派，並以呼叫端提供狀態的物件做為引數。</param>
      <param name="state">物件，表示接續動作所使用的資料。</param>
      <param name="cancellationToken">要指派給新接續工作的 <see cref="T:System.Threading.CancellationToken" />。</param>
      <param name="continuationOptions">接續工作的排程時間及其行為方式的適用選項。這包含準則 (如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />) 和執行選項 (如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />)。</param>
      <param name="scheduler">要與接續工作產生關聯且於執行時使用的 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0},System.Object},System.Object,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>建立目標 <see cref="T:System.Threading.Tasks.Task`1" /> 完成時執行的接續工作。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">當 <see cref="T:System.Threading.Tasks.Task`1" /> 完成時要執行的動作。當執行時，完成的工作會傳遞至委派，並以呼叫端提供狀態的物件做為引數。</param>
      <param name="state">物件，表示接續動作所使用的資料。</param>
      <param name="continuationOptions">接續工作的排程時間及其行為方式的適用選項。這包含準則 (如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />) 和執行選項 (如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />)。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0},System.Object},System.Object,System.Threading.Tasks.TaskScheduler)">
      <summary>建立目標 <see cref="T:System.Threading.Tasks.Task`1" /> 完成時執行的接續工作。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="continuationAction">當 <see cref="T:System.Threading.Tasks.Task`1" /> 完成時要執行的動作。當執行時，完成的工作會傳遞至委派，並以呼叫端提供狀態的物件做為引數。</param>
      <param name="state">物件，表示接續動作所使用的資料。</param>
      <param name="scheduler">要與接續工作產生關聯且於執行時使用的 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},``0})">
      <summary>建立當目標 <see cref="T:System.Threading.Tasks.Task`1" /> 完成時非同步執行的接續。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">當 <see cref="T:System.Threading.Tasks.Task`1" /> 完成時要執行的函式。執行時，會將已完成的工作當做引數傳遞給委派。</param>
      <typeparam name="TNewResult"> 接續作業所產生的結果類型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},``0},System.Threading.CancellationToken)">
      <summary>建立當目標 <see cref="T:System.Threading.Tasks.Task`1" /> 完成時非同步執行的接續。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">當 <see cref="T:System.Threading.Tasks.Task`1" /> 完成時要執行的函式。執行時，會將已完成的工作當做引數傳遞給委派。</param>
      <param name="cancellationToken">要指派給新工作的 <see cref="T:System.Threading.CancellationToken" />。</param>
      <typeparam name="TNewResult"> 接續作業所產生的結果類型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},``0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>建立會根據 <paramref name="continuationOptions" /> 中指定的條件執行的接續。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">要根據 <paramref name="continuationOptions" /> 中指定之條件執行的函式。執行時，會將這個完成的工作當做引數傳遞給委派。</param>
      <param name="cancellationToken">要指派給新工作的 <see cref="T:System.Threading.CancellationToken" />。</param>
      <param name="continuationOptions">接續工作的排程時間及其行為方式的適用選項。這包含準則 (如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />) 和執行選項 (如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />)。</param>
      <param name="scheduler">要與接續工作產生關聯且於執行時使用的 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <typeparam name="TNewResult"> 接續作業所產生的結果類型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},``0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>建立會根據 <paramref name="continuationOptions" /> 中指定的條件執行的接續。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">要根據 <paramref name="continuationOptions" /> 中指定之條件執行的函式。執行時，會將已完成的工作當做引數傳遞給委派。</param>
      <param name="continuationOptions">接續工作的排程時間及其行為方式的適用選項。這包含準則 (如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />) 和執行選項 (如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />)。</param>
      <typeparam name="TNewResult"> 接續作業所產生的結果類型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},``0},System.Threading.Tasks.TaskScheduler)">
      <summary>建立當目標 <see cref="T:System.Threading.Tasks.Task`1" /> 完成時非同步執行的接續。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">當 <see cref="T:System.Threading.Tasks.Task`1" /> 完成時要執行的函式。執行時，會將已完成的工作當做引數傳遞給委派。</param>
      <param name="scheduler">要與接續工作產生關聯且於執行時使用的 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <typeparam name="TNewResult"> 接續作業所產生的結果類型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},System.Object,``0},System.Object)">
      <summary>建立目標 <see cref="T:System.Threading.Tasks.Task`1" /> 完成時執行的接續工作。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">當 <see cref="T:System.Threading.Tasks.Task`1" /> 完成時要執行的函式。當執行時，完成的工作會傳遞至委派，並以呼叫端提供狀態的物件做為引數。</param>
      <param name="state">物件，表示接續函式所使用的資料。</param>
      <typeparam name="TNewResult">接續作業所產生的結果類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},System.Object,``0},System.Object,System.Threading.CancellationToken)">
      <summary>建立目標 <see cref="T:System.Threading.Tasks.Task`1" /> 完成時執行的接續工作。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">當 <see cref="T:System.Threading.Tasks.Task`1" /> 完成時要執行的函式。當執行時，完成的工作會傳遞至委派，並以呼叫端提供狀態的物件做為引數。</param>
      <param name="state">物件，表示接續函式所使用的資料。</param>
      <param name="cancellationToken">要指派給新工作的 <see cref="T:System.Threading.CancellationToken" />。</param>
      <typeparam name="TNewResult">接續作業所產生的結果類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},System.Object,``0},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>建立目標 <see cref="T:System.Threading.Tasks.Task`1" /> 完成時執行的接續工作。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">當 <see cref="T:System.Threading.Tasks.Task`1" /> 完成時要執行的函式。當執行時，完成的工作會傳遞至委派，並以呼叫端提供狀態的物件做為引數。</param>
      <param name="state">物件，表示接續函式所使用的資料。</param>
      <param name="cancellationToken">要指派給新工作的 <see cref="T:System.Threading.CancellationToken" />。</param>
      <param name="continuationOptions">接續工作的排程時間及其行為方式的適用選項。這包含準則 (如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />) 和執行選項 (如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />)。</param>
      <param name="scheduler">要與接續工作產生關聯且於執行時使用的 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <typeparam name="TNewResult">接續作業所產生的結果類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The  <paramref name="continuationOptions" />  argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},System.Object,``0},System.Object,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>建立目標 <see cref="T:System.Threading.Tasks.Task`1" /> 完成時執行的接續工作。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">當 <see cref="T:System.Threading.Tasks.Task`1" /> 完成時要執行的函式。當執行時，完成的工作會傳遞至委派，並以呼叫端提供狀態的物件做為引數。</param>
      <param name="state">物件，表示接續函式所使用的資料。</param>
      <param name="continuationOptions">接續工作的排程時間及其行為方式的適用選項。這包含準則 (如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />) 和執行選項 (如 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />)。</param>
      <typeparam name="TNewResult">接續作業所產生的結果類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},System.Object,``0},System.Object,System.Threading.Tasks.TaskScheduler)">
      <summary>建立目標 <see cref="T:System.Threading.Tasks.Task`1" /> 完成時執行的接續工作。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="continuationFunction">當 <see cref="T:System.Threading.Tasks.Task`1" /> 完成時要執行的函式。當執行時，完成的工作會傳遞至委派，並以呼叫端提供狀態的物件做為引數。</param>
      <param name="state">物件，表示接續函式所使用的資料。</param>
      <param name="scheduler">要與接續工作產生關聯且於執行時使用的 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
      <typeparam name="TNewResult">接續作業所產生的結果類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="P:System.Threading.Tasks.Task`1.Factory">
      <summary>提供 Factory 方法的存取，這些 Factory 方法用於建立及設定 <see cref="T:System.Threading.Tasks.Task`1" /> 執行個體。</summary>
      <returns>Factory 物件，可以建立各種不同的 <see cref="T:System.Threading.Tasks.Task`1" /> 物件。</returns>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.GetAwaiter">
      <summary>取得用來等候這個 <see cref="T:System.Threading.Tasks.Task`1" /> 的 awaiter。</summary>
      <returns>awaiter 執行個體。</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task`1.Result">
      <summary>取得這個 <see cref="T:System.Threading.Tasks.Task`1" /> 的結果值。</summary>
      <returns>這個 <see cref="T:System.Threading.Tasks.Task`1" /> 的結果值，與工作的類型參數同一類型。</returns>
      <exception cref="T:System.AggregateException">The task was canceled.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object.-or-An exception was thrown during the execution of the task.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions.</exception>
    </member>
    <member name="T:System.Threading.Tasks.TaskCanceledException">
      <summary>表示用來傳達工作取消的例外狀況。</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskCanceledException.#ctor">
      <summary>使用系統提供的錯誤說明訊息，初始化 <see cref="T:System.Threading.Tasks.TaskCanceledException" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskCanceledException.#ctor(System.String)">
      <summary>使用指定的錯誤說明訊息，初始化 <see cref="T:System.Threading.Tasks.TaskCanceledException" /> 類別的新執行個體。</summary>
      <param name="message">說明例外狀況的訊息。這個建構函式的呼叫端必須確保這個字串已經為目前系統的文化特性當地語系化。</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskCanceledException.#ctor(System.String,System.Exception)">
      <summary>使用指定的錯誤訊息和造成這個例外狀況原因的內部例外狀況參考，初始化 <see cref="T:System.Threading.Tasks.TaskCanceledException" /> 類別的新執行個體。</summary>
      <param name="message">說明例外狀況的訊息。這個建構函式的呼叫端必須確保這個字串已經為目前系統的文化特性當地語系化。</param>
      <param name="innerException">導致目前例外狀況的例外。如果 <paramref name="innerException" /> 參數不是 null，則目前的例外狀況會在處理內部例外的 catch 區塊中引發。</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskCanceledException.#ctor(System.Threading.Tasks.Task)">
      <summary>使用已取消之 <see cref="T:System.Threading.Tasks.Task" /> 的參考，初始化 <see cref="T:System.Threading.Tasks.TaskCanceledException" /> 類別的新執行個體。</summary>
      <param name="task">已取消的工作。</param>
    </member>
    <member name="P:System.Threading.Tasks.TaskCanceledException.Task">
      <summary>取得與這個例外狀況相關聯的工作。</summary>
      <returns>與這個例外狀況相關聯之 <see cref="T:System.Threading.Tasks.Task" /> 的參考。</returns>
    </member>
    <member name="T:System.Threading.Tasks.TaskCompletionSource`1">
      <summary>表示未與委派繫結之 <see cref="T:System.Threading.Tasks.Task`1" /> 的生產者端，可提供透過 <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" /> 屬性對消費者端的存取。</summary>
      <typeparam name="TResult">與這個 <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" /> 相關聯之結果值的型別。</typeparam>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.#ctor">
      <summary>建立 <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" />。</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.#ctor(System.Object)">
      <summary>使用指定的狀態，建立 <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" />。</summary>
      <param name="state">要當成基礎 <see cref="T:System.Threading.Tasks.Task`1" /> 之 AsyncState 的狀態。</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.#ctor(System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>使用指定的狀態和選項，建立 <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" />。</summary>
      <param name="state">要當成基礎 <see cref="T:System.Threading.Tasks.Task`1" /> 之 AsyncState 的狀態。</param>
      <param name="creationOptions">建立基礎 <see cref="T:System.Threading.Tasks.Task`1" /> 時要使用的選項。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 代表搭配 <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" /> 使用的選項無效。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.#ctor(System.Threading.Tasks.TaskCreationOptions)">
      <summary>使用指定的選項，建立 <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" />。</summary>
      <param name="creationOptions">建立基礎 <see cref="T:System.Threading.Tasks.Task`1" /> 時要使用的選項。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 代表搭配 <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" /> 使用的選項無效。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.SetCanceled">
      <summary>將基礎 <see cref="T:System.Threading.Tasks.Task`1" /> 轉換為 <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" /> 狀態。</summary>
      <exception cref="T:System.InvalidOperationException">基礎 <see cref="T:System.Threading.Tasks.Task`1" /> 已處於三個最終狀態的其中一種：<see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />、<see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> 或 <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />，或是已經處置基礎 <see cref="T:System.Threading.Tasks.Task`1" />。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.SetException(System.Collections.Generic.IEnumerable{System.Exception})">
      <summary>將基礎 <see cref="T:System.Threading.Tasks.Task`1" /> 轉換為 <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> 狀態。</summary>
      <param name="exceptions">要繫結至這個 <see cref="T:System.Threading.Tasks.Task`1" /> 的例外狀況集合。</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" /> 已處置。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="exceptions" /> 引數為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="exceptions" /> 中有一或多個 Null 項目。</exception>
      <exception cref="T:System.InvalidOperationException">基礎 <see cref="T:System.Threading.Tasks.Task`1" /> 已處於三種最終狀態的其中一種：<see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />、<see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> 或 <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.SetException(System.Exception)">
      <summary>將基礎 <see cref="T:System.Threading.Tasks.Task`1" /> 轉換為 <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> 狀態。</summary>
      <param name="exception">要繫結至這個 <see cref="T:System.Threading.Tasks.Task`1" /> 的例外狀況。</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" /> 已處置。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="exception" /> 引數為 null。</exception>
      <exception cref="T:System.InvalidOperationException">基礎 <see cref="T:System.Threading.Tasks.Task`1" /> 已處於三種最終狀態的其中一種：<see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />、<see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> 或 <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.SetResult(`0)">
      <summary>將基礎 <see cref="T:System.Threading.Tasks.Task`1" /> 轉換為 <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" /> 狀態。</summary>
      <param name="result">要繫結至這個 <see cref="T:System.Threading.Tasks.Task`1" /> 的結果值。</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" /> 已處置。</exception>
      <exception cref="T:System.InvalidOperationException">基礎 <see cref="T:System.Threading.Tasks.Task`1" /> 已處於三種最終狀態的其中一種：<see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />、<see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> 或 <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />。</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskCompletionSource`1.Task">
      <summary>取得這個 <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" /> 所建立的 <see cref="T:System.Threading.Tasks.Task`1" />。</summary>
      <returns>傳回這個 <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" /> 所建立的 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.TrySetCanceled">
      <summary>嘗試將基礎 <see cref="T:System.Threading.Tasks.Task`1" /> 轉換為 <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" /> 狀態。</summary>
      <returns>如果作業成功則為 true，如果作業未成功或物件已處置則為 false。</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.TrySetCanceled(System.Threading.CancellationToken)">
      <summary>嘗試轉換基礎<see cref="T:System.Threading.Tasks.Task`1" />到<see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />狀態，並可讓儲存在取消工作的取消語彙基元。</summary>
      <returns>如果作業成功，則為 true，否則為 false。</returns>
      <param name="cancellationToken">取消語彙基元。 </param>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.TrySetException(System.Collections.Generic.IEnumerable{System.Exception})">
      <summary>嘗試將基礎 <see cref="T:System.Threading.Tasks.Task`1" /> 轉換為 <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> 狀態。</summary>
      <returns>如果作業成功則為 true，否則為 false。</returns>
      <param name="exceptions">要繫結至這個 <see cref="T:System.Threading.Tasks.Task`1" /> 的例外狀況集合。</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" /> 已處置。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="exceptions" /> 引數為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="exceptions" /> 中有一或多個 Null 項目。-或-<paramref name="exceptions" /> 集合為空白。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.TrySetException(System.Exception)">
      <summary>嘗試將基礎 <see cref="T:System.Threading.Tasks.Task`1" /> 轉換為 <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> 狀態。</summary>
      <returns>如果作業成功則為 true，否則為 false。</returns>
      <param name="exception">要繫結至這個 <see cref="T:System.Threading.Tasks.Task`1" /> 的例外狀況。</param>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" /> 已處置。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="exception" /> 引數為 null。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.TrySetResult(`0)">
      <summary>嘗試將基礎 <see cref="T:System.Threading.Tasks.Task`1" /> 轉換為 <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" /> 狀態。</summary>
      <returns>如果作業成功則為 true，否則為 false。</returns>
      <param name="result">要繫結至這個 <see cref="T:System.Threading.Tasks.Task`1" /> 的結果值。</param>
    </member>
    <member name="T:System.Threading.Tasks.TaskContinuationOptions">
      <summary>指定使用 <see cref="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)" /> 或 <see cref="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}},System.Threading.Tasks.TaskContinuationOptions)" /> 方法建立之工作的行為。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.AttachedToParent">
      <summary>指定將接續 (如果是子工作) 附加至工作階層中的父代。只有在接續的前項也是子工作時，該接續才可以是子工作。根據預設，子工作 (即外部工作所建立的內部工作) 會在其父代之外單獨執行。您可以使用 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.AttachedToParent" /> 選項，使父工作與子工作同步化。請注意，若父工作是以 <see cref="F:System.Threading.Tasks.TaskCreationOptions.DenyChildAttach" /> 選項進行設定，則子工作中的 <see cref="F:System.Threading.Tasks.TaskCreationOptions.AttachedToParent" /> 選項無效，且會以中斷連結的子工作方式執行子工作。如需詳細資訊，請參閱附加與中斷連結的子工作。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.DenyChildAttach">
      <summary>指定以 <see cref="F:System.Threading.Tasks.TaskCreationOptions.AttachedToParent" /> 選項建立的任何子工作 (亦即這個接續所建立的任何巢狀內部工作)，以及以附加子工作方式執行的嘗試，都將無法附加至父工作，且會改為以中斷連結的子工作方式執行。如需詳細資訊，請參閱附加與中斷連結的子工作。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously">
      <summary>指定應該要同步執行接續工作。指定這個選項時，接續會在造成前項工作轉換為最終狀態的同一個執行緒上執行。如果建立接續時前項已完成，則接續會在建立接續的執行緒上執行。如果前項的 <see cref="T:System.Threading.CancellationTokenSource" /> 在 finally 區塊 (在 Visual Basic 中為 Finally) 中遭處置，則會在該 finally 區塊中執行具有這個選項的接續。只有執行時間很短的接續工作才適合以同步方式執行。因為工作以同步方式執行，所以不需要呼叫方法 (如 <see cref="M:System.Threading.Tasks.Task.Wait" />)，以確保呼叫執行緒等待工作完成。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.HideScheduler">
      <summary>指定接續透過呼叫 <see cref="M:System.Threading.Tasks.Task.Run(System.Action)" /> 或 <see cref="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task})" /> 等方法所建立的工作，會將預設排程器 (<see cref="P:System.Threading.Tasks.TaskScheduler.Default" />) 視為目前的排程器，而不是這個接續執行依據的排程器。 </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.LazyCancellation">
      <summary>在接續取消的情況下，防止接續在前項完成之前完成。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.LongRunning">
      <summary>指定接續會是長時間執行的繁複作業。這會提示 <see cref="T:System.Threading.Tasks.TaskScheduler" /> 可能會發生過度訂閱的狀況。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.None">
      <summary>如果未指定任何接續選項，則指定在執行接續時，應該使用預設行為。當前項工作完成時，接續會以非同步方式執行，而不論前項的最終 <see cref="P:System.Threading.Tasks.Task.Status" /> 屬性值為何。如果接續為子工作，則會建立為中斷連結的巢狀工作。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.NotOnCanceled">
      <summary>指定當前項工作取消時，不應該排定接續工作。如果前項在完成時的 <see cref="P:System.Threading.Tasks.Task.Status" /> 屬性為 <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />，則取消前項。這個選項對多工接續而言無效。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.NotOnFaulted">
      <summary>指定當前項擲回未處理的例外狀況時，不應該排程接續工作。如果前項在完成時的 <see cref="P:System.Threading.Tasks.Task.Status" /> 屬性為 <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" />，則擲回未處理的例外狀況。這個選項對多工接續而言無效。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.NotOnRanToCompletion">
      <summary>指定當前項執行完成時，不應該排程接續工作。如果前項在完成時的 <see cref="P:System.Threading.Tasks.Task.Status" /> 屬性為 <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />，則執行到完成。這個選項對多工接續而言無效。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled">
      <summary>指定只有在取消接續的前項時，才應該排定接續。如果前項在完成時的 <see cref="P:System.Threading.Tasks.Task.Status" /> 屬性為 <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />，則取消前項。這個選項對多工接續而言無效。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnFaulted">
      <summary>指定只有在前項擲回未處理的例外狀況時，才應排程接續工作。如果前項在完成時的 <see cref="P:System.Threading.Tasks.Task.Status" /> 屬性為 <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" />，則擲回未處理的例外狀況。<see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnFaulted" /> 選項可確保前項的 <see cref="P:System.Threading.Tasks.Task.Exception" /> 屬性不是 null。您可以使用該屬性來攔截例外狀況，並查看是哪一種例外狀況導致工作錯誤。如果您未存取 <see cref="P:System.Threading.Tasks.Task.Exception" /> 屬性，則會形成未處理的例外狀況。此外，如果您嘗試存取已取消或已發生錯誤之工作的 <see cref="P:System.Threading.Tasks.Task`1.Result" /> 屬性，將會擲回新的例外狀況。這個選項對多工接續而言無效。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnRanToCompletion">
      <summary>指定只有在接續的前項徹底執行後，才應該排定接續。如果前項在完成時的 <see cref="P:System.Threading.Tasks.Task.Status" /> 屬性為 <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />，則執行到完成。這個選項對多工接續而言無效。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.PreferFairness">
      <summary>提示 <see cref="T:System.Threading.Tasks.TaskScheduler" /> 依序排程工作，讓先排程的工作早執行的機率較高，後排程的工作晚執行的機率較高。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.RunContinuationsAsynchronously">
      <summary>指定應該要非同步執行接續工作。這個選項優先於 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />。</summary>
    </member>
    <member name="T:System.Threading.Tasks.TaskCreationOptions">
      <summary>指定旗標，這些旗標會控制建立和執行工作時的選擇性行為。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.AttachedToParent">
      <summary>指定將工作附加至工作階層中的父代。根據預設，子工作 (即外部工作所建立的內部工作) 會在其父代之外單獨執行。您可以使用 <see cref="F:System.Threading.Tasks.TaskContinuationOptions.AttachedToParent" /> 選項，使父工作與子工作同步化。請注意，若父工作是以 <see cref="F:System.Threading.Tasks.TaskCreationOptions.DenyChildAttach" /> 選項進行設定，則子工作中的 <see cref="F:System.Threading.Tasks.TaskCreationOptions.AttachedToParent" /> 選項無效，且會以中斷連結的子工作方式執行子工作。如需詳細資訊，請參閱附加與中斷連結的子工作。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.DenyChildAttach">
      <summary>指定嘗試以連結的子工作方式執行的任何子工作 (亦即以 <see cref="F:System.Threading.Tasks.TaskCreationOptions.AttachedToParent" /> 選項所建立)，都將無法連結至父工作，且會改為以中斷連結的子工作方式執行。如需詳細資訊，請參閱 附加與中斷連結的子工作。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.HideScheduler">
      <summary>防止環境排程器被視為所建立工作中的目前排程器。表示如 StartNew 或 ContinueWith 這樣在已建立工作中執行的作業會將 <see cref="P:System.Threading.Tasks.TaskScheduler.Default" /> 視為目前排程器。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.LongRunning">
      <summary>指定工作將會是長時間執行的廣泛作業，涉及比細部系統更少、更大的元件。這會提示 <see cref="T:System.Threading.Tasks.TaskScheduler" /> 可能會發生過度訂閱的狀況。過度訂閱可讓您建立比可用硬體執行緒數目更多的執行緒。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.None">
      <summary>指定應該使用預設行為。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.PreferFairness">
      <summary>提示 <see cref="T:System.Threading.Tasks.TaskScheduler" /> 盡量依公平法則排定工作，即盡量讓較早排定的工作較早執行，並讓較晚排定的工作較晚執行。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.RunContinuationsAsynchronously">
      <summary>強制以非同步方式執行加入目前工作的接續。</summary>
    </member>
    <member name="T:System.Threading.Tasks.TaskExtensions">
      <summary>提供一組 static (Visual Basic 中是 Shared) 方法，以處理特定類型的 <see cref="T:System.Threading.Tasks.Task" /> 執行個體。</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskExtensions.Unwrap``1(System.Threading.Tasks.Task{System.Threading.Tasks.Task{``0}})">
      <summary>建立 Proxy <see cref="T:System.Threading.Tasks.Task" />，表示 Task&lt;Task&lt;T&gt;&gt; (C#) 或 Task (Of Task(Of T)) (Visual Basic) 的非同步作業。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task" />，表示提供之 Task&lt;Task&lt;T&gt;&gt; (C#) 或 Task (Of Task(Of T)) (Visual Basic) 的非同步作業。</returns>
      <param name="task">要解除包裝的 Task&lt;Task&lt;T&gt;&gt; (C#) 或 Task (Of Task(Of T)) (Visual Basic)。</param>
      <typeparam name="TResult">工作結果的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">如果 <paramref name="task" /> 引數為 Null 時，所會擲回例外狀況。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskExtensions.Unwrap(System.Threading.Tasks.Task{System.Threading.Tasks.Task})">
      <summary>建立 Proxy <see cref="T:System.Threading.Tasks.Task" />，表示 <see cref="M:System.Threading.Tasks.TaskScheduler.TryExecuteTaskInline(System.Threading.Tasks.Task,System.Boolean)" /> 的非同步作業。</summary>
      <returns>工作，表示提供之 System.Threading.Tasks.Task(Of Task) 的非同步作業。</returns>
      <param name="task">要解除包裝的 Task&lt;Task&gt; (C#) 或 Task (Of Task) (Visual Basic)。</param>
      <exception cref="T:System.ArgumentNullException">如果 <paramref name="task" /> 引數為 Null 時，所會擲回例外狀況。</exception>
    </member>
    <member name="T:System.Threading.Tasks.TaskFactory">
      <summary>提供建立和排程 <see cref="T:System.Threading.Tasks.Task" /> 物件的支援。</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.#ctor">
      <summary>使用預設組態，初始化 <see cref="T:System.Threading.Tasks.TaskFactory" /> 執行個體。</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.#ctor(System.Threading.CancellationToken)">
      <summary>使用指定的組態，初始化 <see cref="T:System.Threading.Tasks.TaskFactory" /> 執行個體。</summary>
      <param name="cancellationToken">除非呼叫 Factory 方法時明確指定另一個 CancellationToken，否則為將指派給這個 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> 所建立之工作的 <see cref="T:System.Threading.Tasks.TaskFactory" />。</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.#ctor(System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>使用指定的組態，初始化 <see cref="T:System.Threading.Tasks.TaskFactory" /> 執行個體。</summary>
      <param name="cancellationToken">除非呼叫 Factory 方法時明確指定另一個 CancellationToken，否則為將指派給這個 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> 所建立之工作的預設 <see cref="T:System.Threading.Tasks.TaskFactory" />。</param>
      <param name="creationOptions">使用這個 TaskFactory 建立工作時要採用的預設 <see cref="T:System.Threading.Tasks.TaskCreationOptions" />。</param>
      <param name="continuationOptions">使用這個 TaskFactory 建立接續工作時要採用的預設 <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />。</param>
      <param name="scheduler">預設 <see cref="T:System.Threading.Tasks.TaskScheduler" />，用來排定任何以這個 TaskFactory 建立的工作。Null 值表示應該使用 TaskScheduler.Current。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 引數指定了無效的 <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> 值。有关详细信息，请参阅的备注部分 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />。-或-<paramref name="continuationOptions" /> 引數指定了無效的值。 </exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.#ctor(System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>使用指定的組態，初始化 <see cref="T:System.Threading.Tasks.TaskFactory" /> 執行個體。</summary>
      <param name="creationOptions">使用這個 TaskFactory 建立工作時要採用的預設 <see cref="T:System.Threading.Tasks.TaskCreationOptions" />。</param>
      <param name="continuationOptions">使用這個 TaskFactory 建立接續工作時要採用的預設 <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 引數指定了無效的 <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> 值。有关详细信息，请参阅的备注部分 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />。-或-<paramref name="continuationOptions" /> 引數指定了無效的值。 </exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.#ctor(System.Threading.Tasks.TaskScheduler)">
      <summary>使用指定的組態，初始化 <see cref="T:System.Threading.Tasks.TaskFactory" /> 執行個體。</summary>
      <param name="scheduler">
        <see cref="T:System.Threading.Tasks.TaskScheduler" />，用來排定任何以這個 TaskFactory 建立的工作。Null 值表示應該使用目前 TaskScheduler。</param>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory.CancellationToken">
      <summary>取得這個工作 Factory 的預設取消語彙基元。</summary>
      <returns>這個工作 Factory 的預設工作取消語彙基元。</returns>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory.ContinuationOptions">
      <summary>取得這個工作 Factory 的預設工作接續選項。</summary>
      <returns>這個工作 Factory 的預設工作接續選項。</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task[]})">
      <summary>當一組指定的工作已完成時，建立啟動的接續工作。</summary>
      <returns>新的接續工作。</returns>
      <param name="tasks">要接續之工作的所在陣列。</param>
      <param name="continuationAction">當 <paramref name="tasks" /> 陣列中的所有工作都已經完成時，要執行的動作委派。</param>
      <exception cref="T:System.ObjectDisposedException">已處置之 <paramref name="tasks" /> 陣列中的項目。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 陣列為 null。-或-<paramref name="continuationAction" /> 引數為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 陣列為空白或包含 null 值。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task[]},System.Threading.CancellationToken)">
      <summary>當一組指定的工作已完成時，建立啟動的接續工作。</summary>
      <returns>新的接續工作。</returns>
      <param name="tasks">要接續之工作的所在陣列。</param>
      <param name="continuationAction">當 <paramref name="tasks" /> 陣列中的所有工作都已經完成時，要執行的動作委派。</param>
      <param name="cancellationToken">要指派給新接續工作的取消語彙基元。</param>
      <exception cref="T:System.ObjectDisposedException">已處置之 <paramref name="tasks" /> 陣列中的項目。-或-已處置建立 <see cref="T:System.Threading.CancellationTokenSource" /> 的 <paramref name="cancellationToken" />。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 陣列為 null。-或-<paramref name="continuationAction" /> 引數為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 陣列為空白或包含 null 值。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task[]},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>當一組指定的工作已完成時，建立啟動的接續工作。</summary>
      <returns>新的接續工作。</returns>
      <param name="tasks">要接續之工作的所在陣列。</param>
      <param name="continuationAction">當 <paramref name="tasks" /> 陣列中的所有工作都已經完成時，要執行的動作委派。</param>
      <param name="cancellationToken">要指派給新接續工作的取消語彙基元。</param>
      <param name="continuationOptions">列舉值的位元組合，這些值會控制新接續工作的行為。</param>
      <param name="scheduler">用來排定新接續工作的物件。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 陣列為 null。-或-<paramref name="continuationAction" /> 引數為 null。-或-<paramref name="scheduler" /> 引數為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 陣列為空白或包含 null 值。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task[]},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>當一組指定的工作已完成時，建立啟動的接續工作。</summary>
      <returns>新的接續工作。</returns>
      <param name="tasks">要接續之工作的所在陣列。</param>
      <param name="continuationAction">當 <paramref name="tasks" /> 陣列中的所有工作都已經完成時，要執行的動作委派。</param>
      <param name="continuationOptions">列舉值的位元組合，這些值會控制新接續工作的行為。不支援 NotOn* 和 OnlyOn* 成員。</param>
      <exception cref="T:System.ObjectDisposedException">已處置之 <paramref name="tasks" /> 陣列中的項目。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 陣列為 null。-或-<paramref name="continuationAction" /> 引數為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 引數指定了無效的值。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 陣列為空白或包含 null 值。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],``0})">
      <summary>當一組指定的工作已完成時，建立啟動的接續工作。</summary>
      <returns>新的接續工作。</returns>
      <param name="tasks">要接續之工作的所在陣列。</param>
      <param name="continuationFunction">當 <paramref name="tasks" /> 陣列中的所有工作都已經完成時，要以非同步方式執行的函式委派。</param>
      <typeparam name="TResult">由 <paramref name="continuationFunction" /> 委派傳回，且與建立之工作相關聯的結果的類型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">已處置之 <paramref name="tasks" /> 陣列中的項目。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 陣列為 null。-或-<paramref name="continuationFunction" /> 引數為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 陣列為空白或包含 null 值。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],``0},System.Threading.CancellationToken)">
      <summary>當一組指定的工作已完成時，建立啟動的接續工作。</summary>
      <returns>新的接續工作。</returns>
      <param name="tasks">要接續之工作的所在陣列。</param>
      <param name="continuationFunction">當 <paramref name="tasks" /> 陣列中的所有工作都已經完成時，要以非同步方式執行的函式委派。</param>
      <param name="cancellationToken">要指派給新接續工作的取消語彙基元。</param>
      <typeparam name="TResult">由 <paramref name="continuationFunction" /> 委派傳回，且與建立之工作相關聯的結果的類型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">已處置之 <paramref name="tasks" /> 陣列中的項目。-或-已處置建立 <see cref="T:System.Threading.CancellationTokenSource" /> 的 <paramref name="cancellationToken" />。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 陣列為 null。-或-<paramref name="continuationFunction" /> 引數為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 陣列為空白或包含 null 值。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],``0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>當一組指定的工作已完成時，建立啟動的接續工作。</summary>
      <returns>新的接續工作。</returns>
      <param name="tasks">要接續之工作的所在陣列。</param>
      <param name="continuationFunction">當 <paramref name="tasks" /> 陣列中的所有工作都已經完成時，要以非同步方式執行的函式委派。</param>
      <param name="cancellationToken">要指派給新接續工作的取消語彙基元。</param>
      <param name="continuationOptions">列舉值的位元組合，這些值會控制新接續工作的行為。不支援 NotOn* 和 OnlyOn* 成員。</param>
      <param name="scheduler">用來排定新接續工作的物件。</param>
      <typeparam name="TResult">由 <paramref name="continuationFunction" /> 委派傳回，且與建立之工作相關聯的結果的類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 陣列為 null。-或-<paramref name="continuationFunction" /> 引數為 null。-或-<paramref name="scheduler" /> 引數為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 陣列為空白或包含 null 值。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],``0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>當一組指定的工作已完成時，建立啟動的接續工作。</summary>
      <returns>新的接續工作。</returns>
      <param name="tasks">要接續之工作的所在陣列。</param>
      <param name="continuationFunction">當 <paramref name="tasks" /> 陣列中的所有工作都已經完成時，要以非同步方式執行的函式委派。</param>
      <param name="continuationOptions">列舉值的位元組合，這些值會控制新接續工作的行為。不支援 NotOn* 和 OnlyOn* 成員。</param>
      <typeparam name="TResult">由 <paramref name="continuationFunction" /> 委派傳回，且與建立之工作相關聯的結果的類型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">已處置之 <paramref name="tasks" /> 陣列中的項目。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 陣列為 null。-或-<paramref name="continuationFunction" /> 引數為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 引數指定了無效的值。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 陣列為空白或包含 null 值。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}[]})">
      <summary>當一組指定的工作已完成時，建立啟動的接續工作。</summary>
      <returns>新的接續工作。</returns>
      <param name="tasks">要接續之工作的所在陣列。</param>
      <param name="continuationAction">當 <paramref name="tasks" /> 陣列中的所有工作都已經完成時，要執行的動作委派。</param>
      <typeparam name="TAntecedentResult">前項 <paramref name="tasks" /> 的結果類型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">已處置之 <paramref name="tasks" /> 陣列中的項目。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 陣列為 null。-或-<paramref name="continuationAction" /> 引數為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 陣列為空白或包含 null 值。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}[]},System.Threading.CancellationToken)">
      <summary>當一組指定的工作已完成時，建立啟動的接續工作。</summary>
      <returns>新的接續工作。</returns>
      <param name="tasks">要接續之工作的所在陣列。</param>
      <param name="continuationAction">當 <paramref name="tasks" /> 陣列中的所有工作都已經完成時，要執行的動作委派。</param>
      <param name="cancellationToken">要指派給新接續工作的取消語彙基元。</param>
      <typeparam name="TAntecedentResult">前項 <paramref name="tasks" /> 的結果類型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">已處置之 <paramref name="tasks" /> 陣列中的項目。-或-已處置建立 <see cref="T:System.Threading.CancellationTokenSource" /> 的 <paramref name="cancellationToken" />。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 陣列為 null。-或-<paramref name="continuationAction" /> 引數為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 陣列為空白或包含 null 值。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}[]},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>當一組指定的工作已完成時，建立啟動的接續工作。</summary>
      <returns>新的接續工作。</returns>
      <param name="tasks">要接續之工作的所在陣列。</param>
      <param name="continuationAction">當 <paramref name="tasks" /> 陣列中的所有工作都已經完成時，要執行的動作委派。</param>
      <param name="cancellationToken">要指派給新接續工作的取消語彙基元。</param>
      <param name="continuationOptions">列舉值的位元組合，這些值會控制新接續工作的行為。不支援 NotOn* 和 OnlyOn* 成員。</param>
      <param name="scheduler">用來排定新接續工作的物件。</param>
      <typeparam name="TAntecedentResult">前項 <paramref name="tasks" /> 的結果類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 陣列為 null。-或-<paramref name="continuationAction" /> 引數為 null。-或-<paramref name="scheduler" /> 引數為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 陣列為空白或包含 null 值。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}[]},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>當一組指定的工作已完成時，建立啟動的接續工作。</summary>
      <returns>新的接續工作。</returns>
      <param name="tasks">要接續之工作的所在陣列。</param>
      <param name="continuationAction">當 <paramref name="tasks" /> 陣列中的所有工作都已經完成時，要執行的動作委派。</param>
      <param name="continuationOptions">列舉值的位元組合，這些值會控制新接續工作的行為。不支援 NotOn* 和 OnlyOn* 成員。</param>
      <typeparam name="TAntecedentResult">前項 <paramref name="tasks" /> 的結果類型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">已處置之 <paramref name="tasks" /> 陣列中的項目。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 陣列為 null。-或-<paramref name="continuationAction" /> 引數為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 引數指定了無效的值。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 陣列為空白或包含 null 值。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],``1})">
      <summary>當一組指定的工作已完成時，建立啟動的接續工作。</summary>
      <returns>新的接續工作。</returns>
      <param name="tasks">要接續之工作的所在陣列。</param>
      <param name="continuationFunction">當 <paramref name="tasks" /> 陣列中的所有工作都已經完成時，要以非同步方式執行的函式委派。</param>
      <typeparam name="TAntecedentResult">前項 <paramref name="tasks" /> 的結果類型。</typeparam>
      <typeparam name="TResult">由 <paramref name="continuationFunction" /> 委派傳回，且與建立之工作相關聯的結果的類型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">已處置之 <paramref name="tasks" /> 陣列中的項目。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 陣列為 null。-或-<paramref name="continuationFunction" /> 引數為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 陣列為空白或包含 null 值。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],``1},System.Threading.CancellationToken)">
      <summary>當一組指定的工作已完成時，建立啟動的接續工作。</summary>
      <returns>新的接續工作。</returns>
      <param name="tasks">要接續之工作的所在陣列。</param>
      <param name="continuationFunction">當 <paramref name="tasks" /> 陣列中的所有工作都已經完成時，要以非同步方式執行的函式委派。</param>
      <param name="cancellationToken">要指派給新接續工作的取消語彙基元。</param>
      <typeparam name="TAntecedentResult">前項 <paramref name="tasks" /> 的結果類型。</typeparam>
      <typeparam name="TResult">由 <paramref name="continuationFunction" /> 委派傳回，且與建立之工作相關聯的結果的類型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">已處置之 <paramref name="tasks" /> 陣列中的項目。-或-<see cref="T:System.Threading.CancellationTokenSource" /> 创建<paramref name=" cancellationToken" /> 已释放。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 陣列為 null。-或-<paramref name="continuationFunction" /> 引數為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 陣列為空白或包含 null 值。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],``1},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>當一組指定的工作已完成時，建立啟動的接續工作。</summary>
      <returns>新的接續工作。</returns>
      <param name="tasks">要接續之工作的所在陣列。</param>
      <param name="continuationFunction">當 <paramref name="tasks" /> 陣列中的所有工作都已經完成時，要以非同步方式執行的函式委派。</param>
      <param name="cancellationToken">要指派給新接續工作的取消語彙基元。</param>
      <param name="continuationOptions">列舉值的位元組合，這些值會控制新接續工作的行為。不支援 NotOn* 和 OnlyOn* 成員。</param>
      <param name="scheduler">用來排定新接續工作的物件。</param>
      <typeparam name="TAntecedentResult">前項 <paramref name="tasks" /> 的結果類型。</typeparam>
      <typeparam name="TResult">由 <paramref name="continuationFunction" /> 委派傳回，且與建立之工作相關聯的結果的類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 陣列為 null。-或-<paramref name="continuationFunction" /> 引數為 null。-或-<paramref name="scheduler" /> 引數為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 陣列為空白或包含 null 值。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 引數指定了無效的值。</exception>
      <exception cref="T:System.ObjectDisposedException">已處置之 <paramref name="tasks" /> 陣列中的項目。-或-已處置建立 <see cref="T:System.Threading.CancellationTokenSource" /> 的 <paramref name="cancellationToken" />。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],``1},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>當一組指定的工作已完成時，建立啟動的接續工作。</summary>
      <returns>新的接續工作。</returns>
      <param name="tasks">要接續之工作的所在陣列。</param>
      <param name="continuationFunction">當 <paramref name="tasks" /> 陣列中的所有工作都已經完成時，要以非同步方式執行的函式委派。</param>
      <param name="continuationOptions">列舉值的位元組合，這些值會控制新接續工作的行為。不支援 NotOn* 和 OnlyOn* 成員。</param>
      <typeparam name="TAntecedentResult">前項 <paramref name="tasks" /> 的結果類型。</typeparam>
      <typeparam name="TResult">由 <paramref name="continuationFunction" /> 委派傳回，且與建立之工作相關聯的結果的類型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">已處置之 <paramref name="tasks" /> 陣列中的項目。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 陣列為 null。-或-<paramref name="continuationFunction" /> 引數為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 引數指定了無效的值。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 陣列為空白或包含 null 值。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task})">
      <summary>建立會在提供的任何一項工作完成時啟動的 <see cref="T:System.Threading.Tasks.Task" />。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="tasks">工作的陣列，當其中一個工作完成時，會接續另一個工作。</param>
      <param name="continuationAction">當 <paramref name="tasks" /> 陣列中的任一個工作已經完成時，要執行的動作委派。</param>
      <exception cref="T:System.ObjectDisposedException">已處置 <paramref name="tasks" /> 陣列的其中一個項目。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 陣列為 null。-或-<paramref name="continuationAction" /> 参数是 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 数组包含 null 值。-或-<paramref name="tasks" /> 数组为空。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task},System.Threading.CancellationToken)">
      <summary>建立會在提供的任何一項工作完成時啟動的 <see cref="T:System.Threading.Tasks.Task" />。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="tasks">工作的陣列，當其中一個工作完成時，會接續另一個工作。</param>
      <param name="continuationAction">當 <paramref name="tasks" /> 陣列中的任一個工作已經完成時，要執行的動作委派。</param>
      <param name="cancellationToken">要指派給新接續工作的 <see cref="T:System.Threading.CancellationToken" />。</param>
      <exception cref="T:System.ObjectDisposedException">已處置 <paramref name="tasks" /> 陣列的其中一個項目。-或-<paramref name="cancellationToken" /> 已释放。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 陣列為 null。-或-<paramref name="continuationAction" /> 引數為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 数组包含 null 值。-或-<paramref name="tasks" /> 数组为空。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>建立會在提供的任何一項工作完成時啟動的 <see cref="T:System.Threading.Tasks.Task" />。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="tasks">工作的陣列，當其中一個工作完成時，會接續另一個工作。</param>
      <param name="continuationAction">當 <paramref name="tasks" /> 陣列中的任一個工作已經完成時，要執行的動作委派。</param>
      <param name="cancellationToken">要指派給新接續工作的 <see cref="T:System.Threading.CancellationToken" />。</param>
      <param name="continuationOptions">
        <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> 值，控制建立之接續 <see cref="T:System.Threading.Tasks.Task" /> 的行為。</param>
      <param name="scheduler">
        <see cref="T:System.Threading.Tasks.TaskScheduler" />，用來排定建立的接續 <see cref="T:System.Threading.Tasks.Task" />。</param>
      <exception cref="T:System.ArgumentNullException">當 <paramref name="tasks" /> 陣列是 Null 時，所擲回的例外狀況。-或-<paramref name="continuationAction" /> 引數為 Null 時，所擲回的例外狀況。-或-<paramref name="scheduler" /> 引數為 Null 時，所擲回的例外狀況。</exception>
      <exception cref="T:System.ArgumentException">當 <paramref name="tasks" /> 陣列包含 Null 值時，所擲回的例外狀況。-或-當 <paramref name="tasks" /> 陣列是空的時，所擲回的例外狀況。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>建立會在提供的任何一項工作完成時啟動的 <see cref="T:System.Threading.Tasks.Task" />。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="tasks">工作的陣列，當其中一個工作完成時，會接續另一個工作。</param>
      <param name="continuationAction">當 <paramref name="tasks" /> 陣列中的任一個工作已經完成時，要執行的動作委派。</param>
      <param name="continuationOptions">
        <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> 值，控制建立之接續 <see cref="T:System.Threading.Tasks.Task" /> 的行為。</param>
      <exception cref="T:System.ObjectDisposedException">處置 <paramref name="tasks" /> 陣列中其中一個項目時，所擲回的例外狀況。</exception>
      <exception cref="T:System.ArgumentNullException">當 <paramref name="tasks" /> 陣列是 Null 時，所擲回的例外狀況。-或-<paramref name="continuationAction" /> 引數為 Null 時，所擲回的例外狀況。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 引數指定不正確的 TaskContinuationOptions 值時，所擲回的例外狀況。</exception>
      <exception cref="T:System.ArgumentException">當 <paramref name="tasks" /> 陣列包含 Null 值時，所擲回的例外狀況。-或-當 <paramref name="tasks" /> 陣列是空的時，所擲回的例外狀況。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,``0})">
      <summary>建立會在提供的任何一項工作完成時啟動的 <see cref="T:System.Threading.Tasks.Task`1" />。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="tasks">工作的陣列，當其中一個工作完成時，會接續另一個工作。</param>
      <param name="continuationFunction">當 <paramref name="tasks" /> 陣列中的任一個工作已經完成時，要以非同步方式執行的函式委派。</param>
      <typeparam name="TResult">由 <paramref name="continuationFunction" /> 委派傳回，且與建立之 <see cref="T:System.Threading.Tasks.Task`1" /> 相關聯的結果的類型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">處置 <paramref name="tasks" /> 陣列中其中一個項目時，所擲回的例外狀況。</exception>
      <exception cref="T:System.ArgumentNullException">當 <paramref name="tasks" /> 陣列是 Null 時，所擲回的例外狀況。-或-<paramref name="continuationFunction" /> 引數為 Null 時，所擲回的例外狀況。</exception>
      <exception cref="T:System.ArgumentException">當 <paramref name="tasks" /> 陣列包含 Null 值時，所擲回的例外狀況。-或-當 <paramref name="tasks" /> 陣列是空的時，所擲回的例外狀況。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,``0},System.Threading.CancellationToken)">
      <summary>建立會在提供的任何一項工作完成時啟動的 <see cref="T:System.Threading.Tasks.Task`1" />。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="tasks">工作的陣列，當其中一個工作完成時，會接續另一個工作。</param>
      <param name="continuationFunction">當 <paramref name="tasks" /> 陣列中的任一個工作已經完成時，要以非同步方式執行的函式委派。</param>
      <param name="cancellationToken">要指派給新接續工作的 <see cref="T:System.Threading.CancellationToken" />。</param>
      <typeparam name="TResult">由 <paramref name="continuationFunction" /> 委派傳回，且與建立之 <see cref="T:System.Threading.Tasks.Task`1" /> 相關聯的結果的類型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">處置 <paramref name="tasks" /> 陣列中其中一個項目時，所擲回的例外狀況。-或-已經處置所提供的 <see cref="T:System.Threading.CancellationToken" />。</exception>
      <exception cref="T:System.ArgumentNullException">當 <paramref name="tasks" /> 陣列是 Null 時，所擲回的例外狀況。-或-<paramref name="continuationFunction" /> 引數為 Null 時，所擲回的例外狀況。</exception>
      <exception cref="T:System.ArgumentException">當 <paramref name="tasks" /> 陣列包含 Null 值時，所擲回的例外狀況。-或-當 <paramref name="tasks" /> 陣列是空的時，所擲回的例外狀況。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,``0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>建立會在提供的任何一項工作完成時啟動的 <see cref="T:System.Threading.Tasks.Task`1" />。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="tasks">工作的陣列，當其中一個工作完成時，會接續另一個工作。</param>
      <param name="continuationFunction">當 <paramref name="tasks" /> 陣列中的任一個工作已經完成時，要以非同步方式執行的函式委派。</param>
      <param name="cancellationToken">要指派給新接續工作的 <see cref="T:System.Threading.CancellationToken" />。</param>
      <param name="continuationOptions">
        <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> 值，控制建立之接續 <see cref="T:System.Threading.Tasks.Task`1" /> 的行為。</param>
      <param name="scheduler">
        <see cref="T:System.Threading.Tasks.TaskScheduler" />，用來排定建立的接續 <see cref="T:System.Threading.Tasks.Task`1" />。</param>
      <typeparam name="TResult">由 <paramref name="continuationFunction" /> 委派傳回，且與建立之 <see cref="T:System.Threading.Tasks.Task`1" /> 相關聯的結果的類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">當 <paramref name="tasks" /> 陣列是 Null 時，所擲回的例外狀況。-或-<paramref name="continuationFunction" /> 引數為 Null 時，所擲回的例外狀況。-或-<paramref name="scheduler" /> 引數為 Null 時，所擲回的例外狀況。</exception>
      <exception cref="T:System.ArgumentException">當 <paramref name="tasks" /> 陣列包含 Null 值時，所擲回的例外狀況。-或-當 <paramref name="tasks" /> 陣列是空的時，所擲回的例外狀況。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,``0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>建立會在提供的任何一項工作完成時啟動的 <see cref="T:System.Threading.Tasks.Task`1" />。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="tasks">工作的陣列，當其中一個工作完成時，會接續另一個工作。</param>
      <param name="continuationFunction">當 <paramref name="tasks" /> 陣列中的任一個工作已經完成時，要以非同步方式執行的函式委派。</param>
      <param name="continuationOptions">
        <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> 值，控制建立之接續 <see cref="T:System.Threading.Tasks.Task`1" /> 的行為。</param>
      <typeparam name="TResult">由 <paramref name="continuationFunction" /> 委派傳回，且與建立之 <see cref="T:System.Threading.Tasks.Task`1" /> 相關聯的結果的類型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">處置 <paramref name="tasks" /> 陣列中其中一個項目時，所擲回的例外狀況。</exception>
      <exception cref="T:System.ArgumentNullException">當 <paramref name="tasks" /> 陣列是 Null 時，所擲回的例外狀況。-或-<paramref name="continuationFunction" /> 引數為 Null 時，所擲回的例外狀況。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 引數指定不正確的 TaskContinuationOptions 值時，所擲回的例外狀況。</exception>
      <exception cref="T:System.ArgumentException">當 <paramref name="tasks" /> 陣列包含 Null 值時，所擲回的例外狀況。-或-當 <paramref name="tasks" /> 陣列是空的時，所擲回的例外狀況。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}})">
      <summary>建立會在提供的任何一項工作完成時啟動的 <see cref="T:System.Threading.Tasks.Task" />。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="tasks">工作的陣列，當其中一個工作完成時，會接續另一個工作。</param>
      <param name="continuationAction">當 <paramref name="tasks" /> 陣列中的任一個工作已經完成時，要執行的動作委派。</param>
      <typeparam name="TAntecedentResult">前項 <paramref name="tasks" /> 的結果類型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">處置 <paramref name="tasks" /> 陣列中其中一個項目時，所擲回的例外狀況。</exception>
      <exception cref="T:System.ArgumentNullException">當 <paramref name="tasks" /> 陣列是 Null 時，所擲回的例外狀況。-或-<paramref name="continuationAction" /> 引數為 Null 時，所擲回的例外狀況。</exception>
      <exception cref="T:System.ArgumentException">當 <paramref name="tasks" /> 陣列包含 Null 值時，所擲回的例外狀況。-或-當 <paramref name="tasks" /> 陣列是空的時，所擲回的例外狀況。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}},System.Threading.CancellationToken)">
      <summary>建立會在提供的任何一項工作完成時啟動的 <see cref="T:System.Threading.Tasks.Task" />。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="tasks">工作的陣列，當其中一個工作完成時，會接續另一個工作。</param>
      <param name="continuationAction">當 <paramref name="tasks" /> 陣列中的任一個工作已經完成時，要執行的動作委派。</param>
      <param name="cancellationToken">要指派給新接續工作的 <see cref="T:System.Threading.CancellationToken" />。</param>
      <typeparam name="TAntecedentResult">前項 <paramref name="tasks" /> 的結果類型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">處置 <paramref name="tasks" /> 陣列中其中一個項目時，所擲回的例外狀況。-或-已經處置所提供的 <see cref="T:System.Threading.CancellationToken" />。</exception>
      <exception cref="T:System.ArgumentNullException">當 <paramref name="tasks" /> 陣列是 Null 時，所擲回的例外狀況。-或-<paramref name="continuationAction" /> 引數為 Null 時，所擲回的例外狀況。</exception>
      <exception cref="T:System.ArgumentException">當 <paramref name="tasks" /> 陣列包含 Null 值時，所擲回的例外狀況。-或-當 <paramref name="tasks" /> 陣列是空的時，所擲回的例外狀況。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>建立會在提供的任何一項工作完成時啟動的 <see cref="T:System.Threading.Tasks.Task" />。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="tasks">工作的陣列，當其中一個工作完成時，會接續另一個工作。</param>
      <param name="continuationAction">當 <paramref name="tasks" /> 陣列中的任一個工作已經完成時，要執行的動作委派。</param>
      <param name="cancellationToken">要指派給新接續工作的 <see cref="T:System.Threading.CancellationToken" />。</param>
      <param name="continuationOptions">
        <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> 值，控制建立之接續 <see cref="T:System.Threading.Tasks.Task" /> 的行為。</param>
      <param name="scheduler">
        <see cref="T:System.Threading.Tasks.TaskScheduler" />，用來排定建立的接續 <see cref="T:System.Threading.Tasks.Task`1" />。</param>
      <typeparam name="TAntecedentResult">前項 <paramref name="tasks" /> 的結果類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">當 <paramref name="tasks" /> 陣列是 Null 時，所擲回的例外狀況。-或-<paramref name="continuationAction" /> 引數為 Null 時，所擲回的例外狀況。-或-<paramref name="scheduler" /> 引數為 Null 時，所擲回的例外狀況。</exception>
      <exception cref="T:System.ArgumentException">當 <paramref name="tasks" /> 陣列包含 Null 值時，所擲回的例外狀況。-或-當 <paramref name="tasks" /> 陣列是空的時，所擲回的例外狀況。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>建立會在提供的任何一項工作完成時啟動的 <see cref="T:System.Threading.Tasks.Task" />。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="tasks">工作的陣列，當其中一個工作完成時，會接續另一個工作。</param>
      <param name="continuationAction">當 <paramref name="tasks" /> 陣列中的任一個工作已經完成時，要執行的動作委派。</param>
      <param name="continuationOptions">
        <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> 值，控制建立之接續 <see cref="T:System.Threading.Tasks.Task" /> 的行為。</param>
      <typeparam name="TAntecedentResult">前項 <paramref name="tasks" /> 的結果類型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">處置 <paramref name="tasks" /> 陣列中其中一個項目時，所擲回的例外狀況。</exception>
      <exception cref="T:System.ArgumentNullException">當 <paramref name="tasks" /> 陣列是 Null 時，所擲回的例外狀況。-或-<paramref name="continuationAction" /> 引數為 Null 時，所擲回的例外狀況。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 引數指定不正確的 TaskContinuationOptions 值時，所擲回的例外狀況。</exception>
      <exception cref="T:System.ArgumentException">當 <paramref name="tasks" /> 陣列包含 Null 值時，所擲回的例外狀況。-或-當 <paramref name="tasks" /> 陣列是空的時，所擲回的例外狀況。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},``1})">
      <summary>建立會在提供的任何一項工作完成時啟動的 <see cref="T:System.Threading.Tasks.Task`1" />。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="tasks">工作的陣列，當其中一個工作完成時，會接續另一個工作。</param>
      <param name="continuationFunction">當 <paramref name="tasks" /> 陣列中的任一個工作已經完成時，要以非同步方式執行的函式委派。</param>
      <typeparam name="TAntecedentResult">前項 <paramref name="tasks" /> 的結果類型。</typeparam>
      <typeparam name="TResult">由 <paramref name="continuationFunction" /> 委派傳回，且與建立之 <see cref="T:System.Threading.Tasks.Task`1" /> 相關聯的結果的類型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">處置 <paramref name="tasks" /> 陣列中其中一個項目時，所擲回的例外狀況。</exception>
      <exception cref="T:System.ArgumentNullException">當 <paramref name="tasks" /> 陣列是 Null 時，所擲回的例外狀況。-或-<paramref name="continuationFunction" /> 引數為 Null 時，所擲回的例外狀況。</exception>
      <exception cref="T:System.ArgumentException">當 <paramref name="tasks" /> 陣列包含 Null 值時，所擲回的例外狀況。-或-當 <paramref name="tasks" /> 陣列是空的時，所擲回的例外狀況。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},``1},System.Threading.CancellationToken)">
      <summary>建立會在提供的任何一項工作完成時啟動的 <see cref="T:System.Threading.Tasks.Task`1" />。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="tasks">工作的陣列，當其中一個工作完成時，會接續另一個工作。</param>
      <param name="continuationFunction">當 <paramref name="tasks" /> 陣列中的任一個工作已經完成時，要以非同步方式執行的函式委派。</param>
      <param name="cancellationToken">要指派給新接續工作的 <see cref="T:System.Threading.CancellationToken" />。</param>
      <typeparam name="TAntecedentResult">前項 <paramref name="tasks" /> 的結果類型。</typeparam>
      <typeparam name="TResult">由 <paramref name="continuationFunction" /> 委派傳回，且與建立之 <see cref="T:System.Threading.Tasks.Task`1" /> 相關聯的結果的類型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">處置 <paramref name="tasks" /> 陣列中其中一個項目時，所擲回的例外狀況。-或-已經處置所提供的 <see cref="T:System.Threading.CancellationToken" />。</exception>
      <exception cref="T:System.ArgumentNullException">當 <paramref name="tasks" /> 陣列是 Null 時，所擲回的例外狀況。-或-<paramref name="continuationFunction" /> 引數為 Null 時，所擲回的例外狀況。</exception>
      <exception cref="T:System.ArgumentException">當 <paramref name="tasks" /> 陣列包含 Null 值時，所擲回的例外狀況。-或-當 <paramref name="tasks" /> 陣列是空的時，所擲回的例外狀況。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},``1},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>建立會在提供的任何一項工作完成時啟動的 <see cref="T:System.Threading.Tasks.Task`1" />。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="tasks">工作的陣列，當其中一個工作完成時，會接續另一個工作。</param>
      <param name="continuationFunction">當 <paramref name="tasks" /> 陣列中的任一個工作已經完成時，要以非同步方式執行的函式委派。</param>
      <param name="cancellationToken">要指派給新接續工作的 <see cref="T:System.Threading.CancellationToken" />。</param>
      <param name="continuationOptions">
        <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> 值，控制建立之接續 <see cref="T:System.Threading.Tasks.Task`1" /> 的行為。</param>
      <param name="scheduler">
        <see cref="T:System.Threading.Tasks.TaskScheduler" />，用來排定建立的接續 <see cref="T:System.Threading.Tasks.Task`1" />。</param>
      <typeparam name="TAntecedentResult">前項 <paramref name="tasks" /> 的結果類型。</typeparam>
      <typeparam name="TResult">由 <paramref name="continuationFunction" /> 委派傳回，且與建立之 <see cref="T:System.Threading.Tasks.Task`1" /> 相關聯的結果的類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">當 <paramref name="tasks" /> 陣列是 Null 時，所擲回的例外狀況。-或-<paramref name="continuationFunction" /> 引數為 Null 時，所擲回的例外狀況。-或-<paramref name="scheduler" /> 引數為 Null 時，所擲回的例外狀況。</exception>
      <exception cref="T:System.ArgumentException">當 <paramref name="tasks" /> 陣列包含 Null 值時，所擲回的例外狀況。-或-當 <paramref name="tasks" /> 陣列是空的時，所擲回的例外狀況。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},``1},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>建立會在提供的任何一項工作完成時啟動的 <see cref="T:System.Threading.Tasks.Task`1" />。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="tasks">工作的陣列，當其中一個工作完成時，會接續另一個工作。</param>
      <param name="continuationFunction">當 <paramref name="tasks" /> 陣列中的任一個工作已經完成時，要以非同步方式執行的函式委派。</param>
      <param name="continuationOptions">
        <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> 值，控制建立之接續 <see cref="T:System.Threading.Tasks.Task`1" /> 的行為。</param>
      <typeparam name="TAntecedentResult">前項 <paramref name="tasks" /> 的結果類型。</typeparam>
      <typeparam name="TResult">由 <paramref name="continuationFunction" /> 委派傳回，且與建立之 <see cref="T:System.Threading.Tasks.Task`1" /> 相關聯的結果的類型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">處置 <paramref name="tasks" /> 陣列中其中一個項目時，所擲回的例外狀況。</exception>
      <exception cref="T:System.ArgumentNullException">當 <paramref name="tasks" /> 陣列是 Null 時，所擲回的例外狀況。-或-<paramref name="continuationFunction" /> 引數為 Null 時，所擲回的例外狀況。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 引數指定不正確的 TaskContinuationOptions 值時，所擲回的例外狀況。</exception>
      <exception cref="T:System.ArgumentException">當 <paramref name="tasks" /> 陣列包含 Null 值時，所擲回的例外狀況。-或-當 <paramref name="tasks" /> 陣列是空的時，所擲回的例外狀況。</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory.CreationOptions">
      <summary>取得這個工作 Factory 的預設工作建立選項。</summary>
      <returns>這個工作 Factory 的預設工作建立選項。</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object)">
      <summary>建立 <see cref="T:System.Threading.Tasks.Task" />，表示一組符合「非同步程式設計模型」模式的開始和結束方法。</summary>
      <returns>建立的 <see cref="T:System.Threading.Tasks.Task" />，表示非同步作業。</returns>
      <param name="beginMethod">開始非同步作業的委派。</param>
      <param name="endMethod">結束非同步作業的委派。</param>
      <param name="state">物件，包含 <paramref name="beginMethod" /> 委派要使用的資料。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 引數為 Null 時，所擲回的例外狀況。-或-<paramref name="endMethod" /> 引數為 Null 時，所擲回的例外狀況。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>建立 <see cref="T:System.Threading.Tasks.Task" />，表示一組符合「非同步程式設計模型」模式的開始和結束方法。</summary>
      <returns>建立的 <see cref="T:System.Threading.Tasks.Task" />，表示非同步作業。</returns>
      <param name="beginMethod">開始非同步作業的委派。</param>
      <param name="endMethod">結束非同步作業的委派。</param>
      <param name="state">物件，包含 <paramref name="beginMethod" /> 委派要使用的資料。</param>
      <param name="creationOptions">TaskCreationOptions 值，控制建立之 <see cref="T:System.Threading.Tasks.Task" /> 的行為。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 引數為 Null 時，所擲回的例外狀況。-或-<paramref name="endMethod" /> 引數為 Null 時，所擲回的例外狀況。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 引數指定不正確的 TaskCreationOptions 值時，所擲回的例外狀況。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``0},System.Object)">
      <summary>建立 <see cref="T:System.Threading.Tasks.Task`1" />，表示一組符合「非同步程式設計模型」模式的開始和結束方法。</summary>
      <returns>建立的 <see cref="T:System.Threading.Tasks.Task`1" />，表示非同步作業。</returns>
      <param name="beginMethod">開始非同步作業的委派。</param>
      <param name="endMethod">結束非同步作業的委派。</param>
      <param name="state">物件，包含 <paramref name="beginMethod" /> 委派要使用的資料。</param>
      <typeparam name="TResult">透過 <see cref="T:System.Threading.Tasks.Task`1" /> 取得的結果類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 引數為 Null 時，所擲回的例外狀況。-或-<paramref name="endMethod" /> 引數為 Null 時，所擲回的例外狀況。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``0},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>建立 <see cref="T:System.Threading.Tasks.Task`1" />，表示一組符合「非同步程式設計模型」模式的開始和結束方法。</summary>
      <returns>建立的 <see cref="T:System.Threading.Tasks.Task`1" />，表示非同步作業。</returns>
      <param name="beginMethod">開始非同步作業的委派。</param>
      <param name="endMethod">結束非同步作業的委派。</param>
      <param name="state">物件，包含 <paramref name="beginMethod" /> 委派要使用的資料。</param>
      <param name="creationOptions">TaskCreationOptions 值，控制建立之 <see cref="T:System.Threading.Tasks.Task`1" /> 的行為。</param>
      <typeparam name="TResult">透過 <see cref="T:System.Threading.Tasks.Task`1" /> 取得的結果類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 引數為 Null 時，所擲回的例外狀況。-或-<paramref name="endMethod" /> 引數為 Null 時，所擲回的例外狀況。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 引數指定不正確的 TaskCreationOptions 值時，所擲回的例外狀況。<paramref name="creationOptions" /> 引數指定不正確的 TaskCreationOptions 值時，所擲回的例外狀況。如需詳細資訊，請參閱 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> 的＜備註＞</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,System.Object)">
      <summary>建立 <see cref="T:System.Threading.Tasks.Task" />，表示一組符合「非同步程式設計模型」模式的開始和結束方法。</summary>
      <returns>建立的 <see cref="T:System.Threading.Tasks.Task" />，表示非同步作業。</returns>
      <param name="beginMethod">開始非同步作業的委派。</param>
      <param name="endMethod">結束非同步作業的委派。</param>
      <param name="arg1">傳遞至 <paramref name="beginMethod" /> 委派的第一個引數。</param>
      <param name="state">物件，包含 <paramref name="beginMethod" /> 委派要使用的資料。</param>
      <typeparam name="TArg1">傳遞至 <paramref name="beginMethod" /> 委派的第一個引數類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 引數為 Null 時，所擲回的例外狀況。-或-<paramref name="endMethod" /> 引數為 Null 時，所擲回的例外狀況。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>建立 <see cref="T:System.Threading.Tasks.Task" />，表示一組符合「非同步程式設計模型」模式的開始和結束方法。</summary>
      <returns>建立的 <see cref="T:System.Threading.Tasks.Task" />，表示非同步作業。</returns>
      <param name="beginMethod">開始非同步作業的委派。</param>
      <param name="endMethod">結束非同步作業的委派。</param>
      <param name="arg1">傳遞至 <paramref name="beginMethod" /> 委派的第一個引數。</param>
      <param name="state">物件，包含 <paramref name="beginMethod" /> 委派要使用的資料。</param>
      <param name="creationOptions">TaskCreationOptions 值，控制建立之 <see cref="T:System.Threading.Tasks.Task" /> 的行為。</param>
      <typeparam name="TArg1">傳遞至 <paramref name="beginMethod" /> 委派的第一個引數類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 引數為 Null 時，所擲回的例外狀況。-或-<paramref name="endMethod" /> 引數為 Null 時，所擲回的例外狀況。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 引數指定不正確的 TaskCreationOptions 值時，所擲回的例外狀況。<paramref name="creationOptions" /> 引數指定不正確的 TaskCreationOptions 值時，所擲回的例外狀況。如需詳細資訊，請參閱 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> 的＜備註＞</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``2(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``1},``0,System.Object)">
      <summary>建立 <see cref="T:System.Threading.Tasks.Task`1" />，表示一組符合「非同步程式設計模型」模式的開始和結束方法。</summary>
      <returns>建立的 <see cref="T:System.Threading.Tasks.Task`1" />，表示非同步作業。</returns>
      <param name="beginMethod">開始非同步作業的委派。</param>
      <param name="endMethod">結束非同步作業的委派。</param>
      <param name="arg1">傳遞至 <paramref name="beginMethod" /> 委派的第一個引數。</param>
      <param name="state">物件，包含 <paramref name="beginMethod" /> 委派要使用的資料。</param>
      <typeparam name="TArg1">傳遞至 <paramref name="beginMethod" /> 委派的第一個引數類型。</typeparam>
      <typeparam name="TResult">透過 <see cref="T:System.Threading.Tasks.Task`1" /> 取得的結果類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 引數為 Null 時，所擲回的例外狀況。-或-<paramref name="endMethod" /> 引數為 Null 時，所擲回的例外狀況。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``2(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``1},``0,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>建立 <see cref="T:System.Threading.Tasks.Task`1" />，表示一組符合「非同步程式設計模型」模式的開始和結束方法。</summary>
      <returns>建立的 <see cref="T:System.Threading.Tasks.Task`1" />，表示非同步作業。</returns>
      <param name="beginMethod">開始非同步作業的委派。</param>
      <param name="endMethod">結束非同步作業的委派。</param>
      <param name="arg1">傳遞至 <paramref name="beginMethod" /> 委派的第一個引數。</param>
      <param name="state">物件，包含 <paramref name="beginMethod" /> 委派要使用的資料。</param>
      <param name="creationOptions">TaskCreationOptions 值，控制建立之 <see cref="T:System.Threading.Tasks.Task`1" /> 的行為。</param>
      <typeparam name="TArg1">傳遞至 <paramref name="beginMethod" /> 委派的第一個引數類型。</typeparam>
      <typeparam name="TResult">透過 <see cref="T:System.Threading.Tasks.Task`1" /> 取得的結果類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 引數為 Null 時，所擲回的例外狀況。-或-<paramref name="endMethod" /> 引數為 Null 時，所擲回的例外狀況。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 引數指定不正確的 TaskCreationOptions 值時，所擲回的例外狀況。<paramref name="creationOptions" /> 引數指定不正確的 TaskCreationOptions 值時，所擲回的例外狀況。如需詳細資訊，請參閱 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> 的＜備註＞</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``2(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,``1,System.Object)">
      <summary>建立 <see cref="T:System.Threading.Tasks.Task" />，表示一組符合「非同步程式設計模型」模式的開始和結束方法。</summary>
      <returns>建立的 <see cref="T:System.Threading.Tasks.Task" />，表示非同步作業。</returns>
      <param name="beginMethod">開始非同步作業的委派。</param>
      <param name="endMethod">結束非同步作業的委派。</param>
      <param name="arg1">傳遞至 <paramref name="beginMethod" /> 委派的第一個引數。</param>
      <param name="arg2">傳遞至 <paramref name="beginMethod" /> 委派的第二個引數。</param>
      <param name="state">物件，包含 <paramref name="beginMethod" /> 委派要使用的資料。</param>
      <typeparam name="TArg1">傳遞至 <paramref name="beginMethod" /> 委派的第二個引數類型。</typeparam>
      <typeparam name="TArg2">傳遞至 <paramref name="beginMethod" /> 委派的第一個引數類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 引數為 Null 時，所擲回的例外狀況。-或-<paramref name="endMethod" /> 引數為 Null 時，所擲回的例外狀況。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``2(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,``1,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>建立 <see cref="T:System.Threading.Tasks.Task" />，表示一組符合「非同步程式設計模型」模式的開始和結束方法。</summary>
      <returns>建立的 <see cref="T:System.Threading.Tasks.Task" />，表示非同步作業。</returns>
      <param name="beginMethod">開始非同步作業的委派。</param>
      <param name="endMethod">結束非同步作業的委派。</param>
      <param name="arg1">傳遞至 <paramref name="beginMethod" /> 委派的第一個引數。</param>
      <param name="arg2">傳遞至 <paramref name="beginMethod" /> 委派的第二個引數。</param>
      <param name="state">物件，包含 <paramref name="beginMethod" /> 委派要使用的資料。</param>
      <param name="creationOptions">TaskCreationOptions 值，控制建立之 <see cref="T:System.Threading.Tasks.Task" /> 的行為。</param>
      <typeparam name="TArg1">傳遞至 <paramref name="beginMethod" /> 委派的第二個引數類型。</typeparam>
      <typeparam name="TArg2">傳遞至 <paramref name="beginMethod" /> 委派的第一個引數類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 引數為 Null 時，所擲回的例外狀況。-或-<paramref name="endMethod" /> 引數為 Null 時，所擲回的例外狀況。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 引數指定不正確的 TaskCreationOptions 值時，所擲回的例外狀況。<paramref name="creationOptions" /> 引數指定不正確的 TaskCreationOptions 值時，所擲回的例外狀況。如需詳細資訊，請參閱 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> 的＜備註＞</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``3(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``2},``0,``1,System.Object)">
      <summary>建立 <see cref="T:System.Threading.Tasks.Task`1" />，表示一組符合「非同步程式設計模型」模式的開始和結束方法。</summary>
      <returns>建立的 <see cref="T:System.Threading.Tasks.Task`1" />，表示非同步作業。</returns>
      <param name="beginMethod">開始非同步作業的委派。</param>
      <param name="endMethod">結束非同步作業的委派。</param>
      <param name="arg1">傳遞至 <paramref name="beginMethod" /> 委派的第一個引數。</param>
      <param name="arg2">傳遞至 <paramref name="beginMethod" /> 委派的第二個引數。</param>
      <param name="state">物件，包含 <paramref name="beginMethod" /> 委派要使用的資料。</param>
      <typeparam name="TArg1">傳遞至 <paramref name="beginMethod" /> 委派的第二個引數類型。</typeparam>
      <typeparam name="TArg2">傳遞至 <paramref name="beginMethod" /> 委派的第一個引數類型。</typeparam>
      <typeparam name="TResult">透過 <see cref="T:System.Threading.Tasks.Task`1" /> 取得的結果類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 引數為 Null 時，所擲回的例外狀況。-或-<paramref name="endMethod" /> 引數為 Null 時，所擲回的例外狀況。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``3(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``2},``0,``1,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>建立 <see cref="T:System.Threading.Tasks.Task`1" />，表示一組符合「非同步程式設計模型」模式的開始和結束方法。</summary>
      <returns>建立的 <see cref="T:System.Threading.Tasks.Task`1" />，表示非同步作業。</returns>
      <param name="beginMethod">開始非同步作業的委派。</param>
      <param name="endMethod">結束非同步作業的委派。</param>
      <param name="arg1">傳遞至 <paramref name="beginMethod" /> 委派的第一個引數。</param>
      <param name="arg2">傳遞至 <paramref name="beginMethod" /> 委派的第二個引數。</param>
      <param name="state">物件，包含 <paramref name="beginMethod" /> 委派要使用的資料。</param>
      <param name="creationOptions">TaskCreationOptions 值，控制建立之 <see cref="T:System.Threading.Tasks.Task`1" /> 的行為。</param>
      <typeparam name="TArg1">傳遞至 <paramref name="beginMethod" /> 委派的第二個引數類型。</typeparam>
      <typeparam name="TArg2">傳遞至 <paramref name="beginMethod" /> 委派的第一個引數類型。</typeparam>
      <typeparam name="TResult">透過 <see cref="T:System.Threading.Tasks.Task`1" /> 取得的結果類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 引數為 Null 時，所擲回的例外狀況。-或-<paramref name="endMethod" /> 引數為 Null 時，所擲回的例外狀況。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 引數指定不正確的 TaskCreationOptions 值時，所擲回的例外狀況。<paramref name="creationOptions" /> 引數指定不正確的 TaskCreationOptions 值時，所擲回的例外狀況。如需詳細資訊，請參閱 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> 的＜備註＞</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``3(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,``1,``2,System.Object)">
      <summary>建立 <see cref="T:System.Threading.Tasks.Task" />，表示一組符合「非同步程式設計模型」模式的開始和結束方法。</summary>
      <returns>建立的 <see cref="T:System.Threading.Tasks.Task" />，表示非同步作業。</returns>
      <param name="beginMethod">開始非同步作業的委派。</param>
      <param name="endMethod">結束非同步作業的委派。</param>
      <param name="arg1">傳遞至 <paramref name="beginMethod" /> 委派的第一個引數。</param>
      <param name="arg2">傳遞至 <paramref name="beginMethod" /> 委派的第二個引數。</param>
      <param name="arg3">傳遞至 <paramref name="beginMethod" /> 委派的第三個引數。</param>
      <param name="state">物件，包含 <paramref name="beginMethod" /> 委派要使用的資料。</param>
      <typeparam name="TArg1">傳遞至 <paramref name="beginMethod" /> 委派的第二個引數類型。</typeparam>
      <typeparam name="TArg2">傳遞至 <paramref name="beginMethod" /> 委派的第三個引數類型。</typeparam>
      <typeparam name="TArg3">傳遞至 <paramref name="beginMethod" /> 委派的第一個引數類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 引數為 Null 時，所擲回的例外狀況。-或-<paramref name="endMethod" /> 引數為 Null 時，所擲回的例外狀況。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``3(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,``1,``2,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>建立 <see cref="T:System.Threading.Tasks.Task" />，表示一組符合「非同步程式設計模型」模式的開始和結束方法。</summary>
      <returns>建立的 <see cref="T:System.Threading.Tasks.Task" />，表示非同步作業。</returns>
      <param name="beginMethod">開始非同步作業的委派。</param>
      <param name="endMethod">結束非同步作業的委派。</param>
      <param name="arg1">傳遞至 <paramref name="beginMethod" /> 委派的第一個引數。</param>
      <param name="arg2">傳遞至 <paramref name="beginMethod" /> 委派的第二個引數。</param>
      <param name="arg3">傳遞至 <paramref name="beginMethod" /> 委派的第三個引數。</param>
      <param name="state">物件，包含 <paramref name="beginMethod" /> 委派要使用的資料。</param>
      <param name="creationOptions">TaskCreationOptions 值，控制建立之 <see cref="T:System.Threading.Tasks.Task" /> 的行為。</param>
      <typeparam name="TArg1">傳遞至 <paramref name="beginMethod" /> 委派的第二個引數類型。</typeparam>
      <typeparam name="TArg2">傳遞至 <paramref name="beginMethod" /> 委派的第三個引數類型。</typeparam>
      <typeparam name="TArg3">傳遞至 <paramref name="beginMethod" /> 委派的第一個引數類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 引數為 Null 時，所擲回的例外狀況。-或-<paramref name="endMethod" /> 引數為 Null 時，所擲回的例外狀況。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 引數指定不正確的 TaskCreationOptions 值時，所擲回的例外狀況。<paramref name="creationOptions" /> 引數指定不正確的 TaskCreationOptions 值時，所擲回的例外狀況。如需詳細資訊，請參閱 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> 的＜備註＞</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``4(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``3},``0,``1,``2,System.Object)">
      <summary>建立 <see cref="T:System.Threading.Tasks.Task`1" />，表示一組符合「非同步程式設計模型」模式的開始和結束方法。</summary>
      <returns>建立的 <see cref="T:System.Threading.Tasks.Task`1" />，表示非同步作業。</returns>
      <param name="beginMethod">開始非同步作業的委派。</param>
      <param name="endMethod">結束非同步作業的委派。</param>
      <param name="arg1">傳遞至 <paramref name="beginMethod" /> 委派的第一個引數。</param>
      <param name="arg2">傳遞至 <paramref name="beginMethod" /> 委派的第二個引數。</param>
      <param name="arg3">傳遞至 <paramref name="beginMethod" /> 委派的第三個引數。</param>
      <param name="state">物件，包含 <paramref name="beginMethod" /> 委派要使用的資料。</param>
      <typeparam name="TArg1">傳遞至 <paramref name="beginMethod" /> 委派的第二個引數類型。</typeparam>
      <typeparam name="TArg2">傳遞至 <paramref name="beginMethod" /> 委派的第三個引數類型。</typeparam>
      <typeparam name="TArg3">傳遞至 <paramref name="beginMethod" /> 委派的第一個引數類型。</typeparam>
      <typeparam name="TResult">透過 <see cref="T:System.Threading.Tasks.Task`1" /> 取得的結果類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 引數為 Null 時，所擲回的例外狀況。-或-<paramref name="endMethod" /> 引數為 Null 時，所擲回的例外狀況。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``4(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``3},``0,``1,``2,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>建立 <see cref="T:System.Threading.Tasks.Task`1" />，表示一組符合「非同步程式設計模型」模式的開始和結束方法。</summary>
      <returns>建立的 <see cref="T:System.Threading.Tasks.Task`1" />，表示非同步作業。</returns>
      <param name="beginMethod">開始非同步作業的委派。</param>
      <param name="endMethod">結束非同步作業的委派。</param>
      <param name="arg1">傳遞至 <paramref name="beginMethod" /> 委派的第一個引數。</param>
      <param name="arg2">傳遞至 <paramref name="beginMethod" /> 委派的第二個引數。</param>
      <param name="arg3">傳遞至 <paramref name="beginMethod" /> 委派的第三個引數。</param>
      <param name="state">物件，包含 <paramref name="beginMethod" /> 委派要使用的資料。</param>
      <param name="creationOptions">TaskCreationOptions 值，控制建立之 <see cref="T:System.Threading.Tasks.Task`1" /> 的行為。</param>
      <typeparam name="TArg1">傳遞至 <paramref name="beginMethod" /> 委派的第二個引數類型。</typeparam>
      <typeparam name="TArg2">傳遞至 <paramref name="beginMethod" /> 委派的第三個引數類型。</typeparam>
      <typeparam name="TArg3">傳遞至 <paramref name="beginMethod" /> 委派的第一個引數類型。</typeparam>
      <typeparam name="TResult">透過 <see cref="T:System.Threading.Tasks.Task`1" /> 取得的結果類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 引數為 Null 時，所擲回的例外狀況。-或-<paramref name="endMethod" /> 引數為 Null 時，所擲回的例外狀況。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 引數指定不正確的 TaskCreationOptions 值時，所擲回的例外狀況。<paramref name="creationOptions" /> 引數指定不正確的 TaskCreationOptions 值時，所擲回的例外狀況。如需詳細資訊，請參閱 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> 的＜備註＞</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync(System.IAsyncResult,System.Action{System.IAsyncResult})">
      <summary>建立 <see cref="T:System.Threading.Tasks.Task" />，這個項目會在指定的 <see cref="T:System.IAsyncResult" /> 完成時執行結束方法動作。</summary>
      <returns>表示非同步作業的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="asyncResult">IAsyncResult，完成時會觸發 <paramref name="endMethod" /> 處理作業。</param>
      <param name="endMethod">動作委派，會處理已完成的 <paramref name="asyncResult" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> 引數為 Null 時，所擲回的例外狀況。-或-<paramref name="endMethod" /> 引數為 Null 時，所擲回的例外狀況。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync(System.IAsyncResult,System.Action{System.IAsyncResult},System.Threading.Tasks.TaskCreationOptions)">
      <summary>建立 <see cref="T:System.Threading.Tasks.Task" />，這個項目會在指定的 <see cref="T:System.IAsyncResult" /> 完成時執行結束方法動作。</summary>
      <returns>表示非同步作業的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="asyncResult">IAsyncResult，完成時會觸發 <paramref name="endMethod" /> 處理作業。</param>
      <param name="endMethod">動作委派，會處理已完成的 <paramref name="asyncResult" />。</param>
      <param name="creationOptions">TaskCreationOptions 值，控制建立之 <see cref="T:System.Threading.Tasks.Task" /> 的行為。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> 引數為 Null 時，所擲回的例外狀況。-或-<paramref name="endMethod" /> 引數為 Null 時，所擲回的例外狀況。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 引數指定不正確的 TaskCreationOptions 值時，所擲回的例外狀況。如需詳細資訊，請參閱 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> 的＜備註＞</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync(System.IAsyncResult,System.Action{System.IAsyncResult},System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>建立 <see cref="T:System.Threading.Tasks.Task" />，這個項目會在指定的 <see cref="T:System.IAsyncResult" /> 完成時執行結束方法動作。</summary>
      <returns>建立的 <see cref="T:System.Threading.Tasks.Task" />，表示非同步作業。</returns>
      <param name="asyncResult">IAsyncResult，完成時會觸發 <paramref name="endMethod" /> 處理作業。</param>
      <param name="endMethod">動作委派，會處理已完成的 <paramref name="asyncResult" />。</param>
      <param name="creationOptions">TaskCreationOptions 值，控制建立之 <see cref="T:System.Threading.Tasks.Task" /> 的行為。</param>
      <param name="scheduler">
        <see cref="T:System.Threading.Tasks.TaskScheduler" />，用來排程可執行結束方法的工作。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> 引數為 Null 時，所擲回的例外狀況。-或-<paramref name="endMethod" /> 引數為 Null 時，所擲回的例外狀況。-或-<paramref name="scheduler" /> 引數為 Null 時，所擲回的例外狀況。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 引數指定不正確的 TaskCreationOptions 值時，所擲回的例外狀況。<paramref name="creationOptions" /> 引數指定不正確的 TaskCreationOptions 值時，所擲回的例外狀況。如需詳細資訊，請參閱 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> 的＜備註＞</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.IAsyncResult,System.Func{System.IAsyncResult,``0})">
      <summary>建立 <see cref="T:System.Threading.Tasks.Task`1" />，當指定的 <see cref="T:System.IAsyncResult" /> 完成時會執行結束方法函式。</summary>
      <returns>表示非同步作業的 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="asyncResult">IAsyncResult，完成時會觸發 <paramref name="endMethod" /> 處理作業。</param>
      <param name="endMethod">函式委派，會處理已完成的 <paramref name="asyncResult" />。</param>
      <typeparam name="TResult">透過 <see cref="T:System.Threading.Tasks.Task`1" /> 取得的結果類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> 引數為 Null 時，所擲回的例外狀況。-或-<paramref name="endMethod" /> 引數為 Null 時，所擲回的例外狀況。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.IAsyncResult,System.Func{System.IAsyncResult,``0},System.Threading.Tasks.TaskCreationOptions)">
      <summary>建立 <see cref="T:System.Threading.Tasks.Task`1" />，當指定的 <see cref="T:System.IAsyncResult" /> 完成時會執行結束方法函式。</summary>
      <returns>表示非同步作業的 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="asyncResult">IAsyncResult，完成時會觸發 <paramref name="endMethod" /> 處理作業。</param>
      <param name="endMethod">函式委派，會處理已完成的 <paramref name="asyncResult" />。</param>
      <param name="creationOptions">TaskCreationOptions 值，控制建立之 <see cref="T:System.Threading.Tasks.Task`1" /> 的行為。</param>
      <typeparam name="TResult">透過 <see cref="T:System.Threading.Tasks.Task`1" /> 取得的結果類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> 引數為 Null 時，所擲回的例外狀況。-或-<paramref name="endMethod" /> 引數為 Null 時，所擲回的例外狀況。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 引數指定不正確的 TaskCreationOptions 值時，所擲回的例外狀況。<paramref name="creationOptions" /> 引數指定不正確的 TaskCreationOptions 值時，所擲回的例外狀況。如需詳細資訊，請參閱 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> 的＜備註＞</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.IAsyncResult,System.Func{System.IAsyncResult,``0},System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>建立 <see cref="T:System.Threading.Tasks.Task`1" />，當指定的 <see cref="T:System.IAsyncResult" /> 完成時會執行結束方法函式。</summary>
      <returns>表示非同步作業的 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="asyncResult">IAsyncResult，完成時會觸發 <paramref name="endMethod" /> 處理作業。</param>
      <param name="endMethod">函式委派，會處理已完成的 <paramref name="asyncResult" />。</param>
      <param name="creationOptions">TaskCreationOptions 值，控制建立之 <see cref="T:System.Threading.Tasks.Task`1" /> 的行為。</param>
      <param name="scheduler">
        <see cref="T:System.Threading.Tasks.TaskScheduler" />，用來排程可執行結束方法的工作。</param>
      <typeparam name="TResult">透過 <see cref="T:System.Threading.Tasks.Task`1" /> 取得的結果類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> 引數為 Null 時，所擲回的例外狀況。-或-<paramref name="endMethod" /> 引數為 Null 時，所擲回的例外狀況。-或-<paramref name="scheduler" /> 引數為 Null 時，所擲回的例外狀況。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 引數指定不正確的 TaskCreationOptions 值時，所擲回的例外狀況。<paramref name="creationOptions" /> 引數指定不正確的 TaskCreationOptions 值時，所擲回的例外狀況。如需詳細資訊，請參閱 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> 的＜備註＞</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory.Scheduler">
      <summary>取得這個工作 Factory 的預設工作排程器。</summary>
      <returns>這個工作 Factory 的預設工作排程器。</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action)">
      <summary>建立並啟動工作。</summary>
      <returns>已開始的工作。</returns>
      <param name="action">要以非同步方式執行的動作委派。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="action" /> 引數為 null。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action,System.Threading.CancellationToken)">
      <summary>建立並啟動 <see cref="T:System.Threading.Tasks.Task" />。</summary>
      <returns>啟動的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="action">要以非同步方式執行的動作委派。</param>
      <param name="cancellationToken">要指派給新工作的 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />。</param>
      <exception cref="T:System.ObjectDisposedException">已經處置所提供的 <see cref="T:System.Threading.CancellationToken" />。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="action" /> 引數為 Null 時，所擲回的例外狀況。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>建立並啟動 <see cref="T:System.Threading.Tasks.Task" />。</summary>
      <returns>啟動的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="action">要以非同步方式執行的動作委派。</param>
      <param name="cancellationToken">
        <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />，將指派給新的 <see cref="T:System.Threading.Tasks.Task" /></param>
      <param name="creationOptions">TaskCreationOptions 值，可控制下列已建立之項目的行為：<see cref="T:System.Threading.Tasks.Task" /></param>
      <param name="scheduler">
        <see cref="T:System.Threading.Tasks.TaskScheduler" />，用來排定建立的接續 <see cref="T:System.Threading.Tasks.Task" />。</param>
      <exception cref="T:System.ObjectDisposedException">已經處置所提供的 <see cref="T:System.Threading.CancellationToken" />。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="action" /> 引數為 Null 時，所擲回的例外狀況。-或-<paramref name="scheduler" /> 引數為 Null 時，所擲回的例外狀況。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 引數指定不正確的 TaskCreationOptions 值時，所擲回的例外狀況。<paramref name="creationOptions" /> 引數指定不正確的 TaskCreationOptions 值時，所擲回的例外狀況。如需詳細資訊，請參閱 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> 的＜備註＞</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action,System.Threading.Tasks.TaskCreationOptions)">
      <summary>建立並啟動 <see cref="T:System.Threading.Tasks.Task" />。</summary>
      <returns>啟動的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="action">要以非同步方式執行的動作委派。</param>
      <param name="creationOptions">TaskCreationOptions 值，可控制下列已建立之項目的行為：<see cref="T:System.Threading.Tasks.Task" /></param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="action" /> 引數為 Null 時，所擲回的例外狀況。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 引數指定不正確的 TaskCreationOptions 值時，所擲回的例外狀況。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action{System.Object},System.Object)">
      <summary>建立並啟動 <see cref="T:System.Threading.Tasks.Task" />。</summary>
      <returns>啟動的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="action">要以非同步方式執行的動作委派。</param>
      <param name="state">物件，包含 <paramref name="action" /> 委派要使用的資料。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="action" /> 引數為 null。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action{System.Object},System.Object,System.Threading.CancellationToken)">
      <summary>建立並啟動 <see cref="T:System.Threading.Tasks.Task" />。</summary>
      <returns>啟動的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="action">要以非同步方式執行的動作委派。</param>
      <param name="state">物件，包含 <paramref name="action" /> 委派要使用的資料。</param>
      <param name="cancellationToken">
        <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />，將指派給新的 <see cref="T:System.Threading.Tasks.Task" /></param>
      <exception cref="T:System.ObjectDisposedException">已經處置所提供的 <see cref="T:System.Threading.CancellationToken" />。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="action" /> 引數為 Null 時，所擲回的例外狀況。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action{System.Object},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>建立並啟動 <see cref="T:System.Threading.Tasks.Task" />。</summary>
      <returns>啟動的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="action">要以非同步方式執行的動作委派。</param>
      <param name="state">物件，包含 <paramref name="action" /> 委派要使用的資料。</param>
      <param name="cancellationToken">要指派給新工作的 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />。</param>
      <param name="creationOptions">TaskCreationOptions 值，可控制下列已建立之項目的行為：<see cref="T:System.Threading.Tasks.Task" /></param>
      <param name="scheduler">
        <see cref="T:System.Threading.Tasks.TaskScheduler" />，用來排定建立的接續 <see cref="T:System.Threading.Tasks.Task" />。</param>
      <exception cref="T:System.ObjectDisposedException">已經處置所提供的 <see cref="T:System.Threading.CancellationToken" />。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="action" /> 引數為 Null 時，所擲回的例外狀況。-或-<paramref name="scheduler" /> 引數為 Null 時，所擲回的例外狀況。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 引數指定不正確的 TaskCreationOptions 值時，所擲回的例外狀況。<paramref name="creationOptions" /> 引數指定不正確的 TaskCreationOptions 值時，所擲回的例外狀況。如需詳細資訊，請參閱 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> 的＜備註＞</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action{System.Object},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>建立並啟動 <see cref="T:System.Threading.Tasks.Task" />。</summary>
      <returns>啟動的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="action">要以非同步方式執行的動作委派。</param>
      <param name="state">物件，包含 <paramref name="action" /> 委派要使用的資料。</param>
      <param name="creationOptions">TaskCreationOptions 值，可控制下列已建立之項目的行為：<see cref="T:System.Threading.Tasks.Task" /></param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="action" /> 引數為 Null 時，所擲回的例外狀況。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 引數指定不正確的 TaskCreationOptions 值時，所擲回的例外狀況。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{``0})">
      <summary>建立並啟動 <see cref="T:System.Threading.Tasks.Task`1" />。</summary>
      <returns>啟動的 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="function">函式委派，會傳回將可透過 <see cref="T:System.Threading.Tasks.Task`1" /> 使用的未來結果。</param>
      <typeparam name="TResult">透過 <see cref="T:System.Threading.Tasks.Task`1" /> 取得的結果類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 引數為 null。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{``0},System.Threading.CancellationToken)">
      <summary>建立並啟動 <see cref="T:System.Threading.Tasks.Task`1" />。</summary>
      <returns>啟動的 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="function">函式委派，會傳回將可透過 <see cref="T:System.Threading.Tasks.Task`1" /> 使用的未來結果。</param>
      <param name="cancellationToken">
        <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />，將指派給新的 <see cref="T:System.Threading.Tasks.Task" /></param>
      <typeparam name="TResult">透過 <see cref="T:System.Threading.Tasks.Task`1" /> 取得的結果類型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">已經處置所提供的 <see cref="T:System.Threading.CancellationToken" />。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 引數為 Null 時，所擲回的例外狀況。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{``0},System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>建立並啟動 <see cref="T:System.Threading.Tasks.Task`1" />。</summary>
      <returns>啟動的 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="function">函式委派，會傳回將可透過 <see cref="T:System.Threading.Tasks.Task`1" /> 使用的未來結果。</param>
      <param name="cancellationToken">要指派給新工作的 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />。</param>
      <param name="creationOptions">TaskCreationOptions 值，可控制已建立之 <see cref="T:System.Threading.Tasks.Task`1" /> 的行為。</param>
      <param name="scheduler">
        <see cref="T:System.Threading.Tasks.TaskScheduler" />，用來排定建立的接續 <see cref="T:System.Threading.Tasks.Task`1" />。</param>
      <typeparam name="TResult">透過 <see cref="T:System.Threading.Tasks.Task`1" /> 取得的結果類型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">已經處置所提供的 <see cref="T:System.Threading.CancellationToken" />。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 引數為 Null 時，所擲回的例外狀況。-或-<paramref name="scheduler" /> 引數為 Null 時，所擲回的例外狀況。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 引數指定不正確的 TaskCreationOptions 值時，所擲回的例外狀況。<paramref name="creationOptions" /> 引數指定不正確的 TaskCreationOptions 值時，所擲回的例外狀況。如需詳細資訊，請參閱 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> 的＜備註＞</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{``0},System.Threading.Tasks.TaskCreationOptions)">
      <summary>建立並啟動 <see cref="T:System.Threading.Tasks.Task`1" />。</summary>
      <returns>啟動的 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="function">函式委派，會傳回將可透過 <see cref="T:System.Threading.Tasks.Task`1" /> 使用的未來結果。</param>
      <param name="creationOptions">TaskCreationOptions 值，可控制已建立之 <see cref="T:System.Threading.Tasks.Task`1" /> 的行為。</param>
      <typeparam name="TResult">透過 <see cref="T:System.Threading.Tasks.Task`1" /> 取得的結果類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 引數為 Null 時，所擲回的例外狀況。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 引數指定不正確的 TaskCreationOptions 值時，所擲回的例外狀況。<paramref name="creationOptions" /> 引數指定不正確的 TaskCreationOptions 值時，所擲回的例外狀況。如需詳細資訊，請參閱 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> 的＜備註＞</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{System.Object,``0},System.Object)">
      <summary>建立並啟動 <see cref="T:System.Threading.Tasks.Task`1" />。</summary>
      <returns>啟動的 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="function">函式委派，會傳回將可透過 <see cref="T:System.Threading.Tasks.Task`1" /> 使用的未來結果。</param>
      <param name="state">物件，包含 <paramref name="function" /> 委派要使用的資料。</param>
      <typeparam name="TResult">透過 <see cref="T:System.Threading.Tasks.Task`1" /> 取得的結果類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 引數為 Null 時，所擲回的例外狀況。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{System.Object,``0},System.Object,System.Threading.CancellationToken)">
      <summary>建立並啟動 <see cref="T:System.Threading.Tasks.Task`1" />。</summary>
      <returns>啟動的 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="function">函式委派，會傳回將可透過 <see cref="T:System.Threading.Tasks.Task`1" /> 使用的未來結果。</param>
      <param name="state">物件，包含 <paramref name="function" /> 委派要使用的資料。</param>
      <param name="cancellationToken">
        <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />，將指派給新的 <see cref="T:System.Threading.Tasks.Task" /></param>
      <typeparam name="TResult">透過 <see cref="T:System.Threading.Tasks.Task`1" /> 取得的結果類型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">已經處置所提供的 <see cref="T:System.Threading.CancellationToken" />。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 引數為 Null 時，所擲回的例外狀況。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{System.Object,``0},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>建立並啟動 <see cref="T:System.Threading.Tasks.Task`1" />。</summary>
      <returns>啟動的 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="function">函式委派，會傳回將可透過 <see cref="T:System.Threading.Tasks.Task`1" /> 使用的未來結果。</param>
      <param name="state">物件，包含 <paramref name="function" /> 委派要使用的資料。</param>
      <param name="cancellationToken">要指派給新工作的 <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" />。</param>
      <param name="creationOptions">TaskCreationOptions 值，可控制已建立之 <see cref="T:System.Threading.Tasks.Task`1" /> 的行為。</param>
      <param name="scheduler">
        <see cref="T:System.Threading.Tasks.TaskScheduler" />，用來排定建立的接續 <see cref="T:System.Threading.Tasks.Task`1" />。</param>
      <typeparam name="TResult">透過 <see cref="T:System.Threading.Tasks.Task`1" /> 取得的結果類型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">已經處置所提供的 <see cref="T:System.Threading.CancellationToken" />。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 引數為 Null 時，所擲回的例外狀況。-或-<paramref name="scheduler" /> 引數為 Null 時，所擲回的例外狀況。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 引數指定不正確的 TaskCreationOptions 值時，所擲回的例外狀況。<paramref name="creationOptions" /> 引數指定不正確的 TaskCreationOptions 值時，所擲回的例外狀況。如需詳細資訊，請參閱 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> 的＜備註＞</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{System.Object,``0},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>建立並啟動 <see cref="T:System.Threading.Tasks.Task`1" />。</summary>
      <returns>啟動的 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="function">函式委派，會傳回將可透過 <see cref="T:System.Threading.Tasks.Task`1" /> 使用的未來結果。</param>
      <param name="state">物件，包含 <paramref name="function" /> 委派要使用的資料。</param>
      <param name="creationOptions">TaskCreationOptions 值，可控制已建立之 <see cref="T:System.Threading.Tasks.Task`1" /> 的行為。</param>
      <typeparam name="TResult">透過 <see cref="T:System.Threading.Tasks.Task`1" /> 取得的結果類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 引數為 Null 時，所擲回的例外狀況。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 引數指定不正確的 TaskCreationOptions 值時，所擲回的例外狀況。<paramref name="creationOptions" /> 引數指定不正確的 TaskCreationOptions 值時，所擲回的例外狀況。如需詳細資訊，請參閱 <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /> 的＜備註＞</exception>
    </member>
    <member name="T:System.Threading.Tasks.TaskFactory`1">
      <summary>提供建立和排程 <see cref="T:System.Threading.Tasks.Task`1" /> 物件的支援。</summary>
      <typeparam name="TResult">此類別的方法所建立之 <see cref="T:System.Threading.Tasks.Task`1" /> 物件的傳回值。</typeparam>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.#ctor">
      <summary>使用預設組態，初始化 <see cref="T:System.Threading.Tasks.TaskFactory`1" /> 執行個體。</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.#ctor(System.Threading.CancellationToken)">
      <summary>使用預設組態，初始化 <see cref="T:System.Threading.Tasks.TaskFactory`1" /> 執行個體。</summary>
      <param name="cancellationToken">除非呼叫處理站方法時明確指定另一個取消語彙基元，否則為將指派給這個 <see cref="T:System.Threading.Tasks.TaskFactory" /> 所建立之工作的預設取消語彙基元。</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.#ctor(System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>使用指定的組態，初始化 <see cref="T:System.Threading.Tasks.TaskFactory`1" /> 執行個體。</summary>
      <param name="cancellationToken">除非呼叫處理站方法時明確指定另一個取消語彙基元，否則為將指派給這個 <see cref="T:System.Threading.Tasks.TaskFactory" /> 所建立之工作的預設取消語彙基元。</param>
      <param name="creationOptions">使用這個 <see cref="T:System.Threading.Tasks.TaskFactory`1" /> 建立工作時要採用的預設選項。</param>
      <param name="continuationOptions">使用這個 <see cref="T:System.Threading.Tasks.TaskFactory`1" /> 建立接續工作時要採用的預設選項。</param>
      <param name="scheduler">預設排程器，用來排定任何使用這個 <see cref="T:System.Threading.Tasks.TaskFactory`1" /> 所建立的工作。null 值表示應該使用 <see cref="P:System.Threading.Tasks.TaskScheduler.Current" />。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 或 <paramref name="continuationOptions" /> 指定了無效值。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.#ctor(System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>使用指定的組態，初始化 <see cref="T:System.Threading.Tasks.TaskFactory`1" /> 執行個體。</summary>
      <param name="creationOptions">使用這個 <see cref="T:System.Threading.Tasks.TaskFactory`1" /> 建立工作時要採用的預設選項。</param>
      <param name="continuationOptions">使用這個 <see cref="T:System.Threading.Tasks.TaskFactory`1" /> 建立接續工作時要採用的預設選項。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 或 <paramref name="continuationOptions" /> 指定了無效值。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.#ctor(System.Threading.Tasks.TaskScheduler)">
      <summary>使用指定的組態，初始化 <see cref="T:System.Threading.Tasks.TaskFactory`1" /> 執行個體。</summary>
      <param name="scheduler">排程器，用來排定任何使用這個 <see cref="T:System.Threading.Tasks.TaskFactory`1" /> 所建立的工作。null 值表示應該使用目前的 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</param>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory`1.CancellationToken">
      <summary>取得這個工作 Factory 的預設取消語彙基元。</summary>
      <returns>這個工作 Factory 的預設取消語彙基元。</returns>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory`1.ContinuationOptions">
      <summary>取得這個工作 Factory 的 <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> 列舉值。</summary>
      <returns>其中一個列舉值，這個值會指定這個工作 Factory 的預設接續選項。</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],`0})">
      <summary>建立會在提供的一組工作完成時啟動的接續工作。</summary>
      <returns>新的接續工作。</returns>
      <param name="tasks">要接續之工作的所在陣列。</param>
      <param name="continuationFunction">當 <paramref name="tasks" /> 陣列中的所有工作都已經完成時，要以非同步方式執行的函式委派。</param>
      <exception cref="T:System.ObjectDisposedException">已處置 <paramref name="tasks" /> 陣列的其中一個項目。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 陣列為 null。-或-<paramref name="continuationFunction" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 陣列包含 null 值或為空白。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],`0},System.Threading.CancellationToken)">
      <summary>建立會在提供的一組工作完成時啟動的接續工作。</summary>
      <returns>新的接續工作。</returns>
      <param name="tasks">要接續之工作的所在陣列。</param>
      <param name="continuationFunction">當 <paramref name="tasks" /> 陣列中的所有工作都已經完成時，要以非同步方式執行的函式委派。</param>
      <param name="cancellationToken">要指派給新接續工作的取消語彙基元。</param>
      <exception cref="T:System.ObjectDisposedException">已處置 <paramref name="tasks" /> 陣列的其中一個項目。-或-<see cref="T:System.Threading.CancellationTokenSource" /> 创建<paramref name=" cancellationToken" /> 已释放。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 陣列為 null。-或-<paramref name="continuationFunction" /> 為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 陣列包含 null 值或為空白。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>建立會在提供的一組工作完成時啟動的接續工作。</summary>
      <returns>新的接續工作。</returns>
      <param name="tasks">要接續之工作的所在陣列。</param>
      <param name="continuationFunction">當 <paramref name="tasks" /> 陣列中的所有工作都已經完成時，要以非同步方式執行的函式委派。</param>
      <param name="cancellationToken">要指派給新接續工作的取消語彙基元。</param>
      <param name="continuationOptions">其中一個列舉值，這些值會控制已建立之接續工作的行為。NotOn* 或 OnlyOn* 值無效。</param>
      <param name="scheduler">用來排定所建立之接續工作的排程器。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 陣列為 null。-或-<paramref name="continuationFunction" /> 引數為 null。-或-<paramref name="scheduler" /> 引數為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 陣列包含 null 值或為空白。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 指定了無效的值。</exception>
      <exception cref="T:System.ObjectDisposedException">已處置 <paramref name="tasks" /> 陣列的其中一個項目。-或-<see cref="T:System.Threading.CancellationTokenSource" /> 创建<paramref name=" cancellationToken" /> 已释放。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],`0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>建立會在提供的一組工作完成時啟動的接續工作。</summary>
      <returns>新的接續工作。</returns>
      <param name="tasks">要接續之工作的所在陣列。</param>
      <param name="continuationFunction">當 <paramref name="tasks" /> 陣列中的所有工作都已經完成時，要以非同步方式執行的函式委派。</param>
      <param name="continuationOptions">其中一個列舉值，這些值會控制已建立之接續工作的行為。NotOn* 或 OnlyOn* 值無效。</param>
      <exception cref="T:System.ObjectDisposedException">已處置 <paramref name="tasks" /> 陣列的其中一個項目。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 陣列為 null。-或-<paramref name="continuationFunction" /> 引數為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 引數指定了無效的值。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 陣列包含 null 值或為空白。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],`0})">
      <summary>建立會在提供的一組工作完成時啟動的接續工作。</summary>
      <returns>新的接續工作。</returns>
      <param name="tasks">要接續之工作的所在陣列。</param>
      <param name="continuationFunction">當 <paramref name="tasks" /> 陣列中的所有工作都已經完成時，要以非同步方式執行的函式委派。</param>
      <typeparam name="TAntecedentResult">前項 <paramref name="tasks" /> 的結果類型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">已處置 <paramref name="tasks" /> 陣列的其中一個項目。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 陣列為 null。-或-<paramref name="continuationFunction" /> 引數為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 陣列包含 null 值或為空白。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],`0},System.Threading.CancellationToken)">
      <summary>建立會在提供的一組工作完成時啟動的接續工作。</summary>
      <returns>新的接續工作。</returns>
      <param name="tasks">要接續之工作的所在陣列。</param>
      <param name="continuationFunction">當 <paramref name="tasks" /> 陣列中的所有工作都已經完成時，要以非同步方式執行的函式委派。</param>
      <param name="cancellationToken">要指派給新接續工作的取消語彙基元。</param>
      <typeparam name="TAntecedentResult">前項 <paramref name="tasks" /> 的結果類型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">已處置 <paramref name="tasks" /> 陣列的其中一個項目。-或-<see cref="T:System.Threading.CancellationTokenSource" /> 创建<paramref name=" cancellationToken" /> 已释放。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 陣列為 null。-或-<paramref name="continuationFunction" /> 引數為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 陣列包含 null 值或為空白。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>建立會在提供的一組工作完成時啟動的接續工作。</summary>
      <returns>新的接續工作。</returns>
      <param name="tasks">要接續之工作的所在陣列。</param>
      <param name="continuationFunction">當 <paramref name="tasks" /> 陣列中的所有工作都已經完成時，要以非同步方式執行的函式委派。</param>
      <param name="cancellationToken">要指派給新接續工作的取消語彙基元。</param>
      <param name="continuationOptions">其中一個列舉值，這些值會控制已建立之接續工作的行為。NotOn* 或 OnlyOn* 值無效。</param>
      <param name="scheduler">用來排定所建立之接續工作的排程器。</param>
      <typeparam name="TAntecedentResult">前項 <paramref name="tasks" /> 的結果類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 陣列為 null。-或-<paramref name="continuationFunction" /> 引數為 null。-或-<paramref name="scheduler" /> 引數為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 陣列包含 null 值或為空白。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 引數指定了無效的值。</exception>
      <exception cref="T:System.ObjectDisposedException">已處置 <paramref name="tasks" /> 陣列的其中一個項目。-或-<see cref="T:System.Threading.CancellationTokenSource" /> 创建<paramref name=" cancellationToken" /> 已释放。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],`0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>建立會在提供的一組工作完成時啟動的接續工作。</summary>
      <returns>新的接續工作。</returns>
      <param name="tasks">要接續之工作的所在陣列。</param>
      <param name="continuationFunction">當 <paramref name="tasks" /> 陣列中的所有工作都已經完成時，要以非同步方式執行的函式委派。</param>
      <param name="continuationOptions">其中一個列舉值，這些值會控制已建立之接續工作的行為。NotOn* 或 OnlyOn* 值無效。</param>
      <typeparam name="TAntecedentResult">前項 <paramref name="tasks" /> 的結果類型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">已處置 <paramref name="tasks" /> 陣列的其中一個項目。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 陣列為 null。-或-<paramref name="continuationFunction" /> 引數為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 引數指定了無效的值。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 陣列包含 null 值或為空白。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,`0})">
      <summary>建立會在提供的任何一項工作完成時啟動的接續工作。</summary>
      <returns>新的接續工作。</returns>
      <param name="tasks">工作的陣列，當其中一個工作完成時，會接續另一個工作。</param>
      <param name="continuationFunction">當 <paramref name="tasks" /> 陣列中的任一個工作已經完成時，要以非同步方式執行的函式委派。</param>
      <exception cref="T:System.ObjectDisposedException">已處置 <paramref name="tasks" /> 陣列的其中一個項目。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 陣列為 null。-或-<paramref name="continuationFunction" /> 引數為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 陣列包含 null 值或為空白。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,`0},System.Threading.CancellationToken)">
      <summary>建立會在提供的任何一項工作完成時啟動的接續工作。</summary>
      <returns>新的接續工作。</returns>
      <param name="tasks">工作的陣列，當其中一個工作完成時，會接續另一個工作。</param>
      <param name="continuationFunction">當 <paramref name="tasks" /> 陣列中的任一個工作已經完成時，要以非同步方式執行的函式委派。</param>
      <param name="cancellationToken">要指派給新接續工作的取消語彙基元。</param>
      <exception cref="T:System.ObjectDisposedException">已處置 <paramref name="tasks" /> 陣列的其中一個項目。-或-<see cref="T:System.Threading.CancellationTokenSource" /> 创建<paramref name=" cancellationToken" /> 已释放。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 陣列為 null。-或-<paramref name="continuationFunction" /> 引數為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 陣列包含 null 值。-或-<paramref name="tasks" /> 数组为空。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>建立會在提供的任何一項工作完成時啟動的接續工作。</summary>
      <returns>新的接續工作。</returns>
      <param name="tasks">工作的陣列，當其中一個工作完成時，會接續另一個工作。</param>
      <param name="continuationFunction">當 <paramref name="tasks" /> 陣列中的任一個工作已經完成時，要以非同步方式執行的函式委派。</param>
      <param name="cancellationToken">要指派給新接續工作的取消語彙基元。</param>
      <param name="continuationOptions">其中一個列舉值，這些值會控制已建立之接續工作的行為。NotOn* 或 OnlyOn* 值無效。</param>
      <param name="scheduler">用來排定所建立之接續工作的工作排程器。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 陣列為 null。-或-<paramref name="continuationFunction" /> 引數為 null。-或-<paramref name="scheduler" /> 引數為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 陣列包含 null 值。-或-<paramref name="tasks" /> 数组为空。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 引數指定了無效的 <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> 值。</exception>
      <exception cref="T:System.ObjectDisposedException">已處置 <paramref name="tasks" /> 陣列的其中一個項目。-或-<see cref="T:System.Threading.CancellationTokenSource" /> 创建<paramref name=" cancellationToken" /> 已释放。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,`0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>建立會在提供的任何一項工作完成時啟動的接續工作。</summary>
      <returns>新的接續工作。</returns>
      <param name="tasks">工作的陣列，當其中一個工作完成時，會接續另一個工作。</param>
      <param name="continuationFunction">當 <paramref name="tasks" /> 陣列中的任一個工作已經完成時，要以非同步方式執行的函式委派。</param>
      <param name="continuationOptions">其中一個列舉值，這些值會控制已建立之接續工作的行為。NotOn* 或 OnlyOn* 值無效。</param>
      <exception cref="T:System.ObjectDisposedException">已處置 <paramref name="tasks" /> 陣列的其中一個項目。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 陣列為 null。-或-<paramref name="continuationFunction" /> 引數為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 引數指定了無效的列舉值。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 陣列包含 null 值。-或-<paramref name="tasks" /> 数组为空。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},`0})">
      <summary>建立會在提供的任何一項工作完成時啟動的接續工作。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="tasks">工作的陣列，當其中一個工作完成時，會接續另一個工作。</param>
      <param name="continuationFunction">當 <paramref name="tasks" /> 陣列中的任一個工作已經完成時，要以非同步方式執行的函式委派。</param>
      <typeparam name="TAntecedentResult">前項 <paramref name="tasks" /> 的結果類型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">已處置 <paramref name="tasks" /> 陣列的其中一個項目。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 陣列為 null。-或-<paramref name="continuationFunction" /> 引數為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 陣列包含 null 值。-或-<paramref name="tasks" /> 数组为空。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},`0},System.Threading.CancellationToken)">
      <summary>建立會在提供的任何一項工作完成時啟動的接續工作。</summary>
      <returns>新的接續工作。</returns>
      <param name="tasks">工作的陣列，當其中一個工作完成時，會接續另一個工作。</param>
      <param name="continuationFunction">當 <paramref name="tasks" /> 陣列中的任一個工作已經完成時，要以非同步方式執行的函式委派。</param>
      <param name="cancellationToken">要指派給新接續工作的取消語彙基元。</param>
      <typeparam name="TAntecedentResult">前項 <paramref name="tasks" /> 的結果類型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">已處置 <paramref name="tasks" /> 陣列的其中一個項目。-或-<see cref="T:System.Threading.CancellationTokenSource" /> 创建<paramref name=" cancellationToken" /> 已释放。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 陣列為 null。-或-<paramref name="continuationFunction" /> 引數為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 陣列包含 null 值。-或-<paramref name="tasks" /> 数组为空。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>建立會在提供的任何一項工作完成時啟動的接續工作。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="tasks">工作的陣列，當其中一個工作完成時，會接續另一個工作。</param>
      <param name="continuationFunction">當 <paramref name="tasks" /> 陣列中的任一個工作已經完成時，要以非同步方式執行的函式委派。</param>
      <param name="cancellationToken">要指派給新接續工作的取消語彙基元。</param>
      <param name="continuationOptions">其中一個列舉值，這些值會控制已建立之接續工作的行為。NotOn* 或 OnlyOn* 值無效。</param>
      <param name="scheduler">
        <see cref="T:System.Threading.Tasks.TaskScheduler" />，用來排定建立的接續 <see cref="T:System.Threading.Tasks.Task`1" />。</param>
      <typeparam name="TAntecedentResult">前項 <paramref name="tasks" /> 的結果類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 陣列為 null。-或-<paramref name="continuationFunction" /> 引數為 null。-或-<paramref name="scheduler" /> 引數為 null。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 陣列包含 null 值。-或-<paramref name="tasks" /> 数组为空。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 引數指定了無效的 TaskContinuationOptions 值。</exception>
      <exception cref="T:System.ObjectDisposedException">已處置 <paramref name="tasks" /> 陣列的其中一個項目。-或-<see cref="T:System.Threading.CancellationTokenSource" /> 创建<paramref name=" cancellationToken" /> 已释放。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},`0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>建立會在提供的任何一項工作完成時啟動的接續工作。</summary>
      <returns>新的接續 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="tasks">工作的陣列，當其中一個工作完成時，會接續另一個工作。</param>
      <param name="continuationFunction">當 <paramref name="tasks" /> 陣列中的任一個工作已經完成時，要以非同步方式執行的函式委派。</param>
      <param name="continuationOptions">其中一個列舉值，這些值會控制已建立之接續工作的行為。NotOn* 或 OnlyOn* 值無效。</param>
      <typeparam name="TAntecedentResult">前項 <paramref name="tasks" /> 的結果類型。</typeparam>
      <exception cref="T:System.ObjectDisposedException">已處置 <paramref name="tasks" /> 陣列的其中一個項目。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> 陣列為 null。-或-<paramref name="continuationFunction" /> 引數為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> 引數指定了無效的列舉值。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tasks" /> 陣列包含 null 值。-或-<paramref name="tasks" /> 数组为空。</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory`1.CreationOptions">
      <summary>取得這個工作 Factory 的 <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> 列舉值。</summary>
      <returns>其中一個列舉值，這個值會指定這個工作 Factory 的預設建立選項。</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},System.Object)">
      <summary>建立工作，這個項目代表一組符合「非同步程式設計模型」模式的開始和結束方法。</summary>
      <returns>建立的工作，代表非同步作業。</returns>
      <param name="beginMethod">開始非同步作業的委派。</param>
      <param name="endMethod">結束非同步作業的委派。</param>
      <param name="state">物件，包含 <paramref name="beginMethod" /> 委派要使用的資料。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 引數為 null。-或-<paramref name="endMethod" /> 引數為 null。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>建立工作，這個項目代表一組符合「非同步程式設計模型」模式的開始和結束方法。</summary>
      <returns>建立的 <see cref="T:System.Threading.Tasks.Task`1" />，表示非同步作業。</returns>
      <param name="beginMethod">開始非同步作業的委派。</param>
      <param name="endMethod">結束非同步作業的委派。</param>
      <param name="state">物件，包含 <paramref name="beginMethod" /> 委派要使用的資料。</param>
      <param name="creationOptions">其中一個列舉值，這些值會控制已建立之工作的行為。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 引數為 null。-或-<paramref name="endMethod" /> 引數為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 引數指定了無效的值。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``1(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,System.Object)">
      <summary>建立工作，這個項目代表一組符合「非同步程式設計模型」模式的開始和結束方法。</summary>
      <returns>建立的工作，代表非同步作業。</returns>
      <param name="beginMethod">開始非同步作業的委派。</param>
      <param name="endMethod">結束非同步作業的委派。</param>
      <param name="arg1">傳遞至 <paramref name="beginMethod" /> 委派的第一個引數。</param>
      <param name="state">物件，包含 <paramref name="beginMethod" /> 委派要使用的資料。</param>
      <typeparam name="TArg1">傳遞至 <paramref name="beginMethod" /> 委派的第一個引數類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 引數為 null。-或-<paramref name="endMethod" /> 引數為 null。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``1(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>建立工作，這個項目代表一組符合「非同步程式設計模型」模式的開始和結束方法。</summary>
      <returns>建立的工作，代表非同步作業。</returns>
      <param name="beginMethod">開始非同步作業的委派。</param>
      <param name="endMethod">結束非同步作業的委派。</param>
      <param name="arg1">傳遞至 <paramref name="beginMethod" /> 委派的第一個引數。</param>
      <param name="state">物件，包含 <paramref name="beginMethod" /> 委派要使用的資料。</param>
      <param name="creationOptions">其中一個列舉值，這些值會控制已建立之工作的行為。</param>
      <typeparam name="TArg1">傳遞至 <paramref name="beginMethod" /> 委派的第一個引數類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 引數為 null。-或-<paramref name="endMethod" /> 引數為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 參數指定了無效的值。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``2(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,``1,System.Object)">
      <summary>建立工作，這個項目代表一組符合「非同步程式設計模型」模式的開始和結束方法。</summary>
      <returns>建立的工作，代表非同步作業。</returns>
      <param name="beginMethod">開始非同步作業的委派。</param>
      <param name="endMethod">結束非同步作業的委派。</param>
      <param name="arg1">傳遞至 <paramref name="beginMethod" /> 委派的第一個引數。</param>
      <param name="arg2">傳遞至 <paramref name="beginMethod" /> 委派的第二個引數。</param>
      <param name="state">物件，包含 <paramref name="beginMethod" /> 委派要使用的資料。</param>
      <typeparam name="TArg1">傳遞至 <paramref name="beginMethod" /> 委派的第二個引數類型。</typeparam>
      <typeparam name="TArg2">傳遞至 <paramref name="beginMethod" /> 委派的第一個引數類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 引數為 null。-或-<paramref name="endMethod" /> 引數為 null。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``2(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,``1,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>建立工作，這個項目代表一組符合「非同步程式設計模型」模式的開始和結束方法。</summary>
      <returns>建立的工作，代表非同步作業。</returns>
      <param name="beginMethod">開始非同步作業的委派。</param>
      <param name="endMethod">結束非同步作業的委派。</param>
      <param name="arg1">傳遞至 <paramref name="beginMethod" /> 委派的第一個引數。</param>
      <param name="arg2">傳遞至 <paramref name="beginMethod" /> 委派的第二個引數。</param>
      <param name="state">物件，包含 <paramref name="beginMethod" /> 委派要使用的資料。</param>
      <param name="creationOptions">控制所建立 <see cref="T:System.Threading.Tasks.Task`1" /> 之行為的物件。</param>
      <typeparam name="TArg1">傳遞至 <paramref name="beginMethod" /> 委派的第二個引數類型。</typeparam>
      <typeparam name="TArg2">傳遞至 <paramref name="beginMethod" /> 委派的第一個引數類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 引數為 null。-或-<paramref name="endMethod" /> 引數為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 參數指定了無效的值。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``3(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,``1,``2,System.Object)">
      <summary>建立工作，這個項目代表一組符合「非同步程式設計模型」模式的開始和結束方法。</summary>
      <returns>建立的工作，代表非同步作業。</returns>
      <param name="beginMethod">開始非同步作業的委派。</param>
      <param name="endMethod">結束非同步作業的委派。</param>
      <param name="arg1">傳遞至 <paramref name="beginMethod" /> 委派的第一個引數。</param>
      <param name="arg2">傳遞至 <paramref name="beginMethod" /> 委派的第二個引數。</param>
      <param name="arg3">傳遞至 <paramref name="beginMethod" /> 委派的第三個引數。</param>
      <param name="state">物件，包含 <paramref name="beginMethod" /> 委派要使用的資料。</param>
      <typeparam name="TArg1">傳遞至 <paramref name="beginMethod" /> 委派的第二個引數類型。</typeparam>
      <typeparam name="TArg2">傳遞至 <paramref name="beginMethod" /> 委派的第三個引數類型。</typeparam>
      <typeparam name="TArg3">傳遞至 <paramref name="beginMethod" /> 委派的第一個引數類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 引數為 null。-或-<paramref name="endMethod" /> 引數為 null。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``3(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,``1,``2,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>建立工作，這個項目代表一組符合「非同步程式設計模型」模式的開始和結束方法。</summary>
      <returns>建立的工作，代表非同步作業。</returns>
      <param name="beginMethod">開始非同步作業的委派。</param>
      <param name="endMethod">結束非同步作業的委派。</param>
      <param name="arg1">傳遞至 <paramref name="beginMethod" /> 委派的第一個引數。</param>
      <param name="arg2">傳遞至 <paramref name="beginMethod" /> 委派的第二個引數。</param>
      <param name="arg3">傳遞至 <paramref name="beginMethod" /> 委派的第三個引數。</param>
      <param name="state">物件，包含 <paramref name="beginMethod" /> 委派要使用的資料。</param>
      <param name="creationOptions">控制所建立工作之行為的物件。</param>
      <typeparam name="TArg1">傳遞至 <paramref name="beginMethod" /> 委派的第二個引數類型。</typeparam>
      <typeparam name="TArg2">傳遞至 <paramref name="beginMethod" /> 委派的第三個引數類型。</typeparam>
      <typeparam name="TArg3">傳遞至 <paramref name="beginMethod" /> 委派的第一個引數類型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="beginMethod" /> 引數為 null。-或-<paramref name="endMethod" /> 引數為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 參數指定了無效的值。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync(System.IAsyncResult,System.Func{System.IAsyncResult,`0})">
      <summary>建立工作，當指定的 <see cref="T:System.IAsyncResult" /> 完成時會執行結束方法函式。</summary>
      <returns>表示非同步作業的 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="asyncResult">
        <see cref="T:System.IAsyncResult" />，完成時會觸發 <paramref name="endMethod" /> 處理作業。</param>
      <param name="endMethod">函式委派，會處理已完成的 <paramref name="asyncResult" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> 引數為 null。-或-<paramref name="endMethod" /> 引數為 null。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync(System.IAsyncResult,System.Func{System.IAsyncResult,`0},System.Threading.Tasks.TaskCreationOptions)">
      <summary>建立工作，當指定的 <see cref="T:System.IAsyncResult" /> 完成時會執行結束方法函式。</summary>
      <returns>代表非同步作業的工作。</returns>
      <param name="asyncResult">
        <see cref="T:System.IAsyncResult" />，完成時會觸發 <paramref name="endMethod" /> 處理作業。</param>
      <param name="endMethod">函式委派，會處理已完成的 <paramref name="asyncResult" />。</param>
      <param name="creationOptions">其中一個列舉值，這些值會控制已建立之工作的行為。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> 引數為 null。-或-<paramref name="endMethod" /> 引數為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 引數指定了無效的值。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync(System.IAsyncResult,System.Func{System.IAsyncResult,`0},System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>建立工作，當指定的 <see cref="T:System.IAsyncResult" /> 完成時會執行結束方法函式。</summary>
      <returns>建立的工作，代表非同步作業。</returns>
      <param name="asyncResult">
        <see cref="T:System.IAsyncResult" />，完成時會觸發 <paramref name="endMethod" /> 處理作業。</param>
      <param name="endMethod">函式委派，會處理已完成的 <paramref name="asyncResult" />。</param>
      <param name="creationOptions">其中一個列舉值，這些值會控制已建立之工作的行為。</param>
      <param name="scheduler">工作排程器，用來排程可執行結束方法的工作。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> 引數為 null。-或-<paramref name="endMethod" /> 引數為 null。-或-<paramref name="scheduler" /> 引數為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 參數指定了無效的值。</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory`1.Scheduler">
      <summary>取得這個工作 Factory 的工作排程器。</summary>
      <returns>這個工作 Factory 的工作排程器。</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{`0})">
      <summary>建立並啟動工作。</summary>
      <returns>已開始的工作。</returns>
      <param name="function">函式委派，會傳回將可透過工作使用的未來結果。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 引數為 null。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{`0},System.Threading.CancellationToken)">
      <summary>建立並啟動工作。</summary>
      <returns>已開始的工作。</returns>
      <param name="function">函式委派，會傳回將可透過工作使用的未來結果。</param>
      <param name="cancellationToken">要指派給新工作的取消語彙基元。</param>
      <exception cref="T:System.ObjectDisposedException">已處置所建立之 <paramref name="cancellationToken" /> 的取消語彙基元。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 引數為 null。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>建立並啟動工作。</summary>
      <returns>已開始的工作。</returns>
      <param name="function">函式委派，會傳回將可透過工作使用的未來結果。</param>
      <param name="cancellationToken">要指派給新工作的取消語彙基元。</param>
      <param name="creationOptions">其中一個列舉值，這些值會控制已建立之工作的行為。</param>
      <param name="scheduler">用來排定所建立之工作的工作排程器。</param>
      <exception cref="T:System.ObjectDisposedException">已處置所建立之 <paramref name="cancellationToken" /> 的取消語彙基元。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 引數為 null。-或-<paramref name="scheduler" /> 引數為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 參數指定了無效的值。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{`0},System.Threading.Tasks.TaskCreationOptions)">
      <summary>建立並啟動工作。</summary>
      <returns>啟動的 <see cref="T:System.Threading.Tasks.Task`1" />。</returns>
      <param name="function">函式委派，會傳回將可透過工作使用的未來結果。</param>
      <param name="creationOptions">其中一個列舉值，這些值會控制已建立之工作的行為。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 引數為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 參數指定了無效的值。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{System.Object,`0},System.Object)">
      <summary>建立並啟動工作。</summary>
      <returns>已開始的工作。</returns>
      <param name="function">函式委派，會傳回將可透過工作使用的未來結果。</param>
      <param name="state">物件，包含 <paramref name="function" /> 委派所要使用的資料。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 引數為 null。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{System.Object,`0},System.Object,System.Threading.CancellationToken)">
      <summary>建立並啟動工作。</summary>
      <returns>已開始的工作。</returns>
      <param name="function">函式委派，會傳回將可透過工作使用的未來結果。</param>
      <param name="state">物件，包含 <paramref name="function" /> 委派所要使用的資料。</param>
      <param name="cancellationToken">要指派給新工作的取消語彙基元。</param>
      <exception cref="T:System.ObjectDisposedException">已處置所建立之 <paramref name="cancellationToken" /> 的取消語彙基元。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 引數為 null。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{System.Object,`0},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>建立並啟動工作。</summary>
      <returns>已開始的工作。</returns>
      <param name="function">函式委派，會傳回將可透過工作使用的未來結果。</param>
      <param name="state">物件，包含 <paramref name="function" /> 委派所要使用的資料。</param>
      <param name="cancellationToken">要指派給新工作的取消語彙基元。</param>
      <param name="creationOptions">其中一個列舉值，這些值會控制已建立之工作的行為。</param>
      <param name="scheduler">用來排定所建立之工作的工作排程器。</param>
      <exception cref="T:System.ObjectDisposedException">已處置所建立之 <paramref name="cancellationToken" /> 的取消語彙基元。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 引數為 null。-或-<paramref name="scheduler" /> 引數為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 參數指定了無效的值。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{System.Object,`0},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>建立並啟動工作。</summary>
      <returns>已開始的工作。</returns>
      <param name="function">函式委派，會傳回將可透過工作使用的未來結果。</param>
      <param name="state">物件，包含 <paramref name="function" /> 委派所要使用的資料。</param>
      <param name="creationOptions">其中一個列舉值，這些值會控制已建立之工作的行為。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="function" /> 引數為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> 參數指定了無效的值。</exception>
    </member>
    <member name="T:System.Threading.Tasks.TaskScheduler">
      <summary>表示物件，這個物件會處理將工作排入至執行緒上的低階工作。</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.#ctor">
      <summary>初始化 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</summary>
    </member>
    <member name="P:System.Threading.Tasks.TaskScheduler.Current">
      <summary>取得與目前執行之工作相關聯的 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</summary>
      <returns>傳回與目前執行之工作相關聯的 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</returns>
    </member>
    <member name="P:System.Threading.Tasks.TaskScheduler.Default">
      <summary>取得 .NET Framework 提供的預設 <see cref="T:System.Threading.Tasks.TaskScheduler" /> 執行個體。</summary>
      <returns>傳回預設 <see cref="T:System.Threading.Tasks.TaskScheduler" /> 執行個體。</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.FromCurrentSynchronizationContext">
      <summary>建立與目前 <see cref="T:System.Threading.SynchronizationContext" /> 相關聯的 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</summary>
      <returns>與目前 <see cref="T:System.Threading.SynchronizationContext" /> (由 <see cref="P:System.Threading.SynchronizationContext.Current" /> 所決定) 相關聯的 <see cref="T:System.Threading.Tasks.TaskScheduler" />。</returns>
      <exception cref="T:System.InvalidOperationException">目前的 SynchronizationContext 不可當做 TaskScheduler 使用。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.GetScheduledTasks">
      <summary>僅限偵錯工具支援，針對目前已排入至排程器中等候執行的 <see cref="T:System.Threading.Tasks.Task" /> 執行個體，產生可列舉項目。</summary>
      <returns>可列舉項目，允許偵錯工具在目前已排入至這個排程器中的工作之間周遊。</returns>
      <exception cref="T:System.NotSupportedException">這個排程器無法在這個階段產生佇列的工作清單。</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskScheduler.Id">
      <summary>取得這個 <see cref="T:System.Threading.Tasks.TaskScheduler" /> 的唯一 ID。</summary>
      <returns>傳回這個 <see cref="T:System.Threading.Tasks.TaskScheduler" /> 的唯一 ID。</returns>
    </member>
    <member name="P:System.Threading.Tasks.TaskScheduler.MaximumConcurrencyLevel">
      <summary>表示這個 <see cref="T:System.Threading.Tasks.TaskScheduler" /> 可以支援的最大並行層級。</summary>
      <returns>傳回整數，表示最大並行層級。預設排程器會傳回 <see cref="F:System.Int32.MaxValue" />。</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.QueueTask(System.Threading.Tasks.Task)">
      <summary>將 <see cref="T:System.Threading.Tasks.Task" /> 排入至排程器。</summary>
      <param name="task">要排入的 <see cref="T:System.Threading.Tasks.Task" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="task" /> 引數為 null。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.TryDequeue(System.Threading.Tasks.Task)">
      <summary>嘗試清除之前排入至這個排程器的 <see cref="T:System.Threading.Tasks.Task" />。</summary>
      <returns>布林值，表示是否已經成功清除 <paramref name="task" /> 引數。</returns>
      <param name="task">要清除的 <see cref="T:System.Threading.Tasks.Task" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="task" /> 引數為 null。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.TryExecuteTask(System.Threading.Tasks.Task)">
      <summary>嘗試在這個排程器上執行提供的 <see cref="T:System.Threading.Tasks.Task" />。</summary>
      <returns>布林值，如果已成功執行 <paramref name="task" /> 則為 true，否則為 false。執行失敗的常見原因是之前已執行過該工作，或另一個執行緒正在執行該工作。</returns>
      <param name="task">要執行的 <see cref="T:System.Threading.Tasks.Task" /> 物件。</param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="task" /> 與此排程器無關。</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.TryExecuteTaskInline(System.Threading.Tasks.Task,System.Boolean)">
      <summary>判斷是否可以在這個呼叫中同步執行所提供的 <see cref="T:System.Threading.Tasks.Task" />，如果可以，即加以執行。</summary>
      <returns>布林值，表示工作是否以內嵌方式執行。</returns>
      <param name="task">要執行的 <see cref="T:System.Threading.Tasks.Task" />。</param>
      <param name="taskWasPreviouslyQueued">布林值，表示之前是否已排入過工作。如果這個參數為 True，則可能之前已排入 (排定) 過工作，如果為 False，則表示未排入過工作，系統便會隨即執行此呼叫，以內嵌方式執行工作，而不將它排入佇列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="task" /> 引數為 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="task" /> 已經執行。</exception>
    </member>
    <member name="E:System.Threading.Tasks.TaskScheduler.UnobservedTaskException">
      <summary>當有錯誤之工作未觀察到的例外狀況即將觸發例外狀況擴大原則 (此原則預設會讓處理序結束) 時發生。</summary>
    </member>
    <member name="T:System.Threading.Tasks.TaskSchedulerException">
      <summary>表示 <see cref="T:System.Threading.Tasks.TaskScheduler" /> 用來傳達無效作業的例外狀況。</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskSchedulerException.#ctor">
      <summary>使用系統提供的錯誤說明訊息，初始化 <see cref="T:System.Threading.Tasks.TaskSchedulerException" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskSchedulerException.#ctor(System.Exception)">
      <summary>使用預設錯誤訊息和造成這個例外狀況的內部例外狀況參考，初始化 <see cref="T:System.Threading.Tasks.TaskSchedulerException" /> 類別的新執行個體。</summary>
      <param name="innerException">導致目前例外狀況的例外。</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskSchedulerException.#ctor(System.String)">
      <summary>使用指定的錯誤說明訊息，初始化 <see cref="T:System.Threading.Tasks.TaskSchedulerException" /> 類別的新執行個體。</summary>
      <param name="message">說明例外狀況的訊息。這個建構函式的呼叫端必須確保這個字串已經為目前系統的文化特性當地語系化。</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskSchedulerException.#ctor(System.String,System.Exception)">
      <summary>使用指定的錯誤訊息和造成這個例外狀況原因的內部例外狀況參考，初始化 <see cref="T:System.Threading.Tasks.TaskSchedulerException" /> 類別的新執行個體。</summary>
      <param name="message">說明例外狀況的訊息。這個建構函式的呼叫端必須確保這個字串已經為目前系統的文化特性當地語系化。</param>
      <param name="innerException">導致目前例外狀況的例外。如果 <paramref name="innerException" /> 參數不是 null，則目前的例外狀況會在處理內部例外的 catch 區塊中引發。</param>
    </member>
    <member name="T:System.Threading.Tasks.TaskStatus">
      <summary>表示 <see cref="T:System.Threading.Tasks.Task" /> 開發週期中的目前階段。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.Canceled">
      <summary>工作確認取消動作，不論是因為工作在語彙基元處於信號狀態時使用自己的 CancellationToken 擲回 OperationCanceledException，或是工作的 CancellationToken 信號在工作開始執行之前便已存在。如需詳細資訊，請參閱工作取消。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.Created">
      <summary>工作已初始化但尚未排程。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.Faulted">
      <summary>工作因未處理的例外狀況而完成。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.RanToCompletion">
      <summary>工作已成功完成執行。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.Running">
      <summary>工作正在執行，但尚未完成。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.WaitingForActivation">
      <summary>工作正在等候由 .NET Framework 基礎結構從內部啟動並排程。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.WaitingForChildrenToComplete">
      <summary>工作已完成執行，而且在暗中等候附加的子工作完成。</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.WaitingToRun">
      <summary>工作已排定執行，但尚未開始執行。</summary>
    </member>
    <member name="T:System.Threading.Tasks.UnobservedTaskExceptionEventArgs">
      <summary>提供當未觀察到錯誤 <see cref="T:System.Threading.Tasks.Task" /> 的例外狀況時，所引發事件的資料。</summary>
    </member>
    <member name="M:System.Threading.Tasks.UnobservedTaskExceptionEventArgs.#ctor(System.AggregateException)">
      <summary>使用未觀察的例外狀況，初始化 <see cref="T:System.Threading.Tasks.UnobservedTaskExceptionEventArgs" /> 類別的新執行個體。</summary>
      <param name="exception">已變成未觀察對象的例外狀況。</param>
    </member>
    <member name="P:System.Threading.Tasks.UnobservedTaskExceptionEventArgs.Exception">
      <summary>變成未觀察到的例外狀況。</summary>
      <returns>變成未觀察到的例外狀況。</returns>
    </member>
    <member name="P:System.Threading.Tasks.UnobservedTaskExceptionEventArgs.Observed">
      <summary>取得這個例外狀況是否已標記為「已觀察」。</summary>
      <returns>如果這個例外狀況已標記為「已觀察」則為 true，否則為 false。</returns>
    </member>
    <member name="M:System.Threading.Tasks.UnobservedTaskExceptionEventArgs.SetObserved">
      <summary>將 <see cref="P:System.Threading.Tasks.UnobservedTaskExceptionEventArgs.Exception" /> 標記為「已觀察」，讓其無法觸發例外狀況擴大原則 (此原則預設會讓處理序結束)。</summary>
    </member>
  </members>
</doc>