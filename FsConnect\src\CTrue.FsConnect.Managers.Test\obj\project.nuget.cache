{"version": 2, "dgSpecHash": "88H2G69VTPU=", "success": false, "projectFilePath": "C:\\dev\\Personal\\FlightPig\\FsConnect\\src\\CTrue.FsConnect.Managers.Test\\CTrue.FsConnect.Managers.Test.csproj", "expectedPackageFiles": [], "logs": [{"code": "NU1301", "level": "Error", "message": "The local source 'C:\\dev\\Personal\\FlightPig\\FsConnect\\artifacts\\packages' doesn't exist.", "libraryId": "NUnit"}, {"code": "NU1301", "level": "Error", "message": "The local source 'C:\\dev\\Personal\\FlightPig\\FsConnect\\artifacts\\packages' doesn't exist.", "libraryId": "Microsoft.NET.Test.Sdk"}, {"code": "NU1301", "level": "Error", "message": "The local source 'C:\\dev\\Personal\\FlightPig\\FsConnect\\artifacts\\packages' doesn't exist.", "libraryId": "NUnit3TestAdapter"}]}