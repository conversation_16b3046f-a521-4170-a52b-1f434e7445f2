{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"CTrue.FsConnect.FsEnumGenerator/1.4.0": {"dependencies": {"CommandLineParser": "2.8.0"}, "runtime": {"CTrue.FsConnect.FsEnumGenerator.dll": {}}}, "CommandLineParser/2.8.0": {"runtime": {"lib/netstandard2.0/CommandLine.dll": {"assemblyVersion": "2.8.0.0", "fileVersion": "2.8.0.0"}}}}}, "libraries": {"CTrue.FsConnect.FsEnumGenerator/1.4.0": {"type": "project", "serviceable": false, "sha512": ""}, "CommandLineParser/2.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-eco2HlKQBY4Joz9odHigzGpVzv6pjsXnY5lziioMveQxr+i2Z7xYcIOMeZTgYiqnMtMAbXMXsVhrNfWO5vJS8Q==", "path": "commandlineparser/2.8.0", "hashPath": "commandlineparser.2.8.0.nupkg.sha512"}}}