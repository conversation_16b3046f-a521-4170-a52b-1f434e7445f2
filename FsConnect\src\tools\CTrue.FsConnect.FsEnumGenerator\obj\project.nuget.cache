{"version": 2, "dgSpecHash": "ZGMbUt0Q4hQ=", "success": false, "projectFilePath": "C:\\dev\\Personal\\FlightPig\\FsConnect\\src\\tools\\CTrue.FsConnect.FsEnumGenerator\\CTrue.FsConnect.FsEnumGenerator.csproj", "expectedPackageFiles": [], "logs": [{"code": "NU1301", "level": "Error", "message": "The local source 'C:\\dev\\Personal\\FlightPig\\FsConnect\\artifacts\\packages' doesn't exist.", "libraryId": "CommandLineParser"}]}