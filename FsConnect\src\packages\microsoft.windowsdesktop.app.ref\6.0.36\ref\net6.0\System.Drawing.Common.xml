<?xml version="1.0"?>
<doc>
    <assembly>
        <name>System.Drawing.Common</name>
    </assembly>
    <members>
        <member name="T:System.Drawing.BitmapSuffixInSameAssemblyAttribute">
            <summary>
            Opt-In flag to look for resources in the same assembly but with the "bitmapSuffix" config setting.
            i.e. System.Web.UI.WebControl.Button.bmp -> System.Web.UI.WebControl.Button.VisualStudio.11.0.bmp
            </summary>
        </member>
        <member name="T:System.Drawing.BitmapSuffixInSatelliteAssemblyAttribute">
            <summary>
            Opt-In flag to look for resources in the another assembly with the "bitmapSuffix" config setting
            i.e. System.Web.dll -> System.Web.VisualStudio.11.0.dll
            </summary>
        </member>
        <member name="T:System.Drawing.BufferedGraphics">
            <summary>
            The BufferedGraphics class can be thought of as a "Token" or "Reference" to the buffer that a
            BufferedGraphicsContext creates. While a BufferedGraphics is outstanding, the memory associated with the
            buffer is locked. The general design is such that under normal conditions a single BufferedGraphics will be in
            use at one time for a given BufferedGraphicsContext.
            </summary>
        </member>
        <member name="P:System.Drawing.BufferedGraphics.DisposeContext">
            <summary>
            Determines if we need to dispose of the Context when this is disposed.
            </summary>
        </member>
        <member name="M:System.Drawing.BufferedGraphics.Render">
            <summary>
            Renders the buffer to the original graphics used to allocate the buffer.
            </summary>
        </member>
        <member name="M:System.Drawing.BufferedGraphics.Render(System.IntPtr)">
            <summary>
            Renders the buffer to the specified target HDC.
            </summary>
        </member>
        <member name="M:System.Drawing.BufferedGraphics.#ctor(System.Drawing.Graphics,System.Drawing.BufferedGraphicsContext,System.Drawing.Graphics,System.IntPtr,System.Drawing.Point,System.Drawing.Size)">
            <summary>
            Internal constructor, this class is created by BufferedGraphicsContext.
            </summary>
        </member>
        <member name="P:System.Drawing.BufferedGraphics.Graphics">
            <summary>
            Allows access to the Graphics wrapper for the buffer.
            </summary>
        </member>
        <member name="M:System.Drawing.BufferedGraphics.Render(System.Drawing.Graphics)">
            <summary>
            Renders the buffer to the specified target graphics.
            </summary>
        </member>
        <member name="M:System.Drawing.BufferedGraphics.RenderInternal(System.Runtime.InteropServices.HandleRef)">
            <summary>
            Internal method that renders the specified buffer into the target.
            </summary>
        </member>
        <member name="T:System.Drawing.BufferedGraphicsContext">
            <summary>
            The BufferedGraphicsContext class can be used to perform standard double buffer rendering techniques.
            </summary>
        </member>
        <member name="M:System.Drawing.BufferedGraphicsContext.#ctor">
            <summary>
            Basic constructor.
            </summary>
        </member>
        <member name="P:System.Drawing.BufferedGraphicsContext.MaximumBuffer">
            <summary>
            Allows you to set the maximum width and height of the buffer that will be retained in memory.
            You can allocate a buffer of any size, however any request for a buffer that would have a total
            memory footprint larger that the maximum size will be allocated temporarily and then discarded
            with the BufferedGraphics is released.
            </summary>
        </member>
        <member name="M:System.Drawing.BufferedGraphicsContext.Allocate(System.Drawing.Graphics,System.Drawing.Rectangle)">
            <summary>
            Returns a BufferedGraphics that is matched for the specified target Graphics object.
            </summary>
        </member>
        <member name="M:System.Drawing.BufferedGraphicsContext.Allocate(System.IntPtr,System.Drawing.Rectangle)">
            <summary>
            Returns a BufferedGraphics that is matched for the specified target HDC object.
            </summary>
        </member>
        <member name="M:System.Drawing.BufferedGraphicsContext.AllocBufferInTempManager(System.Drawing.Graphics,System.IntPtr,System.Drawing.Rectangle)">
            <summary>
            Returns a BufferedGraphics that is matched for the specified target HDC object.
            </summary>
        </member>
        <member name="M:System.Drawing.BufferedGraphicsContext.ShouldUseTempManager(System.Drawing.Rectangle)">
            <summary>
            This routine allows us to control the point were we start using throw away
            managers for painting. Since the buffer manager stays around (by default)
            for the life of the app, we don't want to consume too much memory
            in the buffer. However, re-allocating the buffer for small things (like
            buttons, labels, etc) will hit us on runtime performance.
            </summary>
        </member>
        <member name="M:System.Drawing.BufferedGraphicsContext.AllocBuffer(System.Drawing.Graphics,System.IntPtr,System.Drawing.Rectangle)">
            <summary>
            Returns a BufferedGraphics that is matched for the specified target HDC object.
            </summary>
        </member>
        <member name="M:System.Drawing.BufferedGraphicsContext.FillBitmapInfo(System.IntPtr,System.IntPtr,Interop.Gdi32.BITMAPINFO_FLAT@)">
             <summary>
             Fills in the fields of a BITMAPINFO so that we can create a bitmap
             that matches the format of the display.
            
             This is done by creating a compatible bitmap and calling GetDIBits
             to return the color masks. This is done with two calls. The first
             call passes in biBitCount = 0 to GetDIBits which will fill in the
             base BITMAPINFOHEADER data. The second call to GetDIBits (passing
             in the BITMAPINFO filled in by the first call) will return the color
             table or bitmasks, as appropriate.
             </summary>
             <returns>True if successful, false otherwise.</returns>
        </member>
        <member name="M:System.Drawing.BufferedGraphicsContext.FillColorTable(System.IntPtr,System.IntPtr,Interop.Gdi32.BITMAPINFO_FLAT@)">
             <summary>
             Initialize the color table of the BITMAPINFO pointed to by pbmi. Colors
             are set to the current system palette.
            
             Note: call only valid for displays of 8bpp or less.
             </summary>
             <returns>True is successful, false otherwise.</returns>
        </member>
        <member name="M:System.Drawing.BufferedGraphicsContext.CreateBuffer(System.IntPtr,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Returns a Graphics object representing a buffer.
            </summary>
        </member>
        <member name="M:System.Drawing.BufferedGraphicsContext.CreateCompatibleDIB(System.IntPtr,System.IntPtr,System.Int32,System.Int32,System.IntPtr@)">
             <summary>
             Create a DIB section with an optimal format w.r.t. the specified hdc.
            
             If DIB &lt;= 8bpp, then the DIB color table is initialized based on the
             specified palette. If the palette handle is NULL, then the system
             palette is used.
            
             Note: The hdc must be a direct DC (not an info or memory DC).
            
             Note: On palettized displays, if the system palette changes the
                   UpdateDIBColorTable function should be called to maintain
                   the identity palette mapping between the DIB and the display.
             </summary>
             <returns>A valid bitmap handle if successful, IntPtr.Zero otherwise.</returns>
        </member>
        <member name="M:System.Drawing.BufferedGraphicsContext.DisposeDC">
            <summary>
            Disposes the DC, but leaves the bitmap alone.
            </summary>
        </member>
        <member name="M:System.Drawing.BufferedGraphicsContext.DisposeBitmap">
            <summary>
            Disposes the bitmap, will ASSERT if bitmap is being used (checks oldbitmap). if ASSERTed, call DisposeDC() first.
            </summary>
        </member>
        <member name="M:System.Drawing.BufferedGraphicsContext.Dispose(System.Boolean)">
            <summary>
            Disposes of the Graphics buffer.
            </summary>
        </member>
        <member name="M:System.Drawing.BufferedGraphicsContext.Invalidate">
            <summary>
            Invalidates the cached graphics buffer.
            </summary>
        </member>
        <member name="M:System.Drawing.BufferedGraphicsContext.ReleaseBuffer(System.Drawing.BufferedGraphics)">
            <summary>
            Returns a Graphics object representing a buffer.
            </summary>
        </member>
        <member name="M:System.Drawing.CharacterRange.#ctor(System.Int32,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.CharacterRange'/> class with the specified coordinates.
            </summary>
        </member>
        <member name="P:System.Drawing.CharacterRange.First">
            <summary>
            Gets the First character position of this <see cref='T:System.Drawing.CharacterRange'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.CharacterRange.Length">
            <summary>
            Gets the Length of this <see cref='T:System.Drawing.CharacterRange'/>.
            </summary>
        </member>
        <member name="T:System.Drawing.ContentAlignment">
            <summary>
            Specifies alignment of content on the drawing surface.
            </summary>
        </member>
        <member name="F:System.Drawing.ContentAlignment.TopLeft">
            <summary>
            Content is vertically aligned at the top, and horizontally aligned on the left.
            </summary>
        </member>
        <member name="F:System.Drawing.ContentAlignment.TopCenter">
            <summary>
            Content is vertically aligned at the top, and horizontally aligned at the center.
            </summary>
        </member>
        <member name="F:System.Drawing.ContentAlignment.TopRight">
            <summary>
            Content is vertically aligned at the top, and horizontally aligned on the right.
            </summary>
        </member>
        <member name="F:System.Drawing.ContentAlignment.MiddleLeft">
            <summary>
            Content is vertically aligned in the middle, and horizontally aligned on the left.
            </summary>
        </member>
        <member name="F:System.Drawing.ContentAlignment.MiddleCenter">
            <summary>
            Content is vertically aligned in the middle, and horizontally aligned at the center.
            </summary>
        </member>
        <member name="F:System.Drawing.ContentAlignment.MiddleRight">
            <summary>
            Content is vertically aligned in the middle, and horizontally aligned on the right.
            </summary>
        </member>
        <member name="F:System.Drawing.ContentAlignment.BottomLeft">
            <summary>
            Content is vertically aligned at the bottom, and horizontally aligned on the left.
            </summary>
        </member>
        <member name="F:System.Drawing.ContentAlignment.BottomCenter">
            <summary>
            Content is vertically aligned at the bottom, and horizontally aligned at the center.
            </summary>
        </member>
        <member name="F:System.Drawing.ContentAlignment.BottomRight">
            <summary>
            Content is vertically aligned at the bottom, and horizontally aligned on the right.
            </summary>
        </member>
        <member name="T:System.Drawing.IDeviceContext">
             <summary>
             This interface defines methods for obtaining a display/window device context handle (Win32 hdc).
             Note: Display and window dc handles are obtained and released using BeginPaint/EndPaint and
             GetDC/ReleaseDC; this interface is intended to be used with the last method only.
            
             Warning to implementors: Creating and releasing non-display dc handles using this interface needs
             special care, for instance using other Win32 functions like CreateDC or CreateCompatibleDC require
             DeleteDC instead of ReleaseDC to properly free the dc handle.
            
             See the DeviceContext class for an implementation of this interface, it uses the Dispose method
             for freeing non-display dc handles.
            
             This is a low-level API that is expected to be used with TextRenderer or PInvoke calls.
             </summary>
        </member>
        <member name="P:System.Drawing.SafeNativeMethods.Gdip.Initialized">
            <summary>
            Returns true if GDI+ has been started, but not shut down
            </summary>
        </member>
        <member name="P:System.Drawing.SafeNativeMethods.Gdip.ThreadData">
            <summary>
            This property will give us back a hashtable we can use to store all of our static brushes and pens on
            a per-thread basis. This way we can avoid 'object in use' crashes when different threads are
            referencing the same drawing object.
            </summary>
        </member>
        <member name="F:System.Drawing.SafeNativeMethods.ENHMETAHEADER.iType">
            The ENHMETAHEADER structure is defined natively as a union with WmfHeader.
            Extreme care should be taken if changing the layout of the corresponding managed
            structures to minimize the risk of buffer overruns.  The affected managed classes
            are the following: ENHMETAHEADER, MetaHeader, MetafileHeaderWmf, MetafileHeaderEmf.
        </member>
        <member name="T:System.Drawing.Graphics">
            <summary>
            Encapsulates a GDI+ drawing surface.
            </summary>
            <summary>
            Encapsulates a GDI+ drawing surface.
            </summary>
        </member>
        <member name="F:System.Drawing.Graphics._nativeHdc">
            <summary>
            Handle to native DC - obtained from the GDI+ graphics object. We need to cache it to implement
            IDeviceContext interface.
            </summary>
        </member>
        <member name="T:System.Drawing.Graphics.EnumerateMetafileProc">
            <summary>
            Callback for EnumerateMetafile methods.
            This method can then call Metafile.PlayRecord to play the record that was just enumerated.
            </summary>
            <param name="recordType">if >= MinRecordType, it's an EMF+ record</param>
            <param name="flags">always 0 for EMF records</param>
            <param name="dataSize">size of the data, or 0 if no data</param>
            <param name="data">pointer to the data, or NULL if no data (UINT32 aligned)</param>
            <param name="callbackData">pointer to callbackData, if any</param>
            <returns>False to abort enumerating, true to continue.</returns>
        </member>
        <member name="P:System.Drawing.Graphics.NativeGraphics">
            <summary>
            Handle to native GDI+ graphics object. This object is created on demand.
            </summary>
        </member>
        <member name="P:System.Drawing.Graphics.CompositingMode">
            <summary>
            Gets or sets the <see cref='T:System.Drawing.Drawing2D.CompositingMode'/> associated with this <see cref='T:System.Drawing.Graphics'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Graphics.InterpolationMode">
            <summary>
            Gets or sets the interpolation mode associated with this Graphics.
            </summary>
        </member>
        <member name="P:System.Drawing.Graphics.TextRenderingHint">
            <summary>
            Gets or sets the rendering mode for text associated with this <see cref='T:System.Drawing.Graphics'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Graphics.Transform">
            <summary>
            Gets or sets the world transform for this <see cref='T:System.Drawing.Graphics'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Graphics.TransformElements">
            <summary>
            Gets or sets the world transform elements for this <see cref="T:System.Drawing.Graphics"/>.
            </summary>
            <remarks>
            This is a more performant alternative to <see cref="P:System.Drawing.Graphics.Transform"/> that does not need disposal.
            </remarks>
        </member>
        <member name="M:System.Drawing.Graphics.Flush">
            <summary>
            Forces immediate execution of all operations currently on the stack.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.Flush(System.Drawing.Drawing2D.FlushIntention)">
            <summary>
            Forces execution of all operations currently on the stack.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.ResetTransform">
            <summary>
            Resets the world transform to identity.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.MultiplyTransform(System.Drawing.Drawing2D.Matrix)">
            <summary>
            Multiplies the <see cref='T:System.Drawing.Drawing2D.Matrix'/> that represents the world transform and <paramref name="matrix"/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.MultiplyTransform(System.Drawing.Drawing2D.Matrix,System.Drawing.Drawing2D.MatrixOrder)">
            <summary>
            Multiplies the <see cref='T:System.Drawing.Drawing2D.Matrix'/> that represents the world transform and <paramref name="matrix"/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.DrawArc(System.Drawing.Pen,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Draws an arc from the specified ellipse.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.DrawArc(System.Drawing.Pen,System.Drawing.RectangleF,System.Single,System.Single)">
            <summary>
            Draws an arc from the specified ellipse.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.DrawArc(System.Drawing.Pen,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Draws an arc from the specified ellipse.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.DrawArc(System.Drawing.Pen,System.Drawing.Rectangle,System.Single,System.Single)">
            <summary>
            Draws an arc from the specified ellipse.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.DrawBezier(System.Drawing.Pen,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Draws a cubic bezier curve defined by four ordered pairs that represent points.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.DrawBezier(System.Drawing.Pen,System.Drawing.PointF,System.Drawing.PointF,System.Drawing.PointF,System.Drawing.PointF)">
            <summary>
            Draws a cubic bezier curve defined by four points.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.DrawBezier(System.Drawing.Pen,System.Drawing.Point,System.Drawing.Point,System.Drawing.Point,System.Drawing.Point)">
            <summary>
            Draws a cubic bezier curve defined by four points.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.DrawRectangle(System.Drawing.Pen,System.Drawing.Rectangle)">
            <summary>
            Draws the outline of a rectangle specified by <paramref name="rect"/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.DrawRectangle(System.Drawing.Pen,System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Draws the outline of the specified rectangle.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.DrawRectangle(System.Drawing.Pen,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Draws the outline of the specified rectangle.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.DrawRectangles(System.Drawing.Pen,System.Drawing.RectangleF[])">
            <summary>
            Draws the outlines of a series of rectangles.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.DrawRectangles(System.Drawing.Pen,System.Drawing.Rectangle[])">
            <summary>
            Draws the outlines of a series of rectangles.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.DrawEllipse(System.Drawing.Pen,System.Drawing.RectangleF)">
            <summary>
            Draws the outline of an ellipse defined by a bounding rectangle.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.DrawEllipse(System.Drawing.Pen,System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Draws the outline of an ellipse defined by a bounding rectangle.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.DrawEllipse(System.Drawing.Pen,System.Drawing.Rectangle)">
            <summary>
            Draws the outline of an ellipse specified by a bounding rectangle.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.DrawEllipse(System.Drawing.Pen,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Draws the outline of an ellipse defined by a bounding rectangle.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.DrawPie(System.Drawing.Pen,System.Drawing.RectangleF,System.Single,System.Single)">
            <summary>
            Draws the outline of a pie section defined by an ellipse and two radial lines.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.DrawPie(System.Drawing.Pen,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Draws the outline of a pie section defined by an ellipse and two radial lines.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.DrawPie(System.Drawing.Pen,System.Drawing.Rectangle,System.Single,System.Single)">
            <summary>
            Draws the outline of a pie section defined by an ellipse and two radial lines.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.DrawPie(System.Drawing.Pen,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Draws the outline of a pie section defined by an ellipse and two radial lines.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.DrawPolygon(System.Drawing.Pen,System.Drawing.PointF[])">
            <summary>
            Draws the outline of a polygon defined by an array of points.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.DrawPolygon(System.Drawing.Pen,System.Drawing.Point[])">
            <summary>
            Draws the outline of a polygon defined by an array of points.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.DrawPath(System.Drawing.Pen,System.Drawing.Drawing2D.GraphicsPath)">
            <summary>
            Draws the lines and curves defined by a <see cref='T:System.Drawing.Drawing2D.GraphicsPath'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.DrawCurve(System.Drawing.Pen,System.Drawing.PointF[])">
            <summary>
            Draws a curve defined by an array of points.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.DrawCurve(System.Drawing.Pen,System.Drawing.PointF[],System.Single)">
            <summary>
            Draws a curve defined by an array of points.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.DrawCurve(System.Drawing.Pen,System.Drawing.PointF[],System.Int32,System.Int32,System.Single)">
            <summary>
            Draws a curve defined by an array of points.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.DrawCurve(System.Drawing.Pen,System.Drawing.Point[])">
            <summary>
            Draws a curve defined by an array of points.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.DrawCurve(System.Drawing.Pen,System.Drawing.Point[],System.Single)">
            <summary>
            Draws a curve defined by an array of points.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.DrawCurve(System.Drawing.Pen,System.Drawing.Point[],System.Int32,System.Int32,System.Single)">
            <summary>
            Draws a curve defined by an array of points.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.DrawClosedCurve(System.Drawing.Pen,System.Drawing.PointF[])">
            <summary>
            Draws a closed curve defined by an array of points.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.DrawClosedCurve(System.Drawing.Pen,System.Drawing.PointF[],System.Single,System.Drawing.Drawing2D.FillMode)">
            <summary>
            Draws a closed curve defined by an array of points.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.DrawClosedCurve(System.Drawing.Pen,System.Drawing.Point[])">
            <summary>
            Draws a closed curve defined by an array of points.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.DrawClosedCurve(System.Drawing.Pen,System.Drawing.Point[],System.Single,System.Drawing.Drawing2D.FillMode)">
            <summary>
            Draws a closed curve defined by an array of points.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.Clear(System.Drawing.Color)">
            <summary>
            Fills the entire drawing surface with the specified color.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.FillRectangle(System.Drawing.Brush,System.Drawing.RectangleF)">
            <summary>
            Fills the interior of a rectangle with a <see cref='T:System.Drawing.Brush'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.FillRectangle(System.Drawing.Brush,System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Fills the interior of a rectangle with a <see cref='T:System.Drawing.Brush'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.FillRectangle(System.Drawing.Brush,System.Drawing.Rectangle)">
            <summary>
            Fills the interior of a rectangle with a <see cref='T:System.Drawing.Brush'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.FillRectangle(System.Drawing.Brush,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Fills the interior of a rectangle with a <see cref='T:System.Drawing.Brush'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.FillRectangles(System.Drawing.Brush,System.Drawing.RectangleF[])">
            <summary>
            Fills the interiors of a series of rectangles with a <see cref='T:System.Drawing.Brush'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.FillRectangles(System.Drawing.Brush,System.Drawing.Rectangle[])">
            <summary>
            Fills the interiors of a series of rectangles with a <see cref='T:System.Drawing.Brush'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.FillPolygon(System.Drawing.Brush,System.Drawing.PointF[])">
            <summary>
            Fills the interior of a polygon defined by an array of points.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.FillPolygon(System.Drawing.Brush,System.Drawing.PointF[],System.Drawing.Drawing2D.FillMode)">
            <summary>
            Fills the interior of a polygon defined by an array of points.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.FillPolygon(System.Drawing.Brush,System.Drawing.Point[])">
            <summary>
            Fills the interior of a polygon defined by an array of points.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.FillPolygon(System.Drawing.Brush,System.Drawing.Point[],System.Drawing.Drawing2D.FillMode)">
            <summary>
            Fills the interior of a polygon defined by an array of points.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.FillEllipse(System.Drawing.Brush,System.Drawing.RectangleF)">
            <summary>
            Fills the interior of an ellipse defined by a bounding rectangle.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.FillEllipse(System.Drawing.Brush,System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Fills the interior of an ellipse defined by a bounding rectangle.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.FillEllipse(System.Drawing.Brush,System.Drawing.Rectangle)">
            <summary>
            Fills the interior of an ellipse defined by a bounding rectangle.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.FillEllipse(System.Drawing.Brush,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Fills the interior of an ellipse defined by a bounding rectangle.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.FillPie(System.Drawing.Brush,System.Drawing.Rectangle,System.Single,System.Single)">
            <summary>
            Fills the interior of a pie section defined by an ellipse and two radial lines.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.FillPie(System.Drawing.Brush,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Fills the interior of a pie section defined by an ellipse and two radial lines.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.FillPie(System.Drawing.Brush,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Fills the interior of a pie section defined by an ellipse and two radial lines.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.FillClosedCurve(System.Drawing.Brush,System.Drawing.PointF[])">
            <summary>
            Fills the interior a closed curve defined by an array of points.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.FillClosedCurve(System.Drawing.Brush,System.Drawing.PointF[],System.Drawing.Drawing2D.FillMode)">
            <summary>
            Fills the interior of a closed curve defined by an array of points.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.FillClosedCurve(System.Drawing.Brush,System.Drawing.Point[])">
            <summary>
            Fills the interior a closed curve defined by an array of points.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.DrawString(System.String,System.Drawing.Font,System.Drawing.Brush,System.Single,System.Single)">
            <summary>
            Draws a string with the specified font.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.DrawImage(System.Drawing.Image,System.Drawing.PointF)">
            <summary>
            Draws the specified image at the specified location.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.DrawLine(System.Drawing.Pen,System.Drawing.PointF,System.Drawing.PointF)">
            <summary>
            Draws a line connecting the two specified points.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.DrawLines(System.Drawing.Pen,System.Drawing.PointF[])">
            <summary>
            Draws a series of line segments that connect an array of points.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.DrawLine(System.Drawing.Pen,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Draws a line connecting the two specified points.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.DrawLine(System.Drawing.Pen,System.Drawing.Point,System.Drawing.Point)">
            <summary>
            Draws a line connecting the two specified points.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.DrawLines(System.Drawing.Pen,System.Drawing.Point[])">
            <summary>
            Draws a series of line segments that connect an array of points.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.CopyFromScreen(System.Drawing.Point,System.Drawing.Point,System.Drawing.Size)">
            <summary>
            CopyPixels will perform a gdi "bitblt" operation to the source from the destination with the given size.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.CopyFromScreen(System.Int32,System.Int32,System.Int32,System.Int32,System.Drawing.Size)">
            <summary>
            CopyPixels will perform a gdi "bitblt" operation to the source from the destination with the given size.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.CopyFromScreen(System.Drawing.Point,System.Drawing.Point,System.Drawing.Size,System.Drawing.CopyPixelOperation)">
            <summary>
            CopyPixels will perform a gdi "bitblt" operation to the source from the destination with the given size
            and specified raster operation.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.IgnoreMetafileErrors(System.Drawing.Image,System.Int32@)">
            <summary>
            GDI+ will return a 'generic error' when we attempt to draw an Emf
            image with width/height == 1. Here, we will hack around this by
            resetting the errorstatus. Note that we don't do simple arg checking
            for height || width == 1 here because transforms can be applied to
            the Graphics object making it difficult to identify this scenario.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.GetRegionIfNotInfinite">
            <summary>
            Creates a Region class only if the native region is not infinite.
            </summary>
        </member>
        <member name="F:System.Drawing.Graphics._previousContext">
            <summary>
            The context state previous to the current Graphics context (the head of the stack).
            We don't keep a GraphicsContext for the current context since it is available at any time from GDI+ and
            we don't want to keep track of changes in it.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.#ctor(System.IntPtr)">
            <summary>
            Constructor to initialize this object from a native GDI+ Graphics pointer.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.FromHdc(System.IntPtr)">
            <summary>
            Creates a new instance of the <see cref='T:System.Drawing.Graphics'/> class from the specified handle to a device context.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.FromHdc(System.IntPtr,System.IntPtr)">
            <summary>
            Creates a new instance of the Graphics class from the specified handle to a device context and handle to a device.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.FromHwnd(System.IntPtr)">
            <summary>
            Creates a new instance of the <see cref='T:System.Drawing.Graphics'/> class from a window handle.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.FromImage(System.Drawing.Image)">
            <summary>
            Creates an instance of the <see cref='T:System.Drawing.Graphics'/> class from an existing <see cref='T:System.Drawing.Image'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.Dispose">
            <summary>
            Deletes this <see cref='T:System.Drawing.Graphics'/>, and frees the memory allocated for it.
            </summary>
        </member>
        <member name="P:System.Drawing.Graphics.PrintingHelper">
            <summary>
            Represents an object used in connection with the printing API, it is used to hold a reference to a
            PrintPreviewGraphics (fake graphics) or a printer DeviceContext (and maybe more in the future).
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.CopyFromScreen(System.Int32,System.Int32,System.Int32,System.Int32,System.Drawing.Size,System.Drawing.CopyPixelOperation)">
            <summary>
            CopyPixels will perform a gdi "bitblt" operation to the source from the destination with the given size
            and specified raster operation.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.DrawLine(System.Drawing.Pen,System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Draws a line connecting the two specified points.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.DrawBeziers(System.Drawing.Pen,System.Drawing.PointF[])">
            <summary>
            Draws a series of cubic Bezier curves from an array of points.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.DrawBeziers(System.Drawing.Pen,System.Drawing.Point[])">
            <summary>
            Draws a series of cubic Bezier curves from an array of points.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.FillPath(System.Drawing.Brush,System.Drawing.Drawing2D.GraphicsPath)">
            <summary>
            Fills the interior of a path.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.FillRegion(System.Drawing.Brush,System.Drawing.Region)">
            <summary>
            Fills the interior of a <see cref='T:System.Drawing.Region'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.DrawIcon(System.Drawing.Icon,System.Drawing.Rectangle)">
            <summary>
            Draws this image to a graphics object. The drawing command originates on the graphics
            object, but a graphics object generally has no idea how to render a given image. So,
            it passes the call to the actual image. This version crops the image to the given
            dimensions and allows the user to specify a rectangle within the image to draw.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.DrawIconUnstretched(System.Drawing.Icon,System.Drawing.Rectangle)">
            <summary>
            Draws this image to a graphics object. The drawing command originates on the graphics
            object, but a graphics object generally has no idea how to render a given image. So,
            it passes the call to the actual image. This version stretches the image to the given
            dimensions and allows the user to specify a rectangle within the image to draw.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.GetContextInfo">
            <summary>
            Combines current Graphics context with all previous contexts.
            When BeginContainer() is called, a copy of the current context is pushed into the GDI+ context stack, it keeps track of the
            absolute clipping and transform but reset the public properties so it looks like a brand new context.
            When Save() is called, a copy of the current context is also pushed in the GDI+ stack but the public clipping and transform
            properties are not reset (cumulative). Consecutive Save context are ignored with the exception of the top one which contains
            all previous information.
            The return value is an object array where the first element contains the cumulative clip region and the second the cumulative
            translate transform matrix.
            WARNING: This method is for internal FX support only.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.GetContextInfo(System.Drawing.PointF@)">
            <summary>
             Gets the cumulative offset.
            </summary>
            <param name="offset">The cumulative offset.</param>
        </member>
        <member name="M:System.Drawing.Graphics.GetContextInfo(System.Drawing.PointF@,System.Drawing.Region@)">
            <summary>
             Gets the cumulative offset and clip region.
            </summary>
            <param name="offset">The cumulative offset.</param>
            <param name="clip">The cumulative clip region or null if the clip region is infinite.</param>
        </member>
        <member name="M:System.Drawing.Graphics.PushContext(System.Drawing.GraphicsContext)">
            <summary>
            Saves the current context into the context stack.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.PopContext(System.Int32)">
            <summary>
            Pops all contexts from the specified one included. The specified context is becoming the current context.
            </summary>
        </member>
        <member name="M:System.Drawing.Graphics.CheckErrorStatus(System.Int32)">
             <summary>
             GDI+ will return a 'generic error' with specific win32 last error codes when
             a terminal server session has been closed, minimized, etc... We don't want
             to throw when this happens, so we'll guard against this by looking at the
             'last win32 error code' and checking to see if it is either 1) access denied
             or 2) proc not found and then ignore it.
            
             The problem is that when you lock the machine, the secure desktop is enabled and
             rendering fails which is expected (since the app doesn't have permission to draw
             on the secure desktop). Not sure if there's anything you can do, short of catching
             the desktop switch message and absorbing all the exceptions that get thrown while
             it's the secure desktop.
             </summary>
        </member>
        <member name="T:System.Drawing.GraphicsUnit">
            <summary>
            Specifies the unit of measure for the given data.
            </summary>
        </member>
        <member name="F:System.Drawing.GraphicsUnit.World">
            <summary>
            Specifies the world unit as the unit of measure.
            </summary>
        </member>
        <member name="F:System.Drawing.GraphicsUnit.Display">
            <summary>
            Specifies 1/75 inch as the unit of measure.
            </summary>
        </member>
        <member name="F:System.Drawing.GraphicsUnit.Pixel">
            <summary>
            Specifies a device pixel as the unit of measure.
            </summary>
        </member>
        <member name="F:System.Drawing.GraphicsUnit.Point">
            <summary>
            Specifies a printer's point (1/72 inch) as the unit of measure.
            </summary>
        </member>
        <member name="F:System.Drawing.GraphicsUnit.Inch">
            <summary>
            Specifies the inch as the unit of measure.
            </summary>
        </member>
        <member name="F:System.Drawing.GraphicsUnit.Document">
            <summary>
            Specifies the document unit (1/300 inch) as the unit of measure.
            </summary>
        </member>
        <member name="F:System.Drawing.GraphicsUnit.Millimeter">
            <summary>
            Specifies the millimeter as the unit of measure.
            </summary>
        </member>
        <member name="T:System.Drawing.Image">
            <summary>
            An abstract base class that provides functionality for 'Bitmap', 'Icon', 'Cursor', and 'Metafile' descended classes.
            </summary>
        </member>
        <member name="M:System.Drawing.Image.FromFile(System.String)">
            <summary>
            Creates an <see cref='T:System.Drawing.Image'/> from the specified file.
            </summary>
        </member>
        <member name="M:System.Drawing.Image.FromStream(System.IO.Stream)">
            <summary>
            Creates an <see cref='T:System.Drawing.Image'/> from the specified data stream.
            </summary>
        </member>
        <member name="M:System.Drawing.Image.Dispose">
            <summary>
            Cleans up Windows resources for this <see cref='T:System.Drawing.Image'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Image.Finalize">
            <summary>
            Cleans up Windows resources for this <see cref='T:System.Drawing.Image'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Image.Save(System.String)">
            <summary>
            Saves this <see cref='T:System.Drawing.Image'/> to the specified file.
            </summary>
        </member>
        <member name="P:System.Drawing.Image.PhysicalDimension">
            <summary>
            Gets the width and height of this <see cref='T:System.Drawing.Image'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Image.Size">
            <summary>
            Gets the width and height of this <see cref='T:System.Drawing.Image'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Image.Width">
            <summary>
            Gets the width of this <see cref='T:System.Drawing.Image'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Image.Height">
            <summary>
            Gets the height of this <see cref='T:System.Drawing.Image'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Image.HorizontalResolution">
            <summary>
            Gets the horizontal resolution, in pixels-per-inch, of this <see cref='T:System.Drawing.Image'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Image.VerticalResolution">
            <summary>
            Gets the vertical resolution, in pixels-per-inch, of this <see cref='T:System.Drawing.Image'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Image.Flags">
            <summary>
            Gets attribute flags for this <see cref='T:System.Drawing.Image'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Image.RawFormat">
            <summary>
            Gets the format of this <see cref='T:System.Drawing.Image'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Image.PixelFormat">
            <summary>
            Gets the pixel format for this <see cref='T:System.Drawing.Image'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Image.PropertyIdList">
            <summary>
            Gets an array of the property IDs stored in this <see cref='T:System.Drawing.Image'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Image.PropertyItems">
            <summary>
            Gets an array of <see cref='T:System.Drawing.Imaging.PropertyItem'/> objects that describe this <see cref='T:System.Drawing.Image'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Image.GetFrameCount(System.Drawing.Imaging.FrameDimension)">
            <summary>
            Returns the number of frames of the given dimension.
            </summary>
        </member>
        <member name="M:System.Drawing.Image.GetPropertyItem(System.Int32)">
            <summary>
            Gets the specified property item from this <see cref='T:System.Drawing.Image'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Image.SelectActiveFrame(System.Drawing.Imaging.FrameDimension,System.Int32)">
            <summary>
            Selects the frame specified by the given dimension and index.
            </summary>
        </member>
        <member name="M:System.Drawing.Image.SetPropertyItem(System.Drawing.Imaging.PropertyItem)">
            <summary>
            Sets the specified property item to the specified value.
            </summary>
        </member>
        <member name="M:System.Drawing.Image.RemovePropertyItem(System.Int32)">
            <summary>
            Removes the specified property item from this <see cref='T:System.Drawing.Image'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Image.GetEncoderParameterList(System.Guid)">
            <summary>
            Returns information about the codecs used for this <see cref='T:System.Drawing.Image'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Image.FromHbitmap(System.IntPtr)">
            <summary>
            Creates a <see cref='T:System.Drawing.Bitmap'/> from a Windows handle.
            </summary>
        </member>
        <member name="M:System.Drawing.Image.FromHbitmap(System.IntPtr,System.IntPtr)">
            <summary>
            Creates a <see cref='T:System.Drawing.Bitmap'/> from the specified Windows handle with the specified color palette.
            </summary>
        </member>
        <member name="M:System.Drawing.Image.IsExtendedPixelFormat(System.Drawing.Imaging.PixelFormat)">
            <summary>
            Returns a value indicating whether the pixel format is extended.
            </summary>
        </member>
        <member name="M:System.Drawing.Image.IsCanonicalPixelFormat(System.Drawing.Imaging.PixelFormat)">
            <summary>
            Returns a value indicating whether the pixel format is canonical.
            </summary>
        </member>
        <member name="P:System.Drawing.Image.FrameDimensionsList">
            <summary>
            Gets an array of GUIDs that represent the dimensions of frames within this <see cref='T:System.Drawing.Image'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Image.GetPixelFormatSize(System.Drawing.Imaging.PixelFormat)">
            <summary>
            Returns the size of the specified pixel format.
            </summary>
        </member>
        <member name="M:System.Drawing.Image.IsAlphaPixelFormat(System.Drawing.Imaging.PixelFormat)">
            <summary>
            Returns a value indicating whether the pixel format contains alpha information.
            </summary>
        </member>
        <member name="M:System.Drawing.Image.Clone">
            <summary>
            Creates an exact copy of this <see cref='T:System.Drawing.Image'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Image.Save(System.String,System.Drawing.Imaging.ImageFormat)">
            <summary>
            Saves this <see cref='T:System.Drawing.Image'/> to the specified file in the specified format.
            </summary>
        </member>
        <member name="M:System.Drawing.Image.Save(System.String,System.Drawing.Imaging.ImageCodecInfo,System.Drawing.Imaging.EncoderParameters)">
            <summary>
            Saves this <see cref='T:System.Drawing.Image'/> to the specified file in the specified format and with the specified encoder parameters.
            </summary>
        </member>
        <member name="M:System.Drawing.Image.Save(System.IO.Stream,System.Drawing.Imaging.ImageFormat)">
            <summary>
            Saves this <see cref='T:System.Drawing.Image'/> to the specified stream in the specified format.
            </summary>
        </member>
        <member name="M:System.Drawing.Image.Save(System.IO.Stream,System.Drawing.Imaging.ImageCodecInfo,System.Drawing.Imaging.EncoderParameters)">
            <summary>
            Saves this <see cref='T:System.Drawing.Image'/> to the specified stream in the specified format.
            </summary>
        </member>
        <member name="M:System.Drawing.Image.SaveAdd(System.Drawing.Imaging.EncoderParameters)">
            <summary>
            Adds an <see cref='T:System.Drawing.Imaging.EncoderParameters'/> to this <see cref='T:System.Drawing.Image'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Image.SaveAdd(System.Drawing.Image,System.Drawing.Imaging.EncoderParameters)">
            <summary>
            Adds an <see cref='T:System.Drawing.Imaging.EncoderParameters'/> to the specified <see cref='T:System.Drawing.Image'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Image.GetBounds(System.Drawing.GraphicsUnit@)">
            <summary>
            Gets a bounding rectangle in the specified units for this <see cref='T:System.Drawing.Image'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Image.Palette">
            <summary>
            Gets or sets the color palette used for this <see cref='T:System.Drawing.Image'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Image.GetThumbnailImage(System.Int32,System.Int32,System.Drawing.Image.GetThumbnailImageAbort,System.IntPtr)">
            <summary>
            Returns the thumbnail for this <see cref='T:System.Drawing.Image'/>.
            </summary>
        </member>
        <member name="T:System.Drawing.ImageAnimator">
             <summary>
                 Animates one or more images that have time-based frames.
                 See the ImageInfo.cs file for the helper nested ImageInfo class.
            
                 A common pattern for using this class is as follows (See PictureBox control):
                 1. The winform app (user's code) calls ImageAnimator.Animate() from the main thread.
                 2. Animate() spawns the animating (worker) thread in the background, which will update the image
                    frames and raise the OnFrameChanged event, which handler will be executed in the main thread.
                 3. The main thread triggers a paint event (Invalidate()) from the OnFrameChanged handler.
                 4. From the OnPaint event, the main thread calls ImageAnimator.UpdateFrames() and then paints the
                    image (updated frame).
                 5. The main thread calls ImageAnimator.StopAnimate() when needed. This does not kill the worker thread.
            
                 Comment on locking the image ref:
                 We need to synchronize access to sections of code that modify the image(s), but we don't want to block
                 animation of one image when modifying a different one; for this, we use the image ref for locking the
                 critical section (lock(image)).
            
                 This class is safe for multi-threading but Image is not; multithreaded applications must use a critical
                 section lock using the image ref the image access is not from the same thread that executes ImageAnimator
                 code.  If the user code locks on the image ref forever a deadlock will happen preventing the animation
                 from occurring.
             </summary>
            <summary>
            Animates one or more images that have time-based frames. This file contains the nested ImageInfo class
            - See ImageAnimator.cs for the definition of the outer class.
            </summary>
        </member>
        <member name="F:System.Drawing.ImageAnimator.s_imageInfoList">
            <summary>
                A list of images to be animated.
            </summary>
        </member>
        <member name="F:System.Drawing.ImageAnimator.s_anyFrameDirty">
            <summary>
                A variable to flag when an image or images need to be updated due to the selection of a new frame
                in an image.  We don't need to synchronize access to this variable, in the case it is true we don't
                do anything, otherwise the worse case is where a thread attempts to update the image's frame after
                another one did which is harmless.
            </summary>
        </member>
        <member name="F:System.Drawing.ImageAnimator.s_animationThread">
            <summary>
                The thread used for animating the images.
            </summary>
        </member>
        <member name="F:System.Drawing.ImageAnimator.s_rwImgListLock">
            <summary>
                Lock that allows either concurrent read-access to the images list for multiple threads, or write-
                access to it for a single thread.  Observe that synchronization access to image objects are done
                with critical sections (lock).
            </summary>
        </member>
        <member name="F:System.Drawing.ImageAnimator.t_threadWriterLockWaitCount">
            <summary>
                Flag to avoid a deadlock when waiting on a write-lock and an attempt to acquire a read-lock is
                made in the same thread. If RWLock is currently owned by another thread, the current thread is going to wait on an
                event using CoWaitForMultipleHandles while pumps message.
                The comment above refers to the COM STA message pump, not to be confused with the UI message pump.
                However, the effect is the same, the COM message pump will pump messages and dispatch them to the
                window while waiting on the writer lock; this has the potential of creating a re-entrancy situation
                that if during the message processing a wait on a reader lock is originated the thread will be block
                on itself.
                While processing STA message, the thread may call back into managed code. We do this because
                we can not block finalizer thread.  Finalizer thread may need to release STA objects on this thread. If
                the current thread does not pump message, finalizer thread is blocked, and AD  unload is blocked while
                waiting for finalizer thread. RWLock is a fair lock. If a thread waits for a writer lock, then it needs
                a reader lock while pumping message, the thread is blocked forever.
                This TLS variable is used to flag the above situation and avoid the deadlock, it is ThreadStatic so each
                thread calling into ImageAnimator is guarded against this problem.
            </summary>
        </member>
        <member name="M:System.Drawing.ImageAnimator.#ctor">
            <summary>
                Prevent instantiation of this class.
            </summary>
        </member>
        <member name="M:System.Drawing.ImageAnimator.UpdateFrames(System.Drawing.Image)">
            <summary>
                Advances the frame in the specified image. The new frame is drawn the next time the image is rendered.
            </summary>
        </member>
        <member name="M:System.Drawing.ImageAnimator.UpdateFrames">
            <summary>
                Advances the frame in all images currently being animated. The new frame is drawn the next time the image is rendered.
            </summary>
        </member>
        <member name="M:System.Drawing.ImageAnimator.Animate(System.Drawing.Image,System.EventHandler)">
            <summary>
                Adds an image to the image manager.  If the image does not support animation this method does nothing.
                This method creates the image list and spawns the animation thread the first time it is called.
            </summary>
        </member>
        <member name="M:System.Drawing.ImageAnimator.CanAnimate(System.Drawing.Image)">
            <summary>
               Whether or not the image has multiple time-based frames.
            </summary>
        </member>
        <member name="M:System.Drawing.ImageAnimator.StopAnimate(System.Drawing.Image,System.EventHandler)">
            <summary>
                Removes an image from the image manager so it is no longer animated.
            </summary>
        </member>
        <member name="M:System.Drawing.ImageAnimator.AnimateImages">
            <summary>
                Worker thread procedure which implements the main animation loop.
                NOTE: This is the ONLY code the worker thread executes, keeping it in one method helps better understand
                any synchronization issues.
                WARNING: Also, this is the only place where ImageInfo objects (not the contained image object) are modified,
                so no access synchronization is required to modify them.
            </summary>
        </member>
        <member name="T:System.Drawing.ImageAnimator.ImageInfo">
            <summary>
            ImageAnimator nested helper class used to store extra image state info.
            </summary>
        </member>
        <member name="P:System.Drawing.ImageAnimator.ImageInfo.Animated">
            <summary>
            Whether the image supports animation.
            </summary>
        </member>
        <member name="P:System.Drawing.ImageAnimator.ImageInfo.FrameDirty">
            <summary>
            The current frame has changed but the image has not yet been updated.
            </summary>
        </member>
        <member name="P:System.Drawing.ImageAnimator.ImageInfo.TotalAnimationTime">
            <summary>
            The total animation time of the image in milliseconds, or <value>0</value> if not animated.
            </summary>
        </member>
        <member name="P:System.Drawing.ImageAnimator.ImageInfo.ShouldAnimate">
            <summary>
            Whether animation should progress, respecting the image's animation support
            and if there are animation frames or loops remaining.
            </summary>
        </member>
        <member name="M:System.Drawing.ImageAnimator.ImageInfo.AdvanceAnimationBy(System.Int64)">
            <summary>
            Advance the animation by the specified number of milliseconds. If the advancement
            progresses beyond the end time of the current Frame, <see cref="P:System.Drawing.ImageAnimator.ImageInfo.FrameChangedHandler"/>
            will be called. Subscribed handlers often use that event to call
            <see cref="M:System.Drawing.ImageAnimator.UpdateFrames(System.Drawing.Image)"/>.
            <para>
            If the animation progresses beyond the end of the image's total animation time,
            the animation will loop.
            </para>
            </summary>
            <remarks>
            This animation does not respect a GIF's specified number of animation repeats;
            instead, animations loop indefinitely.
            </remarks>
            <param name="milliseconds">The number of milliseconds to advance the animation by</param>
        </member>
        <member name="P:System.Drawing.ImageAnimator.ImageInfo.Image">
            <summary>
            The image this object wraps.
            </summary>
        </member>
        <member name="M:System.Drawing.ImageAnimator.ImageInfo.UpdateFrame">
            <summary>
            Selects the current frame as the active frame in the image.
            </summary>
        </member>
        <member name="M:System.Drawing.ImageAnimator.ImageInfo.OnFrameChanged(System.EventArgs)">
            <summary>
            Raises the FrameChanged event.
            </summary>
        </member>
        <member name="T:System.Drawing.NumericsExtensions">
            <summary>
            Helpers to allow using System.Numerics types like the System.Drawing equivalents.
            </summary>
        </member>
        <member name="T:System.Drawing.Pen">
            <summary>
            Defines an object used to draw lines and curves.
            </summary>
        </member>
        <member name="M:System.Drawing.Pen.#ctor(System.IntPtr)">
            <summary>
            Creates a Pen from a native GDI+ object.
            </summary>
        </member>
        <member name="M:System.Drawing.Pen.#ctor(System.Drawing.Color)">
            <summary>
            Initializes a new instance of the Pen class with the specified <see cref='P:System.Drawing.Pen.Color'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Pen.#ctor(System.Drawing.Color,System.Single)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Pen'/> class with the specified
            <see cref='P:System.Drawing.Pen.Color'/> and <see cref='P:System.Drawing.Pen.Width'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Pen.#ctor(System.Drawing.Brush)">
            <summary>
            Initializes a new instance of the Pen class with the specified <see cref='P:System.Drawing.Pen.Brush'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Pen.#ctor(System.Drawing.Brush,System.Single)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Pen'/> class with the specified <see cref='T:System.Drawing.Brush'/> and width.
            </summary>
        </member>
        <member name="M:System.Drawing.Pen.Clone">
            <summary>
            Creates an exact copy of this <see cref='T:System.Drawing.Pen'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Pen.Dispose">
            <summary>
            Cleans up Windows resources for this <see cref='T:System.Drawing.Pen'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Pen.Finalize">
            <summary>
            Cleans up Windows resources for this <see cref='T:System.Drawing.Pen'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Pen.Width">
            <summary>
            Gets or sets the width of this <see cref='T:System.Drawing.Pen'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Pen.SetLineCap(System.Drawing.Drawing2D.LineCap,System.Drawing.Drawing2D.LineCap,System.Drawing.Drawing2D.DashCap)">
            <summary>
            Sets the values that determine the style of cap used to end lines drawn by this <see cref='T:System.Drawing.Pen'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Pen.StartCap">
            <summary>
            Gets or sets the cap style used at the beginning of lines drawn with this <see cref='T:System.Drawing.Pen'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Pen.EndCap">
            <summary>
            Gets or sets the cap style used at the end of lines drawn with this <see cref='T:System.Drawing.Pen'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Pen.DashCap">
            <summary>
            Gets or sets the cap style used at the beginning or end of dashed lines drawn with this <see cref='T:System.Drawing.Pen'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Pen.LineJoin">
            <summary>
            Gets or sets the join style for the ends of two overlapping lines drawn with this <see cref='T:System.Drawing.Pen'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Pen.MiterLimit">
            <summary>
            Gets or sets the limit of the thickness of the join on a mitered corner.
            </summary>
        </member>
        <member name="P:System.Drawing.Pen.Alignment">
            <summary>
            Gets or sets the alignment for objects drawn with this <see cref='T:System.Drawing.Pen'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Pen.Transform">
            <summary>
            Gets or sets the geometrical transform for objects drawn with this <see cref='T:System.Drawing.Pen'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Pen.ResetTransform">
            <summary>
            Resets the geometric transform for this <see cref='T:System.Drawing.Pen'/> to identity.
            </summary>
        </member>
        <member name="M:System.Drawing.Pen.MultiplyTransform(System.Drawing.Drawing2D.Matrix)">
            <summary>
            Multiplies the transform matrix for this <see cref='T:System.Drawing.Pen'/> by the specified <see cref='T:System.Drawing.Drawing2D.Matrix'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Pen.MultiplyTransform(System.Drawing.Drawing2D.Matrix,System.Drawing.Drawing2D.MatrixOrder)">
            <summary>
            Multiplies the transform matrix for this <see cref='T:System.Drawing.Pen'/> by the specified <see cref='T:System.Drawing.Drawing2D.Matrix'/> in the specified order.
            </summary>
        </member>
        <member name="M:System.Drawing.Pen.TranslateTransform(System.Single,System.Single)">
            <summary>
            Translates the local geometrical transform by the specified dimensions. This method prepends the translation
            to the transform.
            </summary>
        </member>
        <member name="M:System.Drawing.Pen.TranslateTransform(System.Single,System.Single,System.Drawing.Drawing2D.MatrixOrder)">
            <summary>
            Translates the local geometrical transform by the specified dimensions in the specified order.
            </summary>
        </member>
        <member name="M:System.Drawing.Pen.ScaleTransform(System.Single,System.Single)">
            <summary>
            Scales the local geometric transform by the specified amounts. This method prepends the scaling matrix to the transform.
            </summary>
        </member>
        <member name="M:System.Drawing.Pen.ScaleTransform(System.Single,System.Single,System.Drawing.Drawing2D.MatrixOrder)">
            <summary>
            Scales the local geometric transform by the specified amounts in the specified order.
            </summary>
        </member>
        <member name="M:System.Drawing.Pen.RotateTransform(System.Single)">
            <summary>
            Rotates the local geometric transform by the specified amount. This method prepends the rotation to the transform.
            </summary>
        </member>
        <member name="M:System.Drawing.Pen.RotateTransform(System.Single,System.Drawing.Drawing2D.MatrixOrder)">
            <summary>
            Rotates the local geometric transform by the specified amount in the specified order.
            </summary>
        </member>
        <member name="P:System.Drawing.Pen.PenType">
            <summary>
            Gets the style of lines drawn with this <see cref='T:System.Drawing.Pen'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Pen.Color">
            <summary>
            Gets or sets the color of this <see cref='T:System.Drawing.Pen'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Pen.Brush">
            <summary>
            Gets or sets the <see cref='T:System.Drawing.Brush'/> that determines attributes of this <see cref='T:System.Drawing.Pen'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Pen.DashStyle">
            <summary>
            Gets or sets the style used for dashed lines drawn with this <see cref='T:System.Drawing.Pen'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Pen.EnsureValidDashPattern">
            <summary>
            This method is called after the user sets the pen's dash style to custom. Here, we make sure that there
            is a default value set for the custom pattern.
            </summary>
        </member>
        <member name="P:System.Drawing.Pen.DashOffset">
            <summary>
            Gets or sets the distance from the start of a line to the beginning of a dash pattern.
            </summary>
        </member>
        <member name="P:System.Drawing.Pen.DashPattern">
            <summary>
            Gets or sets an array of custom dashes and spaces. The dashes are made up of line segments.
            </summary>
        </member>
        <member name="P:System.Drawing.Pen.CompoundArray">
            <summary>
            Gets or sets an array of custom dashes and spaces. The dashes are made up of line segments.
            </summary>
        </member>
        <member name="P:System.Drawing.Pen.CustomStartCap">
            <summary>
            Gets or sets a custom cap style to use at the beginning of lines drawn with this <see cref='T:System.Drawing.Pen'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Pen.CustomEndCap">
            <summary>
            Gets or sets a custom cap style to use at the end of lines drawn with this <see cref='T:System.Drawing.Pen'/>.
            </summary>
        </member>
        <member name="T:System.Drawing.RotateFlipType">
            <summary>
            Specifies the different patterns available 'RotateFlipType' objects.
            </summary>
        </member>
        <member name="T:System.Drawing.StringFormat">
            <summary>
            Encapsulates text layout information (such as alignment and linespacing), display manipulations (such as
            ellipsis insertion and national digit substitution) and OpenType features.
            </summary>
        </member>
        <member name="M:System.Drawing.StringFormat.#ctor">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.StringFormat'/> class.
            </summary>
        </member>
        <member name="M:System.Drawing.StringFormat.#ctor(System.Drawing.StringFormatFlags)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.StringFormat'/> class with the specified <see cref='T:System.Drawing.StringFormatFlags'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.StringFormat.#ctor(System.Drawing.StringFormatFlags,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.StringFormat'/> class with the specified
            <see cref='T:System.Drawing.StringFormatFlags'/> and language.
            </summary>
        </member>
        <member name="M:System.Drawing.StringFormat.#ctor(System.Drawing.StringFormat)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.StringFormat'/> class from the specified
            existing <see cref='T:System.Drawing.StringFormat'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.StringFormat.Dispose">
            <summary>
            Cleans up Windows resources for this <see cref='T:System.Drawing.StringFormat'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.StringFormat.Clone">
            <summary>
            Creates an exact copy of this <see cref='T:System.Drawing.StringFormat'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.StringFormat.FormatFlags">
            <summary>
            Gets or sets a <see cref='T:System.Drawing.StringFormatFlags'/> that contains formatting information.
            </summary>
        </member>
        <member name="M:System.Drawing.StringFormat.SetMeasurableCharacterRanges(System.Drawing.CharacterRange[])">
            <summary>
            Sets the measure of characters to the specified range.
            </summary>
        </member>
        <member name="P:System.Drawing.StringFormat.Alignment">
            <summary>
            Specifies text alignment information.
            </summary>
        </member>
        <member name="P:System.Drawing.StringFormat.LineAlignment">
            <summary>
            Gets or sets the line alignment.
            </summary>
        </member>
        <member name="P:System.Drawing.StringFormat.HotkeyPrefix">
            <summary>
            Gets or sets the <see cref='P:System.Drawing.StringFormat.HotkeyPrefix'/> for this <see cref='T:System.Drawing.StringFormat'/> .
            </summary>
        </member>
        <member name="M:System.Drawing.StringFormat.SetTabStops(System.Single,System.Single[])">
            <summary>
            Sets tab stops for this <see cref='T:System.Drawing.StringFormat'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.StringFormat.GetTabStops(System.Single@)">
            <summary>
            Gets the tab stops for this <see cref='T:System.Drawing.StringFormat'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.StringFormat.Trimming">
            <summary>
            Gets or sets the <see cref='T:System.Drawing.StringTrimming'/> for this <see cref='T:System.Drawing.StringFormat'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.StringFormat.GenericDefault">
            <summary>
            Gets a generic default <see cref='T:System.Drawing.StringFormat'/>.
            Remarks from MSDN: A generic, default StringFormat object has the following characteristics:
            - No string format flags are set.
            - Character alignment and line alignment are set to StringAlignmentNear.
            - Language ID is set to neutral language, which means that the current language associated with the calling thread is used.
            - String digit substitution is set to StringDigitSubstituteUser.
            - Hot key prefix is set to HotkeyPrefixNone.
            - Number of tab stops is set to zero.
            - String trimming is set to StringTrimmingCharacter.
            </summary>
        </member>
        <member name="P:System.Drawing.StringFormat.GenericTypographic">
            <summary>
            Gets a generic typographic <see cref='T:System.Drawing.StringFormat'/>.
            Remarks from MSDN: A generic, typographic StringFormat object has the following characteristics:
            - String format flags StringFormatFlagsLineLimit, StringFormatFlagsNoClip, and StringFormatFlagsNoFitBlackBox are set.
            - Character alignment and line alignment are set to StringAlignmentNear.
            - Language ID is set to neutral language, which means that the current language associated with the calling thread is used.
            - String digit substitution is set to StringDigitSubstituteUser.
            - Hot key prefix is set to HotkeyPrefixNone.
            - Number of tab stops is set to zero.
            - String trimming is set to StringTrimmingNone.
            </summary>
        </member>
        <member name="P:System.Drawing.StringFormat.DigitSubstitutionMethod">
            <summary>
            Gets the <see cref='T:System.Drawing.StringDigitSubstitute'/> for this <see cref='T:System.Drawing.StringFormat'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.StringFormat.DigitSubstitutionLanguage">
            <summary>
            Gets the language of <see cref='T:System.Drawing.StringDigitSubstitute'/> for this <see cref='T:System.Drawing.StringFormat'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.StringFormat.Finalize">
            <summary>
            Cleans up Windows resources for this <see cref='T:System.Drawing.StringFormat'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.StringFormat.ToString">
            <summary>
            Converts this <see cref='T:System.Drawing.StringFormat'/> to a human-readable string.
            </summary>
        </member>
        <member name="T:System.Drawing.Imaging.Metafile">
            <summary>
            Defines a graphic metafile. A metafile contains records that describe a sequence of graphics operations that
            can be recorded and played back.
            </summary>
            <summary>
            Defines a graphic metafile. A metafile contains records that describe a sequence of graphics operations that
            can be recorded and played back.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.Metafile.#ctor(System.IntPtr,System.Drawing.Imaging.WmfPlaceableFileHeader,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Imaging.Metafile'/> class from the specified handle and
            <see cref='T:System.Drawing.Imaging.WmfPlaceableFileHeader'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.Metafile.#ctor(System.IntPtr,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Imaging.Metafile'/> class from the specified handle and
            <see cref='T:System.Drawing.Imaging.WmfPlaceableFileHeader'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.Metafile.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Imaging.Metafile'/> class from the specified filename.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.Metafile.#ctor(System.IntPtr,System.Drawing.Rectangle)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Imaging.Metafile'/> class from the specified device context, bounded
            by the specified rectangle.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.Metafile.#ctor(System.IntPtr,System.Drawing.Imaging.EmfType)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Imaging.Metafile'/> class from the specified handle to a device context.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.Metafile.#ctor(System.IntPtr,System.Drawing.RectangleF)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Imaging.Metafile'/> class from the specified device context, bounded
            by the specified rectangle.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.Metafile.#ctor(System.IntPtr,System.Drawing.RectangleF,System.Drawing.Imaging.MetafileFrameUnit)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Imaging.Metafile'/> class from the specified device context, bounded
            by the specified rectangle.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.Metafile.#ctor(System.IntPtr,System.Drawing.RectangleF,System.Drawing.Imaging.MetafileFrameUnit,System.Drawing.Imaging.EmfType)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Imaging.Metafile'/> class from the specified device context, bounded
            by the specified rectangle.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.Metafile.#ctor(System.IntPtr,System.Drawing.RectangleF,System.Drawing.Imaging.MetafileFrameUnit,System.Drawing.Imaging.EmfType,System.String)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Imaging.Metafile'/> class from the specified device context, bounded
            by the specified rectangle.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.Metafile.#ctor(System.IntPtr,System.Drawing.Rectangle,System.Drawing.Imaging.MetafileFrameUnit)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Imaging.Metafile'/> class from the specified device context, bounded
            by the specified rectangle.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.Metafile.#ctor(System.IntPtr,System.Drawing.Rectangle,System.Drawing.Imaging.MetafileFrameUnit,System.Drawing.Imaging.EmfType)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Imaging.Metafile'/> class from the specified device context, bounded
            by the specified rectangle.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.Metafile.#ctor(System.String,System.IntPtr)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Imaging.Metafile'/> class with the specified filename.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.Metafile.#ctor(System.String,System.IntPtr,System.Drawing.Imaging.EmfType)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Imaging.Metafile'/> class with the specified filename.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.Metafile.#ctor(System.String,System.IntPtr,System.Drawing.RectangleF)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Imaging.Metafile'/> class with the specified filename.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.Metafile.#ctor(System.String,System.IntPtr,System.Drawing.RectangleF,System.Drawing.Imaging.MetafileFrameUnit)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Imaging.Metafile'/> class with the specified filename.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.Metafile.#ctor(System.String,System.IntPtr,System.Drawing.RectangleF,System.Drawing.Imaging.MetafileFrameUnit,System.Drawing.Imaging.EmfType)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Imaging.Metafile'/> class with the specified filename.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.Metafile.#ctor(System.String,System.IntPtr,System.Drawing.RectangleF,System.Drawing.Imaging.MetafileFrameUnit,System.String)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Imaging.Metafile'/> class with the specified filename.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.Metafile.#ctor(System.String,System.IntPtr,System.Drawing.RectangleF,System.Drawing.Imaging.MetafileFrameUnit,System.Drawing.Imaging.EmfType,System.String)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Imaging.Metafile'/> class with the specified filename.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.Metafile.#ctor(System.String,System.IntPtr,System.Drawing.Rectangle)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Imaging.Metafile'/> class with the specified filename.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.Metafile.#ctor(System.String,System.IntPtr,System.Drawing.Rectangle,System.Drawing.Imaging.MetafileFrameUnit)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Imaging.Metafile'/> class with the specified filename.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.Metafile.#ctor(System.String,System.IntPtr,System.Drawing.Rectangle,System.Drawing.Imaging.MetafileFrameUnit,System.Drawing.Imaging.EmfType)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Imaging.Metafile'/> class with the specified filename.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.Metafile.#ctor(System.String,System.IntPtr,System.Drawing.Rectangle,System.Drawing.Imaging.MetafileFrameUnit,System.String)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Imaging.Metafile'/> class with the specified filename.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.Metafile.#ctor(System.IO.Stream,System.IntPtr)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Imaging.Metafile'/> class from the specified data stream.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.Metafile.#ctor(System.IO.Stream,System.IntPtr,System.Drawing.Imaging.EmfType)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Imaging.Metafile'/> class from the specified data stream.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.Metafile.#ctor(System.IO.Stream,System.IntPtr,System.Drawing.RectangleF)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Imaging.Metafile'/> class from the specified data stream.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.Metafile.#ctor(System.IO.Stream,System.IntPtr,System.Drawing.RectangleF,System.Drawing.Imaging.MetafileFrameUnit)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Imaging.Metafile'/> class with the specified filename.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.Metafile.#ctor(System.IO.Stream,System.IntPtr,System.Drawing.RectangleF,System.Drawing.Imaging.MetafileFrameUnit,System.Drawing.Imaging.EmfType)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Imaging.Metafile'/> class with the specified filename.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.Metafile.#ctor(System.IO.Stream,System.IntPtr,System.Drawing.Rectangle)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Imaging.Metafile'/> class from the specified data stream.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.Metafile.#ctor(System.IO.Stream,System.IntPtr,System.Drawing.Rectangle,System.Drawing.Imaging.MetafileFrameUnit)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Imaging.Metafile'/> class with the specified filename.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.Metafile.#ctor(System.IO.Stream,System.IntPtr,System.Drawing.Rectangle,System.Drawing.Imaging.MetafileFrameUnit,System.Drawing.Imaging.EmfType)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Imaging.Metafile'/> class with the specified filename.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.Metafile.#ctor(System.IntPtr)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Drawing.Imaging.Metafile"/> class from a native metafile handle.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.Metafile.PlayRecord(System.Drawing.Imaging.EmfPlusRecordType,System.Int32,System.Int32,System.Byte[])">
            <summary>
            Plays an EMF+ file.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.Metafile.#ctor(System.IntPtr,System.Drawing.Imaging.WmfPlaceableFileHeader)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Imaging.Metafile'/> class from the specified handle and
            <see cref='T:System.Drawing.Imaging.WmfPlaceableFileHeader'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.Metafile.#ctor(System.IO.Stream)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Imaging.Metafile'/> class from the specified stream.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.Metafile.#ctor(System.IntPtr,System.Drawing.Imaging.EmfType,System.String)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Imaging.Metafile'/> class from the specified handle to a device context.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.Metafile.#ctor(System.IntPtr,System.Drawing.Rectangle,System.Drawing.Imaging.MetafileFrameUnit,System.Drawing.Imaging.EmfType,System.String)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Imaging.Metafile'/> class from the specified device context, bounded
            by the specified rectangle.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.Metafile.#ctor(System.String,System.IntPtr,System.Drawing.Imaging.EmfType,System.String)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Imaging.Metafile'/> class with the specified filename.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.Metafile.#ctor(System.String,System.IntPtr,System.Drawing.Rectangle,System.Drawing.Imaging.MetafileFrameUnit,System.Drawing.Imaging.EmfType,System.String)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Imaging.Metafile'/> class with the specified filename.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.Metafile.#ctor(System.IO.Stream,System.IntPtr,System.Drawing.Imaging.EmfType,System.String)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Imaging.Metafile'/> class from the specified data stream.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.Metafile.#ctor(System.IO.Stream,System.IntPtr,System.Drawing.RectangleF,System.Drawing.Imaging.MetafileFrameUnit,System.Drawing.Imaging.EmfType,System.String)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Imaging.Metafile'/> class with the specified filename.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.Metafile.#ctor(System.IO.Stream,System.IntPtr,System.Drawing.Rectangle,System.Drawing.Imaging.MetafileFrameUnit,System.Drawing.Imaging.EmfType,System.String)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Imaging.Metafile'/> class with the specified filename.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.Metafile.GetMetafileHeader(System.IntPtr,System.Drawing.Imaging.WmfPlaceableFileHeader)">
            <summary>
            Returns the <see cref='T:System.Drawing.Imaging.MetafileHeader'/> associated with the specified <see cref='T:System.Drawing.Imaging.Metafile'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.Metafile.GetMetafileHeader(System.IntPtr)">
            <summary>
            Returns the <see cref='T:System.Drawing.Imaging.MetafileHeader'/> associated with the specified <see cref='T:System.Drawing.Imaging.Metafile'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.Metafile.GetMetafileHeader(System.String)">
            <summary>
            Returns the <see cref='T:System.Drawing.Imaging.MetafileHeader'/> associated with the specified <see cref='T:System.Drawing.Imaging.Metafile'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.Metafile.GetMetafileHeader(System.IO.Stream)">
            <summary>
            Returns the <see cref='T:System.Drawing.Imaging.MetafileHeader'/> associated with the specified <see cref='T:System.Drawing.Imaging.Metafile'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.Metafile.GetMetafileHeader">
            <summary>
            Returns the <see cref='T:System.Drawing.Imaging.MetafileHeader'/> associated with this <see cref='T:System.Drawing.Imaging.Metafile'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.Metafile.GetHenhmetafile">
            <summary>
            Returns a Windows handle to an enhanced <see cref='T:System.Drawing.Imaging.Metafile'/>.
            </summary>
        </member>
        <member name="T:System.Drawing.Imaging.ColorAdjustType">
            <summary>
            Specifies which GDI+ objects use color adjustment information.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.ColorAdjustType.Default">
            <summary>
            Defines color adjustment information that is used by all GDI+ objects that do not have their own color
            adjustment information.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.ColorAdjustType.Bitmap">
            <summary>
            Defines color adjustment information for <see cref='T:System.Drawing.Bitmap'/>
            objects.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.ColorAdjustType.Brush">
            <summary>
            Defines color adjustment information for <see cref='T:System.Drawing.Brush'/> objects.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.ColorAdjustType.Pen">
            <summary>
            Defines color adjustment information for <see cref='T:System.Drawing.Pen'/> objects.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.ColorAdjustType.Text">
            <summary>
            Defines color adjustment information for text.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.ColorAdjustType.Count">
            <summary>
            Specifies the number of types specified.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.ColorAdjustType.Any">
            <summary>
            Specifies the number of types specified.
            </summary>
        </member>
        <member name="T:System.Drawing.Imaging.ColorChannelFlag">
            <summary>
            Specifies a range of CMYK channels.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.ColorChannelFlag.ColorChannelC">
            <summary>
            Specifies the Cyan color channel.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.ColorChannelFlag.ColorChannelM">
            <summary>
            Specifies the Magenta color channel.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.ColorChannelFlag.ColorChannelY">
            <summary>
            Specifies the Yellow color channel.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.ColorChannelFlag.ColorChannelK">
            <summary>
            Specifies the Black color channel.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.ColorChannelFlag.ColorChannelLast">
            <summary>
            This element specifies to leave the color channel unchanged from the last selected channel.
            </summary>
        </member>
        <member name="T:System.Drawing.Imaging.ColorMap">
            <summary>
            Defines a map for converting colors.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.ColorMap.#ctor">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Imaging.ColorMap'/> class.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.ColorMap.OldColor">
            <summary>
            Specifies the existing <see cref='T:System.Drawing.Color'/> to be converted.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.ColorMap.NewColor">
            <summary>
            Specifies the new <see cref='T:System.Drawing.Color'/> to which to convert.
            </summary>
        </member>
        <member name="T:System.Drawing.Imaging.ColorMapType">
            <summary>
            Specifies the types of color maps.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.ColorMapType.Default">
            <summary>
            A default color map.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.ColorMapType.Brush">
            <summary>
            Specifies a color map for a <see cref='T:System.Drawing.Brush'/>.
            </summary>
        </member>
        <member name="T:System.Drawing.Imaging.ColorMatrixFlag">
            <summary>
            Specifies available options for color-adjusting. GDI+ can adjust color data only, grayscale data only, or both.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.ColorMatrixFlag.Default">
            <summary>
            Both colors and grayscale are color-adjusted.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.ColorMatrixFlag.SkipGrays">
            <summary>
            Grascale values are not color-adjusted.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.ColorMatrixFlag.AltGrays">
            <summary>
            Only grascale values are color-adjusted.
            </summary>
        </member>
        <member name="T:System.Drawing.Imaging.ColorMode">
            <summary>
            Specifies two modes for color component values.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.ColorMode.Argb32Mode">
            <summary>
            Specifies that integer values supplied are 32-bit values.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.ColorMode.Argb64Mode">
            <summary>
            Specifies that integer values supplied are 64-bit values.
            </summary>
        </member>
        <member name="T:System.Drawing.Imaging.EmfPlusRecordType">
            <summary>
            Specifies the methods available in a metafile to read and write graphic commands.
            </summary>
        </member>
        <member name="T:System.Drawing.Imaging.EmfType">
            <summary>
            Specifies the metafile type.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.EmfType.EmfOnly">
            <summary>
            Windows enhanced metafile. Contains GDI commands. Metafiles of this type are referred to as an EMF file.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.EmfType.EmfPlusOnly">
            <summary>
            Windows enhanced metafile plus. Contains GDI+ commands. Metafiles of this type are referred to as an EMF+ file.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.EmfType.EmfPlusDual">
            <summary>
            Dual Windows enhanced metafile. Contains equivalent GDI and GDI+ commands. Metafiles of this type are referred to as an EMF+ file.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.Encoder.ColorSpace">
            <summary>
            An <see cref="T:System.Drawing.Imaging.Encoder" /> object that is initialized with the globally unique identifier for the color space category.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.Encoder.ImageItems">
            <summary>
            An <see cref="T:System.Drawing.Imaging.Encoder" /> object that is initialized with the globally unique identifier for the image items category.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.Encoder.SaveAsCmyk">
            <summary>
            An <see cref="T:System.Drawing.Imaging.Encoder" /> object that is initialized with the globally unique identifier for the save as CMYK category.
            </summary>
        </member>
        <member name="T:System.Drawing.Imaging.EncoderParameterValueType">
            <summary>
            Specifies a EncoderParameter data type.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.EncoderParameterValueType.ValueTypeByte">
            <summary>
            The data is an 8-bit unsigned value.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.EncoderParameterValueType.ValueTypeAscii">
            <summary>
            The data is an 8-bit ASCII value.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.EncoderParameterValueType.ValueTypeShort">
            <summary>
            The data is a 16-bit unsigned value.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.EncoderParameterValueType.ValueTypeLong">
            <summary>
            The data is a 32-bit unsigned value.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.EncoderParameterValueType.ValueTypeRational">
            <summary>
            The data is two long integers, specifying the numerator and the denominator of a rational number, respectively.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.EncoderParameterValueType.ValueTypeLongRange">
            <summary>
            Two longs which specify a range of integer values.
            The first Long specifies the lower end and the second one specifies the higher end.
            All values are inclusive at both ends.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.EncoderParameterValueType.ValueTypeUndefined">
            <summary>
            An 8-bit undefined value that can take any value depending on field definition.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.EncoderParameterValueType.ValueTypeRationalRange">
            <summary>
            Two Rationals. The first Rational specifies the lower end and the second specifies the higher end.
            All values are inclusive at both ends
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.EncoderParameterValueType.ValueTypePointer">
            <summary>
            The parameter is a pointer to a block of custom metadata.
            </summary>
        </member>
        <member name="T:System.Drawing.Imaging.EncoderValue">
            <summary>
            The EncoderValue enum.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.EncoderValue.ColorTypeCMYK">
            <summary>
            Specifies the CMYK color space.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.EncoderValue.ColorTypeYCCK">
            <summary>
            Specifies the YCCK color space.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.EncoderValue.CompressionLZW">
            <summary>
            Specifies the LZW compression method.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.EncoderValue.CompressionCCITT3">
            <summary>
            For a TIFF image, specifies the CCITT3 compression method.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.EncoderValue.CompressionCCITT4">
            <summary>
            For a TIFF image, specifies the CCITT4 compression method.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.EncoderValue.CompressionRle">
            <summary>
            For a TIFF image, specifies the RLE compression method.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.EncoderValue.CompressionNone">
            <summary>
            For a TIFF image, specifies no compression.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.EncoderValue.ScanMethodInterlaced">
            <summary>
            Specifies interlaced mode.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.EncoderValue.ScanMethodNonInterlaced">
            <summary>
            Specifies non-interlaced mode.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.EncoderValue.VersionGif87">
            <summary>
            For a GIF image, specifies version 87.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.EncoderValue.VersionGif89">
            <summary>
            For a GIF images, specifies version 89a.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.EncoderValue.RenderProgressive">
            <summary>
            Specifies progressive mode.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.EncoderValue.RenderNonProgressive">
            <summary>
            Specifies non-progressive mode.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.EncoderValue.TransformRotate90">
            <summary>
            For a JPEG image, specifies lossless 90-degree clockwise rotation.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.EncoderValue.TransformRotate180">
            <summary>
            For a JPEG image, specifies lossless 180-degree rotation.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.EncoderValue.TransformRotate270">
            <summary>
            For a JPEG image, specifies lossless 270-degree clockwise rotation.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.EncoderValue.TransformFlipHorizontal">
            <summary>
            For a JPEG image, specifies a lossless horizontal flip.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.EncoderValue.TransformFlipVertical">
            <summary>
            For a JPEG image, specifies a lossless vertical flip.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.EncoderValue.MultiFrame">
            <summary>
            Specifies multiframe encoding.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.EncoderValue.LastFrame">
            <summary>
            Specifies the last frame of a multi-frame image.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.EncoderValue.Flush">
            <summary>
            Specifies that the encoder object is to be closed.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.EncoderValue.FrameDimensionTime">
            <summary>
            For a GIF image, specifies the time frame dimension.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.EncoderValue.FrameDimensionResolution">
            <summary>
            Specifies the resolution frame dimension.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.EncoderValue.FrameDimensionPage">
            <summary>
            For a TIFF image, specifies the page frame dimension
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.FrameDimension.#ctor(System.Guid)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Imaging.FrameDimension'/> class with the specified GUID.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.FrameDimension.Guid">
            <summary>
            Specifies a global unique identifier (GUID) that represents this <see cref='T:System.Drawing.Imaging.FrameDimension'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.FrameDimension.Time">
            <summary>
            The time dimension.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.FrameDimension.Resolution">
            <summary>
            The resolution dimension.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.FrameDimension.Page">
            <summary>
            The page dimension.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.FrameDimension.Equals(System.Object)">
            <summary>
            Returns a value indicating whether the specified object is an <see cref='T:System.Drawing.Imaging.FrameDimension'/> equivalent to
            this <see cref='T:System.Drawing.Imaging.FrameDimension'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.FrameDimension.ToString">
            <summary>
            Converts this <see cref='T:System.Drawing.Imaging.FrameDimension'/> to a human-readable string.
            </summary>
        </member>
        <member name="T:System.Drawing.Imaging.ImageFlags">
            <summary>
            Specifies the attributes of the pixel data contained in an <see cref='T:System.Drawing.Image'/> object.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.ImageFlags.None">
            <summary>
            There is no format information.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.ImageFlags.Scalable">
            <summary>
            Pixel data is scalable.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.ImageFlags.HasAlpha">
            <summary>
            Pixel data contains alpha information.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.ImageFlags.PartiallyScalable">
            <summary>
            Pixel data is partially scalable, but there are some limitations.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.ImageFlags.ColorSpaceRgb">
            <summary>
            Pixel data uses an RGB color space.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.ImageFlags.ColorSpaceCmyk">
            <summary>
            Pixel data uses a CMYK color space.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.ImageFlags.ColorSpaceGray">
            <summary>
            Pixel data is grayscale.
            </summary>
        </member>
        <member name="T:System.Drawing.Imaging.ImageFormat">
            <summary>
            Specifies the format of the image.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.ImageFormat.#ctor(System.Guid)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Imaging.ImageFormat'/> class with the specified GUID.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.ImageFormat.Guid">
            <summary>
            Specifies a global unique identifier (GUID) that represents this <see cref='T:System.Drawing.Imaging.ImageFormat'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.ImageFormat.MemoryBmp">
            <summary>
            Specifies a memory bitmap image format.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.ImageFormat.Bmp">
            <summary>
            Specifies the bitmap image format.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.ImageFormat.Emf">
            <summary>
            Specifies the enhanced Windows metafile image format.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.ImageFormat.Wmf">
            <summary>
            Specifies the Windows metafile image format.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.ImageFormat.Gif">
            <summary>
            Specifies the GIF image format.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.ImageFormat.Jpeg">
            <summary>
            Specifies the JPEG image format.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.ImageFormat.Png">
            <summary>
            Specifies the W3C PNG image format.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.ImageFormat.Tiff">
            <summary>
            Specifies the Tag Image File Format (TIFF) image format.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.ImageFormat.Exif">
            <summary>
            Specifies the Exchangeable Image Format (EXIF).
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.ImageFormat.Icon">
            <summary>
            Specifies the Windows icon image format.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.ImageFormat.Equals(System.Object)">
            <summary>
            Returns a value indicating whether the specified object is an <see cref='T:System.Drawing.Imaging.ImageFormat'/> equivalent to this
            <see cref='T:System.Drawing.Imaging.ImageFormat'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.ImageFormat.GetHashCode">
            <summary>
            Returns a hash code.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.ImageFormat.ToString">
            <summary>
            Converts this <see cref='T:System.Drawing.Imaging.ImageFormat'/> to a human-readable string.
            </summary>
        </member>
        <member name="T:System.Drawing.Imaging.ImageLockMode">
            <summary>
            Indicates the access mode for an <see cref='T:System.Drawing.Image'/>.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.ImageLockMode.ReadOnly">
            <summary>
            Specifies the image is read-only.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.ImageLockMode.WriteOnly">
            <summary>
            Specifies the image is write-only.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.ImageLockMode.ReadWrite">
            <summary>
            Specifies the image is read-write.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.ImageLockMode.UserInputBuffer">
            <summary>
            Indicates the image resides in a user input buffer, to which the user controls access.
            </summary>
        </member>
        <member name="T:System.Drawing.Imaging.MetafileType">
            <summary>
            Specifies the format of a <see cref='T:System.Drawing.Imaging.Metafile'/>.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.MetafileType.Invalid">
            <summary>
            Specifies an invalid type.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.MetafileType.Wmf">
            <summary>
            Specifies a standard Windows metafile.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.MetafileType.WmfPlaceable">
            <summary>
            Specifies a Windows Placeable metafile.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.MetafileType.Emf">
            <summary>
            Specifies a Windows enhanced metafile.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.MetafileType.EmfPlusOnly">
            <summary>
            Specifies a Windows enhanced metafile plus.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.MetafileType.EmfPlusDual">
            <summary>
            Specifies both enhanced and enhanced plus commands in the same file.
            </summary>
        </member>
        <member name="T:System.Drawing.Imaging.PaletteFlags">
            <summary>
            Specifies the type of color data in the system palette. The data can be color data with alpha, grayscale only,
            or halftone data.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.PaletteFlags.HasAlpha">
            <summary>
            Specifies alpha data.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.PaletteFlags.GrayScale">
            <summary>
            Specifies grayscale data.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.PaletteFlags.Halftone">
            <summary>
            Specifies halftone data.
            </summary>
        </member>
        <member name="T:System.Drawing.Imaging.PixelFormat">
            <summary>
            Specifies the format of the color data for each pixel in the image.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.PixelFormat.Indexed">
            <summary>
            Specifies that pixel data contains color indexed values which means they are an index to colors in the
            system color table, as opposed to individual color values.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.PixelFormat.Gdi">
            <summary>
            Specifies that pixel data contains GDI colors.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.PixelFormat.Alpha">
            <summary>
            Specifies that pixel data contains alpha values that are not pre-multiplied.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.PixelFormat.PAlpha">
            <summary>
            Specifies that pixel format contains pre-multiplied alpha values.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.PixelFormat.Undefined">
            <summary>
            Specifies that pixel format is undefined.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.PixelFormat.DontCare">
            <summary>
            Specifies that pixel format is a don't care.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.PixelFormat.Format1bppIndexed">
            <summary>
            Specifies that pixel format is 1 bit per pixel indexed color. The color table therefore has two colors in it.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.PixelFormat.Format4bppIndexed">
            <summary>
            Specifies that pixel format is 4 bits per pixel indexed color. The color table therefore has 16 colors in it.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.PixelFormat.Format8bppIndexed">
            <summary>
            Specifies that pixel format is 8 bits per pixel indexed color. The color table therefore has 256 colors in it.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.PixelFormat.Format16bppRgb555">
            <summary>
            Specifies that pixel format is 16 bits per pixel. The color information specifies 65536 shades of gray.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.PixelFormat.Format16bppRgb565">
            <summary>
            Specifies that pixel format is 16 bits per pixel. The color information specifies 32768 shades of color of
            which 5 bits are red, 5 bits are green and 5 bits are blue.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.PixelFormat.Format16bppArgb1555">
            <summary>
            Specifies that pixel format is 16 bits per pixel. The color information specifies 32768 shades of color of
            which 5 bits are red, 5 bits are green, 5 bits are blue and 1 bit is alpha.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.PixelFormat.Format24bppRgb">
            <summary>
            Specifies that pixel format is 24 bits per pixel. The color information specifies 16777216 shades of color
            of which 8 bits are red, 8 bits are green and 8 bits are blue.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.PixelFormat.Format32bppRgb">
            <summary>
            Specifies that pixel format is 24 bits per pixel. The color information specifies 16777216 shades of color
            of which 8 bits are red, 8 bits are green and 8 bits are blue.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.PixelFormat.Format32bppArgb">
            <summary>
            Specifies that pixel format is 32 bits per pixel. The color information specifies 16777216 shades of color
            of which 8 bits are red, 8 bits are green and 8 bits are blue. The 8 additional bits are alpha bits.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.PixelFormat.Format32bppPArgb">
            <summary>
            Specifies that pixel format is 32 bits per pixel. The color information specifies 16777216 shades of color
            of which 8 bits are red, 8 bits are green and 8 bits are blue. The 8 additional bits are pre-multiplied alpha bits.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.PixelFormat.Format48bppRgb">
            <summary>
            Specifies that pixel format is 48 bits per pixel. The color information specifies 16777216 shades of color
            of which 8 bits are red, 8 bits are green and 8 bits are blue. The 8 additional bits are alpha bits.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.PixelFormat.Format64bppArgb">
            <summary>
            Specifies pixel format is 64 bits per pixel. The color information specifies 16777216 shades of color of
            which 16 bits are red, 16 bits are green and 16 bits are blue. The 16 additional bits are alpha bits.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.PixelFormat.Format64bppPArgb">
            <summary>
            Specifies that pixel format is 64 bits per pixel. The color information specifies 16777216 shades of color
            of which 16 bits are red, 16 bits are green and 16 bits are blue. The 16 additional bits are pre-multiplied
            alpha bits.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.PixelFormat.Max">
            <summary>
            Specifies that pixel format is 64 bits per pixel. The color information specifies 16777216 shades of color
            of which 16 bits are red, 16 bits are green and 16 bits are blue. The 16 additional bits are alpha bits.
            </summary>
        </member>
        <member name="T:System.Drawing.Imaging.PropertyItem">
            <summary>
            Encapsulates a metadata property to be included in an image file.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.PropertyItem.Id">
            <summary>
            Represents the ID of the property.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.PropertyItem.Len">
            <summary>
            Represents the length of the property.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.PropertyItem.Type">
            <summary>
            Represents the type of the property.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.PropertyItem.Value">
            <summary>
            Contains the property value.
            </summary>
        </member>
        <member name="T:System.Drawing.Imaging.WmfPlaceableFileHeader">
            <summary>
            Defines an Placeable Metafile.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.WmfPlaceableFileHeader.Key">
            <summary>
            Indicates the presence of a placeable metafile header.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.WmfPlaceableFileHeader.Hmf">
            <summary>
            Stores the handle of the metafile in memory.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.WmfPlaceableFileHeader.BboxLeft">
            <summary>
            The x-coordinate of the upper-left corner of the bounding rectangle of the metafile image on the output device.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.WmfPlaceableFileHeader.BboxTop">
            <summary>
            The y-coordinate of the upper-left corner of the bounding rectangle of the metafile image on the output device.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.WmfPlaceableFileHeader.BboxRight">
            <summary>
            The x-coordinate of the lower-right corner of the bounding rectangle of the metafile image on the output device.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.WmfPlaceableFileHeader.BboxBottom">
            <summary>
            The y-coordinate of the lower-right corner of the bounding rectangle of the metafile image on the output device.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.WmfPlaceableFileHeader.Inch">
            <summary>
            Indicates the number of twips per inch.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.WmfPlaceableFileHeader.Reserved">
            <summary>
             Reserved. Do not use.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.WmfPlaceableFileHeader.Checksum">
            <summary>
            Indicates the checksum value for the previous ten WORDs in the header.
            </summary>
        </member>
        <member name="T:System.Drawing.Imaging.BitmapData">
            <summary>
            Specifies the attributes of a bitmap image.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.BitmapData.Width">
            <summary>
            Specifies the pixel width of the <see cref='T:System.Drawing.Bitmap'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.BitmapData.Height">
            <summary>
            Specifies the pixel height of the <see cref='T:System.Drawing.Bitmap'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.BitmapData.Stride">
            <summary>
            Specifies the stride width of the <see cref='T:System.Drawing.Bitmap'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.BitmapData.PixelFormat">
            <summary>
            Specifies the format of the pixel information in this <see cref='T:System.Drawing.Bitmap'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.BitmapData.Scan0">
            <summary>
            Specifies the address of the pixel data.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.BitmapData.Reserved">
            <summary>
            Reserved. Do not use.
            </summary>
        </member>
        <member name="T:System.Drawing.Imaging.EmfPlusFlags">
            EMF+ Flags
        </member>
        <member name="F:System.Drawing.Imaging.MetafileHeaderWmf.type">
            The ENHMETAHEADER structure is defined natively as a union with WmfHeader.
            Extreme care should be taken if changing the layout of the corresponding managed
            structures to minimize the risk of buffer overruns.  The affected managed classes
            are the following: ENHMETAHEADER, MetaHeader, MetafileHeaderWmf, MetafileHeaderEmf.
        </member>
        <member name="F:System.Drawing.Imaging.MetafileHeaderEmf.type">
            The ENHMETAHEADER structure is defined natively as a union with WmfHeader.
            Extreme care should be taken if changing the layout of the corresponding managed
            structures to minimize the risk of buffer overruns.  The affected managed classes
            are the following: ENHMETAHEADER, MetaHeader, MetafileHeaderWmf, MetafileHeaderEmf.
        </member>
        <member name="T:System.Drawing.Imaging.ColorMatrix">
            <summary>
            Defines a 5 x 5 matrix that contains the homogeneous coordinates for the RGBA space.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.ColorMatrix.#ctor">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Imaging.ColorMatrix'/> class.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.ColorMatrix.Matrix00">
            <summary>
            Represents the element at the 0th row and 0th column of this <see cref='T:System.Drawing.Imaging.ColorMatrix'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.ColorMatrix.Matrix01">
            <summary>
            Represents the element at the 0th row and 1st column of this <see cref='T:System.Drawing.Imaging.ColorMatrix'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.ColorMatrix.Matrix02">
            <summary>
            Represents the element at the 0th row and 2nd column of this <see cref='T:System.Drawing.Imaging.ColorMatrix'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.ColorMatrix.Matrix03">
            <summary>
            Represents the element at the 0th row and 3rd column of this <see cref='T:System.Drawing.Imaging.ColorMatrix'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.ColorMatrix.Matrix04">
            <summary>
            Represents the element at the 0th row and 4th column of this <see cref='T:System.Drawing.Imaging.ColorMatrix'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.ColorMatrix.Matrix10">
            <summary>
            Represents the element at the 1st row and 0th column of this <see cref='T:System.Drawing.Imaging.ColorMatrix'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.ColorMatrix.Matrix11">
            <summary>
            Represents the element at the 1st row and 1st column of this <see cref='T:System.Drawing.Imaging.ColorMatrix'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.ColorMatrix.Matrix12">
            <summary>
            Represents the element at the 1st row and 2nd column of this <see cref='T:System.Drawing.Imaging.ColorMatrix'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.ColorMatrix.Matrix13">
            <summary>
            Represents the element at the 1st row and 3rd column of this <see cref='T:System.Drawing.Imaging.ColorMatrix'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.ColorMatrix.Matrix14">
            <summary>
            Represents the element at the 1st row and 4th column of this <see cref='T:System.Drawing.Imaging.ColorMatrix'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.ColorMatrix.Matrix20">
            <summary>
            Represents the element at the 2nd row and 0th column of this <see cref='T:System.Drawing.Imaging.ColorMatrix'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.ColorMatrix.Matrix21">
            <summary>
            Represents the element at the 2nd row and 1st column of this <see cref='T:System.Drawing.Imaging.ColorMatrix'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.ColorMatrix.Matrix22">
            <summary>
            Represents the element at the 2nd row and 2nd column of this <see cref='T:System.Drawing.Imaging.ColorMatrix'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.ColorMatrix.Matrix23">
            <summary>
            Represents the element at the 2nd row and 3rd column of this <see cref='T:System.Drawing.Imaging.ColorMatrix'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.ColorMatrix.Matrix24">
            <summary>
            Represents the element at the 2nd row and 4th column of this <see cref='T:System.Drawing.Imaging.ColorMatrix'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.ColorMatrix.Matrix30">
            <summary>
            Represents the element at the 3rd row and 0th column of this <see cref='T:System.Drawing.Imaging.ColorMatrix'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.ColorMatrix.Matrix31">
            <summary>
            Represents the element at the 3rd row and 1st column of this <see cref='T:System.Drawing.Imaging.ColorMatrix'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.ColorMatrix.Matrix32">
            <summary>
            Represents the element at the 3rd row and 2nd column of this <see cref='T:System.Drawing.Imaging.ColorMatrix'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.ColorMatrix.Matrix33">
            <summary>
            Represents the element at the 3rd row and 3rd column of this <see cref='T:System.Drawing.Imaging.ColorMatrix'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.ColorMatrix.Matrix34">
            <summary>
            Represents the element at the 3rd row and 4th column of this <see cref='T:System.Drawing.Imaging.ColorMatrix'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.ColorMatrix.Matrix40">
            <summary>
            Represents the element at the 4th row and 0th column of this <see cref='T:System.Drawing.Imaging.ColorMatrix'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.ColorMatrix.Matrix41">
            <summary>
            Represents the element at the 4th row and 1st column of this <see cref='T:System.Drawing.Imaging.ColorMatrix'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.ColorMatrix.Matrix42">
            <summary>
            Represents the element at the 4th row and 2nd column of this <see cref='T:System.Drawing.Imaging.ColorMatrix'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.ColorMatrix.Matrix43">
            <summary>
            Represents the element at the 4th row and 3rd column of this <see cref='T:System.Drawing.Imaging.ColorMatrix'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.ColorMatrix.Matrix44">
            <summary>
            Represents the element at the 4th row and 4th column of this <see cref='T:System.Drawing.Imaging.ColorMatrix'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.ColorMatrix.#ctor(System.Single[][])">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Imaging.ColorMatrix'/> class with the elements in the specified matrix.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.ColorMatrix.Item(System.Int32,System.Int32)">
            <summary>
            Gets or sets the value of the specified element of this <see cref='T:System.Drawing.Imaging.ColorMatrix'/>.
            </summary>
        </member>
        <member name="T:System.Drawing.Imaging.ColorPalette">
            <summary>
            Defines an array of colors that make up a color palette.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.ColorPalette.Flags">
            <summary>
            Specifies how to interpret the color information in the array of colors.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.ColorPalette.Entries">
            <summary>
            Specifies an array of <see cref='T:System.Drawing.Color'/> objects.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.EncoderParameter.Encoder">
            <summary>
            Gets/Sets the Encoder for the EncoderPameter.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.EncoderParameter.Type">
            <summary>
            Gets the EncoderParameterValueType object from the EncoderParameter.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.EncoderParameter.ValueType">
            <summary>
            Gets the EncoderParameterValueType object from the EncoderParameter.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.EncoderParameter.NumberOfValues">
            <summary>
            Gets the NumberOfValues from the EncoderParameter.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.EncoderParameters.ConvertToMemory">
             <summary>
             Copy the EncoderParameters data into a chunk of memory to be consumed by native GDI+ code.
            
             We need to marshal the EncoderParameters info from/to native GDI+ ourselves since the definition of the managed/unmanaged classes
             are different and the native class is a bit weird. The native EncoderParameters class is defined in GDI+ as follows:
            
             class EncoderParameters {
                 UINT Count;                      // Number of parameters in this structure
                 EncoderParameter Parameter[1];   // Parameter values
             };
            
             We don't have the 'Count' field since the managed array contains it. In order for this structure to work with more than one
             EncoderParameter we need to preallocate memory for the extra n-1 elements, something like this:
            
             EncoderParameters* pEncoderParameters = (EncoderParameters*) malloc(sizeof(EncoderParameters) + (n-1) * sizeof(EncoderParameter));
            
             Also, in 64-bit platforms, 'Count' is aligned in 8 bytes (4 extra padding bytes) so we use IntPtr instead of Int32 to account for
             that.
             </summary>
        </member>
        <member name="M:System.Drawing.Imaging.EncoderParameters.ConvertFromMemory(System.IntPtr)">
            <summary>
            Copy the native GDI+ EncoderParameters data from a chunk of memory into a managed EncoderParameters object.
            See ConvertToMemory for more info.
            </summary>
        </member>
        <member name="T:System.Drawing.Imaging.ImageAttributes">
            <summary>
            Contains information about how image colors are manipulated during rendering.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.ImageAttributes.#ctor">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Imaging.ImageAttributes'/> class.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.ImageAttributes.Dispose">
            <summary>
            Cleans up Windows resources for this <see cref='T:System.Drawing.Imaging.ImageAttributes'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.ImageAttributes.Finalize">
            <summary>
            Cleans up Windows resources for this <see cref='T:System.Drawing.Imaging.ImageAttributes'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.ImageAttributes.Clone">
            <summary>
            Creates an exact copy of this <see cref='T:System.Drawing.Imaging.ImageAttributes'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.ImageAttributes.SetColorMatrix(System.Drawing.Imaging.ColorMatrix)">
            <summary>
            Sets the 5 X 5 color adjust matrix to the specified <see cref='T:System.Drawing.Drawing2D.Matrix'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.ImageAttributes.SetColorMatrix(System.Drawing.Imaging.ColorMatrix,System.Drawing.Imaging.ColorMatrixFlag)">
            <summary>
            Sets the 5 X 5 color adjust matrix to the specified 'Matrix' with the specified 'ColorMatrixFlags'.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.ImageAttributes.SetColorMatrix(System.Drawing.Imaging.ColorMatrix,System.Drawing.Imaging.ColorMatrixFlag,System.Drawing.Imaging.ColorAdjustType)">
            <summary>
            Sets the 5 X 5 color adjust matrix to the specified 'Matrix' with the  specified 'ColorMatrixFlags'.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.ImageAttributes.ClearColorMatrix">
            <summary>
            Clears the color adjust matrix to all zeroes.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.ImageAttributes.ClearColorMatrix(System.Drawing.Imaging.ColorAdjustType)">
            <summary>
            Clears the color adjust matrix.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.ImageAttributes.SetColorMatrices(System.Drawing.Imaging.ColorMatrix,System.Drawing.Imaging.ColorMatrix)">
            <summary>
            Sets a color adjust matrix for image colors and a separate gray scale adjust matrix for gray scale values.
            </summary>
        </member>
        <member name="T:System.Drawing.Imaging.MetafileFrameUnit">
            <summary>
            Specifies the unit of measurement for the rectangle used to size and position a metafile.
            This is specified during the creation of the <see cref='T:System.Drawing.Imaging.Metafile'/>.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.MetafileFrameUnit.Pixel">
            <summary>
            Specifies a pixel as the unit of measure.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.MetafileFrameUnit.Point">
            <summary>
            Specifies a printer's point as the unit of measure.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.MetafileFrameUnit.Inch">
            <summary>
            Specifies an inch as the unit of measure.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.MetafileFrameUnit.Document">
            <summary>
            Specifies 1/300 of an inch as the unit of measure.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.MetafileFrameUnit.Millimeter">
            <summary>
            Specifies a millimeter as the unit of measure.
            </summary>
        </member>
        <member name="F:System.Drawing.Imaging.MetafileFrameUnit.GdiCompatible">
            <summary>
            Specifies .01 millimeter as the unit of measure. Provided for compatibility with GDI.
            </summary>
        </member>
        <member name="T:System.Drawing.Imaging.MetafileHeader">
            <summary>
            Contains attributes of an associated <see cref='T:System.Drawing.Imaging.Metafile'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.MetafileHeader.Type">
            <summary>
            Gets the type of the associated <see cref='T:System.Drawing.Imaging.Metafile'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.MetafileHeader.MetafileSize">
            <summary>
            Gets the size, in bytes, of the associated <see cref='T:System.Drawing.Imaging.Metafile'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.MetafileHeader.Version">
            <summary>
            Gets the version number of the associated <see cref='T:System.Drawing.Imaging.Metafile'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.MetafileHeader.DpiX">
            <summary>
            Gets the horizontal resolution, in dots-per-inch, of the associated <see cref='T:System.Drawing.Imaging.Metafile'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.MetafileHeader.DpiY">
            <summary>
            Gets the vertical resolution, in dots-per-inch, of the associated <see cref='T:System.Drawing.Imaging.Metafile'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.MetafileHeader.Bounds">
            <summary>
            Gets a <see cref='T:System.Drawing.Rectangle'/> that bounds the associated <see cref='T:System.Drawing.Imaging.Metafile'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.MetafileHeader.IsWmf">
            <summary>
            Returns a value indicating whether the associated <see cref='T:System.Drawing.Imaging.Metafile'/> is in the Windows metafile format.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.MetafileHeader.IsWmfPlaceable">
            <summary>
            Returns a value indicating whether the associated <see cref='T:System.Drawing.Imaging.Metafile'/> is in the Windows Placeable metafile format.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.MetafileHeader.IsEmf">
            <summary>
            Returns a value indicating whether the associated <see cref='T:System.Drawing.Imaging.Metafile'/> is in the Windows enhanced metafile format.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.MetafileHeader.IsEmfOrEmfPlus">
            <summary>
            Returns a value indicating whether the associated <see cref='T:System.Drawing.Imaging.Metafile'/> is in the Windows enhanced
            metafile format or the Windows enhanced metafile plus.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.MetafileHeader.IsEmfPlus">
            <summary>
            Returns a value indicating whether the associated <see cref='T:System.Drawing.Imaging.Metafile'/> is in the Windows enhanced
            metafile plus format.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.MetafileHeader.IsEmfPlusDual">
            <summary>
            Returns a value indicating whether the associated <see cref='T:System.Drawing.Imaging.Metafile'/> is in the Dual enhanced metafile
            format. This format supports both the enhanced and the enhanced plus format.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.MetafileHeader.IsEmfPlusOnly">
            <summary>
            Returns a value indicating whether the associated <see cref='T:System.Drawing.Imaging.Metafile'/> supports only the Windows
            enhanced metafile plus format.
            </summary>
        </member>
        <member name="M:System.Drawing.Imaging.MetafileHeader.IsDisplay">
            <summary>
            Returns a value indicating whether the associated <see cref='T:System.Drawing.Imaging.Metafile'/> is device-dependent.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.MetafileHeader.WmfHeader">
            <summary>
            Gets the WMF header file for the associated <see cref='T:System.Drawing.Imaging.Metafile'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.MetafileHeader.EmfPlusHeaderSize">
            <summary>
            Gets the size, in bytes, of the enhanced metafile plus header file.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.MetafileHeader.LogicalDpiX">
            <summary>
            Gets the logical horizontal resolution, in dots-per-inch, of the associated <see cref='T:System.Drawing.Imaging.Metafile'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.MetafileHeader.LogicalDpiY">
            <summary>
            Gets the logical vertical resolution, in dots-per-inch, of the associated <see cref='T:System.Drawing.Imaging.Metafile'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.MetaHeader.Type">
            <summary>
            Represents the type of the associated <see cref='T:System.Drawing.Imaging.Metafile'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.MetaHeader.HeaderSize">
            <summary>
            Represents the sizi, in bytes, of the header file.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.MetaHeader.Version">
            <summary>
            Represents the version number of the header format.
            </summary>
        </member>
        <member name="P:System.Drawing.Imaging.MetaHeader.Size">
            <summary>
            Represents the size, in bytes, of the associated <see cref='T:System.Drawing.Imaging.Metafile'/>.
            </summary>
        </member>
        <member name="T:System.Drawing.Printing.PrinterUnit">
            <summary>
            Specifies several of the units of measure Microsoft Win32 uses for printing.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PrinterUnit.Display">
            <summary>
            The default unit (0.01 in.).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PrinterUnit.ThousandthsOfAnInch">
            <summary>
            One thousandth of an inch (0.001 in.).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PrinterUnit.HundredthsOfAMillimeter">
            <summary>
            One hundredth of a millimeter (0.01 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PrinterUnit.TenthsOfAMillimeter">
            <summary>
            One tenth of a millimeter (0.1 mm).
            </summary>
        </member>
        <member name="T:System.Drawing.Printing.PreviewPageInfo">
            <summary>
            Specifies print preview information for a single page. This class cannot be inherited.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PreviewPageInfo.#ctor(System.Drawing.Image,System.Drawing.Size)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Printing.PreviewPageInfo'/> class.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PreviewPageInfo.Image">
            <summary>
            Gets the image of the printed page.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PreviewPageInfo.PhysicalSize">
            <summary>
            Gets the size of the printed page, in hundredths of an inch.
            </summary>
        </member>
        <member name="T:System.Drawing.Printing.PreviewPrintController">
            <summary>
            A PrintController which "prints" to a series of images.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PreviewPrintController.OnStartPrint(System.Drawing.Printing.PrintDocument,System.Drawing.Printing.PrintEventArgs)">
            <summary>
            Implements StartPrint for generating print preview information.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PreviewPrintController.OnStartPage(System.Drawing.Printing.PrintDocument,System.Drawing.Printing.PrintPageEventArgs)">
            <summary>
            Implements StartEnd for generating print preview information.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PreviewPrintController.OnEndPage(System.Drawing.Printing.PrintDocument,System.Drawing.Printing.PrintPageEventArgs)">
            <summary>
            Implements EndPage for generating print preview information.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PreviewPrintController.OnEndPrint(System.Drawing.Printing.PrintDocument,System.Drawing.Printing.PrintEventArgs)">
            <summary>
            Implements EndPrint for generating print preview information.
            </summary>
        </member>
        <member name="T:System.Drawing.Printing.PrintEventHandler">
            <summary>
            Represents the method that will handle the <see cref='E:System.Drawing.Printing.PrintDocument.BeginPrint'/>,
            <see cref='E:System.Drawing.Printing.PrintDocument.EndPrint'/>, or <see cref='E:System.Drawing.Printing.PrintDocument.QueryPageSettings'/>
            event of a <see cref='T:System.Drawing.Printing.PrintDocument'/>.
            </summary>
        </member>
        <member name="T:System.Drawing.Printing.PrintAction">
            <summary>
            Specifies the type of action for the <see cref='T:System.Drawing.Printing.PrintEventArgs'/>.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PrintAction.PrintToFile">
            <summary>
            Printing to a file.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PrintAction.PrintToPreview">
            <summary>
            Printing to a preview.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PrintAction.PrintToPrinter">
            <summary>
            Printing to a printer.
            </summary>
        </member>
        <member name="T:System.Drawing.Printing.PrintController">
            <summary>
            Controls how a document is printed.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PrintController.OnStartPage(System.Drawing.Printing.PrintDocument,System.Drawing.Printing.PrintPageEventArgs)">
            <summary>
            When overridden in a derived class, begins the control sequence of when and how to print a page in a document.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PrintController.OnEndPage(System.Drawing.Printing.PrintDocument,System.Drawing.Printing.PrintPageEventArgs)">
            <summary>
            When overridden in a derived class, completes the control sequence of when and how to print a page in a document.
            </summary>
        </member>
        <member name="T:System.Drawing.Printing.PrintController.SafeDeviceModeHandle">
            <summary>
            Represents a SafeHandle for a Printer's Device Mode struct handle (DEVMODE)
            </summary>
            <remarks>
            DEVMODEs are pretty expensive, so we cache one here and share it
            with the Standard and Preview print controllers.
            </remarks>
        </member>
        <member name="M:System.Drawing.Printing.PrintController.SafeDeviceModeHandle.ReleaseHandle">
            <summary>
            Specifies how to free the handle.
            The boolean returned should be true for success and false if the runtime
            should fire a SafeHandleCriticalFailure MDA (CustomerDebugProbe) if that
            MDA is enabled.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PrintController.Print(System.Drawing.Printing.PrintDocument)">
            <remarks>
            If you have nested PrintControllers, this method won't get called on the inner one.
            Add initialization code to StartPrint or StartPage instead.
            </remarks>
        </member>
        <member name="M:System.Drawing.Printing.PrintController.PrintLoop(System.Drawing.Printing.PrintDocument)">
            <summary>
            Returns true if print was aborted.
            </summary>
            <remarks>
            If you have nested PrintControllers, this method won't get called on the inner one
            Add initialization code to StartPrint or StartPage instead.
            </remarks>
        </member>
        <member name="M:System.Drawing.Printing.PrintController.OnStartPrint(System.Drawing.Printing.PrintDocument,System.Drawing.Printing.PrintEventArgs)">
            <summary>
            When overridden in a derived class, begins the control sequence of when and how to print a document.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PrintController.OnEndPrint(System.Drawing.Printing.PrintDocument,System.Drawing.Printing.PrintEventArgs)">
            <summary>
            When overridden in a derived class, completes the control sequence of when and how to print a document.
            </summary>
        </member>
        <member name="T:System.Drawing.Printing.PrintPageEventHandler">
            <summary>
            Represents the method that will handle the <see cref='E:System.Drawing.Printing.PrintDocument.PrintPage'/> event of a <see cref='T:System.Drawing.Printing.PrintDocument'/>.
            </summary>
        </member>
        <member name="T:System.Drawing.Printing.QueryPageSettingsEventArgs">
            <summary>
            Provides data for the <see cref='E:System.Drawing.Printing.PrintDocument.QueryPageSettings'/> event.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.QueryPageSettingsEventArgs.PageSettingsChanged">
            <summary>
            It's too expensive to compare 2 instances of PageSettings class, as the getters
            are accessing the printer spooler, thus we track any explicit invocations of the setters or getters on this class,
            and this field tracks if PageSettings property was accessed. It will return a false
            positive when the user is reading property values, but we'll take a perf hit in this case assuming this event is not
            used often.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.QueryPageSettingsEventArgs.#ctor(System.Drawing.Printing.PageSettings)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Printing.QueryPageSettingsEventArgs'/> class.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.QueryPageSettingsEventArgs.PageSettings">
            <summary>
            Gets or sets the page settings for the page to be printed.
            </summary>
        </member>
        <member name="T:System.Drawing.Printing.QueryPageSettingsEventHandler">
            <summary>
            Represents the method that will handle the <see cref='E:System.Drawing.Printing.PrintDocument.QueryPageSettings'/> event of a
            <see cref='T:System.Drawing.Printing.PrintDocument'/>.
            </summary>
        </member>
        <member name="T:System.Drawing.Printing.Duplex">
            <summary>
            Specifies the printer's duplex setting.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.Duplex.Default">
            <summary>
            The printer's default duplex setting.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.Duplex.Simplex">
            <summary>
            Single-sided printing.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.Duplex.Horizontal">
            <summary>
            Double-sided, horizontal printing.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.Duplex.Vertical">
            <summary>
            Double-sided, vertical printing.
            </summary>
        </member>
        <member name="T:System.Drawing.Printing.InvalidPrinterException">
            <summary>
            Represents the exception that is thrown when trying to access a printer using invalid printer settings.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.InvalidPrinterException.#ctor(System.Drawing.Printing.PrinterSettings)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Printing.InvalidPrinterException'/> class.
            </summary>
        </member>
        <member name="T:System.Drawing.Printing.Margins">
            <summary>
            Specifies the margins of a printed page.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.Margins.#ctor">
            <summary>
            Initializes a new instance of a the <see cref='T:System.Drawing.Printing.Margins'/> class with one-inch margins.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.Margins.#ctor(System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Initializes a new instance of a the <see cref='T:System.Drawing.Printing.Margins'/> class with the specified left, right, top, and bottom margins.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.Margins.Left">
            <summary>
            Gets or sets the left margin, in hundredths of an inch.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.Margins.Right">
            <summary>
            Gets or sets the right margin, in hundredths of an inch.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.Margins.Top">
            <summary>
            Gets or sets the top margin, in hundredths of an inch.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.Margins.Bottom">
            <summary>
            Gets or sets the bottom margin, in hundredths of an inch.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.Margins.DoubleLeft">
            <summary>
            Gets or sets the left margin with double value, in hundredths of an inch.
            When use the setter, the ranger of setting double value should between
            0 to Int.MaxValue;
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.Margins.DoubleRight">
            <summary>
            Gets or sets the right margin with double value, in hundredths of an inch.
            When use the setter, the ranger of setting double value should between
            0 to Int.MaxValue;
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.Margins.DoubleTop">
            <summary>
            Gets or sets the top margin with double value, in hundredths of an inch.
            When use the setter, the ranger of setting double value should between
            0 to Int.MaxValue;
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.Margins.DoubleBottom">
            <summary>
            Gets or sets the bottom margin with double value, in hundredths of an inch.
            When use the setter, the ranger of setting double value should between
            0 to Int.MaxValue;
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.Margins.Clone">
            <summary>
            Retrieves a duplicate of this object, member by member.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.Margins.Equals(System.Object)">
            <summary>
            Compares this <see cref='T:System.Drawing.Printing.Margins'/> to a specified <see cref='T:System.Drawing.Printing.Margins'/> to see whether they
            are equal.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.Margins.GetHashCode">
            <summary>
            Calculates and retrieves a hash code based on the left, right, top, and bottom margins.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.Margins.op_Equality(System.Drawing.Printing.Margins,System.Drawing.Printing.Margins)">
            <summary>
            Tests whether two <see cref='T:System.Drawing.Printing.Margins'/> objects are identical.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.Margins.op_Inequality(System.Drawing.Printing.Margins,System.Drawing.Printing.Margins)">
            <summary>
            Tests whether two <see cref='T:System.Drawing.Printing.Margins'/> objects are different.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.Margins.ToString">
            <summary>
            Provides some interesting information for the Margins in String form.
            </summary>
        </member>
        <member name="T:System.Drawing.Printing.MarginsConverter">
            <summary>
            Provides a type converter to convert <see cref='T:System.Drawing.Printing.Margins'/> to and from various other representations, such as a string.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.MarginsConverter.CanConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Type)">
            <summary>
            Determines if a converter can convert an object of the given source
            type to the native type of the converter.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.MarginsConverter.CanConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Type)">
            <summary>
            Gets a value indicating whether this converter can
            convert an object to the given destination type using the context.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.MarginsConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)">
            <summary>
            Converts the given object to the converter's native type.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.MarginsConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)">
            <summary>
            Converts the given object to another type. The most common types to convert
            are to and from a string object. The default implementation will make a call
            to ToString on the object if the object is valid and if the destination
            type is string. If this cannot convert to the desitnation type, this will
            throw a NotSupportedException.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.MarginsConverter.GetCreateInstanceSupported(System.ComponentModel.ITypeDescriptorContext)">
            <summary>
            Determines if changing a value on this object should require a call to
            CreateInstance to create a new value.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.MarginsConverter.CreateInstance(System.ComponentModel.ITypeDescriptorContext,System.Collections.IDictionary)">
            <summary>
            Creates an instance of this type given a set of property values
            for the object.  This is useful for objects that are immutable, but still
            want to provide changable properties.
            </summary>
        </member>
        <member name="T:System.Drawing.Printing.PaperKind">
            <summary>
            Specifies the standard paper sizes.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.Custom">
            <summary>
            The paper size is defined by the user.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.Letter">
            <summary>
            Letter paper (8.5 in. by 11 in.).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.Legal">
            <summary>
            Legal paper (8.5 in. by 14 in.).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.A4">
            <summary>
            A4 paper (210 mm by 297 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.CSheet">
            <summary>
            C paper (17 in. by 22 in.).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.DSheet">
            <summary>
            D paper (22 in. by 34 in.).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.ESheet">
            <summary>
            E paper (34 in. by 44 in.).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.LetterSmall">
            <summary>
            Letter small paper (8.5 in. by 11 in.).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.Tabloid">
            <summary>
            Tabloid paper (11 in. by 17 in.).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.Ledger">
            <summary>
            Ledger paper (17 in. by 11 in.).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.Statement">
            <summary>
            Statement paper (5.5 in. by 8.5 in.).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.Executive">
            <summary>
            Executive paper (7.25 in. by 10.5 in.).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.A3">
            <summary>
            A3 paper (297 mm by 420 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.A4Small">
            <summary>
            A4 small paper (210 mm by 297 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.A5">
            <summary>
            A5 paper (148 mm by 210 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.B4">
            <summary>
            B4 paper (250 mm by 353 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.B5">
            <summary>
            B5 paper (176 mm by 250 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.Folio">
            <summary>
            Folio paper (8.5 in. by 13 in.).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.Quarto">
            <summary>
            Quarto paper (215 mm by 275 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.Standard10x14">
            <summary>
            10-by-14-inch paper.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.Standard11x17">
            <summary>
            11-by-17-inch paper.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.Note">
            <summary>
            Note paper (8.5 in. by 11 in.).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.Number9Envelope">
            <summary>
            #9 envelope (3.875 in. by 8.875 in.).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.Number10Envelope">
            <summary>
            #10 envelope (4.125 in. by 9.5 in.).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.Number11Envelope">
            <summary>
            #11 envelope (4.5 in. by 10.375 in.).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.Number12Envelope">
            <summary>
            #12 envelope (4.75 in. by 11 in.).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.Number14Envelope">
            <summary>
            #14 envelope (5 in. by 11.5 in.).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.DLEnvelope">
            <summary>
            DL envelope (110 mm by 220 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.C5Envelope">
            <summary>
            C5 envelope (162 mm by 229 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.C3Envelope">
            <summary>
            C3 envelope (324 mm by 458 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.C4Envelope">
            <summary>
            C4 envelope (229 mm by 324 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.C6Envelope">
            <summary>
            C6 envelope (114 mm by 162 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.C65Envelope">
            <summary>
            C65 envelope (114 mm by 229 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.B4Envelope">
            <summary>
            B4 envelope (250 mm by 353 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.B5Envelope">
            <summary>
            B5 envelope (176 mm by 250 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.B6Envelope">
            <summary>
            B6 envelope (176 mm by 125 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.ItalyEnvelope">
            <summary>
            Italy envelope (110 mm by 230 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.MonarchEnvelope">
            <summary>
            Monarch envelope (3.875 in. by 7.5 in.).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.PersonalEnvelope">
            <summary>
            6 3/4 envelope (3.625 in. by 6.5 in.).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.USStandardFanfold">
            <summary>
            US standard fanfold (14.875 in. by 11 in.).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.GermanStandardFanfold">
            <summary>
            German standard fanfold (8.5 in. by 12 in.).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.GermanLegalFanfold">
            <summary>
            German legal fanfold (8.5 in. by 13 in.).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.IsoB4">
            <summary>
            ISO B4 (250 mm by 353 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.JapanesePostcard">
            <summary>
            Japanese postcard (100 mm by 148 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.Standard9x11">
            <summary>
            9-by-11-inch paper.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.Standard10x11">
            <summary>
            10-by-11-inch paper.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.Standard15x11">
            <summary>
            15-by-11-inch paper.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.InviteEnvelope">
            <summary>
            Invite envelope (220 mm by 220 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.LetterExtra">
            <summary>
            Letter extra paper (9.275 in. by 12 in.).
            This value is specific to the PostScript driver and is used only by Linotronic printers in order to conserve paper.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.LegalExtra">
            <summary>
            Legal extra paper (9.275 in. by 15 in.).
            This value is specific to the PostScript driver and is used only by Linotronic printers in order to conserve paper.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.TabloidExtra">
            <summary>
            Tabloid extra paper (11.69 in. by 18 in.).
            This value is specific to the PostScript driver and is used only by Linotronic printers in order to conserve paper.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.A4Extra">
            <summary>
            A4 extra paper (236 mm by 322 mm).
            This value is specific to the PostScript driver and is used only by Linotronic printers in order to conserve paper.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.LetterTransverse">
            <summary>
            Letter transverse paper (8.275 in. by 11 in.).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.A4Transverse">
            <summary>
            A4 transverse paper (210 mm by 297 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.LetterExtraTransverse">
            <summary>
            Letter extra transverse paper (9.275 in. by 12 in.).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.APlus">
            <summary>
            SuperA/SuperA/A4 paper (227 mm by 356 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.BPlus">
            <summary>
            SuperB/SuperB/A3 paper (305 mm by 487 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.LetterPlus">
            <summary>
            Letter plus paper (8.5 in. by 12.69 in.).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.A4Plus">
            <summary>
            A4 plus paper (210 mm by 330 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.A5Transverse">
            <summary>
            A5 transverse paper (148 mm by 210 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.B5Transverse">
            <summary>
            JIS B5 transverse paper (182 mm by 257 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.A3Extra">
            <summary>
            A3 extra paper (322 mm by 445 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.A5Extra">
            <summary>
            A5 extra paper (174 mm by 235 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.B5Extra">
            <summary>
            ISO B5 extra paper (201 mm by 276 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.A2">
            <summary>
            A2 paper (420 mm by 594 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.A3Transverse">
            <summary>
            A3 transverse paper (297 mm by 420 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.A3ExtraTransverse">
            <summary>
            A3 extra transverse paper (322 mm by 445 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.JapaneseDoublePostcard">
            <summary>
            Japanese double postcard (200 mm by 148mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.A6">
            <summary>
            A6 paper (105 mm by 148 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.JapaneseEnvelopeKakuNumber2">
            <summary>
            Japanese Kaku #2 envelope.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.JapaneseEnvelopeKakuNumber3">
            <summary>
            Japanese Kaku #3 envelope.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.JapaneseEnvelopeChouNumber3">
            <summary>
            Japanese Chou #3 envelope.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.JapaneseEnvelopeChouNumber4">
            <summary>
            Japanese Chou #4 envelope.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.LetterRotated">
            <summary>
            Letter rotated paper (11 in. by 8.5 in.).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.A3Rotated">
            <summary>
            A3 rotated paper (420mm by 297 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.A4Rotated">
            <summary>
            A4 rotated paper (297 mm by 210 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.A5Rotated">
            <summary>
            A5 rotated paper (210 mm by 148 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.B4JisRotated">
            <summary>
            JIS B4 rotated paper (364 mm by 257 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.B5JisRotated">
            <summary>
            JIS B5 rotated paper (257 mm by 182 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.JapanesePostcardRotated">
            <summary>
            Japanese rotated postcard (148 mm by 100 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.JapaneseDoublePostcardRotated">
            <summary>
            Japanese rotated double postcard (148 mm by 200 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.A6Rotated">
            <summary>
            A6 rotated paper (148 mm by 105 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.JapaneseEnvelopeKakuNumber2Rotated">
            <summary>
            Japanese rotated Kaku #2 envelope.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.JapaneseEnvelopeKakuNumber3Rotated">
            <summary>
            Japanese rotated Kaku #3 envelope.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.JapaneseEnvelopeChouNumber3Rotated">
            <summary>
            Japanese rotated Chou #3 envelope.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.JapaneseEnvelopeChouNumber4Rotated">
            <summary>
            Japanese rotated Chou #4 envelope.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.B6Jis">
            <summary>
            JIS B6 paper (128 mm by 182 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.B6JisRotated">
            <summary>
            JIS B6 rotated paper (182 mm by 128 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.Standard12x11">
            <summary>
            12-by-11-inch paper.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.JapaneseEnvelopeYouNumber4">
            <summary>
            Japanese You #4 envelope.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.JapaneseEnvelopeYouNumber4Rotated">
            <summary>
            Japanese You #4 rotated envelope.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.Prc16K">
            <summary>
            PRC 16K paper (146 mm by 215 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.Prc32K">
            <summary>
            PRC 32K paper (97 mm by 151 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.Prc32KBig">
            <summary>
            PRC 32K big paper (97 mm by 151 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.PrcEnvelopeNumber1">
            <summary>
            PRC #1 envelope (102 mm by 165 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.PrcEnvelopeNumber2">
            <summary>
            PRC #2 envelope (102 mm by 176 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.PrcEnvelopeNumber3">
            <summary>
            PRC #3 envelope (125 mm by 176 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.PrcEnvelopeNumber4">
            <summary>
            PRC #4 envelope (110 mm by 208 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.PrcEnvelopeNumber5">
            <summary>
            PRC #5 envelope (110 mm by 220 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.PrcEnvelopeNumber6">
            <summary>
            PRC #6 envelope (120 mm by 230 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.PrcEnvelopeNumber7">
            <summary>
            PRC #7 envelope (160 mm by 230 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.PrcEnvelopeNumber8">
            <summary>
            PRC #8 envelope (120 mm by 309 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.PrcEnvelopeNumber9">
            <summary>
            PRC #9 envelope (229 mm by 324 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.PrcEnvelopeNumber10">
            <summary>
            PRC #10 envelope (324 mm by 458 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.Prc16KRotated">
            <summary>
            PRC 16K rotated paper (146 mm by 215 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.Prc32KRotated">
            <summary>
            PRC 32K rotated paper (97 mm by 151 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.Prc32KBigRotated">
            <summary>
            PRC 32K big rotated paper (97 mm by 151 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.PrcEnvelopeNumber1Rotated">
            <summary>
            PRC #1 rotated envelope (165 mm by 102 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.PrcEnvelopeNumber2Rotated">
            <summary>
            PRC #2 rotated envelope (176 mm by 102 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.PrcEnvelopeNumber3Rotated">
            <summary>
            PRC #3 rotated envelope (176 mm by 125 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.PrcEnvelopeNumber4Rotated">
            <summary>
            PRC #4 rotated envelope (208 mm by 110 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.PrcEnvelopeNumber5Rotated">
            <summary>
            PRC #5 rotated envelope (220 mm by 110 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.PrcEnvelopeNumber6Rotated">
            <summary>
            PRC #6 rotated envelope (230 mm by 120 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.PrcEnvelopeNumber7Rotated">
            <summary>
            PRC #7 rotated envelope (230 mm by 160 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.PrcEnvelopeNumber8Rotated">
            <summary>
            PRC #8 rotated envelope (309 mm by 120 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.PrcEnvelopeNumber9Rotated">
            <summary>
            PRC #9 rotated envelope (324 mm by 229 mm).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperKind.PrcEnvelopeNumber10Rotated">
            <summary>
            PRC #10 rotated envelope (458 mm by 324 mm).
            </summary>
        </member>
        <member name="T:System.Drawing.Printing.PaperSize">
            <summary>
            Specifies the size of a piece of paper.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PaperSize.#ctor">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Printing.PaperSize'/> class with default properties.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PaperSize.#ctor(System.String,System.Int32,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Printing.PaperSize'/> class.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PaperSize.Height">
            <summary>
            Gets or sets the height of the paper, in hundredths of an inch.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PaperSize.Kind">
            <summary>
            Gets the type of paper.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PaperSize.PaperName">
            <summary>
            Gets or sets the name of the type of paper.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PaperSize.RawKind">
            <summary>
            Same as Kind, but values larger than or equal to DMPAPER_LAST do not map to PaperKind.Custom.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PaperSize.Width">
            <summary>
            Gets or sets the width of the paper, in hundredths of an inch.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PaperSize.ToString">
            <summary>
            Provides some interesting information about the PaperSize in String form.
            </summary>
        </member>
        <member name="T:System.Drawing.Printing.PaperSource">
            <summary>
            Specifies the paper tray from which the printer gets paper.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PaperSource.#ctor">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Printing.PaperSource'/> class with default properties.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PaperSource.Kind">
            <summary>
            Gets a value indicating the type of paper source.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PaperSource.RawKind">
            <summary>
            Same as Kind, but values larger than DMBIN_USER do not map to PaperSourceKind.Custom.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PaperSource.SourceName">
            <summary>
            Gets the name of the paper source.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PaperSource.ToString">
            <summary>
            Provides some interesting information about the PaperSource in String form.
            </summary>
        </member>
        <member name="T:System.Drawing.Printing.PaperSourceKind">
            <summary>
            Standard paper sources.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperSourceKind.Upper">
            <summary>
            The upper bin of a printer (or, if the printer only has one bin, the only bin).
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperSourceKind.Lower">
            <summary>
            The lower bin of a printer.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperSourceKind.Middle">
            <summary>
            The middle bin of a printer.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperSourceKind.Manual">
            <summary>
            Manually-fed paper.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperSourceKind.Envelope">
            <summary>
            An envelope.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperSourceKind.ManualFeed">
            <summary>
            A manually-fed envelope.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperSourceKind.AutomaticFeed">
            <summary>
            Automatic-fed paper.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperSourceKind.TractorFeed">
            <summary>
            A tractor feed.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperSourceKind.SmallFormat">
            <summary>
            Small-format paper.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperSourceKind.LargeFormat">
            <summary>
            Large-format paper.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperSourceKind.LargeCapacity">
            <summary>
            A large-capacity bin printer.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperSourceKind.Cassette">
            <summary>
            A paper cassette.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PaperSourceKind.Custom">
            <summary>
            A printer-specific paper source.
            </summary>
        </member>
        <member name="T:System.Drawing.Printing.PrinterResolution">
            <summary>
            Retrieves the resolution supported by a printer.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PrinterResolution.#ctor">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Printing.PrinterResolution'/> class with default properties.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PrinterResolution.Kind">
            <summary>
            Gets a value indicating the kind of printer resolution.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PrinterResolution.X">
            <summary>
            Gets the printer resolution in the horizontal direction, in dots per inch.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PrinterResolution.Y">
            <summary>
            Gets the printer resolution in the vertical direction, in dots per inch.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PrinterResolution.ToString">
            <summary>
            Provides some interesting information about the PrinterResolution in String form.
            </summary>
        </member>
        <member name="T:System.Drawing.Printing.PrinterResolutionKind">
            <summary>
            Specifies a printer resolution.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PrinterResolutionKind.High">
            <summary>
            High resolution.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PrinterResolutionKind.Medium">
            <summary>
            Medium resolution.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PrinterResolutionKind.Low">
            <summary>
            Low resolution.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PrinterResolutionKind.Draft">
            <summary>
            Draft-quality resolution.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PrinterResolutionKind.Custom">
            <summary>
            Custom resolution.
            </summary>
        </member>
        <member name="T:System.Drawing.Printing.PrinterUnitConvert">
            <summary>
            Specifies a series of conversion methods that are useful when interoperating with the raw Win32 printing API.
            This class cannot be inherited.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PrinterUnitConvert.Convert(System.Double,System.Drawing.Printing.PrinterUnit,System.Drawing.Printing.PrinterUnit)">
            <summary>
            Converts the value, in fromUnit units, to toUnit units.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PrinterUnitConvert.Convert(System.Int32,System.Drawing.Printing.PrinterUnit,System.Drawing.Printing.PrinterUnit)">
            <summary>
            Converts the value, in fromUnit units, to toUnit units.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PrinterUnitConvert.Convert(System.Drawing.Point,System.Drawing.Printing.PrinterUnit,System.Drawing.Printing.PrinterUnit)">
            <summary>
            Converts the value, in fromUnit units, to toUnit units.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PrinterUnitConvert.Convert(System.Drawing.Size,System.Drawing.Printing.PrinterUnit,System.Drawing.Printing.PrinterUnit)">
            <summary>
            Converts the value, in fromUnit units, to toUnit units.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PrinterUnitConvert.Convert(System.Drawing.Rectangle,System.Drawing.Printing.PrinterUnit,System.Drawing.Printing.PrinterUnit)">
            <summary>
            Converts the value, in fromUnit units, to toUnit units.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PrinterUnitConvert.Convert(System.Drawing.Printing.Margins,System.Drawing.Printing.PrinterUnit,System.Drawing.Printing.PrinterUnit)">
            <summary>
            Converts the value, in fromUnit units, to toUnit units.
            </summary>
        </member>
        <member name="T:System.Drawing.Printing.PrintRange">
            <summary>
            Specifies the option buttons in the print dialog box that designate the part of the document to print.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PrintRange.AllPages">
            <summary>
            All pages are printed.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PrintRange.SomePages">
            <summary>
            The pages between <see cref='P:System.Drawing.Printing.PrinterSettings.FromPage'/> and <see cref='P:System.Drawing.Printing.PrinterSettings.ToPage'/> are printed.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PrintRange.Selection">
            <summary>
            The selected pages are printed.
            </summary>
        </member>
        <member name="F:System.Drawing.Printing.PrintRange.CurrentPage">
            <summary>
            The current page is printed. The print dialog box requires Windows 2000 or later for this setting; if used
            with an earlier operating system, all pages will be printed.
            </summary>
        </member>
        <member name="T:System.Drawing.Printing.StandardPrintController">
            <summary>
            Specifies a print controller that sends information to a printer.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.StandardPrintController.OnStartPrint(System.Drawing.Printing.PrintDocument,System.Drawing.Printing.PrintEventArgs)">
            <summary>
            Implements StartPrint for printing to a physical printer.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.StandardPrintController.OnStartPage(System.Drawing.Printing.PrintDocument,System.Drawing.Printing.PrintPageEventArgs)">
            <summary>
            Implements StartPage for printing to a physical printer.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.StandardPrintController.OnEndPage(System.Drawing.Printing.PrintDocument,System.Drawing.Printing.PrintPageEventArgs)">
            <summary>
            Implements EndPage for printing to a physical printer.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.StandardPrintController.OnEndPrint(System.Drawing.Printing.PrintDocument,System.Drawing.Printing.PrintEventArgs)">
            <summary>
            Implements EndPrint for printing to a physical printer.
            </summary>
        </member>
        <member name="T:System.Drawing.Printing.PageSettings">
            <summary>
            Specifies settings that apply to a single page.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PageSettings.#ctor">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Printing.PageSettings'/> class using the default printer.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PageSettings.#ctor(System.Drawing.Printing.PrinterSettings)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Printing.PageSettings'/> class using the specified printer.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PageSettings.Bounds">
            <summary>
            Gets the bounds of the page, taking into account the Landscape property.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PageSettings.Color">
            <summary>
            Gets or sets a value indicating whether the page is printed in color.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PageSettings.HardMarginX">
            <summary>
            Returns the x dimension of the hard margin
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PageSettings.HardMarginY">
            <summary>
            Returns the y dimension of the hard margin.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PageSettings.Landscape">
            <summary>
            Gets or sets a value indicating whether the page should be printed in landscape or portrait orientation.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PageSettings.Margins">
            <summary>
            Gets or sets a value indicating the margins for this page.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PageSettings.PaperSize">
            <summary>
            Gets or sets the paper size.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PageSettings.PaperSource">
            <summary>
            Gets or sets a value indicating the paper source (i.e. upper bin).
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PageSettings.PrintableArea">
            <summary>
            Gets the PrintableArea for the printer. Units = 100ths of an inch.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PageSettings.PrinterResolution">
            <summary>
            Gets or sets the printer resolution for the page.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PageSettings.PrinterSettings">
            <summary>
            Gets or sets the associated printer settings.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PageSettings.Clone">
            <summary>
            Copies the settings and margins.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PageSettings.CopyToHdevmode(System.IntPtr)">
            <summary>
            Copies the relevant information out of the PageSettings and into the handle.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PageSettings.SetHdevmode(System.IntPtr)">
            <summary>
            Copies the relevant information out of the handle and into the PageSettings.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PageSettings.ToString">
            <summary>
            Provides some interesting information about the PageSettings in String form.
            </summary>
        </member>
        <member name="T:System.Drawing.Printing.PrintDocument">
            <summary>
            Defines a reusable object that sends output to the printer.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PrintDocument.#ctor">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Printing.PrintDocument'/> class.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PrintDocument.DefaultPageSettings">
            <summary>
            Gets or sets the default page settings for the document being printed.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PrintDocument.DocumentName">
            <summary>
            Gets or sets the name to display to the user while printing the document; for example, in a print status
            dialog or a printer queue.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PrintDocument.PrintController">
            <summary>
            Gets or sets the <see cref='T:System.Drawing.Printing.PrintController'/>  that guides the printing process.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PrintDocument.PrinterSettings">
            <summary>
            Gets or sets the printer on which the document is printed.
            </summary>
        </member>
        <member name="E:System.Drawing.Printing.PrintDocument.BeginPrint">
            <summary>
            Occurs when the <see cref='M:System.Drawing.Printing.PrintDocument.Print'/> method is called, before the first page prints.
            </summary>
        </member>
        <member name="E:System.Drawing.Printing.PrintDocument.EndPrint">
            <summary>
            Occurs when <see cref='M:System.Drawing.Printing.PrintDocument.Print'/> is called, after the last page is printed.
            </summary>
        </member>
        <member name="E:System.Drawing.Printing.PrintDocument.PrintPage">
            <summary>
            Occurs when a page is printed.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PrintDocument.OnBeginPrint(System.Drawing.Printing.PrintEventArgs)">
            <summary>
            Raises the <see cref='E:System.Drawing.Printing.PrintDocument.BeginPrint'/> event.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PrintDocument.OnEndPrint(System.Drawing.Printing.PrintEventArgs)">
            <summary>
            Raises the <see cref='E:System.Drawing.Printing.PrintDocument.EndPrint'/> event.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PrintDocument.OnPrintPage(System.Drawing.Printing.PrintPageEventArgs)">
            <summary>
            Raises the <see cref='E:System.Drawing.Printing.PrintDocument.PrintPage'/> event.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PrintDocument.OnQueryPageSettings(System.Drawing.Printing.QueryPageSettingsEventArgs)">
            <summary>
            Raises the <see cref='E:System.Drawing.Printing.PrintDocument.QueryPageSettings'/> event.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PrintDocument.Print">
            <summary>
            Prints the document.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PrintDocument.ToString">
            <summary>
            Provides some interesting information about the PrintDocument in String form.
            </summary>
        </member>
        <member name="T:System.Drawing.Printing.PrinterSettings">
            <summary>
            Information about how a document should be printed, including which printer to print it on.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PrinterSettings.#ctor">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Printing.PrinterSettings'/> class.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PrinterSettings.CanDuplex">
            <summary>
            Gets a value indicating whether the printer supports duplex (double-sided) printing.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PrinterSettings.Copies">
            <summary>
            Gets or sets the number of copies to print.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PrinterSettings.Collate">
            <summary>
            Gets or sets a value indicating whether the print out is collated.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PrinterSettings.DefaultPageSettings">
            <summary>
            Gets the default page settings for this printer.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PrinterSettings.Duplex">
            <summary>
            Gets or sets the printer's duplex setting.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PrinterSettings.FromPage">
            <summary>
            Gets or sets the first page to print.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PrinterSettings.InstalledPrinters">
            <summary>
            Gets the names of all printers installed on the machine.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PrinterSettings.IsDefaultPrinter">
            <summary>
            Gets a value indicating whether the <see cref='P:System.Drawing.Printing.PrinterSettings.PrinterName'/> property designates the default printer.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PrinterSettings.IsPlotter">
            <summary>
            Gets a value indicating whether the printer is a plotter, as opposed to a raster printer.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PrinterSettings.IsValid">
            <summary>
            Gets a value indicating whether the <see cref='P:System.Drawing.Printing.PrinterSettings.PrinterName'/> property designates a valid printer.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PrinterSettings.LandscapeAngle">
            <summary>
            Gets the angle, in degrees, which the portrait orientation is rotated to produce the landscape orientation.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PrinterSettings.MaximumCopies">
            <summary>
            Gets the maximum number of copies allowed by the printer.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PrinterSettings.MaximumPage">
            <summary>
            Gets or sets the highest <see cref='P:System.Drawing.Printing.PrinterSettings.FromPage'/> or <see cref='P:System.Drawing.Printing.PrinterSettings.ToPage'/> which may be selected in a print dialog box.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PrinterSettings.MinimumPage">
            <summary>
            Gets or sets the lowest <see cref='P:System.Drawing.Printing.PrinterSettings.FromPage'/> or <see cref='P:System.Drawing.Printing.PrinterSettings.ToPage'/> which may be selected in a print dialog box.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PrinterSettings.PrintFileName">
            <summary>
            Indicates the name of the printerfile.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PrinterSettings.PaperSizes">
            <summary>
            Gets the paper sizes supported by this printer.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PrinterSettings.PaperSources">
            <summary>
            Gets the paper sources available on this printer.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PrinterSettings.PrintDialogDisplayed">
            <summary>
            Whether the print dialog has been displayed.  In SafePrinting mode, a print dialog is required to print.
            After printing, this property is set to false if the program does not have AllPrinting; this guarantees
            a document is only printed once each time the print dialog is shown.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PrinterSettings.PrintRange">
            <summary>
            Gets or sets the pages the user has asked to print.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PrinterSettings.PrintToFile">
            <summary>
            Indicates whether to print to a file instead of a port.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PrinterSettings.PrinterName">
            <summary>
            Gets or sets the name of the printer.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PrinterSettings.PrinterResolutions">
            <summary>
            Gets the resolutions supported by this printer.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PrinterSettings.IsDirectPrintingSupported(System.Drawing.Imaging.ImageFormat)">
            <summary>
            If the image is a JPEG or a PNG (Image.RawFormat) and the printer returns true from
            ExtEscape(CHECKJPEGFORMAT) or ExtEscape(CHECKPNGFORMAT) then this function returns true.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PrinterSettings.IsDirectPrintingSupported(System.Drawing.Image)">
             <summary>
             This method utilizes the CHECKJPEGFORMAT/CHECKPNGFORMAT printer escape functions
             to determine whether the printer can handle a JPEG image.
            
             If the image is a JPEG or a PNG (Image.RawFormat) and the printer returns true
             from ExtEscape(CHECKJPEGFORMAT) or ExtEscape(CHECKPNGFORMAT) then this function returns true.
             </summary>
        </member>
        <member name="P:System.Drawing.Printing.PrinterSettings.SupportsColor">
            <summary>
            Gets a value indicating whether the printer supports color printing.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PrinterSettings.ToPage">
            <summary>
            Gets or sets the last page to print.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PrinterSettings.Clone">
            <summary>
            Creates an identical copy of this object.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PrinterSettings.GetHdevmode">
            <summary>
            Creates a handle to a DEVMODE structure which correspond too the printer settings.When you are done with the
            handle, you must deallocate it yourself:
              Interop.Kernel32.GlobalFree(handle);
              Where "handle" is the return value from this method.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PrinterSettings.GetHdevmode(System.Drawing.Printing.PageSettings)">
            <summary>
            Creates a handle to a DEVMODE structure which correspond to the printer and page settings.
            When you are done with the handle, you must deallocate it yourself:
              Interop.Kernel32.GlobalFree(handle);
              Where "handle" is the return value from this method.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PrinterSettings.GetHdevnames">
            <summary>
            Creates a handle to a DEVNAMES structure which correspond to the printer settings.
            When you are done with the handle, you must deallocate it yourself:
              Interop.Kernel32.GlobalFree(handle);
              Where "handle" is the return value from this method.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PrinterSettings.SetHdevmode(System.IntPtr)">
            <summary>
            Copies the relevant information out of the handle and into the PrinterSettings.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PrinterSettings.SetHdevnames(System.IntPtr)">
            <summary>
            Copies the relevant information out of the handle and into the PrinterSettings.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PrinterSettings.ToString">
            <summary>
            Provides some interesting information about the PrinterSettings in String form.
            </summary>
        </member>
        <member name="T:System.Drawing.Printing.PrinterSettings.PaperSizeCollection">
            <summary>
            Collection of PaperSize's...
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PrinterSettings.PaperSizeCollection.#ctor(System.Drawing.Printing.PaperSize[])">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Printing.PrinterSettings.PaperSizeCollection'/> class.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PrinterSettings.PaperSizeCollection.Count">
            <summary>
            Gets a value indicating the number of paper sizes.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PrinterSettings.PaperSizeCollection.Item(System.Int32)">
            <summary>
            Retrieves the PaperSize with the specified index.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PrinterSettings.PaperSourceCollection.#ctor(System.Drawing.Printing.PaperSource[])">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Printing.PrinterSettings.PaperSourceCollection'/> class.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PrinterSettings.PaperSourceCollection.Count">
            <summary>
            Gets a value indicating the number of paper sources.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PrinterSettings.PaperSourceCollection.Item(System.Int32)">
            <summary>
            Gets the PaperSource with the specified index.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PrinterSettings.PrinterResolutionCollection.#ctor(System.Drawing.Printing.PrinterResolution[])">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Printing.PrinterSettings.PrinterResolutionCollection'/> class.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PrinterSettings.PrinterResolutionCollection.Count">
            <summary>
            Gets a value indicating the number of available printer resolutions.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PrinterSettings.PrinterResolutionCollection.Item(System.Int32)">
            <summary>
            Retrieves the PrinterResolution with the specified index.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PrinterSettings.StringCollection.#ctor(System.String[])">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Printing.PrinterSettings.StringCollection'/> class.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PrinterSettings.StringCollection.Count">
            <summary>
            Gets a value indicating the number of strings.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PrinterSettings.StringCollection.Item(System.Int32)">
            <summary>
            Gets the string with the specified index.
            </summary>
        </member>
        <member name="T:System.Drawing.Printing.PrintEventArgs">
            <summary>
            Provides data for the <see cref='E:System.Drawing.Printing.PrintDocument.BeginPrint'/> and <see cref='E:System.Drawing.Printing.PrintDocument.EndPrint'/> events.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PrintEventArgs.#ctor">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Printing.PrintEventArgs'/> class.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PrintEventArgs.#ctor(System.Drawing.Printing.PrintAction)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Printing.PrintEventArgs'/> class.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PrintEventArgs.PrintAction">
            <summary>
            Specifies which <see cref='T:System.Drawing.Printing.PrintAction'/> is causing this event.
            </summary>
        </member>
        <member name="T:System.Drawing.Printing.PrintPageEventArgs">
            <summary>
            Provides data for the <see cref='E:System.Drawing.Printing.PrintDocument.PrintPage'/> event.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PrintPageEventArgs.#ctor(System.Drawing.Graphics,System.Drawing.Rectangle,System.Drawing.Rectangle,System.Drawing.Printing.PageSettings)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Printing.PrintPageEventArgs'/> class.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PrintPageEventArgs.Cancel">
            <summary>
            Gets or sets a value indicating whether the print job should be canceled.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PrintPageEventArgs.Graphics">
            <summary>
            Gets the <see cref='T:System.Drawing.Graphics'/> used to paint the item.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PrintPageEventArgs.HasMorePages">
            <summary>
            Gets or sets a value indicating whether an additional page should be printed.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PrintPageEventArgs.MarginBounds">
            <summary>
            Gets the rectangular area that represents the portion of the page between the margins.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PrintPageEventArgs.PageBounds">
            <summary>
            Gets the rectangular area that represents the total area of the page.
            </summary>
        </member>
        <member name="P:System.Drawing.Printing.PrintPageEventArgs.PageSettings">
            <summary>
            Gets the page settings for the current page.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.PrintPageEventArgs.Dispose">
            <summary>
            Disposes of the resources (other than memory) used by the <see cref='T:System.Drawing.Printing.PrintPageEventArgs'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Printing.TriState.ToString">
            <summary>
            Provides some interesting information about the TriState in String form.
            </summary>
        </member>
        <member name="T:System.Drawing.Design.CategoryNameCollection">
            <summary>
            A collection that stores <see cref='T:System.String'/> objects.
            </summary>
        </member>
        <member name="M:System.Drawing.Design.CategoryNameCollection.#ctor(System.Drawing.Design.CategoryNameCollection)">
            <summary>
            Initializes a new instance of <see cref='T:System.Drawing.Design.CategoryNameCollection'/> based on another
            <see cref='T:System.Drawing.Design.CategoryNameCollection'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Design.CategoryNameCollection.#ctor(System.String[])">
            <summary>
            Initializes a new instance of <see cref='T:System.Drawing.Design.CategoryNameCollection'/> containing any array of
            <see cref='T:System.String'/> objects.
            </summary>
        </member>
        <member name="P:System.Drawing.Design.CategoryNameCollection.Item(System.Int32)">
            <summary>
            Represents the entry at the specified index of the <see cref='T:System.String'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Design.CategoryNameCollection.Contains(System.String)">
            <summary>
            Gets a value indicating whether the  <see cref='T:System.Drawing.Design.CategoryNameCollection'/> contains the specified
            <see cref='T:System.String'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Design.CategoryNameCollection.CopyTo(System.String[],System.Int32)">
            <summary>
            Copies the <see cref='T:System.Drawing.Design.CategoryNameCollection'/> values to a one-dimensional <see cref='T:System.Array'/> instance
            at the specified index.
            </summary>
        </member>
        <member name="M:System.Drawing.Design.CategoryNameCollection.IndexOf(System.String)">
            <summary>
            Returns the index of a <see cref='T:System.String'/> in  the <see cref='T:System.Drawing.Design.CategoryNameCollection'/> .
            </summary>
        </member>
        <member name="M:System.Drawing.Drawing2D.Matrix.#ctor(System.Numerics.Matrix3x2)">
            <summary>
            Construct a <see cref="T:System.Drawing.Drawing2D.Matrix"/> utilizing the given <paramref name="matrix"/>.
            </summary>
            <param name="matrix">Matrix data to construct from.</param>
        </member>
        <member name="P:System.Drawing.Drawing2D.Matrix.MatrixElements">
            <summary>
             Gets/sets the elements for the matrix.
            </summary>
        </member>
        <member name="T:System.Drawing.Drawing2D.DashCap">
            <summary>
            Specifies the available dash cap styles with which a <see cref='T:System.Drawing.Pen'/> can end a line.
            </summary>
        </member>
        <member name="M:System.Drawing.Drawing2D.GraphicsPath.AddCurve(System.Drawing.PointF[])">
            <summary>
            Add cardinal splines to the path object
            </summary>
        </member>
        <member name="M:System.Drawing.Drawing2D.GraphicsPath.AddPolygon(System.Drawing.Point[])">
            <summary>
            Adds a polygon to the current figure.
            </summary>
        </member>
        <member name="T:System.Drawing.CopyPixelOperation">
            <summary>
            Specifies the Copy Pixel (ROP) operation.
            </summary>
        </member>
        <member name="F:System.Drawing.CopyPixelOperation.Blackness">
            <summary>
            Fills the Destination Rectangle using the color associated with the index 0 in the physical palette.
            </summary>
        </member>
        <member name="F:System.Drawing.CopyPixelOperation.CaptureBlt">
            <summary>
            Includes any windows that are Layered on Top.
            </summary>
        </member>
        <member name="T:System.Drawing.FontStyle">
            <summary>
            Specifies style information applied to text.
            </summary>
        </member>
        <member name="F:System.Drawing.FontStyle.Regular">
            <summary>
            Normal text.
            </summary>
        </member>
        <member name="F:System.Drawing.FontStyle.Bold">
            <summary>
            Bold text.
            </summary>
        </member>
        <member name="F:System.Drawing.FontStyle.Italic">
            <summary>
            Italic text.
            </summary>
        </member>
        <member name="F:System.Drawing.FontStyle.Underline">
            <summary>
            Underlined text.
            </summary>
        </member>
        <member name="F:System.Drawing.FontStyle.Strikeout">
            <summary>
            Text with a line through the middle.
            </summary>
        </member>
        <member name="T:System.Drawing.StringAlignment">
            <summary>
            Specifies the alignment of a text string relative to its layout rectangle.
            </summary>
        </member>
        <member name="F:System.Drawing.StringAlignment.Near">
            <summary>
            Specifies the text be aligned near the layout. In a left-to-right layout, the near position is left. In a
            right-to-left layout, the near position is right.
            </summary>
        </member>
        <member name="F:System.Drawing.StringAlignment.Center">
            <summary>
            Specifies that text is aligned in the center of the layout rectangle.
            </summary>
        </member>
        <member name="F:System.Drawing.StringAlignment.Far">
            <summary>
            Specifies that text is aligned far from the origin position of the layout rectangle. In a left-to-right
            layout, the far position is right. In a right-to-left layout, the far position is left.
            </summary>
        </member>
        <member name="T:System.Drawing.StringDigitSubstitute">
            <summary>
            Specifies style information applied to String Digit Substitute.
            </summary>
        </member>
        <member name="T:System.Drawing.StringFormatFlags">
            <summary>
            Specifies the display and layout information for text strings.
            </summary>
        </member>
        <member name="F:System.Drawing.StringFormatFlags.DirectionRightToLeft">
            <summary>
            Specifies that text is right to left.
            </summary>
        </member>
        <member name="F:System.Drawing.StringFormatFlags.DirectionVertical">
            <summary>
            Specifies that text is vertical.
            </summary>
        </member>
        <member name="F:System.Drawing.StringFormatFlags.FitBlackBox">
             <summary>
             Specifies that no part of any glyph overhangs the bounding rectangle. By default some glyphs
             overhang the rectangle slightly where necessary to appear at the edge visually. For example
             when an italic lower case letter f in a font such as Garamond is aligned at the far left
             of a rectangle, the lower part of the f will reach slightly further left than the left edge
             of the rectangle. Setting this flag will ensure no painting outside the rectangle but will
             cause the aligned edges of adjacent lines of text to appear uneven.
            
             WARNING:
             The GDI+ equivalent for this is StringFormatFlags::StringFormatFlagsNoFitBlackBox,
             which is defined as 0x4.  This was a mistake introduced since the first version of
             the product and fixing it at this point would be a breaking change.
             <see href="https://docs.microsoft.com/en-us/windows/desktop/api/gdiplusenums/ne-gdiplusenums-stringformatflags"/>
             </summary>
        </member>
        <member name="F:System.Drawing.StringFormatFlags.DisplayFormatControl">
            <summary>
            Causes control characters such as the left-to-right mark to be shown in the output with a representative glyph.
            </summary>
        </member>
        <member name="F:System.Drawing.StringFormatFlags.NoFontFallback">
            <summary>
            Disables fallback to alternate fonts for characters not supported in the requested font. Any missing characters are
            displayed with the fonts missing glyph, usually an open square.
            </summary>
        </member>
        <member name="F:System.Drawing.StringFormatFlags.MeasureTrailingSpaces">
            <summary>
            Specifies that the space at the end of each line is included in a string measurement.
            </summary>
        </member>
        <member name="F:System.Drawing.StringFormatFlags.NoWrap">
            <summary>
            Specifies that the wrapping of text to the next line is disabled. NoWrap is implied when a point of origin
            is used instead of a layout rectangle. When drawing text within a rectangle, by default, text is broken at
            the last word boundary that is inside the rectangle's boundary and wrapped to the next line.
            </summary>
        </member>
        <member name="F:System.Drawing.StringFormatFlags.LineLimit">
            <summary>
            Specifies that only entire lines are laid out in the layout rectangle. By default, layout
            continues until the end of the text or until no more lines are visible as a result of clipping,
            whichever comes first. The default settings allow the last line to be partially obscured by a
            layout rectangle that is not a whole multiple of the line height.
            To ensure that only whole lines are seen, set this flag and be careful to provide a layout
            rectangle at least as tall as the height of one line.
            </summary>
        </member>
        <member name="F:System.Drawing.StringFormatFlags.NoClip">
            <summary>
            Specifies that characters overhanging the layout rectangle and text extending outside the layout
            rectangle are allowed to show. By default, all overhanging characters and text that extends outside
            the layout rectangle are clipped. Any trailing spaces (spaces that are at the end of a line) that
            extend outside the layout rectangle are clipped. Therefore, the setting of this flag will have an
            effect on a string measurement if trailing spaces are being included in the measurement.
            If clipping is enabled, trailing spaces that extend outside the layout rectangle are not included
            in the measurement. If clipping is disabled, all trailing spaces are included in the measurement,
            regardless of whether they are outside the layout rectangle.
            </summary>
        </member>
        <member name="T:System.Drawing.StringTrimming">
            <summary>
            Specifies how to trim characters from a string that does not completely fit into a layout shape.
            </summary>
        </member>
        <member name="F:System.Drawing.StringTrimming.None">
            <summary>
            Specifies no trimming.
            </summary>
        </member>
        <member name="F:System.Drawing.StringTrimming.Character">
            <summary>
            Specifies that the string is broken at the boundary of the last character
            that is inside the layout rectangle. This is the default.
            </summary>
        </member>
        <member name="F:System.Drawing.StringTrimming.Word">
            <summary>
            Specifies that the string is broken at the boundary of the last word that is inside the layout rectangle.
            </summary>
        </member>
        <member name="F:System.Drawing.StringTrimming.EllipsisCharacter">
            <summary>
            Specifies that the string is broken at the boundary of the last character that is inside
            the layout rectangle and an ellipsis (...) is inserted after the character.
            </summary>
        </member>
        <member name="F:System.Drawing.StringTrimming.EllipsisWord">
            <summary>
            Specifies that the string is broken at the boundary of the last word that is inside the
            layout rectangle and an ellipsis (...) is inserted after the word.
            </summary>
        </member>
        <member name="F:System.Drawing.StringTrimming.EllipsisPath">
            <summary>
            Specifies that the center is removed from the string and replaced by an ellipsis.
            The algorithm keeps as much of the last portion of the string as possible.
            </summary>
        </member>
        <member name="T:System.Drawing.StringUnit">
            <summary>
            Specifies the units of measure for a text string.
            </summary>
        </member>
        <member name="F:System.Drawing.StringUnit.World">
            <summary>
            Specifies world units as the unit of measure.
            </summary>
        </member>
        <member name="F:System.Drawing.StringUnit.Display">
            <summary>
            Specifies the device unit as the unit of measure.
            </summary>
        </member>
        <member name="F:System.Drawing.StringUnit.Pixel">
            <summary>
            Specifies a pixel as the unit of measure.
            </summary>
        </member>
        <member name="F:System.Drawing.StringUnit.Point">
            <summary>
            Specifies a printer's point as the unit of measure.
            </summary>
        </member>
        <member name="F:System.Drawing.StringUnit.Inch">
            <summary>
            Specifies an inch as the unit of measure.
            </summary>
        </member>
        <member name="F:System.Drawing.StringUnit.Document">
            <summary>
            Specifies 1/300 of an inch as the unit of measure.
            </summary>
        </member>
        <member name="F:System.Drawing.StringUnit.Millimeter">
            <summary>
            Specifies a millimeter as the unit of measure
            </summary>
        </member>
        <member name="F:System.Drawing.StringUnit.Em">
            <summary>
            Specifies a printer's em size of 32 as the unit of measure.
            </summary>
        </member>
        <member name="T:System.Drawing.Text.GenericFontFamilies">
            <summary>
            Specifies a generic <see cref='T:System.Drawing.FontFamily'/>.
            </summary>
        </member>
        <member name="F:System.Drawing.Text.GenericFontFamilies.Serif">
            <summary>
            A generic Serif <see cref='T:System.Drawing.FontFamily'/>.
            </summary>
        </member>
        <member name="F:System.Drawing.Text.GenericFontFamilies.SansSerif">
            <summary>
            A generic SansSerif <see cref='T:System.Drawing.FontFamily'/>.
            </summary>
        </member>
        <member name="F:System.Drawing.Text.GenericFontFamilies.Monospace">
            <summary>
            A generic Monospace <see cref='T:System.Drawing.FontFamily'/>.
            </summary>
        </member>
        <member name="T:System.Drawing.Text.HotkeyPrefix">
            <summary>
            Specifies the type of display for hotkey prefixes for text.
            </summary>
        </member>
        <member name="F:System.Drawing.Text.HotkeyPrefix.None">
            <summary>
            No hotkey prefix.
            </summary>
        </member>
        <member name="F:System.Drawing.Text.HotkeyPrefix.Show">
            <summary>
            Display the hotkey prefix.
            </summary>
        </member>
        <member name="F:System.Drawing.Text.HotkeyPrefix.Hide">
            <summary>
            Do not display the hotkey prefix.
            </summary>
        </member>
        <member name="T:System.Drawing.Text.TextRenderingHint">
            <summary>
            Specifies the quality of text rendering.
            </summary>
        </member>
        <member name="F:System.Drawing.Text.TextRenderingHint.SystemDefault">
            <summary>
            Glyph with system default rendering hint.
            </summary>
        </member>
        <member name="F:System.Drawing.Text.TextRenderingHint.SingleBitPerPixelGridFit">
            <summary>
            Glyph bitmap with hinting.
            </summary>
        </member>
        <member name="F:System.Drawing.Text.TextRenderingHint.SingleBitPerPixel">
            <summary>
            Glyph bitmap without hinting.
            </summary>
        </member>
        <member name="F:System.Drawing.Text.TextRenderingHint.AntiAliasGridFit">
            <summary>
            Anti-aliasing with hinting.
            </summary>
        </member>
        <member name="F:System.Drawing.Text.TextRenderingHint.AntiAlias">
            <summary>
            Glyph anti-alias bitmap without hinting.
            </summary>
        </member>
        <member name="F:System.Drawing.Text.TextRenderingHint.ClearTypeGridFit">
            <summary>
            Glyph CT bitmap with hinting.
            </summary>
        </member>
        <member name="T:System.Drawing.Text.PrivateFontCollection">
            <summary>
            Encapsulates a collection of <see cref='T:System.Drawing.Font'/> objects.
            </summary>
        </member>
        <member name="M:System.Drawing.Text.PrivateFontCollection.#ctor">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Text.PrivateFontCollection'/> class.
            </summary>
        </member>
        <member name="M:System.Drawing.Text.PrivateFontCollection.Dispose(System.Boolean)">
            <summary>
            Cleans up Windows resources for this <see cref='T:System.Drawing.Text.PrivateFontCollection'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Text.PrivateFontCollection.AddFontFile(System.String)">
            <summary>
            Adds a font from the specified file to this <see cref='T:System.Drawing.Text.PrivateFontCollection'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Text.PrivateFontCollection.AddMemoryFont(System.IntPtr,System.Int32)">
            <summary>
            Adds a font contained in system memory to this <see cref='T:System.Drawing.Text.PrivateFontCollection'/>.
            </summary>
        </member>
        <member name="T:System.Drawing.Text.FontCollection">
            <summary>
            When inherited, enumerates the FontFamily objects in a collection of fonts.
            </summary>
        </member>
        <member name="M:System.Drawing.Text.FontCollection.Dispose">
            <summary>
            Disposes of this <see cref='T:System.Drawing.Text.FontCollection'/>
            </summary>
        </member>
        <member name="P:System.Drawing.Text.FontCollection.Families">
            <summary>
            Gets the array of <see cref='T:System.Drawing.FontFamily'/> objects associated
            with this <see cref='T:System.Drawing.Text.FontCollection'/>.
            </summary>
        </member>
        <member name="T:System.Drawing.Text.InstalledFontCollection">
            <summary>
            Represents the fonts installed on the system.
            </summary>
        </member>
        <member name="M:System.Drawing.Text.InstalledFontCollection.#ctor">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Text.InstalledFontCollection'/> class.
            </summary>
        </member>
        <member name="T:System.Drawing.ClientUtils.WeakRefCollection">
             <summary>
             WeakRefCollection - a collection that holds onto weak references.
            
             Essentially you pass in the object as it is, and under the covers
             we only hold a weak reference to the object.
            
             -----------------------------------------------------------------
             !!!IMPORTANT USAGE NOTE!!!
             Users of this class should set the RefCheckThreshold property
             explicitly or call ScavengeReferences every once in a while to
             remove dead references.
             Also avoid calling Remove(item). Instead call RemoveByHashCode(item)
             to make sure dead refs are removed.
             </summary>
        </member>
        <member name="P:System.Drawing.ClientUtils.WeakRefCollection.RefCheckThreshold">
            <summary>
            Indicates the value where the collection should check its items to remove dead weakref left over.
            Note: When GC collects weak refs from this collection the WeakRefObject identity changes since its
                  Target becomes null. This makes the item unrecognizable by the collection and cannot be
                  removed - Remove(item) and Contains(item) will not find it anymore.
            A value of int.MaxValue means disabled by default.
            </summary>
        </member>
        <member name="M:System.Drawing.ClientUtils.WeakRefCollection.RemoveByHashCode(System.Object)">
            <summary>
            Removes the value using its hash code as its identity.
            This is needed because the underlying item in the collection may have already been collected changing
            the identity of the WeakRefObject making it impossible for the collection to identify it.
            See WeakRefObject for more info.
            </summary>
        </member>
        <member name="T:System.Drawing.ClientUtils.WeakRefCollection.WeakRefObject">
            <summary>
            Wraps a weak ref object.
            WARNING: Use this class carefully!
            When the weak ref is collected, this object looses its identity. This is bad when the object has been
            added to a collection since Contains(WeakRef(item)) and Remove(WeakRef(item)) would not be able to
            identify the item.
            </summary>
        </member>
        <member name="M:System.Drawing.Internal.DbgUtil.AssertFinalization(System.Object,System.Boolean)">
            <summary>
            Call this method from your Dispose(bool) to assert that unmanaged resources has been explicitly disposed.
            </summary>
        </member>
        <member name="T:System.Drawing.Internal.ApplyGraphicsProperties">
            <summary>
            Enumeration defining the different Graphics properties to apply to a WindowsGraphics when creating it from a
            Graphics object.
            </summary>
        </member>
        <member name="T:System.Drawing.Internal.DeviceContext">
             <summary>
             Represents a Win32 device context.  Provides operations for setting some of the properties of a device context.
             It's the managed wrapper for an HDC.
            
             This class is divided into two files separating the code that needs to be compiled into retail builds and
             debugging code.
             </summary>
        </member>
        <member name="F:System.Drawing.Internal.DeviceContext._hDC">
             <summary>
             This class is a wrapper to a Win32 device context, and the Hdc property is the way to get a
             handle to it.
            
             The hDc is released/deleted only when owned by the object, meaning it was created internally;
             in this case, the object is responsible for releasing/deleting it.
             In the case the object is created from an existing hdc, it is not released; this is consistent
             with the Win32 guideline that says if you call GetDC/CreateDC/CreatIC/CreateEnhMetafile, you are
             responsible for calling ReleaseDC/DeleteDC/DeleteEnhMetafile respectively.
            
             This class implements some of the operations commonly performed on the properties of a dc in WinForms,
             specially for interacting with GDI+, like clipping and coordinate transformation.
             Several properties are not persisted in the dc but instead they are set/reset during a more comprehensive
             operation like text rendering or painting; for instance text alignment is set and reset during DrawText (GDI),
             DrawString (GDI+).
            
             Other properties are persisted from operation to operation until they are reset, like clipping,
             one can make several calls to Graphics or WindowsGraphics object after setting the dc clip area and
             before resetting it; these kinds of properties are the ones implemented in this class.
             This kind of properties place an extra challenge in the scenario where a DeviceContext is obtained
             from a Graphics object that has been used with GDI+, because GDI+ saves the hdc internally, rendering the
             DeviceContext underlying hdc out of sync.  DeviceContext needs to support these kind of properties to
             be able to keep the GDI+ and GDI HDCs in sync.
            
             A few other persisting properties have been implemented in DeviceContext2, among them:
             1. Window origin.
             2. Bounding rectangle.
             3. DC origin.
             4. View port extent.
             5. View port origin.
             6. Window extent
            
             Other non-persisted properties just for information: Background/Foreground color, Palette, Color adjustment,
             Color space, ICM mode and profile, Current pen position, Binary raster op (not supported by GDI+),
             Background mode, Logical Pen, DC pen color, ARc direction, Miter limit, Logical brush, DC brush color,
             Brush origin, Polygon filling mode, Bitmap stretching mode, Logical font, Intercharacter spacing,
             Font mapper flags, Text alignment, Test justification, Layout, Path, Meta region.
             See book "Windows Graphics Programming - Feng Yuang", P315 - Device Context Attributes.
             </summary>
        </member>
        <member name="P:System.Drawing.Internal.DeviceContext.Hdc">
            <summary>
            This object's hdc.  If this property is called, then the object will be used as an HDC wrapper, so the hdc
            is cached and calls to GetHdc/ReleaseHdc won't PInvoke into GDI. Call Dispose to properly release the hdc.
            </summary>
        </member>
        <member name="M:System.Drawing.Internal.DeviceContext.#ctor(System.IntPtr,System.Drawing.Internal.DeviceContextType)">
            <summary>
            Constructor to construct a DeviceContext object from an existing Win32 device context handle.
            </summary>
        </member>
        <member name="M:System.Drawing.Internal.DeviceContext.CreateDC(System.String,System.String,System.String,System.IntPtr)">
            <summary>
            CreateDC creates a DeviceContext object wrapping an hdc created with the Win32 CreateDC function.
            </summary>
        </member>
        <member name="M:System.Drawing.Internal.DeviceContext.CreateIC(System.String,System.String,System.String,System.IntPtr)">
            <summary>
            CreateIC creates a DeviceContext object wrapping an hdc created with the Win32 CreateIC function.
            </summary>
        </member>
        <member name="M:System.Drawing.Internal.DeviceContext.FromHdc(System.IntPtr)">
            <summary>
            Used for wrapping an existing hdc.  In this case, this object doesn't own the hdc so calls to
            GetHdc/ReleaseHdc don't PInvoke into GDI.
            </summary>
        </member>
        <member name="M:System.Drawing.Internal.DeviceContext.RestoreHdc">
            <summary>
            Restores the device context to the specified state. The DC is restored by popping state information off a
            stack created by earlier calls to the SaveHdc function.
            The stack can contain the state information for several instances of the DC. If the state specified by the
            specified parameter is not at the top of the stack, RestoreDC deletes all state information between the top
            of the stack and the specified instance.
            Specifies the saved state to be restored. If this parameter is positive, nSavedDC represents a specific
            instance of the state to be restored. If this parameter is negative, nSavedDC represents an instance relative
            to the current state. For example, -1 restores the most recently saved state.
            See MSDN for more info.
            </summary>
        </member>
        <member name="M:System.Drawing.Internal.DeviceContext.SaveHdc">
            <summary>
            Saves the current state of the device context by copying data describing selected objects and graphic
            modes (such as the bitmap, brush, palette, font, pen, region, drawing mode, and mapping mode) to a
            context stack.
            The SaveDC function can be used any number of times to save any number of instances of the DC state.
            A saved state can be restored by using the RestoreHdc method.
            See MSDN for more details.
            </summary>
        </member>
        <member name="M:System.Drawing.Internal.DeviceContext.SetClip(System.Drawing.Internal.WindowsRegion)">
            <summary>
            Selects a region as the current clipping region for the device context.
            Remarks (From MSDN):
            - Only a copy of the selected region is used. The region itself can be selected for any number of other device contexts or it can be deleted.
            - The SelectClipRgn function assumes that the coordinates for a region are specified in device units.
            - To remove a device-context's clipping region, specify a NULL region handle.
            </summary>
        </member>
        <member name="M:System.Drawing.Internal.DeviceContext.IntersectClip(System.Drawing.Internal.WindowsRegion)">
            <summary>
             Creates a new clipping region from the intersection of the current clipping region and the specified rectangle.
            </summary>
        </member>
        <member name="M:System.Drawing.Internal.DeviceContext.TranslateTransform(System.Int32,System.Int32)">
            <summary>
             Modifies the viewport origin for a device context using the specified horizontal and vertical offsets in
             logical units.
            </summary>
        </member>
        <member name="M:System.Drawing.Internal.DeviceContext.Equals(System.Object)">
            <summary>
            </summary>
        </member>
        <member name="M:System.Drawing.Internal.DeviceContext.GetHashCode">
            <summary>
            This allows collections to treat DeviceContext objects wrapping the same HDC as the same objects.
            </summary>
        </member>
        <member name="T:System.Drawing.Internal.DeviceContexts">
            <summary>
            Keeps a cache of some graphics primitives. Created to improve performance of TextRenderer.MeasureText methods
            that don't receive a WindowsGraphics. This class maintains a cache of MRU WindowsFont objects in the process.
            </summary>
        </member>
        <member name="M:System.Drawing.Internal.DeviceContexts.AddDeviceContext(System.Drawing.Internal.DeviceContext)">
            <summary>
            WindowsGraphicsCacheManager needs to track DeviceContext objects so it can ask them if a font is in use
            before they it's deleted.
            </summary>
        </member>
        <member name="T:System.Drawing.Internal.DeviceContextType">
            <summary>
            Represent the device type the context refers to.
            </summary>
        </member>
        <member name="T:System.Drawing.Internal.WindowsGraphics">
             <summary>
             WindowsGraphics is a library for rendering text and drawing using GDI; it was created to address performance
             and compatibility issues found in GDI+ Graphics class.
            
             Note: WindowsGraphics is a stateful component, DC properties are persisted from method calls, as opposed to
             Graphics (GDI+) which performs atomic operations and always restores the hdc. The underlying hdc is always
             saved and restored on dispose so external HDCs won't be modified by WindowsGraphics. So we don't need to
             restore previous objects into the dc in method calls.
            </summary>
        </member>
        <member name="T:System.Drawing.Internal.WindowsRegion">
            <summary>
            Encapsulates a GDI Region object.
            </summary>
        </member>
        <member name="M:System.Drawing.Internal.WindowsRegion.FromHregion(System.IntPtr,System.Boolean)">
            <summary>
            Creates a WindowsRegion from a region handle, if 'takeOwnership' is true, the handle is added to the
            HandleCollector and is removed &amp; destroyed on dispose.
            </summary>
        </member>
        <member name="M:System.Drawing.Internal.WindowsRegion.FromRegion(System.Drawing.Region,System.Drawing.Graphics)">
            <summary>
            Creates a WindowsRegion from a System.Drawing.Region.
            </summary>
        </member>
        <member name="M:System.Drawing.Internal.WindowsRegion.CombineRegion(System.Drawing.Internal.WindowsRegion,System.Drawing.Internal.WindowsRegion,Interop.Gdi32.CombineMode)">
            <summary>
            Combines region1 &amp; region2 into this region. The regions cannot be null. The three regions need not be
            distinct. For example, the sourceRgn1 can equal this region.
            </summary>
        </member>
        <member name="P:System.Drawing.Internal.WindowsRegion.HRegion">
            <summary>
            The native region handle.
            </summary>
        </member>
        <member name="M:System.Drawing.Internal.WindowsRegion.ToRectangle">
            <summary>
            A rectangle representing the window region set with the SetWindowRgn function.
            </summary>
        </member>
        <member name="T:System.Drawing.Font">
            <summary>
            Defines a particular format for text, including font face, size, and style attributes.
            </summary>
        </member>
        <member name="P:System.Drawing.Font.Size">
            <summary>
            Gets the size of this <see cref='T:System.Drawing.Font'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Font.Style">
            <summary>
            Gets style information for this <see cref='T:System.Drawing.Font'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Font.Bold">
            <summary>
            Gets a value indicating whether this <see cref='T:System.Drawing.Font'/> is bold.
            </summary>
        </member>
        <member name="P:System.Drawing.Font.Italic">
            <summary>
            Gets a value indicating whether this <see cref='T:System.Drawing.Font'/> is Italic.
            </summary>
        </member>
        <member name="P:System.Drawing.Font.Strikeout">
            <summary>
            Gets a value indicating whether this <see cref='T:System.Drawing.Font'/> is strikeout (has a line through it).
            </summary>
        </member>
        <member name="P:System.Drawing.Font.Underline">
            <summary>
            Gets a value indicating whether this <see cref='T:System.Drawing.Font'/> is underlined.
            </summary>
        </member>
        <member name="P:System.Drawing.Font.FontFamily">
            <summary>
            Gets the <see cref='T:System.Drawing.FontFamily'/> of this <see cref='T:System.Drawing.Font'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Font.Name">
            <summary>
            Gets the face name of this <see cref='T:System.Drawing.Font'/> .
            </summary>
        </member>
        <member name="P:System.Drawing.Font.Unit">
            <summary>
            Gets the unit of measure for this <see cref='T:System.Drawing.Font'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Font.GdiCharSet">
             <summary>
             Returns the GDI char set for this instance of a font. This will only
             be valid if this font was created from a classic GDI font definition,
             like a LOGFONT or HFONT, or it was passed into the constructor.
            
             This is here for compatibility with native Win32 intrinsic controls
             on non-Unicode platforms.
             </summary>
        </member>
        <member name="P:System.Drawing.Font.GdiVerticalFont">
             <summary>
             Determines if this font was created to represent a GDI vertical font. This will only be valid if this font
             was created from a classic GDIfont definition, like a LOGFONT or HFONT, or it was passed into the constructor.
            
             This is here for compatibility with native Win32 intrinsic controls on non-Unicode platforms.
             </summary>
        </member>
        <member name="P:System.Drawing.Font.OriginalFontName">
            <summary>
            This property is required by the framework and not intended to be used directly.
            </summary>
        </member>
        <member name="P:System.Drawing.Font.SystemFontName">
            <summary>
            Gets the name of this <see cref='T:System.Drawing.Font'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Font.IsSystemFont">
            <summary>
            Returns true if this <see cref='T:System.Drawing.Font'/> is a SystemFont.
            </summary>
        </member>
        <member name="P:System.Drawing.Font.Height">
            <summary>
            Gets the height of this <see cref='T:System.Drawing.Font'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Font.NativeFont">
            <summary>
            Get native GDI+ object pointer. This property triggers the creation of the GDI+ native object if not initialized yet.
            </summary>
        </member>
        <member name="M:System.Drawing.Font.Finalize">
            <summary>
            Cleans up Windows resources for this <see cref='T:System.Drawing.Font'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Font.Dispose">
            <summary>
            Cleans up Windows resources for this <see cref='T:System.Drawing.Font'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Font.GetHeight(System.Drawing.Graphics)">
            <summary>
            Returns the height of this Font in the specified graphics context.
            </summary>
        </member>
        <member name="M:System.Drawing.Font.Equals(System.Object)">
            <summary>
            Returns a value indicating whether the specified object is a <see cref='T:System.Drawing.Font'/> equivalent to this
            <see cref='T:System.Drawing.Font'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Font.GetHashCode">
            <summary>
            Gets the hash code for this <see cref='T:System.Drawing.Font'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Font.ToString">
            <summary>
            Returns a human-readable string representation of this <see cref='T:System.Drawing.Font'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Font.CreateNativeFont">
            <summary>
             Creates the GDI+ native font object.
            </summary>
        </member>
        <member name="M:System.Drawing.Font.#ctor(System.Drawing.Font,System.Drawing.FontStyle)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Font'/> class from the specified existing <see cref='T:System.Drawing.Font'/>
            and <see cref='T:System.Drawing.FontStyle'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Font.#ctor(System.Drawing.FontFamily,System.Single,System.Drawing.FontStyle,System.Drawing.GraphicsUnit)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Font'/> class with the specified attributes.
            </summary>
        </member>
        <member name="M:System.Drawing.Font.#ctor(System.Drawing.FontFamily,System.Single,System.Drawing.FontStyle,System.Drawing.GraphicsUnit,System.Byte)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Font'/> class with the specified attributes.
            </summary>
        </member>
        <member name="M:System.Drawing.Font.#ctor(System.Drawing.FontFamily,System.Single,System.Drawing.FontStyle,System.Drawing.GraphicsUnit,System.Byte,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Font'/> class with the specified attributes.
            </summary>
        </member>
        <member name="M:System.Drawing.Font.#ctor(System.String,System.Single,System.Drawing.FontStyle,System.Drawing.GraphicsUnit,System.Byte)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Font'/> class with the specified attributes.
            </summary>
        </member>
        <member name="M:System.Drawing.Font.#ctor(System.String,System.Single,System.Drawing.FontStyle,System.Drawing.GraphicsUnit,System.Byte,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Font'/> class with the specified attributes.
            </summary>
        </member>
        <member name="M:System.Drawing.Font.#ctor(System.Drawing.FontFamily,System.Single,System.Drawing.FontStyle)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Font'/> class with the specified attributes.
            </summary>
        </member>
        <member name="M:System.Drawing.Font.#ctor(System.Drawing.FontFamily,System.Single,System.Drawing.GraphicsUnit)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Font'/> class with the specified attributes.
            </summary>
        </member>
        <member name="M:System.Drawing.Font.#ctor(System.Drawing.FontFamily,System.Single)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Font'/> class with the specified attributes.
            </summary>
        </member>
        <member name="M:System.Drawing.Font.#ctor(System.String,System.Single,System.Drawing.FontStyle,System.Drawing.GraphicsUnit)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Font'/> class with the specified attributes.
            </summary>
        </member>
        <member name="M:System.Drawing.Font.#ctor(System.String,System.Single,System.Drawing.FontStyle)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Font'/> class with the specified attributes.
            </summary>
        </member>
        <member name="M:System.Drawing.Font.#ctor(System.String,System.Single,System.Drawing.GraphicsUnit)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Font'/> class with the specified attributes.
            </summary>
        </member>
        <member name="M:System.Drawing.Font.#ctor(System.String,System.Single)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.Font'/> class with the specified attributes.
            </summary>
        </member>
        <member name="M:System.Drawing.Font.#ctor(System.IntPtr,System.Byte,System.Boolean)">
            <summary>
            Constructor to initialize fields from an existing native GDI+ object reference. Used by ToLogFont.
            </summary>
        </member>
        <member name="M:System.Drawing.Font.Initialize(System.String,System.Single,System.Drawing.FontStyle,System.Drawing.GraphicsUnit,System.Byte,System.Boolean)">
            <summary>
            Initializes this object's fields.
            </summary>
        </member>
        <member name="M:System.Drawing.Font.Initialize(System.Drawing.FontFamily,System.Single,System.Drawing.FontStyle,System.Drawing.GraphicsUnit,System.Byte,System.Boolean)">
            <summary>
            Initializes this object's fields.
            </summary>
        </member>
        <member name="M:System.Drawing.Font.FromHfont(System.IntPtr)">
            <summary>
            Creates a <see cref='T:System.Drawing.Font'/> from the specified Windows handle.
            </summary>
        </member>
        <member name="M:System.Drawing.Font.FromLogFont(System.Object)">
            <summary>
            Creates a <see cref="T:System.Drawing.Font"/> from the given LOGFONT using the screen device context.
            </summary>
            <param name="lf">A boxed LOGFONT.</param>
            <returns>The newly created <see cref="T:System.Drawing.Font"/>.</returns>
        </member>
        <member name="M:System.Drawing.Font.FromLogFont(System.Object,System.IntPtr)">
            <summary>
            Creates a <see cref="T:System.Drawing.Font"/> from the given LOGFONT using the given device context.
            </summary>
            <param name="lf">A boxed LOGFONT.</param>
            <param name="hdc">Handle to a device context (HDC).</param>
            <returns>The newly created <see cref="T:System.Drawing.Font"/>.</returns>
        </member>
        <member name="M:System.Drawing.Font.FromHdc(System.IntPtr)">
            <summary>
            Creates a <see cref="T:System.Drawing.Font"/> from the specified handle to a device context (HDC).
            </summary>
            <returns>The newly created <see cref="T:System.Drawing.Font"/>.</returns>
        </member>
        <member name="M:System.Drawing.Font.Clone">
            <summary>
            Creates an exact copy of this <see cref='T:System.Drawing.Font'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.Font.ToHfont">
            <summary>
            Returns a handle to this <see cref='T:System.Drawing.Font'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.Font.SizeInPoints">
            <summary>
            Gets the size, in points, of this <see cref='T:System.Drawing.Font'/>.
            </summary>
        </member>
        <member name="T:System.Drawing.FontFamily">
            <summary>
            Abstracts a group of type faces having a similar basic design but having certain variation in styles.
            </summary>
            <summary>
            Abstracts a group of type faces having a similar basic design but having certain variation in styles.
            </summary>
        </member>
        <member name="M:System.Drawing.FontFamily.#ctor(System.String,System.Boolean)">
             <summary>
             Initializes a new instance of the <see cref='T:System.Drawing.FontFamily'/> class with the specified name.
            
             The <paramref name="createDefaultOnFail"/> parameter determines how errors are handled when creating a
             font based on a font family that does not exist on the end user's system at run time. If this parameter is
             true, then a fall-back font will always be used instead. If this parameter is false, an exception will be thrown.
             </summary>
        </member>
        <member name="M:System.Drawing.FontFamily.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.FontFamily'/> class with the specified name.
            </summary>
        </member>
        <member name="M:System.Drawing.FontFamily.#ctor(System.String,System.Drawing.Text.FontCollection)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.FontFamily'/> class in the specified
            <see cref='T:System.Drawing.Text.FontCollection'/> and with the specified name.
            </summary>
        </member>
        <member name="M:System.Drawing.FontFamily.#ctor(System.Drawing.Text.GenericFontFamilies)">
            <summary>
            Initializes a new instance of the <see cref='T:System.Drawing.FontFamily'/> class from the specified generic font family.
            </summary>
        </member>
        <member name="M:System.Drawing.FontFamily.ToString">
            <summary>
            Converts this <see cref='T:System.Drawing.FontFamily'/> to a human-readable string.
            </summary>
        </member>
        <member name="M:System.Drawing.FontFamily.GetHashCode">
            <summary>
            Gets a hash code for this <see cref='T:System.Drawing.FontFamily'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.FontFamily.Dispose">
            <summary>
            Disposes of this <see cref='T:System.Drawing.FontFamily'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.FontFamily.Name">
            <summary>
            Gets the name of this <see cref='T:System.Drawing.FontFamily'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.FontFamily.GetName(System.Int32)">
            <summary>
            Returns the name of this <see cref='T:System.Drawing.FontFamily'/> in the specified language.
            </summary>
        </member>
        <member name="P:System.Drawing.FontFamily.Families">
            <summary>
            Returns an array that contains all of the <see cref='T:System.Drawing.FontFamily'/> objects associated with the current
            graphics context.
            </summary>
        </member>
        <member name="P:System.Drawing.FontFamily.GenericSansSerif">
            <summary>
            Gets a generic SansSerif <see cref='T:System.Drawing.FontFamily'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.FontFamily.GenericSerif">
            <summary>
            Gets a generic Serif <see cref='T:System.Drawing.FontFamily'/>.
            </summary>
        </member>
        <member name="P:System.Drawing.FontFamily.GenericMonospace">
            <summary>
            Gets a generic monospace <see cref='T:System.Drawing.FontFamily'/>.
            </summary>
        </member>
        <member name="M:System.Drawing.FontFamily.GetFamilies(System.Drawing.Graphics)">
            <summary>
            Returns an array that contains all of the <see cref='T:System.Drawing.FontFamily'/> objects associated with the specified
            graphics context.
            </summary>
        </member>
        <member name="M:System.Drawing.FontFamily.IsStyleAvailable(System.Drawing.FontStyle)">
            <summary>
            Indicates whether the specified <see cref='T:System.Drawing.FontStyle'/> is available.
            </summary>
        </member>
        <member name="M:System.Drawing.FontFamily.GetEmHeight(System.Drawing.FontStyle)">
            <summary>
            Gets the size of the Em square for the specified style in font design units.
            </summary>
        </member>
        <member name="M:System.Drawing.FontFamily.GetCellAscent(System.Drawing.FontStyle)">
            <summary>
            Returns the ascender metric for Windows.
            </summary>
        </member>
        <member name="M:System.Drawing.FontFamily.GetCellDescent(System.Drawing.FontStyle)">
            <summary>
            Returns the descender metric for Windows.
            </summary>
        </member>
        <member name="M:System.Drawing.FontFamily.GetLineSpacing(System.Drawing.FontStyle)">
            <summary>
            Returns the distance between two consecutive lines of text for this <see cref='T:System.Drawing.FontFamily'/> with the
            specified <see cref='T:System.Drawing.FontStyle'/>.
            </summary>
        </member>
        <member name="T:System.Drawing.BitmapSelector">
            <summary>
            Provides methods to select bitmaps.
            </summary>
        </member>
        <member name="M:System.Drawing.BitmapSelector.GetResourceStream(System.Reflection.Assembly,System.Type,System.String)">
            <summary>
            Returns a resource stream loaded from the appropriate location according to the current
            suffix.
            </summary>
            <param name="assembly">The assembly from which the stream is loaded</param>
            <param name="type">The type whose namespace is used to scope the manifest resource name</param>
            <param name="originalName">The name of the manifest resource being requested</param>
            <returns>
            The manifest resource stream corresponding to <paramref name="originalName"/>.
            </returns>
        </member>
        <member name="M:System.Drawing.BitmapSelector.GetResourceStream(System.Type,System.String)">
            <summary>
            Returns a resource stream loaded from the appropriate location according to the current
            suffix.
            </summary>
            <param name="type">The type from whose assembly the stream is loaded and whose namespace is used to scope the resource name</param>
            <param name="originalName">The name of the manifest resource being requested</param>
            <returns>
            The manifest resource stream corresponding to <paramref name="originalName"/>.
            </returns>
        </member>
        <member name="T:System.Drawing.BufferedGraphicsManager">
            <summary>
            The BufferedGraphicsManager is used for accessing a BufferedGraphicsContext.
            </summary>
        </member>
        <member name="M:System.Drawing.BufferedGraphicsManager.#cctor">
            <summary>
            Static constructor.  Here, we hook the exit &amp; unload events so we can clean up our context buffer.
            </summary>
        </member>
        <member name="P:System.Drawing.BufferedGraphicsManager.Current">
            <summary>
            Retrieves the context associated with the app domain.
            </summary>
        </member>
        <member name="M:System.Drawing.BufferedGraphicsManager.OnShutdown(System.Object,System.EventArgs)">
            <summary>
            Called on process exit
            </summary>
        </member>
        <member name="T:System.Drawing.DrawingCom">
             <summary>
             The ComWrappers implementation for System.Drawing.Common's COM interop usages.
            
             Supports IStream and IPicture COM interfaces.
             </summary>
        </member>
        <member name="T:System.Drawing.GraphicsContext">
            <summary>
            Contains information about the context of a Graphics object.
            </summary>
        </member>
        <member name="M:System.Drawing.GraphicsContext.Dispose">
            <summary>
            Disposes this and all contexts up the stack.
            </summary>
        </member>
        <member name="M:System.Drawing.GraphicsContext.Dispose(System.Boolean)">
            <summary>
            Disposes this and all contexts up the stack.
            </summary>
        </member>
        <member name="P:System.Drawing.GraphicsContext.State">
            <summary>
            The state id representing the GraphicsContext.
            </summary>
        </member>
        <member name="P:System.Drawing.GraphicsContext.TransformOffset">
            <summary>
            The translate transform in the GraphicsContext.
            </summary>
        </member>
        <member name="P:System.Drawing.GraphicsContext.Clip">
            <summary>
            The clipping region the GraphicsContext.
            </summary>
        </member>
        <member name="P:System.Drawing.GraphicsContext.Next">
            <summary>
            The next GraphicsContext object in the stack.
            </summary>
        </member>
        <member name="P:System.Drawing.GraphicsContext.Previous">
            <summary>
            The previous GraphicsContext object in the stack.
            </summary>
        </member>
        <member name="P:System.Drawing.GraphicsContext.IsCumulative">
            <summary>
            Flag that determines whether the context was created for a Graphics.Save() operation.
            This kind of contexts are cumulative across subsequent Save() calls so the top context
            info is cumulative.  This is not the same for contexts created for a Graphics.BeginContainer()
            operation, in this case the new context information is reset.  See Graphics.BeginContainer()
            and Graphics.Save() for more information.
            </summary>
        </member>
        <member name="T:System.Drawing.PrintPreviewGraphics">
            <summary>
            Retrieves the printer graphics during preview.
            </summary>
        </member>
        <member name="P:System.Drawing.PrintPreviewGraphics.VisibleClipBounds">
            <summary>
            Gets the Visible bounds of this graphics object. Used during print preview.
            </summary>
        </member>
        <member name="T:System.Drawing.ScreenDC">
            <summary>
            Simple wrapper to create a screen HDC within a using statement.
            </summary>
        </member>
        <member name="T:System.Drawing.ToolboxBitmapAttribute">
            <summary>
            ToolboxBitmapAttribute defines the images associated with a specified component.
            The component can offer a small and large image (large is optional).
            </summary>
        </member>
        <member name="M:System.Text.ValueStringBuilder.GetPinnableReference">
            <summary>
            Get a pinnable reference to the builder.
            Does not ensure there is a null char after <see cref="P:System.Text.ValueStringBuilder.Length"/>
            This overload is pattern matched in the C# 7.3+ compiler so you can omit
            the explicit method call, and write eg "fixed (char* c = builder)"
            </summary>
        </member>
        <member name="M:System.Text.ValueStringBuilder.GetPinnableReference(System.Boolean)">
            <summary>
            Get a pinnable reference to the builder.
            </summary>
            <param name="terminate">Ensures that the builder has a null char after <see cref="P:System.Text.ValueStringBuilder.Length"/></param>
        </member>
        <member name="P:System.Text.ValueStringBuilder.RawChars">
            <summary>Returns the underlying storage of the builder.</summary>
        </member>
        <member name="M:System.Text.ValueStringBuilder.AsSpan(System.Boolean)">
            <summary>
            Returns a span around the contents of the builder.
            </summary>
            <param name="terminate">Ensures that the builder has a null char after <see cref="P:System.Text.ValueStringBuilder.Length"/></param>
        </member>
        <member name="M:System.Text.ValueStringBuilder.Grow(System.Int32)">
            <summary>
            Resize the internal buffer either by doubling current buffer size or
            by adding <paramref name="additionalCapacityBeyondPos"/> to
            <see cref="F:System.Text.ValueStringBuilder._pos"/> whichever is greater.
            </summary>
            <param name="additionalCapacityBeyondPos">
            Number of chars requested beyond current position.
            </param>
        </member>
        <member name="T:System.Windows.Forms.DpiHelper">
            <summary>
            Helper class for scaling coordinates and images according to current DPI scaling set in Windows for the primary screen.
            </summary>
        </member>
        <member name="F:System.Windows.Forms.DpiHelper.s_deviceDpiX">
            <summary>
            The primary screen's (device) current horizontal DPI
            </summary>
        </member>
        <member name="F:System.Windows.Forms.DpiHelper.s_deviceDpiY">
            <summary>
            The primary screen's (device) current vertical DPI
            </summary>
        </member>
        <member name="P:System.Windows.Forms.DpiHelper.IsScalingRequired">
            <summary>
            Returns whether scaling is required when converting between logical-device units,
            if the application opted in the automatic scaling in the .config file.
            </summary>
        </member>
        <member name="M:System.Windows.Forms.DpiHelper.LogicalToDeviceUnitsX(System.Int32)">
            <summary>
            Transforms a horizontal integer coordinate from logical to device units
            by scaling it up  for current DPI and rounding to nearest integer value
            Note: this method should be called only inside an if (DpiHelper.IsScalingRequired) clause
            </summary>
            <param name="value">The horizontal value in logical units</param>
            <returns>The horizontal value in device units</returns>
        </member>
        <member name="M:System.Windows.Forms.DpiHelper.LogicalToDeviceUnitsY(System.Int32)">
            <summary>
            Transforms a vertical integer coordinate from logical to device units
            by scaling it up  for current DPI and rounding to nearest integer value
            Note: this method should be called only inside an if (DpiHelper.IsScalingRequired) clause
            </summary>
            <param name="value">The vertical value in logical units</param>
            <returns>The vertical value in device units</returns>
        </member>
        <member name="M:System.Windows.Forms.DpiHelper.LogicalToDeviceUnits(System.Drawing.Size)">
            <summary>
            Returns a new Size with the input's
            dimensions converted from logical units to device units.
            Note: this method should be called only inside an if (DpiHelper.IsScalingRequired) clause
            </summary>
            <param name="logicalSize">Size in logical units</param>
            <returns>Size in device units</returns>
        </member>
        <member name="M:System.Windows.Forms.DpiHelper.CreateResizedBitmap(System.Drawing.Bitmap,System.Drawing.Size)">
            <summary>
            Create and return a new bitmap scaled to the specified size.
            Note: this method should be called only inside an if (DpiHelper.IsScalingRequired) clause
            </summary>
            <param name="logicalImage">The image to scale from logical units to device units</param>
            <param name="targetImageSize">The size to scale image to</param>
        </member>
        <member name="M:System.Windows.Forms.DpiHelper.ScaleBitmapLogicalToDevice(System.Drawing.Bitmap@)">
            <summary>
            Create a new bitmap scaled for the device units.
            When displayed on the device, the scaled image will have same size as the original image would have when displayed at 96dpi.
            Note: this method should be called only inside an if (DpiHelper.IsScalingRequired) clause
            </summary>
            <param name="logicalBitmap">The image to scale from logical units to device units</param>
        </member>
        <member name="P:System.SR.CantTellPrinterName">
            <summary>(printer name protected due to security restrictions)</summary>
        </member>
        <member name="P:System.SR.CantChangeImmutableObjects">
            <summary>Changes cannot be made to {0} because permissions are not valid.</summary>
        </member>
        <member name="P:System.SR.CantMakeIconTransparent">
            <summary>Bitmaps that are icons cannot be made transparent. Icons natively support transparency. Use the Icon constructor to create an icon.</summary>
        </member>
        <member name="P:System.SR.ColorNotSystemColor">
            <summary>The color {0} is not a system color.</summary>
        </member>
        <member name="P:System.SR.DotNET_ComponentType">
            <summary>.NET Component</summary>
        </member>
        <member name="P:System.SR.GdiplusAborted">
            <summary>Function was ended.</summary>
        </member>
        <member name="P:System.SR.GdiplusAccessDenied">
            <summary>File access is denied.</summary>
        </member>
        <member name="P:System.SR.GdiplusCannotCreateGraphicsFromIndexedPixelFormat">
            <summary>A Graphics object cannot be created from an image that has an indexed pixel format.</summary>
        </member>
        <member name="P:System.SR.GdiplusCannotSetPixelFromIndexedPixelFormat">
            <summary>SetPixel is not supported for images with indexed pixel formats.</summary>
        </member>
        <member name="P:System.SR.GdiplusDestPointsInvalidParallelogram">
            <summary>Destination points define a parallelogram which must have a length of 3. These points will represent the upper-left, upper-right, and lower-left coordinates (defined in that order).</summary>
        </member>
        <member name="P:System.SR.GdiplusDestPointsInvalidLength">
            <summary>Destination points must be an array with a length of 3 or 4. A length of 3 defines a parallelogram with the upper-left, upper-right, and lower-left corners. A length of 4 defines a quadrilateral with the fourth element of the array specifying the lower-rig ...</summary>
        </member>
        <member name="P:System.SR.GdiplusFileNotFound">
            <summary>File not found.</summary>
        </member>
        <member name="P:System.SR.GdiplusFontFamilyNotFound">
            <summary>Font '{0}' cannot be found.</summary>
        </member>
        <member name="P:System.SR.GdiplusFontStyleNotFound">
            <summary>Font '{0}' does not support style '{1}'.</summary>
        </member>
        <member name="P:System.SR.GdiplusGenericError">
            <summary>A generic error occurred in GDI+.</summary>
        </member>
        <member name="P:System.SR.GdiplusInsufficientBuffer">
            <summary>Buffer is too small (internal GDI+ error).</summary>
        </member>
        <member name="P:System.SR.GdiplusInvalidParameter">
            <summary>Parameter is not valid.</summary>
        </member>
        <member name="P:System.SR.GdiplusInvalidRectangle">
            <summary>Rectangle '{0}' cannot have a width or height equal to 0.</summary>
        </member>
        <member name="P:System.SR.GdiplusInvalidSize">
            <summary>Operation requires a transformation of the image from GDI+ to GDI. GDI does not support images with a width or height greater than 32767.</summary>
        </member>
        <member name="P:System.SR.GdiplusOutOfMemory">
            <summary>Out of memory.</summary>
        </member>
        <member name="P:System.SR.GdiplusNotImplemented">
            <summary>Not implemented.</summary>
        </member>
        <member name="P:System.SR.GdiplusNotInitialized">
            <summary>GDI+ is not properly initialized (internal GDI+ error).</summary>
        </member>
        <member name="P:System.SR.GdiplusNotTrueTypeFont">
            <summary>Only TrueType fonts are supported. '{0}' is not a TrueType font.</summary>
        </member>
        <member name="P:System.SR.GdiplusNotTrueTypeFont_NoName">
            <summary>Only TrueType fonts are supported. This is not a TrueType font.</summary>
        </member>
        <member name="P:System.SR.GdiplusObjectBusy">
            <summary>Object is currently in use elsewhere.</summary>
        </member>
        <member name="P:System.SR.GdiplusOverflow">
            <summary>Overflow error.</summary>
        </member>
        <member name="P:System.SR.GdiplusPropertyNotFoundError">
            <summary>Property cannot be found.</summary>
        </member>
        <member name="P:System.SR.GdiplusPropertyNotSupportedError">
            <summary>Property is not supported.</summary>
        </member>
        <member name="P:System.SR.GdiplusUnknown">
            <summary>Unknown GDI+ error occurred.</summary>
        </member>
        <member name="P:System.SR.GdiplusUnknownImageFormat">
            <summary>Image format is unknown.</summary>
        </member>
        <member name="P:System.SR.GdiplusUnsupportedGdiplusVersion">
            <summary>Current version of GDI+ does not support this feature.</summary>
        </member>
        <member name="P:System.SR.GdiplusWrongState">
            <summary>Bitmap region is already locked.</summary>
        </member>
        <member name="P:System.SR.GlobalAssemblyCache">
            <summary>(Global Assembly Cache)</summary>
        </member>
        <member name="P:System.SR.GraphicsBufferCurrentlyBusy">
            <summary>BufferedGraphicsContext cannot be disposed of because a buffer operation is currently in progress.</summary>
        </member>
        <member name="P:System.SR.GraphicsBufferQueryFail">
            <summary>Screen-compatible bitmap cannot be created. The screen bitmap format cannot be determined.</summary>
        </member>
        <member name="P:System.SR.IconInvalidMaskLength">
            <summary>'{0}' data length expected {1}, read {2}</summary>
        </member>
        <member name="P:System.SR.ToolboxItemLocked">
            <summary>Toolbox item cannot be modified.</summary>
        </member>
        <member name="P:System.SR.ToolboxItemInvalidPropertyType">
            <summary>Property {0} requires an argument of type {1}.</summary>
        </member>
        <member name="P:System.SR.ToolboxItemValueNotSerializable">
            <summary>Data type {0} is not serializable. Items added to a property dictionary must be serializable.</summary>
        </member>
        <member name="P:System.SR.ToolboxItemInvalidKey">
            <summary>Argument should be a non-empty string.</summary>
        </member>
        <member name="P:System.SR.IllegalState">
            <summary>Internal state of the {0} class is invalid.</summary>
        </member>
        <member name="P:System.SR.InterpolationColorsColorBlendNotSet">
            <summary>Property must be set to a valid ColorBlend object to use interpolation colors.</summary>
        </member>
        <member name="P:System.SR.InterpolationColorsCommon">
            <summary>{0}{1} ColorBlend objects must be constructed with the same number of positions and color values. Positions must be between 0.0 and 1.0, 1.0 indicating the last element in the array.</summary>
        </member>
        <member name="P:System.SR.InterpolationColorsInvalidColorBlendObject">
            <summary>ColorBlend object that was set is not valid.</summary>
        </member>
        <member name="P:System.SR.InterpolationColorsInvalidStartPosition">
            <summary>Position's first element must be equal to 0.</summary>
        </member>
        <member name="P:System.SR.InterpolationColorsInvalidEndPosition">
            <summary>Position's last element must be equal to 1.0.</summary>
        </member>
        <member name="P:System.SR.InterpolationColorsLength">
            <summary>Array of colors and positions must contain at least two elements.</summary>
        </member>
        <member name="P:System.SR.InterpolationColorsLengthsDiffer">
            <summary>Colors and positions do not have the same number of elements.</summary>
        </member>
        <member name="P:System.SR.InvalidArgumentValue">
            <summary>Value of '{1}' is not valid for '{0}'.</summary>
        </member>
        <member name="P:System.SR.InvalidArgumentValueFontConverter">
            <summary>Value of '{0}' is not valid for font size unit.</summary>
        </member>
        <member name="P:System.SR.InvalidBoundArgument">
            <summary>Value of '{1}' is not valid for '{0}'. '{0}' should be greater than {2} and less than or equal to {3}.</summary>
        </member>
        <member name="P:System.SR.InvalidClassName">
            <summary>Class name is not valid.</summary>
        </member>
        <member name="P:System.SR.InvalidColor">
            <summary>Color '{0}' is not valid.</summary>
        </member>
        <member name="P:System.SR.InvalidDashPattern">
            <summary>DashPattern value is not valid.</summary>
        </member>
        <member name="P:System.SR.InvalidEx2BoundArgument">
            <summary>Value of '{1}' is not valid for '{0}'. '{0}' should be greater than or equal to {2} and less than or equal to {3}.</summary>
        </member>
        <member name="P:System.SR.InvalidGDIHandle">
            <summary>Win32 handle that was passed to {0} is not valid or is the wrong type.</summary>
        </member>
        <member name="P:System.SR.InvalidImage">
            <summary>Image type is unknown.</summary>
        </member>
        <member name="P:System.SR.InvalidLowBoundArgumentEx">
            <summary>Value of '{1}' is not valid for '{0}'. '{0}' must be greater than or equal to {2}.</summary>
        </member>
        <member name="P:System.SR.InvalidPermissionLevel">
            <summary>Permission level is not valid.</summary>
        </member>
        <member name="P:System.SR.InvalidPermissionState">
            <summary>Permission state is not valid.</summary>
        </member>
        <member name="P:System.SR.InvalidPictureType">
            <summary>Argument '{0}' must be a picture that can be used as a {1}.</summary>
        </member>
        <member name="P:System.SR.InvalidPrinterException_InvalidPrinter">
            <summary>Settings to access printer '{0}' are not valid.</summary>
        </member>
        <member name="P:System.SR.InvalidPrinterException_NoDefaultPrinter">
            <summary>No printers are installed.</summary>
        </member>
        <member name="P:System.SR.InvalidPrinterHandle">
            <summary>Handle {0} is not valid.</summary>
        </member>
        <member name="P:System.SR.ValidRangeX">
            <summary>Parameter must be positive and &lt; Width.</summary>
        </member>
        <member name="P:System.SR.ValidRangeY">
            <summary>Parameter must be positive and &lt; Height.</summary>
        </member>
        <member name="P:System.SR.NativeHandle0">
            <summary>Native handle is 0.</summary>
        </member>
        <member name="P:System.SR.NoDefaultPrinter">
            <summary>Default printer is not set.</summary>
        </member>
        <member name="P:System.SR.NotImplemented">
            <summary>Not implemented.</summary>
        </member>
        <member name="P:System.SR.PDOCbeginPrintDescr">
            <summary>Occurs when the document is about to be printed.</summary>
        </member>
        <member name="P:System.SR.PDOCdocumentNameDescr">
            <summary>The name of the document shown to the user.</summary>
        </member>
        <member name="P:System.SR.PDOCdocumentPageSettingsDescr">
            <summary>The page settings of the page currently being printed.</summary>
        </member>
        <member name="P:System.SR.PDOCendPrintDescr">
            <summary>Occurs after the document has been printed.</summary>
        </member>
        <member name="P:System.SR.PDOCoriginAtMarginsDescr">
            <summary>Indicates that the graphics origin is located at the user-specified page margins.</summary>
        </member>
        <member name="P:System.SR.PDOCprintControllerDescr">
            <summary>Retrieves the print controller for this document.</summary>
        </member>
        <member name="P:System.SR.PDOCprintPageDescr">
            <summary>Occurs once for each page to be printed.</summary>
        </member>
        <member name="P:System.SR.PDOCprinterSettingsDescr">
            <summary>Retrieves the settings for the printer the document is currently being printed to.</summary>
        </member>
        <member name="P:System.SR.PDOCqueryPageSettingsDescr">
            <summary>Occurs before each page is printed.  Useful for changing PageSettings for a particular page.</summary>
        </member>
        <member name="P:System.SR.PlatformNotSupported_Drawing">
            <summary>System.Drawing is not supported on this platform.</summary>
        </member>
        <member name="P:System.SR.PlatformNotSupported_Unix">
            <summary>System.Drawing.Common is not supported on non-Windows platforms. See https://aka.ms/systemdrawingnonwindows for more information.</summary>
        </member>
        <member name="P:System.SR.PrintDocumentDesc">
            <summary>Defines an object that sends output to a printer.</summary>
        </member>
        <member name="P:System.SR.PrintingPermissionBadXml">
            <summary>XML is not valid.</summary>
        </member>
        <member name="P:System.SR.PrintingPermissionAttributeInvalidPermissionLevel">
            <summary>Permission level must be between PrintingPermissionLevel.NoPrinting and PrintingPermissionLevel.AllPrinting.</summary>
        </member>
        <member name="P:System.SR.PropertyValueInvalidEntry">
            <summary>IDictionary parameter contains at least one entry that is not valid. Ensure all values are consistent with the object's properties.</summary>
        </member>
        <member name="P:System.SR.PSizeNotCustom">
            <summary>PaperSize cannot be changed unless the Kind property is set to Custom.</summary>
        </member>
        <member name="P:System.SR.ResourceNotFound">
            <summary>Resource '{1}' cannot be found in class '{0}'.</summary>
        </member>
        <member name="P:System.SR.TargetNotPrintingPermission">
            <summary>Target does not have permission to print.</summary>
        </member>
        <member name="P:System.SR.TextParseFailedFormat">
            <summary>Text "{0}" cannot be parsed. The expected text format is "{1}".</summary>
        </member>
        <member name="P:System.SR.TriStateCompareError">
            <summary>TriState.Default cannot be converted into a Boolean.</summary>
        </member>
        <member name="P:System.SR.toStringIcon">
            <summary>(Icon)</summary>
        </member>
        <member name="P:System.SR.toStringNone">
            <summary>(none)</summary>
        </member>
        <member name="P:System.SR.DCTypeInvalid">
            <summary>GetObjectType on this dc returned an invalid value.</summary>
        </member>
        <member name="P:System.SR.InvalidEnumArgument">
            <summary>The value of argument '{0}' ({1}) is invalid for Enum type '{2}'.</summary>
        </member>
        <member name="P:System.SR.ConvertInvalidPrimitive">
            <summary>{0} is not a valid value for {1}.</summary>
        </member>
        <member name="P:System.SR.BlendObjectMustHaveTwoElements">
            <summary>Invalid Blend object. It should have at least 2 elements in each of the factors and positions arrays.</summary>
        </member>
        <member name="P:System.SR.BlendObjectFirstElementInvalid">
            <summary>Invalid Blend object. The positions array must have 0.0 as its first element.</summary>
        </member>
        <member name="P:System.SR.BlendObjectLastElementInvalid">
            <summary>Invalid Blend object. The positions array must have 1.0 as its last element.</summary>
        </member>
        <member name="P:System.SR.DllNotFoundExceptionMessage">
            <summary>Unable to load DLL '{0}': The specified module could not be found.</summary>
        </member>
        <member name="P:System.SR.EntryPointNotFoundExceptionMessage">
            <summary>Unable to find an entry point named '{0}' in DLL '{1}'.</summary>
        </member>
        <member name="P:System.SR.AvailableOnlyOnWMF">
            <summary>{0} only available on WMF files.</summary>
        </member>
        <member name="P:System.SR.CannotCreateGraphics">
            <summary>Cannot create Graphics from an indexed bitmap.</summary>
        </member>
        <member name="P:System.SR.CouldNotOpenDisplay">
            <summary>Could not open display (X-Server required. Check your DISPLAY environment variable)</summary>
        </member>
        <member name="P:System.SR.CouldntFindSpecifiedFile">
            <summary>Couldn't find specified file.</summary>
        </member>
        <member name="P:System.SR.IconInstanceWasDisposed">
            <summary>Icon instance was disposed.</summary>
        </member>
        <member name="P:System.SR.InvalidGraphicsUnit">
            <summary>Invalid GraphicsUnit</summary>
        </member>
        <member name="P:System.SR.InvalidThumbnailSize">
            <summary>Invalid thumbnail size</summary>
        </member>
        <member name="P:System.SR.NoCodecAvailableForFormat">
            <summary>No codec available for format:{0}</summary>
        </member>
        <member name="P:System.SR.NotImplementedUnderX11">
            <summary>Operation not implemented under X11</summary>
        </member>
        <member name="P:System.SR.none">
            <summary>(none)</summary>
        </member>
        <member name="P:System.SR.NoValidIconImageFound">
            <summary>No valid icon image found</summary>
        </member>
        <member name="P:System.SR.NullOrEmptyPath">
            <summary>Null or empty path.</summary>
        </member>
        <member name="P:System.SR.NumberOfPointsAndTypesMustBeSame">
            <summary>Invalid parameter passed. Number of points and types must be same.</summary>
        </member>
        <member name="P:System.SR.ObjectDisposed">
            <summary>Object has been disposed.</summary>
        </member>
        <member name="P:System.SR.ValueLessThenZero">
            <summary>The value of the {0} property is less than zero.</summary>
        </member>
        <member name="P:System.SR.ValueNotOneOfValues">
            <summary>The value of the {0} property is not one of the {1} values</summary>
        </member>
        <member name="P:System.SR.TargetDirectoryDoesNotExist">
            <summary>The directory {0} of the filename {1} does not exist.</summary>
        </member>
        <member name="P:System.SR.SystemDrawingCommon_PlatformNotSupported">
            <summary>System.Drawing.Common is not supported on this platform.</summary>
        </member>
        <member name="T:Interop.Ole32.STATSTG">
            <summary>
            Statistics for <see cref="T:Interop.Ole32.IStream"/>.
            <see href="https://docs.microsoft.com/en-us/windows/desktop/api/objidl/ns-objidl-tagstatstg"/>
            </summary>
            <remarks>
            The definition in <see cref="N:System.Runtime.InteropServices.ComTypes"/> isn't blittable.
            </remarks>
        </member>
        <member name="F:Interop.Ole32.STATSTG.pwcsName">
            <summary>
            Pointer to the name.
            </summary>
        </member>
        <member name="F:Interop.Ole32.STATSTG.cbSize">
            <summary>
            Size of the stream in bytes.
            </summary>
        </member>
        <member name="F:Interop.Ole32.STATSTG.grfMode">
            <summary>
            The stream mode.
            </summary>
        </member>
        <member name="F:Interop.Ole32.STATSTG.grfLocksSupported">
            <summary>
            Supported locking modes.
            <see href="https://docs.microsoft.com/en-us/windows/desktop/api/objidl/ne-objidl-taglocktype"/>
            </summary>
            <remarks>
            '0' means does not support lock modes.
            </remarks>
        </member>
        <member name="F:Interop.Ole32.STATSTG.clsid">
            <remarks>
            Only for IStorage objects
            </remarks>
        </member>
        <member name="F:Interop.Ole32.STATSTG.grfStateBits">
            <remarks>
            Only valid for IStorage objects.
            </remarks>
        </member>
        <member name="M:Interop.Ole32.STATSTG.FreeName">
            <summary>
            Caller is responsible for freeing the name memory.
            </summary>
        </member>
        <member name="M:Interop.Ole32.STATSTG.AllocName(System.String)">
            <summary>
            Callee is repsonsible for allocating the name memory.
            </summary>
        </member>
        <member name="T:Interop.Ole32.STGTY">
            <summary>
            Type of the storage element. Used with <see cref="T:Interop.Ole32.STATSTG"/>.
            <see href="https://docs.microsoft.com/en-us/windows/desktop/api/objidl/ne-objidl-tagstgty"/>
            </summary>
        </member>
        <member name="T:Interop.Ole32.STATFLAG">
            <summary>
            Stat flags for <see cref="M:Interop.Ole32.IStream.Stat(Interop.Ole32.STATSTG*,Interop.Ole32.STATFLAG)"/>.
            <see href="https://docs.microsoft.com/en-us/windows/desktop/api/wtypes/ne-wtypes-tagstatflag"/>
            </summary>
        </member>
        <member name="F:Interop.Ole32.STATFLAG.STATFLAG_DEFAULT">
            <summary>
            Stat includes the name.
            </summary>
        </member>
        <member name="F:Interop.Ole32.STATFLAG.STATFLAG_NONAME">
            <summary>
            Stat doesn't include the name.
            </summary>
        </member>
        <member name="T:Interop.Ole32.STGM">
            <summary>
            Stream / storage modes.
            <see href="https://docs.microsoft.com/en-us/windows/desktop/Stg/stgm-constants"/>
            </summary>
        </member>
        <member name="F:Interop.Ole32.STGM.Default">
            <summary>
            Read only, and each change to a storage or stream element is written as it occurs.
            Fails if the given storage object already exists.
            [STGM_DIRECT] [STGM_READ] [STGM_FAILIFTHERE] [STGM_SHARE_DENY_WRITE]
            </summary>
        </member>
        <member name="T:Interop.Ole32.IStream">
            <summary>
            IStream interface. <see href="https://docs.microsoft.com/windows/desktop/api/objidl/nn-objidl-istream"/>
            </summary>
            <remarks>
            This interface explicitly doesn't use the built-in COM support, but instead is only used with ComWrappers.
            </remarks>
        </member>
    </members>
</doc>
