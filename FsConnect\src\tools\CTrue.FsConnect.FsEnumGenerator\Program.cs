﻿using System;
using System.CodeDom.Compiler;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using CommandLine;
using CommandLine.Text;

namespace CTrue.FsConnect.FsEnumGenerator
{
    class Program
    {
        static void Main(string[] args)
        {
            Parser parser = new Parser(with => with.HelpWriter = null);
            var parserResult = parser.ParseArguments<Options>(args);

            parserResult
                .WithParsed(Run)
                .WithNotParsed(errs => DisplayHelp(parserResult, errs));
        }

        static void DisplayHelp<T>(ParserResult<T> result, IEnumerable<Error> errs)
        {
            HelpText helpText;
            if (errs.IsVersion())  //check if error is version request
                helpText = HelpText.AutoBuild(result);
            else
            {
                helpText = HelpText.AutoBuild(result, h =>
                {
                    //configure help
                    h.AdditionalNewLineAfterOption = false;
                    h.Heading = "Flight Simulator Enum Generator";
                    h.Copyright = "";
                    return HelpText.DefaultParsingErrorsHandler(result, h);
                }, e => e);
            }
            Console.WriteLine(helpText);
        }

        private static void Run(Options options)
        {
            string[] eventLines = File.ReadAllLines(options.SourceFileName);

            var fsEventList = GetFsEvents(eventLines);

            GenerateFsEnumsFile(fsEventList, options.TargetFileName);

        }

        static List<FsEventInfo> GetFsEvents(string[] lines)
        {
            var list = new List<FsEventInfo>();

            foreach (var eventLine in lines)
            {
                string[] splits = eventLine.Split('\t');

                if(splits.Length != 4) continue;
                if(splits[0] == "Event ID") continue;
                if(splits[1] == "Unsupported") continue;
                if(splits[1] == "Not supported") continue;

                string[] nameSplit = splits[0].Split(' ');

                string name = splits[1];
                string id = "";
                string description = splits[2];

                // Remove training parts of names with spaces
                if (nameSplit.Length > 0)
                    name = nameSplit[0];
                else
                    name = splits[1];

                name = name.Trim(new[] {','});

                // Remove names starting with KEY_ with seems to be the old FSX event names
                if (name.StartsWith("key_", StringComparison.InvariantCultureIgnoreCase))
                    name = name.Substring(4);

                id = GetFsEventNameId(name);

                description = description.Replace(">", "&gt;");
                description = description.Replace("<", "&lt;");

                // Remove duplicates
                if (list.Exists(x => x.Id.Equals(id))) continue;

                list.Add(new FsEventInfo()
                {
                    Id = id,
                    Name = name,
                    Description = description
                });
            }

            return list;
        }

        private static void GenerateFsEnumsFile(List<FsEventInfo> fsEventList, string fileName)
        {
            System.IO.StringWriter baseTextWriter = new System.IO.StringWriter();
            System.CodeDom.Compiler.IndentedTextWriter indentWriter = new IndentedTextWriter(baseTextWriter, "    ");

            // Sets the indentation level.
            indentWriter.Indent = 0;
            indentWriter.WriteLine($"// Generated by FsEnumGenerator on {DateTime.Now:s}");
            indentWriter.WriteLine("namespace CTrue.FsConnect");
            indentWriter.WriteLine("{");
            indentWriter.Indent++;

            indentWriter.WriteLine("/// <summary>");
            indentWriter.WriteLine("/// The <see cref=\"FsEventNameId\"/> enum contains all known events.");
            indentWriter.WriteLine("/// </summary>");
            indentWriter.WriteLine("/// <remarks>");
            indentWriter.WriteLine("/// Note: This list is based on known legacy events. Not all events are supported by Microsoft Flight Simulator 2020.");
            indentWriter.WriteLine("/// </remarks>");
            indentWriter.WriteLine("public enum FsEventNameId");
            indentWriter.WriteLine("{");
            indentWriter.Indent++;

            foreach (var fsEvent in fsEventList)
            {
                indentWriter.WriteLine($"/// <summary>");
                indentWriter.WriteLine($"/// {fsEvent.Description}");
                indentWriter.WriteLine($"/// </summary>");
                indentWriter.WriteLine($"{fsEvent.Id},");
            }

            indentWriter.Indent--;
            indentWriter.WriteLine("};");

            indentWriter.WriteLine();

            indentWriter.WriteLine($"/// <summary>");
            indentWriter.WriteLine($"/// Provides lookup of FsEventNameId enums to event names that can be registered in MSFS.");
            indentWriter.WriteLine($"/// </summary>");
            indentWriter.WriteLine("internal static class FsEventNameLookup");
            indentWriter.WriteLine("{");
            indentWriter.Indent++;

            indentWriter.WriteLine("private static string[] _fsEventName = new string[] {");
            indentWriter.Indent++;

            foreach (var fsEvent in fsEventList)
            {
                indentWriter.WriteLine($"\"{fsEvent.Name}\",");
            }

            indentWriter.Indent--;
            indentWriter.WriteLine("};");

            indentWriter.WriteLine();
            indentWriter.WriteLine($"/// <summary>");
            indentWriter.WriteLine($"/// Returns an event name for a given event name id.");
            indentWriter.WriteLine($"/// </summary>");
            indentWriter.WriteLine($"/// <param name=\"eventNameId\">An <see cref=\"FsEventNameId\"/> representing a MSFS event.</param>");
            indentWriter.WriteLine("public static string GetFsEventName(FsEventNameId eventNameId)");
            indentWriter.WriteLine("{");
            indentWriter.Indent++;

            indentWriter.WriteLine("return _fsEventName[(int)eventNameId];");

            indentWriter.Indent--;
            indentWriter.WriteLine("}");

            indentWriter.Indent--;
            indentWriter.WriteLine("}");

            indentWriter.Indent--;
            indentWriter.WriteLine("}");

            File.WriteAllText(fileName, baseTextWriter.ToString());
        }

        private static string GetFsEventNameId(string name)
        {
            string id = name.ToPascalCase();

            if (id.StartsWith("key", StringComparison.InvariantCultureIgnoreCase))
                id = id.Substring(3);

            return id;
        }
    }

    internal class FsEventInfo
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
    }

    public static class ExtensionMethods
    {
        public static string ToUnderscoreCase(this string str)
        {
            return string.Concat(str.Select((x, i) => i > 0 && char.IsUpper(x) ? "_" + x.ToString() : x.ToString())).ToLower();
        }

        public static string ToPascalCase(this string name)
        {
            if (string.IsNullOrEmpty(name))
                return name;

            var builder = new StringBuilder(name.Length + Math.Min(2, name.Length / 5));
            bool lastCharacterUnderscore = true;

            for (var currentIndex = 0; currentIndex < name.Length; currentIndex++)
            {
                var currentChar = name[currentIndex];
                if (currentChar == '_')
                {
                    lastCharacterUnderscore = true;
                    continue;
                }

                if (lastCharacterUnderscore)
                {
                    currentChar = char.ToUpper(currentChar);
                    lastCharacterUnderscore = false;
                }
                else
                    currentChar = char.ToLower(currentChar);

                builder.Append(currentChar);
            }

            return builder.ToString();
        }

        public static string ToSnakeCase(this string name)
        {
            if (string.IsNullOrEmpty(name))
                return name;

            var builder = new StringBuilder(name.Length + Math.Min(2, name.Length / 5));
            var previousCategory = default(UnicodeCategory?);

            for (var currentIndex = 0; currentIndex < name.Length; currentIndex++)
            {
                var currentChar = name[currentIndex];
                if (currentChar == '_')
                {
                    builder.Append('_');
                    previousCategory = null;
                    continue;
                }

                var currentCategory = char.GetUnicodeCategory(currentChar);
                switch (currentCategory)
                {
                    case UnicodeCategory.UppercaseLetter:
                    case UnicodeCategory.TitlecaseLetter:
                        if (previousCategory == UnicodeCategory.SpaceSeparator ||
                            previousCategory == UnicodeCategory.LowercaseLetter ||
                            previousCategory != UnicodeCategory.DecimalDigitNumber &&
                            previousCategory != null &&
                            currentIndex > 0 &&
                            currentIndex + 1 < name.Length &&
                            char.IsLower(name[currentIndex + 1]))
                        {
                            builder.Append('_');
                        }

                        currentChar = char.ToLower(currentChar);
                        break;

                    case UnicodeCategory.LowercaseLetter:
                    case UnicodeCategory.DecimalDigitNumber:
                        if (previousCategory == UnicodeCategory.SpaceSeparator)
                            builder.Append('_');
                        break;

                    default:
                        if (previousCategory != null)
                            previousCategory = UnicodeCategory.SpaceSeparator;
                        continue;
                }

                builder.Append(currentChar);
                previousCategory = currentCategory;
            }

            return builder.ToString();
        }
    }

}
