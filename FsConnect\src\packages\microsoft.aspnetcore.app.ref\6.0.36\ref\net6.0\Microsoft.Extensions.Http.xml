<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Extensions.Http</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Extensions.Http.HttpClientFactoryOptions">
            <summary>
            An options class for configuring the default <see cref="T:System.Net.Http.IHttpClientFactory"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Http.HttpClientFactoryOptions.HttpMessageHandlerBuilderActions">
            <summary>
            Gets a list of operations used to configure an <see cref="T:Microsoft.Extensions.Http.HttpMessageHandlerBuilder"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Http.HttpClientFactoryOptions.HttpClientActions">
            <summary>
            Gets a list of operations used to configure an <see cref="T:System.Net.Http.HttpClient"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Http.HttpClientFactoryOptions.HandlerLifetime">
            <summary>
            Gets or sets the length of time that a <see cref="T:System.Net.Http.HttpMessageHandler"/> instance can be reused. Each named
            client can have its own configured handler lifetime value. The default value of this property is two minutes.
            Set the lifetime to <see cref="F:System.Threading.Timeout.InfiniteTimeSpan"/> to disable handler expiry.
            </summary>
            <remarks>
            <para>
            The default implementation of <see cref="T:System.Net.Http.IHttpClientFactory"/> will pool the <see cref="T:System.Net.Http.HttpMessageHandler"/>
            instances created by the factory to reduce resource consumption. This setting configures the amount of time
            a handler can be pooled before it is scheduled for removal from the pool and disposal.
            </para>
            <para>
            Pooling of handlers is desirable as each handler typically manages its own underlying HTTP connections; creating
            more handlers than necessary can result in connection delays. Some handlers also keep connections open indefinitely
            which can prevent the handler from reacting to DNS changes. The value of <see cref="P:Microsoft.Extensions.Http.HttpClientFactoryOptions.HandlerLifetime"/> should be
            chosen with an understanding of the application's requirement to respond to changes in the network environment.
            </para>
            <para>
            Expiry of a handler will not immediately dispose the handler. An expired handler is placed in a separate pool
            which is processed at intervals to dispose handlers only when they become unreachable. Using long-lived
            <see cref="T:System.Net.Http.HttpClient"/> instances will prevent the underlying <see cref="T:System.Net.Http.HttpMessageHandler"/> from being
            disposed until all references are garbage-collected.
            </para>
            </remarks>
        </member>
        <member name="P:Microsoft.Extensions.Http.HttpClientFactoryOptions.ShouldRedactHeaderValue">
            <summary>
            The <see cref="T:System.Func`2"/> which determines whether to redact the HTTP header value before logging.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Http.HttpClientFactoryOptions.SuppressHandlerScope">
            <summary>
            <para>
            Gets or sets a value that determines whether the <see cref="T:System.Net.Http.IHttpClientFactory"/> will
            create a dependency injection scope when building an <see cref="T:System.Net.Http.HttpMessageHandler"/>.
            If <c>false</c> (default), a scope will be created, otherwise a scope will not be created.
            </para>
            <para>
            This option is provided for compatibility with existing applications. It is recommended
            to use the default setting for new applications.
            </para>
            </summary>
            <remarks>
            <para>
            The <see cref="T:System.Net.Http.IHttpClientFactory"/> will (by default) create a dependency injection scope
            each time it creates an <see cref="T:System.Net.Http.HttpMessageHandler"/>. The created scope has the same
            lifetime as the message handler, and will be disposed when the message handler is disposed.
            </para>
            <para>
            When operations that are part of <see cref="P:Microsoft.Extensions.Http.HttpClientFactoryOptions.HttpMessageHandlerBuilderActions"/> are executed
            they will be provided with the scoped <see cref="T:System.IServiceProvider"/> via
            <see cref="P:Microsoft.Extensions.Http.HttpMessageHandlerBuilder.Services"/>. This includes retrieving a message handler
            from dependency injection, such as one registered using
            <see cref="M:Microsoft.Extensions.DependencyInjection.HttpClientBuilderExtensions.AddHttpMessageHandler``1(Microsoft.Extensions.DependencyInjection.IHttpClientBuilder)"/>.
            </para>
            </remarks>
        </member>
        <member name="T:Microsoft.Extensions.Http.HttpMessageHandlerBuilder">
            <summary>
            A builder abstraction for configuring <see cref="T:System.Net.Http.HttpMessageHandler"/> instances.
            </summary>
            <remarks>
            The <see cref="T:Microsoft.Extensions.Http.HttpMessageHandlerBuilder"/> is registered in the service collection as
            a transient service. Callers should retrieve a new instance for each <see cref="T:System.Net.Http.HttpMessageHandler"/> to
            be created. Implementors should expect each instance to be used a single time.
            </remarks>
        </member>
        <member name="P:Microsoft.Extensions.Http.HttpMessageHandlerBuilder.Name">
            <summary>
            Gets or sets the name of the <see cref="T:System.Net.Http.HttpClient"/> being created.
            </summary>
            <remarks>
            The <see cref="P:Microsoft.Extensions.Http.HttpMessageHandlerBuilder.Name"/> is set by the <see cref="T:System.Net.Http.IHttpClientFactory"/> infrastructure
            and is public for unit testing purposes only. Setting the <see cref="P:Microsoft.Extensions.Http.HttpMessageHandlerBuilder.Name"/> outside of
            testing scenarios may have unpredictable results.
            </remarks>
        </member>
        <member name="P:Microsoft.Extensions.Http.HttpMessageHandlerBuilder.PrimaryHandler">
            <summary>
            Gets or sets the primary <see cref="T:System.Net.Http.HttpMessageHandler"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Http.HttpMessageHandlerBuilder.AdditionalHandlers">
            <summary>
            Gets a list of additional <see cref="T:System.Net.Http.DelegatingHandler"/> instances used to configure an
            <see cref="T:System.Net.Http.HttpClient"/> pipeline.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Http.HttpMessageHandlerBuilder.Services">
            <summary>
            Gets an <see cref="T:System.IServiceProvider"/> which can be used to resolve services
            from the dependency injection container.
            </summary>
            <remarks>
            This property is sensitive to the value of
            <see cref="P:Microsoft.Extensions.Http.HttpClientFactoryOptions.SuppressHandlerScope"/>. If <c>true</c> this
            property will be a reference to the application's root service provider. If <c>false</c>
            (default) this will be a reference to a scoped service provider that has the same
            lifetime as the handler being created.
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.Http.HttpMessageHandlerBuilder.Build">
            <summary>
            Creates an <see cref="T:System.Net.Http.HttpMessageHandler"/>.
            </summary>
            <returns>
            An <see cref="T:System.Net.Http.HttpMessageHandler"/> built from the <see cref="P:Microsoft.Extensions.Http.HttpMessageHandlerBuilder.PrimaryHandler"/> and
            <see cref="P:Microsoft.Extensions.Http.HttpMessageHandlerBuilder.AdditionalHandlers"/>.
            </returns>
        </member>
        <member name="T:Microsoft.Extensions.Http.IHttpMessageHandlerBuilderFilter">
            <summary>
            Used by the <see cref="T:Microsoft.Extensions.Http.DefaultHttpClientFactory"/> to apply additional initialization to the configure the
            <see cref="T:Microsoft.Extensions.Http.HttpMessageHandlerBuilder"/> immediately before <see cref="M:Microsoft.Extensions.Http.HttpMessageHandlerBuilder.Build"/>
            is called.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Http.IHttpMessageHandlerBuilderFilter.Configure(System.Action{Microsoft.Extensions.Http.HttpMessageHandlerBuilder})">
            <summary>
            Applies additional initialization to the <see cref="T:Microsoft.Extensions.Http.HttpMessageHandlerBuilder"/>
            </summary>
            <param name="next">A delegate which will run the next <see cref="T:Microsoft.Extensions.Http.IHttpMessageHandlerBuilderFilter"/>.</param>
        </member>
        <member name="T:Microsoft.Extensions.Http.ITypedHttpClientFactory`1">
             <summary>
             A factory abstraction for a component that can create typed client instances with custom
             configuration for a given logical name.
             </summary>
             <typeparam name="TClient">The type of typed client to create.</typeparam>
             <remarks>
             <para>
             The <see cref="T:Microsoft.Extensions.Http.ITypedHttpClientFactory`1"/> is infrastructure that supports the
             <see cref="M:Microsoft.Extensions.DependencyInjection.HttpClientFactoryServiceCollectionExtensions.AddHttpClient``1(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.String)"/> and
             <see cref="M:Microsoft.Extensions.DependencyInjection.HttpClientBuilderExtensions.AddTypedClient``1(Microsoft.Extensions.DependencyInjection.IHttpClientBuilder)"/> functionality. This type
             should rarely be used directly in application code, use <see cref="M:System.IServiceProvider.GetService(System.Type)"/> instead
             to retrieve typed clients.
             </para>
             <para>
             A default <see cref="T:Microsoft.Extensions.Http.ITypedHttpClientFactory`1"/> can be registered in an <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/>
             by calling <see cref="M:Microsoft.Extensions.DependencyInjection.HttpClientFactoryServiceCollectionExtensions.AddHttpClient(Microsoft.Extensions.DependencyInjection.IServiceCollection)"/>.
             The default <see cref="T:Microsoft.Extensions.Http.ITypedHttpClientFactory`1"/> will be registered in the service collection as a singleton
             open-generic service.
             </para>
             <para>
             The default <see cref="T:Microsoft.Extensions.Http.ITypedHttpClientFactory`1"/> uses type activation to create typed client instances. Typed
             client types are not retrieved directly from the <see cref="T:System.IServiceProvider"/>. See
             <see cref="M:Microsoft.Extensions.DependencyInjection.ActivatorUtilities.CreateInstance(System.IServiceProvider,System.Type,System.Object[])" /> for details.
             </para>
             </remarks>
             <example>
             This sample shows the basic pattern for defining a typed client class.
             <code>
             class ExampleClient
             {
                 private readonly HttpClient _httpClient;
                 private readonly ILogger _logger;
            
                 // typed clients can use constructor injection to access additional services
                 public ExampleClient(HttpClient httpClient, ILogger&lt;ExampleClient&gt; logger)
                 {
                     _httpClient = httpClient;
                     _logger = logger;
                 }
            
                 // typed clients can expose the HttpClient for application code to call directly
                 public HttpClient HttpClient => _httpClient;
            
                 // typed clients can also define methods that abstract usage of the HttpClient
                 public async Task SendHelloRequest()
                 {
                     var response = await _httpClient.GetAsync("/helloworld");
                     response.EnsureSuccessStatusCode();
                 }
             }
             </code>
             </example>
             <example>
             This sample shows how to consume a typed client from an ASP.NET Core middleware.
             <code>
             // in Startup.cs
             public void Configure(IApplicationBuilder app, ExampleClient exampleClient)
             {
                 app.Run(async (context) =>
                 {
                     var response = await _exampleClient.GetAsync("/helloworld");
                     await context.Response.WriteAsync("Remote server said: ");
                     await response.Content.CopyToAsync(context.Response.Body);
                 });
             }
             </code>
             </example>
             <example>
             This sample shows how to consume a typed client from an ASP.NET Core MVC Controller.
             <code>
             // in Controllers/HomeController.cs
             public class HomeController : ControllerBase(IApplicationBuilder app, ExampleClient exampleClient)
             {
                 private readonly ExampleClient _exampleClient;
            
                 public HomeController(ExampleClient exampleClient)
                 {
                     _exampleClient = exampleClient;
                 }
            
                 public async Task&lt;IActionResult&gt; Index()
                 {
                     var response = await _exampleClient.GetAsync("/helloworld");
                     var text = await response.Content.ReadAsStringAsync();
                     return Content("Remote server said: " + text, "text/plain");
                 };
             }
             </code>
             </example>
        </member>
        <member name="M:Microsoft.Extensions.Http.ITypedHttpClientFactory`1.CreateClient(System.Net.Http.HttpClient)">
            <summary>
            Creates a typed client given an associated <see cref="T:System.Net.Http.HttpClient"/>.
            </summary>
            <param name="httpClient">
            An <see cref="T:System.Net.Http.HttpClient"/> created by the <see cref="T:System.Net.Http.IHttpClientFactory"/> for the named client
            associated with <typeparamref name="TClient"/>.
            </param>
            <returns>An instance of <typeparamref name="TClient"/>.</returns>
        </member>
        <member name="T:Microsoft.Extensions.DependencyInjection.HttpClientBuilderExtensions">
            <summary>
            Extension methods for configuring an <see cref="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"/>
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HttpClientBuilderExtensions.ConfigureHttpClient(Microsoft.Extensions.DependencyInjection.IHttpClientBuilder,System.Action{System.Net.Http.HttpClient})">
            <summary>
            Adds a delegate that will be used to configure a named <see cref="T:System.Net.Http.HttpClient"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"/>.</param>
            <param name="configureClient">A delegate that is used to configure an <see cref="T:System.Net.Http.HttpClient"/>.</param>
            <returns>An <see cref="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"/> that can be used to configure the client.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HttpClientBuilderExtensions.ConfigureHttpClient(Microsoft.Extensions.DependencyInjection.IHttpClientBuilder,System.Action{System.IServiceProvider,System.Net.Http.HttpClient})">
            <summary>
            Adds a delegate that will be used to configure a named <see cref="T:System.Net.Http.HttpClient"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"/>.</param>
            <param name="configureClient">A delegate that is used to configure an <see cref="T:System.Net.Http.HttpClient"/>.</param>
            <returns>An <see cref="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"/> that can be used to configure the client.</returns>
            <remarks>
            The <see cref="T:System.IServiceProvider"/> provided to <paramref name="configureClient"/> will be the
            same application's root service provider instance.
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HttpClientBuilderExtensions.AddHttpMessageHandler(Microsoft.Extensions.DependencyInjection.IHttpClientBuilder,System.Func{System.Net.Http.DelegatingHandler})">
            <summary>
            Adds a delegate that will be used to create an additional message handler for a named <see cref="T:System.Net.Http.HttpClient"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"/>.</param>
            <param name="configureHandler">A delegate that is used to create a <see cref="T:System.Net.Http.DelegatingHandler"/>.</param>
            <returns>An <see cref="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"/> that can be used to configure the client.</returns>
            <remarks>
            The <see paramref="configureHandler"/> delegate should return a new instance of the message handler each time it
            is invoked.
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HttpClientBuilderExtensions.AddHttpMessageHandler(Microsoft.Extensions.DependencyInjection.IHttpClientBuilder,System.Func{System.IServiceProvider,System.Net.Http.DelegatingHandler})">
            <summary>
            Adds a delegate that will be used to create an additional message handler for a named <see cref="T:System.Net.Http.HttpClient"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"/>.</param>
            <param name="configureHandler">A delegate that is used to create a <see cref="T:System.Net.Http.DelegatingHandler"/>.</param>       /// <returns>An <see cref="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"/> that can be used to configure the client.</returns>
            <remarks>
            <para>
            The <see paramref="configureHandler"/> delegate should return a new instance of the message handler each time it
            is invoked.
            </para>
            <para>
            The <see cref="T:System.IServiceProvider"/> argument provided to <paramref name="configureHandler"/> will be
            a reference to a scoped service provider that shares the lifetime of the handler being constructed.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HttpClientBuilderExtensions.AddHttpMessageHandler``1(Microsoft.Extensions.DependencyInjection.IHttpClientBuilder)">
            <summary>
            Adds an additional message handler from the dependency injection container for a named <see cref="T:System.Net.Http.HttpClient"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"/>.</param>
            <returns>An <see cref="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"/> that can be used to configure the client.</returns>
            <typeparam name="THandler">
            The type of the <see cref="T:System.Net.Http.DelegatingHandler"/>. The handler type must be registered as a transient service.
            </typeparam>
            <remarks>
            <para>
            The <typeparamref name="THandler"/> will be resolved from a scoped service provider that shares
            the lifetime of the handler being constructed.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HttpClientBuilderExtensions.ConfigurePrimaryHttpMessageHandler(Microsoft.Extensions.DependencyInjection.IHttpClientBuilder,System.Func{System.Net.Http.HttpMessageHandler})">
            <summary>
            Adds a delegate that will be used to configure the primary <see cref="T:System.Net.Http.HttpMessageHandler"/> for a
            named <see cref="T:System.Net.Http.HttpClient"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"/>.</param>
            <param name="configureHandler">A delegate that is used to create an <see cref="T:System.Net.Http.HttpMessageHandler"/>.</param>
            <returns>An <see cref="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"/> that can be used to configure the client.</returns>
            <remarks>
            The <see paramref="configureHandler"/> delegate should return a new instance of the message handler each time it
            is invoked.
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HttpClientBuilderExtensions.ConfigurePrimaryHttpMessageHandler(Microsoft.Extensions.DependencyInjection.IHttpClientBuilder,System.Func{System.IServiceProvider,System.Net.Http.HttpMessageHandler})">
            <summary>
            Adds a delegate that will be used to configure the primary <see cref="T:System.Net.Http.HttpMessageHandler"/> for a
            named <see cref="T:System.Net.Http.HttpClient"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"/>.</param>
            <param name="configureHandler">A delegate that is used to create an <see cref="T:System.Net.Http.HttpMessageHandler"/>.</param>
            <returns>An <see cref="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"/> that can be used to configure the client.</returns>
            <remarks>
            <para>
            The <see paramref="configureHandler"/> delegate should return a new instance of the message handler each time it
            is invoked.
            </para>
            <para>
            The <see cref="T:System.IServiceProvider"/> argument provided to <paramref name="configureHandler"/> will be
            a reference to a scoped service provider that shares the lifetime of the handler being constructed.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HttpClientBuilderExtensions.ConfigurePrimaryHttpMessageHandler``1(Microsoft.Extensions.DependencyInjection.IHttpClientBuilder)">
            <summary>
            Configures the primary <see cref="T:System.Net.Http.HttpMessageHandler"/> from the dependency injection container
            for a  named <see cref="T:System.Net.Http.HttpClient"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"/>.</param>
            <returns>An <see cref="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"/> that can be used to configure the client.</returns>
            <typeparam name="THandler">
            The type of the <see cref="T:System.Net.Http.DelegatingHandler"/>. The handler type must be registered as a transient service.
            </typeparam>
            <remarks>
            <para>
            The <typeparamref name="THandler"/> will be resolved from a scoped service provider that shares
            the lifetime of the handler being constructed.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HttpClientBuilderExtensions.ConfigureHttpMessageHandlerBuilder(Microsoft.Extensions.DependencyInjection.IHttpClientBuilder,System.Action{Microsoft.Extensions.Http.HttpMessageHandlerBuilder})">
            <summary>
            Adds a delegate that will be used to configure message handlers using <see cref="T:Microsoft.Extensions.Http.HttpMessageHandlerBuilder"/>
            for a named <see cref="T:System.Net.Http.HttpClient"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"/>.</param>
            <param name="configureBuilder">A delegate that is used to configure an <see cref="T:Microsoft.Extensions.Http.HttpMessageHandlerBuilder"/>.</param>
            <returns>An <see cref="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"/> that can be used to configure the client.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HttpClientBuilderExtensions.AddTypedClient``1(Microsoft.Extensions.DependencyInjection.IHttpClientBuilder)">
            <summary>
            Configures a binding between the <typeparamref name="TClient" /> type and the named <see cref="T:System.Net.Http.HttpClient"/>
            associated with the <see cref="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"/>.
            </summary>
            <typeparam name="TClient">
            The type of the typed client. The type specified will be registered in the service collection as
            a transient service. See <see cref="T:Microsoft.Extensions.Http.ITypedHttpClientFactory`1" /> for more details about authoring typed clients.
            </typeparam>
            <param name="builder">The <see cref="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"/>.</param>
            <remarks>
            <para>
            <typeparamref name="TClient"/> instances constructed with the appropriate <see cref="T:System.Net.Http.HttpClient" />
            can be retrieved from <see cref="M:System.IServiceProvider.GetService(System.Type)" /> (and related methods) by providing
            <typeparamref name="TClient"/> as the service type.
            </para>
            <para>
            Calling <see cref="M:Microsoft.Extensions.DependencyInjection.HttpClientBuilderExtensions.AddTypedClient``1(Microsoft.Extensions.DependencyInjection.IHttpClientBuilder)"/> will register a typed
            client binding that creates <typeparamref name="TClient"/> using the <see cref="T:Microsoft.Extensions.Http.ITypedHttpClientFactory`1" />.
            </para>
            <para>
            The typed client's service dependencies will be resolved from the same service provider
            that is used to resolve the typed client. It is not possible to access services from the
            scope bound to the message handler, which is managed independently.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HttpClientBuilderExtensions.AddTypedClient``2(Microsoft.Extensions.DependencyInjection.IHttpClientBuilder)">
            <summary>
            Configures a binding between the <typeparamref name="TClient" /> type and the named <see cref="T:System.Net.Http.HttpClient"/>
            associated with the <see cref="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"/>. The created instances will be of type
            <typeparamref name="TImplementation"/>.
            </summary>
            <typeparam name="TClient">
            The declared type of the typed client. They type specified will be registered in the service collection as
            a transient service. See <see cref="T:Microsoft.Extensions.Http.ITypedHttpClientFactory`1" /> for more details about authoring typed clients.
            </typeparam>
            <typeparam name="TImplementation">
            The implementation type of the typed client. The type specified by will be instantiated by the
            <see cref="T:Microsoft.Extensions.Http.ITypedHttpClientFactory`1"/>.
            </typeparam>
            <param name="builder">The <see cref="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"/>.</param>
            <remarks>
            <para>
            <typeparamref name="TClient"/> instances constructed with the appropriate <see cref="T:System.Net.Http.HttpClient" />
            can be retrieved from <see cref="M:System.IServiceProvider.GetService(System.Type)" /> (and related methods) by providing
            <typeparamref name="TClient"/> as the service type.
            </para>
            <para>
            Calling <see cref="M:Microsoft.Extensions.DependencyInjection.HttpClientBuilderExtensions.AddTypedClient``2(Microsoft.Extensions.DependencyInjection.IHttpClientBuilder)"/>
            will register a typed client binding that creates <typeparamref name="TImplementation"/> using the
            <see cref="T:Microsoft.Extensions.Http.ITypedHttpClientFactory`1" />.
            </para>
            <para>
            The typed client's service dependencies will be resolved from the same service provider
            that is used to resolve the typed client. It is not possible to access services from the
            scope bound to the message handler, which is managed independently.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HttpClientBuilderExtensions.AddTypedClient``1(Microsoft.Extensions.DependencyInjection.IHttpClientBuilder,System.Func{System.Net.Http.HttpClient,``0})">
            <summary>
            Configures a binding between the <typeparamref name="TClient" /> type and the named <see cref="T:System.Net.Http.HttpClient"/>
            associated with the <see cref="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"/>.
            </summary>
            <typeparam name="TClient">
            The type of the typed client. They type specified will be registered in the service collection as
            a transient service.
            </typeparam>
            <param name="builder">The <see cref="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"/>.</param>
            <param name="factory">A factory function that will be used to construct the typed client.</param>
            <remarks>
            <para>
            <typeparamref name="TClient"/> instances constructed with the appropriate <see cref="T:System.Net.Http.HttpClient" />
            can be retrieved from <see cref="M:System.IServiceProvider.GetService(System.Type)" /> (and related methods) by providing
            <typeparamref name="TClient"/> as the service type.
            </para>
            <para>
            Calling <see cref="M:Microsoft.Extensions.DependencyInjection.HttpClientBuilderExtensions.AddTypedClient``1(Microsoft.Extensions.DependencyInjection.IHttpClientBuilder,System.Func{System.Net.Http.HttpClient,``0})"/>
            will register a typed client binding that creates <typeparamref name="TClient"/> using the provided factory function.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HttpClientBuilderExtensions.AddTypedClient``1(Microsoft.Extensions.DependencyInjection.IHttpClientBuilder,System.Func{System.Net.Http.HttpClient,System.IServiceProvider,``0})">
            <summary>
            Configures a binding between the <typeparamref name="TClient" /> type and the named <see cref="T:System.Net.Http.HttpClient"/>
            associated with the <see cref="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"/>.
            </summary>
            <typeparam name="TClient">
            The type of the typed client. They type specified will be registered in the service collection as
            a transient service.
            </typeparam>
            <param name="builder">The <see cref="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"/>.</param>
            <param name="factory">A factory function that will be used to construct the typed client.</param>
            <remarks>
            <para>
            <typeparamref name="TClient"/> instances constructed with the appropriate <see cref="T:System.Net.Http.HttpClient" />
            can be retrieved from <see cref="M:System.IServiceProvider.GetService(System.Type)" /> (and related methods) by providing
            <typeparamref name="TClient"/> as the service type.
            </para>
            <para>
            Calling <see cref="M:Microsoft.Extensions.DependencyInjection.HttpClientBuilderExtensions.AddTypedClient``1(Microsoft.Extensions.DependencyInjection.IHttpClientBuilder,System.Func{System.Net.Http.HttpClient,System.IServiceProvider,``0})"/>
            will register a typed client binding that creates <typeparamref name="TClient"/> using the provided factory function.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HttpClientBuilderExtensions.RedactLoggedHeaders(Microsoft.Extensions.DependencyInjection.IHttpClientBuilder,System.Func{System.String,System.Boolean})">
            <summary>
            Sets the <see cref="T:System.Func`2"/> which determines whether to redact the HTTP header value before logging.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"/>.</param>
            <param name="shouldRedactHeaderValue">The <see cref="T:System.Func`2"/> which determines whether to redact the HTTP header value before logging.</param>
            <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"/>.</returns>
            <remarks>The provided <paramref name="shouldRedactHeaderValue"/> predicate will be evaluated for each header value when logging. If the predicate returns <c>true</c> then the header value will be replaced with a marker value <c>*</c> in logs; otherwise the header value will be logged.
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HttpClientBuilderExtensions.RedactLoggedHeaders(Microsoft.Extensions.DependencyInjection.IHttpClientBuilder,System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Sets the collection of HTTP headers names for which values should be redacted before logging.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"/>.</param>
            <param name="redactedLoggedHeaderNames">The collection of HTTP headers names for which values should be redacted before logging.</param>
            <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HttpClientBuilderExtensions.SetHandlerLifetime(Microsoft.Extensions.DependencyInjection.IHttpClientBuilder,System.TimeSpan)">
            <summary>
            Sets the length of time that a <see cref="T:System.Net.Http.HttpMessageHandler"/> instance can be reused. Each named
            client can have its own configured handler lifetime value. The default value is two minutes. Set the lifetime to
            <see cref="F:System.Threading.Timeout.InfiniteTimeSpan"/> to disable handler expiry.
            </summary>
            <remarks>
            <para>
            The default implementation of <see cref="T:System.Net.Http.IHttpClientFactory"/> will pool the <see cref="T:System.Net.Http.HttpMessageHandler"/>
            instances created by the factory to reduce resource consumption. This setting configures the amount of time
            a handler can be pooled before it is scheduled for removal from the pool and disposal.
            </para>
            <para>
            Pooling of handlers is desirable as each handler typically manages its own underlying HTTP connections; creating
            more handlers than necessary can result in connection delays. Some handlers also keep connections open indefinitely
            which can prevent the handler from reacting to DNS changes. The value of <paramref name="handlerLifetime"/> should be
            chosen with an understanding of the application's requirement to respond to changes in the network environment.
            </para>
            <para>
            Expiry of a handler will not immediately dispose the handler. An expired handler is placed in a separate pool
            which is processed at intervals to dispose handlers only when they become unreachable. Using long-lived
            <see cref="T:System.Net.Http.HttpClient"/> instances will prevent the underlying <see cref="T:System.Net.Http.HttpMessageHandler"/> from being
            disposed until all references are garbage-collected.
            </para>
            </remarks>
        </member>
        <member name="T:Microsoft.Extensions.DependencyInjection.HttpClientFactoryServiceCollectionExtensions">
            <summary>
            Extension methods to configure an <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> for <see cref="T:System.Net.Http.IHttpClientFactory"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HttpClientFactoryServiceCollectionExtensions.AddHttpClient(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Adds the <see cref="T:System.Net.Http.IHttpClientFactory"/> and related services to the <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/>.
            </summary>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/>.</param>
            <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HttpClientFactoryServiceCollectionExtensions.AddHttpClient(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.String)">
            <summary>
            Adds the <see cref="T:System.Net.Http.IHttpClientFactory"/> and related services to the <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> and configures
            a named <see cref="T:System.Net.Http.HttpClient"/>.
            </summary>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/>.</param>
            <param name="name">The logical name of the <see cref="T:System.Net.Http.HttpClient"/> to configure.</param>
            <returns>An <see cref="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"/> that can be used to configure the client.</returns>
            <remarks>
            <para>
            <see cref="T:System.Net.Http.HttpClient"/> instances that apply the provided configuration can be retrieved using
            <see cref="M:System.Net.Http.IHttpClientFactory.CreateClient(System.String)"/> and providing the matching name.
            </para>
            <para>
            Use <see cref="F:Microsoft.Extensions.Options.Options.DefaultName"/> as the name to configure the default client.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HttpClientFactoryServiceCollectionExtensions.AddHttpClient(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.String,System.Action{System.Net.Http.HttpClient})">
            <summary>
            Adds the <see cref="T:System.Net.Http.IHttpClientFactory"/> and related services to the <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> and configures
            a named <see cref="T:System.Net.Http.HttpClient"/>.
            </summary>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/>.</param>
            <param name="name">The logical name of the <see cref="T:System.Net.Http.HttpClient"/> to configure.</param>
            <param name="configureClient">A delegate that is used to configure an <see cref="T:System.Net.Http.HttpClient"/>.</param>
            <returns>An <see cref="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"/> that can be used to configure the client.</returns>
            <remarks>
            <para>
            <see cref="T:System.Net.Http.HttpClient"/> instances that apply the provided configuration can be retrieved using
            <see cref="M:System.Net.Http.IHttpClientFactory.CreateClient(System.String)"/> and providing the matching name.
            </para>
            <para>
            Use <see cref="F:Microsoft.Extensions.Options.Options.DefaultName"/> as the name to configure the default client.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HttpClientFactoryServiceCollectionExtensions.AddHttpClient(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.String,System.Action{System.IServiceProvider,System.Net.Http.HttpClient})">
            <summary>
            Adds the <see cref="T:System.Net.Http.IHttpClientFactory"/> and related services to the <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> and configures
            a named <see cref="T:System.Net.Http.HttpClient"/>.
            </summary>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/>.</param>
            <param name="name">The logical name of the <see cref="T:System.Net.Http.HttpClient"/> to configure.</param>
            <param name="configureClient">A delegate that is used to configure an <see cref="T:System.Net.Http.HttpClient"/>.</param>
            <returns>An <see cref="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"/> that can be used to configure the client.</returns>
            <remarks>
            <para>
            <see cref="T:System.Net.Http.HttpClient"/> instances that apply the provided configuration can be retrieved using
            <see cref="M:System.Net.Http.IHttpClientFactory.CreateClient(System.String)"/> and providing the matching name.
            </para>
            <para>
            Use <see cref="F:Microsoft.Extensions.Options.Options.DefaultName"/> as the name to configure the default client.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HttpClientFactoryServiceCollectionExtensions.AddHttpClient``1(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Adds the <see cref="T:System.Net.Http.IHttpClientFactory"/> and related services to the <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> and configures
            a binding between the <typeparamref name="TClient"/> type and a named <see cref="T:System.Net.Http.HttpClient"/>. The client name
            will be set to the type name of <typeparamref name="TClient"/>.
            </summary>
            <typeparam name="TClient">
            The type of the typed client. The type specified will be registered in the service collection as
            a transient service. See <see cref="T:Microsoft.Extensions.Http.ITypedHttpClientFactory`1" /> for more details about authoring typed clients.
            </typeparam>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/>.</param>
            <returns>An <see cref="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"/> that can be used to configure the client.</returns>
            <remarks>
            <para>
            <see cref="T:System.Net.Http.HttpClient"/> instances that apply the provided configuration can be retrieved using
            <see cref="M:System.Net.Http.IHttpClientFactory.CreateClient(System.String)"/> and providing the matching name.
            </para>
            <para>
            <typeparamref name="TClient"/> instances constructed with the appropriate <see cref="T:System.Net.Http.HttpClient" />
            can be retrieved from <see cref="M:System.IServiceProvider.GetService(System.Type)" /> (and related methods) by providing
            <typeparamref name="TClient"/> as the service type.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HttpClientFactoryServiceCollectionExtensions.AddHttpClient``2(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Adds the <see cref="T:System.Net.Http.IHttpClientFactory"/> and related services to the <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> and configures
            a binding between the <typeparamref name="TClient" /> type and a named <see cref="T:System.Net.Http.HttpClient"/>. The client name will
            be set to the type name of <typeparamref name="TClient"/>.
            </summary>
            <typeparam name="TClient">
            The type of the typed client. The type specified will be registered in the service collection as
            a transient service. See <see cref="T:Microsoft.Extensions.Http.ITypedHttpClientFactory`1" /> for more details about authoring typed clients.
            </typeparam>
            <typeparam name="TImplementation">
            The implementation type of the typed client. The type specified will be instantiated by the
            <see cref="T:Microsoft.Extensions.Http.ITypedHttpClientFactory`1"/>
            </typeparam>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/>.</param>
            <returns>An <see cref="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"/> that can be used to configure the client.</returns>
            <remarks>
            <para>
            <see cref="T:System.Net.Http.HttpClient"/> instances that apply the provided configuration can be retrieved using
            <see cref="M:System.Net.Http.IHttpClientFactory.CreateClient(System.String)"/> and providing the matching name.
            </para>
            <para>
            <typeparamref name="TClient"/> instances constructed with the appropriate <see cref="T:System.Net.Http.HttpClient" />
            can be retrieved from <see cref="M:System.IServiceProvider.GetService(System.Type)" /> (and related methods) by providing
            <typeparamref name="TClient"/> as the service type.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HttpClientFactoryServiceCollectionExtensions.AddHttpClient``1(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.String)">
            <summary>
            Adds the <see cref="T:System.Net.Http.IHttpClientFactory"/> and related services to the <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> and configures
            a binding between the <typeparamref name="TClient"/> type and a named <see cref="T:System.Net.Http.HttpClient"/>.
            </summary>
            <typeparam name="TClient">
            The type of the typed client. The type specified will be registered in the service collection as
            a transient service. See <see cref="T:Microsoft.Extensions.Http.ITypedHttpClientFactory`1" /> for more details about authoring typed clients.
            </typeparam>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/>.</param>
            <param name="name">The logical name of the <see cref="T:System.Net.Http.HttpClient"/> to configure.</param>
            <returns>An <see cref="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"/> that can be used to configure the client.</returns>
            <remarks>
            <para>
            <see cref="T:System.Net.Http.HttpClient"/> instances that apply the provided configuration can be retrieved using
            <see cref="M:System.Net.Http.IHttpClientFactory.CreateClient(System.String)"/> and providing the matching name.
            </para>
            <para>
            <typeparamref name="TClient"/> instances constructed with the appropriate <see cref="T:System.Net.Http.HttpClient" />
            can be retrieved from <see cref="M:System.IServiceProvider.GetService(System.Type)" /> (and related methods) by providing
            <typeparamref name="TClient"/> as the service type.
            </para>
            <para>
            Use <see cref="F:Microsoft.Extensions.Options.Options.DefaultName"/> as the name to configure the default client.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HttpClientFactoryServiceCollectionExtensions.AddHttpClient``2(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.String)">
            <summary>
            Adds the <see cref="T:System.Net.Http.IHttpClientFactory"/> and related services to the <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> and configures
            a binding between the <typeparamref name="TClient" /> type and a named <see cref="T:System.Net.Http.HttpClient"/>. The client name will
            be set to the type name of <typeparamref name="TClient"/>.
            </summary>
            <typeparam name="TClient">
            The type of the typed client. The type specified will be registered in the service collection as
            a transient service. See <see cref="T:Microsoft.Extensions.Http.ITypedHttpClientFactory`1" /> for more details about authoring typed clients.
            </typeparam>
            <typeparam name="TImplementation">
            The implementation type of the typed client. The type specified will be instantiated by the
            <see cref="T:Microsoft.Extensions.Http.ITypedHttpClientFactory`1"/>
            </typeparam>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/>.</param>
            <param name="name">The logical name of the <see cref="T:System.Net.Http.HttpClient"/> to configure.</param>
            <returns>An <see cref="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"/> that can be used to configure the client.</returns>
            <remarks>
            <para>
            <see cref="T:System.Net.Http.HttpClient"/> instances that apply the provided configuration can be retrieved using
            <see cref="M:System.Net.Http.IHttpClientFactory.CreateClient(System.String)"/> and providing the matching name.
            </para>
            <para>
            <typeparamref name="TClient"/> instances constructed with the appropriate <see cref="T:System.Net.Http.HttpClient" />
            can be retrieved from <see cref="M:System.IServiceProvider.GetService(System.Type)" /> (and related methods) by providing
            <typeparamref name="TClient"/> as the service type.
            </para>
            <para>
            Use <see cref="F:Microsoft.Extensions.Options.Options.DefaultName"/> as the name to configure the default client.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HttpClientFactoryServiceCollectionExtensions.AddHttpClient``1(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action{System.Net.Http.HttpClient})">
            <summary>
            Adds the <see cref="T:System.Net.Http.IHttpClientFactory"/> and related services to the <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> and configures
            a binding between the <typeparamref name="TClient" /> type and a named <see cref="T:System.Net.Http.HttpClient"/>. The client name will
            be set to the type name of <typeparamref name="TClient"/>.
            </summary>
            <typeparam name="TClient">
            The type of the typed client. The type specified will be registered in the service collection as
            a transient service. See <see cref="T:Microsoft.Extensions.Http.ITypedHttpClientFactory`1" /> for more details about authoring typed clients.
            </typeparam>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/>.</param>
            <param name="configureClient">A delegate that is used to configure an <see cref="T:System.Net.Http.HttpClient"/>.</param>
            <returns>An <see cref="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"/> that can be used to configure the client.</returns>
            <remarks>
            <para>
            <see cref="T:System.Net.Http.HttpClient"/> instances that apply the provided configuration can be retrieved using
            <see cref="M:System.Net.Http.IHttpClientFactory.CreateClient(System.String)"/> and providing the matching name.
            </para>
            <para>
            <typeparamref name="TClient"/> instances constructed with the appropriate <see cref="T:System.Net.Http.HttpClient" />
            can be retrieved from <see cref="M:System.IServiceProvider.GetService(System.Type)" /> (and related methods) by providing
            <typeparamref name="TClient"/> as the service type.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HttpClientFactoryServiceCollectionExtensions.AddHttpClient``1(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action{System.IServiceProvider,System.Net.Http.HttpClient})">
            <summary>
            Adds the <see cref="T:System.Net.Http.IHttpClientFactory"/> and related services to the <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> and configures
            a binding between the <typeparamref name="TClient" /> type and a named <see cref="T:System.Net.Http.HttpClient"/>. The client name will
            be set to the type name of <typeparamref name="TClient"/>.
            </summary>
            <typeparam name="TClient">
            The type of the typed client. The type specified will be registered in the service collection as
            a transient service. See <see cref="T:Microsoft.Extensions.Http.ITypedHttpClientFactory`1" /> for more details about authoring typed clients.
            </typeparam>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/>.</param>
            <param name="configureClient">A delegate that is used to configure an <see cref="T:System.Net.Http.HttpClient"/>.</param>
            <returns>An <see cref="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"/> that can be used to configure the client.</returns>
            <remarks>
            <para>
            <see cref="T:System.Net.Http.HttpClient"/> instances that apply the provided configuration can be retrieved using
            <see cref="M:System.Net.Http.IHttpClientFactory.CreateClient(System.String)"/> and providing the matching name.
            </para>
            <para>
            <typeparamref name="TClient"/> instances constructed with the appropriate <see cref="T:System.Net.Http.HttpClient" />
            can be retrieved from <see cref="M:System.IServiceProvider.GetService(System.Type)" /> (and related methods) by providing
            <typeparamref name="TClient"/> as the service type.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HttpClientFactoryServiceCollectionExtensions.AddHttpClient``2(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action{System.Net.Http.HttpClient})">
            <summary>
            Adds the <see cref="T:System.Net.Http.IHttpClientFactory"/> and related services to the <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> and configures
            a binding between the <typeparamref name="TClient" /> type and a named <see cref="T:System.Net.Http.HttpClient"/>. The client name will
            be set to the type name of <typeparamref name="TClient"/>.
            </summary>
            <typeparam name="TClient">
            The type of the typed client. The type specified will be registered in the service collection as
            a transient service. See <see cref="T:Microsoft.Extensions.Http.ITypedHttpClientFactory`1" /> for more details about authoring typed clients.
            </typeparam>
            <typeparam name="TImplementation">
            The implementation type of the typed client. The type specified will be instantiated by the
            <see cref="T:Microsoft.Extensions.Http.ITypedHttpClientFactory`1"/>
            </typeparam>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/>.</param>
            <param name="configureClient">A delegate that is used to configure an <see cref="T:System.Net.Http.HttpClient"/>.</param>
            <returns>An <see cref="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"/> that can be used to configure the client.</returns>
            <remarks>
            <para>
            <see cref="T:System.Net.Http.HttpClient"/> instances that apply the provided configuration can be retrieved using
            <see cref="M:System.Net.Http.IHttpClientFactory.CreateClient(System.String)"/> and providing the matching name.
            </para>
            <para>
            <typeparamref name="TClient"/> instances constructed with the appropriate <see cref="T:System.Net.Http.HttpClient" />
            can be retrieved from <see cref="M:System.IServiceProvider.GetService(System.Type)" /> (and related methods) by providing
            <typeparamref name="TClient"/> as the service type.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HttpClientFactoryServiceCollectionExtensions.AddHttpClient``2(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action{System.IServiceProvider,System.Net.Http.HttpClient})">
            <summary>
            Adds the <see cref="T:System.Net.Http.IHttpClientFactory"/> and related services to the <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> and configures
            a binding between the <typeparamref name="TClient" /> type and a named <see cref="T:System.Net.Http.HttpClient"/>. The client name will
            be set to the type name of <typeparamref name="TClient"/>.
            </summary>
            <typeparam name="TClient">
            The type of the typed client. The type specified will be registered in the service collection as
            a transient service. See <see cref="T:Microsoft.Extensions.Http.ITypedHttpClientFactory`1" /> for more details about authoring typed clients.
            </typeparam>
            <typeparam name="TImplementation">
            The implementation type of the typed client. The type specified will be instantiated by the
            <see cref="T:Microsoft.Extensions.Http.ITypedHttpClientFactory`1"/>
            </typeparam>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/>.</param>
            <param name="configureClient">A delegate that is used to configure an <see cref="T:System.Net.Http.HttpClient"/>.</param>
            <returns>An <see cref="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"/> that can be used to configure the client.</returns>
            <remarks>
            <para>
            <see cref="T:System.Net.Http.HttpClient"/> instances that apply the provided configuration can be retrieved using
            <see cref="M:System.Net.Http.IHttpClientFactory.CreateClient(System.String)"/> and providing the matching name.
            </para>
            <para>
            <typeparamref name="TClient"/> instances constructed with the appropriate <see cref="T:System.Net.Http.HttpClient" />
            can be retrieved from <see cref="M:System.IServiceProvider.GetService(System.Type)" /> (and related methods) by providing
            <typeparamref name="TClient"/> as the service type.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HttpClientFactoryServiceCollectionExtensions.AddHttpClient``1(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.String,System.Action{System.Net.Http.HttpClient})">
            <summary>
            Adds the <see cref="T:System.Net.Http.IHttpClientFactory"/> and related services to the <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> and configures
            a binding between the <typeparamref name="TClient" /> type and a named <see cref="T:System.Net.Http.HttpClient"/>.
            </summary>
            <typeparam name="TClient">
            The type of the typed client. The type specified will be registered in the service collection as
            a transient service. See <see cref="T:Microsoft.Extensions.Http.ITypedHttpClientFactory`1" /> for more details about authoring typed clients.
            </typeparam>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/>.</param>
            <param name="name">The logical name of the <see cref="T:System.Net.Http.HttpClient"/> to configure.</param>
            <param name="configureClient">A delegate that is used to configure an <see cref="T:System.Net.Http.HttpClient"/>.</param>
            <returns>An <see cref="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"/> that can be used to configure the client.</returns>
            <remarks>
            <para>
            <see cref="T:System.Net.Http.HttpClient"/> instances that apply the provided configuration can be retrieved using
            <see cref="M:System.Net.Http.IHttpClientFactory.CreateClient(System.String)"/> and providing the matching name.
            </para>
            <para>
            <typeparamref name="TClient"/> instances constructed with the appropriate <see cref="T:System.Net.Http.HttpClient" />
            can be retrieved from <see cref="M:System.IServiceProvider.GetService(System.Type)" /> (and related methods) by providing
            <typeparamref name="TClient"/> as the service type.
            </para>
            <para>
            Use <see cref="F:Microsoft.Extensions.Options.Options.DefaultName"/> as the name to configure the default client.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HttpClientFactoryServiceCollectionExtensions.AddHttpClient``1(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.String,System.Action{System.IServiceProvider,System.Net.Http.HttpClient})">
            <summary>
            Adds the <see cref="T:System.Net.Http.IHttpClientFactory"/> and related services to the <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> and configures
            a binding between the <typeparamref name="TClient" /> type and a named <see cref="T:System.Net.Http.HttpClient"/>.
            </summary>
            <typeparam name="TClient">
            The type of the typed client. The type specified will be registered in the service collection as
            a transient service. See <see cref="T:Microsoft.Extensions.Http.ITypedHttpClientFactory`1" /> for more details about authoring typed clients.
            </typeparam>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/>.</param>
            <param name="name">The logical name of the <see cref="T:System.Net.Http.HttpClient"/> to configure.</param>
            <param name="configureClient">A delegate that is used to configure an <see cref="T:System.Net.Http.HttpClient"/>.</param>
            <returns>An <see cref="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"/> that can be used to configure the client.</returns>
            <remarks>
            <para>
            <see cref="T:System.Net.Http.HttpClient"/> instances that apply the provided configuration can be retrieved using
            <see cref="M:System.Net.Http.IHttpClientFactory.CreateClient(System.String)"/> and providing the matching name.
            </para>
            <para>
            <typeparamref name="TClient"/> instances constructed with the appropriate <see cref="T:System.Net.Http.HttpClient" />
            can be retrieved from <see cref="M:System.IServiceProvider.GetService(System.Type)" /> (and related methods) by providing
            <typeparamref name="TClient"/> as the service type.
            </para>
            <para>
            Use <see cref="F:Microsoft.Extensions.Options.Options.DefaultName"/> as the name to configure the default client.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HttpClientFactoryServiceCollectionExtensions.AddHttpClient``2(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.String,System.Action{System.Net.Http.HttpClient})">
            <summary>
            Adds the <see cref="T:System.Net.Http.IHttpClientFactory"/> and related services to the <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> and configures
            a binding between the <typeparamref name="TClient" /> type and a named <see cref="T:System.Net.Http.HttpClient"/>.
            </summary>
            <typeparam name="TClient">
            The type of the typed client. The type specified will be registered in the service collection as
            a transient service. See <see cref="T:Microsoft.Extensions.Http.ITypedHttpClientFactory`1" /> for more details about authoring typed clients.
            </typeparam>
            <typeparam name="TImplementation">
            The implementation type of the typed client. The type specified will be instantiated by the
            <see cref="T:Microsoft.Extensions.Http.ITypedHttpClientFactory`1"/>
            </typeparam>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/>.</param>
            <param name="name">The logical name of the <see cref="T:System.Net.Http.HttpClient"/> to configure.</param>
            <param name="configureClient">A delegate that is used to configure an <see cref="T:System.Net.Http.HttpClient"/>.</param>
            <returns>An <see cref="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"/> that can be used to configure the client.</returns>
            <remarks>
            <para>
            <see cref="T:System.Net.Http.HttpClient"/> instances that apply the provided configuration can be retrieved using
            <see cref="M:System.Net.Http.IHttpClientFactory.CreateClient(System.String)"/> and providing the matching name.
            </para>
            <para>
            <typeparamref name="TClient"/> instances constructed with the appropriate <see cref="T:System.Net.Http.HttpClient" />
            can be retrieved from <see cref="M:System.IServiceProvider.GetService(System.Type)" /> (and related methods) by providing
            <typeparamref name="TClient"/> as the service type.
            </para>
            <para>
            Use <see cref="F:Microsoft.Extensions.Options.Options.DefaultName"/> as the name to configure the default client.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HttpClientFactoryServiceCollectionExtensions.AddHttpClient``2(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.String,System.Action{System.IServiceProvider,System.Net.Http.HttpClient})">
            <summary>
            Adds the <see cref="T:System.Net.Http.IHttpClientFactory"/> and related services to the <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> and configures
            a binding between the <typeparamref name="TClient" /> type and a named <see cref="T:System.Net.Http.HttpClient"/>.
            </summary>
            <typeparam name="TClient">
            The type of the typed client. The type specified will be registered in the service collection as
            a transient service. See <see cref="T:Microsoft.Extensions.Http.ITypedHttpClientFactory`1" /> for more details about authoring typed clients.
            </typeparam>
            <typeparam name="TImplementation">
            The implementation type of the typed client. The type specified will be instantiated by the
            <see cref="T:Microsoft.Extensions.Http.ITypedHttpClientFactory`1"/>
            </typeparam>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/>.</param>
            <param name="name">The logical name of the <see cref="T:System.Net.Http.HttpClient"/> to configure.</param>
            <param name="configureClient">A delegate that is used to configure an <see cref="T:System.Net.Http.HttpClient"/>.</param>
            <returns>An <see cref="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"/> that can be used to configure the client.</returns>
            <remarks>
            <para>
            <see cref="T:System.Net.Http.HttpClient"/> instances that apply the provided configuration can be retrieved using
            <see cref="M:System.Net.Http.IHttpClientFactory.CreateClient(System.String)"/> and providing the matching name.
            </para>
            <para>
            <typeparamref name="TClient"/> instances constructed with the appropriate <see cref="T:System.Net.Http.HttpClient" />
            can be retrieved from <see cref="M:System.IServiceProvider.GetService(System.Type)" /> (and related methods) by providing
            <typeparamref name="TClient"/> as the service type.
            </para>
            <para>
            Use <see cref="F:Microsoft.Extensions.Options.Options.DefaultName"/> as the name to configure the default client.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HttpClientFactoryServiceCollectionExtensions.AddHttpClient``2(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Func{System.Net.Http.HttpClient,``1})">
            <summary>
            Adds the <see cref="T:System.Net.Http.IHttpClientFactory"/> and related services to the <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> and configures
            a binding between the <typeparamref name="TClient" /> type and a named <see cref="T:System.Net.Http.HttpClient"/>.
            </summary>
            <typeparam name="TClient">
            The type of the typed client. The type specified will be registered in the service collection as
            a transient service. See <see cref="T:Microsoft.Extensions.Http.ITypedHttpClientFactory`1" /> for more details about authoring typed clients.
            </typeparam>
            <typeparam name="TImplementation">
            The implementation type of the typed client.
            </typeparam>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/>.</param>
            <param name="factory">A delegate that is used to create an instance of <typeparamref name="TClient"/>.</param>
            <returns>An <see cref="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"/> that can be used to configure the client.</returns>
            <remarks>
            <para>
            <see cref="T:System.Net.Http.HttpClient"/> instances that apply the provided configuration can be retrieved using
            <see cref="M:System.Net.Http.IHttpClientFactory.CreateClient(System.String)"/> and providing the matching name.
            </para>
            <para>
            <typeparamref name="TClient"/> instances constructed with the appropriate <see cref="T:System.Net.Http.HttpClient" />
            can be retrieved from <see cref="M:System.IServiceProvider.GetService(System.Type)" /> (and related methods) by providing
            <typeparamref name="TClient"/> as the service type.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HttpClientFactoryServiceCollectionExtensions.AddHttpClient``2(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.String,System.Func{System.Net.Http.HttpClient,``1})">
            <summary>
            Adds the <see cref="T:System.Net.Http.IHttpClientFactory"/> and related services to the <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> and configures
            a binding between the <typeparamref name="TClient" /> type and a named <see cref="T:System.Net.Http.HttpClient"/>.
            </summary>
            <typeparam name="TClient">
            The type of the typed client. The type specified will be registered in the service collection as
            a transient service. See <see cref="T:Microsoft.Extensions.Http.ITypedHttpClientFactory`1" /> for more details about authoring typed clients.
            </typeparam>
            <typeparam name="TImplementation">
            The implementation type of the typed client.
            </typeparam>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/>.</param>
            <param name="name">The logical name of the <see cref="T:System.Net.Http.HttpClient"/> to configure.</param>
            <param name="factory">A delegate that is used to create an instance of <typeparamref name="TClient"/>.</param>
            <returns>An <see cref="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"/> that can be used to configure the client.</returns>
            <remarks>
            <para>
            <see cref="T:System.Net.Http.HttpClient"/> instances that apply the provided configuration can be retrieved using
            <see cref="M:System.Net.Http.IHttpClientFactory.CreateClient(System.String)"/> and providing the matching name.
            </para>
            <para>
            <typeparamref name="TClient"/> instances constructed with the appropriate <see cref="T:System.Net.Http.HttpClient" />
            can be retrieved from <see cref="M:System.IServiceProvider.GetService(System.Type)" /> (and related methods) by providing
            <typeparamref name="TClient"/> as the service type.
            </para>
            <typeparamref name="TImplementation">
            </typeparamref>
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HttpClientFactoryServiceCollectionExtensions.AddHttpClient``2(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Func{System.Net.Http.HttpClient,System.IServiceProvider,``1})">
            <summary>
            Adds the <see cref="T:System.Net.Http.IHttpClientFactory"/> and related services to the <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> and configures
            a binding between the <typeparamref name="TClient" /> type and a named <see cref="T:System.Net.Http.HttpClient"/>.
            </summary>
            <typeparam name="TClient">
            The type of the typed client. The type specified will be registered in the service collection as
            a transient service. See <see cref="T:Microsoft.Extensions.Http.ITypedHttpClientFactory`1" /> for more details about authoring typed clients.
            </typeparam>
            <typeparam name="TImplementation">
            The implementation type of the typed client.
            </typeparam>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/>.</param>
            <param name="factory">A delegate that is used to create an instance of <typeparamref name="TClient"/>.</param>
            <returns>An <see cref="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"/> that can be used to configure the client.</returns>
            <remarks>
            <para>
            <see cref="T:System.Net.Http.HttpClient"/> instances that apply the provided configuration can be retrieved using
            <see cref="M:System.Net.Http.IHttpClientFactory.CreateClient(System.String)"/> and providing the matching name.
            </para>
            <para>
            <typeparamref name="TClient"/> instances constructed with the appropriate <see cref="T:System.Net.Http.HttpClient" />
            can be retrieved from <see cref="M:System.IServiceProvider.GetService(System.Type)" /> (and related methods) by providing
            <typeparamref name="TClient"/> as the service type.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.HttpClientFactoryServiceCollectionExtensions.AddHttpClient``2(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.String,System.Func{System.Net.Http.HttpClient,System.IServiceProvider,``1})">
            <summary>
            Adds the <see cref="T:System.Net.Http.IHttpClientFactory"/> and related services to the <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> and configures
            a binding between the <typeparamref name="TClient" /> type and a named <see cref="T:System.Net.Http.HttpClient"/>.
            </summary>
            <typeparam name="TClient">
            The type of the typed client. The type specified will be registered in the service collection as
            a transient service. See <see cref="T:Microsoft.Extensions.Http.ITypedHttpClientFactory`1" /> for more details about authoring typed clients.
            </typeparam>
            <typeparam name="TImplementation">
            The implementation type of the typed client.
            </typeparam>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/>.</param>
            <param name="name">The logical name of the <see cref="T:System.Net.Http.HttpClient"/> to configure.</param>
            <param name="factory">A delegate that is used to create an instance of <typeparamref name="TClient"/>.</param>
            <returns>An <see cref="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"/> that can be used to configure the client.</returns>
            <remarks>
            <para>
            <see cref="T:System.Net.Http.HttpClient"/> instances that apply the provided configuration can be retrieved using
            <see cref="M:System.Net.Http.IHttpClientFactory.CreateClient(System.String)"/> and providing the matching name.
            </para>
            <para>
            <typeparamref name="TClient"/> instances constructed with the appropriate <see cref="T:System.Net.Http.HttpClient" />
            can be retrieved from <see cref="M:System.IServiceProvider.GetService(System.Type)" /> (and related methods) by providing
            <typeparamref name="TClient"/> as the service type.
            </para>
            </remarks>
        </member>
        <member name="T:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder">
            <summary>
            A builder for configuring named <see cref="T:System.Net.Http.HttpClient"/> instances returned by <see cref="T:System.Net.Http.IHttpClientFactory"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder.Name">
            <summary>
            Gets the name of the client configured by this builder.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.DependencyInjection.IHttpClientBuilder.Services">
            <summary>
            Gets the application service collection.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Internal.TypeNameHelper.GetTypeDisplayName(System.Type,System.Boolean,System.Boolean,System.Boolean,System.Char)">
            <summary>
            Pretty print a type name.
            </summary>
            <param name="type">The <see cref="T:System.Type"/>.</param>
            <param name="fullName"><c>true</c> to print a fully qualified name.</param>
            <param name="includeGenericParameterNames"><c>true</c> to include generic parameter names.</param>
            <param name="includeGenericParameters"><c>true</c> to include generic parameters.</param>
            <param name="nestedTypeDelimiter">Character to use as a delimiter in nested type names</param>
            <returns>The pretty printed type name.</returns>
        </member>
        <member name="T:System.Net.Http.HttpClientFactoryExtensions">
            <summary>
            Extensions methods for <see cref="T:System.Net.Http.IHttpClientFactory"/>.
            </summary>
        </member>
        <member name="M:System.Net.Http.HttpClientFactoryExtensions.CreateClient(System.Net.Http.IHttpClientFactory)">
            <summary>
            Creates a new <see cref="T:System.Net.Http.HttpClient"/> using the default configuration.
            </summary>
            <param name="factory">The <see cref="T:System.Net.Http.IHttpClientFactory"/>.</param>
            <returns>An <see cref="T:System.Net.Http.HttpClient"/> configured using the default configuration.</returns>
        </member>
        <member name="T:System.Net.Http.HttpMessageHandlerFactoryExtensions">
            <summary>
            Extensions methods for <see cref="T:System.Net.Http.IHttpMessageHandlerFactory"/>.
            </summary>
        </member>
        <member name="M:System.Net.Http.HttpMessageHandlerFactoryExtensions.CreateHandler(System.Net.Http.IHttpMessageHandlerFactory)">
            <summary>
            Creates a new <see cref="T:System.Net.Http.HttpMessageHandler"/> using the default configuration.
            </summary>
            <param name="factory">The <see cref="T:System.Net.Http.IHttpMessageHandlerFactory"/>.</param>
            <returns>An <see cref="T:System.Net.Http.HttpMessageHandler"/> configured using the default configuration.</returns>
        </member>
        <member name="T:System.Net.Http.IHttpClientFactory">
            <summary>
            A factory abstraction for a component that can create <see cref="T:System.Net.Http.HttpClient"/> instances with custom
            configuration for a given logical name.
            </summary>
            <remarks>
            A default <see cref="T:System.Net.Http.IHttpClientFactory"/> can be registered in an <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/>
            by calling <see cref="M:Microsoft.Extensions.DependencyInjection.HttpClientFactoryServiceCollectionExtensions.AddHttpClient(Microsoft.Extensions.DependencyInjection.IServiceCollection)"/>.
            The default <see cref="T:System.Net.Http.IHttpClientFactory"/> will be registered in the service collection as a singleton.
            </remarks>
        </member>
        <member name="M:System.Net.Http.IHttpClientFactory.CreateClient(System.String)">
            <summary>
            Creates and configures an <see cref="T:System.Net.Http.HttpClient"/> instance using the configuration that corresponds
            to the logical name specified by <paramref name="name"/>.
            </summary>
            <param name="name">The logical name of the client to create.</param>
            <returns>A new <see cref="T:System.Net.Http.HttpClient"/> instance.</returns>
            <remarks>
            <para>
            Each call to <see cref="M:System.Net.Http.IHttpClientFactory.CreateClient(System.String)"/> is guaranteed to return a new <see cref="T:System.Net.Http.HttpClient"/>
            instance. It is generally not necessary to dispose of the <see cref="T:System.Net.Http.HttpClient"/> as the
            <see cref="T:System.Net.Http.IHttpClientFactory"/> tracks and disposes resources used by the <see cref="T:System.Net.Http.HttpClient"/>.
            </para>
            <para>
            Callers are also free to mutate the returned <see cref="T:System.Net.Http.HttpClient"/> instance's public properties
            as desired.
            </para>
            </remarks>
        </member>
        <member name="T:System.Net.Http.IHttpMessageHandlerFactory">
            <summary>
            A factory abstraction for a component that can create <see cref="T:System.Net.Http.HttpMessageHandler"/> instances with custom
            configuration for a given logical name.
            </summary>
            <remarks>
            A default <see cref="T:System.Net.Http.IHttpMessageHandlerFactory"/> can be registered in an <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/>
            by calling <see cref="M:Microsoft.Extensions.DependencyInjection.HttpClientFactoryServiceCollectionExtensions.AddHttpClient(Microsoft.Extensions.DependencyInjection.IServiceCollection)"/>.
            The default <see cref="T:System.Net.Http.IHttpMessageHandlerFactory"/> will be registered in the service collection as a singleton.
            </remarks>
        </member>
        <member name="M:System.Net.Http.IHttpMessageHandlerFactory.CreateHandler(System.String)">
            <summary>
            Creates and configures an <see cref="T:System.Net.Http.HttpMessageHandler"/> instance using the configuration that corresponds
            to the logical name specified by <paramref name="name"/>.
            </summary>
            <param name="name">The logical name of the message handler to create.</param>
            <returns>A new <see cref="T:System.Net.Http.HttpMessageHandler"/> instance.</returns>
            <remarks>
            <para>
            The default <see cref="T:System.Net.Http.IHttpMessageHandlerFactory"/> implementation may cache the underlying
            <see cref="T:System.Net.Http.HttpMessageHandler"/> instances to improve performance.
            </para>
            <para>
            The default <see cref="T:System.Net.Http.IHttpMessageHandlerFactory"/> implementation also manages the lifetime of the
            handler created, so disposing of the <see cref="T:System.Net.Http.HttpMessageHandler"/> returned by this method may
            have no effect.
            </para>
            </remarks>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMembersAttribute">
             <summary>
             Indicates that certain members on a specified <see cref="T:System.Type"/> are accessed dynamically,
             for example through <see cref="N:System.Reflection"/>.
             </summary>
             <remarks>
             This allows tools to understand which members are being accessed during the execution
             of a program.
            
             This attribute is valid on members whose type is <see cref="T:System.Type"/> or <see cref="T:System.String"/>.
            
             When this attribute is applied to a location of type <see cref="T:System.String"/>, the assumption is
             that the string represents a fully qualified type name.
            
             When this attribute is applied to a class, interface, or struct, the members specified
             can be accessed dynamically on <see cref="T:System.Type"/> instances returned from calling
             <see cref="M:System.Object.GetType"/> on instances of that class, interface, or struct.
            
             If the attribute is applied to a method it's treated as a special case and it implies
             the attribute should be applied to the "this" parameter of the method. As such the attribute
             should only be used on instance methods of types assignable to System.Type (or string, but no methods
             will use it there).
             </remarks>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMembersAttribute.#ctor(System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMembersAttribute"/> class
            with the specified member types.
            </summary>
            <param name="memberTypes">The types of members dynamically accessed.</param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMembersAttribute.MemberTypes">
            <summary>
            Gets the <see cref="T:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes"/> which specifies the type
            of members dynamically accessed.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes">
             <summary>
             Specifies the types of members that are dynamically accessed.
            
             This enumeration has a <see cref="T:System.FlagsAttribute"/> attribute that allows a
             bitwise combination of its member values.
             </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.None">
            <summary>
            Specifies no members.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.PublicParameterlessConstructor">
            <summary>
            Specifies the default, parameterless public constructor.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.PublicConstructors">
            <summary>
            Specifies all public constructors.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.NonPublicConstructors">
            <summary>
            Specifies all non-public constructors.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.PublicMethods">
            <summary>
            Specifies all public methods.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.NonPublicMethods">
            <summary>
            Specifies all non-public methods.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.PublicFields">
            <summary>
            Specifies all public fields.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.NonPublicFields">
            <summary>
            Specifies all non-public fields.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.PublicNestedTypes">
            <summary>
            Specifies all public nested types.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.NonPublicNestedTypes">
            <summary>
            Specifies all non-public nested types.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.PublicProperties">
            <summary>
            Specifies all public properties.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.NonPublicProperties">
            <summary>
            Specifies all non-public properties.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.PublicEvents">
            <summary>
            Specifies all public events.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.NonPublicEvents">
            <summary>
            Specifies all non-public events.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.Interfaces">
            <summary>
            Specifies all interfaces implemented by the type.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.All">
            <summary>
            Specifies all members.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute">
            <summary>
            Suppresses reporting of a specific rule violation, allowing multiple suppressions on a
            single code artifact.
            </summary>
            <remarks>
            <see cref="T:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute"/> is different than
            <see cref="T:System.Diagnostics.CodeAnalysis.SuppressMessageAttribute"/> in that it doesn't have a
            <see cref="T:System.Diagnostics.ConditionalAttribute"/>. So it is always preserved in the compiled assembly.
            </remarks>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute"/>
            class, specifying the category of the tool and the identifier for an analysis rule.
            </summary>
            <param name="category">The category for the attribute.</param>
            <param name="checkId">The identifier of the analysis rule the attribute applies to.</param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.Category">
            <summary>
            Gets the category identifying the classification of the attribute.
            </summary>
            <remarks>
            The <see cref="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.Category"/> property describes the tool or tool analysis category
            for which a message suppression attribute applies.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.CheckId">
            <summary>
            Gets the identifier of the analysis tool rule to be suppressed.
            </summary>
            <remarks>
            Concatenated together, the <see cref="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.Category"/> and <see cref="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.CheckId"/>
            properties form a unique check identifier.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.Scope">
            <summary>
            Gets or sets the scope of the code that is relevant for the attribute.
            </summary>
            <remarks>
            The Scope property is an optional argument that specifies the metadata scope for which
            the attribute is relevant.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.Target">
            <summary>
            Gets or sets a fully qualified path that represents the target of the attribute.
            </summary>
            <remarks>
            The <see cref="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.Target"/> property is an optional argument identifying the analysis target
            of the attribute. An example value is "System.IO.Stream.ctor():System.Void".
            Because it is fully qualified, it can be long, particularly for targets such as parameters.
            The analysis tool user interface should be capable of automatically formatting the parameter.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.MessageId">
            <summary>
            Gets or sets an optional argument expanding on exclusion criteria.
            </summary>
            <remarks>
            The <see cref="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.MessageId"/> property is an optional argument that specifies additional
            exclusion where the literal metadata target is not sufficiently precise. For example,
            the <see cref="T:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute"/> cannot be applied within a method,
            and it may be desirable to suppress a violation against a statement in the method that will
            give a rule violation, but not against all statements in the method.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.Justification">
            <summary>
            Gets or sets the justification for suppressing the code analysis message.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.AllowNullAttribute">
            <summary>Specifies that null is allowed as an input even if the corresponding type disallows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DisallowNullAttribute">
            <summary>Specifies that null is disallowed as an input even if the corresponding type allows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MaybeNullAttribute">
            <summary>Specifies that an output may be null even if the corresponding type disallows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullAttribute">
            <summary>Specifies that an output will not be null even if the corresponding type allows it. Specifies that an input argument was not null when the call returns.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute">
            <summary>Specifies that when a method returns <see cref="P:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.ReturnValue"/>, the parameter may be null even if the corresponding type disallows it.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified return value condition.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter may be null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute">
            <summary>Specifies that when a method returns <see cref="P:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.ReturnValue"/>, the parameter will not be null even if the corresponding type allows it.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified return value condition.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter will not be null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute">
            <summary>Specifies that the output will be non-null if the named parameter is non-null.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute.#ctor(System.String)">
            <summary>Initializes the attribute with the associated parameter name.</summary>
            <param name="parameterName">
            The associated parameter name.  The output will be non-null if the argument to the parameter specified is non-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute.ParameterName">
            <summary>Gets the associated parameter name.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DoesNotReturnAttribute">
            <summary>Applied to a method that will never return under any circumstance.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute">
            <summary>Specifies that the method will not return if the associated Boolean parameter is passed the specified value.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified parameter value.</summary>
            <param name="parameterValue">
            The condition parameter value. Code after the method will be considered unreachable by diagnostics if the argument to
            the associated parameter matches this value.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute.ParameterValue">
            <summary>Gets the condition parameter value.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute">
            <summary>Specifies that the method or property will ensure that the listed field and property members have not-null values.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.#ctor(System.String)">
            <summary>Initializes the attribute with a field or property member.</summary>
            <param name="member">
            The field or property member that is promised to be not-null.
            </param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.#ctor(System.String[])">
            <summary>Initializes the attribute with the list of field and property members.</summary>
            <param name="members">
            The list of field and property members that are promised to be not-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.Members">
            <summary>Gets field or property member names.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute">
            <summary>Specifies that the method or property will ensure that the listed field and property members have not-null values when returning with the specified return value condition.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.#ctor(System.Boolean,System.String)">
            <summary>Initializes the attribute with the specified return value condition and a field or property member.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter will not be null.
            </param>
            <param name="member">
            The field or property member that is promised to be not-null.
            </param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.#ctor(System.Boolean,System.String[])">
            <summary>Initializes the attribute with the specified return value condition and list of field and property members.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter will not be null.
            </param>
            <param name="members">
            The list of field and property members that are promised to be not-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.Members">
            <summary>Gets field or property member names.</summary>
        </member>
        <member name="P:System.SR.HttpMessageHandlerBuilder_AdditionalHandlerIsNull">
            <summary>The '{0}' must not contain a null entry.</summary>
        </member>
        <member name="P:System.SR.HttpMessageHandlerBuilder_AdditionHandlerIsInvalid">
            <summary>The '{0}' property must be null. '{1}' instances provided to '{2}' must not be reused or cached.{3}Handler: '{4}'</summary>
        </member>
        <member name="P:System.SR.HttpMessageHandlerBuilder_PrimaryHandlerIsNull">
            <summary>The '{0}' must not be null.</summary>
        </member>
        <member name="P:System.SR.HandlerLifetime_InvalidValue">
            <summary>The handler lifetime must be at least 1 second.</summary>
        </member>
    </members>
</doc>
