using System;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using FlightPig.Models;

namespace FlightPig.Services
{
    /// <summary>
    /// Service for communicating with Ollama API
    /// </summary>
    public class OllamaService
    {
        private readonly HttpClient _httpClient;
        private readonly string _baseUrl;
        private readonly string _modelName;

        public OllamaService(HttpClient httpClient, string baseUrl = "http://localhost:11434", string modelName = "deepseek-r1:8b")
        {
            _httpClient = httpClient;
            _baseUrl = baseUrl;
            _modelName = modelName;

            // Set a longer timeout for model downloads (30 minutes)
            _httpClient.Timeout = TimeSpan.FromMinutes(30);
        }

        /// <summary>
        /// Check if the model is already available locally
        /// </summary>
        public async Task<bool> IsModelAvailableAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync($"{_baseUrl}/api/tags");
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var tagsResponse = JsonSerializer.Deserialize<JsonElement>(content);

                    if (tagsResponse.TryGetProperty("models", out var models))
                    {
                        foreach (var model in models.EnumerateArray())
                        {
                            if (model.TryGetProperty("name", out var name) &&
                                name.GetString().StartsWith(_modelName.Split(':')[0]))
                            {
                                return true;
                            }
                        }
                    }
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Ensure the model is downloaded and available
        /// </summary>
        public async Task<bool> PullModelAsync()
        {
            try
            {
                Console.WriteLine($"Checking if model '{_modelName}' is already available...");

                // First check if model is already available
                if (await IsModelAvailableAsync())
                {
                    Console.WriteLine($"Model '{_modelName}' is already available locally.");
                    return true;
                }

                Console.WriteLine($"Model '{_modelName}' not found locally. Downloading...");
                Console.WriteLine("This may take several minutes for large models. Please wait...");

                var pullRequest = new
                {
                    name = _modelName
                };

                var json = JsonSerializer.Serialize(pullRequest);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                Console.WriteLine("Sending pull request to Ollama...");
                var response = await _httpClient.PostAsync($"{_baseUrl}/api/pull", content);

                if (response.IsSuccessStatusCode)
                {
                    Console.WriteLine($"Model '{_modelName}' download completed successfully.");
                    return true;
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"Failed to pull model: {response.StatusCode}");
                    Console.WriteLine($"Error details: {errorContent}");
                    return false;
                }
            }
            catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException)
            {
                Console.WriteLine($"Model download timed out. The model '{_modelName}' may be very large.");
                Console.WriteLine("Please try running 'ollama pull deepseek-r1:8b' manually from command line.");
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error pulling model: {ex.Message}");
                if (ex.Message.Contains("timeout") || ex.Message.Contains("Timeout"))
                {
                    Console.WriteLine("This appears to be a timeout issue. Try running 'ollama pull deepseek-r1:8b' manually.");
                }
                return false;
            }
        }

        /// <summary>
        /// Generate a mission based on current aircraft and location information
        /// </summary>
        public async Task<Mission> GenerateMissionAsync(AircraftInfo aircraftInfo)
        {
            try
            {
                var prompt = CreateMissionPrompt(aircraftInfo);
                
                var chatRequest = new
                {
                    model = _modelName,
                    messages = new[]
                    {
                        new { role = "system", content = "You are a flight mission generator for Microsoft Flight Simulator. Generate realistic and engaging flight missions based on the current aircraft and location. Always respond with valid JSON in the exact format requested." },
                        new { role = "user", content = prompt }
                    },
                    stream = false,
                    format = "json"
                };

                var json = JsonSerializer.Serialize(chatRequest);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_baseUrl}/api/chat", content);
                
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var chatResponse = JsonSerializer.Deserialize<JsonElement>(responseContent);
                    
                    if (chatResponse.TryGetProperty("message", out var message) &&
                        message.TryGetProperty("content", out var messageContent))
                    {
                        var missionJson = messageContent.GetString();
                        if (!string.IsNullOrEmpty(missionJson))
                        {
                            return JsonSerializer.Deserialize<Mission>(missionJson);
                        }
                    }
                }
                else
                {
                    Console.WriteLine($"Failed to generate mission: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error generating mission: {ex.Message}");
            }

            return null;
        }

        private string CreateMissionPrompt(AircraftInfo aircraftInfo)
        {
            var onGroundStatus = aircraftInfo.OnGround ? "on the ground" : "in flight";
            
            return $@"Generate a flight mission for the following aircraft and situation:

Aircraft: {aircraftInfo.Title}
Current Location: Latitude {aircraftInfo.Latitude:F4}, Longitude {aircraftInfo.Longitude:F4}
Altitude: {aircraftInfo.Altitude:F0} feet
Heading: {aircraftInfo.Heading:F0} degrees
Airspeed: {aircraftInfo.AirspeedKnots:F0} knots
Status: {onGroundStatus}

Create a realistic and engaging mission with 2-4 objectives. The mission should be appropriate for the aircraft type and current location.

Respond with JSON in this exact format:
{{
  ""title"": ""Mission Title"",
  ""description"": ""Brief mission description"",
  ""objectives"": [
    {{
      ""title"": ""Objective Title"",
      ""description"": ""Objective description"",
      ""type"": ""FlyOver"",
      ""latitude"": 40.7128,
      ""longitude"": -74.0060,
      ""altitude"": 3000,
      ""completed"": false,
      ""progress"": 0.0
    }}
  ]
}}

Use objective types: FlyOver, LandAt, LandAtAndWait, TakeOffFrom, NavigateTo, MaintainAltitude, MaintainSpeed, ContactATC, FollowRoute";
        }
    }
}
