﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Runtime.InteropServices</name>
  </assembly>
  <members>
    <member name="T:System.DataMisalignedException">
      <summary>데이터 크기의 배수가 아닌 주소에서 데이터 단위를 읽거나 쓰면 이 예외가 throw됩니다.이 클래스는 상속될 수 없습니다.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.DataMisalignedException.#ctor">
      <summary>
        <see cref="T:System.DataMisalignedException" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
    </member>
    <member name="M:System.DataMisalignedException.#ctor(System.String)">
      <summary>지정된 오류 메시지를 사용하여 <see cref="T:System.DataMisalignedException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">오류를 설명하는 <see cref="T:System.String" /> 개체입니다.<paramref name="message" /> 내용은 사용자의 이해를 돕기 위한 것입니다.이 생성자의 호출자는 이 문자열이 현재 시스템 문화권에 맞게 지역화되었는지 확인하는 데 필요합니다.</param>
    </member>
    <member name="M:System.DataMisalignedException.#ctor(System.String,System.Exception)">
      <summary>지정된 오류 메시지와 내부 예외를 사용하여 <see cref="T:System.DataMisalignedException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">오류를 설명하는 <see cref="T:System.String" /> 개체입니다.<paramref name="message" /> 내용은 사용자의 이해를 돕기 위한 것입니다.이 생성자의 호출자는 이 문자열이 현재 시스템 문화권에 맞게 지역화되었는지 확인하는 데 필요합니다.</param>
      <param name="innerException">현재 <see cref="T:System.DataMisalignedException" />의 원인이 되는 예외입니다.<paramref name="innerException" /> 매개 변수가 null이 아니면 현재 예외는 내부 예외를 처리하는 catch 블록에서 발생합니다.</param>
    </member>
    <member name="T:System.DllNotFoundException">
      <summary>DLL 가져오기에 지정된 DLL을 찾을 수 없을 때 throw되는 예외입니다.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.DllNotFoundException.#ctor">
      <summary>기본 속성을 사용하여 <see cref="T:System.DllNotFoundException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.DllNotFoundException.#ctor(System.String)">
      <summary>지정된 오류 메시지를 사용하여 <see cref="T:System.DllNotFoundException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">예외에 대한 이유를 설명하는 오류 메시지입니다. </param>
    </member>
    <member name="M:System.DllNotFoundException.#ctor(System.String,System.Exception)">
      <summary>지정된 오류 메시지와 해당 예외의 근본 원인인 내부 예외에 대한 참조를 사용하여 <see cref="T:System.DllNotFoundException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">예외에 대한 이유를 설명하는 오류 메시지입니다. </param>
      <param name="inner">현재 예외의 원인이 되는 예외입니다.<paramref name="inner" /> 매개 변수가 null이 아니면 현재 예외는 내부 예외를 처리하는 catch 블록에서 발생합니다.</param>
    </member>
    <member name="T:System.Reflection.Missing">
      <summary>누락된 <see cref="T:System.Object" />를 나타냅니다.이 클래스는 상속될 수 없습니다.</summary>
    </member>
    <member name="F:System.Reflection.Missing.Value">
      <summary>
        <see cref="T:System.Reflection.Missing" /> 클래스의 유일한 인스턴스를 나타냅니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ArrayWithOffset">
      <summary>지정된 배열 내에 배열 및 오프셋을 캡슐화합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.#ctor(System.Object,System.Int32)">
      <summary>
        <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 구조체의 새 인스턴스를 초기화합니다.</summary>
      <param name="array">관리되는 배열입니다. </param>
      <param name="offset">플랫폼 호출을 통해 전달될 요소의 오프셋(바이트)입니다. </param>
      <exception cref="T:System.ArgumentException">배열이 2GB(기가바이트)보다 큰 경우</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.Equals(System.Object)">
      <summary>지정된 개체가 현재 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 개체와 일치하는지 여부를 나타냅니다.</summary>
      <returns>개체가 이 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />과 일치하면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="obj">이 인스턴스와 비교할 개체입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.Equals(System.Runtime.InteropServices.ArrayWithOffset)">
      <summary>지정된 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 개체가 현재 인스턴스와 일치하는지 여부를 나타냅니다.</summary>
      <returns>지정된 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 개체가 현재 인스턴스와 일치하면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="obj">이 인스턴스와 비교할 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 개체입니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.GetArray">
      <summary>이 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />에서 참조한 관리되는 배열을 반환합니다.</summary>
      <returns>이 인스턴스가 참조하는 관리되는 배열입니다.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.GetHashCode">
      <summary>이 값 형식에 대한 해시 코드를 반환합니다.</summary>
      <returns>이 인스턴스의 해시 코드입니다.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.GetOffset">
      <summary>이 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" />이 생성될 때 제공된 오프셋을 반환합니다.</summary>
      <returns>이 인스턴스의 오프셋입니다.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.op_Equality(System.Runtime.InteropServices.ArrayWithOffset,System.Runtime.InteropServices.ArrayWithOffset)">
      <summary>지정된 두 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 개체의 값이 같은지 여부를 확인합니다.</summary>
      <returns>
        <paramref name="a" />의 값이 <paramref name="b" />의 값과 같으면 true를 반환하고, 그렇지 않으면 false를 반환합니다.</returns>
      <param name="a">
        <paramref name="b" /> 매개 변수와 비교할 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 개체입니다. </param>
      <param name="b">
        <paramref name="a" /> 매개 변수와 비교할 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 개체입니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ArrayWithOffset.op_Inequality(System.Runtime.InteropServices.ArrayWithOffset,System.Runtime.InteropServices.ArrayWithOffset)">
      <summary>지정된 두 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 개체의 값이 같은지 여부를 확인합니다.</summary>
      <returns>
        <paramref name="a" />의 값이 <paramref name="b" />의 값과 같지 않으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="a">
        <paramref name="b" /> 매개 변수와 비교할 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 개체입니다. </param>
      <param name="b">
        <paramref name="a" /> 매개 변수와 비교할 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 개체입니다.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.BestFitMappingAttribute">
      <summary>유니코드 문자를 일치하는 가장 비슷한 ANSI 문자로 변환할지 여부를 제어합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.BestFitMappingAttribute.#ctor(System.Boolean)">
      <summary>
        <see cref="P:System.Runtime.InteropServices.BestFitMappingAttribute.BestFitMapping" /> 속성 값으로 설정된 <see cref="T:System.Runtime.InteropServices.BestFitMappingAttribute" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="BestFitMapping">true는 가장 적합한 매핑 기능이 활성화된다는 것을 나타내고, 그렇지 않으면 false입니다.기본값은 true입니다.</param>
    </member>
    <member name="P:System.Runtime.InteropServices.BestFitMappingAttribute.BestFitMapping">
      <summary>유니코드 문자를 ANSI 문자로 변환할 때 가장 적합한 매핑 동작을 가져옵니다.</summary>
      <returns>가장 적합한 매핑이 활성화된 경우에는 true이고, 그렇지 않으면 false입니다.기본값은 true입니다.</returns>
    </member>
    <member name="F:System.Runtime.InteropServices.BestFitMappingAttribute.ThrowOnUnmappableChar">
      <summary>ANSI "?" 문자로 변환되는 매핑할 수 없는 유니코드 문자가 나오면 예외를 throw하거나 throw하지 않습니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.BStrWrapper">
      <summary>VT_BSTR 형식의 데이터를 관리 코드에서 비관리 코드로 마샬링합니다.이 클래스는 상속될 수 없습니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.BStrWrapper.#ctor(System.Object)">
      <summary>지정된 <see cref="T:System.Object" /> 개체를 사용하여 <see cref="T:System.Runtime.InteropServices.BStrWrapper" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="value">래핑하여 VT_BSTR로 마샬링할 개체합니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.BStrWrapper.#ctor(System.String)">
      <summary>지정된 <see cref="T:System.String" /> 개체를 사용하여 <see cref="T:System.Runtime.InteropServices.BStrWrapper" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="value">래핑하여 VT_BSTR로 마샬링할 개체합니다.</param>
    </member>
    <member name="P:System.Runtime.InteropServices.BStrWrapper.WrappedObject">
      <summary>VT_BSTR 형식으로 마샬링할 래핑된 <see cref="T:System.String" /> 개체를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Runtime.InteropServices.BStrWrapper" />로 래핑되는 개체입니다.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.CallingConvention">
      <summary>비관리 코드에서 구현된 메서드를 호출하는 데 필요한 호출 규칙을 지정합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CallingConvention.Cdecl">
      <summary>호출자가 스택을 정리합니다.varargs를 사용하여 함수를 호출할 수 있으므로 Printf와 같은 가변 개수의 매개 변수를 허용하는 메서드에 사용하기에 적합합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CallingConvention.StdCall">
      <summary>호출 수신자가 스택을 정리합니다.이는 플랫폼 호출을 사용하여 관리되지 않는 함수를 호출하는 기본 규칙입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CallingConvention.ThisCall">
      <summary>첫 번째 매개 변수는 this 포인터이며 레지스터 ECX에 저장됩니다.다른 매개 변수는 스택에 푸시됩니다.이 호출 규칙은 관리되지 않는 DLL에서 내보낸 클래스의 메서드를 호출하는 데 사용됩니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CallingConvention.Winapi">
      <summary>이 멤버는 실제로는 호출 규칙이 아니라 기본 플랫폼 호출 규칙을 사용합니다.예를 들어, Windows에서는 기본값이 <see cref="F:System.Runtime.InteropServices.CallingConvention.StdCall" />이고 Windows CE .NET에서는 기본값이 <see cref="F:System.Runtime.InteropServices.CallingConvention.Cdecl" />입니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ClassInterfaceAttribute">
      <summary>COM에 노출될 클래스에 대해 생성될 클래스 인터페이스의 형식을 나타냅니다(인터페이스가 생성되는 경우).</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ClassInterfaceAttribute.#ctor(System.Int16)">
      <summary>지정된 <see cref="T:System.Runtime.InteropServices.ClassInterfaceType" /> 열거형 값을 사용하여 <see cref="T:System.Runtime.InteropServices.ClassInterfaceAttribute" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="classInterfaceType">클래스에 대해 생성되는 인터페이스의 형식을 설명합니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ClassInterfaceAttribute.#ctor(System.Runtime.InteropServices.ClassInterfaceType)">
      <summary>지정된 <see cref="T:System.Runtime.InteropServices.ClassInterfaceType" /> 열거형 멤버를 사용하여 <see cref="T:System.Runtime.InteropServices.ClassInterfaceAttribute" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="classInterfaceType">클래스에 대해 생성된 인터페이스의 형식을 설명하는 <see cref="T:System.Runtime.InteropServices.ClassInterfaceType" /> 값 중 하나입니다. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.ClassInterfaceAttribute.Value">
      <summary>클래스에 대해 어떤 형식의 인터페이스가 생성되어야 하는지를 설명하는 <see cref="T:System.Runtime.InteropServices.ClassInterfaceType" /> 값을 가져옵니다.</summary>
      <returns>클래스에 대해 어떤 형식의 인스턴스가 생성되어야 하는지를 설명하는 <see cref="T:System.Runtime.InteropServices.ClassInterfaceType" /> 값입니다.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ClassInterfaceType">
      <summary>클래스에 대해 생성되는 클래스 인터페이스의 형식을 식별합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ClassInterfaceType.AutoDispatch">
      <summary>클래스가 COM 클라이언트에 대해 런타임에 바인딩만 지원함을 나타냅니다.클래스의 dispinterface는 요청 시 COM 클라이언트에 자동으로 노출됩니다.Tlbexp.exe(형식 라이브러리 내보내기)에서 생성된 형식 라이브러리는 클라이언트가 인터페이스의 DISPID를 캐시하지 못하게 하기 위해 dispinterface에 대한 형식 정보를 포함하지 않습니다.클라이언트는 런타임에만 인터페이스에 바인딩할 수 있으므로 dispinterface에는 <see cref="T:System.Runtime.InteropServices.ClassInterfaceAttribute" />에서 설명한 버전 관리 문제가 나타내지 않습니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ClassInterfaceType.AutoDual">
      <summary>이중 클래스 인터페이스가 클래스에 대해 자동으로 생성되고 COM에 노출됨을 나타냅니다.클래스 인터페이스에 대해 형식 정보가 생성되어 형식 라이브러리에 게시됩니다.<see cref="T:System.Runtime.InteropServices.ClassInterfaceAttribute" />에서 설명하는 것처럼 버전 관리 제한 때문에 AutoDual은 사용하지 않아야 합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ClassInterfaceType.None">
      <summary>해당 클래스에 대해 클래스 인터페이스가 생성되지 않음을 나타냅니다.명시적으로 구현되는 인터페이스가 없으면 런타임에 바인딩될 때 IDispatch 인터페이스를 통해서만 클래스에 액세스할 수 있습니다.이것이 <see cref="T:System.Runtime.InteropServices.ClassInterfaceAttribute" />에 대해 권장되는 설정입니다.클래스에서 명시적으로 구현한 인터페이스를 통해 기능을 노출시키는 유일한 방법은 ClassInterfaceType.None을 사용하는 것입니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.CoClassAttribute">
      <summary>형식 라이브러리에서 가져온 coclass의 클래스 식별자를 지정합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.CoClassAttribute.#ctor(System.Type)">
      <summary>원본 coclass의 클래스 식별자를 사용하여 <see cref="T:System.Runtime.InteropServices.CoClassAttribute" />의 새 인스턴스를 초기화합니다.</summary>
      <param name="coClass">원본 coclass의 클래스 식별자를 포함하는 <see cref="T:System.Type" />입니다. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.CoClassAttribute.CoClass">
      <summary>원본 coclass의 클래스 식별자를 가져옵니다.</summary>
      <returns>원본 coclass의 클래스 식별자를 포함하는 <see cref="T:System.Type" />입니다.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ComAwareEventInfo">
      <summary>이벤트 처리기의 런타임 바인딩 등록을 허용합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComAwareEventInfo.#ctor(System.Type,System.String)">
      <summary>지정된 형식과 해당 형식의 이벤트 이름을 사용하여 <see cref="T:System.Runtime.InteropServices.ComAwareEventInfo" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="type">개체의 형식입니다. </param>
      <param name="eventName">
        <paramref name="type" />의 이벤트 이름입니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComAwareEventInfo.AddEventHandler(System.Object,System.Delegate)">
      <summary>이벤트 처리기를 COM 개체에 연결합니다.</summary>
      <param name="target">이벤트 대리자가 바인딩될 대상 개체입니다.</param>
      <param name="handler">이벤트 대리자입니다.</param>
    </member>
    <member name="P:System.Runtime.InteropServices.ComAwareEventInfo.Attributes">
      <summary>이 이벤트에 대한 특성을 가져옵니다.</summary>
      <returns>이 이벤트에 대한 읽기 전용 특성입니다.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.ComAwareEventInfo.DeclaringType">
      <summary>이 멤버를 선언하는 클래스를 가져옵니다.</summary>
      <returns>이 멤버를 선언하는 클래스에 대한 <see cref="T:System.Type" /> 개체입니다.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.ComAwareEventInfo.Name">
      <summary>현재 멤버의 이름을 가져옵니다.</summary>
      <returns>이 멤버의 이름입니다.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComAwareEventInfo.RemoveEventHandler(System.Object,System.Delegate)">
      <summary>COM 개체에서 이벤트 처리기를 분리합니다.</summary>
      <param name="target">이벤트 대리자가 바인딩되어 있는 대상 개체입니다.</param>
      <param name="handler">이벤트 대리자입니다.</param>
      <exception cref="T:System.InvalidOperationException">이벤트에 public remove 접근자가 없는 경우</exception>
      <exception cref="T:System.ArgumentException">전달된 처리기를 사용할 수 없는 경우</exception>
      <exception cref="T:System.Reflection.TargetException">Windows 스토어 앱용 .NET 또는 이식 가능한 클래스 라이브러리에서 대신 <see cref="T:System.Exception" />를 catch합니다.<paramref name="target" /> 매개 변수가 null이고 이벤트가 정적이 아닌 경우또는 <see cref="T:System.Reflection.EventInfo" />가 대상에 선언되지 않은 경우</exception>
      <exception cref="T:System.MethodAccessException">Windows 스토어 앱용 .NET 또는 이식 가능한 클래스 라이브러리에서 대신 기본 클래스 예외 <see cref="T:System.MemberAccessException" />를 catch합니다.호출자에게 멤버에 액세스할 권한이 없는 경우</exception>
    </member>
    <member name="T:System.Runtime.InteropServices.ComDefaultInterfaceAttribute">
      <summary>COM에 노출할 기본 인터페이스를 지정합니다.이 클래스는 상속될 수 없습니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComDefaultInterfaceAttribute.#ctor(System.Type)">
      <summary>지정된 <see cref="T:System.Type" /> 개체를 COM에 노출되는 기본 인터페이스로 설정하여 <see cref="T:System.Runtime.InteropServices.ComDefaultInterfaceAttribute" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="defaultInterface">COM에 노출할 기본 인터페이스를 나타내는 <see cref="T:System.Type" /> 값입니다. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.ComDefaultInterfaceAttribute.Value">
      <summary>COM에 노출할 기본 인터페이스를 지정하는 <see cref="T:System.Type" /> 개체를 가져옵니다.</summary>
      <returns>COM에 노출할 기본 인터페이스를 지정하는 <see cref="T:System.Type" /> 개체입니다.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ComEventInterfaceAttribute">
      <summary>소스 인터페이스를 식별하고 COM 형식 라이브러리에서 coclass를 가져올 때 생성되는 이벤트 인터페이스의 메서드를 구현하는 클래스를 식별합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComEventInterfaceAttribute.#ctor(System.Type,System.Type)">
      <summary>소스 인터페이스와 이벤트 공급자 클래스를 사용하여 <see cref="T:System.Runtime.InteropServices.ComEventInterfaceAttribute" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="SourceInterface">형식 라이브러리의 원본 소스 인터페이스를 포함하는 <see cref="T:System.Type" />입니다.COM에서는 이 인터페이스를 사용하여 관리되는 클래스를 다시 호출합니다.</param>
      <param name="EventProvider">이벤트 인터페이스의 메서드를 구현하는 클래스를 포함하는 <see cref="T:System.Type" />입니다. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.ComEventInterfaceAttribute.EventProvider">
      <summary>이벤트 인터페이스의 메서드를 구현하는 클래스를 가져옵니다.</summary>
      <returns>이벤트 인터페이스의 메서드를 구현하는 클래스를 포함하는 <see cref="T:System.Type" />입니다.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.ComEventInterfaceAttribute.SourceInterface">
      <summary>형식 라이브러리에서 원본 소스 인터페이스를 가져옵니다.</summary>
      <returns>소스 인터페이스를 포함하는 <see cref="T:System.Type" />입니다.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ComEventsHelper">
      <summary>COM 개체에 추가되거나 COM 개체에서 제거되는 이벤트를 처리하는 .NET Framework 대리자를 사용하도록 하는 메서드를 제공합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComEventsHelper.Combine(System.Object,System.Guid,System.Int32,System.Delegate)">
      <summary>COM 개체에서 발생된 이벤트의 호출 목록에 대리자를 추가합니다.</summary>
      <param name="rcw">호출자가 응답하려고 하는 이벤트를 트리거하는 COM 개체입니다.</param>
      <param name="iid">COM 개체에서 이벤트를 트리거하기 위해 사용하는 소스 인터페이스의 식별자입니다. </param>
      <param name="dispid">소스 인터페이스의 메서드에 대한 디스패치 식별자입니다.</param>
      <param name="d">COM 이벤트가 발생될 때 호출할 대리자입니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComEventsHelper.Remove(System.Object,System.Guid,System.Int32,System.Delegate)">
      <summary>COM 개체에서 발생된 이벤트의 호출 목록에서 대리자를 제거합니다.</summary>
      <returns>호출 목록에서 제거된 대리자입니다.</returns>
      <param name="rcw">대리자가 연결될 COM 개체입니다.</param>
      <param name="iid">COM 개체에서 이벤트를 트리거하기 위해 사용하는 소스 인터페이스의 식별자입니다. </param>
      <param name="dispid">소스 인터페이스의 메서드에 대한 디스패치 식별자입니다.</param>
      <param name="d">호출 목록에서 제거할 대리자입니다.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.COMException">
      <summary>인식할 수 없는 HRESULT가 COM 메서드 호출에서 반환되면 예외가 throw됩니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.COMException.#ctor">
      <summary>기본값을 사용하여 <see cref="T:System.Runtime.InteropServices.COMException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.COMException.#ctor(System.String)">
      <summary>지정된 메시지를 사용하여 <see cref="T:System.Runtime.InteropServices.COMException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">예외에 대한 이유를 나타내는 메시지입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.COMException.#ctor(System.String,System.Exception)">
      <summary>지정된 오류 메시지와 해당 예외의 근본 원인인 내부 예외에 대한 참조를 사용하여 <see cref="T:System.Runtime.InteropServices.COMException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">예외에 대한 이유를 설명하는 오류 메시지입니다. </param>
      <param name="inner">현재 예외의 원인이 되는 예외입니다.<paramref name="inner" /> 매개 변수가 null이 아니면 현재 예외는 내부 예외를 처리하는 catch 블록에서 발생합니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.COMException.#ctor(System.String,System.Int32)">
      <summary>지정된 메시지와 오류 코드를 사용하여 <see cref="T:System.Runtime.InteropServices.COMException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">예외가 발생한 이유를 나타내는 메시지입니다. </param>
      <param name="errorCode">이 예외와 관련된 오류 코드(HRESULT) 값입니다. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComImportAttribute">
      <summary>특성 사용 형식이 이미 COM에서 정의되었음을 나타냅니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComImportAttribute.#ctor">
      <summary>
        <see cref="T:System.Runtime.InteropServices.ComImportAttribute" />의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComInterfaceType">
      <summary>COM에 인터페이스를 노출시키는 방법을 식별합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComInterfaceType.InterfaceIsDual">
      <summary>인터페이스가 초기 바인딩 및 런타임에 바인딩 모두를 사용하는 이중 인터페이스로 COM에 노출됨을 나타냅니다.기본값은 <see cref="F:System.Runtime.InteropServices.ComInterfaceType.InterfaceIsDual" />입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComInterfaceType.InterfaceIsIDispatch">
      <summary>인터페이스가 런타임에 바인딩만 사용하는 dispinterface로 COM에 노출됨을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComInterfaceType.InterfaceIsIInspectable">
      <summary>인터페이스가 COM에 Windows 런타임 인터페이스로 노출됨을 나타냅니다. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComInterfaceType.InterfaceIsIUnknown">
      <summary>인터페이스가 초기 바인딩만 사용하는 IUnknown에서 파생된 인터페이스로 COM에 노출됨을 나타냅니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComMemberType">
      <summary>COM 멤버의 형식을 설명합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComMemberType.Method">
      <summary>멤버는 일반 메서드입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComMemberType.PropGet">
      <summary>멤버는 속성을 가져옵니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComMemberType.PropSet">
      <summary>멤버는 속성을 설정합니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComSourceInterfacesAttribute">
      <summary>특성 사용 클래스에 대한 COM 이벤트 소스로 노출되는 인터페이스 목록을 식별합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComSourceInterfacesAttribute.#ctor(System.String)">
      <summary>이벤트 소스 인터페이스의 이름을 사용하여 <see cref="T:System.Runtime.InteropServices.ComSourceInterfacesAttribute" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="sourceInterfaces">정규화된 이벤트 소스 인터페이스 이름의 null 구분 목록입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComSourceInterfacesAttribute.#ctor(System.Type)">
      <summary>소스 인터페이스로 사용할 형식을 사용하여 <see cref="T:System.Runtime.InteropServices.ComSourceInterfacesAttribute" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="sourceInterface">소스 인터페이스의 <see cref="T:System.Type" />입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComSourceInterfacesAttribute.#ctor(System.Type,System.Type)">
      <summary>소스 인터페이스로 사용할 형식을 사용하여 <see cref="T:System.Runtime.InteropServices.ComSourceInterfacesAttribute" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="sourceInterface1">기본 소스 인터페이스의 <see cref="T:System.Type" />입니다. </param>
      <param name="sourceInterface2">소스 인터페이스의 <see cref="T:System.Type" />입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComSourceInterfacesAttribute.#ctor(System.Type,System.Type,System.Type)">
      <summary>소스 인터페이스로 사용할 형식을 사용하여 ComSourceInterfacesAttribute 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="sourceInterface1">기본 소스 인터페이스의 <see cref="T:System.Type" />입니다. </param>
      <param name="sourceInterface2">소스 인터페이스의 <see cref="T:System.Type" />입니다. </param>
      <param name="sourceInterface3">소스 인터페이스의 <see cref="T:System.Type" />입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComSourceInterfacesAttribute.#ctor(System.Type,System.Type,System.Type,System.Type)">
      <summary>소스 인터페이스로 사용할 형식을 사용하여 <see cref="T:System.Runtime.InteropServices.ComSourceInterfacesAttribute" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="sourceInterface1">기본 소스 인터페이스의 <see cref="T:System.Type" />입니다. </param>
      <param name="sourceInterface2">소스 인터페이스의 <see cref="T:System.Type" />입니다. </param>
      <param name="sourceInterface3">소스 인터페이스의 <see cref="T:System.Type" />입니다. </param>
      <param name="sourceInterface4">소스 인터페이스의 <see cref="T:System.Type" />입니다. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.ComSourceInterfacesAttribute.Value">
      <summary>이벤트 소스 인터페이스의 정규화된 이름을 가져옵니다.</summary>
      <returns>이벤트 소스 인터페이스의 정규화된 이름입니다.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.CurrencyWrapper">
      <summary>마샬러가 VT_CY으로 마샬링할 개체를 래핑합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.CurrencyWrapper.#ctor(System.Decimal)">
      <summary>래핑하여 VT_CY 형식으로 마샬링할 Decimal을 사용하여 <see cref="T:System.Runtime.InteropServices.CurrencyWrapper" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="obj">래핑하여 VT_CY로 마샬링할 Decimal입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.CurrencyWrapper.#ctor(System.Object)">
      <summary>래핑하여 VT_CY 형식으로 마샬링할 Decimal을 포함하는 개체를 사용하여 <see cref="T:System.Runtime.InteropServices.CurrencyWrapper" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="obj">래핑하여 VT_CY로 마샬링할 Decimal을 포함하는 개체입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="obj" /> 매개 변수가 <see cref="T:System.Decimal" /> 형식이 아닌 경우</exception>
    </member>
    <member name="P:System.Runtime.InteropServices.CurrencyWrapper.WrappedObject">
      <summary>VT_CY 형식으로 마샬링할 래핑된 개체를 가져옵니다.</summary>
      <returns>VT_CY 형식으로 마샬링할 래핑된 개체입니다.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.CustomQueryInterfaceMode">
      <summary>
        <see cref="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject(System.Object,System.Type,System.Runtime.InteropServices.CustomQueryInterfaceMode)" /> 메서드의 IUnknown::QueryInterface 호출에서 <see cref="T:System.Runtime.InteropServices.ICustomQueryInterface" /> 인터페이스를 사용할 수 있는지 여부를 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CustomQueryInterfaceMode.Allow">
      <summary>IUnknown::QueryInterface 메서드 호출은 <see cref="T:System.Runtime.InteropServices.ICustomQueryInterface" /> 인터페이스를 사용할 수 있습니다.이 값을 사용하면 <see cref="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject(System.Object,System.Type,System.Runtime.InteropServices.CustomQueryInterfaceMode)" /> 메서드 오버로드는 <see cref="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject(System.Object,System.Type)" /> 오버로드와 비슷하게 동작합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CustomQueryInterfaceMode.Ignore">
      <summary>IUnknown::QueryInterface 메서드 호출은 <see cref="T:System.Runtime.InteropServices.ICustomQueryInterface" /> 인터페이스를 무시해야 합니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.CustomQueryInterfaceResult">
      <summary>
        <see cref="M:System.Runtime.InteropServices.ICustomQueryInterface.GetInterface(System.Guid@,System.IntPtr@)" /> 메서드의 반환 값을 제공합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CustomQueryInterfaceResult.Failed">
      <summary>특정 인터페이스 ID에 대한 인터페이스는 사용할 수 없습니다.이 경우 반환되는 인터페이스는 null입니다.E_NOINTERFACE가 IUnknown::QueryInterface의 호출자에게 반환됩니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CustomQueryInterfaceResult.Handled">
      <summary>
        <see cref="M:System.Runtime.InteropServices.ICustomQueryInterface.GetInterface(System.Guid@,System.IntPtr@)" /> 메서드에서 반환된 인터페이스 포인터를 IUnknown::QueryInterface의 결과로 사용할 수 있습니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.CustomQueryInterfaceResult.NotHandled">
      <summary>사용자 지정 QueryInterface는 사용되지 않습니다.대신 IUnknown::QueryInterface의 기본 구현이 사용되어야 합니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.DefaultCharSetAttribute">
      <summary>
        <see cref="T:System.Runtime.InteropServices.CharSet" /> 열거형의 값을 지정합니다.이 클래스는 상속될 수 없습니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DefaultCharSetAttribute.#ctor(System.Runtime.InteropServices.CharSet)">
      <summary>지정된 <see cref="T:System.Runtime.InteropServices.CharSet" /> 값을 사용하여 <see cref="T:System.Runtime.InteropServices.DefaultCharSetAttribute" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="charSet">
        <see cref="T:System.Runtime.InteropServices.CharSet" /> 값 중 하나입니다.</param>
    </member>
    <member name="P:System.Runtime.InteropServices.DefaultCharSetAttribute.CharSet">
      <summary>모든 <see cref="T:System.Runtime.InteropServices.DllImportAttribute" /> 호출에 사용되는 <see cref="T:System.Runtime.InteropServices.CharSet" />의 기본값을 가져옵니다.</summary>
      <returns>모든 <see cref="T:System.Runtime.InteropServices.DllImportAttribute" /> 호출에 사용되는 <see cref="T:System.Runtime.InteropServices.CharSet" />의 기본값입니다.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DefaultDllImportSearchPathsAttribute">
      <summary>플랫폼 호출 기능을 제공하는 DLL을 검색하는 데 사용되는 경로를 지정합니다. </summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DefaultDllImportSearchPathsAttribute.#ctor(System.Runtime.InteropServices.DllImportSearchPath)">
      <summary>플랫폼 대상의 검색을 호출할 때 사용할 경로를 지정하여 <see cref="T:System.Runtime.InteropServices.DefaultDllImportSearchPathsAttribute" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
      <param name="paths">LoadLibraryEx 함수가 플랫폼 호출 중에 검색하는 경로를 지정하는 열거형 값의 비트 조합입니다. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.DefaultDllImportSearchPathsAttribute.Paths">
      <summary>LoadLibraryEx 함수가 플랫폼 호출 중에 검색하는 경로를 지정하는 열거형 값의 비트 조합을 가져옵니다. </summary>
      <returns>플랫폼을 호출의 검색 경로를 지정하는 열거형 값의 비트 조합입니다. </returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DefaultParameterValueAttribute">
      <summary>기본 매개 변수를 지원하는 언어에서 호출할 경우 매개 변수의 기본값을 설정합니다.이 클래스는 상속될 수 없습니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DefaultParameterValueAttribute.#ctor(System.Object)">
      <summary>매개 변수 기본값을 사용하여 <see cref="T:System.Runtime.InteropServices.DefaultParameterValueAttribute" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="value">매개 변수의 기본값을 나타내는 개체입니다.</param>
    </member>
    <member name="P:System.Runtime.InteropServices.DefaultParameterValueAttribute.Value">
      <summary>매개 변수의 기본값을 가져옵니다.</summary>
      <returns>매개 변수의 기본값을 나타내는 개체입니다.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DispatchWrapper">
      <summary>마샬러가 VT_DISPATCH으로 마샬링할 개체를 래핑합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DispatchWrapper.#ctor(System.Object)">
      <summary>래핑할 개체를 사용하여 <see cref="T:System.Runtime.InteropServices.DispatchWrapper" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="obj">래핑되고 <see cref="F:System.Runtime.InteropServices.VarEnum.VT_DISPATCH" />로 변환될 개체입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="obj" />가 클래스 또는 배열이 아닌 경우또는 <paramref name="obj" />는 IDispatch를 지원하지 않습니다. </exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="obj" /> 매개 변수가 false 값으로 전달된 <see cref="T:System.Runtime.InteropServices.ComVisibleAttribute" /> 특성으로 표시된 경우또는<paramref name="obj" /> 매개 변수가 false 값으로 전달된 <see cref="T:System.Runtime.InteropServices.ComVisibleAttribute" /> 특성으로 표시된 형식에서 상속하는 경우</exception>
    </member>
    <member name="P:System.Runtime.InteropServices.DispatchWrapper.WrappedObject">
      <summary>
        <see cref="T:System.Runtime.InteropServices.DispatchWrapper" />로 래핑될 개체를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Runtime.InteropServices.DispatchWrapper" />에서 래핑한 개체입니다.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DispIdAttribute">
      <summary>메서드, 필드 또는 속성의 COM DispId(디스패치 식별자)를 지정합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DispIdAttribute.#ctor(System.Int32)">
      <summary>지정된 DISPID를 사용하여 DispIdAttribute 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="dispId">멤버의 DISPID입니다. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.DispIdAttribute.Value">
      <summary>멤버의 DISPID를 가져옵니다.</summary>
      <returns>멤버의 DISPID입니다.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DllImportAttribute">
      <summary>관리되지 않는 DLL(동적 연결 라이브러리)에서 특성 사용 해당 메서드를 정적 진입점으로 노출함을 나타냅니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.DllImportAttribute.#ctor(System.String)">
      <summary>가져올 메서드가 포함된 DLL의 이름을 사용하여 <see cref="T:System.Runtime.InteropServices.DllImportAttribute" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="dllName">관리되지 않는 메서드를 포함하는 DLL의 이름입니다.DLL이 어셈블리에 포함되어 있는 경우 이 이름에는 어셈블리 표시 이름이 포함될 수 있습니다.</param>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.BestFitMapping">
      <summary>유니코드 문자를 ANSI 문자로 변환할 때 가장 적합한 매핑 동작을 활성화 또는 비활성화합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.CallingConvention">
      <summary>진입점의 호출 규칙을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.CharSet">
      <summary>문자열 매개 변수를 메서드로 마샬링하는 방법을 지정하고 이름 관리를 제어합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.EntryPoint">
      <summary>호출할 DLL 진입점의 이름 또는 서수를 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.ExactSpelling">
      <summary>
        <see cref="F:System.Runtime.InteropServices.DllImportAttribute.CharSet" /> 필드로 인해 공용 언어 런타임이 지정된 이름 이외의 진입점 이름을 관리되지 않는 DLL에서 검색할지 여부를 제어합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.PreserveSig">
      <summary>HRESULT 또는 retval 반환 값이 있는 관리되지 않는 메서드가 직접 변환되는지 아니면 HRESULT 또는 retval 반환 값이 자동으로 예외로 변환되는지 여부를 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.SetLastError">
      <summary>특성 사용 메서드에서 반환하기 전에 호출 수신자가 Win32 API SetLastError를 호출할지 여부를 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportAttribute.ThrowOnUnmappableChar">
      <summary>ANSI "?" 문자로 변환되는 매핑할 수 없는 유니코드 문자가 나오면 예외를 throw하거나 throw하지 않습니다.</summary>
    </member>
    <member name="P:System.Runtime.InteropServices.DllImportAttribute.Value">
      <summary>진입점을 포함하는 DLL 파일의 이름을 가져옵니다.</summary>
      <returns>진입점을 포함하는 DLL 파일의 이름입니다.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.DllImportSearchPath">
      <summary>플랫폼 호출 기능을 제공하는 DLL을 검색하는 데 사용되는 경로를 지정합니다. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.ApplicationDirectory">
      <summary>DLL 검색 경로에 응용 프로그램 디렉터리를 포함합니다. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.AssemblyDirectory">
      <summary>어셈블리 종속성을 검색할 때 어셈블리 자체를 포함하는 디렉터리와 함께 해당 디렉터리 목록을 검색합니다.이 값은 경로가 Win32 LoadLibraryEx 함수에 전달되기 전에 .NET Framework에서 사용됩니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.LegacyBehavior">
      <summary>응용 프로그램 디렉터리를 검색하고 LOAD_WITH_ALTERED_SEARCH_PATH 플래그가 있는 Win32 LoadLibraryEx 함수를 호출합니다.이 값은 다른 값이 지정되는 경우 무시됩니다.<see cref="T:System.Runtime.InteropServices.DefaultDllImportSearchPathsAttribute" /> 특성을 지원하지 않는 운영 체제는 이 값을 사용하고 다른 값은 무시합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.SafeDirectories">
      <summary>응용 프로그램 디렉터리인 %WinDir%\System32 디렉터리를 포함하고 DLL 검색 경로에 사용자 디렉터리를 포함합니다. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.System32">
      <summary>DLL 검색 경로에 %WinDir%\System32 디렉터리를 포함합니다. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.UseDllDirectoryForDependencies">
      <summary>다른 폴더를 검색하기 전에 DLL이 위치한 폴더에서 DLL의 종속성을 검색합니다. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.DllImportSearchPath.UserDirectories">
      <summary>Win32 AddDllDirectory 기능을 사용하여 프로세스 전체 검색 경로에 명시적으로 추가되었던 모든 경로를 포함합니다. </summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ErrorWrapper">
      <summary>마샬러가 VT_ERROR으로 마샬링할 개체를 래핑합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ErrorWrapper.#ctor(System.Exception)">
      <summary>제공된 예외에 해당하는 HRESULT를 사용하여 <see cref="T:System.Runtime.InteropServices.ErrorWrapper" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="e">오류 코드로 변환될 예외입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ErrorWrapper.#ctor(System.Int32)">
      <summary>오류의 HRESULT를 사용하여 <see cref="T:System.Runtime.InteropServices.ErrorWrapper" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="errorCode">오류의 HRESULT입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ErrorWrapper.#ctor(System.Object)">
      <summary>오류의 HRESULT가 들어 있는 개체를 사용하여 <see cref="T:System.Runtime.InteropServices.ErrorWrapper" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="errorCode">오류의 HRESULT가 들어 있는 개체입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="errorCode" /> 매개 변수가 <see cref="T:System.Int32" /> 형식이 아닌 경우</exception>
    </member>
    <member name="P:System.Runtime.InteropServices.ErrorWrapper.ErrorCode">
      <summary>래퍼의 오류 코드를 가져옵니다.</summary>
      <returns>오류의 HRESULT입니다.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.GCHandle">
      <summary>관리되지 않는 메모리에서 관리되는 개체에 액세스하기 위한 방법을 제공합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.AddrOfPinnedObject">
      <summary>
        <see cref="F:System.Runtime.InteropServices.GCHandleType.Pinned" /> 핸들에서 개체의 주소를 검색합니다.</summary>
      <returns>고정된 개체의 주소를 나타내는 <see cref="T:System.IntPtr" />입니다. </returns>
      <exception cref="T:System.InvalidOperationException">The handle is any type other than <see cref="F:System.Runtime.InteropServices.GCHandleType.Pinned" />. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.Alloc(System.Object)">
      <summary>지정된 개체의 <see cref="F:System.Runtime.InteropServices.GCHandleType.Normal" /> 핸들을 할당합니다.</summary>
      <returns>개체를 가비지 수집하지 못하게 하는 새 <see cref="T:System.Runtime.InteropServices.GCHandle" />입니다.이 <see cref="T:System.Runtime.InteropServices.GCHandle" />은 더 이상 필요하지 않을 때 <see cref="M:System.Runtime.InteropServices.GCHandle.Free" />를 사용하여 해제해야 합니다.</returns>
      <param name="value">
        <see cref="T:System.Runtime.InteropServices.GCHandle" />을 사용하는 개체입니다. </param>
      <exception cref="T:System.ArgumentException">An instance with nonprimitive (non-blittable) members cannot be pinned. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.Alloc(System.Object,System.Runtime.InteropServices.GCHandleType)">
      <summary>지정된 개체에 특정 형식의 핸들을 할당합니다.</summary>
      <returns>지정한 형식의 새 <see cref="T:System.Runtime.InteropServices.GCHandle" />입니다.이 <see cref="T:System.Runtime.InteropServices.GCHandle" />은 더 이상 필요하지 않을 때 <see cref="M:System.Runtime.InteropServices.GCHandle.Free" />를 사용하여 해제해야 합니다.</returns>
      <param name="value">
        <see cref="T:System.Runtime.InteropServices.GCHandle" />을 사용하는 개체입니다. </param>
      <param name="type">만들려는 <see cref="T:System.Runtime.InteropServices.GCHandle" />의 형식을 나타내는 <see cref="T:System.Runtime.InteropServices.GCHandleType" /> 값 중 하나입니다. </param>
      <exception cref="T:System.ArgumentException">An instance with nonprimitive (non-blittable) members cannot be pinned. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.Equals(System.Object)">
      <summary>지정한 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 개체와 현재 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 개체가 같은지를 확인합니다.</summary>
      <returns>지정한 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 개체가 현재 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 개체와 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="o">현재 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 개체와 비교할 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 개체입니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.Free">
      <summary>
        <see cref="T:System.Runtime.InteropServices.GCHandle" />을 해제합니다.</summary>
      <exception cref="T:System.InvalidOperationException">The handle was freed or never initialized. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.FromIntPtr(System.IntPtr)">
      <summary>관리되는 개체에 대한 핸들에서 만든 새 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 개체를 반환합니다.</summary>
      <returns>값 매개 변수에 해당하는 새 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 개체입니다.  </returns>
      <param name="value">
        <see cref="T:System.Runtime.InteropServices.GCHandle" /> 개체를 만들 관리되는 개체에 대한 <see cref="T:System.IntPtr" /> 핸들입니다.</param>
      <exception cref="T:System.InvalidOperationException">The value of the <paramref name="value" /> parameter is <see cref="F:System.IntPtr.Zero" />.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.GetHashCode">
      <summary>현재 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 개체에 대한 식별자를 반환합니다.</summary>
      <returns>현재 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 개체에 대한 식별자입니다.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.GCHandle.IsAllocated">
      <summary>핸들이 할당되는지를 나타내는 값을 가져옵니다.</summary>
      <returns>핸들이 할당되면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.op_Equality(System.Runtime.InteropServices.GCHandle,System.Runtime.InteropServices.GCHandle)">
      <summary>두 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 개체가 같은지를 나타내는 값을 반환합니다.</summary>
      <returns>
        <paramref name="a" />와 <paramref name="b" /> 매개 변수가 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="a">
        <paramref name="b" /> 매개 변수와 비교할 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 개체입니다. </param>
      <param name="b">
        <paramref name="a" /> 매개 변수와 비교할 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 개체입니다.  </param>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.op_Explicit(System.IntPtr)~System.Runtime.InteropServices.GCHandle">
      <summary>
        <see cref="T:System.Runtime.InteropServices.GCHandle" />은 내부 정수 표현을 사용하여 저장됩니다.</summary>
      <returns>내부 정수 표현을 사용하여 저장된 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 개체입니다.</returns>
      <param name="value">변환이 필요한 핸들을 나타내는 <see cref="T:System.IntPtr" />입니다. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.op_Explicit(System.Runtime.InteropServices.GCHandle)~System.IntPtr">
      <summary>
        <see cref="T:System.Runtime.InteropServices.GCHandle" />은 내부 정수 표현을 사용하여 저장됩니다.</summary>
      <returns>정수 값입니다.</returns>
      <param name="value">정수가 필요한 <see cref="T:System.Runtime.InteropServices.GCHandle" />입니다. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.op_Inequality(System.Runtime.InteropServices.GCHandle,System.Runtime.InteropServices.GCHandle)">
      <summary>두 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 개체가 다른지를 나타내는 값을 반환합니다.</summary>
      <returns>
        <paramref name="a" />와 <paramref name="b" /> 매개 변수가 다르면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="a">
        <paramref name="b" /> 매개 변수와 비교할 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 개체입니다. </param>
      <param name="b">
        <paramref name="a" /> 매개 변수와 비교할 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 개체입니다.  </param>
    </member>
    <member name="P:System.Runtime.InteropServices.GCHandle.Target">
      <summary>이 핸들이 나타나는 개체를 가져오거나 설정합니다.</summary>
      <returns>이 핸들이 나타나는 개체입니다.</returns>
      <exception cref="T:System.InvalidOperationException">The handle was freed, or never initialized. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.GCHandle.ToIntPtr(System.Runtime.InteropServices.GCHandle)">
      <summary>
        <see cref="T:System.Runtime.InteropServices.GCHandle" /> 개체의 내부 정수 표현을 반환합니다.</summary>
      <returns>
        <see cref="T:System.Runtime.InteropServices.GCHandle" /> 개체를 나타내는 <see cref="T:System.IntPtr" /> 개체입니다. </returns>
      <param name="value">내부 정수 표현을 검색할 <see cref="T:System.Runtime.InteropServices.GCHandle" /> 개체입니다.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.GCHandleType">
      <summary>
        <see cref="T:System.Runtime.InteropServices.GCHandle" /> 클래스가 할당할 수 있는 핸들의 형식을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.GCHandleType.Normal">
      <summary>이 핸들 형식은 불투명 핸들을 나타냅니다. 즉, 핸들을 통해 고정된 개체의 주소를 확인할 수 없습니다.이 형식은 개체를 추적하고 가비지 수집기에서 수집하지 않도록 하는 데 사용할 수 있습니다.관리되지 않는 클라이언트에 관리되는 개체에 대한 유일한 참조가 있을 경우에는 가비지 수집기에서 이러한 참조를 발견할 수 없는데, 이럴 때 이 열거형 멤버를 사용하면 편리합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.GCHandleType.Pinned">
      <summary>이 핸들 형식은 <see cref="F:System.Runtime.InteropServices.GCHandleType.Normal" />과 유사하지만 고정 개체의 주소를 사용할 수 있습니다.이렇게 되면 가비지 수집기가 개체를 이동할 수 없으므로 가비지 수집기의 효율성이 저하됩니다.<see cref="M:System.Runtime.InteropServices.GCHandle.Free" /> 메서드를 사용하여 할당된 핸들을 가능한 빨리 해제하십시오.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.GCHandleType.Weak">
      <summary>이 핸들 형식은 개체를 추적하는 데 사용되지만 개체를 수집할 수 있습니다.개체가 수집되면 <see cref="T:System.Runtime.InteropServices.GCHandle" />의 내용은 0이 됩니다.종료자가 실행되기 전에 Weak 참조가 0이 되므로 종료자가 개체를 재활용하더라도 Weak 참조는 계속 0이 됩니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.GCHandleType.WeakTrackResurrection">
      <summary>이 핸들 형식은 <see cref="F:System.Runtime.InteropServices.GCHandleType.Weak" />와 유사하지만 종료 중에 개체가 재활용되어도 핸들이 0이 되지 않습니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.GuidAttribute">
      <summary>자동 GUID가 부적당할 경우 명시적 <see cref="T:System.Guid" />를 제공합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.GuidAttribute.#ctor(System.String)">
      <summary>지정된 GUID를 사용하는 <see cref="T:System.Runtime.InteropServices.GuidAttribute" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="guid">할당할 <see cref="T:System.Guid" />입니다. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.GuidAttribute.Value">
      <summary>클래스의 <see cref="T:System.Guid" />를 가져옵니다.</summary>
      <returns>클래스의 <see cref="T:System.Guid" />입니다.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.HandleCollector">
      <summary>지정된 임계값에 도달한 경우 사용 중인 핸들을 추적하고 가비지 수집을 강제로 수행합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.HandleCollector.#ctor(System.String,System.Int32)">
      <summary>핸들 수집을 시작할 임계값과 이름을 사용하여 <see cref="T:System.Runtime.InteropServices.HandleCollector" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
      <param name="name">수집기의 이름입니다.이 매개 변수를 사용하면 핸들 형식을 개별적으로 추적하는 수집기의 이름을 지정할 수 있습니다.</param>
      <param name="initialThreshold">수집을 시작할 시점을 지정하는 값입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="initialThreshold" /> 매개 변수가 0보다 작은 경우</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.HandleCollector.#ctor(System.String,System.Int32,System.Int32)">
      <summary>이름, 핸들 수집을 시작할 임계값 및 핸들 수집을 반드시 시작해야 하는 임계값을 사용하여 <see cref="T:System.Runtime.InteropServices.HandleCollector" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
      <param name="name">수집기의 이름입니다.  이 매개 변수를 사용하면 핸들 형식을 개별적으로 추적하는 수집기의 이름을 지정할 수 있습니다.</param>
      <param name="initialThreshold">수집을 시작할 시점을 지정하는 값입니다.</param>
      <param name="maximumThreshold">수집을 반드시 시작해야 하는 시점을 지정하는 값입니다.이 매개 변수에는 사용할 수 있는 최대 핸들 수를 설정해야 합니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="initialThreshold" /> 매개 변수가 0보다 작은 경우또는<paramref name="maximumThreshold" /> 매개 변수가 0보다 작은 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="maximumThreshold" /> 매개 변수가 <paramref name="initialThreshold" /> 매개 변수보다 작은 경우</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.HandleCollector.Add">
      <summary>현재 핸들 수를 늘립니다.</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.Runtime.InteropServices.HandleCollector.Count" /> 속성이 0보다 작은 경우</exception>
    </member>
    <member name="P:System.Runtime.InteropServices.HandleCollector.Count">
      <summary>수집된 핸들의 수를 가져옵니다.</summary>
      <returns>수집된 핸들의 수입니다.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.HandleCollector.InitialThreshold">
      <summary>수집을 시작할 시점을 지정하는 값을 가져옵니다.</summary>
      <returns>수집을 시작할 시점을 지정하는 값입니다.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.HandleCollector.MaximumThreshold">
      <summary>수집을 반드시 시작해야 하는 시점을 지정하는 값을 가져옵니다.</summary>
      <returns>수집을 반드시 시작해야 하는 시점을 지정하는 값입니다.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.HandleCollector.Name">
      <summary>
        <see cref="T:System.Runtime.InteropServices.HandleCollector" /> 개체의 이름을 가져옵니다.</summary>
      <returns>
        <see cref="P:System.Runtime.InteropServices.HandleCollector.Name" /> 속성을 사용하면 핸들 형식을 개별적으로 추적하는 수집기의 이름을 지정할 수 있습니다.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.HandleCollector.Remove">
      <summary>현재 핸들 수를 줄입니다.</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.Runtime.InteropServices.HandleCollector.Count" /> 속성이 0보다 작은 경우</exception>
    </member>
    <member name="T:System.Runtime.InteropServices.ICustomAdapter">
      <summary>클라이언트에서 사용자 지정 마샬러가 제공한 어댑터 개체가 아니라 실제 개체에 액세스할 수 있는 방법을 제공합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ICustomAdapter.GetUnderlyingObject">
      <summary>사용자 지정 마샬러에서 래핑한 내부 개체에 액세스할 수 있습니다.</summary>
      <returns>어댑터 개체에 포함된 개체입니다.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ICustomQueryInterface">
      <summary>개발자가 IUnknown::QueryInterface(REFIID riid, void **ppvObject) 메서드의 관리되는 사용자 지정 구현을 제공할 수 있도록 합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ICustomQueryInterface.GetInterface(System.Guid@,System.IntPtr@)">
      <summary>지정된 인터페이스 ID에 따라 인터페이스를 반환합니다.</summary>
      <returns>IUnknown::QueryInterface의 사용자 지정 구현이 사용되었는지 여부를 나타내는 열거형 값 중 하나입니다.</returns>
      <param name="iid">요청된 인터페이스의 GUID입니다.</param>
      <param name="ppv">이 메서드에서 반환할 때 요청된 인터페이스에 대한 참조입니다.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.InAttribute">
      <summary>데이터가 호출자에서 피호출자로 마샬링되어야 하고, 호출자에게는 다시 마샬링되지 않아도 됨을 나타냅니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InAttribute.#ctor">
      <summary>
        <see cref="T:System.Runtime.InteropServices.InAttribute" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.InterfaceTypeAttribute">
      <summary>관리되는 인터페이스를 COM에 노출할 경우 이중 인터페이스인지, 디스패치 전용 인터페이스인지 또는 IUnknown 전용 인터페이스인지 여부를 나타냅니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InterfaceTypeAttribute.#ctor(System.Int16)">
      <summary>지정된 <see cref="T:System.Runtime.InteropServices.ComInterfaceType" /> 열거형 멤버를 사용하여 <see cref="T:System.Runtime.InteropServices.InterfaceTypeAttribute" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="interfaceType">인터페이스를 COM 클라이언트에 노출하는 방법을 설명합니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.InterfaceTypeAttribute.#ctor(System.Runtime.InteropServices.ComInterfaceType)">
      <summary>지정된 <see cref="T:System.Runtime.InteropServices.ComInterfaceType" /> 열거형 멤버를 사용하여 <see cref="T:System.Runtime.InteropServices.InterfaceTypeAttribute" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="interfaceType">인터페이스를 COM에 노출시키는 방법을 설명하는 <see cref="T:System.Runtime.InteropServices.ComInterfaceType" /> 값 중 하나입니다. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.InterfaceTypeAttribute.Value">
      <summary>인터페이스를 COM에 노출하는 방법을 설명하는 <see cref="T:System.Runtime.InteropServices.ComInterfaceType" /> 값을 가져옵니다.</summary>
      <returns>인터페이스를 COM에 노출하는 방법을 설명하는 <see cref="T:System.Runtime.InteropServices.ComInterfaceType" /> 값입니다.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.InvalidComObjectException">
      <summary>잘못된 COM 개체가 사용된 경우 throw되는 예외입니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidComObjectException.#ctor">
      <summary>기본 속성을 사용하여 InvalidComObjectException의 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidComObjectException.#ctor(System.String)">
      <summary>메시지를 사용하여 InvalidComObjectException의 인스턴스를 초기화합니다.</summary>
      <param name="message">예외에 대한 이유를 나타내는 메시지입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidComObjectException.#ctor(System.String,System.Exception)">
      <summary>지정된 오류 메시지와 해당 예외의 근본 원인인 내부 예외에 대한 참조를 사용하여 <see cref="T:System.Runtime.InteropServices.InvalidComObjectException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">예외에 대한 이유를 설명하는 오류 메시지입니다. </param>
      <param name="inner">현재 예외의 원인이 되는 예외입니다.<paramref name="inner" /> 매개 변수가 null이 아니면 현재 예외는 내부 예외를 처리하는 catch 블록에서 발생합니다.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.InvalidOleVariantTypeException">
      <summary>관리 코드에 마샬링 할 수 없는 변형 형식의 인수를 만날 때 마샬러가 발생시키는 예외입니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidOleVariantTypeException.#ctor">
      <summary>기본값으로 InvalidOleVariantTypeException 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidOleVariantTypeException.#ctor(System.String)">
      <summary>지정된 메시지를 사용하여 InvalidOleVariantTypeException 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">예외에 대한 이유를 나타내는 메시지입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.InvalidOleVariantTypeException.#ctor(System.String,System.Exception)">
      <summary>지정된 오류 메시지와 해당 예외의 근본 원인인 내부 예외에 대한 참조를 사용하여 <see cref="T:System.Runtime.InteropServices.InvalidOleVariantTypeException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">예외에 대한 이유를 설명하는 오류 메시지입니다. </param>
      <param name="inner">현재 예외의 원인이 되는 예외입니다.<paramref name="inner" /> 매개 변수가 null이 아니면 현재 예외는 내부 예외를 처리하는 catch 블록에서 발생합니다.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.Marshal">
      <summary>관리되지 않는 메모리를 할당하고, 관리되지 않는 메모리 블록을 복사하고, 관리되는 형식을 관리되지 않는 형식으로 변환하는 메서드의 컬렉션 및 비관리 코드와 상호 작용할 때 사용되는 기타 메서드의 컬렉션을 제공합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.AddRef(System.IntPtr)">
      <summary>지정된 인터페이스의 참조 횟수를 증가시킵니다.</summary>
      <returns>
        <paramref name="pUnk" /> 매개 변수에 대한 참조 횟수의 새 값입니다.</returns>
      <param name="pUnk">증가시킬 인터페이스 참조 횟수입니다.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.AllocCoTaskMem(System.Int32)">
      <summary>COM 작업 메모리 할당자에서 지정된 크기의 메모리 블록을 할당합니다.</summary>
      <returns>할당된 메모리 블록 주소를 나타내는 정수입니다.이 메모리는 <see cref="M:System.Runtime.InteropServices.Marshal.FreeCoTaskMem(System.IntPtr)" />을 사용하여 해제되어야 합니다.</returns>
      <param name="cb">할당될 메모리 블록 크기입니다.</param>
      <exception cref="T:System.OutOfMemoryException">요청을 만족시킬 충분한 메모리가 없는 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.AllocHGlobal(System.Int32)">
      <summary>지정된 바이트 수를 사용하여 프로세스의 관리되지 않는 메모리에서 메모리를 할당합니다.</summary>
      <returns>새로 할당된 메모리에 대한 포인터입니다.이 메모리는 <see cref="M:System.Runtime.InteropServices.Marshal.FreeHGlobal(System.IntPtr)" /> 메서드를 사용하여 해제되어야 합니다.</returns>
      <param name="cb">메모리에서 필요한 바이트 수 입니다.</param>
      <exception cref="T:System.OutOfMemoryException">요청을 만족시킬 충분한 메모리가 없는 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.AllocHGlobal(System.IntPtr)">
      <summary>지정된 바이트 수에 대한 포인터를 사용하여 프로세스의 관리되지 않는 메모리에서 메모리를 할당합니다.</summary>
      <returns>새로 할당된 메모리에 대한 포인터입니다.이 메모리는 <see cref="M:System.Runtime.InteropServices.Marshal.FreeHGlobal(System.IntPtr)" /> 메서드를 사용하여 해제되어야 합니다.</returns>
      <param name="cb">메모리에서 필요한 바이트 수 입니다.</param>
      <exception cref="T:System.OutOfMemoryException">요청을 만족시킬 충분한 메모리가 없는 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.AreComObjectsAvailableForCleanup">
      <summary>어떤 컨텍스트에서 RCW(런타임 호출 가능 래퍼)를 정리에 사용할 수 있는지 여부를 나타냅니다.</summary>
      <returns>정리에 사용할 수 있는 RCW가 하나라도 있으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Byte[],System.Int32,System.IntPtr,System.Int32)">
      <summary>관리되는 8비트 부호 없는 1차원 정수 배열의 데이터를 관리되지 않는 메모리 포인터로 복사합니다.</summary>
      <param name="source">복사할 1차원 배열입니다.</param>
      <param name="startIndex">소스 배열에서 복사를 시작해야 할 인덱스(0부터 시작)입니다.</param>
      <param name="destination">복사할 대상 메모리 포인터입니다.</param>
      <param name="length">복사할 배열 요소 수입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 및 <paramref name="length" />가 잘못된 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="startIndex" />, <paramref name="destination" /> 또는 <paramref name="length" />가 null인 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Char[],System.Int32,System.IntPtr,System.Int32)">
      <summary>관리되는 1차원 문자 배열의 데이터를 관리되지 않는 메모리 포인터로 복사합니다.</summary>
      <param name="source">복사할 1차원 배열입니다.</param>
      <param name="startIndex">소스 배열에서 복사를 시작해야 할 인덱스(0부터 시작)입니다.</param>
      <param name="destination">복사할 대상 메모리 포인터입니다.</param>
      <param name="length">복사할 배열 요소 수입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 및 <paramref name="length" />가 잘못된 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="startIndex" />, <paramref name="destination" /> 또는 <paramref name="length" />가 null인 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Double[],System.Int32,System.IntPtr,System.Int32)">
      <summary>관리되는 1차원 배정밀도 부동 소수점 수 배열의 데이터를 관리되지 않는 메모리 포인터로 복사합니다.</summary>
      <param name="source">복사할 1차원 배열입니다.</param>
      <param name="startIndex">소스 배열에서 복사를 시작해야 할 인덱스(0부터 시작)입니다.</param>
      <param name="destination">복사할 대상 메모리 포인터입니다.</param>
      <param name="length">복사할 배열 요소 수입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 및 <paramref name="length" />가 잘못된 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="startIndex" />, <paramref name="destination" /> 또는 <paramref name="length" />가 null인 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Int16[],System.Int32,System.IntPtr,System.Int32)">
      <summary>관리되는 16비트 부호 있는 1차원 정수 배열의 데이터를 관리되지 않는 메모리 포인터로 복사합니다.</summary>
      <param name="source">복사할 1차원 배열입니다.</param>
      <param name="startIndex">소스 배열에서 복사를 시작해야 할 인덱스(0부터 시작)입니다.</param>
      <param name="destination">복사할 대상 메모리 포인터입니다.</param>
      <param name="length">복사할 배열 요소 수입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 및 <paramref name="length" />가 잘못된 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="startIndex" />, <paramref name="destination" /> 또는 <paramref name="length" />가 null인 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Int32[],System.Int32,System.IntPtr,System.Int32)">
      <summary>관리되는 32비트 부호 있는 1차원 정수 배열의 데이터를 관리되지 않는 메모리 포인터로 복사합니다.</summary>
      <param name="source">복사할 1차원 배열입니다.</param>
      <param name="startIndex">소스 배열에서 복사를 시작해야 할 인덱스(0부터 시작)입니다.</param>
      <param name="destination">복사할 대상 메모리 포인터입니다.</param>
      <param name="length">복사할 배열 요소 수입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 및 <paramref name="length" />가 잘못된 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="startIndex" /> 또는 <paramref name="length" />가 null인 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Int64[],System.Int32,System.IntPtr,System.Int32)">
      <summary>관리되는 64비트 부호 있는 1차원 정수 배열의 데이터를 관리되지 않는 메모리 포인터로 복사합니다.</summary>
      <param name="source">복사할 1차원 배열입니다.</param>
      <param name="startIndex">소스 배열에서 복사를 시작해야 할 인덱스(0부터 시작)입니다.</param>
      <param name="destination">복사할 대상 메모리 포인터입니다.</param>
      <param name="length">복사할 배열 요소 수입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 및 <paramref name="length" />가 잘못된 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="startIndex" />, <paramref name="destination" /> 또는 <paramref name="length" />가 null인 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Byte[],System.Int32,System.Int32)">
      <summary>관리되지 않는 메모리 포인터의 데이터를 관리되는 8비트 부호 없는 정수 배열로 복사합니다.</summary>
      <param name="source">복사할 메모리 포인터입니다.</param>
      <param name="destination">복사할 대상 배열입니다.</param>
      <param name="startIndex">대상 배열에서 복사를 시작해야 할 0부터 시작하는 인덱스입니다.</param>
      <param name="length">복사할 배열 요소 수입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> 또는 <paramref name="length" />가 null인 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Char[],System.Int32,System.Int32)">
      <summary>관리되지 않는 메모리 포인터의 데이터를 관리되는 문자 배열로 복사합니다.</summary>
      <param name="source">복사할 메모리 포인터입니다.</param>
      <param name="destination">복사할 대상 배열입니다.</param>
      <param name="startIndex">대상 배열에서 복사를 시작해야 할 0부터 시작하는 인덱스입니다.</param>
      <param name="length">복사할 배열 요소 수입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> 또는 <paramref name="length" />가 null인 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Double[],System.Int32,System.Int32)">
      <summary>관리되지 않는 메모리 포인터의 데이터를 관리되는 배정밀도 부동 소수점 수 배열로 복사합니다.</summary>
      <param name="source">복사할 메모리 포인터입니다.</param>
      <param name="destination">복사할 대상 배열입니다.</param>
      <param name="startIndex">대상 배열에서 복사를 시작해야 할 0부터 시작하는 인덱스입니다.</param>
      <param name="length">복사할 배열 요소 수입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> 또는 <paramref name="length" />가 null인 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Int16[],System.Int32,System.Int32)">
      <summary>관리되지 않는 메모리 포인터의 데이터를 관리되는 16비트 부호 있는 정수 배열로 복사합니다.</summary>
      <param name="source">복사할 메모리 포인터입니다.</param>
      <param name="destination">복사할 대상 배열입니다.</param>
      <param name="startIndex">대상 배열에서 복사를 시작해야 할 0부터 시작하는 인덱스입니다.</param>
      <param name="length">복사할 배열 요소 수입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> 또는 <paramref name="length" />가 null인 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Int32[],System.Int32,System.Int32)">
      <summary>관리되지 않는 메모리 포인터의 데이터를 관리되는 32비트 부호 있는 정수 배열로 복사합니다.</summary>
      <param name="source">복사할 메모리 포인터입니다.</param>
      <param name="destination">복사할 대상 배열입니다.</param>
      <param name="startIndex">대상 배열에서 복사를 시작해야 할 0부터 시작하는 인덱스입니다.</param>
      <param name="length">복사할 배열 요소 수입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> 또는 <paramref name="length" />가 null인 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Int64[],System.Int32,System.Int32)">
      <summary>관리되지 않는 메모리 포인터의 데이터를 관리되는 64비트 부호 있는 정수 배열로 복사합니다.</summary>
      <param name="source">복사할 메모리 포인터입니다.</param>
      <param name="destination">복사할 대상 배열입니다.</param>
      <param name="startIndex">대상 배열에서 복사를 시작해야 할 0부터 시작하는 인덱스입니다.</param>
      <param name="length">복사할 배열 요소 수입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> 또는 <paramref name="length" />가 null인 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.IntPtr[],System.Int32,System.Int32)">
      <summary>관리되지 않는 메모리 포인터의 데이터를 관리되는 <see cref="T:System.IntPtr" /> 배열에 복사합니다.</summary>
      <param name="source">복사할 메모리 포인터입니다. </param>
      <param name="destination">복사할 대상 배열입니다.</param>
      <param name="startIndex">대상 배열에서 복사를 시작해야 할 0부터 시작하는 인덱스입니다.</param>
      <param name="length">복사할 배열 요소 수입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> 또는 <paramref name="length" />가 null인 경우</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr,System.Single[],System.Int32,System.Int32)">
      <summary>관리되지 않는 메모리 포인터의 데이터를 관리되는 단정밀도 부동 소수점 수 배열로 복사합니다.</summary>
      <param name="source">복사할 메모리 포인터입니다. </param>
      <param name="destination">복사할 대상 배열입니다. </param>
      <param name="startIndex">대상 배열에서 복사를 시작해야 할 0부터 시작하는 인덱스입니다. </param>
      <param name="length">복사할 배열 요소 수입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> 또는 <paramref name="length" />가 null인 경우 </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.IntPtr[],System.Int32,System.IntPtr,System.Int32)">
      <summary>관리되는 1차원 <see cref="T:System.IntPtr" /> 배열의 데이터를 관리되지 않는 메모리 포인터에 복사합니다.</summary>
      <param name="source">복사할 1차원 배열입니다.</param>
      <param name="startIndex">소스 배열에서 복사를 시작해야 할 인덱스(0부터 시작)입니다.</param>
      <param name="destination">복사할 대상 메모리 포인터입니다.</param>
      <param name="length">복사할 배열 요소 수입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="destination" />, <paramref name="startIndex" /> 또는 <paramref name="length" />가 null인 경우</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Copy(System.Single[],System.Int32,System.IntPtr,System.Int32)">
      <summary>관리되는 1차원 단정밀도 부동 소수점 수 배열의 데이터를 관리되지 않는 메모리 포인터로 복사합니다.</summary>
      <param name="source">복사할 1차원 배열입니다. </param>
      <param name="startIndex">소스 배열에서 복사를 시작해야 할 인덱스(0부터 시작)입니다. </param>
      <param name="destination">복사할 대상 메모리 포인터입니다. </param>
      <param name="length">복사할 배열 요소 수입니다. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> 및 <paramref name="length" />가 잘못된 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />, <paramref name="startIndex" />, <paramref name="destination" /> 또는 <paramref name="length" />가 null인 경우 </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.CreateAggregatedObject(System.IntPtr,System.Object)">
      <summary>지정된 COM 개체를 사용하여 관리되는 개체를 집계합니다.</summary>
      <returns>관리되는 개체의 내부 IUnknown 포인터입니다.</returns>
      <param name="pOuter">외부 IUnknown 포인터입니다.</param>
      <param name="o">집계할 개체입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="o" />는 Windows 런타임 개체입니다.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.CreateAggregatedObject``1(System.IntPtr,``0)">
      <summary>[.NET Framework 4.5.1 이상 버전에서 지원됨] 지정된 COM 개체를 사용하여 지정된 형식의 관리되는 개체를 집계합니다. </summary>
      <returns>관리되는 개체의 내부 IUnknown 포인터입니다. </returns>
      <param name="pOuter">외부 IUnknown 포인터입니다. </param>
      <param name="o">집계할 관리되는 개체입니다. </param>
      <typeparam name="T">집계할 관리되는 개체의 형식입니다. </typeparam>
      <exception cref="T:System.ArgumentException">
        <paramref name="o" />는 Windows 런타임 개체입니다. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.CreateWrapperOfType(System.Object,System.Type)">
      <summary>지정된 형식의 개체에 지정된 COM 개체를 래핑합니다.</summary>
      <returns>원하는 형식의 인스턴스인 새로 래핑된 개체입니다.</returns>
      <param name="o">래핑될 개체입니다. </param>
      <param name="t">만들 래퍼의 형식입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="t" />가 __ComObject에서 파생된 경우 또는<paramref name="t" />이 Windows 런타임 형식인 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="t" /> 매개 변수가 null입니다.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="o" />가 원하는 모든 인터페이스를 지원하지 않으므로 대상 형식으로 변환할 수 없는 경우 </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.CreateWrapperOfType``2(``0)">
      <summary>[.NET Framework 4.5.1 이상 버전에서 지원됨] 지정된 형식의 개체에 지정된 COM 개체를 래핑합니다.</summary>
      <returns>새로 래핑된 개체입니다. </returns>
      <param name="o">래핑될 개체입니다. </param>
      <typeparam name="T">래핑할 개체의 형식입니다. </typeparam>
      <typeparam name="TWrapper">반환할 개체의 형식입니다. </typeparam>
      <exception cref="T:System.ArgumentException">
        <paramref name="T" />가 __ComObject에서 파생된 경우 또는<paramref name="T" />이 Windows 런타임 형식인 경우</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="o" />가 원하는 모든 인터페이스를 지원하지 않으므로 <paramref name="TWrapper" />으로 변환할 수 없는 경우 </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.DestroyStructure``1(System.IntPtr)">
      <summary>[.NET Framework 4.5.1 이상 버전에서 지원됨] 지정된 관리되지 않는 메모리 블록이 가리키는 지정된 형식의 모든 하위 구조체를 해제합니다. </summary>
      <param name="ptr">관리되지 않는 메모리 블록에 대한 포인터입니다. </param>
      <typeparam name="T">형식이 지정된 구조체의 형식입니다.<paramref name="ptr" /> 매개 변수에 있는 버퍼를 삭제하는 데 필요한 레이아웃 정보를 제공합니다.</typeparam>
      <exception cref="T:System.ArgumentException">
        <paramref name="T" />에 Auto 레이아웃이 지정된 경우.대신 Sequential 또는 Explicit 레이아웃을 사용합니다.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.DestroyStructure(System.IntPtr,System.Type)">
      <summary>지정된 관리되지 않는 메모리 블록이 가리키는 모든 하위 구조체를 해제합니다.</summary>
      <param name="ptr">관리되지 않는 메모리 블록에 대한 포인터입니다. </param>
      <param name="structuretype">형식이 지정된 클래스의 형식으로,<paramref name="ptr" /> 매개 변수에 있는 버퍼를 삭제하는 데 필요한 레이아웃 정보를 제공합니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="structureType" />에 Auto 레이아웃이 지정된 경우.대신 Sequential 또는 Explicit 레이아웃을 사용합니다.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.FinalReleaseComObject(System.Object)">
      <summary>참조 횟수를 0으로 설정하여 RCW(RCW)에 대한 모든 참조를 해제합니다.</summary>
      <returns>
        <paramref name="o" /> 매개 변수에 연결된 RCW의 새 참조 횟수 값입니다. 해제 작업을 성공적으로 마치면 값이 0입니다.</returns>
      <param name="o">해제할 RCW입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="o" />가 유효한 COM 개체가 아닌 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="o" />가 null인 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.FreeBSTR(System.IntPtr)">
      <summary>COM SysFreeString 함수를 사용하여 BSTR을 해제합니다.</summary>
      <param name="ptr">해제할 BSTR의 주소입니다. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.FreeCoTaskMem(System.IntPtr)">
      <summary>관리되지 않는 COM 작업 메모리 할당자에서 할당한 메모리 블록을 해제합니다.</summary>
      <param name="ptr">해제할 메모리의 주소입니다. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.FreeHGlobal(System.IntPtr)">
      <summary>프로세스의 관리되지 않는 메모리에서 이전에 할당한 메모리를 해제합니다.</summary>
      <param name="hglobal">
        <see cref="M:System.Runtime.InteropServices.Marshal.AllocHGlobal(System.IntPtr)" /> 호출과 일치하는 원본 호출에서 반환되는 핸들입니다. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject(System.Object,System.Type)">
      <summary>지정된 개체의 지정된 인터페이스를 나타내는 IUnknown 인터페이스에 대한 포인터를 반환합니다.사용자 지정 쿼리 인터페이스 액세스가 기본적으로 사용됩니다.</summary>
      <returns>개체에 대해 지정된 인터페이스를 나타내는 인터페이스 포인터입니다.</returns>
      <param name="o">인터페이스를 제공하는 개체입니다. </param>
      <param name="T">요청된 인터페이스의 형식입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="T" /> 매개 변수가 인터페이스가 아닌 경우또는 COM에 해당 형식이 표시되지 않는 경우 또는<paramref name="T" /> 매개 변수가 제네릭 형식인 경우</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="o" /> 매개 변수가 요청된 인터페이스를 지원하지 않는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="o" /> 매개 변수가 null입니다.또는 <paramref name="T" /> 매개 변수가 null입니다.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject(System.Object,System.Type,System.Runtime.InteropServices.CustomQueryInterfaceMode)">
      <summary>지정된 개체의 지정된 인터페이스를 나타내는 IUnknown 인터페이스에 대한 포인터를 반환합니다.사용자 지정 쿼리 인터페이스 액세스는 지정된 사용자 지정 모드에 의해 제어됩니다.</summary>
      <returns>개체에 대한 인터페이스를 나타내는 인터페이스 포인터입니다.</returns>
      <param name="o">인터페이스를 제공하는 개체입니다.</param>
      <param name="T">요청된 인터페이스의 형식입니다.</param>
      <param name="mode">
        <see cref="T:System.Runtime.InteropServices.ICustomQueryInterface" />에서 제공하는 IUnknown::QueryInterface 사용자 지정을 적용하는지 여부를 나타내는 열거형 값 중 하나입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="T" /> 매개 변수가 인터페이스가 아닌 경우또는 COM에 해당 형식이 표시되지 않는 경우또는<paramref name="T" /> 매개 변수가 제네릭 형식인 경우</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="o" /> 개체가 요청된 인터페이스를 지원하지 않는 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="o" /> 매개 변수가 null입니다.또는 <paramref name="T" /> 매개 변수가 null입니다.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetComInterfaceForObject``2(``0)">
      <summary>[.NET Framework 4.5.1 이상 버전에서 지원됨] 지정된 형식의 개체에 지정된 인터페이스를 나타내는 IUnknown 인터페이스에 대한 포인터를 반환합니다.사용자 지정 쿼리 인터페이스 액세스가 기본적으로 사용됩니다.</summary>
      <returns>
        <paramref name="TInterface" /> 인터페이스를 나타내는 인터페이스 포인터입니다.</returns>
      <param name="o">인터페이스를 제공하는 개체입니다. </param>
      <typeparam name="T">
        <paramref name="o" />의 형식입니다. </typeparam>
      <typeparam name="TInterface">반환할 인터페이스의 형식입니다. </typeparam>
      <exception cref="T:System.ArgumentException">
        <paramref name="TInterface" /> 매개 변수가 인터페이스가 아닌 경우또는 COM에 해당 형식이 표시되지 않는 경우 또는<paramref name="T" /> 매개 변수는 개방형 제네릭 형식입니다.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="o" /> 매개 변수가 <paramref name="TInterface" /> 인터페이스를 지원하지 않는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="o" /> 매개 변수가 null입니다.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetDelegateForFunctionPointer``1(System.IntPtr)">
      <summary>[.NET Framework 4.5.1 이상 버전에서 지원됨] 관리되지 않는 함수 포인터를 지정된 형식의 대리자로 변환합니다. </summary>
      <returns>지정된 대리자 형식의 인스턴스입니다.</returns>
      <param name="ptr">변환할 관리되지 않는 함수 포인터입니다. </param>
      <typeparam name="TDelegate">반환할 대리자의 형식입니다. </typeparam>
      <exception cref="T:System.ArgumentException">
        <paramref name="TDelegate" /> 제네릭 매개 변수가 대리자가 아니거나 개방형 제네릭 형식입니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="ptr" /> 매개 변수가 null입니다.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetDelegateForFunctionPointer(System.IntPtr,System.Type)">
      <summary>관리되지 않는 함수 포인터를 대리자로 변환합니다.</summary>
      <returns>적절한 대리자 형식으로 캐스팅될 수 있는 대리자 인스턴스입니다.</returns>
      <param name="ptr">변환할 관리되지 않는 함수 포인터입니다.</param>
      <param name="t">반환될 대리자의 형식입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="t" /> 매개 변수가 대리자가 아니거나 제네릭인 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="ptr" /> 매개 변수가 null입니다.또는<paramref name="t" /> 매개 변수가 null입니다.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetExceptionCode">
      <summary>발생한 예외의 형식을 식별하는 코드를 검색합니다.</summary>
      <returns>예외의 형식입니다.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetExceptionForHR(System.Int32)">
      <summary>지정된 HRESULT 오류 코드를 해당하는 <see cref="T:System.Exception" /> 개체로 변환합니다.</summary>
      <returns>변환된 HRESULT를 나타내는 개체입니다.</returns>
      <param name="errorCode">변환할 HRESULT입니다.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetExceptionForHR(System.Int32,System.IntPtr)">
      <summary>예외 개체에 대한 IErrorInfo 인터페이스에 전달되는 추가 오류 정보를 사용하여 지정된 HRESULT 오류 코드를 해당하는 <see cref="T:System.Exception" /> 개체로 변환합니다.</summary>
      <returns>변환된 HRESULT와 <paramref name="errorInfo" />에서 가져온 정보를 나타내는 개체입니다.</returns>
      <param name="errorCode">변환할 HRESULT입니다.</param>
      <param name="errorInfo">오류에 대한 추가 정보를 제공하는 IErrorInfo 인터페이스에 대한 포인터입니다.현재 IErrorInfo 인터페이스를 사용하려면 IntPtr(0)를 지정하고, 현재 IErrorInfo 인터페이스를 무시하고 오류 코드에서만 예외를 생성하려면 IntPtr(-1)를 지정합니다.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetFunctionPointerForDelegate(System.Delegate)">
      <summary>대리자를 비관리 코드에서 호출할 수 있는 함수 포인터로 변환합니다.</summary>
      <returns>비관리 코드에 전달할 수 있는 값입니다. 비관리 코드에서는 이 값을 사용하여 관리되는 내부 대리자를 호출할 수 있습니다. </returns>
      <param name="d">비관리 코드에 전달할 대리자입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="d" /> 매개 변수가 제네릭 형식인 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="d" /> 매개 변수가 null입니다.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetFunctionPointerForDelegate``1(``0)">
      <summary>[.NET Framework 4.5.1 이상 버전에서 지원됨] 지정된 형식의 대리자를 관리되지 않는 코드에서 호출할 수 있는 함수 포인터로 변환합니다. </summary>
      <returns>비관리 코드에 전달할 수 있는 값입니다. 비관리 코드에서는 이 값을 사용하여 관리되는 내부 대리자를 호출할 수 있습니다. </returns>
      <param name="d">비관리 코드에 전달할 대리자입니다. </param>
      <typeparam name="TDelegate">변환할 대리자의 형식입니다. </typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="d" /> 매개 변수가 null입니다. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetHRForException(System.Exception)">
      <summary>지정된 예외를 HRESULT로 변환합니다.</summary>
      <returns>해당 예외로 매핑되는 HRESULT입니다.</returns>
      <param name="e">HRESULT로 변환할 예외입니다.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetHRForLastWin32Error">
      <summary>
        <see cref="T:System.Runtime.InteropServices.Marshal" />을 사용하여 실행된 Win32 코드에서 발생한 마지막 오류에 해당하는 HRESULT를 반환합니다.</summary>
      <returns>마지막 Win32 오류 코드에 해당하는 HRESULT입니다.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetIUnknownForObject(System.Object)">
      <summary>관리되는 개체에서 IUnknown 인터페이스를 반환합니다.</summary>
      <returns>
        <paramref name="o" /> 매개 변수에 대한 IUnknown 포인터입니다.</returns>
      <param name="o">IUnknown 인터페이스가 요청되는 개체입니다.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetLastWin32Error">
      <summary>
        <see cref="F:System.Runtime.InteropServices.DllImportAttribute.SetLastError" /> 플래그가 설정된 플랫폼 호출을 사용하여 호출된 관리되지 않는 마지막 함수에서 반환하는 오류 코드를 반환합니다.</summary>
      <returns>Win32 SetLastError 함수에 대한 호출로 설정된 마지막 오류 코드입니다.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetNativeVariantForObject(System.Object,System.IntPtr)">
      <summary>개체를 COM VARIANT로 변환합니다.</summary>
      <param name="obj">COM VARIANT를 가져올 개체입니다.</param>
      <param name="pDstNativeVariant">
        <paramref name="obj" /> 매개 변수에 해당하는 VARIANT를 받을 포인터입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="obj" /> 매개 변수가 제네릭 형식인 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetNativeVariantForObject``1(``0,System.IntPtr)">
      <summary>[.NET Framework 4.5.1 이상 버전에서 지원됨] 지정한 형식의 개체를 COM VARIANT로 변환합니다. </summary>
      <param name="obj">COM VARIANT를 가져올 개체입니다. </param>
      <param name="pDstNativeVariant">
        <paramref name="obj" /> 매개 변수에 해당하는 VARIANT를 받을 포인터입니다. </param>
      <typeparam name="T">변환할 개체의 형식입니다. </typeparam>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetObjectForIUnknown(System.IntPtr)">
      <summary>IUnknown 인터페이스에 대한 포인터를 사용하여 COM 개체를 나타내는 형식의 인스턴스를 반환합니다.</summary>
      <returns>지정된 관리되지 않는 COM 개체를 나타내는 개체입니다.</returns>
      <param name="pUnk">IUnknown 인터페이스에 대한 포인터입니다. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetObjectForNativeVariant(System.IntPtr)">
      <summary>COM VARIANT를 개체로 변환합니다.</summary>
      <returns>
        <paramref name="pSrcNativeVariant" /> 매개 변수에 해당하는 개체입니다.</returns>
      <param name="pSrcNativeVariant">COM VARIANT에 대한 포인터입니다.</param>
      <exception cref="T:System.Runtime.InteropServices.InvalidOleVariantTypeException">
        <paramref name="pSrcNativeVariant" />가 유효한 VARIANT 형식이 아닌 경우</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="pSrcNativeVariant" />가 지원되지 않는 형식인 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetObjectForNativeVariant``1(System.IntPtr)">
      <summary>[.NET Framework 4.5.1 이상 버전에서 지원됨] COM VARIANT를 지정된 형식의 개체로 변환합니다. </summary>
      <returns>
        <paramref name="pSrcNativeVariant" /> 매개 변수에 해당하는 지정된 형식의 개체입니다. </returns>
      <param name="pSrcNativeVariant">COM VARIANT에 대한 포인터입니다. </param>
      <typeparam name="T">COM VARIANT를 변환할 형식입니다. </typeparam>
      <exception cref="T:System.Runtime.InteropServices.InvalidOleVariantTypeException">
        <paramref name="pSrcNativeVariant" />가 유효한 VARIANT 형식이 아닌 경우 </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="pSrcNativeVariant" />가 지원되지 않는 형식인 경우 </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetObjectsForNativeVariants(System.IntPtr,System.Int32)">
      <summary>COM VARIANT 배열을 개체 배열로 변환합니다. </summary>
      <returns>
        <paramref name="aSrcNativeVariant" />에 해당하는 개체 배열입니다.</returns>
      <param name="aSrcNativeVariant">COM VARIANT 배열의 첫 번째 요소에 대한 포인터입니다.</param>
      <param name="cVars">
        <paramref name="aSrcNativeVariant" />에서 COM VARIANT의 수입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="cVars" />가 음수인 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetObjectsForNativeVariants``1(System.IntPtr,System.Int32)">
      <summary>[.NET Framework 4.5.1 이상 버전에서 지원됨] COM VARIANT의 배열을 지정된 형식의 배열로 변환합니다. </summary>
      <returns>
        <paramref name="aSrcNativeVariant" />에 해당하는 <paramref name="T" /> 개체 배열입니다. </returns>
      <param name="aSrcNativeVariant">COM VARIANT 배열의 첫 번째 요소에 대한 포인터입니다. </param>
      <param name="cVars">
        <paramref name="aSrcNativeVariant" />에서 COM VARIANT의 수입니다. </param>
      <typeparam name="T">반환할 배열의 형식입니다. </typeparam>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="cVars" />가 음수인 경우 </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetStartComSlot(System.Type)">
      <summary>사용자 정의 메서드가 포함된 가상 함수 테이블(v-table 또는 VTBL)의 첫 번째 슬롯을 가져옵니다.</summary>
      <returns>사용자 정의 메서드가 포함된 첫 번째 VTBL 슬롯입니다.IUnknown을 기준으로 하는 인터페이스의 경우 첫 번째 슬롯은 3이고, IDispatch를 기준으로 하는 인터페이스의 경우 7입니다.</returns>
      <param name="t">인터페이스를 나타내는 형식입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="t" />가 COM에 표시되지 않는 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetTypeFromCLSID(System.Guid)">
      <summary>지정된 CLSID(클래스 식별자)와 연관된 형식을 반환합니다. </summary>
      <returns>CLSID가 유효한지 여부에 관계 없이 System.__ComObject를 반환합니다. </returns>
      <param name="clsid">반환할 형식의 CLSID입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetTypeInfoName(System.Runtime.InteropServices.ComTypes.ITypeInfo)">
      <summary>ITypeInfo 개체가 나타내는 형식의 이름을 검색합니다.</summary>
      <returns>
        <paramref name="typeInfo" /> 매개 변수가 가리키는 형식의 이름입니다.</returns>
      <param name="typeInfo">ITypeInfo 포인터를 나타내는 개체입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="typeInfo" /> 매개 변수가 null입니다.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.GetUniqueObjectForIUnknown(System.IntPtr)">
      <summary>지정된 IUnknown 인터페이스에 대해 고유한 RCW(RCW) 개체를 만듭니다.</summary>
      <returns>지정된 IUnknown 인터페이스에 대한 고유한 RCW입니다.</returns>
      <param name="unknown">IUnknown 인터페이스에 대한 관리되는 포인터입니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.IsComObject(System.Object)">
      <summary>지정된 개체가 COM 개체를 나타낼지 여부를 나타냅니다.</summary>
      <returns>
        <paramref name="o" /> 매개 변수가 COM 형식이면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="o">검사할 개체입니다.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.OffsetOf``1(System.String)">
      <summary>[.NET Framework 4.5.1 이상 버전에서 지원됨] 지정된 관리되는 클래스의 관리되지 않는 형식의 필드 오프셋을 반환합니다.</summary>
      <returns>플랫폼 호출로 선언된 지정된 클래스 내의 <paramref name="fieldName" /> 매개 변수에 대한 오프셋(바이트)입니다. </returns>
      <param name="fieldName">
        <paramref name="T" /> 형식의 필드 이름입니다. </param>
      <typeparam name="T">관리되는 값 또는 formatted 참조 형식입니다.클래스에 <see cref="T:System.Runtime.InteropServices.StructLayoutAttribute" /> 특성을 적용해야 합니다.</typeparam>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.OffsetOf(System.Type,System.String)">
      <summary>관리되는 클래스의 관리되지 않는 형식의 필드 오프셋을 반환합니다.</summary>
      <returns>플랫폼 호출로 선언된 지정된 클래스 내의 <paramref name="fieldName" /> 매개 변수에 대한 오프셋(바이트)입니다.</returns>
      <param name="t">관리되는 클래스를 지정하는 값 형식이나 서식이 지정된 참조 형식입니다.클래스에 <see cref="T:System.Runtime.InteropServices.StructLayoutAttribute" />를 적용해야 합니다.</param>
      <param name="fieldName">
        <paramref name="t" /> 매개 변수에 있는 필드입니다.</param>
      <exception cref="T:System.ArgumentException">클래스를 구조체로 내보낼 수 없거나 필드가 공용이 아닌 경우.NET Framework 버전 2.0 이상에서는 이 필드가 private 필드일 수 있습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="t" /> 매개 변수가 null입니다.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStringAnsi(System.IntPtr)">
      <summary>관리되지 않는 ANSI 문자열에서 첫 번째 null 문자가 나올 때까지의 모든 문자를 관리되는 <see cref="T:System.String" />으로 복사하고 각 ANSI 문자를 유니코드로 확장합니다.</summary>
      <returns>관리되지 않는 ANSI 문자열의 복사본을 보유하는 관리되는 문자열입니다.<paramref name="ptr" />이 null이면 메서드가 null 문자열을 반환합니다.</returns>
      <param name="ptr">관리되지 않는 문자열의 첫 문자 주소입니다.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStringAnsi(System.IntPtr,System.Int32)">
      <summary>관리되는 <see cref="T:System.String" />을 할당하고, 관리되지 않는 ANSI 문자열에서 지정된 수의 문자를 해당 개체로 복사한 다음 각 ANSI 문자를 유니코드로 확장합니다.</summary>
      <returns>
        <paramref name="ptr" /> 매개 변수의 값이 null이 아니면 네이티브 ANSI 문자열의 복사본을 보유하는 관리되는 문자열이고, 그렇지 않으면 이 메서드에서 null을 반환합니다.</returns>
      <param name="ptr">관리되지 않는 문자열의 첫 문자 주소입니다.</param>
      <param name="len">복사할 입력 문자열의 바이트 수입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="len" />가 0보다 작은 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStringBSTR(System.IntPtr)">
      <summary>관리되는 <see cref="T:System.String" />을 할당하고 관리되지 않는 메모리에 저장된 BSTR 문자열을 그 안에 복사합니다.</summary>
      <returns>
        <paramref name="ptr" /> 매개 변수의 값이 null이 아니면 관리되지 않는 문자열의 복사본이 들어 있는 관리되는 문자열이고, 그렇지 않으면 null입니다.</returns>
      <param name="ptr">관리되지 않는 문자열의 첫 문자 주소입니다.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStringUni(System.IntPtr)">
      <summary>관리되는 <see cref="T:System.String" />을 할당하고 관리되지 않는 유니코드 문자열에서 첫 번째 null이 나올 때까지의 모든 문자를 해당 개체로 복사합니다.</summary>
      <returns>
        <paramref name="ptr" /> 매개 변수의 값이 null이 아니면 관리되지 않는 문자열의 복사본이 들어 있는 관리되는 문자열이고, 그렇지 않으면 null입니다.</returns>
      <param name="ptr">관리되지 않는 문자열의 첫 문자 주소입니다.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStringUni(System.IntPtr,System.Int32)">
      <summary>관리되는 <see cref="T:System.String" />을 할당하고 관리되지 않는 유니코드 문자열의 지정된 문자 수를 그 안에 복사합니다.</summary>
      <returns>
        <paramref name="ptr" /> 매개 변수의 값이 null이 아니면 관리되지 않는 문자열의 복사본이 들어 있는 관리되는 문자열이고, 그렇지 않으면 null입니다.</returns>
      <param name="ptr">관리되지 않는 문자열의 첫 문자 주소입니다.</param>
      <param name="len">복사할 유니코드 문자 수입니다.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStructure``1(System.IntPtr)">
      <summary>[.NET Framework 4.5.1 이상 버전에서 지원됨] 관리되지 않는 메모리 블록의 데이터를 제네릭 형식 매개 변수에 의해 지정된 형식의 새로 할당된 관리되는 개체로 마샬링합니다. </summary>
      <returns>
        <paramref name="ptr" /> 매개 변수가 가리키는 데이터가 있는 관리되는 개체입니다. </returns>
      <param name="ptr">관리되지 않는 메모리 블록에 대한 포인터입니다. </param>
      <typeparam name="T">데이터가 복사될 개체의 형식입니다.이 개체는 서식이 지정된 클래스나 구조체여야 합니다.</typeparam>
      <exception cref="T:System.ArgumentException">
        <paramref name="T" /> 레이아웃이 Sequential 또는 Explicit이 아닌 경우</exception>
      <exception cref="T:System.MissingMethodException">
        <paramref name="T" />에서 지정한 클래스에는 액세스할 수 있는 기본 생성자가 없습니다. </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStructure(System.IntPtr,System.Object)">
      <summary>관리되지 않는 메모리 블록의 데이터를 관리되는 개체로 마샬링합니다.</summary>
      <param name="ptr">관리되지 않는 메모리 블록에 대한 포인터입니다.</param>
      <param name="structure">데이터가 복사될 대상 개체입니다.이 개체는 형식이 지정된 클래스의 인스턴스여야 합니다.</param>
      <exception cref="T:System.ArgumentException">구조체 레이아웃이 Sequential 또는 Explicit이 아닌 경우또는 구조체가 boxed 값 형식인 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStructure(System.IntPtr,System.Type)">
      <summary>관리되지 않는 메모리 블록의 데이터를 지정된 형식의 새로 할당된 관리되는 개체로 마샬링합니다.</summary>
      <returns>
        <paramref name="ptr" /> 매개 변수가 가리키는 데이터가 있는 관리되는 개체입니다.</returns>
      <param name="ptr">관리되지 않는 메모리 블록에 대한 포인터입니다.</param>
      <param name="structureType">만들 개체의 형식입니다.이 개체는 서식이 지정된 클래스나 구조체를 나타내야 합니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="structureType" /> 매개 변수 레이아웃이 Sequential 또는 Explicit이 아닌 경우또는<paramref name="structureType" /> 매개 변수가 제네릭 형식인 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="structureType" />가 null인 경우</exception>
      <exception cref="T:System.MissingMethodException">
        <paramref name="structureType" />에서 지정한 클래스에는 액세스할 수 있는 기본 생성자가 없습니다. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.PtrToStructure``1(System.IntPtr,``0)">
      <summary>[.NET Framework 4.5.1 이상 버전에서 지원됨] 관리되지 않는 메모리 블록의 데이터를 지정된 형식의 관리되는 개체로 마샬링합니다. </summary>
      <param name="ptr">관리되지 않는 메모리 블록에 대한 포인터입니다. </param>
      <param name="structure">데이터가 복사될 대상 개체입니다. </param>
      <typeparam name="T">
        <paramref name="structure" />의 형식입니다.형식이 지정된 클래스여야 합니다.</typeparam>
      <exception cref="T:System.ArgumentException">구조체 레이아웃이 Sequential 또는 Explicit이 아닌 경우 </exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.QueryInterface(System.IntPtr,System.Guid@,System.IntPtr@)">
      <summary>COM 개체에서 지정된 인터페이스에 대한 포인터를 요청합니다.</summary>
      <returns>호출의 성공이나 실패를 나타내는 HRESULT입니다.</returns>
      <param name="pUnk">쿼리될 인터페이스입니다.</param>
      <param name="iid">요청된 인터페이스의 IID(인터페이스 식별자)입니다.</param>
      <param name="ppv">이 메서드는 반환될 때 반환된 인터페이스에 대한 참조를 포함합니다.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadByte(System.IntPtr)">
      <summary>관리되지 않는 메모리에서 싱글바이트를 읽습니다.</summary>
      <returns>관리되지 않는 메모리에서 읽은 바이트입니다.</returns>
      <param name="ptr">읽을 관리되지 않는 메모리의 주소입니다.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" />의 형식을 인식할 수 없는 경우또는<paramref name="ptr" />가 null인 경우 또는<paramref name="ptr" />이(가) 잘못되었습니다.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadByte(System.IntPtr,System.Int32)">
      <summary>관리되지 않는 메모리의 지정된 오프셋(또는 인덱스) 위치에서 싱글바이트를 읽습니다.</summary>
      <returns>관리되지 않는 메모리의 지정된 오프셋 위치에서 읽은 바이트입니다.</returns>
      <param name="ptr">읽을 관리되지 않는 메모리의 기본 주소입니다.</param>
      <param name="ofs">읽기 전에 <paramref name="ptr" /> 매개 변수에 추가되는 추가 바이트 오프셋입니다.</param>
      <exception cref="T:System.AccessViolationException">기준 주소(<paramref name="ptr" />)에 오프셋 바이트(<paramref name="ofs" />)를 더하면 null 또는 잘못된 주소가 생성되는 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadByte(System.Object,System.Int32)">
      <summary>관리되지 않는 메모리의 지정된 오프셋(또는 인덱스) 위치에서 싱글바이트를 읽습니다. </summary>
      <returns>관리되지 않는 메모리의 지정된 오프셋 위치에서 읽은 바이트입니다.</returns>
      <param name="ptr">소스 개체에 대한 관리되지 않는 메모리의 기본 주소입니다.</param>
      <param name="ofs">읽기 전에 <paramref name="ptr" /> 매개 변수에 추가되는 추가 바이트 오프셋입니다.</param>
      <exception cref="T:System.AccessViolationException">기준 주소(<paramref name="ptr" />)에 오프셋 바이트(<paramref name="ofs" />)를 더하면 null 또는 잘못된 주소가 생성되는 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" />이 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 개체인 경우.이 메서드에는 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 매개 변수를 사용할 수 없습니다.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt16(System.IntPtr)">
      <summary>관리되지 않는 메모리에서 16비트 부호 있는 정수를 읽습니다.</summary>
      <returns>관리되지 않는 메모리에서 읽은 16비트 부호 있는 정수입니다.</returns>
      <param name="ptr">읽을 관리되지 않는 메모리의 주소입니다.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" />의 형식을 인식할 수 없는 경우또는<paramref name="ptr" />가 null인 경우또는<paramref name="ptr" />이(가) 잘못되었습니다.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt16(System.IntPtr,System.Int32)">
      <summary>관리되지 않는 메모리의 지정된 오프셋 위치에서 16비트 부호 있는 정수를 읽습니다.</summary>
      <returns>관리되지 않는 메모리의 지정된 오프셋 위치에서 읽은 16비트 부호 있는 정수입니다.</returns>
      <param name="ptr">읽을 관리되지 않는 메모리의 기본 주소입니다.</param>
      <param name="ofs">읽기 전에 <paramref name="ptr" /> 매개 변수에 추가되는 추가 바이트 오프셋입니다.</param>
      <exception cref="T:System.AccessViolationException">기준 주소(<paramref name="ptr" />)에 오프셋 바이트(<paramref name="ofs" />)를 더하면 null 또는 잘못된 주소가 생성되는 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt16(System.Object,System.Int32)">
      <summary>관리되지 않는 메모리의 지정된 오프셋 위치에서 16비트 부호 있는 정수를 읽습니다.</summary>
      <returns>관리되지 않는 메모리의 지정된 오프셋 위치에서 읽은 16비트 부호 있는 정수입니다.</returns>
      <param name="ptr">소스 개체에 대한 관리되지 않는 메모리의 기본 주소입니다.</param>
      <param name="ofs">읽기 전에 <paramref name="ptr" /> 매개 변수에 추가되는 추가 바이트 오프셋입니다.</param>
      <exception cref="T:System.AccessViolationException">기준 주소(<paramref name="ptr" />)에 오프셋 바이트(<paramref name="ofs" />)를 더하면 null 또는 잘못된 주소가 생성되는 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" />이 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 개체인 경우.이 메서드에는 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 매개 변수를 사용할 수 없습니다.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt32(System.IntPtr)">
      <summary>관리되지 않는 메모리에서 32비트 부호 있는 정수를 읽습니다.</summary>
      <returns>관리되지 않는 메모리에서 읽은 32비트 부호 있는 정수입니다.</returns>
      <param name="ptr">읽을 관리되지 않는 메모리의 주소입니다.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" />의 형식을 인식할 수 없는 경우또는<paramref name="ptr" />가 null인 경우또는<paramref name="ptr" />이(가) 잘못되었습니다.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt32(System.IntPtr,System.Int32)">
      <summary>관리되지 않는 메모리의 지정된 오프셋 위치에서 32비트 부호 있는 정수를 읽습니다.</summary>
      <returns>관리되지 않는 메모리에서 읽은 32비트 부호 있는 정수입니다.</returns>
      <param name="ptr">읽을 관리되지 않는 메모리의 기본 주소입니다.</param>
      <param name="ofs">읽기 전에 <paramref name="ptr" /> 매개 변수에 추가되는 추가 바이트 오프셋입니다.</param>
      <exception cref="T:System.AccessViolationException">기준 주소(<paramref name="ptr" />)에 오프셋 바이트(<paramref name="ofs" />)를 더하면 null 또는 잘못된 주소가 생성되는 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt32(System.Object,System.Int32)">
      <summary>관리되지 않는 메모리의 지정된 오프셋 위치에서 32비트 부호 있는 정수를 읽습니다.</summary>
      <returns>관리되지 않는 메모리의 지정된 오프셋 위치에서 읽은 32비트 부호 있는 정수입니다.</returns>
      <param name="ptr">소스 개체에 대한 관리되지 않는 메모리의 기본 주소입니다.</param>
      <param name="ofs">읽기 전에 <paramref name="ptr" /> 매개 변수에 추가되는 추가 바이트 오프셋입니다.</param>
      <exception cref="T:System.AccessViolationException">기준 주소(<paramref name="ptr" />)에 오프셋 바이트(<paramref name="ofs" />)를 더하면 null 또는 잘못된 주소가 생성되는 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" />이 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 개체인 경우.이 메서드에는 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 매개 변수를 사용할 수 없습니다.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt64(System.IntPtr)">
      <summary>관리되지 않는 메모리에서 64비트 부호 있는 정수를 읽습니다.</summary>
      <returns>관리되지 않는 메모리에서 읽은 64비트 부호 있는 정수입니다.</returns>
      <param name="ptr">읽을 관리되지 않는 메모리의 주소입니다.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" />의 형식을 인식할 수 없는 경우또는<paramref name="ptr" />가 null인 경우또는<paramref name="ptr" />이(가) 잘못되었습니다.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt64(System.IntPtr,System.Int32)">
      <summary>관리되지 않는 메모리의 지정된 오프셋 위치에서 64비트 부호 있는 정수를 읽습니다.</summary>
      <returns>관리되지 않는 메모리의 지정된 오프셋 위치에서 읽은 64비트 부호 있는 정수입니다.</returns>
      <param name="ptr">읽을 관리되지 않는 메모리의 기본 주소입니다.</param>
      <param name="ofs">읽기 전에 <paramref name="ptr" /> 매개 변수에 추가되는 추가 바이트 오프셋입니다.</param>
      <exception cref="T:System.AccessViolationException">기준 주소(<paramref name="ptr" />)에 오프셋 바이트(<paramref name="ofs" />)를 더하면 null 또는 잘못된 주소가 생성되는 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadInt64(System.Object,System.Int32)">
      <summary>관리되지 않는 메모리의 지정된 오프셋 위치에서 64비트 부호 있는 정수를 읽습니다.</summary>
      <returns>관리되지 않는 메모리의 지정된 오프셋 위치에서 읽은 64비트 부호 있는 정수입니다.</returns>
      <param name="ptr">소스 개체에 대한 관리되지 않는 메모리의 기본 주소입니다.</param>
      <param name="ofs">읽기 전에 <paramref name="ptr" /> 매개 변수에 추가되는 추가 바이트 오프셋입니다.</param>
      <exception cref="T:System.AccessViolationException">기준 주소(<paramref name="ptr" />)에 오프셋 바이트(<paramref name="ofs" />)를 더하면 null 또는 잘못된 주소가 생성되는 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" />이 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 개체인 경우.이 메서드에는 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 매개 변수를 사용할 수 없습니다.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadIntPtr(System.IntPtr)">
      <summary>관리되지 않는 메모리에서 프로세서의 기본 크기로 지정된 정수를 읽습니다.</summary>
      <returns>관리되지 않는 메모리에서 읽은 정수입니다.32비트 컴퓨터에서는 32비트 정수가 반환되고 64비트 컴퓨터에서는 64비트 정수가 반환됩니다.</returns>
      <param name="ptr">읽을 관리되지 않는 메모리의 주소입니다.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" />의 형식을 인식할 수 없는 경우또는<paramref name="ptr" />가 null인 경우 또는<paramref name="ptr" />이(가) 잘못되었습니다.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadIntPtr(System.IntPtr,System.Int32)">
      <summary>관리되지 않는 메모리의 지정된 오프셋 위치에서 프로세서의 기본 크기로 지정된 정수를 읽습니다.</summary>
      <returns>관리되지 않는 메모리의 지정된 오프셋 위치에서 읽은 정수입니다.</returns>
      <param name="ptr">읽을 관리되지 않는 메모리의 기본 주소입니다.</param>
      <param name="ofs">읽기 전에 <paramref name="ptr" /> 매개 변수에 추가되는 추가 바이트 오프셋입니다.</param>
      <exception cref="T:System.AccessViolationException">기준 주소(<paramref name="ptr" />)에 오프셋 바이트(<paramref name="ofs" />)를 더하면 null 또는 잘못된 주소가 생성되는 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReadIntPtr(System.Object,System.Int32)">
      <summary>관리되지 않는 메모리에서 프로세서의 기본 크기로 지정된 정수를 읽습니다.</summary>
      <returns>관리되지 않는 메모리의 지정된 오프셋 위치에서 읽은 정수입니다.</returns>
      <param name="ptr">소스 개체에 대한 관리되지 않는 메모리의 기본 주소입니다.</param>
      <param name="ofs">읽기 전에 <paramref name="ptr" /> 매개 변수에 추가되는 추가 바이트 오프셋입니다.</param>
      <exception cref="T:System.AccessViolationException">기준 주소(<paramref name="ptr" />)에 오프셋 바이트(<paramref name="ofs" />)를 더하면 null 또는 잘못된 주소가 생성되는 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" />이 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 개체인 경우.이 메서드에는 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 매개 변수를 사용할 수 없습니다.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReAllocCoTaskMem(System.IntPtr,System.Int32)">
      <summary>
        <see cref="M:System.Runtime.InteropServices.Marshal.AllocCoTaskMem(System.Int32)" />을 사용하여 이전에 할당된 메모리 블록 크기를 조정합니다.</summary>
      <returns>다시 할당된 메모리 블록 주소를 나타내는 정수입니다.이 메모리는 <see cref="M:System.Runtime.InteropServices.Marshal.FreeCoTaskMem(System.IntPtr)" />을 사용하여 해제되어야 합니다.</returns>
      <param name="pv">
        <see cref="M:System.Runtime.InteropServices.Marshal.AllocCoTaskMem(System.Int32)" />을 사용하여 할당된 메모리에 대한 포인터입니다.</param>
      <param name="cb">할당된 블록의 새 크기입니다.</param>
      <exception cref="T:System.OutOfMemoryException">요청을 만족시킬 충분한 메모리가 없는 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReAllocHGlobal(System.IntPtr,System.IntPtr)">
      <summary>
        <see cref="M:System.Runtime.InteropServices.Marshal.AllocHGlobal(System.IntPtr)" />을 사용하여 이전에 할당된 메모리 블록 크기를 조정합니다.</summary>
      <returns>다시 할당된 메모리에 대한 포인터입니다.이 메모리는 <see cref="M:System.Runtime.InteropServices.Marshal.FreeHGlobal(System.IntPtr)" />을 사용하여 해제되어야 합니다.</returns>
      <param name="pv">
        <see cref="M:System.Runtime.InteropServices.Marshal.AllocHGlobal(System.IntPtr)" />을 사용하여 할당된 메모리에 대한 포인터입니다.</param>
      <param name="cb">할당된 블록의 새 크기입니다.이것은 포인터가 아니라 요청하는 바이트 수로, 형식 <see cref="T:System.IntPtr" />로 캐스팅합니다.포인터를 전달하면 크기로 처리됩니다.</param>
      <exception cref="T:System.OutOfMemoryException">요청을 만족시킬 충분한 메모리가 없는 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.Release(System.IntPtr)">
      <summary>지정된 인터페이스의 참조 횟수를 감소시킵니다.</summary>
      <returns>
        <paramref name="pUnk" /> 매개 변수에서 지정하는 인터페이스에 대한 참조 횟수의 새 값입니다.</returns>
      <param name="pUnk">해제할 인터페이스입니다.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ReleaseComObject(System.Object)">
      <summary>지정된 COM 개체와 연결된 지정된 RCW(RCW)의 참조 횟수를 감소시킵니다.</summary>
      <returns>
        <paramref name="o" />와 연결된 RCW 참조 횟수의 새 값입니다.RCW는 참조를 호출하는 관리되는 클라이언트의 수에 관계없이 래핑된 COM 개체에 대한 참조를 하나만 유지하므로 일반적으로 이 값은 0이 됩니다.</returns>
      <param name="o">해제할 COM 개체입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="o" />가 유효한 COM 개체가 아닌 경우</exception>
      <exception cref="T:System.NullReferenceException">
        <paramref name="o" />가 null인 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.SizeOf``1">
      <summary>[.NET Framework 4.5.1 이상 버전에서 지원됨] 관리되지 않는 형식의 크기(바이트)를 반환합니다. </summary>
      <returns>
        <paramref name="T" /> 제네릭 형식 매개 변수로 지정된 형식의 크기(바이트 단위)입니다. </returns>
      <typeparam name="T">크기가 반환되는 형식입니다. </typeparam>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.SizeOf(System.Object)">
      <summary>개체의 관리되지 않는 크기(바이트)를 반환합니다.</summary>
      <returns>비관리 코드에서 지정된 개체의 크기입니다.</returns>
      <param name="structure">크기가 반환되는 개체입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="structure" /> 매개 변수가 null입니다.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.SizeOf(System.Type)">
      <summary>관리되지 않는 형식의 크기(바이트)를 반환합니다.</summary>
      <returns>비관리 코드에서 지정된 형식의 크기입니다.</returns>
      <param name="t">크기가 반환되는 형식입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="t" /> 매개 변수가 제네릭 형식인 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="t" /> 매개 변수가 null입니다.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.SizeOf``1(``0)">
      <summary>[.NET Framework 4.5.1 이상 버전에서 지원됨] 지정된 형식의 개체의 관리되지 않은 크기를 반환합니다(바이트 단위). </summary>
      <returns>비관리 코드에서 지정된 개체의 크기(바이트)입니다. </returns>
      <param name="structure">크기가 반환되는 개체입니다. </param>
      <typeparam name="T">
        <paramref name="structure" /> 매개 변수의 형식입니다. </typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="structure" /> 매개 변수가 null입니다.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StringToBSTR(System.String)">
      <summary>BSTR을 할당하고 관리되는 <see cref="T:System.String" />의 내용을 그 안에 복사합니다.</summary>
      <returns>BSTR에 대한 관리되지 않는 포인터이거나 <paramref name="s" />가 null인 경우 0입니다.</returns>
      <param name="s">복사할 관리되는 문자열입니다.</param>
      <exception cref="T:System.OutOfMemoryException">사용 가능한 메모리가 부족한 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="s" />의 길이가 범위를 벗어난 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StringToCoTaskMemAnsi(System.String)">
      <summary>관리되는 <see cref="T:System.String" />의 내용을 관리되지 않는 COM 작업 할당자에서 할당된 메모리 블록으로 복사합니다.</summary>
      <returns>문자열에 할당된 메모리 블록에 대한 포인터를 나타내는 정수이거나 <paramref name="s" />가 null인 경우 0입니다.</returns>
      <param name="s">복사할 관리되는 문자열입니다.</param>
      <exception cref="T:System.OutOfMemoryException">사용 가능한 메모리가 부족한 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="s" /> 매개 변수가 운영 체제에서 허용되는 최대 길이를 초과하는 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StringToCoTaskMemUni(System.String)">
      <summary>관리되는 <see cref="T:System.String" />의 내용을 관리되지 않는 COM 작업 할당자에서 할당된 메모리 블록으로 복사합니다.</summary>
      <returns>문자열에 할당된 메모리 블록에 대한 포인터를 나타내는 정수이거나 s가 null인 경우 0입니다.</returns>
      <param name="s">복사할 관리되는 문자열입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="s" /> 매개 변수가 운영 체제에서 허용되는 최대 길이를 초과하는 경우</exception>
      <exception cref="T:System.OutOfMemoryException">사용 가능한 메모리가 부족한 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StringToHGlobalAnsi(System.String)">
      <summary>복사할 때 ANSI 형식으로 변환하여 관리되는 <see cref="T:System.String" />의 내용을 관리되지 않는 메모리로 복사합니다.</summary>
      <returns>관리되지 않는 메모리에서 <paramref name="s" />가 복사된 주소이거나 <paramref name="s" />가 null인 경우 0입니다.</returns>
      <param name="s">복사할 관리되는 문자열입니다.</param>
      <exception cref="T:System.OutOfMemoryException">사용 가능한 메모리가 부족한 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="s" /> 매개 변수가 운영 체제에서 허용되는 최대 길이를 초과하는 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StringToHGlobalUni(System.String)">
      <summary>관리되는 <see cref="T:System.String" />의 내용을 관리되지 않는 메모리로 복사합니다.</summary>
      <returns>관리되지 않는 메모리에서 <paramref name="s" />가 복사된 주소이거나 <paramref name="s" />가 null인 경우 0입니다.</returns>
      <param name="s">복사할 관리되는 문자열입니다.</param>
      <exception cref="T:System.OutOfMemoryException">메서드가 충분한 네이티브 힙 메모리를 할당할 수 없는 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="s" /> 매개 변수가 운영 체제에서 허용되는 최대 길이를 초과하는 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StructureToPtr(System.Object,System.IntPtr,System.Boolean)">
      <summary>관리되는 개체의 데이터를 관리되지 않는 메모리 블록으로 마샬링합니다.</summary>
      <param name="structure">마샬링될 데이터가 있는 관리되는 개체입니다.이 개체는 구조체이거나 형식이 지정된 클래스의 인스턴스여야 합니다.</param>
      <param name="ptr">관리되지 않는 메모리 블록에 대한 포인터로서 이 메서드가 호출되기 전에 할당되어야 합니다.</param>
      <param name="fDeleteOld">이 메서드가 데이터를 복사하기 전에 <paramref name="ptr" /> 매개 변수에 대해 <see cref="M:System.Runtime.InteropServices.Marshal.DestroyStructure(System.IntPtr,System.Type)" /> 메서드를 호출하려면 true로 설정합니다.블록에는 유효한 데이터가 있어야 합니다.메모리 블록에 이미 데이터가 포함되어 있을 때 false를 전달하면 메모리 누수가 발생할 수 있습니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="structure" />는 형식이 지정된 클래스가 아닌 참조 형식입니다. 또는<paramref name="structure" />은 제네릭 형식입니다. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.StructureToPtr``1(``0,System.IntPtr,System.Boolean)">
      <summary>[.NET Framework 4.5.1 이상 버전에서 지원됨] 지정된 형식의 관리되는 개체의 데이터를 관리되지 않는 메모리 블록으로 마샬링합니다. </summary>
      <param name="structure">마샬링될 데이터가 있는 관리되는 개체입니다.이 개체는 구조체이거나 형식이 지정된 클래스의 인스턴스여야 합니다.</param>
      <param name="ptr">관리되지 않는 메모리 블록에 대한 포인터로서 이 메서드가 호출되기 전에 할당되어야 합니다. </param>
      <param name="fDeleteOld">이 메서드가 데이터를 복사하기 전에 <paramref name="ptr" /> 매개 변수에 대해 <see cref="M:System.Runtime.InteropServices.Marshal.DestroyStructure``1(System.IntPtr)" /> 메서드를 호출하려면 true로 설정합니다.블록에는 유효한 데이터가 있어야 합니다.메모리 블록에 이미 데이터가 포함되어 있을 때 false를 전달하면 메모리 누수가 발생할 수 있습니다.</param>
      <typeparam name="T">관리되는 개체의 형식입니다. </typeparam>
      <exception cref="T:System.ArgumentException">
        <paramref name="structure" />는 형식이 지정된 클래스가 아닌 참조 형식입니다. </exception>
    </member>
    <member name="F:System.Runtime.InteropServices.Marshal.SystemDefaultCharSize">
      <summary>시스템의 기본 문자 크기를 나타냅니다. 유니코드 시스템의 경우 기본값이 2이고, ANSI 시스템의 경우 1입니다.이 필드는 읽기 전용입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.Marshal.SystemMaxDBCSCharSize">
      <summary>현재 운영 체제에서 DBCS(더블바이트 문자 집합)의 최대 크기(바이트)를 나타냅니다.이 필드는 읽기 전용입니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ThrowExceptionForHR(System.Int32)">
      <summary>오류가 있는 특정 HRESULT 값을 사용하여 예외를 throw합니다.</summary>
      <param name="errorCode">해당 예외에 해당하는 HRESULT입니다.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ThrowExceptionForHR(System.Int32,System.IntPtr)">
      <summary>지정된 IErrorInfo 인터페이스를 기준으로 특정 오류 HRESULT를 사용하여 예외를 throw합니다.</summary>
      <param name="errorCode">해당 예외에 해당하는 HRESULT입니다.</param>
      <param name="errorInfo">오류에 대한 추가 정보를 제공하는 IErrorInfo 인터페이스에 대한 포인터입니다.현재 IErrorInfo 인터페이스를 사용하려면 IntPtr(0)를 지정하고, 현재 IErrorInfo 인터페이스를 무시하고 오류 코드에서만 예외를 생성하려면 IntPtr(-1)를 지정합니다.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.UnsafeAddrOfPinnedArrayElement(System.Array,System.Int32)">
      <summary>지정된 배열의 지정된 인덱스에 있는 요소의 주소를 가져옵니다.</summary>
      <returns>
        <paramref name="arr" />에 있는 <paramref name="index" /> 주소입니다.</returns>
      <param name="arr">필요한 요소가 포함된 배열입니다.</param>
      <param name="index">원하는 요소의 <paramref name="arr" /> 매개 변수에 있는 인덱스입니다.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.UnsafeAddrOfPinnedArrayElement``1(``0[],System.Int32)">
      <summary>[.NET Framework 4.5.1 이상 버전에서 지원됨] 지정된 형식의 배열에 지정된 인덱스에 있는 요소의 주소를 가져옵니다. </summary>
      <returns>
        <paramref name="arr" />에 있는 <paramref name="index" /> 주소입니다. </returns>
      <param name="arr">필요한 요소가 포함된 배열입니다. </param>
      <param name="index">
        <paramref name="arr" /> 배열에서 원하는 요소의 인덱스입니다. </param>
      <typeparam name="T">배열의 형식입니다. </typeparam>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteByte(System.IntPtr,System.Byte)">
      <summary>관리되지 않는 메모리에 싱글바이트 값을 씁니다.</summary>
      <param name="ptr">관리되지 않는 메모리에서 값을 쓸 주소입니다.</param>
      <param name="val">작성할 값입니다.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" />의 형식을 인식할 수 없는 경우또는<paramref name="ptr" />가 null인 경우또는<paramref name="ptr" />이(가) 잘못되었습니다.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteByte(System.IntPtr,System.Int32,System.Byte)">
      <summary>관리되지 않는 메모리의 지정된 오프셋 위치에 싱글바이트 값을 씁니다.</summary>
      <param name="ptr">관리되지 않는 메모리에서 값을 쓸 기본 주소입니다.</param>
      <param name="ofs">쓰기 전에 <paramref name="ptr" /> 매개 변수에 추가되는 추가 바이트 오프셋입니다.</param>
      <param name="val">작성할 값입니다.</param>
      <exception cref="T:System.AccessViolationException">기준 주소(<paramref name="ptr" />)에 오프셋 바이트(<paramref name="ofs" />)를 더하면 null 또는 잘못된 주소가 생성되는 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteByte(System.Object,System.Int32,System.Byte)">
      <summary>관리되지 않는 메모리의 지정된 오프셋 위치에 싱글바이트 값을 씁니다.</summary>
      <param name="ptr">대상 개체에 대한 관리되지 않는 메모리의 기본 주소입니다.</param>
      <param name="ofs">쓰기 전에 <paramref name="ptr" /> 매개 변수에 추가되는 추가 바이트 오프셋입니다.</param>
      <param name="val">작성할 값입니다.</param>
      <exception cref="T:System.AccessViolationException">기준 주소(<paramref name="ptr" />)에 오프셋 바이트(<paramref name="ofs" />)를 더하면 null 또는 잘못된 주소가 생성되는 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" />이 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 개체인 경우.이 메서드에는 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 매개 변수를 사용할 수 없습니다.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.IntPtr,System.Char)">
      <summary>관리되지 않는 메모리에 16비트 정수 값으로 문자를 씁니다.</summary>
      <param name="ptr">관리되지 않는 메모리에서 값을 쓸 주소입니다.</param>
      <param name="val">작성할 값입니다.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" />의 형식을 인식할 수 없는 경우또는<paramref name="ptr" />가 null인 경우또는<paramref name="ptr" />이(가) 잘못되었습니다.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.IntPtr,System.Int16)">
      <summary>관리되지 않는 메모리에 16비트 정수 값을 씁니다.</summary>
      <param name="ptr">관리되지 않는 메모리에서 값을 쓸 주소입니다.</param>
      <param name="val">작성할 값입니다.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" />의 형식을 인식할 수 없는 경우또는<paramref name="ptr" />가 null인 경우또는<paramref name="ptr" />이(가) 잘못되었습니다.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.IntPtr,System.Int32,System.Char)">
      <summary>관리되지 않는 메모리의 지정된 오프셋 위치에 16비트 부호 있는 정수 값을 씁니다.</summary>
      <param name="ptr">네이티브 힙에서 값을 쓸 기본 주소입니다.</param>
      <param name="ofs">쓰기 전에 <paramref name="ptr" /> 매개 변수에 추가되는 추가 바이트 오프셋입니다.</param>
      <param name="val">작성할 값입니다.</param>
      <exception cref="T:System.AccessViolationException">기준 주소(<paramref name="ptr" />)에 오프셋 바이트(<paramref name="ofs" />)를 더하면 null 또는 잘못된 주소가 생성되는 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.IntPtr,System.Int32,System.Int16)">
      <summary>관리되지 않는 메모리의 지정된 오프셋 위치에 16비트 부호 있는 정수 값을 씁니다.</summary>
      <param name="ptr">관리되지 않는 메모리에서 값을 쓸 기본 주소입니다.</param>
      <param name="ofs">쓰기 전에 <paramref name="ptr" /> 매개 변수에 추가되는 추가 바이트 오프셋입니다.</param>
      <param name="val">작성할 값입니다.</param>
      <exception cref="T:System.AccessViolationException">기준 주소(<paramref name="ptr" />)에 오프셋 바이트(<paramref name="ofs" />)를 더하면 null 또는 잘못된 주소가 생성되는 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.Object,System.Int32,System.Char)">
      <summary>관리되지 않는 메모리의 지정된 오프셋 위치에 16비트 부호 있는 정수 값을 씁니다.</summary>
      <param name="ptr">대상 개체에 대한 관리되지 않는 메모리의 기본 주소입니다.</param>
      <param name="ofs">쓰기 전에 <paramref name="ptr" /> 매개 변수에 추가되는 추가 바이트 오프셋입니다.</param>
      <param name="val">작성할 값입니다.</param>
      <exception cref="T:System.AccessViolationException">기준 주소(<paramref name="ptr" />)에 오프셋 바이트(<paramref name="ofs" />)를 더하면 null 또는 잘못된 주소가 생성되는 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" />이 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 개체인 경우.이 메서드에는 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 매개 변수를 사용할 수 없습니다.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt16(System.Object,System.Int32,System.Int16)">
      <summary>관리되지 않는 메모리의 지정된 오프셋 위치에 16비트 부호 있는 정수 값을 씁니다.</summary>
      <param name="ptr">대상 개체에 대한 관리되지 않는 메모리의 기본 주소입니다.</param>
      <param name="ofs">쓰기 전에 <paramref name="ptr" /> 매개 변수에 추가되는 추가 바이트 오프셋입니다. </param>
      <param name="val">작성할 값입니다.</param>
      <exception cref="T:System.AccessViolationException">기준 주소(<paramref name="ptr" />)에 오프셋 바이트(<paramref name="ofs" />)를 더하면 null 또는 잘못된 주소가 생성되는 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" />이 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 개체인 경우.이 메서드에는 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 매개 변수를 사용할 수 없습니다.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt32(System.IntPtr,System.Int32)">
      <summary>관리되지 않는 메모리에 부호 있는 32비트 정수 값을 씁니다.</summary>
      <param name="ptr">관리되지 않는 메모리에서 값을 쓸 주소입니다.</param>
      <param name="val">작성할 값입니다.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" />의 형식을 인식할 수 없는 경우또는<paramref name="ptr" />가 null인 경우 또는<paramref name="ptr" />이(가) 잘못되었습니다.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt32(System.IntPtr,System.Int32,System.Int32)">
      <summary>관리되지 않는 메모리의 지정된 오프셋 위치에 32비트 부호 있는 정수 값을 씁니다.</summary>
      <param name="ptr">관리되지 않는 메모리에서 값을 쓸 기본 주소입니다.</param>
      <param name="ofs">쓰기 전에 <paramref name="ptr" /> 매개 변수에 추가되는 추가 바이트 오프셋입니다.</param>
      <param name="val">작성할 값입니다.</param>
      <exception cref="T:System.AccessViolationException">기준 주소(<paramref name="ptr" />)에 오프셋 바이트(<paramref name="ofs" />)를 더하면 null 또는 잘못된 주소가 생성되는 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt32(System.Object,System.Int32,System.Int32)">
      <summary>관리되지 않는 메모리의 지정된 오프셋 위치에 32비트 부호 있는 정수 값을 씁니다.</summary>
      <param name="ptr">대상 개체에 대한 관리되지 않는 메모리의 기본 주소입니다.</param>
      <param name="ofs">쓰기 전에 <paramref name="ptr" /> 매개 변수에 추가되는 추가 바이트 오프셋입니다.</param>
      <param name="val">작성할 값입니다.</param>
      <exception cref="T:System.AccessViolationException">기준 주소(<paramref name="ptr" />)에 오프셋 바이트(<paramref name="ofs" />)를 더하면 null 또는 잘못된 주소가 생성되는 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" />이 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 개체인 경우.이 메서드에는 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 매개 변수를 사용할 수 없습니다.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt64(System.IntPtr,System.Int32,System.Int64)">
      <summary>관리되지 않는 메모리의 지정된 오프셋 위치에 64비트 부호 있는 정수 값을 씁니다.</summary>
      <param name="ptr">데이터를 쓸 관리되지 않는 메모리의 기본 주소입니다.</param>
      <param name="ofs">쓰기 전에 <paramref name="ptr" /> 매개 변수에 추가되는 추가 바이트 오프셋입니다.</param>
      <param name="val">작성할 값입니다.</param>
      <exception cref="T:System.AccessViolationException">기준 주소(<paramref name="ptr" />)에 오프셋 바이트(<paramref name="ofs" />)를 더하면 null 또는 잘못된 주소가 생성되는 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt64(System.IntPtr,System.Int64)">
      <summary>관리되지 않는 메모리에 부호 있는 64비트 정수 값을 씁니다.</summary>
      <param name="ptr">관리되지 않는 메모리에서 값을 쓸 주소입니다.</param>
      <param name="val">작성할 값입니다.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" />의 형식을 인식할 수 없는 경우또는<paramref name="ptr" />가 null인 경우또는<paramref name="ptr" />이(가) 잘못되었습니다.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteInt64(System.Object,System.Int32,System.Int64)">
      <summary>관리되지 않는 메모리의 지정된 오프셋 위치에 64비트 부호 있는 정수 값을 씁니다.</summary>
      <param name="ptr">대상 개체에 대한 관리되지 않는 메모리의 기본 주소입니다.</param>
      <param name="ofs">쓰기 전에 <paramref name="ptr" /> 매개 변수에 추가되는 추가 바이트 오프셋입니다.</param>
      <param name="val">작성할 값입니다.</param>
      <exception cref="T:System.AccessViolationException">기준 주소(<paramref name="ptr" />)에 오프셋 바이트(<paramref name="ofs" />)를 더하면 null 또는 잘못된 주소가 생성되는 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" />이 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 개체인 경우.이 메서드에는 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 매개 변수를 사용할 수 없습니다.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteIntPtr(System.IntPtr,System.Int32,System.IntPtr)">
      <summary>관리되지 않는 메모리의 지정된 오프셋 위치에 프로세서의 기본 크기로 지정된 정수 값을 씁니다.</summary>
      <param name="ptr">관리되지 않는 메모리에서 값을 쓸 기본 주소입니다.</param>
      <param name="ofs">쓰기 전에 <paramref name="ptr" /> 매개 변수에 추가되는 추가 바이트 오프셋입니다.</param>
      <param name="val">작성할 값입니다.</param>
      <exception cref="T:System.AccessViolationException">기준 주소(<paramref name="ptr" />)에 오프셋 바이트(<paramref name="ofs" />)를 더하면 null 또는 잘못된 주소가 생성되는 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteIntPtr(System.IntPtr,System.IntPtr)">
      <summary>관리되지 않는 메모리에 프로세서의 기본 크기로 지정된 정수 값을 씁니다.</summary>
      <param name="ptr">관리되지 않는 메모리에서 값을 쓸 주소입니다.</param>
      <param name="val">작성할 값입니다.</param>
      <exception cref="T:System.AccessViolationException">
        <paramref name="ptr" />의 형식을 인식할 수 없는 경우또는<paramref name="ptr" />가 null인 경우또는<paramref name="ptr" />이(가) 잘못되었습니다.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.WriteIntPtr(System.Object,System.Int32,System.IntPtr)">
      <summary>관리되지 않는 메모리에 프로세서의 기본 크기로 지정된 정수 값을 씁니다.</summary>
      <param name="ptr">대상 개체에 대한 관리되지 않는 메모리의 기본 주소입니다.</param>
      <param name="ofs">쓰기 전에 <paramref name="ptr" /> 매개 변수에 추가되는 추가 바이트 오프셋입니다.</param>
      <param name="val">작성할 값입니다.</param>
      <exception cref="T:System.AccessViolationException">기준 주소(<paramref name="ptr" />)에 오프셋 바이트(<paramref name="ofs" />)를 더하면 null 또는 잘못된 주소가 생성되는 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="ptr" />이 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 개체인 경우.이 메서드에는 <see cref="T:System.Runtime.InteropServices.ArrayWithOffset" /> 매개 변수를 사용할 수 없습니다.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ZeroFreeBSTR(System.IntPtr)">
      <summary>
        <see cref="M:System.Runtime.InteropServices.Marshal.SecureStringToBSTR(System.Security.SecureString)" /> 메서드를 사용하여 할당된 BSTR 포인터를 해제합니다.</summary>
      <param name="s">해제할 BSTR의 주소입니다.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ZeroFreeCoTaskMemAnsi(System.IntPtr)">
      <summary>
        <see cref="M:System.Runtime.InteropServices.Marshal.SecureStringToCoTaskMemAnsi(System.Security.SecureString)" /> 메서드를 사용하여 할당한 관리되지 않는 문자열 포인터를 해제합니다.</summary>
      <param name="s">해제할 관리되지 않는 문자열의 주소입니다.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ZeroFreeCoTaskMemUnicode(System.IntPtr)">
      <summary>
        <see cref="M:System.Runtime.InteropServices.Marshal.SecureStringToCoTaskMemUnicode(System.Security.SecureString)" /> 메서드를 사용하여 할당한 관리되지 않는 문자열 포인터를 해제합니다.</summary>
      <param name="s">해제할 관리되지 않는 문자열의 주소입니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ZeroFreeGlobalAllocAnsi(System.IntPtr)">
      <summary>
        <see cref="M:System.Runtime.InteropServices.Marshal.SecureStringToGlobalAllocAnsi(System.Security.SecureString)" /> 메서드를 사용하여 할당한 관리되지 않는 문자열 포인터를 해제합니다.</summary>
      <param name="s">해제할 관리되지 않는 문자열의 주소입니다.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Runtime.InteropServices.Marshal.ZeroFreeGlobalAllocUnicode(System.IntPtr)">
      <summary>
        <see cref="M:System.Runtime.InteropServices.Marshal.SecureStringToGlobalAllocUnicode(System.Security.SecureString)" /> 메서드를 사용하여 할당한 관리되지 않는 문자열 포인터를 해제합니다.</summary>
      <param name="s">해제할 관리되지 않는 문자열의 주소입니다.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.MarshalAsAttribute">
      <summary>관리 코드와 비관리 코드 간에 데이터를 마샬링하는 방법을 나타냅니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.MarshalAsAttribute.#ctor(System.Int16)">
      <summary>지정된 <see cref="T:System.Runtime.InteropServices.UnmanagedType" /> 값을 사용하여 <see cref="T:System.Runtime.InteropServices.MarshalAsAttribute" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="unmanagedType">데이터를 마샬링할 값입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.MarshalAsAttribute.#ctor(System.Runtime.InteropServices.UnmanagedType)">
      <summary>지정된 <see cref="T:System.Runtime.InteropServices.UnmanagedType" /> 열거형 멤버를 사용하여 <see cref="T:System.Runtime.InteropServices.MarshalAsAttribute" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="unmanagedType">데이터를 마샬링할 값입니다. </param>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.ArraySubType">
      <summary>관리되지 않는 <see cref="F:System.Runtime.InteropServices.UnmanagedType.LPArray" /> 또는 <see cref="F:System.Runtime.InteropServices.UnmanagedType.ByValArray" />의 요소 형식을 지정합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.IidParameterIndex">
      <summary>COM에서 사용하는 관리되지 않는 iid_is 특성의 매개 변수 인덱스를 지정합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.MarshalCookie">
      <summary>사용자 지정 마샬러에 추가 정보를 제공합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.MarshalType">
      <summary>사용자 지정 마샬러의 정규화된 이름을 지정합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.MarshalTypeRef">
      <summary>
        <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.MarshalType" />을 형식으로 구현합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.SafeArraySubType">
      <summary>
        <see cref="F:System.Runtime.InteropServices.UnmanagedType.SafeArray" />의 요소 형식을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.SafeArrayUserDefinedSubType">
      <summary>
        <see cref="F:System.Runtime.InteropServices.UnmanagedType.SafeArray" />의 사용자 정의 요소 형식을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeConst">
      <summary>가져올 고정 길이 배열의 요소 개수 또는 문자열의 문자 수(바이트 수가 아님)를 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeParamIndex">
      <summary>COM에 있는 size_is와 같이 배열 요소의 수가 들어 있고 0부터 시작하는 매개 변수를 나타냅니다.</summary>
    </member>
    <member name="P:System.Runtime.InteropServices.MarshalAsAttribute.Value">
      <summary>데이터를 마샬링할 <see cref="T:System.Runtime.InteropServices.UnmanagedType" /> 값을 가져옵니다.</summary>
      <returns>데이터를 마샬링할 <see cref="T:System.Runtime.InteropServices.UnmanagedType" /> 값입니다.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.MarshalDirectiveException">
      <summary>지원되지 않는 <see cref="T:System.Runtime.InteropServices.MarshalAsAttribute" />가 나타날 때 마샬러에서 throw하는 예외입니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.MarshalDirectiveException.#ctor">
      <summary>기본 속성을 사용하여 MarshalDirectiveException 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.MarshalDirectiveException.#ctor(System.String)">
      <summary>지정된 오류 메시지를 사용하여 MarshalDirectiveException 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">예외의 원인을 지정하는 오류 메시지입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.MarshalDirectiveException.#ctor(System.String,System.Exception)">
      <summary>지정된 오류 메시지와 해당 예외의 근본 원인인 내부 예외에 대한 참조를 사용하여 <see cref="T:System.Runtime.InteropServices.MarshalDirectiveException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">예외에 대한 이유를 설명하는 오류 메시지입니다. </param>
      <param name="inner">현재 예외의 원인이 되는 예외입니다.<paramref name="inner" /> 매개 변수가 null이 아니면 현재 예외는 내부 예외를 처리하는 catch 블록에서 발생합니다.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.OptionalAttribute">
      <summary>매개 변수가 선택적임을 나타냅니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.OptionalAttribute.#ctor">
      <summary>기본값을 사용하여 OptionalAttribute 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.PreserveSigAttribute">
      <summary>COM interop 호출 중에 발생하는 HRESULT 또는 retval 시그니처 변환을 생략해야 함을 나타냅니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.PreserveSigAttribute.#ctor">
      <summary>
        <see cref="T:System.Runtime.InteropServices.PreserveSigAttribute" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.SafeArrayRankMismatchException">
      <summary>들어오는 SAFEARRAY의 순위가 관리되는 시그니처에서 지정된 순위와 일치하지 않을 경우 발생한 예외입니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayRankMismatchException.#ctor">
      <summary>기본값으로 SafeArrayTypeMismatchException 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayRankMismatchException.#ctor(System.String)">
      <summary>지정된 메시지를 사용하여 SafeArrayRankMismatchException 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">예외에 대한 이유를 나타내는 메시지입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayRankMismatchException.#ctor(System.String,System.Exception)">
      <summary>지정된 오류 메시지와 해당 예외의 근본 원인인 내부 예외에 대한 참조를 사용하여 <see cref="T:System.Runtime.InteropServices.SafeArrayRankMismatchException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">예외에 대한 이유를 설명하는 오류 메시지입니다. </param>
      <param name="inner">현재 예외의 원인이 되는 예외입니다.<paramref name="inner" /> 매개 변수가 null이 아니면 현재 예외는 내부 예외를 처리하는 catch 블록에서 발생합니다.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.SafeArrayTypeMismatchException">
      <summary>들어오는 SAFEARRAY의 형식이 관리되는 시그니처에서 지정된 형식과 일치하지 않을 경우 발생한 예외입니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayTypeMismatchException.#ctor">
      <summary>기본값으로 SafeArrayTypeMismatchException 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayTypeMismatchException.#ctor(System.String)">
      <summary>지정된 메시지를 사용하여 SafeArrayTypeMismatchException 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">예외에 대한 이유를 나타내는 메시지입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeArrayTypeMismatchException.#ctor(System.String,System.Exception)">
      <summary>지정된 오류 메시지와 해당 예외의 근본 원인인 내부 예외에 대한 참조를 사용하여 <see cref="T:System.Runtime.InteropServices.SafeArrayTypeMismatchException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">예외에 대한 이유를 설명하는 오류 메시지입니다. </param>
      <param name="inner">현재 예외의 원인이 되는 예외입니다.<paramref name="inner" /> 매개 변수가 null이 아니면 현재 예외는 내부 예외를 처리하는 catch 블록에서 발생합니다.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.SafeBuffer">
      <summary>읽기 및 쓰기에 사용할 수 있는 제어되는 메모리 버퍼를 제공합니다.제어되는 버퍼 외부의 메모리에 액세스(언더런 및 오버런)하려고 하면 예외가 발생됩니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.#ctor(System.Boolean)">
      <summary>
        <see cref="T:System.Runtime.InteropServices.SafeBuffer" /> 클래스의 새 인스턴스를 만들고 버퍼 핸들이 안정적으로 해제될지 여부를 지정합니다. </summary>
      <param name="ownsHandle">종료 단계에서 핸들을 안정적으로 해제하려면 true이고, 안정적으로 해제할 수 없게 하려면 false(권장되지 않음)입니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.AcquirePointer(System.Byte*@)">
      <summary>
        <see cref="T:System.Runtime.InteropServices.SafeBuffer" /> 개체에서 메모리 블록에 대한 포인터를 가져옵니다.</summary>
      <param name="pointer">
        <see cref="T:System.Runtime.InteropServices.SafeBuffer" /> 개체 내부에서 포인터를 받기 위해 참조에서 전달하는 바이트 포인터입니다.이 메서드를 호출하기 전에 이 포인터를 null로 설정해야 합니다.</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" /> 메서드가 호출되지 않았습니다. </exception>
    </member>
    <member name="P:System.Runtime.InteropServices.SafeBuffer.ByteLength">
      <summary>버퍼의 크기(바이트)를 가져옵니다.</summary>
      <returns>메모리 버퍼의 바이트 수입니다.</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" /> 메서드가 호출되지 않았습니다.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.Initialize``1(System.UInt32)">
      <summary>값 형식의 수를 지정하여 메모리 영역의 할당 크기를 정의합니다.<see cref="T:System.Runtime.InteropServices.SafeBuffer" /> 인스턴스를 사용하려면 먼저 이 메서드를 호출해야 합니다.</summary>
      <param name="numElements">메모리를 할당할 값 형식의 요소 수입니다.</param>
      <typeparam name="T">메모리를 할당할 값 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="numElements" />가 0보다 작은 경우또는<paramref name="numElements" />는 각 요소의 크기를 곱한 것으로 사용 가능한 주소 공간보다 큽니다.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.Initialize(System.UInt32,System.UInt32)">
      <summary>지정된 요소 수 및 요소 크기를 사용하여 메모리 버퍼의 할당 크기를 지정합니다.<see cref="T:System.Runtime.InteropServices.SafeBuffer" /> 인스턴스를 사용하려면 먼저 이 메서드를 호출해야 합니다.</summary>
      <param name="numElements">버퍼의 요소 수입니다.</param>
      <param name="sizeOfEachElement">버퍼의 각 요소 크기입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="numElements" />가 0보다 작은 경우 또는<paramref name="sizeOfEachElement" />가 0보다 작은 경우또는<paramref name="numElements" />에 <paramref name="sizeOfEachElement" />를 곱한 크기가 사용 가능한 주소 공간보다 큽니다.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.Initialize(System.UInt64)">
      <summary>메모리 영역의 할당 크기(바이트)를 정의합니다.<see cref="T:System.Runtime.InteropServices.SafeBuffer" /> 인스턴스를 사용하려면 먼저 이 메서드를 호출해야 합니다.</summary>
      <param name="numBytes">버퍼의 바이트 수입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="numBytes" />가 0보다 작은 경우또는<paramref name="numBytes" />가 사용 가능한 주소 공간보다 큽니다.</exception>
    </member>
    <member name="P:System.Runtime.InteropServices.SafeBuffer.IsInvalid"></member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.Read``1(System.UInt64)">
      <summary>메모리의 지정된 오프셋 위치에서 값 형식을 읽습니다.</summary>
      <returns>메모리에서 읽어온 값 형식입니다.</returns>
      <param name="byteOffset">값 형식을 읽어올 위치입니다.정렬 문제를 고려해야 할 수 있습니다.</param>
      <typeparam name="T">읽을 값 형식입니다.</typeparam>
      <exception cref="T:System.InvalidOperationException">
        <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" /> 메서드가 호출되지 않았습니다.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.ReadArray``1(System.UInt64,``0[],System.Int32,System.Int32)">
      <summary>메모리의 오프셋 시작 위치에서 지정된 수의 값 형식을 읽어서 이를 배열의 인덱스 시작 위치에 씁니다. </summary>
      <param name="byteOffset">읽기를 시작할 위치입니다.</param>
      <param name="array">쓸 출력 배열입니다.</param>
      <param name="index">출력 배열에서 쓰기를 시작할 위치입니다.</param>
      <param name="count">입력 배열에서 읽어서 출력 배열에 쓸 값 형식의 수입니다.</param>
      <typeparam name="T">읽을 값 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 0보다 작은 경우또는<paramref name="count" />가 0보다 작은 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />가 null입니다.</exception>
      <exception cref="T:System.ArgumentException">배열에서 인덱스를 뺀 길이가 <paramref name="count" />보다 작은 경우</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" /> 메서드가 호출되지 않았습니다.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.ReleasePointer">
      <summary>
        <see cref="M:System.Runtime.InteropServices.SafeBuffer.AcquirePointer(System.Byte*@)" /> 메서드에서 가져온 포인터를 해제합니다.</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" /> 메서드가 호출되지 않았습니다.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.Write``1(System.UInt64,``0)">
      <summary>메모리의 지정된 위치에 값 형식을 씁니다.</summary>
      <param name="byteOffset">쓰기를 시작할 위치입니다.정렬 문제를 고려해야 할 수 있습니다.</param>
      <param name="value">쓸 값입니다.</param>
      <typeparam name="T">쓸 값 형식입니다.</typeparam>
      <exception cref="T:System.InvalidOperationException">
        <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" /> 메서드가 호출되지 않았습니다.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.SafeBuffer.WriteArray``1(System.UInt64,``0[],System.Int32,System.Int32)">
      <summary>입력 배열의 지정된 위치에서 시작하여 바이트를 읽어 메모리 위치에 지정된 수의 값 형식을 씁니다.</summary>
      <param name="byteOffset">메모리에서 쓸 위치입니다.</param>
      <param name="array">입력 배열입니다.</param>
      <param name="index">배열에서 읽기를 시작할 오프셋입니다.</param>
      <param name="count">쓸 값 형식의 수입니다.</param>
      <typeparam name="T">쓸 값 형식입니다.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />가 null입니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 또는 <paramref name="count" />가 0보다 작은 경우</exception>
      <exception cref="T:System.ArgumentException">입력 배열에서 <paramref name="index" />를 뺀 길이가 <paramref name="count" />보다 작습니다.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="Overload:System.Runtime.InteropServices.SafeBuffer.Initialize" /> 메서드가 호출되지 않았습니다.</exception>
    </member>
    <member name="T:System.Runtime.InteropServices.SEHException">
      <summary>SEH(구조적 예외 처리기) 오류를 나타냅니다. </summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SEHException.#ctor">
      <summary>
        <see cref="T:System.Runtime.InteropServices.SEHException" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
    </member>
    <member name="M:System.Runtime.InteropServices.SEHException.#ctor(System.String)">
      <summary>지정된 메시지를 사용하여 <see cref="T:System.Runtime.InteropServices.SEHException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">예외에 대한 이유를 나타내는 메시지입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.SEHException.#ctor(System.String,System.Exception)">
      <summary>지정된 오류 메시지와 해당 예외의 근본 원인인 내부 예외에 대한 참조를 사용하여 <see cref="T:System.Runtime.InteropServices.SEHException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">예외에 대한 이유를 설명하는 오류 메시지입니다. </param>
      <param name="inner">현재 예외의 원인이 되는 예외입니다.<paramref name="inner" /> 매개 변수가 null이 아니면 현재 예외는 내부 예외를 처리하는 catch 블록에서 발생합니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.SEHException.CanResume">
      <summary>예외를 복구할 수 있는지 여부와 예외가 throw된 위치부터 코드를 계속 진행할 수 있는지 여부를 나타냅니다.</summary>
      <returns>다시 시작할 수 있는 예외가 아직 구현되지 않았으므로 항상 false입니다.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.TypeIdentifierAttribute">
      <summary>동일 형식에 대한 지원을 제공합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.TypeIdentifierAttribute.#ctor">
      <summary>
        <see cref="T:System.Runtime.InteropServices.TypeIdentifierAttribute" /> 클래스의 새 인스턴스를 만듭니다. </summary>
    </member>
    <member name="M:System.Runtime.InteropServices.TypeIdentifierAttribute.#ctor(System.String,System.String)">
      <summary>지정된 범위와 식별자를 사용하여 <see cref="T:System.Runtime.InteropServices.TypeIdentifierAttribute" /> 클래스의 새 인스턴스를 만듭니다. </summary>
      <param name="scope">첫 번째 동일 형식 문자열입니다.</param>
      <param name="identifier">두 번째 동일 형식 문자열입니다.</param>
    </member>
    <member name="P:System.Runtime.InteropServices.TypeIdentifierAttribute.Identifier">
      <summary>
        <see cref="M:System.Runtime.InteropServices.TypeIdentifierAttribute.#ctor(System.String,System.String)" /> 생성자에 전달된 <paramref name="identifier" /> 매개 변수의 값을 가져옵니다.</summary>
      <returns>생성자의 <paramref name="identifier" /> 매개 변수 값입니다.</returns>
    </member>
    <member name="P:System.Runtime.InteropServices.TypeIdentifierAttribute.Scope">
      <summary>
        <see cref="M:System.Runtime.InteropServices.TypeIdentifierAttribute.#ctor(System.String,System.String)" /> 생성자에 전달된 <paramref name="scope" /> 매개 변수의 값을 가져옵니다.</summary>
      <returns>생성자의 <paramref name="scope" /> 매개 변수 값입니다.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.UnknownWrapper">
      <summary>마샬러가 VT_UNKNOWN으로 마샬링할 개체를 래핑합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.UnknownWrapper.#ctor(System.Object)">
      <summary>래핑할 개체를 사용하여 <see cref="T:System.Runtime.InteropServices.UnknownWrapper" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="obj">래핑 중인 개체입니다. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.UnknownWrapper.WrappedObject">
      <summary>이 래퍼에 포함된 개체를 가져옵니다.</summary>
      <returns>래핑된 개체입니다.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute">
      <summary>관리되지 않는 함수 포인터로 비관리 코드에 전달하거나 비관리 코드로부터 전달된 대리자 시그니처의 마샬링 동작을 제어합니다.이 클래스는 상속될 수 없습니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.#ctor(System.Runtime.InteropServices.CallingConvention)">
      <summary>지정된 호출 규칙을 사용하여 <see cref="T:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
      <param name="callingConvention">지정된 호출 규칙입니다.</param>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.BestFitMapping">
      <summary>유니코드 문자를 ANSI 문자로 변환할 때 가장 적합한 매핑 동작을 활성화 또는 비활성화합니다.</summary>
    </member>
    <member name="P:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.CallingConvention">
      <summary>호출 규칙의 값을 가져옵니다.</summary>
      <returns>
        <see cref="M:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.#ctor(System.Runtime.InteropServices.CallingConvention)" /> 생성자에 지정된 호출 규칙의 값입니다.</returns>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.CharSet">
      <summary>문자열 매개 변수를 메서드로 마샬링하는 방법을 지정하고 이름 변환을 제어합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.SetLastError">
      <summary>특성 사용 메서드에서 반환하기 전에 호출 수신자가 Win32 API SetLastError를 호출할지 여부를 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedFunctionPointerAttribute.ThrowOnUnmappableChar">
      <summary>ANSI "?" 문자로 변환되는 매핑할 수 없는 유니코드 문자가 나오면 예외를 throw하거나 throw하지 않습니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.UnmanagedType">
      <summary>매개 변수나 필드를 비관리 코드로 마샬링하는 방법을 식별합니다. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.AnsiBStr">
      <summary>싱글 바이트 길이 접두사가 있는 ANSI 문자열입니다.<see cref="T:System.String" /> 데이터 형식에 이 멤버를 사용할 수 있습니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.AsAny">
      <summary>런타임에서 개체의 형식을 결정하고 해당 형식으로 개체를 마샬링하는 동적 형식입니다.이 메서드는 플랫폼 호출 메서드에만 유효합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.Bool">
      <summary>4바이트 부울 값(true != 0, false = 0)입니다.Win32 BOOL 형식입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.BStr">
      <summary>더블바이트 길이 접두사가 있는 유니코드 문자열입니다.COM의 기본 문자열인 이 멤버는 <see cref="T:System.String" /> 데이터 형식에 사용할 수 있습니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.ByValArray">
      <summary>
        <see cref="P:System.Runtime.InteropServices.MarshalAsAttribute.Value" /> 속성이 ByValArray로 설정된 경우, <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeConst" /> 필드는 배열에 있는 요소의 수를 나타내도록 설정되어야 합니다.<see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.ArraySubType" /> 필드는 문자열 형식 사이에서 구별을 지을 필요가 있는 경우 배열 요소의 <see cref="T:System.Runtime.InteropServices.UnmanagedType" />을 포함할 수도 있습니다.이 <see cref="T:System.Runtime.InteropServices.UnmanagedType" />은 구조체에서 필드로 나타나는 요소가 있는 배열에만 사용할 수 있습니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.ByValTStr">
      <summary>구조체 내에 나타나는 인라인 고정 길이 문자 배열에 사용됩니다.포함하는 구조체에 적용된 <see cref="T:System.Runtime.InteropServices.StructLayoutAttribute" /> 특성의 <see cref="T:System.Runtime.InteropServices.CharSet" /> 인수에 의해 결정된 <see cref="F:System.Runtime.InteropServices.UnmanagedType.ByValTStr" />와 함께 사용된 문자 형식입니다.항상 <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeConst" /> 필드를 사용하여 배열의 크기를 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.Currency">
      <summary>통화 형식입니다.Decimal 대신 COM 통화 형식으로 10진 값을 마샬링하기 위해 <see cref="T:System.Decimal" />에서 사용됩니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.Error">
      <summary>
        <see cref="F:System.Runtime.InteropServices.UnmanagedType.I4" /> 또는 <see cref="F:System.Runtime.InteropServices.UnmanagedType.U4" />와 관련되어 있고 매개 변수가 내보낸 형식 라이브러리에 있는 HRESULT로 내보내지도록 하는 네이티브 형식입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.FunctionPtr">
      <summary>C 스타일 함수 포인터로 사용할 수 있는 정수입니다.이 멤버는 <see cref="T:System.Delegate" /> 데이터 형식 또는 <see cref="T:System.Delegate" />에서 상속된 형식에 사용할 수 있습니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.HString">
      <summary>Windows 런타임 문자열입니다.<see cref="T:System.String" /> 데이터 형식에 이 멤버를 사용할 수 있습니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.I1">
      <summary>부호 있는 1바이트 정수입니다.이 멤버를 사용하여 부울 값을 1바이트 C 스타일 bool(true = 1, false = 0)로 변환할 수 있습니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.I2">
      <summary>부호 있는 2바이트 정수입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.I4">
      <summary>부호 있는 4바이트 정수입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.I8">
      <summary>부호 있는 8바이트 정수입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.IDispatch">
      <summary>COM IDispatch 포인터(Microsoft Visual Basic 6.0의 Object)입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.IInspectable">
      <summary>Windows 런타임 인터페이스 포인터입니다.<see cref="T:System.Object" /> 데이터 형식에 이 멤버를 사용할 수 있습니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.Interface">
      <summary>COM 인터페이스 포인터.인터페이스의 <see cref="T:System.Guid" />는 클래스 메타데이터에서 얻을 수 있습니다.정확한 인터페이스 형식 또는 클래스에 적용할 경우 기본 인터페이스 형식을 지정하려면 이 멤버를 사용하세요.이 멤버를 <see cref="T:System.Object" /> 데이터 형식에 적용하면 <see cref="F:System.Runtime.InteropServices.UnmanagedType.IUnknown" />과 같은 동작이 생성됩니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.IUnknown">
      <summary>COM IUnknown 포인터<see cref="T:System.Object" /> 데이터 형식에 이 멤버를 사용할 수 있습니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.LPArray">
      <summary>C 스타일 배열의 첫 번째 요소에 대한 포인터입니다.관리되는 코드에서 관리되지 않는 코드로 마샬링하면 배열의 길이는 관리되는 배열의 길이에 의해 결정됩니다.관리되지 않는 코드에서 관리되는 코드로 마샬링할 때 배열의 길이는 문자열 형식 사이에서 구별을 지을 필요가 있는 경우 배열 내 요소의 관리되지 않는 형식이 뒤에 나올 수도 <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeConst" /> 및 <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.SizeParamIndex" /> 필드를 통해 결정됩니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.LPStr">
      <summary>싱글 바이트 null로 끝나는 ANSI 문자열입니다.<see cref="T:System.String" /> 및 <see cref="T:System.Text.StringBuilder" /> 데이터 형식에 이 멤버를 사용할 수 있습니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.LPStruct">
      <summary>관리되는 서식이 지정된 클래스를 마샬링할 때 사용하는 C 스타일 구조체에 대한 포인터입니다.이 메서드는 플랫폼 호출 메서드에만 유효합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.LPTStr">
      <summary>플랫폼 종속 문자열, 즉 Windows 98에서는 ANSI, Windows NT 및 Windows XP에서는 유니코드입니다.LPTStr 형식의 문자열을 내보낼 수 없으므로 이 값은 플랫폼 호출에 대해서만 지원되고 COM interop에 대해서는 지원되지 않습니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.LPWStr">
      <summary>2바이트 null로 끝나는 유니코드 문자열입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.R4">
      <summary>4바이트 부동 소수점 수입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.R8">
      <summary>8바이트 부동 소수점 수입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.SafeArray">
      <summary>형식, 차수, 관련된 배열 데이터의 범위를 전달하는 자동 기술 배열인 SafeArray입니다.이 멤버를 <see cref="F:System.Runtime.InteropServices.MarshalAsAttribute.SafeArraySubType" /> 필드와 함께 사용하여 기본 요소 형식을 재정의할 수 있습니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.Struct">
      <summary>형식이 지정된 관리되는 클래스와 값 형식을 마샬링하는 데 사용되는 VARIANT입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.SysInt">
      <summary>플랫폼에 종속적인 부호 있는 정수로, 32비트 Windows의 경우 4바이트이고 64비트 Windows의 경우 8바이트입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.SysUInt">
      <summary>플랫폼에 종속적인 부호 없는 정수로, 32비트 Windows의 경우 4바이트이고 64비트 Windows의 경우 8바이트입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.TBStr">
      <summary>길이 접두사가 있는 플랫폼에 종속적인 char 문자열입니다(Windows 98의 경우 ANSI, Windows NT의 경우 유니코드).BSTR과와 비슷한 이 멤버는 거의 사용하지 않습니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.U1">
      <summary>부호 없는 1바이트 정수입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.U2">
      <summary>부호 없는 2바이트 정수입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.U4">
      <summary>부호 없는 4바이트 정수입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.U8">
      <summary>부호 없는 8바이트 정수입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.VariantBool">
      <summary>2바이트 OLE 정의 VARIANT_BOOL 형식(true = -1, false = 0)입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.UnmanagedType.VBByRefStr">
      <summary>Visual Basic에서 비관리 코드의 문자열을 변경하고 결과를 관리 코드에 반영되도록 하는 데 사용할 수 있는 값입니다.이 값은 플랫폼 호출에만 지원됩니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.VarEnum">
      <summary>배열이 관리 코드에서 비관리 코드로 <see cref="F:System.Runtime.InteropServices.UnmanagedType.SafeArray" />로 마샬링될 때 배열 요소가 마샬링될 방법을 나타냅니다. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_ARRAY">
      <summary>SAFEARRAY 포인터를 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_BLOB">
      <summary>길이 접두사가 있는 바이트를 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_BLOB_OBJECT">
      <summary>blob이 개체를 포함하고 있다는 것을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_BOOL">
      <summary>부울 값을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_BSTR">
      <summary>BSTR 문자열을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_BYREF">
      <summary>값이 참조임을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_CARRAY">
      <summary>C 스타일 배열을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_CF">
      <summary>클립보드 형식을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_CLSID">
      <summary>클래스 ID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_CY">
      <summary>통화 값을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_DATE">
      <summary>DATE 값을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_DECIMAL">
      <summary>decimal 값을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_DISPATCH">
      <summary>IDispatch 포인터를 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_EMPTY">
      <summary>값이 지정되지 않았음을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_ERROR">
      <summary>SCODE를 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_FILETIME">
      <summary>FILETIME 값을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_HRESULT">
      <summary>HRESULT를 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_I1">
      <summary>char 값을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_I2">
      <summary>short 정수를 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_I4">
      <summary>long 정수를 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_I8">
      <summary>64비트 정수를 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_INT">
      <summary>정수 값을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_LPSTR">
      <summary>null로 끝나는 문자열을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_LPWSTR">
      <summary>null로 끝나는 와이드 문자열을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_NULL">
      <summary>SQL의 null 값과 비슷한 null 값을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_PTR">
      <summary>포인터 형식을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_R4">
      <summary>float 값을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_R8">
      <summary>double 값을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_RECORD">
      <summary>사용자 정의된 형식을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_SAFEARRAY">
      <summary>SAFEARRAY를 나타냅니다.VARIANT에서는 유효하지 않습니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_STORAGE">
      <summary>그 뒤에 저장소 이름이 나온다는 것을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_STORED_OBJECT">
      <summary>저장소가 개체를 포함하고 있음을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_STREAM">
      <summary>그 뒤에 스트림 이름이 나온다는 것을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_STREAMED_OBJECT">
      <summary>스트림이 개체를 포함하고 있음을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UI1">
      <summary>byte를 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UI2">
      <summary>unsignedshort를 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UI4">
      <summary>unsignedlong을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UI8">
      <summary>부호 없는 64비트 정수를 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UINT">
      <summary>unsigned 정수 값을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_UNKNOWN">
      <summary>IUnknown 포인터를 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_USERDEFINED">
      <summary>사용자 정의된 형식을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_VARIANT">
      <summary>VARIANT far 포인터를 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_VECTOR">
      <summary>셀 수 있는 단순한 배열을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.VarEnum.VT_VOID">
      <summary>C 스타일 void를 나타냅니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.VariantWrapper">
      <summary>VT_VARIANT | VT_BYREF 형식의 데이터를 관리 코드에서 비관리 코드로 마샬링합니다.이 클래스는 상속될 수 없습니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.VariantWrapper.#ctor(System.Object)">
      <summary>지정된 <see cref="T:System.Object" /> 매개 변수에 대해 <see cref="T:System.Runtime.InteropServices.VariantWrapper" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="obj">마샬링할 개체입니다. </param>
    </member>
    <member name="P:System.Runtime.InteropServices.VariantWrapper.WrappedObject">
      <summary>
        <see cref="T:System.Runtime.InteropServices.VariantWrapper" /> 개체로 래핑된 개체를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Runtime.InteropServices.VariantWrapper" /> 개체로 래핑된 개체입니다.</returns>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ADVF">
      <summary>advise 싱크나 개체와의 캐시 연결을 설정할 때 요청되는 동작을 지정합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVF_DATAONSTOP">
      <summary>데이터 advise 연결의 경우, 데이터에 대한 액세스 가능성을 보장합니다. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVF_NODATA">
      <summary>데이터 advise 연결(<see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.DAdvise(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.ADVF,System.Runtime.InteropServices.ComTypes.IAdviseSink,System.Int32@)" /> 또는 <see cref="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Advise(System.Object,System.Int32@)" />)의 경우, 이 플래그는 <see cref="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnDataChange(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.STGMEDIUM@)" />를 호출할 때 데이터 개체에서 데이터를 보내지 않도록 요청합니다. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVF_ONLYONCE">
      <summary>연결을 삭제하기 전에 개체에서 하나의 변경 알림만 만들거나 캐시를 업데이트하도록 요청합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVF_PRIMEFIRST">
      <summary>데이터가 변경될 때까지 기다리지 않고 개체에서 <see cref="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnDataChange(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.STGMEDIUM@)" />에 대한 초기 호출(데이터 또는 뷰 advise 연결의 경우) 또는 캐시 업데이트(캐시 연결의 경우)를 수행하도록 요청합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVFCACHE_FORCEBUILTIN">
      <summary>이 값은 DLL 개체 응용 프로그램과 해당 개체의 그리기를 수행하는 개체 처리기에서 사용됩니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVFCACHE_NOHANDLER">
      <summary>보다 일반적으로 사용되는 <see cref="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVFCACHE_FORCEBUILTIN" />의 동의어입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ADVF.ADVFCACHE_ONSAVE">
      <summary>캐시 연결의 경우, 이 플래그는 캐시가 들어 있는 개체가 저장될 때만 캐시된 표현을 업데이트합니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.BIND_OPTS">
      <summary>모니커 바인딩 작업을 하는 동안 사용된 매개 변수를 저장합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BIND_OPTS.cbStruct">
      <summary>BIND_OPTS 구조체의 크기를 바이트 단위로 지정합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BIND_OPTS.dwTickCountDeadline">
      <summary>호출자가 바인딩 작업을 완료하도록 지정한 시간(GetTickCount 함수가 반환하는 밀리초 단위의 시간)을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BIND_OPTS.grfFlags">
      <summary>모니커 바인딩 작업의 여러 측면을 제어합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BIND_OPTS.grfMode">
      <summary>모니커가 식별한 개체가 들어 있는 파일을 열 때 사용해야 하는 플래그를 나타냅니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.BINDPTR">
      <summary>바인딩 대상 <see cref="T:System.Runtime.InteropServices.FUNCDESC" /> 구조체, <see cref="T:System.Runtime.InteropServices.VARDESC" /> 구조체 또는 ITypeComp 인터페이스에 대한 포인터를 포함합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BINDPTR.lpfuncdesc">
      <summary>
        <see cref="T:System.Runtime.InteropServices.FUNCDESC" /> 구조체에 대한 포인터를 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BINDPTR.lptcomp">
      <summary>
        <see cref="T:System.Runtime.InteropServices.ComTypes.ITypeComp" /> 인터페이스에 대한 포인터를 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.BINDPTR.lpvardesc">
      <summary>
        <see cref="T:System.Runtime.InteropServices.VARDESC" /> 구조체에 대한 포인터를 나타냅니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.CALLCONV">
      <summary>METHODDATA 구조체에 설명된 메서드가 사용하는 호출 규칙을 식별합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_CDECL">
      <summary>CDECL(C 선언) 호출 규칙을 메서드에 사용해야 함을 나타냅니다. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_MACPASCAL">
      <summary>MACPASCAL(Macintosh Pascal) 호출 규칙을 메서드에 사용해야 함을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_MAX">
      <summary>
        <see cref="T:System.Runtime.InteropServices.ComTypes.CALLCONV" /> 열거형의 끝을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_MPWCDECL">
      <summary>MPW(Macintosh Programmers' Workbench) CDECL 호출 규칙을 메서드에 사용해야 함을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_MPWPASCAL">
      <summary>MPW(Macintosh Programmers' Workbench) PASCAL 호출 규칙을 메서드에 사용해야 함을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_MSCPASCAL">
      <summary>MSCPASCAL(MSC Pascal) 호출 규칙을 메서드에 사용해야 함을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_PASCAL">
      <summary>파스칼 호출 규칙을 메서드에 사용해야 함을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_RESERVED">
      <summary>이 값은 나중에 사용되도록 예약되어 있습니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_STDCALL">
      <summary>STDCALL(표준 호출 규칙)을 메서드에 사용해야 함을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CALLCONV.CC_SYSCALL">
      <summary>표준 SYSCALL 호출 규칙을 메서드에 사용해야 함을 나타냅니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.CONNECTDATA">
      <summary>해당 연결 지점에 대한 기존의 연결을 설명합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CONNECTDATA.dwCookie">
      <summary>
        <see cref="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Advise(System.Object,System.Int32@)" />에 대한 호출에서 반환되는 연결 토큰을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.CONNECTDATA.pUnk">
      <summary>연결된 advise 싱크의 IUnknown 인터페이스에 대한 포인터를 나타냅니다.CONNECTDATA 구조체가 더 이상 필요하지 않으면 호출자는 이 포인터에서 IUnknown::Release를 호출해야 합니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.DATADIR">
      <summary>
        <see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.EnumFormatEtc(System.Runtime.InteropServices.ComTypes.DATADIR)" /> 메서드의 <paramref name="dwDirection" /> 매개 변수에서 설정되는 데이터 흐름의 방향을 지정합니다.이 메서드는 결과 열거자가 열거할 수 있는 형식을 결정합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DATADIR.DATADIR_GET">
      <summary>
        <see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.EnumFormatEtc(System.Runtime.InteropServices.ComTypes.DATADIR)" />가 <see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.GetData(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.STGMEDIUM@)" />에 지정할 수 있는 형식에 대한 열거자를 제공하도록 요청합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DATADIR.DATADIR_SET">
      <summary>
        <see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.EnumFormatEtc(System.Runtime.InteropServices.ComTypes.DATADIR)" />가 <see cref="M:System.Runtime.InteropServices.ComTypes.IDataObject.SetData(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.STGMEDIUM@,System.Boolean)" />에 지정할 수 있는 형식에 대한 열거자를 제공하도록 요청합니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.DESCKIND">
      <summary>바인딩되는 형식 설명을 식별합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_FUNCDESC">
      <summary>
        <see cref="T:System.Runtime.InteropServices.FUNCDESC" /> 구조체가 반환되었음을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_IMPLICITAPPOBJ">
      <summary>IMPLICITAPPOBJ가 반환되었음을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_MAX">
      <summary>열거형 마커의 끝을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_NONE">
      <summary>일치하는 항목을 찾을 수 없음을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_TYPECOMP">
      <summary>TYPECOMP가 반환되었음을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DESCKIND.DESCKIND_VARDESC">
      <summary>VARDESC가 반환되었음을 나타냅니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.DISPPARAMS">
      <summary>IDispatch::Invoke에 의해 메서드나 속성에 전달되는 인수를 포함합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DISPPARAMS.cArgs">
      <summary>인수의 수를 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DISPPARAMS.cNamedArgs">
      <summary>명명된 인수의 수를 나타냅니다. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DISPPARAMS.rgdispidNamedArgs">
      <summary>명명된 인수의 디스패치 ID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DISPPARAMS.rgvarg">
      <summary>인수 배열에 대한 참조를 나타냅니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.DVASPECT">
      <summary>데이터를 그리거나 가져올 때 개체의 원하는 데이터 또는 뷰 모양을 지정합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DVASPECT.DVASPECT_CONTENT">
      <summary>개체를 컨테이너 내에 포함 개체로 표시하는 데 사용할 수 있는 개체 표현입니다.일반적으로 복합 문서 개체에 대해 이 값을 지정합니다.이 표시 방식은 화면 또는 프린터에 대해 제공될 수 있습니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DVASPECT.DVASPECT_DOCPRINT">
      <summary>파일 메뉴에서 인쇄 명령을 사용하여 프린터로 인쇄할 때와 같은 방식으로 화면에 개체를 표현합니다.설명된 데이터가 페이지 순서를 나타낼 수도 있습니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DVASPECT.DVASPECT_ICON">
      <summary>개체의 아이콘 표현입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.DVASPECT.DVASPECT_THUMBNAIL">
      <summary>해당 개체를 검색 도구에 표시할 때 사용할 수 있는 개체의 축소판 표현입니다.축소판은 메타파일에 래핑될 수 있는 약 120x120픽셀 크기의, 16색(권장) 장치 독립적 비트맵입니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ELEMDESC">
      <summary>변수, 함수 또는 함수 매개 변수에 대한 형식 설명과 프로세스 전달 정보를 포함합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ELEMDESC.desc">
      <summary>요소에 대한 정보를 포함합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ELEMDESC.tdesc">
      <summary>요소의 형식을 식별합니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ELEMDESC.DESCUNION">
      <summary>요소에 대한 정보를 포함합니다. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ELEMDESC.DESCUNION.idldesc">
      <summary>요소 원격화 정보를 포함합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.ELEMDESC.DESCUNION.paramdesc">
      <summary>매개 변수에 대한 정보를 포함합니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.EXCEPINFO">
      <summary>IDispatch::Invoke에서 발생하는 예외를 설명합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.bstrDescription">
      <summary>고객을 위한 오류를 설명합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.bstrHelpFile">
      <summary>오류에 대한 자세한 정보를 포함하는 도움말 파일의 정규화된 드라이브, 경로 및 파일 이름을 포함합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.bstrSource">
      <summary>예외 소스의 이름을 나타냅니다.일반적으로 이 이름은 응용 프로그램 이름입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.dwHelpContext">
      <summary>도움말 파일 내에서 해당 항목의 도움말 컨텍스트 ID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.pfnDeferredFillIn">
      <summary>
        <see cref="T:System.Runtime.InteropServices.EXCEPINFO" /> 구조체를 인수로 사용하고 HRESULT 값을 반환하는 함수에 대한 포인터를 나타냅니다.지연된 채우기가 적절하지 않으면 이 필드는 null로 설정됩니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.pvReserved">
      <summary>이 필드는 예약되어 있으므로 null로 설정해야 합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.scode">
      <summary>오류를 설명하는 반환 값입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.wCode">
      <summary>오류를 식별하는 오류 코드를 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.EXCEPINFO.wReserved">
      <summary>이 필드는 예약되어 있으므로 0으로 설정해야 합니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.FILETIME">
      <summary>1601년 1월 1일 이후 경과한 100나노초 간격의 수를 나타냅니다.이 구조체는 64비트 값입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FILETIME.dwHighDateTime">
      <summary>FILETIME의 상위 32비트를 지정합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FILETIME.dwLowDateTime">
      <summary>FILETIME의 하위 32비트를 지정합니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.FORMATETC">
      <summary>일반화된 클립보드 형식을 나타냅니다. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FORMATETC.cfFormat">
      <summary>원하는 특정 클립보드 형식을 지정합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FORMATETC.dwAspect">
      <summary>렌더링 상세 수준을 나타내는 <see cref="T:System.Runtime.InteropServices.ComTypes.DVASPECT" /> 열거형 상수 중 하나를 지정합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FORMATETC.lindex">
      <summary>데이터를 여러 페이지로 분할해야 할 때의 모양을 지정합니다. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FORMATETC.ptd">
      <summary>데이터가 작성되는 대상 장치에 대한 정보가 들어 있는 DVTARGETDEVICE 구조체에 대한 포인터를 지정합니다. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FORMATETC.tymed">
      <summary>개체의 데이터를 전송하는 데 사용되는 저장 미디어 형식을 나타내는 <see cref="T:System.Runtime.InteropServices.ComTypes.TYMED" /> 열거형 상수 중 하나를 지정합니다. </summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.FUNCDESC">
      <summary>함수 설명을 정의합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.callconv">
      <summary>함수의 호출 규칙을 지정합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.cParams">
      <summary>총 매개 변수 수를 계산합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.cParamsOpt">
      <summary>선택적인 매개 변수를 계산합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.cScodes">
      <summary>허용된 반환 값을 계산합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.elemdescFunc">
      <summary>함수의 반환 형식을 포함합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.funckind">
      <summary>함수가 가상 함수인지 정적 함수인지 또는 디스패치 전용인지 지정합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.invkind">
      <summary>속성 함수의 형식을 지정합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.lprgelemdescParam">
      <summary>
        <see cref="F:System.Runtime.InteropServices.FUNCDESC.cParams" />의 크기를 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.lprgscode">
      <summary>16비트 시스템에서 함수가 반환할 수 있는 오류 수를 저장합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.memid">
      <summary>함수 멤버 ID를 식별합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.oVft">
      <summary>
        <see cref="F:System.Runtime.InteropServices.FUNCKIND.FUNC_VIRTUAL" />의 VTBL에 있는 오프셋을 지정합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCDESC.wFuncFlags">
      <summary>함수의 <see cref="T:System.Runtime.InteropServices.FUNCFLAGS" />를 나타냅니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.FUNCFLAGS">
      <summary>함수의 속성을 정의하는 상수를 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FBINDABLE">
      <summary>데이터 바인딩을 지원하는 함수입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FDEFAULTBIND">
      <summary>개체를 가장 잘 표현하는 함수입니다.형식에 있는 한 함수만 이 특성을 가질 수 있습니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FDEFAULTCOLLELEM">
      <summary>컴파일러가 “abc” 형식에서 이름이 “xyz”인 멤버를 찾는 최적화를 허용합니다.이러한 멤버를 발견하여 기본 컬렉션의 요소에 대한 접근자 함수로 플래그하면, 호출은 해당 멤버 함수로 생성됩니다.모듈에서 허용되지 않고 dispinterface와 인터페이스의 멤버에서 허용됩니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FDISPLAYBIND">
      <summary>사용자에게 바인딩할 수 있는 것으로 표시되는 함수입니다.<see cref="F:System.Runtime.InteropServices.FUNCFLAGS.FUNCFLAG_FBINDABLE" />도 설정되어야 합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FHIDDEN">
      <summary>함수가 존재하고 바인딩 가능하더라도 사용자에게 표시되면 안 됩니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FIMMEDIATEBIND">
      <summary>별개의 바인딩 가능한 속성으로 매핑되었습니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FNONBROWSABLE">
      <summary>이 속성은 속성 브라우저가 아니라 개체 브라우저에 나타납니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FREPLACEABLE">
      <summary>인터페이스가 기본 동작을 사용하는 것으로 표시합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FREQUESTEDIT">
      <summary>설정된 경우, 속성을 설정하는 메서드를 호출하면 먼저 IPropertyNotifySink::OnRequestEdit가 호출됩니다.OnRequestEdit를 구현하면 호출이 속성을 설정하도록 허용될지 결정됩니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FRESTRICTED">
      <summary>매크로 언어에서 함수에 액세스해서는 안 됩니다.이 플래그는 시스템 수준 함수 또는 형식 브라우저가 표시하지 않는 함수를 위한 것입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FSOURCE">
      <summary>이 함수는 이벤트의 소스인 개체를 반환합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FUIDEFAULT">
      <summary>형식 정보 멤버는 사용자 인터페이스에 표시하는 기본 멤버입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCFLAGS.FUNCFLAG_FUSESGETLASTERROR">
      <summary>이 함수는 GetLastError를 지원합니다.함수에서 오류가 발생하면 호출자가 GetLastError를 호출하여 오류 코드를 검색할 수 있습니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.FUNCKIND">
      <summary>함수에 액세스하는 방법을 정의합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCKIND.FUNC_DISPATCH">
      <summary>함수는 IDispatch를 통해서만 액세스됩니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCKIND.FUNC_NONVIRTUAL">
      <summary>함수는 static 주소로 액세스되고 암시적인 this 포인터를 사용합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCKIND.FUNC_PUREVIRTUAL">
      <summary>함수는 VTBL(가상 함수 테이블)을 통해 액세스되고 암시적인 this 포인터를 사용합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCKIND.FUNC_STATIC">
      <summary>함수는 static 주소로 액세스되고 암시적인 this 포인터를 사용하지 않습니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.FUNCKIND.FUNC_VIRTUAL">
      <summary>이 함수는 구현된다는 점을 제외하고는 <see cref="F:System.Runtime.InteropServices.FUNCKIND.FUNC_PUREVIRTUAL" />과 동일한 방식으로 액세스됩니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IAdviseSink">
      <summary>IAdviseSink 인터페이스의 관리되는 정의를 제공합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnClose">
      <summary>등록된 모든 advise 싱크에 개체가 실행 상태에서 로드된 상태로 변경되었음을 알립니다.  이 메서드는 서버에서 호출됩니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnDataChange(System.Runtime.InteropServices.ComTypes.FORMATETC@,System.Runtime.InteropServices.ComTypes.STGMEDIUM@)">
      <summary>모든 데이터 개체의 현재 등록된 advise 싱크에 개체의 데이터가 변경되었음을 알립니다.</summary>
      <param name="format">호출 데이터 개체의 형식, 대상 장치, 렌더링 및 저장소 정보를 설명하는 <see cref="T:System.Runtime.InteropServices.ComTypes.FORMATETC" />로, 참조로 전달됩니다.</param>
      <param name="stgmedium">호출 데이터 개체의 저장 미디어(전역 메모리, 디스크 파일, 저장소 개체, 스트림 개체, GDI(Graphics Device Interface) 개체 또는 정의되지 않은 미디어)와 해당 미디어의 소유권을 정의하는 <see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" />으로, 참조로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnRename(System.Runtime.InteropServices.ComTypes.IMoniker)">
      <summary>등록된 모든 advise 싱크에 개체 이름이 바뀌었음을 알립니다.이 메서드는 서버에서 호출됩니다.</summary>
      <param name="moniker">개체의 새로운 전체 모니커에 있는 IMoniker 인터페이스에 대한 포인터입니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnSave">
      <summary>등록된 모든 advise 싱크에 개체가 저장되었음을 알립니다.이 메서드는 서버에서 호출됩니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IAdviseSink.OnViewChange(System.Int32,System.Int32)">
      <summary>개체의 등록된 advise 싱크에 개체 뷰가 변경되었음을 알립니다.이 메서드는 서버에서 호출됩니다.</summary>
      <param name="aspect">개체의 모양 또는 뷰입니다.<see cref="T:System.Runtime.InteropServices.ComTypes.DVASPECT" /> 열거형에서 가져온 값이 포함되어 있습니다.</param>
      <param name="index">변경된 뷰 부분입니다.현재 -1만 유효합니다.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IBindCtx">
      <summary>IBindCtx 인터페이스의 관리되는 정의를 제공합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.EnumObjectParam(System.Runtime.InteropServices.ComTypes.IEnumString@)">
      <summary>내부적으로 유지되는 컨텍스트 개체 매개 변수 테이블의 키인 문자열을 열거합니다.</summary>
      <param name="ppenum">이 메서드는 반환될 때 개체 매개 변수 열거자에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.GetBindOptions(System.Runtime.InteropServices.ComTypes.BIND_OPTS@)">
      <summary>현재 바인드 컨텍스트에 저장되어 있는 현재 바인딩 옵션을 반환합니다.</summary>
      <param name="pbindopts">바인딩 옵션을 받는 구조체에 대한 포인터입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.GetObjectParam(System.String,System.Object@)">
      <summary>내부적으로 유지되는 컨텍스트 개체 매개 변수 테이블에서 지정한 키를 조회한 다음 해당하는 개체가 있으면 이를 반환합니다.</summary>
      <param name="pszKey">검색할 개체의 이름입니다. </param>
      <param name="ppunk">이 메서드는 반환될 때 개체 인터페이스 포인터를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.GetRunningObjectTable(System.Runtime.InteropServices.ComTypes.IRunningObjectTable@)">
      <summary>이 바인딩 프로세스와 관련된 ROT(실행 개체 테이블)에 대한 액세스를 반환합니다.</summary>
      <param name="pprot">이 메서드는 반환될 때 ROT(실행 개체 테이블)에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.RegisterObjectBound(System.Object)">
      <summary>전달된 개체를 모니커 작업을 하는 동안 바인딩되고 작업이 완료되면 해제되어야 하는 개체 중 하나로 등록합니다.</summary>
      <param name="punk">해제하기 위해 등록할 개체입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.RegisterObjectParam(System.String,System.Object)">
      <summary>지정된 개체 포인터를 내부적으로 유지되는 개체 포인터 테이블에 지정된 이름으로 등록합니다.</summary>
      <param name="pszKey">
        <paramref name="punk" />를 등록할 이름입니다. </param>
      <param name="punk">등록할 개체입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.ReleaseBoundObjects">
      <summary>
        <see cref="M:System.Runtime.InteropServices.ComTypes.IBindCtx.RegisterObjectBound(System.Object)" /> 메서드를 사용하여 바인드 컨텍스트에 현재 등록되어 있는 모든 개체를 해제합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.RevokeObjectBound(System.Object)">
      <summary>등록된 개체의 집합에서 해제해야 하는 개체를 제거합니다.</summary>
      <param name="punk">해제하기 위해 등록 취소할 개체입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.RevokeObjectParam(System.String)">
      <summary>내부적으로 유지되는 컨텍스트 개체 매개 변수 테이블의 지정된 키가 현재 등록되어 있으면 해당 키 아래에 있는 개체의 등록을 취소합니다.</summary>
      <returns>지정된 키가 테이블에서 성공적으로 제거되었으면 S_OKHRESULT 값이고, 그렇지 않으면 S_FALSEHRESULT 값입니다.</returns>
      <param name="pszKey">등록 취소할 키입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IBindCtx.SetBindOptions(System.Runtime.InteropServices.ComTypes.BIND_OPTS@)">
      <summary>매개 변수 블록을 바인드 컨텍스트에 저장합니다.이러한 매개 변수는 이 바인드 컨텍스트를 사용하는 나중 UCOMIMoniker 작업에 적용됩니다.</summary>
      <param name="pbindopts">설정할 바인딩 옵션이 들어 있는 구조체입니다. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IConnectionPoint">
      <summary>IConnectionPoint 인터페이스의 관리되는 정의를 제공합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Advise(System.Object,System.Int32@)">
      <summary>연결점과 호출자의 싱크 개체 사이에 권장되는 연결을 설치합니다.</summary>
      <param name="pUnkSink">이 연결점이 관리하는 송신 인터페이스의 호출을 받는 싱크에 대한 참조입니다. </param>
      <param name="pdwCookie">이 메서드는 반환될 때 연결 쿠키를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.EnumConnections(System.Runtime.InteropServices.ComTypes.IEnumConnections@)">
      <summary>이 연결점에 있는 접속을 통해 반복적으로 열거자 개체를 만듭니다.</summary>
      <param name="ppEnum">이 메서드는 반환될 때 새로 만든 열거자를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.GetConnectionInterface(System.Guid@)">
      <summary>이 연결점이 관리하는 송신 인터페이스의 IID를 반환합니다.</summary>
      <param name="pIID">이 매개 변수는 반환될 때, 이 연결점이 관리하는 송신 인터페이스의 IID를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.GetConnectionPointContainer(System.Runtime.InteropServices.ComTypes.IConnectionPointContainer@)">
      <summary>이 연결점을 개념적으로 소유하는 연결 가능한 개체에 대한 IConnectionPointContainer 인터페이스 포인터를 검색합니다.</summary>
      <param name="ppCPC">이 매개 변수는 반환될 때, 연결 가능한 개체의 IConnectionPointContainer 인터페이스를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Unadvise(System.Int32)">
      <summary>
        <see cref="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Advise(System.Object,System.Int32@)" /> 메서드를 통해 이전에 설치된 권장된 연결을 종료합니다</summary>
      <param name="dwCookie">이전에 <see cref="M:System.Runtime.InteropServices.ComTypes.IConnectionPoint.Advise(System.Object,System.Int32@)" /> 메서드에서 반환된 연결 쿠키입니다. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IConnectionPointContainer">
      <summary>IConnectionPointContainer 인터페이스의 관리되는 정의를 제공합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPointContainer.EnumConnectionPoints(System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints@)">
      <summary>IID마다 하나의 연결점이 있는 연결 가능한 개체에서 지원되는 모든 연결점의 열거자를 만듭니다.</summary>
      <param name="ppEnum">이 메서드는 반환될 때 열거자의 인터페이스 포인터를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IConnectionPointContainer.FindConnectionPoint(System.Guid@,System.Runtime.InteropServices.ComTypes.IConnectionPoint@)">
      <summary>연결 가능한 개체가 특수한 하나의 IID에 대해 하나의 연결점을 가지는지 여부를 묻습니다. 연결점을 가지는 경우 해당 연결점에 대한 IConnectionPoint 인터페이스 포인터를 반환합니다.</summary>
      <param name="riid">연결점이 요구되고 있는 송신 인터페이스 IID에 대한 참조입니다. </param>
      <param name="ppCP">이 메서드는 반환될 때. 송신 인터페이스 <paramref name="riid" />를 관리하는 연결 지점을 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IDLDESC">
      <summary>구조체 요소, 매개 변수, 프로세스간 함수 반환 값을 전달하는데 필요한 정보가 들어 있습니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLDESC.dwReserved">
      <summary>null로 설정되어 예약되었습니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLDESC.wIDLFlags">
      <summary>형식을 설명하는 <see cref="T:System.Runtime.InteropServices.IDLFLAG" /> 값을 나타냅니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IDLFLAG">
      <summary>구조체 요소, 매개 변수 또는 함수 반환 값을 한 프로세스에서 다른 프로세스로 전송하는 방법을 설명합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLFLAG.IDLFLAG_FIN">
      <summary>매개 변수는 호출자에서 호출 수신자로 정보를 전달합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLFLAG.IDLFLAG_FLCID">
      <summary>매개 변수가 클라이언트 응용 프로그램의 로컬 식별자입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLFLAG.IDLFLAG_FOUT">
      <summary>매개 변수가 호출 수신자에게서 호출자에게로 정보를 반환합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLFLAG.IDLFLAG_FRETVAL">
      <summary>매개 변수가 멤버의 반환 값입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IDLFLAG.IDLFLAG_NONE">
      <summary>매개 변수가 정보를 전달하는지 아니면 정보를 받는지 지정하지 않습니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints">
      <summary>IEnumConnectionPoints 인터페이스의 정의를 관리합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints.Clone(System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints@)">
      <summary>현재 열거자와 열거 상태가 같은 새 열거자를 만듭니다.</summary>
      <param name="ppenum">이 메서드가 반환될 때 새로 만들어진 열거자에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints.Next(System.Int32,System.Runtime.InteropServices.ComTypes.IConnectionPoint[],System.IntPtr)">
      <summary>열거형 시퀀스에서 지정된 수의 항목을 검색합니다.</summary>
      <returns>
        <paramref name="pceltFetched" /> 매개 변수가 <paramref name="celt" /> 매개 변수와 같으면 S_OK이고, 그렇지 않으면 S_FALSE입니다.</returns>
      <param name="celt">
        <paramref name="rgelt" />에서 반환할 IConnectionPoint 참조의 수입니다. </param>
      <param name="rgelt">이 메서드는 반환될 때 열거된 연결에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
      <param name="pceltFetched">이 메서드는 반환될 때 <paramref name="rgelt" />에 열거된 연결의 실제 수에 대한 참조를 포함합니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints.Reset">
      <summary>열거형 시퀀스를 처음으로 다시 설정합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnectionPoints.Skip(System.Int32)">
      <summary>열거형 시퀀스에서 지정된 수의 항목을 건너뜁니다.</summary>
      <returns>건너뛴 항목 수가 <paramref name="celt" /> 매개 변수와 같으면 S_OK이고, 그렇지 않으면 S_FALSE입니다.</returns>
      <param name="celt">열거형에서 건너뛸 요소 수입니다. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumConnections">
      <summary>IEnumConnections 인터페이스의 정의를 관리합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnections.Clone(System.Runtime.InteropServices.ComTypes.IEnumConnections@)">
      <summary>현재 열거자와 열거 상태가 같은 새 열거자를 만듭니다.</summary>
      <param name="ppenum">이 메서드가 반환될 때 새로 만들어진 열거자에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnections.Next(System.Int32,System.Runtime.InteropServices.ComTypes.CONNECTDATA[],System.IntPtr)">
      <summary>열거형 시퀀스에서 지정된 수의 항목을 검색합니다.</summary>
      <returns>
        <paramref name="pceltFetched" /> 매개 변수가 <paramref name="celt" /> 매개 변수와 같으면 S_OK이고, 그렇지 않으면 S_FALSE입니다.</returns>
      <param name="celt">
        <paramref name="rgelt" />에서 반환할 <see cref="T:System.Runtime.InteropServices.CONNECTDATA" /> 구조체의 수입니다. </param>
      <param name="rgelt">이 메서드는 반환될 때 열거된 연결에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
      <param name="pceltFetched">이 메서드는 반환될 때 <paramref name="rgelt" />에 열거된 연결의 실제 수에 대한 참조를 포함합니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnections.Reset">
      <summary>열거형 시퀀스를 처음으로 다시 설정합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumConnections.Skip(System.Int32)">
      <summary>열거형 시퀀스에서 지정된 수의 항목을 건너뜁니다.</summary>
      <returns>건너뛴 항목 수가 <paramref name="celt" /> 매개 변수와 같으면 S_OK이고, 그렇지 않으면 S_FALSE입니다.</returns>
      <param name="celt">열거형에서 건너뛸 요소 수입니다. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumFORMATETC">
      <summary>IEnumFORMATETC 인터페이스의 관리되는 정의를 제공합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumFORMATETC.Clone(System.Runtime.InteropServices.ComTypes.IEnumFORMATETC@)">
      <summary>현재 열거자와 열거 상태가 같은 새 열거자를 만듭니다.</summary>
      <param name="newEnum">이 메서드가 반환될 때 새로 만들어진 열거자에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumFORMATETC.Next(System.Int32,System.Runtime.InteropServices.ComTypes.FORMATETC[],System.Int32[])">
      <summary>열거형 시퀀스에서 지정된 수의 항목을 검색합니다.</summary>
      <returns>
        <paramref name="pceltFetched" /> 매개 변수가 <paramref name="celt" /> 매개 변수와 같으면 S_OK이고, 그렇지 않으면 S_FALSE입니다.</returns>
      <param name="celt">
        <paramref name="rgelt" />에서 반환할 <see cref="T:System.Runtime.InteropServices.ComTypes.FORMATETC" /> 참조의 수입니다.</param>
      <param name="rgelt">이 메서드가 반환될 때 열거된 <see cref="T:System.Runtime.InteropServices.ComTypes.FORMATETC" /> 참조에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
      <param name="pceltFetched">이 메서드가 반환될 때 <paramref name="rgelt" />에 열거된 실제 참조 수에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumFORMATETC.Reset">
      <summary>열거형 시퀀스를 처음으로 다시 설정합니다.</summary>
      <returns>값이 S_OK인 HRESULT입니다.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumFORMATETC.Skip(System.Int32)">
      <summary>열거형 시퀀스에서 지정된 수의 항목을 건너뜁니다.</summary>
      <returns>건너뛴 항목 수가 <paramref name="celt" /> 매개 변수와 같으면 S_OK이고, 그렇지 않으면 S_FALSE입니다.</returns>
      <param name="celt">열거형에서 건너뛸 요소 수입니다.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumMoniker">
      <summary>IEnumMoniker 인터페이스의 정의를 관리합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumMoniker.Clone(System.Runtime.InteropServices.ComTypes.IEnumMoniker@)">
      <summary>현재 열거자와 열거 상태가 같은 새 열거자를 만듭니다.</summary>
      <param name="ppenum">이 메서드가 반환될 때 새로 만들어진 열거자에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumMoniker.Next(System.Int32,System.Runtime.InteropServices.ComTypes.IMoniker[],System.IntPtr)">
      <summary>열거형 시퀀스에서 지정된 수의 항목을 검색합니다.</summary>
      <returns>
        <paramref name="pceltFetched" /> 매개 변수가 <paramref name="celt" /> 매개 변수와 같으면 S_OK이고, 그렇지 않으면 S_FALSE입니다.</returns>
      <param name="celt">
        <paramref name="rgelt" />에서 반환할 모니커의 수입니다. </param>
      <param name="rgelt">이 메서드는 반환될 때 열거된 모니커에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
      <param name="pceltFetched">이 메서드는 반환될 때 <paramref name="rgelt" />에 열거된 모니커의 실제 수에 대한 참조를 포함합니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumMoniker.Reset">
      <summary>열거형 시퀀스를 처음으로 다시 설정합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumMoniker.Skip(System.Int32)">
      <summary>열거형 시퀀스에서 지정된 수의 항목을 건너뜁니다.</summary>
      <returns>건너뛴 항목 수가 <paramref name="celt" /> 매개 변수와 같으면 S_OK이고, 그렇지 않으면 S_FALSE입니다.</returns>
      <param name="celt">열거형에서 건너뛸 요소 수입니다. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumString">
      <summary>IEnumString 인터페이스의 정의를 관리합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumString.Clone(System.Runtime.InteropServices.ComTypes.IEnumString@)">
      <summary>현재 열거자와 열거 상태가 같은 새 열거자를 만듭니다.</summary>
      <param name="ppenum">이 메서드가 반환될 때 새로 만들어진 열거자에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumString.Next(System.Int32,System.String[],System.IntPtr)">
      <summary>열거형 시퀀스에서 지정된 수의 항목을 검색합니다.</summary>
      <returns>
        <paramref name="pceltFetched" /> 매개 변수가 <paramref name="celt" /> 매개 변수와 같으면 S_OK이고, 그렇지 않으면 S_FALSE입니다.</returns>
      <param name="celt">
        <paramref name="rgelt" />에서 반환할 문자열의 수입니다. </param>
      <param name="rgelt">이 메서드는 반환될 때 열거형 문자열에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
      <param name="pceltFetched">이 메서드는 반환될 때, <paramref name="rgelt" />에 열거된 문자열의 실제 수에 대한 참조를 포함합니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumString.Reset">
      <summary>열거형 시퀀스를 처음으로 다시 설정합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumString.Skip(System.Int32)">
      <summary>열거형 시퀀스에서 지정된 수의 항목을 건너뜁니다.</summary>
      <returns>건너뛴 항목 수가 <paramref name="celt" /> 매개 변수와 같으면 S_OK이고, 그렇지 않으면 S_FALSE입니다.</returns>
      <param name="celt">열거형에서 건너뛸 요소 수입니다. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IEnumVARIANT">
      <summary>IEnumVARIANT 인터페이스의 정의를 관리합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumVARIANT.Clone">
      <summary>현재 열거자와 열거 상태가 같은 새 열거자를 만듭니다.</summary>
      <returns>새로 만든 열거자에 대한 <see cref="T:System.Runtime.InteropServices.ComTypes.IEnumVARIANT" /> 참조입니다.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumVARIANT.Next(System.Int32,System.Object[],System.IntPtr)">
      <summary>열거형 시퀀스에서 지정된 수의 항목을 검색합니다.</summary>
      <returns>
        <paramref name="pceltFetched" /> 매개 변수가 <paramref name="celt" /> 매개 변수와 같으면 S_OK이고, 그렇지 않으면 S_FALSE입니다.</returns>
      <param name="celt">
        <paramref name="rgelt" />에서 반환할 요소 수입니다. </param>
      <param name="rgVar">이 메서드는 반환될 때 열거된 요소에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
      <param name="pceltFetched">이 메서드는 반환될 때 <paramref name="rgelt" />에 열거된 요소의 실제 수에 대한 참조를 포함합니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumVARIANT.Reset">
      <summary>열거형 시퀀스를 처음으로 다시 설정합니다.</summary>
      <returns>값이 S_OK인 HRESULT입니다.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IEnumVARIANT.Skip(System.Int32)">
      <summary>열거형 시퀀스에서 지정된 수의 항목을 건너뜁니다.</summary>
      <returns>건너뛴 요소 수가 <paramref name="celt" /> 매개 변수와 같으면 S_OK이고, 그렇지 않으면 S_FALSE입니다.</returns>
      <param name="celt">열거형에서 건너뛸 요소 수입니다. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IMoniker">
      <summary>IPersist와 IPersistStream의 COM 기능이 있는 IMoniker 인터페이스의 관리되는 정의를 제공합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.BindToObject(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.Guid@,System.Object@)">
      <summary>모니커를 사용하여 모니커가 식별하는 개체에 바인딩합니다.</summary>
      <param name="pbc">이 바인딩 작업에서 사용되는 바인딩 컨텍스트 개체의 IBindCtx 인터페이스에 대한 참조입니다. </param>
      <param name="pmkToLeft">현재 모니커가 구성 모니커의 일부인 경우 해당 모니커의 왼쪽에 있는 모니커에 대한 참조입니다. </param>
      <param name="riidResult">모니커가 식별하는 개체와 통신하기 위해 클라이언트에서 사용할 인터페이스의 IID(인터페이스 식별자)입니다. </param>
      <param name="ppvResult">이 메서드는 반환될 때 <paramref name="riidResult" />에서 요청한 인터페이스에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.BindToStorage(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.Guid@,System.Object@)">
      <summary>모니커로 식별된 개체가 들어 있는 저장소에 대한 인터페이스 포인터를 검색합니다.</summary>
      <param name="pbc">이 바인딩 작업을 수행하는 동안 사용되는 바인드 컨텍스트 개체의 IBindCtx 인터페이스에 대한 참조입니다. </param>
      <param name="pmkToLeft">현재 모니커가 구성 모니커의 일부인 경우 해당 모니커의 왼쪽에 있는 모니커에 대한 참조입니다. </param>
      <param name="riid">요청된 저장소 인터페이스의 IID(인터페이스 식별자)입니다. </param>
      <param name="ppvObj">이 메서드는 반환될 때 <paramref name="riid" />에서 요청한 인터페이스에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.CommonPrefixWith(System.Runtime.InteropServices.ComTypes.IMoniker,System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>이 모니커와 다른 모니커가 공유하는 공통 접두사를 사용하여 새 모니커를 만듭니다.</summary>
      <param name="pmkOther">현재 모니커와 공통 접두사를 비교할 다른 모니커의 IMoniker 인터페이스에 대한 참조입니다. </param>
      <param name="ppmkPrefix">이 메서드는 반환될 때 현재 모니커와 <paramref name="pmkOther" />의 공통 접두사인 모니커를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.ComposeWith(System.Runtime.InteropServices.ComTypes.IMoniker,System.Boolean,System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>이 모니커와 다른 모니커를 조합하여 새로운 구성 모니커를 만듭니다.</summary>
      <param name="pmkRight">현재 모니커의 끝에 추가할 모니커의 IMoniker 인터페이스에 대한 참조입니다. </param>
      <param name="fOnlyIfNotGeneric">호출자가 제네릭이 아닌 구성을 요구한다는 것을 나타내려면 true입니다.<paramref name="pmkRight" />가 현재 모니커가 제네릭 구성을 만드는 것과 다른 방법으로 결합할 수 있는 모니커 클래스인 경우에만 작업이 진행됩니다.이 메서드가 필요한 경우 제네릭 구성을 만들 수 있음을 나타내려면 false입니다.</param>
      <param name="ppmkComposite">이 메서드는 반환될 때 만들어지는 구성 모니커에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Enum(System.Boolean,System.Runtime.InteropServices.ComTypes.IEnumMoniker@)">
      <summary>구성 모니커의 구성 요소를 열거할 수 있는 열거자에 대한 포인터를 제공합니다.</summary>
      <param name="fForward">모니커를 왼쪽에서 오른쪽으로 열거하려면 true이고,오른쪽에서 왼쪽으로 열거하려면 false입니다.</param>
      <param name="ppenumMoniker">이 메서드는 반환될 때 모니커의 열거자 개체에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.GetClassID(System.Guid@)">
      <summary>개체의 CLSID(클래스 식별자)를 검색합니다.</summary>
      <param name="pClassID">이 메서드는 반환될 때 CLSID를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.GetDisplayName(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.String@)">
      <summary>사용자가 현재 모니커를 읽을 수 있도록 표현하는 표시 이름을 가져옵니다.</summary>
      <param name="pbc">이 작업에서 사용할 바인드 컨텍스트에 대한 참조입니다. </param>
      <param name="pmkToLeft">현재 모니커가 구성 모니커의 일부인 경우 해당 모니커의 왼쪽에 있는 모니커에 대한 참조입니다. </param>
      <param name="ppszDisplayName">이 메서드는 반환될 때 표시 이름 문자열을 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.GetSizeMax(System.Int64@)">
      <summary>개체를 저장하는 데 필요한 스트림의 크기를 바이트 단위로 반환합니다.</summary>
      <param name="pcbSize">이 메서드는 반환될 때 이 개체를 저장하는 데 필요한 스트림의 크기를 바이트 단위로 나타내는 long 값을 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.GetTimeOfLastChange(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.Runtime.InteropServices.ComTypes.FILETIME@)">
      <summary>현재 모니커에서 식별한 개체가 마지막으로 변경된 시간을 나타내는 숫자를 제공합니다.</summary>
      <param name="pbc">이 바인딩 작업에서 사용할 바인드 컨텍스트에 대한 참조입니다. </param>
      <param name="pmkToLeft">현재 모니커가 구성 모니커의 일부인 경우 해당 모니커의 왼쪽에 있는 모니커에 대한 참조입니다. </param>
      <param name="pFileTime">이 메서드는 반환될 때 마지막으로 변경한 시간을 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Hash(System.Int32@)">
      <summary>모니커의 내부 상태를 사용하여 32비트 정수를 계산합니다.</summary>
      <param name="pdwHash">이 메서드는 반환될 때 이 모니커에 대한 해시 값을 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Inverse(System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>현재 모니커 또는 구조가 비슷한 모니커의 오른쪽에 구성되면 구성이 이루어지지 않는 모니커를 제공합니다.</summary>
      <param name="ppmk">이 메서드는 반환될 때 현재 모니커와 반대되는 모니커를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.IsDirty">
      <summary>마지막으로 저장된 이후 개체의 변경 사항을 검사합니다.</summary>
      <returns>개체가 변경되었으면 S_OKHRESULT 값이고, 그렇지 않으면 S_FALSEHRESULT 값입니다.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.IsEqual(System.Runtime.InteropServices.ComTypes.IMoniker)">
      <summary>현재 모니커를 지정된 모니커와 비교하여 두 모니커가 같은지 여부를 나타냅니다.</summary>
      <returns>모니커가 동일하면 S_OKHRESULT 값이고, 그렇지 않으면 S_FALSEHRESULT 값입니다.  </returns>
      <param name="pmkOtherMoniker">비교할 모니커에 대한 참조입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.IsRunning(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.Runtime.InteropServices.ComTypes.IMoniker)">
      <summary>현재 모니커에서 식별한 개체가 현재 로드되고 실행되는지 여부를 확인합니다.</summary>
      <returns>S_OKHRESULT 값(모니커가 실행 중인 경우), S_FALSEHRESULT 값(모니커가 실행 중이 아닌 경우) 또는 E_UNEXPECTEDHRESULT 값입니다.</returns>
      <param name="pbc">이 바인딩 작업에서 사용할 바인드 컨텍스트에 대한 참조입니다. </param>
      <param name="pmkToLeft">현재 모니커가 구성 모니커의 일부인 경우 현재 모니커의 왼쪽에 있는 모니커에 대한 참조입니다. </param>
      <param name="pmkNewlyRunning">가장 최근에 ROT(실행 개체 테이블)에 추가된 모니커에 대한 참조입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.IsSystemMoniker(System.Int32@)">
      <summary>모니커가 시스템에 제공된 모니커 클래스 중의 하나인지 여부를 나타냅니다.</summary>
      <returns>모니커가 시스템 모니커이면 S_OKHRESULT 값이고, 그렇지 않으면 S_FALSEHRESULT 값입니다.</returns>
      <param name="pdwMksys">이 메서드는 반환될 때 MKSYS 열거형의 값 중 하나이며 COM 모니커 클래스 중 하나를 참조하는 정수에 대한 포인터를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Load(System.Runtime.InteropServices.ComTypes.IStream)">
      <summary>이전에 개체가 저장된 스트림에서 해당 개체를 초기화합니다.</summary>
      <param name="pStm">개체가 로드되는 스트림입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.ParseDisplayName(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.String,System.Int32@,System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>
        <see cref="M:System.Runtime.InteropServices.ComTypes.IMoniker.ParseDisplayName(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Runtime.InteropServices.ComTypes.IMoniker,System.String,System.Int32@,System.Runtime.InteropServices.ComTypes.IMoniker@)" />에서 이해할 수 있을 만큼 지정된 표시 이름의 문자를 읽고, 읽은 부분에 해당하는 모니커를 빌드합니다.</summary>
      <param name="pbc">이 바인딩 작업에서 사용할 바인드 컨텍스트에 대한 참조입니다. </param>
      <param name="pmkToLeft">표시 이름에서 이 지점까지 빌드된 모니커에 대한 참조입니다. </param>
      <param name="pszDisplayName">구문 분석할 나머지 표시 이름이 들어 있는 문자열에 대한 참조입니다. </param>
      <param name="pchEaten">이 메서드는 반환될 때 <paramref name="pszDisplayName" />을 구문 분석할 때 사용한 문자 수를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
      <param name="ppmkOut">이 메서드는 반환될 때 <paramref name="pszDisplayName" />에서 빌드된 모니커에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Reduce(System.Runtime.InteropServices.ComTypes.IBindCtx,System.Int32,System.Runtime.InteropServices.ComTypes.IMoniker@,System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>현재 모니커와 같은 개체를 참조하지만 더 크거나 같은 효율로 바인딩될 수 있는 다른 모니커인 축소된 모니커를 반환합니다.</summary>
      <param name="pbc">이 바인딩 작업에 사용할 바인드 컨텍스트의 IBindCtx 인터페이스에 대한 참조입니다. </param>
      <param name="dwReduceHowFar">현재 모니커를 얼마나 축소할지 지정하는 값입니다. </param>
      <param name="ppmkToLeft">현재 모니커의 왼쪽에 있는 모니커에 대한 참조입니다. </param>
      <param name="ppmkReduced">이 메서드는 반환될 때 현재 모니커의 축소된 형식에 대한 참조를 포함합니다. 이 참조는 오류가 발생하거나 현재 모니커가 축소되지 않을 경우 null이 될 수 있습니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.RelativePathTo(System.Runtime.InteropServices.ComTypes.IMoniker,System.Runtime.InteropServices.ComTypes.IMoniker@)">
      <summary>현재 모니커나 구조가 비슷한 모니커에 추가될 때 지정된 모니커를 생성하는 모니커를 제공합니다.</summary>
      <param name="pmkOther">상대 경로를 사용할 모니커에 대한 참조입니다. </param>
      <param name="ppmkRelPath">이 메서드는 반환될 때 상대 모니커에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IMoniker.Save(System.Runtime.InteropServices.ComTypes.IStream,System.Boolean)">
      <summary>개체를 지정된 스트림에 저장합니다.</summary>
      <param name="pStm">개체를 저장할 스트림입니다. </param>
      <param name="fClearDirty">저장이 완료된 후 수정된 플래그를 지우려면 true이고, 그렇지 않으면 false입니다.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS">
      <summary>구현되거나 상속된 인터페이스 형식의 특성을 정의합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS.IMPLTYPEFLAG_FDEFAULT">
      <summary>인터페이스나 dispinterface는 소스나 싱크의 기본값을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS.IMPLTYPEFLAG_FDEFAULTVTABLE">
      <summary>싱크는 VTBL(가상 함수 테이블)을 통해 이벤트를 받습니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS.IMPLTYPEFLAG_FRESTRICTED">
      <summary>사용자가 멤버를 표시하거나 프로그래밍할 수 없어야 합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS.IMPLTYPEFLAG_FSOURCE">
      <summary>coclass의 이 멤버는 구현되는 것이 아니라 호출됩니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND">
      <summary>IDispatch::Invoke를 사용하여 함수를 호출하는 방법을 지정합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.INVOKEKIND.INVOKE_FUNC">
      <summary>일반 함수 호출 구문을 통해 멤버가 호출됩니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.INVOKEKIND.INVOKE_PROPERTYGET">
      <summary>일반 속성 액세스 구문을 통해 함수가 호출됩니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.INVOKEKIND.INVOKE_PROPERTYPUT">
      <summary>속성 값 할당 구문을 통해 함수가 호출됩니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.INVOKEKIND.INVOKE_PROPERTYPUTREF">
      <summary>속성 참조 할당 구문을 통해 함수가 호출됩니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IPersistFile">
      <summary>IPersist의 기능이 있는 IPersistFile 인터페이스의 관리되는 정의를 제공합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.GetClassID(System.Guid@)">
      <summary>개체의 CLSID(클래스 식별자)를 검색합니다.</summary>
      <param name="pClassID">이 메서드는 반환될 때 CLSID에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.GetCurFile(System.String@)">
      <summary>개체의 현재 작업 파일의 절대 경로를 검색하거나 현재 작업 파일이 없는 경우에는 개체의 기본 파일 이름 프롬프트를 검색합니다.</summary>
      <param name="ppszFileName">이 메서드는 반환될 때 현재 파일의 경로가 들어 있는 0으로 끝나는 문자열에 대한 포인터의 주소 또는 기본 파일 이름 프롬프트(예: *.txt)를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.IsDirty">
      <summary>현재 파일에 마지막으로 저장된 이후 개체의 변경 사항을 검사합니다.</summary>
      <returns>마지막으로 저장된 이후 파일이 변경되었으면 S_OK이고, 변경되지 않았으면 S_FALSE입니다.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.Load(System.String,System.Int32)">
      <summary>지정된 파일을 열고 파일 내용에서 개체를 초기화합니다.</summary>
      <param name="pszFileName">열려는 파일의 절대 경로가 들어 있는 0으로 끝나는 문자열입니다. </param>
      <param name="dwMode">
        <paramref name="pszFileName" />을 열 액세스 모드를 표시할 STGM 열거형에 있는 값의 조합입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.Save(System.String,System.Boolean)">
      <summary>지정된 파일에 개체의 복사본을 저장합니다.</summary>
      <param name="pszFileName">개체가 저장되는 파일의 절대 경로가 들어 있는 0으로 끝나는 문자열입니다. </param>
      <param name="fRemember">
        <paramref name="pszFileName" /> 매개 변수를 현재 작업 파일로 사용하면 true이고, 그렇지 않으면 false입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IPersistFile.SaveCompleted(System.String)">
      <summary>개체가 해당 파일에 쓸 수 있다는 것을 개체에 알립니다.</summary>
      <param name="pszFileName">개체가 이미 저장되어 있는 파일의 절대 경로입니다. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IRunningObjectTable">
      <summary>IRunningObjectTable 인터페이스의 관리되는 정의를 제공합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.EnumRunning(System.Runtime.InteropServices.ComTypes.IEnumMoniker@)">
      <summary>실행 중 등록된 개체를 열거합니다.</summary>
      <param name="ppenumMoniker">이 메서드는 반환될 때 ROT(실행 개체 테이블)에 대한 새 열거자를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.GetObject(System.Runtime.InteropServices.ComTypes.IMoniker,System.Object@)">
      <summary>제공된 개체 이름이 실행 중에 등록되면 등록된 개체를 반환합니다.</summary>
      <returns>작업의 성공이나 실패를 나타내는 HRESULT 값입니다. </returns>
      <param name="pmkObjectName">ROT(실행 개체 테이블)에서 검색할 모니커에 대한 참조입니다. </param>
      <param name="ppunkObject">이 메서드는 반환될 때 요청한 실행 개체를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.GetTimeOfLastChange(System.Runtime.InteropServices.ComTypes.IMoniker,System.Runtime.InteropServices.ComTypes.FILETIME@)">
      <summary>ROT(실행 개체 테이블)에서 이 모니커를 검색하고 기록된 변경 시간이 있으면 이를 보고합니다.</summary>
      <returns>작업의 성공이나 실패를 나타내는 HRESULT 값입니다.</returns>
      <param name="pmkObjectName">ROT(실행 개체 테이블)에서 검색할 모니커에 대한 참조입니다. </param>
      <param name="pfiletime">이 개체는 반환될 때 개체가 마지막으로 변경된 시간을 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.IsRunning(System.Runtime.InteropServices.ComTypes.IMoniker)">
      <summary>지정한 모니커가 ROT(실행 개체 테이블)에 현재 등록되어 있는지 여부를 확인합니다.</summary>
      <returns>작업의 성공이나 실패를 나타내는 HRESULT 값입니다.</returns>
      <param name="pmkObjectName">ROT(실행 개체 테이블)에서 검색할 모니커에 대한 참조입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.NoteChangeTime(System.Int32,System.Runtime.InteropServices.ComTypes.FILETIME@)">
      <summary>특정 개체가 변경된 시간을 기록하여 IMoniker::GetTimeOfLastChange가 해당 변경 시간을 보고할 수 있도록 합니다.</summary>
      <param name="dwRegister">변경된 개체의 ROT(실행 개체 테이블) 항목입니다. </param>
      <param name="pfiletime">개체가 마지막으로 변경된 시간에 대한 참조입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.Register(System.Int32,System.Object,System.Runtime.InteropServices.ComTypes.IMoniker)">
      <summary>제공된 개체가 실행 상태로 들어갔음을 등록합니다.</summary>
      <returns>
        <see cref="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.Revoke(System.Int32)" /> 또는 <see cref="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.NoteChangeTime(System.Int32,System.Runtime.InteropServices.ComTypes.FILETIME@)" />에 대한 이후 호출에서 이 ROT 항목을 식별하는 데 사용할 수 있는 값입니다.</returns>
      <param name="grfFlags">
        <paramref name="punkObject" />에 대한 ROT(실행 개체 테이블)의 참조가 약한지 강한지 여부를 지정하여 ROT에 있는 항목을 통해 개체에 액세스하는 것을 제어합니다. </param>
      <param name="punkObject">실행 중 등록되는 개체에 대한 참조입니다. </param>
      <param name="pmkObjectName">
        <paramref name="punkObject" />를 식별하는 모니커에 대한 참조입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IRunningObjectTable.Revoke(System.Int32)">
      <summary>ROT(실행 개체 테이블)에서 지정한 개체의 등록을 취소합니다.</summary>
      <param name="dwRegister">취소할 ROT(실행 개체 테이블) 항목입니다. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.IStream">
      <summary>ISequentialStream 기능이 있는 IStream 인터페이스의 관리되는 정의를 제공합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Clone(System.Runtime.InteropServices.ComTypes.IStream@)">
      <summary>동일한 바이트를 원래의 스트림으로 참조하는 자체 검색 포인터를 사용하여 새 스트림 개체를 만듭니다.</summary>
      <param name="ppstm">이 메서드는 반환될 때 새 스트림 개체를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Commit(System.Int32)">
      <summary>트랜잭트 모드에서 열려 있는 스트림 개체에 대한 변경 사항이 모두 부모 저장소에 반영되도록 합니다.</summary>
      <param name="grfCommitFlags">스트림 개체에 대한 변경 사항이 커밋되는 방법을 제어하는 값입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.CopyTo(System.Runtime.InteropServices.ComTypes.IStream,System.Int64,System.IntPtr,System.IntPtr)">
      <summary>스트림에 있는 현재 검색 포인터에서 다른 스트림에 있는 현재 검색 포인터로 지정된 바이트 수를 복사합니다.</summary>
      <param name="pstm">대상 스트림에 대한 참조입니다. </param>
      <param name="cb">소스 스트림에서 복사할 바이트 수입니다. </param>
      <param name="pcbRead">반환이 성공적이면, 소스에서 읽은 실제 바이트 수를 포함합니다. </param>
      <param name="pcbWritten">반환이 성공적이면, 대상에 쓰여진 실제 바이트 수를 포함합니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.LockRegion(System.Int64,System.Int64,System.Int32)">
      <summary>스트림에서 지정된 바이트 범위로 액세스하는 것을 제한합니다.</summary>
      <param name="libOffset">시작 범위에 대한 바이트 오프셋입니다. </param>
      <param name="cb">제한할 범위의 길이(바이트)입니다. </param>
      <param name="dwLockType">범위에 액세스할 때 필요한 제한입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Read(System.Byte[],System.Int32,System.IntPtr)">
      <summary>현재 검색 포인터에서 스트림 개체에서 메모리 시작까지 지정된 바이트 수를 읽습니다.</summary>
      <param name="pv">이 메서드는 반환될 때 해당 스트림에서 읽은 데이터를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
      <param name="cb">스트림 개체에서 읽을 바이트 수입니다. </param>
      <param name="pcbRead">스트림 개체에서 읽은 실제 바이트 수를 받는 ULONG 변수에 대한 포인터입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Revert">
      <summary>마지막 <see cref="M:System.Runtime.InteropServices.ComTypes.IStream.Commit(System.Int32)" /> 호출 이후 트랜잭트 스트림에서 일어난 모든 변경 내용을 삭제합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Seek(System.Int64,System.Int32,System.IntPtr)">
      <summary>검색 포인터를 스트림의 시작 부분, 스트림의 끝 부분 또는 현재 검색 포인터와 관련된 새 위치로 변경합니다.</summary>
      <param name="dlibMove">
        <paramref name="dwOrigin" />에 추가할 변위입니다. </param>
      <param name="dwOrigin">검색할 원점입니다.원점은 파일의 시작, 현재 검색 포인터 또는 파일의 끝일 수 있습니다.</param>
      <param name="plibNewPosition">반환이 성공적이면, 스트림의 시작 부분에서 검색 포인터의 오프셋을 포함합니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.SetSize(System.Int64)">
      <summary>스트림 개체의 크기를 변경합니다.</summary>
      <param name="libNewSize">바이트 단위로 표현된 스트림의 새 크기입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Stat(System.Runtime.InteropServices.ComTypes.STATSTG@,System.Int32)">
      <summary>이 스트림에 대한 <see cref="T:System.Runtime.InteropServices.STATSTG" /> 구조를 검색합니다.</summary>
      <param name="pstatstg">이 메서드는 반환될 때 이 스트림 개체를 설명하는 STATSTG 구조체를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
      <param name="grfStatFlag">이 메서드가 반환되지 않으므로 메모리 할당 작업이 없는 STATSTG 구조체의 멤버입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.UnlockRegion(System.Int64,System.Int64,System.Int32)">
      <summary>이전에 <see cref="M:System.Runtime.InteropServices.ComTypes.IStream.LockRegion(System.Int64,System.Int64,System.Int32)" /> 메서드를 통해 제한한 바이트의 범위에 대한 액세스 제한을 제거합니다.</summary>
      <param name="libOffset">시작 범위에 대한 바이트 오프셋입니다. </param>
      <param name="cb">제한할 범위의 길이(바이트)입니다. </param>
      <param name="dwLockType">이전에 범위에 배치된 액세스 제한입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.IStream.Write(System.Byte[],System.Int32,System.IntPtr)">
      <summary>현재 검색 포인터에서 스트림 개체 시작에 지정된 바이트 수를 씁니다.</summary>
      <param name="pv">이 스트림을 쓸 버퍼입니다. </param>
      <param name="cb">스트림에 쓸 바이트 수입니다. </param>
      <param name="pcbWritten">반환이 성공적이면, 스트림 개체에 쓰여진 실제 바이트 수를 포함합니다.호출자가 이 포인터를 <see cref="F:System.IntPtr.Zero" />로 설정하면 이 메서드는 쓰여진 실제 바이트 수를 제공하지 않습니다.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ITypeComp">
      <summary>ITypeComp 인터페이스의 관리되는 정의를 제공합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeComp.Bind(System.String,System.Int32,System.Int16,System.Runtime.InteropServices.ComTypes.ITypeInfo@,System.Runtime.InteropServices.ComTypes.DESCKIND@,System.Runtime.InteropServices.ComTypes.BINDPTR@)">
      <summary>이름을 형식 멤버에 매핑하거나 형식 라이브러리에 있는 전역 변수와 함수를 바인딩합니다.</summary>
      <param name="szName">바인딩할 이름입니다. </param>
      <param name="lHashVal">LHashValOfNameSys를 사용하여 계산되는 <paramref name="szName" />의 해시 값입니다. </param>
      <param name="wFlags">여기에 사용된 단어 flags는 INVOKEKIND 열거형에 정의된 하나 이상의 호출 플래그를 나타냅니다. </param>
      <param name="ppTInfo">이 메서드는 반환될 때 FUNCDESC 또는 VARDESC가 반환된 경우 바인딩 대상 항목이 들어 있는 형식 설명에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
      <param name="pDescKind">이 메서드는 반환될 때 바인딩되는 이름이 VARDESC, FUNCDESC인지 또는 TYPECOMP인지 표시하는 DESCKIND 열거자에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
      <param name="pBindPtr">이 메서드는 반환될 때 바인딩 대상 VARDESC, FUNCDESC 및 ITypeComp 인터페이스에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeComp.BindType(System.String,System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo@,System.Runtime.InteropServices.ComTypes.ITypeComp@)">
      <summary>형식 라이브러리에 있는 형식 설명에 바인딩합니다.</summary>
      <param name="szName">바인딩할 이름입니다. </param>
      <param name="lHashVal">LHashValOfNameSys를 사용하여 확인되는 <paramref name="szName" />의 해시 값입니다. </param>
      <param name="ppTInfo">이 메서드는 반환될 때 <paramref name="szName" />이 바인딩된 대상 형식의 ITypeInfo에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
      <param name="ppTComp">이 메서드는 반환될 때 ITypeComp 변수에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ITypeInfo">
      <summary>구성 요소 자동화 ITypeInfo 인터페이스의 관리되는 정의를 제공합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.AddressOfMember(System.Int32,System.Runtime.InteropServices.ComTypes.INVOKEKIND,System.IntPtr@)">
      <summary>DLL에 정의된 것과 같은 정적 함수나 변수의 주소를 검색합니다.</summary>
      <param name="memid">검색할 static 멤버 주소의 멤버 ID입니다. </param>
      <param name="invKind">멤버가 속성인지 여부와 속성일 경우 종류를 지정하는 <see cref="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND" /> 값 중 하나입니다. </param>
      <param name="ppv">이 메서드는 반환될 때 static 멤버에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.CreateInstance(System.Object,System.Guid@,System.Object@)">
      <summary>구성 요소 클래스(coclass)를 설명하는 형식의 새 인스턴스를 만듭니다.</summary>
      <param name="pUnkOuter">제어용 IUnknown으로 작용하는 개체입니다. </param>
      <param name="riid">호출자가 결과 개체와 통신하기 위해 사용하는 인터페이스의 IID입니다. </param>
      <param name="ppvObj">이 메서드는 반환될 때 만들어진 개체에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetContainingTypeLib(System.Runtime.InteropServices.ComTypes.ITypeLib@,System.Int32@)">
      <summary>이 형식 설명과 해당 형식 라이브러리에 있는 인덱스를 포함하는 형식 라이브러리를 검색합니다.</summary>
      <param name="ppTLB">이 메서드는 반환될 때 포함하는 형식 라이브러리에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
      <param name="pIndex">이 메서드는 반환될 때 포함하는 형식 라이브러리에 있는 형식 설명의 인덱스에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetDllEntry(System.Int32,System.Runtime.InteropServices.ComTypes.INVOKEKIND,System.IntPtr,System.IntPtr,System.IntPtr)">
      <summary>DLL에 있는 함수에 대한 진입점의 설명 또는 사양을 검색합니다.</summary>
      <param name="memid">DLL 항목 설명이 반환되는 멤버 함수의 ID입니다. </param>
      <param name="invKind">
        <paramref name="memid" />로 식별되는 멤버 종류를 지정하는 <see cref="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND" /> 값 중 하나입니다. </param>
      <param name="pBstrDllName">null이 아니면 함수는 DLL의 이름을 포함하는 BSTR에 <paramref name="pBstrDllName" />을 설정합니다. </param>
      <param name="pBstrName">null이 아니면 함수는 진입점의 이름을 포함하는 BSTR에 <paramref name="lpbstrName" />을 설정합니다. </param>
      <param name="pwOrdinal">null이 아니고 함수가 서수에 의해 정의되면 <paramref name="lpwOrdinal" />이 서수를 가리키도록 설정됩니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetDocumentation(System.Int32,System.String@,System.String@,System.Int32@,System.String@)">
      <summary>설명서 문자열, 전체 도움말 파일 이름과 경로, 지정된 형식 설명에 대한 도움말 항목의 컨텍스트 ID를 검색합니다.</summary>
      <param name="index">설명서가 반환되는 멤버의 ID입니다. </param>
      <param name="strName">이 메서드는 반환될 때 항목 메서드의 이름을 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
      <param name="strDocString">이 메서드가 반환될 때 지정된 항목에 대한 설명서 문자열을 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
      <param name="dwHelpContext">이 메서드는 반환될 때 지정한 항목과 연관된 도움말 컨텍스트에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
      <param name="strHelpFile">이 메서드는 반환될 때 도움말 파일의 정규화된 이름을 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetFuncDesc(System.Int32,System.IntPtr@)">
      <summary>지정된 함수에 대한 정보가 들어 있는 <see cref="T:System.Runtime.InteropServices.FUNCDESC" /> 구조체를 검색합니다.</summary>
      <param name="index">반환할 함수 설명의 인덱스입니다. </param>
      <param name="ppFuncDesc">이 메서드는 반환될 때 지정한 함수를 설명하는 FUNCDESC 구조체에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetIDsOfNames(System.String[],System.Int32,System.Int32[])">
      <summary>멤버 이름과 멤버 ID, 매개 변수 이름과 매개 변수 ID를 매핑합니다.</summary>
      <param name="rgszNames">매핑할 이름 배열입니다. </param>
      <param name="cNames">매핑할 이름의 수입니다. </param>
      <param name="pMemId">이 메서드는 반환될 때 이름 매핑이 저장될 배열에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetImplTypeFlags(System.Int32,System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS@)">
      <summary>형식 설명의 구현된 인터페이스나 기본 인터페이스에 대한 <see cref="T:System.Runtime.InteropServices.IMPLTYPEFLAGS" /> 값을 검색합니다.</summary>
      <param name="index">구현된 인터페이스나 기본 인터페이스의 인덱스입니다. </param>
      <param name="pImplTypeFlags">이 메서드는 반환될 때 IMPLTYPEFLAGS 열거형에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetMops(System.Int32,System.String@)">
      <summary>마샬링 정보를 검색합니다.</summary>
      <param name="memid">마샬링 정보가 필요함을 나타내는 멤버 ID입니다. </param>
      <param name="pBstrMops">이 메서드는 반환될 때 참조된 형식 설명에 설명된 구조체의 필드를 마샬링하는 데 사용한 opcode 문자열에 대한 참조를 포함합니다. 반환할 정보가 없으면 null을 반환합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetNames(System.Int32,System.String[],System.Int32,System.Int32@)">
      <summary>지정한 함수 ID에 해당하는 지정한 멤버 ID를 가진 변수(속성, 메서드 및 해당 매개 변수의 이름)를 검색합니다.</summary>
      <param name="memid">이름 또는 이름들을 반환할 멤버의 ID입니다. </param>
      <param name="rgBstrNames">이 메서드는 반환되면, 멤버와 연관된 이름을 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
      <param name="cMaxNames">
        <paramref name="rgBstrNames" /> 배열의 길이입니다. </param>
      <param name="pcNames">이 메서드는 반환될 때 <paramref name="rgBstrNames" /> 배열에 이름 수를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetRefTypeInfo(System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>형식 설명에서 다른 형식 설명을 참조할 경우 참조된 형식 설명을 검색합니다.</summary>
      <param name="hRef">반환할 참조된 형식 설명에 대한 핸들입니다. </param>
      <param name="ppTI">이 메서드는 반환될 때 참조된 형식 설명을 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetRefTypeOfImplType(System.Int32,System.Int32@)">
      <summary>형식 설명이 COM 클래스를 설명할 경우 구현된 인터페이스 형식에 대한 형식 설명을 검색합니다.</summary>
      <param name="index">핸들이 반환되는 구현된 형식의 인덱스입니다. </param>
      <param name="href">이 메서드는 반환될 때 구현된 인터페이스의 핸들에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetTypeAttr(System.IntPtr@)">
      <summary>형식 설명의 특성이 들어 있는 <see cref="T:System.Runtime.InteropServices.TYPEATTR" /> 구조체를 검색합니다.</summary>
      <param name="ppTypeAttr">이 메서드는 반환될 때 이 형식 설명의 특성을 포함하는 구조체에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetTypeComp(System.Runtime.InteropServices.ComTypes.ITypeComp@)">
      <summary>클라이언트 컴파일러가 형식 설명의 멤버를 바인딩할 수 있도록 하는 형식 설명에 대한 ITypeComp 인터페이스를 검색합니다.</summary>
      <param name="ppTComp">이 메서드는 반환될 때 포함하는 형식 라이브러리의 ITypeComp 인터페이스에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetVarDesc(System.Int32,System.IntPtr@)">
      <summary>지정된 변수를 설명하는 VARDESC 구조를 검색합니다.</summary>
      <param name="index">반환할 변수 설명의 인덱스입니다. </param>
      <param name="ppVarDesc">이 메서드는 반환될 때 지정한 변수를 설명하는 VARDESC 구조체에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.Invoke(System.Object,System.Int32,System.Int16,System.Runtime.InteropServices.ComTypes.DISPPARAMS@,System.IntPtr,System.IntPtr,System.Int32@)">
      <summary>메서드를 호출하거나 개체의 속성에 액세스하여 형식 설명에서 설명한 인터페이스를 구현합니다.</summary>
      <param name="pvInstance">이 형식 설명에 설명된 인터페이스에 대한 참조입니다. </param>
      <param name="memid">인터페이스 멤버를 식별하는 값입니다. </param>
      <param name="wFlags">Invoke 호출의 컨텍스트를 설명하는 플래그입니다. </param>
      <param name="pDispParams">인수의 배열, 명명된 인수에 대한 인수 DISPID의 배열 및 배열에 있는 요소의 개수가 들어 있는 구조체에 대한 참조입니다. </param>
      <param name="pVarResult">결과를 저장할 위치에 대한 참조입니다.<paramref name="wFlags" />가 DISPATCH_PROPERTYPUT 또는 DISPATCH_PROPERTYPUTREF를 지정하는 경우에는 <paramref name="pVarResult" />가 무시됩니다.결과가 필요하지 않으면 null로 설정합니다.</param>
      <param name="pExcepInfo">예외 정보 구조체에 대한 포인터로, DISP_E_EXCEPTION이 반환되는 경우에만 채워집니다. </param>
      <param name="puArgErr">Invoke가 DISP_E_TYPEMISMATCH를 반환하는 경우 <paramref name="puArgErr" />는 형식이 잘못된 인수의 <paramref name="rgvarg" /> 내에 있는 인덱스를 나타냅니다.둘 이상의 인수가 오류를 반환하면 <paramref name="puArgErr" />는 오류를 포함하는 첫 번째 인수만 나타냅니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.ReleaseFuncDesc(System.IntPtr)">
      <summary>이전에 <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetFuncDesc(System.Int32,System.IntPtr@)" /> 메서드에서 반환한 <see cref="T:System.Runtime.InteropServices.FUNCDESC" /> 구조체를 해제합니다.</summary>
      <param name="pFuncDesc">해제할 FUNCDESC 구조체에 대한 참조입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.ReleaseTypeAttr(System.IntPtr)">
      <summary>이전에 <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetTypeAttr(System.IntPtr@)" /> 메서드에서 반환한 <see cref="T:System.Runtime.InteropServices.TYPEATTR" /> 구조체를 해제합니다.</summary>
      <param name="pTypeAttr">해제할 TYPEATTR 구조체에 대한 참조입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.ReleaseVarDesc(System.IntPtr)">
      <summary>이전에 <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetVarDesc(System.Int32,System.IntPtr@)" /> 메서드에서 반환한 VARDESC 구조체를 해제합니다.</summary>
      <param name="pVarDesc">해제할 VARDESC 구조체에 대한 참조입니다. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ITypeInfo2">
      <summary>ITypeInfo2 인터페이스의 관리되는 정의를 제공합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.AddressOfMember(System.Int32,System.Runtime.InteropServices.ComTypes.INVOKEKIND,System.IntPtr@)">
      <summary>DLL에 정의된 것과 같은 정적 함수나 변수의 주소를 검색합니다.</summary>
      <param name="memid">검색할 static 멤버 주소의 멤버 ID입니다. </param>
      <param name="invKind">멤버가 속성인지 여부와 속성일 경우 종류를 지정하는 <see cref="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND" /> 값 중 하나입니다. </param>
      <param name="ppv">이 메서드는 반환될 때 static 멤버에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.CreateInstance(System.Object,System.Guid@,System.Object@)">
      <summary>구성 요소 클래스(coclass)를 설명하는 형식의 새 인스턴스를 만듭니다.</summary>
      <param name="pUnkOuter">제어용 IUnknown으로 작용하는 개체입니다. </param>
      <param name="riid">호출자가 결과 개체와 통신하기 위해 사용하는 인터페이스의 IID입니다. </param>
      <param name="ppvObj">이 메서드는 반환될 때 만들어진 개체에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetAllCustData(System.IntPtr)">
      <summary>라이브러리에 대한 모든 사용자 지정 데이터 항목을 가져옵니다.</summary>
      <param name="pCustData">모든 사용자 지정 데이터 항목을 보유한 CUSTDATA의 포인터입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetAllFuncCustData(System.Int32,System.IntPtr)">
      <summary>지정한 함수에서 모든 사용자 지정 데이터를 가져옵니다.</summary>
      <param name="index">사용자 지정 데이터를 가져올 함수의 인덱스입니다. </param>
      <param name="pCustData">모든 사용자 지정 데이터 항목을 보유한 CUSTDATA의 포인터입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetAllImplTypeCustData(System.Int32,System.IntPtr)">
      <summary>지정한 구현 형식에 대한 모든 사용자 지정 데이터를 가져옵니다.</summary>
      <param name="index">사용자 지정 데이터에 대한 구현 형식의 인덱스입니다. </param>
      <param name="pCustData">모든 사용자 지정 데이터 항목을 보유한 CUSTDATA의 포인터입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetAllParamCustData(System.Int32,System.Int32,System.IntPtr)">
      <summary>지정한 함수 매개 변수에 대한 모든 사용자 지정 데이터를 가져옵니다.</summary>
      <param name="indexFunc">사용자 지정 데이터를 가져올 함수의 인덱스입니다. </param>
      <param name="indexParam">사용자 지정 데이터를 가져올 이 함수의 매개 변수의 인덱스입니다. </param>
      <param name="pCustData">모든 사용자 지정 데이터 항목을 보유한 CUSTDATA의 포인터입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetAllVarCustData(System.Int32,System.IntPtr)">
      <summary>사용자 지정 데이터에 대한 변수를 가져옵니다.</summary>
      <param name="index">사용자 지정 데이터를 가져올 변수의 인덱스입니다. </param>
      <param name="pCustData">모든 사용자 지정 데이터 항목을 보유한 CUSTDATA의 포인터입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetContainingTypeLib(System.Runtime.InteropServices.ComTypes.ITypeLib@,System.Int32@)">
      <summary>이 형식 설명과 해당 형식 라이브러리에 있는 인덱스를 포함하는 형식 라이브러리를 검색합니다.</summary>
      <param name="ppTLB">이 메서드는 반환될 때 포함하는 형식 라이브러리에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
      <param name="pIndex">이 메서드는 반환될 때 포함하는 형식 라이브러리에 있는 형식 설명의 인덱스에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetCustData(System.Guid@,System.Object@)">
      <summary>사용자 지정 데이터를 가져옵니다.</summary>
      <param name="guid">데이터를 식별하는 데 사용되는 GUID입니다. </param>
      <param name="pVarVal">이 메서드는 반환될 때 검색된 데이터를 넣을 위치를 지정하는 Object를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetDllEntry(System.Int32,System.Runtime.InteropServices.ComTypes.INVOKEKIND,System.IntPtr,System.IntPtr,System.IntPtr)">
      <summary>DLL에 있는 함수에 대한 진입점의 설명 또는 사양을 검색합니다.</summary>
      <param name="memid">DLL 항목 설명이 반환되는 멤버 함수의 ID입니다. </param>
      <param name="invKind">
        <paramref name="memid" />로 식별되는 멤버 종류를 지정하는 <see cref="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND" /> 값 중 하나입니다. </param>
      <param name="pBstrDllName">null이 아니면 함수는 DLL의 이름을 포함하는 BSTR에 <paramref name="pBstrDllName" />을 설정합니다. </param>
      <param name="pBstrName">null이 아니면 함수는 진입점의 이름을 포함하는 BSTR에 <paramref name="lpbstrName" />을 설정합니다. </param>
      <param name="pwOrdinal">null이 아니고 함수가 서수에 의해 정의되면 <paramref name="lpwOrdinal" />이 서수를 가리키도록 설정됩니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetDocumentation(System.Int32,System.String@,System.String@,System.Int32@,System.String@)">
      <summary>설명서 문자열, 전체 도움말 파일 이름과 경로, 지정된 형식 설명에 대한 도움말 항목의 컨텍스트 ID를 검색합니다.</summary>
      <param name="index">설명서가 반환되는 멤버의 ID입니다. </param>
      <param name="strName">이 메서드는 반환될 때 항목 메서드의 이름을 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
      <param name="strDocString">이 메서드가 반환될 때 지정된 항목에 대한 설명서 문자열을 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
      <param name="dwHelpContext">이 메서드는 반환될 때 지정한 항목과 연관된 도움말 컨텍스트에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
      <param name="strHelpFile">이 메서드는 반환될 때 도움말 파일의 정규화된 이름을 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetDocumentation2(System.Int32,System.String@,System.Int32@,System.String@)">
      <summary>설명서 문자열, 전체 도움말 파일 이름과 경로, 사용할 지역화 컨텍스트, 도움말 파일의 라이브러리 도움말 항목에 대한 컨텍스트 ID 등을 검색합니다.</summary>
      <param name="memid">형식 설명에 대한 멤버 식별자입니다. </param>
      <param name="pbstrHelpString">이 메서드는 반환될 때 지정한 항목의 이름이 들어 있는 BSTR을 포함합니다.호출자에 항목 이름이 필요하지 않은 경우 <paramref name="pbstrHelpString" />은 null일 수 있습니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
      <param name="pdwHelpStringContext">이 메서드가 반환될 때 도움말 지역화 컨텍스트를 포함합니다.호출자에 도움말 컨텍스트가 필요하지 않은 경우 <paramref name="pdwHelpStringContext" />는 null일 수 있습니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
      <param name="pbstrHelpStringDll">이 메서드는 반환될 때 도움말 파일에 사용되는 DLL이 들어 있는 파일의 정규화된 이름이 포함된 BSTR을 포함합니다.호출자에 파일 이름이 필요하지 않은 경우 <paramref name="pbstrHelpStringDll" />은 null일 수 있습니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetFuncCustData(System.Int32,System.Guid@,System.Object@)">
      <summary>지정한 함수에서 사용자 지정 데이터를 가져옵니다.</summary>
      <param name="index">사용자 지정 데이터를 가져올 함수의 인덱스입니다. </param>
      <param name="guid">데이터를 식별하는 데 사용되는 GUID입니다. </param>
      <param name="pVarVal">이 메서드는 반환될 때 해당 데이터를 넣을 위치를 지정하는 Object를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetFuncDesc(System.Int32,System.IntPtr@)">
      <summary>지정된 함수에 대한 정보가 들어 있는 <see cref="T:System.Runtime.InteropServices.FUNCDESC" /> 구조체를 검색합니다.</summary>
      <param name="index">반환할 함수 설명의 인덱스입니다. </param>
      <param name="ppFuncDesc">이 메서드는 반환될 때 지정한 함수를 설명하는 FUNCDESC 구조체에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetFuncIndexOfMemId(System.Int32,System.Runtime.InteropServices.ComTypes.INVOKEKIND,System.Int32@)">
      <summary>알려진 DISPID에 따라 멤버 이름을 알 수 없는 특정 멤버에 바인딩합니다(예: 기본 멤버에 바인딩).</summary>
      <param name="memid">멤버 식별자입니다. </param>
      <param name="invKind">memid로 식별되는 멤버 종류를 지정하는 <see cref="T:System.Runtime.InteropServices.ComTypes.INVOKEKIND" /> 값 중 하나입니다.</param>
      <param name="pFuncIndex">이 메서드는 반환될 때 함수에 대한 인덱스를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetIDsOfNames(System.String[],System.Int32,System.Int32[])">
      <summary>멤버 이름과 멤버 ID, 매개 변수 이름과 매개 변수 ID를 매핑합니다.</summary>
      <param name="rgszNames">매핑할 이름 배열입니다. </param>
      <param name="cNames">매핑할 이름의 수입니다. </param>
      <param name="pMemId">이 메서드는 반환될 때 이름 매핑이 저장될 배열에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetImplTypeCustData(System.Int32,System.Guid@,System.Object@)">
      <summary>사용자 지정 데이터의 구현 형식을 가져옵니다.</summary>
      <param name="index">사용자 지정 데이터에 대한 구현 형식의 인덱스입니다. </param>
      <param name="guid">데이터를 식별하는 데 사용되는 GUID입니다. </param>
      <param name="pVarVal">이 메서드는 반환될 때 검색된 데이터를 넣을 위치를 지정하는 Object를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetImplTypeFlags(System.Int32,System.Runtime.InteropServices.ComTypes.IMPLTYPEFLAGS@)">
      <summary>형식 설명의 구현된 인터페이스나 기본 인터페이스에 대한 <see cref="T:System.Runtime.InteropServices.IMPLTYPEFLAGS" /> 값을 검색합니다.</summary>
      <param name="index">구현된 인터페이스나 기본 인터페이스의 인덱스입니다. </param>
      <param name="pImplTypeFlags">이 메서드는 반환될 때 IMPLTYPEFLAGS 열거형에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetMops(System.Int32,System.String@)">
      <summary>마샬링 정보를 검색합니다.</summary>
      <param name="memid">마샬링 정보가 필요함을 나타내는 멤버 ID입니다. </param>
      <param name="pBstrMops">이 메서드는 반환될 때 참조된 형식 설명에 설명된 구조체의 필드를 마샬링하는 데 사용한 opcode 문자열에 대한 참조를 포함합니다. 반환할 정보가 없으면 null을 반환합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetNames(System.Int32,System.String[],System.Int32,System.Int32@)">
      <summary>지정한 함수 ID에 해당하는 지정한 멤버 ID를 가진 변수(속성, 메서드 및 해당 매개 변수의 이름)를 검색합니다.</summary>
      <param name="memid">이름 또는 이름들을 반환할 멤버의 ID입니다. </param>
      <param name="rgBstrNames">이 메서드는 반환되면, 멤버와 연관된 이름을 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
      <param name="cMaxNames">
        <paramref name="rgBstrNames" /> 배열의 길이입니다. </param>
      <param name="pcNames">이 메서드는 반환될 때 <paramref name="rgBstrNames" /> 배열에 이름 수를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetParamCustData(System.Int32,System.Int32,System.Guid@,System.Object@)">
      <summary>지정한 사용자 지정 데이터 매개 변수를 가져옵니다.</summary>
      <param name="indexFunc">사용자 지정 데이터를 가져올 함수의 인덱스입니다. </param>
      <param name="indexParam">사용자 지정 데이터를 가져올 이 함수의 매개 변수의 인덱스입니다. </param>
      <param name="guid">데이터를 식별하는 데 사용되는 GUID입니다. </param>
      <param name="pVarVal">이 메서드는 반환될 때 검색된 데이터를 넣을 위치를 지정하는 Object를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetRefTypeInfo(System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>형식 설명에서 다른 형식 설명을 참조할 경우 참조된 형식 설명을 검색합니다.</summary>
      <param name="hRef">반환할 참조된 형식 설명에 대한 핸들입니다. </param>
      <param name="ppTI">이 메서드는 반환될 때 참조된 형식 설명을 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetRefTypeOfImplType(System.Int32,System.Int32@)">
      <summary>형식 설명이 COM 클래스를 설명할 경우 구현된 인터페이스 형식에 대한 형식 설명을 검색합니다.</summary>
      <param name="index">핸들이 반환되는 구현된 형식의 인덱스입니다. </param>
      <param name="href">이 메서드는 반환될 때 구현된 인터페이스의 핸들에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetTypeAttr(System.IntPtr@)">
      <summary>형식 설명의 특성이 들어 있는 <see cref="T:System.Runtime.InteropServices.TYPEATTR" /> 구조체를 검색합니다.</summary>
      <param name="ppTypeAttr">이 메서드는 반환될 때 이 형식 설명의 특성을 포함하는 구조체에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetTypeComp(System.Runtime.InteropServices.ComTypes.ITypeComp@)">
      <summary>클라이언트 컴파일러가 형식 설명의 멤버를 바인딩할 수 있도록 하는 형식 설명에 대한 ITypeComp 인터페이스를 검색합니다.</summary>
      <param name="ppTComp">이 메서드는 반환될 때 포함하는 형식 라이브러리의 ITypeComp에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetTypeFlags(System.Int32@)">
      <summary>할당하지 않고 형식 플래그를 반환합니다.이 메서드는 TYPEATTR(형식 특성)를 증가시키지 않고 형식 플래그를 확장하는 DWORD 형식 플래그를 반환합니다.</summary>
      <param name="pTypeFlags">이 메서드는 반환될 때 TYPEFLAG에 대한 DWORD 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetTypeKind(System.Runtime.InteropServices.ComTypes.TYPEKIND@)">
      <summary>할당하지 않고 TYPEKIND 열거형을 신속하게 반환합니다.</summary>
      <param name="pTypeKind">이 메서드는 반환될 때 TYPEKIND 열거형에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetVarCustData(System.Int32,System.Guid@,System.Object@)">
      <summary>사용자 지정 데이터에 대한 변수를 가져옵니다.</summary>
      <param name="index">사용자 지정 데이터를 가져올 변수의 인덱스입니다. </param>
      <param name="guid">데이터를 식별하는 데 사용되는 GUID입니다. </param>
      <param name="pVarVal">이 메서드는 반환될 때 검색된 데이터를 넣을 위치를 지정하는 Object를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetVarDesc(System.Int32,System.IntPtr@)">
      <summary>지정된 변수를 설명하는 VARDESC 구조를 검색합니다.</summary>
      <param name="index">반환할 변수 설명의 인덱스입니다. </param>
      <param name="ppVarDesc">이 메서드는 반환될 때 지정한 변수를 설명하는 VARDESC 구조체에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.GetVarIndexOfMemId(System.Int32,System.Int32@)">
      <summary>알려진 DISPID에 따라 멤버 이름을 알 수 없는 특정 멤버에 바인딩합니다(예: 기본 멤버에 바인딩).</summary>
      <param name="memid">멤버 식별자입니다. </param>
      <param name="pVarIndex">이 메서드는 반환될 때 <paramref name="memid" />의 인덱스를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.Invoke(System.Object,System.Int32,System.Int16,System.Runtime.InteropServices.ComTypes.DISPPARAMS@,System.IntPtr,System.IntPtr,System.Int32@)">
      <summary>메서드를 호출하거나 개체의 속성에 액세스하여 형식 설명에서 설명한 인터페이스를 구현합니다.</summary>
      <param name="pvInstance">이 형식 설명에 설명된 인터페이스에 대한 참조입니다. </param>
      <param name="memid">인터페이스 멤버의 식별자입니다. </param>
      <param name="wFlags">Invoke 호출의 컨텍스트를 설명하는 플래그입니다. </param>
      <param name="pDispParams">인수의 배열, 명명된 인수에 대한 인수 DISPID의 배열 및 배열에 있는 요소의 개수가 들어 있는 구조체에 대한 참조입니다. </param>
      <param name="pVarResult">결과를 저장할 위치에 대한 참조입니다.<paramref name="wFlags" />가 DISPATCH_PROPERTYPUT 또는 DISPATCH_PROPERTYPUTREF를 지정하는 경우에는 <paramref name="pVarResult" />가 무시됩니다.결과가 필요하지 않으면 null로 설정합니다.</param>
      <param name="pExcepInfo">예외 정보 구조체에 대한 포인터로, DISP_E_EXCEPTION이 반환되는 경우에만 채워집니다. </param>
      <param name="puArgErr">Invoke가 DISP_E_TYPEMISMATCH를 반환하는 경우 <paramref name="puArgErr" />는 형식이 잘못된 인수의 인덱스를 나타냅니다.둘 이상의 인수가 오류를 반환하면 <paramref name="puArgErr" />는 오류를 포함하는 첫 번째 인수만 나타냅니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.ReleaseFuncDesc(System.IntPtr)">
      <summary>이전에 <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetFuncDesc(System.Int32,System.IntPtr@)" /> 메서드에서 반환한 <see cref="T:System.Runtime.InteropServices.FUNCDESC" /> 구조체를 해제합니다.</summary>
      <param name="pFuncDesc">해제할 FUNCDESC 구조체에 대한 참조입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.ReleaseTypeAttr(System.IntPtr)">
      <summary>이전에 <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetTypeAttr(System.IntPtr@)" /> 메서드에서 반환한 <see cref="T:System.Runtime.InteropServices.TYPEATTR" /> 구조체를 해제합니다.</summary>
      <param name="pTypeAttr">해제할 TYPEATTR 구조체에 대한 참조입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeInfo2.ReleaseVarDesc(System.IntPtr)">
      <summary>이전에 <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeInfo.GetVarDesc(System.Int32,System.IntPtr@)" /> 메서드에서 반환한 VARDESC 구조체를 해제합니다.</summary>
      <param name="pVarDesc">해제할 VARDESC 구조체에 대한 참조입니다. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ITypeLib">
      <summary>ITypeLib 인터페이스의 관리되는 정의를 제공합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.FindName(System.String,System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo[],System.Int32[],System.Int16@)">
      <summary>형식 라이브러리에 들어 있는 형식 설명의 항목을 찾습니다.</summary>
      <param name="szNameBuf">검색할 이름입니다.이는 in/out 매개 변수입니다.</param>
      <param name="lHashVal">검색 속도를 높이기 위한 해시 값이며 LHashValOfNameSys 함수에서 계산됩니다.<paramref name="lHashVal" />이 0이면 값이 계산됩니다.</param>
      <param name="ppTInfo">이 메서드는 반환될 때 <paramref name="szNameBuf" />에 지정된 이름을 포함하는 형식 설명에 대한 포인터의 배열을 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
      <param name="rgMemId">발견된 항목에 대한 MEMBERID의 배열 <paramref name="rgMemId" />[i]는 <paramref name="ppTInfo" />[i]에서 지정한 형식 설명으로 인덱스를 만드는 MEMBERID이며null일 수 없습니다.</param>
      <param name="pcFound">항목에서 찾을 인스턴스의 수를 나타냅니다.예를 들어, 첫 번째 항목을 찾기 위해 <paramref name="pcFound" /> = 1을 호출할 수 있습니다.이때 인스턴스를 하나 발견하면 검색을 중지합니다.종료할 때 발견한 인스턴스의 수를 알려 줍니다.<paramref name="pcFound" />의 in과 out의 값이 동일하면 이름을 포함하는 형식 설명이 더 많을 수 있습니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetDocumentation(System.Int32,System.String@,System.String@,System.Int32@,System.String@)">
      <summary>라이브러리의 설명서 문자열, 전체 도움말 파일 이름과 경로, 도움말 파일의 라이브러리 도움말 항목에 대한 컨텍스트 식별자를 검색합니다.</summary>
      <param name="index">반환될 설명서가 있는 형식 설명의 인덱스입니다. </param>
      <param name="strName">이 메서드는 반환될 때 지정한 항목의 이름을 나타내는 문자열을 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
      <param name="strDocString">이 메서드는 반환될 때 지정한 항목의 설명서 문자열을 나타내는 문자열을 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
      <param name="dwHelpContext">이 메서드는 반환될 때 지정한 항목과 연관된 도움말 컨텍스트 식별자를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
      <param name="strHelpFile">이 메서드는 반환될 때 도움말 파일의 정규화된 이름을 나타내는 문자열을 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetLibAttr(System.IntPtr@)">
      <summary>라이브러리의 특성이 들어 있는 구조를 검색합니다.</summary>
      <param name="ppTLibAttr">이 메서드는 반환될 때 라이브러리의 특성을 포함하는 구조체를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetTypeComp(System.Runtime.InteropServices.ComTypes.ITypeComp@)">
      <summary>클라이언트 컴파일러가 라이브러리의 형식, 변수, 상수 및 전역 함수를 바인딩할 수 있도록 합니다.</summary>
      <param name="ppTComp">이 메서드는 반환될 때 이 ITypeLib에 대한 ITypeComp 인스턴스의 인스턴스를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetTypeInfo(System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>라이브러리에서 지정된 형식 설명을 검색합니다.</summary>
      <param name="index">반환할 ITypeInfo 인터페이스의 인덱스입니다. </param>
      <param name="ppTI">이 메서드는 반환될 때 <paramref name="index" />가 참조하는 형식을 설명하는 ITypeInfo를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetTypeInfoCount">
      <summary>형식 라이브러리에 있는 형식 설명의 수를 반환합니다.</summary>
      <returns>형식 라이브러리에 있는 형식 설명의 수입니다.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetTypeInfoOfGuid(System.Guid@,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>지정된 GUID에 해당하는 형식 설명을 검색합니다.</summary>
      <param name="guid">형식 정보가 필요한 클래스 CLSID 또는 인터페이스 IID입니다. </param>
      <param name="ppTInfo">이 메서드는 반환될 때 요청한 ITypeInfo 인터페이스를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetTypeInfoType(System.Int32,System.Runtime.InteropServices.ComTypes.TYPEKIND@)">
      <summary>형식 설명의 형식을 검색합니다.</summary>
      <param name="index">형식 라이브러리에 있는 형식 설명의 인덱스입니다. </param>
      <param name="pTKind">이 메서드는 반환될 때 형식 설명의 TYPEKIND 열거형에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.IsName(System.String,System.Int32)">
      <summary>라이브러리에 설명되어 있는 형식이나 멤버의 이름이 전달된 문자열에 들어 있는지 여부를 나타냅니다.</summary>
      <returns>
        <paramref name="szNameBuf" />가 형식 라이브러리에 있으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="szNameBuf">테스트할 문자열입니다.이는 in/out 매개 변수입니다.</param>
      <param name="lHashVal">
        <paramref name="szNameBuf" />의 해시 값입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib.ReleaseTLibAttr(System.IntPtr)">
      <summary>
        <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetLibAttr(System.IntPtr@)" /> 메서드에서 처음 가져온 <see cref="T:System.Runtime.InteropServices.TYPELIBATTR" /> 구조체를 해제합니다.</summary>
      <param name="pTLibAttr">해제할 TLIBATTR 구조체입니다. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.ITypeLib2">
      <summary>ITypeLib2 인터페이스의 관리되는 정의를 제공합니다.</summary>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.FindName(System.String,System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo[],System.Int32[],System.Int16@)">
      <summary>형식 라이브러리에 들어 있는 형식 설명의 항목을 찾습니다.</summary>
      <param name="szNameBuf">검색할 이름입니다. </param>
      <param name="lHashVal">검색 속도를 높이기 위한 해시 값이며 LHashValOfNameSys 함수에서 계산됩니다.<paramref name="lHashVal" />이 0이면 값이 계산됩니다.</param>
      <param name="ppTInfo">이 메서드는 반환될 때 <paramref name="szNameBuf" />에 지정된 이름을 포함하는 형식 설명에 대한 포인터의 배열을 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
      <param name="rgMemId">이 메서드가 반환될 때 찾은 항목에 대한 MEMBERID의 배열을 포함합니다. <paramref name="rgMemId" />[i]는 <paramref name="ppTInfo" />[i]에서 지정한 형식 설명으로 인덱싱하는 MEMBERID입니다.이 매개 변수는 null일 수 없습니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
      <param name="pcFound">메서드가 시작될 때 참조로 전달되는 값으로, 찾을 인스턴스의 수를 나타냅니다.예를 들어, 첫 번째 항목을 찾기 위해 <paramref name="pcFound" /> = 1을 호출할 수 있습니다.이때 인스턴스를 하나 발견하면 검색을 중지합니다.종료할 때 발견한 인스턴스의 수를 알려 줍니다.<paramref name="pcFound" />의 in과 out의 값이 동일하면 이름을 포함하는 형식 설명이 더 많을 수 있습니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetAllCustData(System.IntPtr)">
      <summary>라이브러리에 대한 모든 사용자 지정 데이터 항목을 가져옵니다.</summary>
      <param name="pCustData">모든 사용자 지정 데이터 항목을 보유한 CUSTDATA의 포인터입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetCustData(System.Guid@,System.Object@)">
      <summary>사용자 지정 데이터를 가져옵니다.</summary>
      <param name="guid">데이터를 식별하는 데 사용되는 <see cref="T:System.Guid" />로, 참조로 전달됩니다. </param>
      <param name="pVarVal">이 메서드가 반환될 때 검색된 데이터를 넣을 위치를 지정하는 개체를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetDocumentation(System.Int32,System.String@,System.String@,System.Int32@,System.String@)">
      <summary>라이브러리의 설명서 문자열, 전체 도움말 파일 이름과 경로, 도움말 파일의 라이브러리 도움말 항목에 대한 컨텍스트 식별자를 검색합니다.</summary>
      <param name="index">설명서를 반환할 형식 설명의 인덱스입니다. </param>
      <param name="strName">이 메서드가 반환될 때 지정된 항목의 이름을 나타내는 문자열을 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
      <param name="strDocString">이 메서드가 반환될 때 지정된 항목에 대한 설명서 문자열을 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
      <param name="dwHelpContext">이 메서드는 반환될 때 지정한 항목과 연관된 도움말 컨텍스트 식별자를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
      <param name="strHelpFile">이 메서드가 반환될 때 도움말 파일의 정규화된 이름을 나타내는 문자열을 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetDocumentation2(System.Int32,System.String@,System.Int32@,System.String@)">
      <summary>라이브러리의 설명서 문자열, 전체 도움말 파일 이름과 경로, 사용할 지역화 컨텍스트, 도움말 파일의 라이브러리 도움말 항목에 대한 컨텍스트 ID 등을 검색합니다.</summary>
      <param name="index">설명서를 반환할 형식 설명의 인덱스입니다. <paramref name="index" />가 -1이면 라이브러리의 설명서가 반환됩니다. </param>
      <param name="pbstrHelpString">이 메서드가 반환될 때 지정된 항목의 이름을 나타내는 BSTR를 포함합니다.호출자에 항목 이름이 필요하지 않은 경우 <paramref name="pbstrHelpString" />은 null일 수 있습니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
      <param name="pdwHelpStringContext">이 메서드가 반환될 때 도움말 지역화 컨텍스트를 포함합니다.호출자에 도움말 컨텍스트가 필요하지 않은 경우 <paramref name="pdwHelpStringContext" />는 null일 수 있습니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
      <param name="pbstrHelpStringDll">이 메서드가 반환될 때 도움말 파일에 사용되는 DLL이 들어 있는 파일의 정규화된 이름을 나타내는 BSTR를 포함합니다.호출자에 파일 이름이 필요하지 않은 경우 <paramref name="pbstrHelpStringDll" />은 null일 수 있습니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetLibAttr(System.IntPtr@)">
      <summary>라이브러리의 특성이 들어 있는 구조를 검색합니다.</summary>
      <param name="ppTLibAttr">이 메서드는 반환될 때 라이브러리의 특성을 포함하는 구조체를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetLibStatistics(System.IntPtr,System.Int32@)">
      <summary>해시 테이블의 크기를 효율적으로 조정하는 데 필요한 형식 라이브러리에 대한 통계를 반환합니다.</summary>
      <param name="pcUniqueNames">고유 이름의 개수에 대한 포인터입니다.호출자가 이 정보를 필요로 하지 않으면 null로 설정합니다.</param>
      <param name="pcchUniqueNames">이 메서드가 반환될 때 고유 이름 개수의 변경 내용에 대한 포인터를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetTypeComp(System.Runtime.InteropServices.ComTypes.ITypeComp@)">
      <summary>클라이언트 컴파일러가 라이브러리의 형식, 변수, 상수 및 전역 함수를 바인딩할 수 있도록 합니다.</summary>
      <param name="ppTComp">이 메서드가 반환될 때 이 ITypeLib에 대한 ITypeComp 인스턴스를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetTypeInfo(System.Int32,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>라이브러리에서 지정된 형식 설명을 검색합니다.</summary>
      <param name="index">반환할 ITypeInfo 인터페이스의 인덱스입니다. </param>
      <param name="ppTI">이 메서드는 반환될 때 <paramref name="index" />가 참조하는 형식을 설명하는 ITypeInfo를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetTypeInfoCount">
      <summary>형식 라이브러리에 있는 형식 설명의 수를 반환합니다.</summary>
      <returns>형식 라이브러리에 있는 형식 설명의 수입니다.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetTypeInfoOfGuid(System.Guid@,System.Runtime.InteropServices.ComTypes.ITypeInfo@)">
      <summary>지정된 GUID에 해당하는 형식 설명을 검색합니다.</summary>
      <param name="guid">형식 정보가 필요한 클래스에 대한 CLSID의 인터페이스 IID를 나타내는 <see cref="T:System.Guid" />로, 참조로 전달됩니다. </param>
      <param name="ppTInfo">이 메서드는 반환될 때 요청한 ITypeInfo 인터페이스를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.GetTypeInfoType(System.Int32,System.Runtime.InteropServices.ComTypes.TYPEKIND@)">
      <summary>형식 설명의 형식을 검색합니다.</summary>
      <param name="index">형식 라이브러리에 있는 형식 설명의 인덱스입니다. </param>
      <param name="pTKind">이 메서드는 반환될 때 형식 설명의 TYPEKIND 열거형에 대한 참조를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.IsName(System.String,System.Int32)">
      <summary>라이브러리에 설명되어 있는 형식이나 멤버의 이름이 전달된 문자열에 들어 있는지 여부를 나타냅니다.</summary>
      <returns>
        <paramref name="szNameBuf" />가 형식 라이브러리에 있으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="szNameBuf">테스트할 문자열입니다. </param>
      <param name="lHashVal">
        <paramref name="szNameBuf" />의 해시 값입니다. </param>
    </member>
    <member name="M:System.Runtime.InteropServices.ComTypes.ITypeLib2.ReleaseTLibAttr(System.IntPtr)">
      <summary>
        <see cref="M:System.Runtime.InteropServices.ComTypes.ITypeLib.GetLibAttr(System.IntPtr@)" /> 메서드에서 처음 가져온 <see cref="T:System.Runtime.InteropServices.TYPELIBATTR" /> 구조체를 해제합니다.</summary>
      <param name="pTLibAttr">해제할 TLIBATTR 구조체입니다. </param>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.LIBFLAGS">
      <summary>형식 라이브러리에 해당하는 플래그를 정의합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.LIBFLAGS.LIBFLAG_FCONTROL">
      <summary>형식 라이브러리는 컨트롤을 설명하고 보이지 않는 개체용 형식 브라우저에 표시되지 않아야 합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.LIBFLAGS.LIBFLAG_FHASDISKIMAGE">
      <summary>형식 라이브러리는 디스크에서 지속된 형식으로 존재합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.LIBFLAGS.LIBFLAG_FHIDDEN">
      <summary>형식 라이브러리는 용도가 제한되어 있지 않지만 사용자에게 표시되어서는 안 됩니다.형식 라이브러리는 컨트롤에 의해 사용되어야 합니다.호스트는 확장된 속성을 사용하여 컨트롤을 래핑하는 새 형식 라이브러리를 만들어야 합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.LIBFLAGS.LIBFLAG_FRESTRICTED">
      <summary>형식 라이브러리는 제한되어 있으며, 사용자에게 표시되어서는 안 됩니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.PARAMDESC">
      <summary>구조체 요소, 매개 변수, 프로세스 간 함수 반환 값을 전달하는 방법에 대한 필요한 정보가 들어 있습니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMDESC.lpVarValue">
      <summary>프로세스 간에 전달되는 값에 대한 포인터를 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMDESC.wParamFlags">
      <summary>구조체 요소, 매개 변수 또는 반환 값을 설명하는 비트 마스크 값을 나타냅니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.PARAMFLAG">
      <summary>구조체 요소, 매개 변수 또는 함수 반환 값을 한 프로세스에서 다른 프로세스로 전송하는 방법을 설명합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FHASCUSTDATA">
      <summary>매개 변수에 사용자 지정 데이터가 있습니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FHASDEFAULT">
      <summary>매개 변수에 정의된 기본 동작이 있습니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FIN">
      <summary>매개 변수는 호출자에서 호출 수신자로 정보를 전달합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FLCID">
      <summary>매개 변수가 클라이언트 응용 프로그램의 로컬 식별자입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FOPT">
      <summary>매개 변수는 선택 사항입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FOUT">
      <summary>매개 변수가 호출 수신자에게서 호출자에게로 정보를 반환합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_FRETVAL">
      <summary>매개 변수가 멤버의 반환 값입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.PARAMFLAG.PARAMFLAG_NONE">
      <summary>매개 변수가 정보를 전달하는지 아니면 정보를 받는지 지정하지 않습니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.STATDATA">
      <summary>STATDATA 구조체의 관리되는 정의를 제공합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATDATA.advf">
      <summary>advise 싱크에서 데이터의 변경 내용에 대한 알림을 받을 시기를 결정하는 <see cref="T:System.Runtime.InteropServices.ComTypes.ADVF" /> 열거형 값을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATDATA.advSink">
      <summary>변경 알림을 받을 <see cref="T:System.Runtime.InteropServices.ComTypes.IAdviseSink" /> 인터페이스를 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATDATA.connection">
      <summary>advise 연결을 고유하게 식별하는 토큰을 나타냅니다.이 토큰은 advise 연결을 설정하는 메서드에 의해 반환됩니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATDATA.formatetc">
      <summary>advise 싱크에 대한 대상 데이터의 <see cref="T:System.Runtime.InteropServices.ComTypes.FORMATETC" /> 구조체를 나타냅니다.advise 싱크는 이 <see cref="T:System.Runtime.InteropServices.ComTypes.FORMATETC" /> 구조체에 지정된 데이터에 대한 변경 알림을 받습니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.STATSTG">
      <summary>열린 저장소, 스트림 또는 바이트 배열 개체에 관한 통계 정보를 포함합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.atime">
      <summary>이 저장소, 스트림 또는 바이트 배열에 대한 마지막 액세스 시간을 나타냅니다. </summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.cbSize">
      <summary>스트림 또는 바이트 배열의 크기를 바이트 단위로 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.clsid">
      <summary>저장소 개체에 대한 클래스 식별자를 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.ctime">
      <summary>이 저장소, 스트림 또는 바이트 배열을 만든 시간을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.grfLocksSupported">
      <summary>이 스트림 또는 바이트 배열이 지원하는 영역 잠금 형식을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.grfMode">
      <summary>개체가 열렸을때 명시된 액세스 모드를 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.grfStateBits">
      <summary>IStorage::SetStateBits 메서드가 설정한 가장 최근 값인 저장소 개체의 현재 상태를 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.mtime">
      <summary>이 저장소, 스트림 또는 바이트 배열에 대한 최종 수정 시간을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.pwcsName">
      <summary>이 구조체가 설명하는 개체의 이름이 들어 있으며 null로 끝나는 문자열에 대한 포인터를 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.reserved">
      <summary>다음에 사용하도록 예약됩니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STATSTG.type">
      <summary>STGTY 열거형의 값 중 하나인 저장소 개체의 형식을 나타냅니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM">
      <summary>STGMEDIUM 구조체의 관리되는 정의를 제공합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease">
      <summary>받는 프로세스가 ReleaseStgMedium 함수를 호출할 때 보내는 프로세스에서 저장소를 해제하는 방법을 제어할 수 있도록 하는 인터페이스 인스턴스에 대한 포인터를 나타냅니다.<see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" />가 null이면 ReleaseStgMedium에서는 기본 프로시저를 사용하여 저장소를 해제하고, 그렇지 않으면 ReleaseStgMedium에서는 지정된 IUnknown 인터페이스를 사용합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.tymed">
      <summary>저장 미디어 형식을 지정합니다.마샬링 및 역마샬링 루틴에서는 이 값을 사용하여 공용 구조체 멤버가 사용되었는지를 확인합니다.이 값은 <see cref="T:System.Runtime.InteropServices.ComTypes.TYMED" /> 열거형의 요소 중 하나여야 합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.unionmember">
      <summary>받는 프로세스에서 전송 중인 데이터에 액세스하는 데 사용할 수 있는 핸들, 문자열 또는 인터페이스 포인터를 나타냅니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.SYSKIND">
      <summary>대상 운영 체제 플랫폼을 식별합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.SYSKIND.SYS_MAC">
      <summary>형식 라이브러리에 대한 대상 운영 체제는 Apple Macintosh입니다.기본적으로 모든 데이터 필드는 짝수 바이트 경계로 맞추어집니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.SYSKIND.SYS_WIN16">
      <summary>형식 라이브러리에 대한 대상 운영 체제는 16비트 Windows 시스템입니다.기본적으로 데이터 필드는 패킹됩니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.SYSKIND.SYS_WIN32">
      <summary>형식 라이브러리에 대한 대상 운영 체제는 32비트 Windows 시스템입니다.기본적으로 데이터 필드는 저절로 맞추어지는데 예를 들면 2바이트 정수는 짝수 바이트 경계로, 4바이트 정수는 네 단어 경계 등으로 맞추어집니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.SYSKIND.SYS_WIN64">
      <summary>형식 라이브러리에 대한 대상 운영 체제는 64비트 Windows 시스템입니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYMED">
      <summary>TYMED 구조체의 관리되는 정의를 제공합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_ENHMF">
      <summary>저장 미디어가 확장 메타파일입니다.<see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /><see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> 멤버가 null이면 대상 프로세스에서 DeleteEnhMetaFile을 사용하여 비트맵을 삭제해야 합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_FILE">
      <summary>저장 미디어가 경로로 식별되는 디스크 파일입니다.STGMEDIUM<see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> 멤버가 null이면 대상 프로세스에서 OpenFile을 사용하여 파일을 삭제해야 합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_GDI">
      <summary>저장 미디어가 GDI(그래픽 장치 인터페이스) 구성 요소(HBITMAP)입니다.<see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /><see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> 멤버가 null이면 대상 프로세스에서 DeleteObject를 사용하여 비트맵을 삭제해야 합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_HGLOBAL">
      <summary>저장 미디어가 전역 메모리 핸들(HGLOBAL)입니다.이 전역 핸들은 GMEM_SHARE 플래그를 사용하여 할당합니다.<see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /><see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> 멤버가 null이면 대상 프로세스에서 GlobalFree를 사용하여 메모리를 해제해야 합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_ISTORAGE">
      <summary>저장 미디어가 IStorage 포인터로 식별되는 저장소 구성 요소입니다.데이터는 이 IStorage 인스턴스에 포함된 스트림 및 저장소에 있습니다.<see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /><see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> 멤버가 null이 아니면 대상 프로세스에서 IStorage::Release를 사용하여 저장소 구성 요소를 해제해야 합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_ISTREAM">
      <summary>저장 미디어가 IStream 포인터로 식별되는 스트림 개체입니다.이 데이터를 읽으려면 ISequentialStream::Read를 사용합니다.<see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /><see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> 멤버가 null이 아니면 대상 프로세스에서 IStream::Release를 사용하여 스트림 구성 요소를 해제해야 합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_MFPICT">
      <summary>저장 미디어가 메타파일(HMETAFILE)입니다.이 메타파일의 데이터에 액세스하려면 Windows 또는 WIN32 함수를 사용합니다.<see cref="T:System.Runtime.InteropServices.ComTypes.STGMEDIUM" /><see cref="F:System.Runtime.InteropServices.ComTypes.STGMEDIUM.pUnkForRelease" /> 멤버가 null이면 대상 프로세스에서 DeleteMetaFile을 사용하여 비트맵을 삭제해야 합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYMED.TYMED_NULL">
      <summary>전달 중인 데이터가 없습니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYPEATTR">
      <summary>UCOMITypeInfo의 특성을 포함합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cbAlignment">
      <summary>이 형식의 인스턴스에 대해 바이트 맞춤을 지정합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cbSizeInstance">
      <summary>이 형식의 인스턴스 크기입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cbSizeVft">
      <summary>이 형식의 VTBL(가상 메서드 테이블)의 크기입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cFuncs">
      <summary>이 구조에서 설명하는 인터페이스에 관한 함수의 수를 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cImplTypes">
      <summary>이 구조에서 설명하는 인터페이스에 대해 구현된 인터페이스의 수를 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.cVars">
      <summary>이 구조체에서 기술하는 인터페이스의 변수 및 데이터 필드 수를 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.dwReserved">
      <summary>다음에 사용하도록 예약됩니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.guid">
      <summary>형식 정보의 GUID입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.idldescType">
      <summary>설명된 형식의 IDL 특성입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.lcid">
      <summary>멤버 이름과 설명서 문자열의 로캘입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.lpstrSchema">
      <summary>다음에 사용하도록 예약됩니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.MEMBER_ID_NIL">
      <summary>
        <see cref="F:System.Runtime.InteropServices.TYPEATTR.memidConstructor" /> 및 <see cref="F:System.Runtime.InteropServices.TYPEATTR.memidDestructor" /> 필드에 사용된 상수입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.memidConstructor">
      <summary>생성자의 ID이거나, 없으면 <see cref="F:System.Runtime.InteropServices.TYPEATTR.MEMBER_ID_NIL" /> 입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.memidDestructor">
      <summary>소멸자의 ID이거나, 없으면 <see cref="F:System.Runtime.InteropServices.TYPEATTR.MEMBER_ID_NIL" /> 입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.tdescAlias">
      <summary>
        <see cref="F:System.Runtime.InteropServices.TYPEATTR.typekind" /> == <see cref="F:System.Runtime.InteropServices.TYPEKIND.TKIND_ALIAS" />이면 이 형식이 별칭인 형식을 지정합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.typekind">
      <summary>이 정보가 설명하는 형식을 설명하는 <see cref="T:System.Runtime.InteropServices.TYPEKIND" /> 값입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.wMajorVerNum">
      <summary>주 버전 번호입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.wMinorVerNum">
      <summary>부 버전 번호입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEATTR.wTypeFlags">
      <summary>이 정보를 설명하는 <see cref="T:System.Runtime.InteropServices.TYPEFLAGS" /> 값입니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYPEDESC">
      <summary>변수의 형식, 함수의 반환 형식 또는 함수 매개 변수의 형식을 설명합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEDESC.lpValue">
      <summary>변수가 VT_SAFEARRAY나 VT_PTR이면, lpValue 필드에 요소 형식을 지정하는 TYPEDESC에 대한 포인터가 들어 있습니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEDESC.vt">
      <summary>이 TYPEDESC가 설명한 항목에 대한 변형 형식을 나타냅니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYPEFLAGS">
      <summary>형식 설명의 속성과 특성을 정의합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FAGGREGATABLE">
      <summary>이 클래스는 집계를 지원합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FAPPOBJECT">
      <summary>Application 개체를 설명하는 형식 설명입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FCANCREATE">
      <summary>이 형식의 인스턴스는 ITypeInfo::CreateInstance가 만들 수 있습니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FCONTROL">
      <summary>이 형식은 다른 형식을 파생시킨 컨트롤이며, 사용자에게 표시되지 않아야 합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FDISPATCHABLE">
      <summary>이 인터페이스가 IDispatch에서 직접적이든 간접적이든 파생됨을 나타냅니다.이 플래그는 계산되므로 해당 플래그에 대한 ODL(Object Description Language)이 없습니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FDUAL">
      <summary>이 인터페이스는 IDispatch와 VTBL 바인딩을 모두 지원합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FHIDDEN">
      <summary>이 형식이 브라우저에 나타나면 안 됩니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FLICENSED">
      <summary>이 형식은 허가되었습니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FNONEXTENSIBLE">
      <summary>이 인터페이스는 런타임에서 멤버를 추가할 수 없습니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FOLEAUTOMATION">
      <summary>인터페이스에서 사용된 형식은 VTBL바인딩 지원을 포함하여 Automation과 완벽하게 호환됩니다.인터페이스에서 dual을 설정하면 이 플래그와 <see cref="F:System.Runtime.InteropServices.TYPEFLAGS.TYPEFLAG_FDUAL" />이 모두 설정됩니다.dispinterface에서는 이 플래그를 사용할 수 없습니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FPREDECLID">
      <summary>이 형식은 미리 정의됩니다.클라이언트 응용 프로그램은 이 특성을 가진 개체의 단일 인스턴스를 자동으로 만들어야 합니다.개체를 가리키는 변수의 이름은 개체의 클래스 이름과 동일합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FPROXY">
      <summary>인터페이스가 프록시/스텁 동적 연결 라이브러리를 사용할 것임을 나타냅니다.이 플래그는 형식 라이브러리가 등록 취소될 때에도 형식 라이브러리 프록시의 등록이 취소되지 않음을 지정합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FREPLACEABLE">
      <summary>이 개체는 IConnectionPointWithDefault를 지원하며 기본 동작을 수행합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FRESTRICTED">
      <summary>매크로 언어에서 액세스해서는 안 됩니다.이 플래그는 시스템 수준 형식 또는 형식 브라우저가 표시하지 않는 형식을 위한 것입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEFLAGS.TYPEFLAG_FREVERSEBIND">
      <summary>자식을 확인하기 전에 기본 인터페이스의 이름을 확인을 했는지를 나타냅니다. 이것은 기본 동작과 반대입니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYPEKIND">
      <summary>데이터와 함수의 여러 형식을 지정합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_ALIAS">
      <summary>다른 형식의 별칭인 형식입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_COCLASS">
      <summary>구현되는 구성 요소 인터페이스 집합입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_DISPATCH">
      <summary>IDispatch::Invoke를 통해 액세스할 수 있는 메서드와 속성 집합입니다.기본적으로 이중 인터페이스는 TKIND_DISPATCH를 반환합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_ENUM">
      <summary>열거자 집합입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_INTERFACE">
      <summary>가상 함수가 있는 형식입니다. 이때 모든 가상 함수는 순수 가상 함수입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_MAX">
      <summary>열거형 마커의 끝입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_MODULE">
      <summary>정적 함수와 데이터(예: DLL)만 포함할 수 있는 모듈입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_RECORD">
      <summary>메서드가 없는 구조체입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPEKIND.TKIND_UNION">
      <summary>오프셋 0이 있는 모든 멤버의 공용 구조체입니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.TYPELIBATTR">
      <summary>특정 형식 라이브러리를 식별하고 멤버 이름에 대한 지역화 지원을 제공합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.guid">
      <summary>형식 라이브러리의 전역 고유 라이브러리 ID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.lcid">
      <summary>형식 라이브러리의 로캘 ID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.syskind">
      <summary>형식 라이브러리의 대상 하드웨어 플랫폼을 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.wLibFlags">
      <summary>라이브러리 플래그를 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.wMajorVerNum">
      <summary>형식 라이브러리의 주 버전 번호를 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.TYPELIBATTR.wMinorVerNum">
      <summary>형식 라이브러리의 부 버전 번호를 나타냅니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.VARDESC">
      <summary>변수, 상수 또는 데이터 멤버를 설명합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.desc">
      <summary>변수에 대한 정보를 포함합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.elemdescVar">
      <summary>변수 형식을 포함합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.lpstrSchema">
      <summary>이 필드는 나중에 사용되도록 예약되어 있습니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.memid">
      <summary>변수의 멤버 ID를 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.varkind">
      <summary>변수를 마샬링하는 방법을 정의합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.wVarFlags">
      <summary>변수의 속성을 정의합니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.VARDESC.DESCUNION">
      <summary>변수에 대한 정보를 포함합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.DESCUNION.lpvarValue">
      <summary>기호화된 상수를 설명합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARDESC.DESCUNION.oInst">
      <summary>인스턴스에 있는 이 변수의 오프셋을 나타냅니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.VARFLAGS">
      <summary>변수의 속성을 정의하는 상수를 나타냅니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FBINDABLE">
      <summary>이 변수는 데이터 바인딩을 지원합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FDEFAULTBIND">
      <summary>변수는 개체를 가장 잘 표현하는 단일 속성입니다.형식 정보에 있는 유일한 변수가 이러한 특성을 가질 수 있습니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FDEFAULTCOLLELEM">
      <summary>컴파일러가 “abc” 형식에서 이름이 “xyz”인 멤버를 찾는 최적화를 허용합니다.이러한 멤버를 발견하여 기본 컬렉션의 요소에 대한 접근자 함수로써 플래그하면, 호출은 해당 멤버 함수로 생성됩니다.모듈에서 허용되지 않고 dispinterface와 인터페이스의 멤버에서 허용됩니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FDISPLAYBIND">
      <summary>해당 변수는 사용자에게 바인딩할 수 있는 것으로 표시됩니다.<see cref="F:System.Runtime.InteropServices.VARFLAGS.VARFLAG_FBINDABLE" />도 설정되어야 합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FHIDDEN">
      <summary>이 변수가 존재하고 있고 바인딩할 수 있지만 사용자의 브라우저에 표시되어서는 안 됩니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FIMMEDIATEBIND">
      <summary>변수는 별개의 바인딩 가능한 속성으로 매핑됩니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FNONBROWSABLE">
      <summary>이 변수는 속성 브라우저가 아니라 개체 브라우저에 나타납니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FREADONLY">
      <summary>변수로의 할당은 허용되지 않습니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FREPLACEABLE">
      <summary>인터페이스가 기본 동작을 사용하는 것으로 표시합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FREQUESTEDIT">
      <summary>설정 시 속성을 직접 변경하려고 하면 IPropertyNotifySink::OnRequestEdit이 호출됩니다.OnRequestEdit가 구현되면 변경 사항이 적용될지 결정됩니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FRESTRICTED">
      <summary>매크로 언어에서 변수에 액세스해서는 안 됩니다.이 플래그는 시스템 수준 변수 또는 형식 브라우저가 표시되지 않는 변수를 위한 플래그입니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FSOURCE">
      <summary>변수는 이벤트의 소스인 개체를 반환합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARFLAGS.VARFLAG_FUIDEFAULT">
      <summary>변수가 사용자 인터페이스에 기본적으로 표시됩니다.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.ComTypes.VARKIND">
      <summary>변수의 종류를 정의합니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARKIND.VAR_CONST">
      <summary>VARDESC 구조체는 기호화된 상수를 설명합니다.이 상수와 연결된 메모리는 없습니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARKIND.VAR_DISPATCH">
      <summary>IDispatch::Invoke를 통해서만 변수에 액세스할 수 있습니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARKIND.VAR_PERINSTANCE">
      <summary>변수가 형식의 필드 또는 멤버입니다.이 변수는 형식의 각 인스턴스 내에서 고정 오프셋 위치에 있습니다.</summary>
    </member>
    <member name="F:System.Runtime.InteropServices.ComTypes.VARKIND.VAR_STATIC">
      <summary>변수의 인스턴스가 하나만 있습니다.</summary>
    </member>
  </members>
</doc>