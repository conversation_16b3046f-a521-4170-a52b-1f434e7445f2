using System.Runtime.InteropServices;
using CTrue.FsConnect;

namespace FlightPig.Models
{
    /// <summary>
    /// Aircraft information structure for SimConnect
    /// </summary>
    [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Ansi, Pack = 1)]
    public struct AircraftInfo
    {
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 256)]
        public string Title;

        [SimVar(UnitId = FsUnit.Degree)]
        public double Latitude;

        [SimVar(UnitId = FsUnit.Degree)]
        public double Longitude;

        [SimVar(UnitId = FsUnit.Feet)]
        public double Altitude;

        [SimVar(UnitId = FsUnit.Degree)]
        public double Heading;

        [SimVar(NameId = FsSimVar.AirspeedTrue, UnitId = FsUnit.Knot)]
        public double AirspeedKnots;

        [SimVar(NameId = FsSimVar.GroundVelocity, UnitId = FsUnit.Knot)]
        public double GroundSpeedKnots;

        [SimVar(NameId = FsSimVar.VerticalSpeed, UnitId = FsUnit.FeetPerMinute)]
        public double VerticalSpeedFpm;

        [SimVar(NameId = FsSimVar.SimOnGround, UnitId = FsUnit.Bool)]
        public bool OnGround;

        [SimVar(NameId = FsSimVar.EngineType, UnitId = FsUnit.Enum)]
        public double EngineType;
    }
}
