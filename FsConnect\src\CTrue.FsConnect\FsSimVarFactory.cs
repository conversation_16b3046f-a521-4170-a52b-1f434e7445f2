﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace CTrue.FsConnect
{
    internal class FsSimVarInfo
    {
		public FsSimVar FsSimVarId { get; set; }
		public string SimVarName { get; set; }
        public string SimVarNameNoWhitespace { get; set; }
	}

	internal static class FsSimVarFactory
	{
		private static Dictionary<FsSimVar, FsSimVarInfo> _enumToCodeDictionary = new Dictionary<FsSimVar, FsSimVarInfo>();

        private static readonly string[] _simVarNames = new string[]
		{
			"NONE",
			"ABSOLUTE TIME",
			"ZULU TIME",
			"ZULU DAY OF WEEK",
			"ZULU DAY OF MONTH",
			"ZULU MONTH OF YEAR",
			"ZULU DAY OF YEAR",
			"ZULU YEAR",
			"LOCAL TIME",
			"LOCAL DAY OF WEEK",
			"LOCAL DAY OF MONTH",
			"LOCAL MONTH OF YEAR",
			"LOCAL DAY OF YEAR",
			"LOCAL YEAR",
			"TIME ZONE OFFSET",
			"TIME OF DAY",
			"SIMULATION RATE",
			"UNITS OF MEASURE",
			"ANGLE OF ATTACK INDICATOR",
			"GUN AMMO",
			"CANNON AMMO",
			"ROCKET AMMO",
			"BOMB AMMO",
			"LIGHT ON STATES",
			"LIGHT STATES",
			"LIGHT PANEL",
			"LIGHT STROBE",
			"LIGHT LANDING",
			"STROBE FLASH",
			"LIGHT TAXI",
			"LIGHT BEACON",
			"LIGHT NAV",
			"LIGHT LOGO",
			"LIGHT WING",
			"LIGHT RECOGNITION",
			"LIGHT CABIN",
			"LANDING LIGHT PBH",
			"LIGHT NAV ON",
			"LIGHT BEACON ON",
			"LIGHT LANDING ON",
			"LIGHT TAXI ON",
			"LIGHT STROBE ON",
			"LIGHT PANEL ON",
			"LIGHT RECOGNITION ON",
			"LIGHT WING ON",
			"LIGHT LOGO ON",
			"LIGHT CABIN ON",
			"LIGHT HEAD ON",
			"LIGHT BRAKE ON",
			"WHEEL RPM",
			"CENTER WHEEL RPM",
			"LEFT WHEEL RPM",
			"RIGHT WHEEL RPM",
			"AUX WHEEL RPM",
			"WHEEL ROTATION ANGLE",
			"CENTER WHEEL ROTATION ANGLE",
			"LEFT WHEEL ROTATION ANGLE",
			"RIGHT WHEEL ROTATION ANGLE",
			"AUX WHEEL ROTATION ANGLE",
			"SIGMA SQRT",
			"DYNAMIC PRESSURE",
			"TOTAL VELOCITY",
			"TOTAL WORLD VELOCITY",
			"GROUND VELOCITY",
			"SURFACE RELATIVE GROUND SPEED",
			"AIRSPEED TRUE",
			"AIRSPEED INDICATED",
			"AIRSPEED SELECT INDICATED OR TRUE",
			"AIRSPEED TRUE CALIBRATE",
			"AIRSPEED BARBER POLE",
			"AIRSPEED MACH",
			"VERTICAL SPEED",
			"VARIOMETER RATE",
			"VARIOMETER SWITCH",
			"MACH MAX OPERATE",
			"STALL WARNING",
			"OVERSPEED WARNING",
			"BARBER POLE MACH",
			"VELOCITY BODY X",
			"VELOCITY BODY Y",
			"VELOCITY BODY Z",
			"VELOCITY WORLD X",
			"VELOCITY WORLD Y",
			"VELOCITY WORLD Z",
			"RELATIVE WIND VELOCITY BODY X",
			"RELATIVE WIND VELOCITY BODY Y",
			"RELATIVE WIND VELOCITY BODY Z",
			"ACCELERATION WORLD X",
			"ACCELERATION WORLD Y",
			"ACCELERATION WORLD Z",
			"ACCELERATION BODY X",
			"ACCELERATION BODY Y",
			"ACCELERATION BODY Z",
			"ROTATION VELOCITY BODY X",
			"ROTATION VELOCITY BODY Y",
			"ROTATION VELOCITY BODY Z",
			"DESIGN SPEED VS0",
			"DESIGN SPEED VS1",
			"DESIGN SPEED VC",
			"DESIGN SPEED MIN ROTATION",
			"DESIGN SPEED CLIMB",
			"DESIGN CRUISE ALT",
			"DESIGN TAKEOFF SPEED",
			"AI CONTROLS",
			"DELEGATE CONTROLS TO AI",
			"MIN DRAG VELOCITY",
			"PLANE LATITUDE",
			"PLANE LONGITUDE",
			"PLANE ALTITUDE",
			"PLANE ALT ABOVE GROUND",
			"PLANE PITCH DEGREES",
			"PLANE BANK DEGREES",
			"PLANE HEADING DEGREES MAGNETIC",
			"PLANE HEADING DEGREES TRUE",
			"INDICATED ALTITUDE",
			"PRESSURE ALTITUDE",
			"KOHLSMAN SETTING MB",
			"KOHLSMAN SETTING HG",
			"ATTITUDE INDICATOR PITCH DEGREES",
			"ATTITUDE INDICATOR BANK DEGREES",
			"ATTITUDE BARS POSITION",
			"ATTITUDE CAGE",
			"MAGVAR",
			"WISKEY COMPASS INDICATION DEGREES",
			"MAGNETIC COMPASS",
			"PLANE HEADING DEGREES GYRO",
			"GYRO DRIFT ERROR",
			"DELTA HEADING RATE",
			"TURN INDICATOR RATE",
			"TURN INDICATOR SWITCH",
			"GROUND ALTITUDE",
			"SIM ON GROUND",
			"SIM SHOULD SET ON GROUND",
			"TURN COORDINATOR BALL",
			"YOKE Y POSITION",
			"YOKE Y INDICATOR",
			"YOKE X POSITION",
			"YOKE X INIDICATOR",
			"YOKE X INDICATOR",
			"AILERON POSITION",
			"RUDDER PEDAL POSITION",
			"RUDDER PEDAL INDICATOR",
			"RUDDER POSITION",
			"ELEVATOR POSITION",
			"ELEVATOR TRIM POSITION",
			"ELEVATOR TRIM INDICATOR",
			"ELEVATOR TRIM PCT",
			"BRAKE LEFT POSITION",
			"BRAKE RIGHT POSITION",
			"BRAKE INDICATOR",
			"BRAKE PARKING POSITION",
			"BRAKE PARKING INDICATOR",
			"BRAKE DEPENDENT HYDRAULIC PRESSURE",
			"SPOILERS ARMED",
			"SPOILERS HANDLE POSITION",
			"SPOILERS LEFT POSITION",
			"SPOILERS RIGHT POSITION",
			"FLY BY WIRE ELAC SWITCH",
			"FLY BY WIRE FAC SWITCH",
			"FLY BY WIRE SEC SWITCH",
			"FLY BY WIRE ELAC FAILED",
			"FLY BY WIRE FAC FAILED",
			"FLY BY WIRE SEC FAILED",
			"FLY BY WIRE ALPHA PROTECTION",
			"FLAPS NUM HANDLE POSITIONS",
			"FLAPS HANDLE PERCENT",
			"FLAPS HANDLE INDEX",
			"TRAILING EDGE FLAPS LEFT PERCENT",
			"TRAILING EDGE FLAPS RIGHT PERCENT",
			"LEADING EDGE FLAPS LEFT PERCENT",
			"LEADING EDGE FLAPS RIGHT PERCENT",
			"TRAILING EDGE FLAPS LEFT ANGLE",
			"TRAILING EDGE FLAPS RIGHT ANGLE",
			"LEADING EDGE FLAPS LEFT ANGLE",
			"LEADING EDGE FLAPS RIGHT ANGLE",
			"FLAP POSITION SET",
			"IS GEAR RETRACTABLE",
			"IS GEAR WHEELS",
			"IS GEAR SKIS",
			"IS GEAR FLOATS",
			"IS GEAR SKIDS",
			"GEAR HANDLE POSITION",
			"GEAR EMERGENCY HANDLE POSITION",
			"GEAR CENTER POSITION",
			"GEAR LEFT POSITION",
			"GEAR RIGHT POSITION",
			"GEAR TAIL POSITION",
			"GEAR AUX POSITION",
			"GEAR POSITION",
			"GEAR ANIMATION POSITION",
			"GEAR TOTAL PCT EXTENDED",
			"GEAR WARNING",
			"TAILWHEEL LOCK ON",
			"NOSEWHEEL LOCK ON",
			"COWL FLAPS",
			"AVIONICS MASTER SWITCH",
			"PANEL AUTO FEATHER SWITCH",
			"PANEL ANTI ICE SWITCH",
			"AUTO BRAKE SWITCH CB",
			"ANTISKID BRAKES ACTIVE",
			"WATER RUDDER HANDLE POSITION",
			"WATER LEFT RUDDER EXTENDED",
			"WATER RIGHT RUDDER EXTENDED",
			"RETRACT FLOAT SWITCH",
			"RETRACT LEFT FLOAT EXTENDED",
			"RETRACT RIGHT FLOAT EXTENDED",
			"GEAR CENTER STEER ANGLE",
			"GEAR LEFT STEER ANGLE",
			"GEAR RIGHT STEER ANGLE",
			"GEAR AUX STEER ANGLE",
			"GEAR STEER ANGLE",
			"WATER LEFT RUDDER STEER ANGLE",
			"WATER RIGHT RUDDER STEER ANGLE",
			"GEAR CENTER STEER ANGLE PCT",
			"GEAR LEFT STEER ANGLE PCT",
			"GEAR RIGHT STEER ANGLE PCT",
			"GEAR AUX STEER ANGLE PCT",
			"GEAR STEER ANGLE PCT",
			"WATER LEFT RUDDER STEER ANGLE PCT",
			"WATER RIGHT RUDDER STEER ANGLE PCT",
			"STEER INPUT CONTROL",
			"ELEVATOR DEFLECTION",
			"ELEVATOR DEFLECTION PCT",
			"AILERON LEFT DEFLECTION",
			"AILERON LEFT DEFLECTION PCT",
			"AILERON RIGHT DEFLECTION",
			"AILERON RIGHT DEFLECTION PCT",
			"AILERON AVERAGE DEFLECTION",
			"AILERON TRIM",
			"AILERON TRIM PCT",
			"RUDDER DEFLECTION",
			"RUDDER DEFLECTION PCT",
			"RUDDER TRIM",
			"RUDDER TRIM PCT",
			"WING FLEX PCT",
			"WING AREA",
			"WING SPAN",
			"PROP SYNC ACTIVE",
			"INCIDENCE ALPHA",
			"INCIDENCE BETA",
			"BETA DOT",
			"LINEAR CL ALPHA",
			"STALL ALPHA",
			"ZERO LIFT ALPHA",
			"CG PERCENT",
			"CG PERCENT LATERAL",
			"CG AFT LIMIT",
			"CG FWD LIMIT",
			"CG MAX MACH",
			"CG MIN MACH",
			"PAYLOAD STATION WEIGHT",
			"PAYLOAD STATION NAME",
			"PAYLOAD STATION COUNT",
			"PAYLOAD STATION OBJECT",
			"PAYLOAD STATION NUM SIMOBJECTS",
			"ELEVON DEFLECTION",
			"FOLDING WING LEFT PERCENT",
			"FOLDING WING RIGHT PERCENT",
			"FOLDING WING HANDLE POSITION",
			"CANOPY OPEN",
			"TAILHOOK POSITION",
			"TAILHOOK HANDLE",
			"LAUNCHBAR POSITION",
			"LAUNCHBAR SWITCH",
			"LAUNCHBAR HELD EXTENDED",
			"EXIT OPEN",
			"EXIT TYPE",
			"EXIT POSX",
			"EXIT POSY",
			"EXIT POSZ",
			"RADIO HEIGHT",
			"DECISION HEIGHT",
			"DECISION ALTITUDE MSL",
			"TOTAL WEIGHT",
			"MAX GROSS WEIGHT",
			"EMPTY WEIGHT",
			"EMPTY WEIGHT PITCH MOI",
			"EMPTY WEIGHT ROLL MOI",
			"EMPTY WEIGHT YAW MOI",
			"EMPTY WEIGHT CROSS COUPLED MOI",
			"TOTAL WEIGHT PITCH MOI",
			"TOTAL WEIGHT ROLL MOI",
			"TOTAL WEIGHT YAW MOI",
			"TOTAL WEIGHT CROSS COUPLED MOI",
			"WATER BALLAST VALVE",
			"AUTOPILOT MASTER",
			"AUTOPILOT WING LEVELER",
			"AUTOPILOT NAV1 LOCK",
			"AUTOPILOT HEADING LOCK",
			"AUTOPILOT HEADING LOCK DIR",
			"AUTOPILOT ALTITUDE LOCK",
			"AUTOPILOT ALTITUDE LOCK VAR",
			"AUTOPILOT ATTITUDE HOLD",
			"AUTOPILOT GLIDESLOPE HOLD",
			"AUTOPILOT APPROACH HOLD",
			"AUTOPILOT BACKCOURSE HOLD",
			"AUTOPILOT YAW DAMPER",
			"AUTOPILOT AIRSPEED HOLD",
			"AUTOPILOT AIRSPEED HOLD VAR",
			"AUTOPILOT MACH HOLD",
			"AUTOPILOT MACH HOLD VAR",
			"AUTOPILOT VERTICAL HOLD",
			"AUTOPILOT VERTICAL HOLD VAR",
			"AUTOPILOT ALTITUDE MANUALLY TUNABLE",
			"AUTOPILOT HEADING MANUALLY TUNABLE",
			"AUTOPILOT THROTTLE ARM",
			"AUTOPILOT TAKEOFF POWER ACTIVE",
			"AUTOPILOT RPM HOLD",
			"AUTOPILOT RPM HOLD VAR",
			"AUTOPILOT SPEED SETTING",
			"AUTOPILOT AIRSPEED ACQUISITION",
			"AUTOPILOT AIRSPEED HOLD CURRENT",
			"AUTOPILOT MAX SPEED HOLD",
			"AUTOPILOT CRUISE SPEED HOLD",
			"AUTOPILOT FLIGHT DIRECTOR ACTIVE",
			"AUTOPILOT FLIGHT DIRECTOR PITCH",
			"AUTOPILOT FLIGHT DIRECTOR BANK",
			"AUTOPILOT PITCH HOLD",
			"AUTOPILOT PITCH HOLD REF",
			"AUTOPILOT NAV SELECTED",
			"GPS DRIVES NAV1",
			"AUTOTHROTTLE ACTIVE",
			"AUTOPILOT MAX BANK",
			"NUMBER OF CATAPULTS",
			"HOLDBACK BAR INSTALLED",
			"BLAST SHIELD POSITION",
			"CATAPULT STROKE POSITION",
			"ENGINE CONTROL SELECT",
			"NUMBER OF ENGINES",
			"MAX RATED ENGINE RPM",
			"PROPELLER ADVANCED SELECTION",
			"THROTTLE LOWER LIMIT",
			"OIL AMOUNT",
			"ENGINE PRIMER",
			"ENGINE TYPE",
			"ENG RPM ANIMATION PERCENT",
			"FULL THROTTLE THRUST TO WEIGHT RATIO",
			"PROP RPM",
			"PROP MAX RPM PERCENT",
			"PROP THRUST",
			"PROP BETA",
			"PROP FEATHERING INHIBIT",
			"PROP FEATHERED",
			"PROP SYNC DELTA LEVER",
			"PROP AUTO FEATHER ARMED",
			"PROP FEATHER SWITCH",
			"PROP AUTO CRUISE ACTIVE",
			"PROP ROTATION ANGLE",
			"PROP BETA MAX",
			"PROP BETA MIN",
			"PROP BETA MIN REVERSE",
			"MASTER IGNITION SWITCH",
			"ENG COMBUSTION",
			"OLD ENG STARTER",
			"ENG N1 RPM",
			"ENG N2 RPM",
			"ENG FUEL FLOW GPH",
			"ENG FUEL FLOW PPH",
			"ENG FUEL FLOW PPH SSL",
			"ENG TORQUE",
			"ENG ANTI ICE",
			"ENG PRESSURE RATIO",
			"ENG PRESSURE RATIO GES",
			"ENG EXHAUST GAS TEMPERATURE",
			"ENG EXHAUST GAS TEMPERATURE GES",
			"ENG CYLINDER HEAD TEMPERATURE",
			"ENG OIL TEMPERATURE",
			"ENG OIL PRESSURE",
			"ENG OIL QUANTITY",
			"ENG HYDRAULIC PRESSURE",
			"ENG HYDRAULIC QUANTITY",
			"ENG MANIFOLD PRESSURE",
			"ENG VIBRATION",
			"ENG RPM SCALER",
			"ENG TURBINE TEMPERATURE",
			"ENG TORQUE PERCENT",
			"ENG FUEL PRESSURE",
			"ENG ELECTRICAL LOAD",
			"ENG TRANSMISSION PRESSURE",
			"ENG TRANSMISSION TEMPERATURE",
			"ENG ROTOR RPM",
			"ENG FUEL FLOW BUG POSITION",
			"ENG MAX RPM",
			"ENG ON FIRE",
			"GENERAL ENG COMBUSTION",
			"GENERAL ENG MASTER ALTERNATOR",
			"GENERAL ENG FUEL PUMP SWITCH",
			"GENERAL ENG FUEL PUMP ON",
			"GENERAL ENG RPM",
			"GENERAL ENG PCT MAX RPM",
			"GENERAL ENG MAX REACHED RPM",
			"GENERAL ENG THROTTLE LEVER POSITION",
			"GENERAL ENG MIXTURE LEVER POSITION",
			"GENERAL ENG PROPELLER LEVER POSITION",
			"GENERAL ENG STARTER",
			"GENERAL ENG STARTER ACTIVE",
			"GENERAL ENG EXHAUST GAS TEMPERATURE",
			"GENERAL ENG OIL PRESSURE",
			"GENERAL ENG OIL LEAKED PERCENT",
			"GENERAL ENG COMBUSTION SOUND PERCENT",
			"GENERAL ENG DAMAGE PERCENT",
			"GENERAL ENG OIL TEMPERATURE",
			"GENERAL ENG FAILED",
			"GENERAL ENG GENERATOR SWITCH",
			"GENERAL ENG GENERATOR ACTIVE",
			"GENERAL ENG ANTI ICE POSITION",
			"GENERAL ENG FUEL VALVE",
			"GENERAL ENG FUEL PRESSURE",
			"GENERAL ENG ELAPSED TIME",
			"GENERAL ENG FIRE DETECTED",
			"GENERAL ENG FUEL USED SINCE START",
			"RECIP ENG COWL FLAP POSITION",
			"RECIP ENG PRIMER",
			"RECIP ENG MANIFOLD PRESSURE",
			"RECIP ENG ALTERNATE AIR POSITION",
			"RECIP ENG COOLANT RESERVOIR PERCENT",
			"RECIP ENG LEFT MAGNETO",
			"RECIP ENG RIGHT MAGNETO",
			"RECIP ENG BRAKE POWER",
			"RECIP ENG STARTER TORQUE",
			"RECIP ENG TURBOCHARGER FAILED",
			"RECIP ENG EMERGENCY BOOST ACTIVE",
			"RECIP ENG EMERGENCY BOOST ELAPSED TIME",
			"RECIP ENG WASTEGATE POSITION",
			"RECIP ENG TURBINE INLET TEMPERATURE",
			"RECIP ENG CYLINDER HEAD TEMPERATURE",
			"RECIP ENG RADIATOR TEMPERATURE",
			"RECIP ENG FUEL AVAILABLE",
			"RECIP ENG FUEL FLOW",
			"RECIP ENG FUEL TANK SELECTOR",
			"RECIP ENG FUEL TANKS USED",
			"RECIP ENG FUEL NUMBER TANKS USED",
			"RECIP ENG DETONATING",
			"RECIP ENG CYLINDER HEALTH",
			"RECIP ENG NUM CYLINDERS",
			"RECIP ENG NUM CYLINDERS FAILED",
			"RECIP CARBURETOR TEMPERATURE",
			"RECIP MIXTURE RATIO",
			"RECIP ENG ANTIDETONATION TANK VALVE",
			"RECIP ENG ANTIDETONATION TANK QUANTITY",
			"RECIP ENG ANTIDETONATION TANK MAX QUANTITY",
			"RECIP ENG NITROUS TANK VALVE",
			"RECIP ENG NITROUS TANK QUANTITY",
			"RECIP ENG NITROUS TANK MAX QUANTITY",
			"TURB ENG N1",
			"TURB ENG N2",
			"TURB ENG CORRECTED N1",
			"TURB ENG CORRECTED N2",
			"TURB ENG CORRECTED FF",
			"TURB ENG MAX TORQUE PERCENT",
			"TURB ENG PRESSURE RATIO",
			"TURB ENG ITT",
			"TURB ENG AFTERBURNER",
			"TURB ENG AFTERBURNER STAGE ACTIVE",
			"TURB ENG AFTERBURNER PCT ACTIVE",
			"TURB ENG JET THRUST",
			"TURB ENG BLEED AIR",
			"TURB ENG TANK SELECTOR",
			"TURB ENG TANKS USED",
			"TURB ENG NUM TANKS USED",
			"TURB ENG FUEL FLOW PPH",
			"TURB ENG FUEL AVAILABLE",
			"TURB ENG PRIMARY NOZZLE PERCENT",
			"TURB ENG REVERSE NOZZLE PERCENT",
			"TURB ENG VIBRATION",
			"TURB ENG IGNITION SWITCH",
			"TURB ENG MASTER STARTER SWITCH",
			"ENG FAILED",
			"PARTIAL PANEL ADF",
			"PARTIAL PANEL AIRSPEED",
			"PARTIAL PANEL ALTIMETER",
			"PARTIAL PANEL ATTITUDE",
			"PARTIAL PANEL COMM",
			"PARTIAL PANEL COMPASS",
			"PARTIAL PANEL ELECTRICAL",
			"PARTIAL PANEL AVIONICS",
			"PARTIAL PANEL ENGINE",
			"PARTIAL PANEL FUEL INDICATOR",
			"PARTIAL PANEL HEADING",
			"PARTIAL PANEL VERTICAL VELOCITY",
			"PARTIAL PANEL TRANSPONDER",
			"PARTIAL PANEL NAV",
			"PARTIAL PANEL PITOT",
			"PARTIAL PANEL TURN COORDINATOR",
			"PARTIAL PANEL VACUUM",
			"FUEL TANK CENTER LEVEL",
			"FUEL TANK CENTER CAPACITY",
			"FUEL TANK CENTER QUANTITY",
			"FUEL TANK CENTER2 LEVEL",
			"FUEL TANK CENTER2 CAPACITY",
			"FUEL TANK CENTER2 QUANTITY",
			"FUEL TANK CENTER3 LEVEL",
			"FUEL TANK CENTER3 CAPACITY",
			"FUEL TANK CENTER3 QUANTITY",
			"FUEL TANK LEFT MAIN LEVEL",
			"FUEL TANK LEFT MAIN CAPACITY",
			"FUEL TANK LEFT MAIN QUANTITY",
			"FUEL TANK LEFT AUX LEVEL",
			"FUEL TANK LEFT AUX CAPACITY",
			"FUEL TANK LEFT AUX QUANTITY",
			"FUEL TANK LEFT TIP LEVEL",
			"FUEL TANK LEFT TIP CAPACITY",
			"FUEL TANK LEFT TIP QUANTITY",
			"FUEL LEFT QUANTITY",
			"FUEL TANK RIGHT MAIN LEVEL",
			"FUEL TANK RIGHT MAIN CAPACITY",
			"FUEL TANK RIGHT MAIN QUANTITY",
			"FUEL TANK RIGHT AUX LEVEL",
			"FUEL TANK RIGHT AUX CAPACITY",
			"FUEL TANK RIGHT AUX QUANTITY",
			"FUEL TANK RIGHT TIP LEVEL",
			"FUEL TANK RIGHT TIP CAPACITY",
			"FUEL TANK RIGHT TIP QUANTITY",
			"FUEL RIGHT QUANTITY",
			"FUEL TANK EXTERNAL1 LEVEL",
			"FUEL TANK EXTERNAL1 CAPACITY",
			"FUEL TANK EXTERNAL1 QUANTITY",
			"FUEL TANK EXTERNAL2 LEVEL",
			"FUEL TANK EXTERNAL2 CAPACITY",
			"FUEL TANK EXTERNAL2 QUANTITY",
			"FUEL TOTAL QUANTITY",
			"FUEL TOTAL CAPACITY",
			"FUEL LEFT CAPACITY",
			"FUEL RIGHT CAPACITY",
			"FUEL WEIGHT PER GALLON",
			"FUEL TANK SELECTOR",
			"FUEL CROSS FEED",
			"NUM FUEL SELECTORS",
			"FUEL SELECTED QUANTITY PERCENT",
			"FUEL SELECTED QUANTITY",
			"FUEL TOTAL QUANTITY WEIGHT",
			"FUEL SELECTED TRANSFER MODE",
			"FUEL DUMP SWITCH",
			"FUEL DUMP ACTIVE",
			"DROPPABLE OBJECTS COUNT",
			"DROPPABLE OBJECTS TYPE",
			"DROPPABLE OBJECTS UI NAME",
			"WARNING FUEL",
			"WARNING FUEL LEFT",
			"WARNING FUEL RIGHT",
			"WARNING VACUUM",
			"WARNING VACUUM LEFT",
			"WARNING VACUUM RIGHT",
			"WARNING OIL PRESSURE",
			"WARNING VOLTAGE",
			"WARNING LOW HEIGHT",
			"AUTOPILOT AVAILABLE",
			"FLAPS AVAILABLE",
			"STALL HORN AVAILABLE",
			"ENGINE MIXURE AVAILABLE",
			"CARB HEAT AVAILABLE",
			"SPOILER AVAILABLE",
			"STROBES AVAILABLE",
			"PROP TYPE AVAILABLE",
			"TOE BRAKES AVAILABLE",
			"IS TAIL DRAGGER",
			"SYSTEMS AVAILABLE",
			"INSTRUMENTS AVAILABLE",
			"FUEL PUMP",
			"MANUAL FUEL PUMP HANDLE",
			"ALTERNATE STATIC SOURCE OPEN",
			"BLEED AIR SOURCE CONTROL",
			"ELECTRICAL MASTER BATTERY",
			"ELECTRICAL OLD CHARGING AMPS",
			"ELECTRICAL TOTAL LOAD AMPS",
			"ELECTRICAL BATTERY LOAD",
			"ELECTRICAL BATTERY VOLTAGE",
			"ELECTRICAL MAIN BUS VOLTAGE",
			"ELECTRICAL MAIN BUS AMPS",
			"ELECTRICAL AVIONICS BUS VOLTAGE",
			"ELECTRICAL AVIONICS BUS AMPS",
			"ELECTRICAL HOT BATTERY BUS VOLTAGE",
			"ELECTRICAL HOT BATTERY BUS AMPS",
			"ELECTRICAL BATTERY BUS VOLTAGE",
			"ELECTRICAL BATTERY BUS AMPS",
			"ELECTRICAL GENALT BUS VOLTAGE",
			"ELECTRICAL GENALT BUS AMPS",
			"CIRCUIT GENERAL PANEL ON",
			"CIRCUIT FLAP MOTOR ON",
			"CIRCUIT GEAR MOTOR ON",
			"CIRCUIT AUTOPILOT ON",
			"CIRCUIT AVIONICS ON",
			"CIRCUIT PITOT HEAT ON",
			"CIRCUIT PROP SYNC ON",
			"CIRCUIT AUTO FEATHER ON",
			"CIRCUIT AUTO BRAKES ON",
			"CIRCUIT STANDY VACUUM ON",
			"CIRCUIT STANDBY VACUUM ON",
			"CIRCUIT MARKER BEACON ON",
			"CIRCUIT GEAR WARNING ON",
			"CIRCUIT HYDRAULIC PUMP ON",
			"AMBIENT DENSITY",
			"AMBIENT TEMPERATURE",
			"AMBIENT PRESSURE",
			"AMBIENT WIND VELOCITY",
			"AMBIENT WIND DIRECTION",
			"AMBIENT WIND X",
			"AMBIENT WIND Y",
			"AMBIENT WIND Z",
			"AMBIENT PRECIP STATE",
			"AMBIENT IN CLOUD",
			"AMBIENT VISIBILITY",
			"BAROMETER PRESSURE",
			"SEA LEVEL PRESSURE",
			"TOTAL AIR TEMPERATURE",
			"STANDARD ATM TEMPERATURE",
			"AIRCRAFT WIND X",
			"AIRCRAFT WIND Y",
			"AIRCRAFT WIND Z",
			"HYDRAULIC PRESSURE",
			"HYDRAULIC RESERVOIR PERCENT",
			"HYDRAULIC SYSTEM INTEGRITY",
			"HYDRAULIC SWITCH",
			"GEAR HYDRAULIC PRESSURE",
			"CONCORDE VISOR NOSE HANDLE",
			"CONCORDE VISOR POSITION PERCENT",
			"CONCORDE NOSE ANGLE",
			"RADIOS AVAILABLE",
			"COM TRANSMIT",
			"COM RECEIVE ALL",
			"COM RECIEVE ALL",
			"NAV SOUND",
			"DME SOUND",
			"ADF SOUND",
			"ADF CARD",
			"MARKER SOUND",
			"COM AVAILABLE",
			"COM ACTIVE FREQUENCY",
			"COM STANDBY FREQUENCY",
			"COM STATUS",
			"COM TEST",
			"TRANSPONDER AVAILABLE",
			"TRANSPONDER CODE",
			"ADF AVAILABLE",
			"ADF FREQUENCY",
			"ADF EXT FREQUENCY",
			"ADF ACTIVE FREQUENCY",
			"ADF STANDBY FREQUENCY",
			"ADF LATLONALT",
			"ADF SIGNAL",
			"ADF RADIAL",
			"ADF IDENT",
			"ADF NAME",
			"NAV AVAILABLE",
			"NAV ACTIVE FREQUENCY",
			"NAV STANDBY FREQUENCY",
			"NAV SIGNAL",
			"NAV IDENT",
			"NAV NAME",
			"NAV CODES",
			"NAV HAS NAV",
			"NAV HAS LOCALIZER",
			"NAV HAS DME",
			"NAV HAS GLIDE SLOPE",
			"NAV BACK COURSE FLAGS",
			"NAV MAGVAR",
			"NAV RADIAL",
			"NAV RADIAL ERROR",
			"NAV LOCALIZER",
			"NAV GLIDE SLOPE",
			"NAV GLIDE SLOPE ERROR",
			"NAV CDI",
			"NAV GSI",
			"NAV TOFROM",
			"NAV GS FLAG",
			"NAV OBS",
			"NAV DME",
			"NAV DMESPEED",
			"NAV VOR LATLONALT",
			"NAV GS LATLONALT",
			"NAV DME LATLONALT",
			"NAV RELATIVE BEARING TO STATION",
			"MARKER BEACON STATE",
			"INNER MARKER",
			"MIDDLE MARKER",
			"OUTER MARKER",
			"INNER MARKER LATLONALT",
			"MIDDLE MARKER LATLONALT",
			"OUTER MARKER LATLONALT",
			"SELECTED DME",
			"REALISM",
			"AUTO COORDINATION",
			"UNLIMITED FUEL",
			"REALISM CRASH WITH OTHERS",
			"REALISM CRASH DETECTION",
			"MANUAL INSTRUMENT LIGHTS",
			"TRUE AIRSPEED SELECTED",
			"ATC TYPE",
			"ATC MODEL",
			"ATC HEAVY",
			"ATC ID",
			"ATC AIRLINE",
			"ATC FLIGHT NUMBER",
			"STRUCT LATLONALT",
			"STRUCT LATLONALTPBH",
			"STRUCT PBH32",
			"STRUCT DAMAGEVISIBLE",
			"STRUCT SURFACE RELATIVE VELOCITY",
			"STRUCT WORLDVELOCITY",
			"STRUCT WORLD ROTATION VELOCITY",
			"STRUCT BODY VELOCITY",
			"STRUCT BODY ROTATION VELOCITY",
			"STRUCT BODY ROTATION ACCELERATION",
			"STRUCT WORLD ACCELERATION",
			"STRUCT ENGINE POSITION",
			"STRUCT AMBIENT WIND",
			"STRUCT REALISM VARS",
			"STRUC HEADING HOLD PID CONSTS",
			"STRUC AIRSPEED HOLD PID CONSTS",
			"STRUCT EYEPOINT DYNAMIC ANGLE",
			"STRUCT EYEPOINT DYNAMIC OFFSET",
			"PITOT HEAT",
			"PITOT ICE PCT",
			"SMOKE ENABLE",
			"SMOKESYSTEM AVAILABLE",
			"G FORCE",
			"SEMIBODY LOADFACTOR X",
			"SEMIBODY LOADFACTOR Y",
			"SEMIBODY LOADFACTOR Z",
			"SEMIBODY LOADFACTOR YDOT",
			"MAX G FORCE",
			"MIN G FORCE",
			"SUCTION PRESSURE",
			"RAD INS SWITCH",
			"TYPICAL DESCENT RATE",
			"VISUAL MODEL RADIUS",
			"SIMULATED RADIUS",
			"IS USER SIM",
			"CONTROLLABLE",
			"HEADING INDICATOR",
			"TITLE",
			"CATEGORY",
			"SIM DISABLED",
			"PROP DEICE SWITCH",
			"STRUCTURAL DEICE SWITCH",
			"STRUCTURAL ICE PCT",
			"ARTIFICIAL GROUND ELEVATION",
			"SURFACE INFO VALID",
			"SURFACE TYPE",
			"SURFACE CONDITION",
			"PUSHBACK STATE",
			"PUSHBACK ANGLE",
			"PUSHBACK CONTACTX",
			"PUSHBACK CONTACTY",
			"PUSHBACK CONTACTZ",
			"PUSHBACK WAIT",
			"HSI CDI NEEDLE",
			"HSI GSI NEEDLE",
			"HSI CDI NEEDLE VALID",
			"HSI GSI NEEDLE VALID",
			"HSI TF FLAGS",
			"HSI BEARING",
			"HSI BEARING VALID",
			"HSI HAS LOCALIZER",
			"HSI SPEED",
			"HSI DISTANCE",
			"HSI STATION IDENT",
			"IS SLEW ACTIVE",
			"IS SLEW ALLOWED",
			"ATC SUGGESTED MIN RWY TAKEOFF",
			"ATC SUGGESTED MIN RWY LANDING",
			"YAW STRING ANGLE",
			"YAW STRING PCT EXTENDED",
			"INDUCTOR COMPASS PERCENT DEVIATION",
			"INDUCTOR COMPASS HEADING REF",
			"ANEMOMETER PCT RPM",
			"GPS POSITION LAT",
			"GPS POSITION LON",
			"GPS POSITION ALT",
			"GPS MAGVAR",
			"GPS IS ACTIVE FLIGHT PLAN",
			"GPS IS ACTIVE WAY POINT",
			"GPS IS ARRIVED",
			"GPS IS DIRECTTO FLIGHTPLAN",
			"GPS GROUND SPEED",
			"GPS GROUND TRUE HEADING",
			"GPS GROUND MAGNETIC TRACK",
			"GPS GROUND TRUE TRACK",
			"GPS ETE",
			"GPS ETA",
			"GPS WP DISTANCE",
			"GPS WP BEARING",
			"GPS WP TRUE BEARING",
			"GPS WP CROSS TRK",
			"GPS WP DESIRED TRACK",
			"GPS WP TRUE REQ HDG",
			"GPS WP VERTICAL SPEED",
			"GPS WP TRACK ANGLE ERROR",
			"GPS WP NEXT ID",
			"GPS WP NEXT LAT",
			"GPS WP NEXT LON",
			"GPS WP NEXT ALT",
			"GPS WP PREV VALID",
			"GPS WP PREV ID",
			"GPS WP PREV LAT",
			"GPS WP PREV LON",
			"GPS WP PREV ALT",
			"GPS WP ETE",
			"GPS WP ETA",
			"GPS COURSE TO STEER",
			"GPS FLIGHT PLAN WP INDEX",
			"GPS FLIGHT PLAN WP COUNT",
			"GPS IS ACTIVE WP LOCKED",
			"GPS IS APPROACH LOADED",
			"GPS IS APPROACH ACTIVE",
			"GPS APPROACH MODE",
			"GPS APPROACH WP TYPE",
			"GPS APPROACH IS WP RUNWAY",
			"GPS APPROACH SEGMENT TYPE",
			"GPS APPROACH AIRPORT ID",
			"GPS APPROACH APPROACH INDEX",
			"GPS APPROACH APPROACH ID",
			"GPS APPROACH APPROACH TYPE",
			"GPS APPROACH TRANSITION INDEX",
			"GPS APPROACH TRANSITION ID",
			"GPS APPROACH IS FINAL",
			"GPS APPROACH IS MISSED",
			"GPS APPROACH TIMEZONE DEVIATION",
			"GPS APPROACH WP INDEX",
			"GPS APPROACH WP COUNT",
			"GPS TARGET DISTANCE",
			"GPS TARGET ALTITUDE",
			"USER INPUT ENABLED",
			"ROTOR BRAKE HANDLE POS",
			"ROTOR BRAKE ACTIVE",
			"ROTOR CLUTCH SWITCH POS",
			"ROTOR CLUTCH ACTIVE",
			"ROTOR TEMPERATURE",
			"ROTOR CHIP DETECTED",
			"ROTOR GOV SWITCH POS",
			"ROTOR GOV ACTIVE",
			"ROTOR LATERAL TRIM PCT",
			"ROTOR RPM PCT",
			"ROTOR ROTATION ANGLE",
			"COLLECTIVE POSITION",
			"DISK PITCH ANGLE",
			"DISK BANK ANGLE",
			"DISK PITCH PCT",
			"DISK BANK PCT",
			"DISK CONING PCT",
			"GEAR DAMAGE BY SPEED",
			"GEAR SPEED EXCEEDED",
			"FLAP DAMAGE BY SPEED",
			"FLAP SPEED EXCEEDED",
			"ESTIMATED CRUISE SPEED",
			"ESTIMATED FUEL FLOW",
			"EYEPOINT POSITION",
			"NAV VOR LLAF64",
			"NAV GS LLAF64",
			"NAV RAW GLIDE SLOPE",
			"WINDSHIELD RAIN EFFECT AVAILABLE",
			"STATIC CG TO GROUND",
			"STATIC PITCH",
			"CRASH SEQUENCE",
			"CRASH FLAG",
			"APPLY HEAT TO SYSTEMS",
			"TOW RELEASE HANDLE",
			"TOW CONNECTION",
			"APU PCT RPM",
			"APU PCT STARTER",
			"APU VOLTS",
			"APU GENERATOR SWITCH",
			"APU GENERATOR ACTIVE",
			"APU ON FIRE DETECTED",
			"PRESSURIZATION CABIN ALTITUDE",
			"PRESSURIZATION CABIN ALTITUDE GOAL",
			"PRESSURIZATION CABIN ALTITUDE RATE",
			"PRESSURIZATION PRESSURE DIFFERENTIAL",
			"PRESSURIZATION DUMP SWITCH",
			"FIRE BOTTLE SWITCH",
			"FIRE BOTTLE DISCHARGED",
			"CABIN NO SMOKING ALERT SWITCH",
			"CABIN SEATBELTS ALERT SWITCH",
			"GPWS WARNING",
			"GPWS SYSTEM ACTIVE",
			"IS LATITUDE LONGITUDE FREEZE ON",
			"IS ALTITUDE FREEZE ON",
			"IS ATTITUDE FREEZE ON",
			"NUM SLING CABLES",
			"SLING OBJECT ATTACHED",
			"SLING CABLE BROKEN",
			"SLING CABLE EXTENDED LENGTH",
			"SLING ACTIVE PAYLOAD STATION",
			"SLING HOIST PERCENT DEPLOYED",
			"SLING HOIST SWITCH",
			"SLING HOOK IN PICKUP MODE",
			"IS ATTACHED TO SLING",
			"CABLE CAUGHT BY TAILHOOK",
			"EXTERNAL SYSTEM VALUE",
			"ANNUNCIATOR SWITCH",
			"AUTOBRAKES ACTIVE",
			"REJECTED TAKEOFF BRAKES ACTIVE",
			"SHUTOFF VALVE PULLED",
			"LIGHT POTENTIOMETER",
			"FAKE AC LWR",
			"FAKE AC UPR",
			"FAKE AC TRIM L",
			"FAKE AC TRIM R",
			"FAKE WINDOW HEAT L",
			"FAKE WINDOW HEAT R",
			"FAKE BUS TIE",
			"FAKE EXT PWR",
			"FAKE GEN CONT",
			"FAKE UTIL PWR L",
			"FAKE UTIL PWR R",
			"FAKE CRT TANK PUMP L",
			"FAKE CRT TANK PUMP R",
			"FAKE FUEL MAIN AFT",
			"FAKE FUEL MAIN FWD",
			"FAKE FUEL OVRD AFT",
			"FAKE FUEL OVRD FWD",
			"FAKE STAB TANK PUMP L",
			"FAKE STAB TANK PUMP R",
			"FAKE HYD PUMP SWITCH",
			"FAKE O2 YD LOWER",
			"FAKE O2 YD UPPER",
			"FAKE APU BLEED",
			"FAKE BLEED",
			"FAKE ISOLATION VALVE L",
			"FAKE ISOLATION VALVE R",
			"FAKE AC FLT DECK",
			"FAKE AC PASS TEMP",
			"FAKE STANDBY POWER",
			"FAKE DEMAND PUMP SEL",
			"FAKE IRS C",
			"FAKE IRS L",
			"FAKE IRS R",
			"FAKE ANTI ICE NACELLE",
			"FAKE ANTI ICE WING",
			"FAKE OUTFLOW VALVES",
			"FAKE XFEED",
			"FAKE EEC",
			"FAKE PACK",
			"FAKE EMERG LIGHTS",
			"FAKE TRIM STAB",
			"FAKE CARGO ARM AFT",
			"FAKE XPNDR",
			"FAKE IDENT",
			"FAKE NO SMOKING",
			"FAKE SEATBELTS",
			"FAKE CARGO TEMP",
			"FAKE EMERGENCY LIGHT",
			"AUTOPILOT DISENGAGED",
			"FAKE APU GEN SWITCH",
			"BREAKER AVNFAN",
			"BREAKER AUTOPILOT",
			"BREAKER GPS",
			"BREAKER NAVCOM1",
			"BREAKER NAVCOM2",
			"BREAKER NAVCOM3",
			"BREAKER ADF",
			"BREAKER XPNDR",
			"BREAKER FLAP",
			"BREAKER INST",
			"BREAKER AVNBUS1",
			"BREAKER AVNBUS2",
			"BREAKER TURNCOORD",
			"BREAKER INSTLTS",
			"BREAKER ALTFLD",
			"BREAKER WARN",
			"BREAKER LTS PWR",
			"PILOT TRANSMITTER TYPE",
			"COPILOT TRANSMITTER TYPE",
			"PILOT TRANSMITTING",
			"COPILOT TRANSMITTING",
			"SPEAKER ACTIVE",
			"INTERCOM SYSTEM ACTIVE",
			"AUDIO PANEL VOLUME",
			"MARKER BEACON SENSITIVITY HIGH",
			"MARKER BEACON TEST MUTE",
			"INTERCOM MODE",
			"COM RECEIVE",
			"AUTOPILOT ALTITUDE ARM",
			"COM VOLUME",
			"NAV VOLUME",
			"ATC CLEARED IFR",
			"ATC IFR FP TO REQUEST",
			"ATC RUNWAY SELECTED",
			"ATC TAXIPATH DISTANCE",
			"ATC RUNWAY START DISTANCE",
			"ATC RUNWAY END DISTANCE",
			"ATC RUNWAY DISTANCE",
			"ATC RUNWAY RELATIVE POSITION X",
			"ATC RUNWAY RELATIVE POSITION Y",
			"ATC RUNWAY RELATIVE POSITION Z",
			"ATC RUNWAY TDPOINT RELATIVE POSITION X",
			"ATC RUNWAY TDPOINT RELATIVE POSITION Y",
			"ATC RUNWAY TDPOINT RELATIVE POSITION Z",
			"ATC RUNWAY HEADING DEGREES TRUE",
			"ATC RUNWAY LENGTH",
			"ATC RUNWAY WIDTH",
			"ATC RUNWAY AIRPORT NAME",
			"SLOPE TO ATC RUNWAY",
			"ATC CLEARED TAKEOFF",
			"ATC CLEARED LANDING",
			"ATC CLEARED TAXI",
			"ON ANY RUNWAY",
			"ATC FLIGHTPLAN DIFF HEADING",
			"ATC FLIGHTPLAN DIFF ALT",
			"ATC FLIGHTPLAN DIFF DISTANCE",
			"ATC PREVIOUS WAYPOINT ALTITUDE",
			"ATC CURRENT WAYPOINT ALTITUDE",
			"ASSISTANCE LANDING ENABLED",
			"COM1 STORED FREQUENCY",
			"COM2 STORED FREQUENCY",
			"COM3 STORED FREQUENCY",
			"RUDDER TRIM DISABLED",
			"AILERON TRIM DISABLED",
			"ELEVATOR TRIM DISABLED",
			"PLANE TOUCHDOWN LATITUDE",
			"PLANE TOUCHDOWN LONGITUDE",
			"PLANE TOUCHDOWN PITCH DEGREES",
			"PLANE TOUCHDOWN BANK DEGREES",
			"PLANE TOUCHDOWN HEADING DEGREES MAGNETIC",
			"PLANE TOUCHDOWN HEADING DEGREES TRUE",
			"PLANE TOUCHDOWN NORMAL VELOCITY",
			"TURB ENG IGNITION SWITCH EX1",
			"TURB ENG IS IGNITING",
			"PLANE IN PARKING STATE",
			"ELT ACTIVATED",
			"RECIP ENG ENGINE MASTER SWITCH",
			"RECIP ENG GLOW PLUG ACTIVE",
			"LIGHT GLARESHIELD",
			"LIGHT PEDESTRAL",
			"LIGHT GLARESHIELD ON",
			"LIGHT PEDESTRAL ON",
			"CIRCUIT NAVCOM1 ON",
			"CIRCUIT NAVCOM2 ON",
			"CIRCUIT NAVCOM3 ON",
			"AIRSPEED TRUE RAW",
			"GENERAL ENG FUEL PUMP SWITCH EX1",
			"FUEL TRANSFER PUMP ON",
			"IS ANY INTERIOR LIGHT ON",
			"GPS FLIGHTPLAN TOTAL DISTANCE",
			"CIRCUIT ON",
			"CIRCUIT SWITCH ON",
			"BUS LOOKUP INDEX",
			"BUS CONNECTION ON",
			"BATTERY CONNECTION ON",
			"ALTERNATOR CONNECTION ON",
			"CIRCUIT CONNECTION ON",
			"BUS BREAKER PULLED",
			"BATTERY BREAKER PULLED",
			"ALTERNATOR BREAKER PULLED",
			"CIRCUIT BREAKER PULLED",
			"CAMERA STATE",
			"CAMERA SUBSTATE",
			"SMART CAMERA ACTIVE",
			"CAMERA REQUEST ACTION",
			"ADF VOLUME",
			"BLEED AIR APU",
			"BLEED AIR ENGINE",
			"APU BLEED TO ENGINE",
			"EXTERNAL POWER CONNECTION ON",
			"EXTERNAL POWER BREAKER PULLED",
			"EXTERNAL POWER AVAILABLE",
			"EXTERNAL POWER ON",
		};


		static FsSimVarFactory()
		{
			foreach (FsSimVar simVarId in Enum.GetValues(typeof(FsSimVar)))
            {
                FsSimVarInfo svi = new FsSimVarInfo();

                svi.SimVarName = _simVarNames[(uint)simVarId];
				svi.SimVarNameNoWhitespace = string.Join("", svi.SimVarName.Split(default(string[]), StringSplitOptions.RemoveEmptyEntries));

				_enumToCodeDictionary[simVarId] = svi;
			}
        }

		/// <summary>
		/// Gets the corresponding simulation variable name to a simulation variable code.
		/// </summary>
		/// <param name="simVarId">A <see cref="FsSimVar"/> enum.</param>
		/// <returns>The corresponding sim variable name.</returns>
		public static string GetSimVarName(FsSimVar simVarId)
        {
            if (!_enumToCodeDictionary.ContainsKey(simVarId))
                throw new Exception("SimVar id not found.");

			return _enumToCodeDictionary[simVarId].SimVarName;
		}

		/// <summary>
		/// Looks up a text and tries to resolve the corresponding SimVar name.
		/// </summary>
		/// <param name="simVarName">A name containing any case or underscores.</param>
		/// <returns>An identified SimVar name or null if not recognized.</returns>
        public static string GetSimVarName(string simVarName)
        {
			// Strip any underscores
            simVarName = string.Join("", simVarName.Split('_'));

			var svi = _enumToCodeDictionary.Values.ToList().Find(x =>
                x.SimVarNameNoWhitespace.Equals(simVarName, StringComparison.InvariantCultureIgnoreCase));

            return svi?.SimVarName;
        }
	}
}
