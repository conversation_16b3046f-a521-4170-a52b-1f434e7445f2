﻿using System;
using System.Collections.Generic;

namespace CTrue.FsConnect
{
#pragma warning disable 1591

	public enum FsUnit
	{
		None,
		Amp,
		Ampere,
		<PERSON>per<PERSON>,
		Amps,
		Angl16,
		Angl32,
		Atm,
		Atmosphere,
		Atmospheres,
		Bar,
		Bars,
		Bco16,
		Bel,
		Bels,
		Bool,
		Boolean,
		BoostCmhg,
		BoostInhg,
		BoostPsi,
		Celsius,
		CelsiusFs7Egt,
		CelsiusFs7OilTemp,
		CelsiusScaler1Per256,
		CelsiusScaler16k,
		CelsiusScaler256,
		Centimeter,
		CentimeterOfMercury,
		Centimeters,
		CentimetersOfMercury,
		Cm,
		Cm2,
		Cm3,
		Cmhg,
		CuCm,
		CuFt,
		CuIn,
		CuKm,
		CuM,
		CuMm,
		CuYd,
		CubicCentimeter,
		CubicCentimeters,
		CubicFeet,
		CubicFoot,
		CubicInch,
		CubicInches,
		CubicKilometer,
		CubicKilometers,
		CubicMeter,
		CubicMeters,
		CubicMile,
		CubicMiles,
		CubicMillimeter,
		CubicMillimeters,
		CubicYard,
		CubicYards,
		Day,
		Days,
		Decibel,
		Decibels,
		Decimile,
		Decimiles,
		Decinmile,
		Decinmiles,
		Degree,
		DegreeAngl16,
		DegreeAngl32,
		DegreeLatitude,
		DegreeLongitude,
		DegreePerSecond,
		DegreePerSecondAng16,
		Degrees,
		DegreesAngl16,
		DegreesAngl32,
		DegreesLatitude,
		DegreesLongitude,
		DegreesPerSecond,
		DegreesPerSecondAng16,
		Enum,
		Fahrenheit,
		Farenheit,
		Feet,
		FeetPerMinute,
		FeetPerMinute2,
		FeetPerSecond,
		FeetPerSecond2,
		FeetPerSecondSquared,
		Flags,
		Foot,
		FootPerSecondSquared,
		FootPound,
		FootPound2,
		FootPounds,
		FootPounds2,
		FrequencyAdfBcd32,
		FrequencyBcd16,
		FrequencyBcd32,
		Fs7ChargingAmps,
		Fs7OilQuantity,
		Ft,
		FtLbPerSecond,
		FtLbs,
		FtPerMin,
		Ft2,
		Ft3,
		GForce,
		GForce624Scaled,
		Gallon,
		GallonPerHour,
		Gallons,
		GallonsPerHour,
		Geepound,
		Geepounds,
		Gforce,
		GlobalpDeltaHeadingRate,
		GlobalpEng1ManifoldPressure,
		GlobalpEng1OilPrs,
		GlobalpEng1OilTmp,
		GlobalpVerticalSpeed,
		Gph,
		Grad,
		Grads,
		Half,
		Halfs,
		Hectopascal,
		Hectopascals,
		Hertz,
		Hour,
		HourOver10,
		Hours,
		HoursOver10,
		Hz,
		In,
		In2,
		In3,
		Inch,
		InchOfMercury,
		Inches,
		InchesOfMercury,
		Inhg,
		Inhg64Over64k,
		Kelvin,
		Keyframe,
		Keyframes,
		Kg,
		KgfMeter,
		KgfMeters,
		Kgfsqcm,
		Khz,
		Kilogram,
		KilogramForcePerSquareCentimeter,
		KilogramMeter,
		KilogramMeterSquared,
		KilogramMeters,
		KilogramPerCubicMeter,
		KilogramPerSecond,
		Kilograms,
		KilogramsMeterSquared,
		KilogramsPerCubicMeter,
		KilogramsPerSecond,
		Kilohertz,
		Kilometer,
		KilometerPerHour,
		KilometerPerHour2,
		Kilometers,
		KilometersPerHour,
		KilometersPerHour2,
		Kilopascal,
		Km,
		Km2,
		Km3,
		Knot,
		KnotScaler128,
		Knots,
		KnotsScaler128,
		Kpa,
		Kph,
		LbfFeet,
		Lbs,
		Liter,
		LiterPerHour,
		Liters,
		LitersPerHour,
		M,
		MPerS,
		M2,
		M3,
		Mach,
		Mach3d2Over64k,
		Machs,
		Mask,
		Mbar,
		Mbars,
		Megahertz,
		Meter,
		MeterCubed,
		MeterCubedPerSecond,
		MeterLatitude,
		MeterPerMinute,
		MeterPerSecond,
		MeterPerSecond2,
		MeterPerSecondScaler256,
		MeterPerSecondSquared,
		MeterScaler256,
		Meters,
		MetersCubed,
		MetersCubedPerSecond,
		MetersLatitude,
		MetersPerMinute,
		MetersPerSecond,
		MetersPerSecond2,
		MetersPerSecondScaler256,
		MetersPerSecondSquared,
		MetersScaler256,
		Mhz,
		Mile,
		MilePerHour,
		Miles,
		MilesPerHour,
		Millibar,
		MillibarScaler16,
		Millibars,
		MillibarsScaler16,
		Millimeter,
		MillimeterOfMercury,
		MillimeterOfWater,
		Millimeters,
		MillimetersOfMercury,
		MillimetersOfWater,
		Minute,
		MinutePerRound,
		Minutes,
		MinutesPerRound,
		Mm2,
		Mm3,
		Mmhg,
		MoreThanAHalf,
		Mph,
		NauticalMile,
		NauticalMiles,
		NewtonMeter,
		NewtonMeters,
		NewtonPerSquareMeter,
		NewtonsPerSquareMeter,
		NiceMinutePerRound,
		NiceMinutesPerRound,
		Nm,
		Nmile,
		Nmiles,
		Number,
		Numbers,
		Pa,
		Part,
		Pascal,
		Pascals,
		PerDegree,
		PerHour,
		PerMinute,
		PerRadian,
		PerSecond,
		Percent,
		PercentOver100,
		PercentScaler16k,
		PercentScaler2pow23,
		PercentScaler32k,
		Percentage,
		Position,
		Position128,
		Position16k,
		Position32k,
		Pound,
		PoundForcePerSquareFoot,
		PoundForcePerSquareInch,
		PoundPerHour,
		PoundScaler256,
		PoundalFeet,
		Pounds,
		PoundsPerHour,
		PoundsScaler256,
		Pph,
		Psf,
		PsfScaler16k,
		Psi,
		Psi4Over16k,
		PsiFs7OilPressure,
		PsiScaler16k,
		Quart,
		Quarts,
		Radian,
		RadianPerSecond,
		Radians,
		RadiansPerSecond,
		Rankine,
		Ratio,
		RevolutionPerMinute,
		RevolutionsPerMinute,
		Round,
		Rounds,
		Rpm,
		Rpm1Over16k,
		Rpms,
		Scaler,
		Second,
		Seconds,
		Slug,
		SlugFeetSquared,
		SlugPerCubicFeet,
		SlugPerCubicFoot,
		SlugPerFt3,
		Slugs,
		SlugsFeetSquared,
		SlugsPerCubicFeet,
		SlugsPerCubicFoot,
		SqCm,
		SqFt,
		SqIn,
		SqKm,
		SqM,
		SqMm,
		SqYd,
		SquareCentimeter,
		SquareCentimeters,
		SquareFeet,
		SquareFoot,
		SquareInch,
		SquareInches,
		SquareKilometer,
		SquareKilometers,
		SquareMeter,
		SquareMeters,
		SquareMile,
		SquareMiles,
		SquareMillimeter,
		SquareMillimeters,
		SquareYard,
		SquareYards,
		Third,
		Thirds,
		Times,
		Volt,
		Volts,
		Watt,
		Watts,
		Yard,
		Yards,
		Yd2,
		Yd3,
		Year,
		Years,
		Undefined = Int32.MaxValue,
	};

#pragma warning restore 1591
}