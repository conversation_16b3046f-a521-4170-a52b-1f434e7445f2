﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata>
    <id>Serilog</id>
    <version>2.10.0</version>
    <authors>Serilog Contributors</authors>
    <requireLicenseAcceptance>false</requireLicenseAcceptance>
    <license type="expression">Apache-2.0</license>
    <licenseUrl>https://licenses.nuget.org/Apache-2.0</licenseUrl>
    <icon>icon.png</icon>
    <projectUrl>https://serilog.net/</projectUrl>
    <description>Simple .NET logging with fully-structured events</description>
    <tags>serilog logging semantic structured</tags>
    <repository type="git" url="https://github.com/serilog/serilog.git" commit="24b67c6cb4535ff13095a7f8338070917d7bf550" />
    <dependencies>
      <group targetFramework=".NETFramework4.5" />
      <group targetFramework=".NETFramework4.6" />
      <group targetFramework=".NETStandard1.0">
        <dependency id="NETStandard.Library" version="1.6.1" exclude="Build,Analyzers" />
      </group>
      <group targetFramework=".NETStandard1.3">
        <dependency id="NETStandard.Library" version="1.6.1" exclude="Build,Analyzers" />
        <dependency id="System.Collections.NonGeneric" version="4.3.0" exclude="Build,Analyzers" />
      </group>
      <group targetFramework=".NETStandard2.0" />
      <group targetFramework=".NETStandard2.1" />
    </dependencies>
    <frameworkAssemblies>
      <frameworkAssembly assemblyName="Microsoft.CSharp" targetFramework=".NETFramework4.5, .NETFramework4.6" />
      <frameworkAssembly assemblyName="System.Core" targetFramework=".NETFramework4.5, .NETFramework4.6" />
      <frameworkAssembly assemblyName="System" targetFramework=".NETFramework4.5, .NETFramework4.6" />
    </frameworkAssemblies>
  </metadata>
</package>