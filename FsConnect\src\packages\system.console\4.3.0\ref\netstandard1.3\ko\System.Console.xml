﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Console</name>
  </assembly>
  <members>
    <member name="T:System.Console">
      <summary>콘솔 응용 프로그램에 대한 표준 입력, 출력 및 오류 스트림을 나타냅니다.이 클래스는 상속될 수 없습니다.이 형식에 대 한.NET Framework 소스 코드를 찾아보려면 참조는 Reference Source.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Console.BackgroundColor">
      <summary>콘솔의 배경색을 가져오거나 설정합니다.</summary>
      <returns>각 문자의 배경에 표시되는 콘솔의 배경색을 지정하는 값입니다.기본값은 검정입니다.</returns>
      <exception cref="T:System.ArgumentException">set 작업에서 지정한 색이 <see cref="T:System.ConsoleColor" />의 유효한 멤버가 아닌 경우 </exception>
      <exception cref="T:System.Security.SecurityException">사용자에게 이 작업을 수행할 수 있는 권한이 없는 경우 </exception>
      <exception cref="T:System.IO.IOException">I/O 오류가 발생하는 경우 </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.UIPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Window="SafeTopLevelWindows" />
      </PermissionSet>
    </member>
    <member name="E:System.Console.CancelKeyPress">
      <summary>
        <see cref="F:System.ConsoleModifiers.Control" /> 보조 키(Ctrl)와 <see cref="F:System.ConsoleKey.C" /> 콘솔 키(C) 또는 Break 키를 동시에 누르면(Ctrl+C 또는 Ctrl+Break) 발생합니다.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Console.Error">
      <summary>표준 오류 출력 스트림을 가져옵니다.</summary>
      <returns>표준 오류 출력 스트림을 나타내는 <see cref="T:System.IO.TextWriter" />입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Console.ForegroundColor">
      <summary>콘솔의 전경색을 가져오거나 설정합니다.</summary>
      <returns>표시되는 각 문자의 색인 콘솔의 전경색을 지정하는 <see cref="T:System.ConsoleColor" />입니다.기본값은 회색입니다.</returns>
      <exception cref="T:System.ArgumentException">set 작업에서 지정한 색이 <see cref="T:System.ConsoleColor" />의 유효한 멤버가 아닌 경우 </exception>
      <exception cref="T:System.Security.SecurityException">사용자에게 이 작업을 수행할 수 있는 권한이 없는 경우 </exception>
      <exception cref="T:System.IO.IOException">I/O 오류가 발생하는 경우 </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.UIPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Window="SafeTopLevelWindows" />
      </PermissionSet>
    </member>
    <member name="P:System.Console.In">
      <summary>표준 입력 스트림을 가져옵니다.</summary>
      <returns>표준 입력 스트림을 나타내는 <see cref="T:System.IO.TextReader" />입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Console.OpenStandardError">
      <summary>표준 오류 스트림을 가져옵니다.</summary>
      <returns>표준 오류 스트림입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Console.OpenStandardInput">
      <summary>표준 입력 스트림을 가져옵니다.</summary>
      <returns>표준 입력 스트림입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Console.OpenStandardOutput">
      <summary>표준 출력 스트림을 가져옵니다.</summary>
      <returns>표준 출력 스트림입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Console.Out">
      <summary>표준 출력 스트림을 가져옵니다.</summary>
      <returns>표준 출력 스트림을 나타내는 <see cref="T:System.IO.TextWriter" />입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Console.Read">
      <summary>표준 입력 스트림에서 다음 문자를 읽습니다.</summary>
      <returns>입력 스트림의 다음 문자를 반환하거나 현재 읽을 문자가 더 이상 없으면 -1을 반환합니다.</returns>
      <exception cref="T:System.IO.IOException">I/O 오류가 발생하는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Console.ReadLine">
      <summary>표준 입력 스트림에서 다음 줄의 문자를 읽습니다.</summary>
      <returns>입력 스트림의 다음 줄 문자를 반환하거나 사용할 수 있는 줄이 더 이상 없으면 null을 반환합니다.</returns>
      <exception cref="T:System.IO.IOException">I/O 오류가 발생하는 경우 </exception>
      <exception cref="T:System.OutOfMemoryException">반환된 문자열을 위한 버퍼를 할당할 메모리가 부족한 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">다음 줄의 문자 수가 <see cref="F:System.Int32.MaxValue" />보다 큰 경우</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Console.ResetColor">
      <summary>콘솔의 전경색과 배경색을 해당 기본값으로 설정합니다.</summary>
      <exception cref="T:System.Security.SecurityException">사용자에게 이 작업을 수행할 수 있는 권한이 없는 경우 </exception>
      <exception cref="T:System.IO.IOException">I/O 오류가 발생하는 경우 </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.UIPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Window="SafeTopLevelWindows" />
      </PermissionSet>
    </member>
    <member name="M:System.Console.SetError(System.IO.TextWriter)">
      <summary>
        <see cref="P:System.Console.Error" /> 속성을 지정한 <see cref="T:System.IO.TextWriter" /> 개체로 설정합니다.</summary>
      <param name="newError">새 표준 오류 출력을 나타내는 스트림입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="newError" />가 null인 경우 </exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Console.SetIn(System.IO.TextReader)">
      <summary>
        <see cref="P:System.Console.In" /> 속성을 지정한 <see cref="T:System.IO.TextReader" /> 개체로 설정합니다.</summary>
      <param name="newIn">새 표준 입력을 나타내는 스트림입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="newIn" />가 null인 경우 </exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Console.SetOut(System.IO.TextWriter)">
      <summary>
        <see cref="P:System.Console.Out" /> 속성을 지정한 <see cref="T:System.IO.TextWriter" /> 개체로 설정합니다.</summary>
      <param name="newOut">새 표준 출력을 나타내는 스트림입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="newOut" />가 null인 경우 </exception>
      <exception cref="T:System.Security.SecurityException">호출자에게 필요한 권한이 없는 경우 </exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.Console.Write(System.Boolean)">
      <summary>지정한 부울 값의 텍스트 표현을 표준 출력 스트림에 씁니다.</summary>
      <param name="value">작성할 값입니다. </param>
      <exception cref="T:System.IO.IOException">I/O 오류가 발생하는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Console.Write(System.Char)">
      <summary>지정한 유니코드 문자 값을 표준 출력 스트림에 씁니다.</summary>
      <param name="value">작성할 값입니다. </param>
      <exception cref="T:System.IO.IOException">I/O 오류가 발생하는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Console.Write(System.Char[])">
      <summary>지정한 유니코드 문자의 배열을 표준 출력 스트림에 씁니다.</summary>
      <param name="buffer">유니코드 문자 배열입니다. </param>
      <exception cref="T:System.IO.IOException">I/O 오류가 발생하는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Console.Write(System.Char[],System.Int32,System.Int32)">
      <summary>지정한 유니코드 문자의 하위 배열을 표준 출력 스트림에 씁니다.</summary>
      <param name="buffer">유니코드 문자 배열입니다. </param>
      <param name="index">
        <paramref name="buffer" />의 시작 위치입니다. </param>
      <param name="count">쓸 문자 수입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" />가 null인 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 또는 <paramref name="count" />가 0보다 작은 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" />와 <paramref name="count" />의 합이 <paramref name="buffer" /> 내부에 없는 위치를 지정하는 경우 </exception>
      <exception cref="T:System.IO.IOException">I/O 오류가 발생하는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Console.Write(System.Decimal)">
      <summary>지정한 <see cref="T:System.Decimal" /> 값의 텍스트 표현을 표준 출력 스트림에 씁니다.</summary>
      <param name="value">작성할 값입니다. </param>
      <exception cref="T:System.IO.IOException">I/O 오류가 발생하는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Console.Write(System.Double)">
      <summary>지정한 배정밀도 부동 소수점 값의 텍스트 표현을 표준 출력 스트림에 씁니다.</summary>
      <param name="value">작성할 값입니다. </param>
      <exception cref="T:System.IO.IOException">I/O 오류가 발생하는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Console.Write(System.Int32)">
      <summary>지정한 부호 있는 32비트 정수 값의 텍스트 표현을 표준 출력 스트림에 씁니다.</summary>
      <param name="value">작성할 값입니다. </param>
      <exception cref="T:System.IO.IOException">I/O 오류가 발생하는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Console.Write(System.Int64)">
      <summary>지정한 부호 있는 64비트 정수 값의 텍스트 표현을 표준 출력 스트림에 씁니다.</summary>
      <param name="value">작성할 값입니다. </param>
      <exception cref="T:System.IO.IOException">I/O 오류가 발생하는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Console.Write(System.Object)">
      <summary>지정한 개체의 텍스트 표현을 표준 출력 스트림에 씁니다.</summary>
      <param name="value">쓸 값이거나 null입니다. </param>
      <exception cref="T:System.IO.IOException">I/O 오류가 발생하는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Console.Write(System.Single)">
      <summary>지정한 단정밀도 부동 소수점 값의 텍스트 표현을 표준 출력 스트림에 씁니다.</summary>
      <param name="value">작성할 값입니다. </param>
      <exception cref="T:System.IO.IOException">I/O 오류가 발생하는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Console.Write(System.String)">
      <summary>지정한 문자열 값을 표준 출력 스트림에 씁니다.</summary>
      <param name="value">작성할 값입니다. </param>
      <exception cref="T:System.IO.IOException">I/O 오류가 발생하는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Console.Write(System.String,System.Object)">
      <summary>지정한 형식 정보를 사용하여 지정한 개체의 텍스트 표현을 표준 출력 스트림에 씁니다.</summary>
      <param name="format">합성 형식 문자열입니다(설명 부분 참조). </param>
      <param name="arg0">
        <paramref name="format" />을 사용하여 쓸 개체입니다. </param>
      <exception cref="T:System.IO.IOException">I/O 오류가 발생하는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="format" />가 null인 경우 </exception>
      <exception cref="T:System.FormatException">
        <paramref name="format" />의 형식 사양이 잘못된 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Console.Write(System.String,System.Object,System.Object)">
      <summary>지정한 형식 정보를 사용하여 지정한 개체의 텍스트 표현을 표준 출력 스트림에 씁니다.</summary>
      <param name="format">합성 형식 문자열입니다(설명 부분 참조).</param>
      <param name="arg0">
        <paramref name="format" />을 사용하여 쓸 첫 번째 개체입니다. </param>
      <param name="arg1">
        <paramref name="format" />을 사용하여 쓸 두 번째 개체입니다. </param>
      <exception cref="T:System.IO.IOException">I/O 오류가 발생하는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="format" />가 null인 경우 </exception>
      <exception cref="T:System.FormatException">
        <paramref name="format" />의 형식 사양이 잘못된 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Console.Write(System.String,System.Object,System.Object,System.Object)">
      <summary>지정한 형식 정보를 사용하여 지정한 개체의 텍스트 표현을 표준 출력 스트림에 씁니다.</summary>
      <param name="format">합성 형식 문자열입니다(설명 부분 참조).</param>
      <param name="arg0">
        <paramref name="format" />을 사용하여 쓸 첫 번째 개체입니다. </param>
      <param name="arg1">
        <paramref name="format" />을 사용하여 쓸 두 번째 개체입니다. </param>
      <param name="arg2">
        <paramref name="format" />을 사용하여 쓸 세 번째 개체입니다. </param>
      <exception cref="T:System.IO.IOException">I/O 오류가 발생하는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="format" />가 null인 경우 </exception>
      <exception cref="T:System.FormatException">
        <paramref name="format" />의 형식 사양이 잘못된 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Console.Write(System.String,System.Object[])">
      <summary>지정한 형식 정보를 사용하여 지정한 개체 배열의 텍스트 표현을 표준 출력 스트림에 씁니다.</summary>
      <param name="format">합성 형식 문자열입니다(설명 부분 참조).</param>
      <param name="arg">
        <paramref name="format" />을 사용하여 쓸 개체의 배열입니다. </param>
      <exception cref="T:System.IO.IOException">I/O 오류가 발생하는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="format" /> 또는 <paramref name="arg" />가 null인 경우 </exception>
      <exception cref="T:System.FormatException">
        <paramref name="format" />의 형식 사양이 잘못된 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Console.Write(System.UInt32)">
      <summary>지정한 부호 없는 32비트 정수 값의 텍스트 표현을 표준 출력 스트림에 씁니다.</summary>
      <param name="value">작성할 값입니다. </param>
      <exception cref="T:System.IO.IOException">I/O 오류가 발생하는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Console.Write(System.UInt64)">
      <summary>지정한 부호 없는 64비트 정수 값의 텍스트 표현을 표준 출력 스트림에 씁니다.</summary>
      <param name="value">작성할 값입니다. </param>
      <exception cref="T:System.IO.IOException">I/O 오류가 발생하는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Console.WriteLine">
      <summary>현재 줄 종결자를 표준 출력 스트림에 씁니다.</summary>
      <exception cref="T:System.IO.IOException">I/O 오류가 발생하는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Console.WriteLine(System.Boolean)">
      <summary>뒤에 현재 줄 종결자가 오는, 지정한 부울 값의 텍스트 표현을 표준 출력 스트림에 씁니다.</summary>
      <param name="value">작성할 값입니다. </param>
      <exception cref="T:System.IO.IOException">I/O 오류가 발생하는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Console.WriteLine(System.Char)">
      <summary>뒤에 현재 줄 종결자가 오는, 지정한 유니코드 문자 값을 표준 출력 스트림에 씁니다.</summary>
      <param name="value">작성할 값입니다. </param>
      <exception cref="T:System.IO.IOException">I/O 오류가 발생하는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Console.WriteLine(System.Char[])">
      <summary>뒤에 현재 줄 종결자가 오는, 지정한 유니코드 문자의 배열을 표준 출력 스트림에 씁니다.</summary>
      <param name="buffer">유니코드 문자 배열입니다. </param>
      <exception cref="T:System.IO.IOException">I/O 오류가 발생하는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Console.WriteLine(System.Char[],System.Int32,System.Int32)">
      <summary>뒤에 현재 줄 종결자가 오는, 지정한 유니코드 문자의 하위 배열을 표준 출력 스트림에 씁니다.</summary>
      <param name="buffer">유니코드 문자 배열입니다. </param>
      <param name="index">
        <paramref name="buffer" />의 시작 위치입니다. </param>
      <param name="count">쓸 문자 수입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" />가 null인 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 또는 <paramref name="count" />가 0보다 작은 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" />와 <paramref name="count" />의 합이 <paramref name="buffer" /> 내부에 없는 위치를 지정하는 경우 </exception>
      <exception cref="T:System.IO.IOException">I/O 오류가 발생하는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Console.WriteLine(System.Decimal)">
      <summary>뒤에 현재 줄 종결자가 오는, 지정한 <see cref="T:System.Decimal" /> 값의 텍스트 표현을 표준 출력 스트림에 씁니다.</summary>
      <param name="value">작성할 값입니다. </param>
      <exception cref="T:System.IO.IOException">I/O 오류가 발생하는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Console.WriteLine(System.Double)">
      <summary>뒤에 현재 줄 종결자가 오는, 지정한 배정밀도 부동 소수점 값의 텍스트 표현을 표준 출력 스트림에 씁니다.</summary>
      <param name="value">작성할 값입니다. </param>
      <exception cref="T:System.IO.IOException">I/O 오류가 발생하는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Console.WriteLine(System.Int32)">
      <summary>뒤에 현재 줄 종결자가 오는, 부호 있는 32비트 정수 값의 텍스트 표현을 표준 출력 스트림에 씁니다.</summary>
      <param name="value">작성할 값입니다. </param>
      <exception cref="T:System.IO.IOException">I/O 오류가 발생하는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Console.WriteLine(System.Int64)">
      <summary>뒤에 현재 줄 종결자가 오는, 부호 있는 64비트 정수 값의 텍스트 표현을 표준 출력 스트림에 씁니다.</summary>
      <param name="value">작성할 값입니다. </param>
      <exception cref="T:System.IO.IOException">I/O 오류가 발생하는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Console.WriteLine(System.Object)">
      <summary>뒤에 현재 줄 종결자가 오는, 지정한 개체의 텍스트 표현을 표준 출력 스트림에 씁니다.</summary>
      <param name="value">작성할 값입니다. </param>
      <exception cref="T:System.IO.IOException">I/O 오류가 발생하는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Console.WriteLine(System.Single)">
      <summary>뒤에 현재 줄 종결자가 오는, 지정한 단정밀도 부동 소수점 값의 텍스트 표현을 표준 출력 스트림에 씁니다.</summary>
      <param name="value">작성할 값입니다. </param>
      <exception cref="T:System.IO.IOException">I/O 오류가 발생하는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Console.WriteLine(System.String)">
      <summary>뒤에 현재 줄 종결자가 오는, 지정한 문자열 값을 표준 출력 스트림에 씁니다.</summary>
      <param name="value">작성할 값입니다. </param>
      <exception cref="T:System.IO.IOException">I/O 오류가 발생하는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Console.WriteLine(System.String,System.Object)">
      <summary>지정한 형식 정보를 사용하여 뒤에 현재 줄 종결자가 오는, 지정한 개체의 텍스트 표현을 표준 출력 스트림에 씁니다.</summary>
      <param name="format">합성 형식 문자열입니다(설명 부분 참조).</param>
      <param name="arg0">
        <paramref name="format" />을 사용하여 쓸 개체입니다. </param>
      <exception cref="T:System.IO.IOException">I/O 오류가 발생하는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="format" />가 null인 경우 </exception>
      <exception cref="T:System.FormatException">
        <paramref name="format" />의 형식 사양이 잘못된 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Console.WriteLine(System.String,System.Object,System.Object)">
      <summary>지정한 형식 정보를 사용하여 뒤에 현재 줄 종결자가 오는, 지정한 개체의 텍스트 표현을 표준 출력 스트림에 씁니다.</summary>
      <param name="format">합성 형식 문자열입니다(설명 부분 참조).</param>
      <param name="arg0">
        <paramref name="format" />을 사용하여 쓸 첫 번째 개체입니다. </param>
      <param name="arg1">
        <paramref name="format" />을 사용하여 쓸 두 번째 개체입니다. </param>
      <exception cref="T:System.IO.IOException">I/O 오류가 발생하는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="format" />가 null인 경우 </exception>
      <exception cref="T:System.FormatException">
        <paramref name="format" />의 형식 사양이 잘못된 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Console.WriteLine(System.String,System.Object,System.Object,System.Object)">
      <summary>지정한 형식 정보를 사용하여 뒤에 현재 줄 종결자가 오는, 지정한 개체의 텍스트 표현을 표준 출력 스트림에 씁니다.</summary>
      <param name="format">합성 형식 문자열입니다(설명 부분 참조).</param>
      <param name="arg0">
        <paramref name="format" />을 사용하여 쓸 첫 번째 개체입니다. </param>
      <param name="arg1">
        <paramref name="format" />을 사용하여 쓸 두 번째 개체입니다. </param>
      <param name="arg2">
        <paramref name="format" />을 사용하여 쓸 세 번째 개체입니다. </param>
      <exception cref="T:System.IO.IOException">I/O 오류가 발생하는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="format" />가 null인 경우 </exception>
      <exception cref="T:System.FormatException">
        <paramref name="format" />의 형식 사양이 잘못된 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Console.WriteLine(System.String,System.Object[])">
      <summary>지정한 형식 정보를 사용하여 뒤에 현재 줄 종결자가 오는, 지정한 개체 배열의 텍스트 표현을 표준 출력 스트림에 씁니다.</summary>
      <param name="format">합성 형식 문자열입니다(설명 부분 참조).</param>
      <param name="arg">
        <paramref name="format" />을 사용하여 쓸 개체의 배열입니다. </param>
      <exception cref="T:System.IO.IOException">I/O 오류가 발생하는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="format" /> 또는 <paramref name="arg" />가 null인 경우 </exception>
      <exception cref="T:System.FormatException">
        <paramref name="format" />의 형식 사양이 잘못된 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Console.WriteLine(System.UInt32)">
      <summary>뒤에 현재 줄 종결자가 오는, 부호 없는 32비트 정수 값의 텍스트 표현을 표준 출력 스트림에 씁니다.</summary>
      <param name="value">작성할 값입니다. </param>
      <exception cref="T:System.IO.IOException">I/O 오류가 발생하는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Console.WriteLine(System.UInt64)">
      <summary>뒤에 현재 줄 종결자가 오는, 부호 없는 64비트 정수 값의 텍스트 표현을 표준 출력 스트림에 씁니다.</summary>
      <param name="value">작성할 값입니다. </param>
      <exception cref="T:System.IO.IOException">I/O 오류가 발생하는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.ConsoleCancelEventArgs">
      <summary>
        <see cref="E:System.Console.CancelKeyPress" /> 이벤트에 대한 데이터를 제공합니다.이 클래스는 상속될 수 없습니다.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.ConsoleCancelEventArgs.Cancel">
      <summary>
        <see cref="F:System.ConsoleModifiers.Control" /> 보조키와 <see cref="F:System.ConsoleKey.C" /> 콘솔 키(Ctrl+C) 또는 Ctrl+Break 키를 동시에 누르면 현재 프로세스가 종료되는지 여부를 나타내는 값을 가져오거나 설정합니다.기본값은 현재 프로세스를 종료하는 false입니다.</summary>
      <returns>이벤트 처리기가 종료될 때 현재 프로세스가 다시 시작되면 true이고, 현재 프로세스가 종료되면 false입니다.기본값은 false이며, 이벤트 핸들러가 반환될 때 현재 프로세스가 종료합니다.true인 경우 현재 프로세스가 계속됩니다.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.ConsoleCancelEventArgs.SpecialKey">
      <summary>현재 프로세스를 중단한 보조키와 콘솔 키의 조합을 가져옵니다.</summary>
      <returns>현재 프로세스를 중단한 키 조합을 지정하는 열거형 값 중 하나입니다.기본값은 없습니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.ConsoleCancelEventHandler">
      <summary>
        <see cref="T:System.Console" />의 <see cref="E:System.Console.CancelKeyPress" /> 이벤트를 처리할 메서드를 나타냅니다.</summary>
      <param name="sender">이벤트 소스입니다. </param>
      <param name="e">이벤트 데이터가 포함된 <see cref="T:System.ConsoleCancelEventArgs" /> 개체입니다. </param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.ConsoleColor">
      <summary>콘솔의 전경색과 배경색을 정의하는 상수를 지정합니다.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.ConsoleColor.Black">
      <summary>검은색입니다.</summary>
    </member>
    <member name="F:System.ConsoleColor.Blue">
      <summary>파란색입니다.</summary>
    </member>
    <member name="F:System.ConsoleColor.Cyan">
      <summary>녹청색입니다.</summary>
    </member>
    <member name="F:System.ConsoleColor.DarkBlue">
      <summary>진한 파란색입니다.</summary>
    </member>
    <member name="F:System.ConsoleColor.DarkCyan">
      <summary>진한 녹청색입니다.</summary>
    </member>
    <member name="F:System.ConsoleColor.DarkGray">
      <summary>진한 회색입니다.</summary>
    </member>
    <member name="F:System.ConsoleColor.DarkGreen">
      <summary>진한 녹색입니다.</summary>
    </member>
    <member name="F:System.ConsoleColor.DarkMagenta">
      <summary>진한 자홍색입니다.</summary>
    </member>
    <member name="F:System.ConsoleColor.DarkRed">
      <summary>진한 빨간색입니다.</summary>
    </member>
    <member name="F:System.ConsoleColor.DarkYellow">
      <summary>진한 노란색(황토색)입니다.</summary>
    </member>
    <member name="F:System.ConsoleColor.Gray">
      <summary>회색입니다.</summary>
    </member>
    <member name="F:System.ConsoleColor.Green">
      <summary>녹색입니다.</summary>
    </member>
    <member name="F:System.ConsoleColor.Magenta">
      <summary>자홍색입니다.</summary>
    </member>
    <member name="F:System.ConsoleColor.Red">
      <summary>빨간색입니다.</summary>
    </member>
    <member name="F:System.ConsoleColor.White">
      <summary>흰색입니다.</summary>
    </member>
    <member name="F:System.ConsoleColor.Yellow">
      <summary>노란색입니다.</summary>
    </member>
    <member name="T:System.ConsoleSpecialKey">
      <summary>현재 프로세스를 중단할 수 있는 보조키와 콘솔 키의 조합을 지정합니다.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:System.ConsoleSpecialKey.ControlBreak">
      <summary>
        <see cref="F:System.ConsoleModifiers.Control" /> 보조키+Break 콘솔 키입니다.</summary>
    </member>
    <member name="F:System.ConsoleSpecialKey.ControlC">
      <summary>
        <see cref="F:System.ConsoleModifiers.Control" /> 보조키+<see cref="F:System.ConsoleKey.C" /> 콘솔 키입니다.</summary>
    </member>
  </members>
</doc>