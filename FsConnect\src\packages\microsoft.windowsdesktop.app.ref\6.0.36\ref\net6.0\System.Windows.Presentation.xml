﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Windows.Presentation</name>
  </assembly>
  <members>
    <member name="T:System.Windows.Threading.DispatcherExtensions">
      <summary>Provides a set of <see langword="static" /> methods that extend the <see cref="T:System.Windows.Threading.Dispatcher" /> class.</summary>
    </member>
    <member name="M:System.Windows.Threading.DispatcherExtensions.BeginInvoke(System.Windows.Threading.Dispatcher,System.Action)">
      <summary>Executes the specified delegate asynchronously with normal priority on the thread that the specified <see cref="T:System.Windows.Threading.Dispatcher" /> was created on.</summary>
      <param name="dispatcher">The dispatcher that executes the delegate.</param>
      <param name="action">The delegate to execute, which takes no arguments and does not return a value.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="action" /> is <see langword="null" />.</exception>
      <returns>An <see cref="T:System.IAsyncResult" /> object that represents the result of the <see cref="M:System.Windows.Threading.DispatcherExtensions.BeginInvoke(System.Windows.Threading.Dispatcher,System.Action)" /> operation.</returns>
    </member>
    <member name="M:System.Windows.Threading.DispatcherExtensions.BeginInvoke(System.Windows.Threading.Dispatcher,System.Action,System.Windows.Threading.DispatcherPriority)">
      <summary>Executes the specified delegate asynchronously with the specified priority on the thread that the specified <see cref="T:System.Windows.Threading.Dispatcher" /> was created on.</summary>
      <param name="dispatcher">The dispatcher that executes the delegate.</param>
      <param name="action">The delegate to execute, which takes no arguments and does not return a value.</param>
      <param name="priority">The execution priority of the delegate relative to other pending operations in the <see cref="T:System.Windows.Threading.Dispatcher" /> event queue.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="action" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">
        <paramref name="priority" /> is not a valid <see cref="T:System.Windows.Threading.DispatcherPriority" />.</exception>
      <returns>An <see cref="T:System.IAsyncResult" /> object that represents the result of the <see cref="M:System.Windows.Threading.DispatcherExtensions.BeginInvoke(System.Windows.Threading.Dispatcher,System.Action,System.Windows.Threading.DispatcherPriority)" /> operation.</returns>
    </member>
    <member name="M:System.Windows.Threading.DispatcherExtensions.Invoke(System.Windows.Threading.Dispatcher,System.Action)">
      <summary>Executes the specified delegate synchronously with normal priority on the thread that the specified <see cref="T:System.Windows.Threading.Dispatcher" /> was created on.</summary>
      <param name="dispatcher">The dispatcher that executes the delegate.</param>
      <param name="action">The delegate to execute, which takes no arguments and does not return a value.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="action" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Windows.Threading.DispatcherExtensions.Invoke(System.Windows.Threading.Dispatcher,System.Action,System.TimeSpan)">
      <summary>Executes the specified delegate synchronously on the thread that the specified <see cref="T:System.Windows.Threading.Dispatcher" /> was created on, and stops execution after the specified time-out period.</summary>
      <param name="dispatcher">The dispatcher that executes the delegate.</param>
      <param name="action">The delegate to execute, which takes no arguments and does not return a value.</param>
      <param name="timeout">The maximum time to wait for the operation to finish.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="action" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="priority" /> is equal to <see cref="F:System.Windows.Threading.DispatcherPriority.Inactive" />.</exception>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">
        <paramref name="priority" /> is not a valid <see cref="T:System.Windows.Threading.DispatcherPriority" />.</exception>
    </member>
    <member name="M:System.Windows.Threading.DispatcherExtensions.Invoke(System.Windows.Threading.Dispatcher,System.Action,System.TimeSpan,System.Windows.Threading.DispatcherPriority)">
      <summary>Executes the specified delegate synchronously with the specified priority on the thread that the specified <see cref="T:System.Windows.Threading.Dispatcher" /> was created on, and stops execution after the specified time-out period.</summary>
      <param name="dispatcher">The dispatcher that executes the delegate.</param>
      <param name="action">The delegate to execute, which takes no arguments and does not return a value.</param>
      <param name="timeout">The maximum time to wait for the operation to finish.</param>
      <param name="priority">The execution priority of the delegate relative to other pending operations in the <see cref="T:System.Windows.Threading.Dispatcher" /> event queue.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="action" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="priority" /> is equal to <see cref="F:System.Windows.Threading.DispatcherPriority.Inactive" />.</exception>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">
        <paramref name="priority" /> is not a valid <see cref="T:System.Windows.Threading.DispatcherPriority" />.</exception>
    </member>
    <member name="M:System.Windows.Threading.DispatcherExtensions.Invoke(System.Windows.Threading.Dispatcher,System.Action,System.Windows.Threading.DispatcherPriority)">
      <summary>Executes the specified delegate synchronously with the specified priority on the thread that the specified <see cref="T:System.Windows.Threading.Dispatcher" /> was created on.</summary>
      <param name="dispatcher">The dispatcher that executes the delegate.</param>
      <param name="action">The delegate to execute, which takes no arguments and does not return a value.</param>
      <param name="priority">The execution priority of the delegate relative to other pending operations in the <see cref="T:System.Windows.Threading.Dispatcher" /> event queue.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="action" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="priority" /> is equal to <see cref="F:System.Windows.Threading.DispatcherPriority.Inactive" />.</exception>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">
        <paramref name="priority" /> is not a valid <see cref="T:System.Windows.Threading.DispatcherPriority" />.</exception>
    </member>
    <member name="T:System.Windows.Threading.TaskExtensions">
      <summary>Provides a set of <see langword="static" /> methods that extend the <see cref="T:System.Threading.Tasks.Task" /> class.</summary>
    </member>
    <member name="M:System.Windows.Threading.TaskExtensions.DispatcherOperationWait(System.Threading.Tasks.Task)">
      <summary>Waits indefinitely for the underlying <see cref="T:System.Windows.Threading.DispatcherOperation" /> to complete.</summary>
      <param name="this">The <see cref="T:System.Threading.Tasks.Task" /> that is associated with the <see cref="T:System.Windows.Threading.DispatcherOperation" />.</param>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Threading.Tasks.Task" /> is not associated with a <see cref="T:System.Windows.Threading.DispatcherOperation" />.</exception>
      <returns>The status of the underlying <see cref="T:System.Windows.Threading.DispatcherOperation" />.</returns>
    </member>
    <member name="M:System.Windows.Threading.TaskExtensions.DispatcherOperationWait(System.Threading.Tasks.Task,System.TimeSpan)">
      <summary>Waits for the specified amount of time for the underlying <see cref="T:System.Windows.Threading.DispatcherOperation" /> to complete.</summary>
      <param name="this">The <see cref="T:System.Threading.Tasks.Task" /> that is associated with the <see cref="T:System.Windows.Threading.DispatcherOperation" />.</param>
      <param name="timeout">The amount of time to wait.</param>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Threading.Tasks.Task" /> is not associated with a <see cref="T:System.Windows.Threading.DispatcherOperation" />.</exception>
      <returns>The status of the underlying <see cref="T:System.Windows.Threading.DispatcherOperation" />.</returns>
    </member>
    <member name="M:System.Windows.Threading.TaskExtensions.IsDispatcherOperationTask(System.Threading.Tasks.Task)">
      <summary>Returns a value that indicates whether this <see cref="T:System.Threading.Tasks.Task" /> is associated with a <see cref="T:System.Windows.Threading.DispatcherOperation" />.</summary>
      <param name="this">The <see cref="T:System.Threading.Tasks.Task" /> to check.</param>
      <returns>The status of the underlying <see cref="T:System.Windows.Threading.DispatcherOperation" />.</returns>
    </member>
  </members>
</doc>