<?xml version="1.0"?>
<doc>
    <assembly>
        <name>System.Diagnostics.EventLog</name>
    </assembly>
    <members>
        <member name="T:System.Diagnostics.EventLog">
            <summary>
            Provides interaction with Windows event logs.
            </summary>
        </member>
        <member name="P:System.Diagnostics.EventLog.Entries">
            <summary>
            The contents of the log.
            </summary>
        </member>
        <member name="P:System.Diagnostics.EventLog.Log">
            <summary>
            Gets or sets the name of the log to read from and write to.
            </summary>
        </member>
        <member name="P:System.Diagnostics.EventLog.MachineName">
            <summary>
            The machine on which this event log resides.
            </summary>
        </member>
        <member name="P:System.Diagnostics.EventLog.EnableRaisingEvents">
            <summary>
            Indicates if the component monitors the event log for changes.
            </summary>
        </member>
        <member name="P:System.Diagnostics.EventLog.SynchronizingObject">
            <summary>
            The object used to marshal the event handler calls issued as a result of an EventLog change.
            </summary>
        </member>
        <member name="P:System.Diagnostics.EventLog.Source">
            <summary>
            The application name (source name) to use when writing to the event log.
            </summary>
        </member>
        <member name="E:System.Diagnostics.EventLog.EntryWritten">
            <summary>
            Raised each time any application writes an entry to the event log.
            </summary>
        </member>
        <member name="P:System.Diagnostics.EventLogEntry.MachineName">
            <summary>
            The machine on which this event log resides.
            </summary>
        </member>
        <member name="P:System.Diagnostics.EventLogEntry.Data">
            <summary>
            The binary data associated with this entry in the event log.
            </summary>
        </member>
        <member name="P:System.Diagnostics.EventLogEntry.Index">
            <summary>
            The sequence of this entry in the event log.
            </summary>
        </member>
        <member name="P:System.Diagnostics.EventLogEntry.Category">
            <summary>
            The category for this message.
            </summary>
        </member>
        <member name="P:System.Diagnostics.EventLogEntry.CategoryNumber">
            <summary>
            An application-specific category number assigned to this entry.
            </summary>
        </member>
        <member name="P:System.Diagnostics.EventLogEntry.EventID">
            <summary>
            The number identifying the message for this source.
            </summary>
        </member>
        <member name="P:System.Diagnostics.EventLogEntry.EntryType">
            <summary>
            The type of entry - Information, Warning, etc.
            </summary>
        </member>
        <member name="P:System.Diagnostics.EventLogEntry.Message">
            <summary>
            The text of the message for this entry.
            </summary>
        </member>
        <member name="P:System.Diagnostics.EventLogEntry.Source">
            <summary>
            The name of the application that wrote this entry.
            </summary>
        </member>
        <member name="P:System.Diagnostics.EventLogEntry.ReplacementStrings">
            <summary>
            The application-supplied strings used in the message.
            </summary>
        </member>
        <member name="P:System.Diagnostics.EventLogEntry.InstanceId">
            <summary>
            The full number identifying the message in the event message dll.
            </summary>
        </member>
        <member name="P:System.Diagnostics.EventLogEntry.TimeGenerated">
            <summary>
            The time at which the application logged this entry.
            </summary>
        </member>
        <member name="P:System.Diagnostics.EventLogEntry.TimeWritten">
            <summary>
            The time at which the system logged this entry to the event log.
            </summary>
        </member>
        <member name="P:System.Diagnostics.EventLogEntry.UserName">
            <summary>
            The username of the account associated with this entry by the writing application.
            </summary>
        </member>
        <member name="T:System.Diagnostics.Eventing.Reader.CoTaskMemSafeHandle">
            <summary>
            A SafeHandle implementation over a native CoTaskMem allocated via StringToCoTaskMemAuto.
            </summary>
        </member>
        <member name="T:System.Diagnostics.Eventing.Reader.CoTaskMemUnicodeSafeHandle">
            <summary>
            A SafeHandle implementation over a native CoTaskMem allocated via SecureStringToCoTaskMemUnicode.
            </summary>
        </member>
        <member name="T:System.Diagnostics.Eventing.Reader.EventBookmark">
            <summary>
            Represents an opaque Event Bookmark obtained from an EventRecord.
            The bookmark denotes a unique identifier for the event instance as
            well as marks the location in the result set of the EventReader
            that the event instance was obtained from.
            </summary>
        </member>
        <member name="T:System.Diagnostics.Eventing.Reader.EventKeyword">
            <summary>
            Describes the metadata for a specific Keyword defined by a Provider.
            An instance of this class is obtained from a ProviderMetadata object.
            </summary>
        </member>
        <member name="T:System.Diagnostics.Eventing.Reader.EventLevel">
            <summary>
            Describes the metadata for a specific Level defined by a Provider.
            An instance of this class is obtained from a ProviderMetadata object.
            </summary>
        </member>
        <member name="T:System.Diagnostics.Eventing.Reader.EventLogType">
            <summary>
            Log Type
            </summary>
        </member>
        <member name="T:System.Diagnostics.Eventing.Reader.EventLogIsolation">
            <summary>
            Log Isolation
            </summary>
        </member>
        <member name="T:System.Diagnostics.Eventing.Reader.EventLogMode">
            <summary>
            Log Mode
            </summary>
        </member>
        <member name="T:System.Diagnostics.Eventing.Reader.EventLogConfiguration">
            <summary>
            Provides access to static log information and configures
            log publishing and log file properties.
            </summary>
        </member>
        <member name="T:System.Diagnostics.Eventing.Reader.EventLogException">
            <summary>
            describes an exception thrown from Event Log related classes.
            </summary>
        </member>
        <member name="T:System.Diagnostics.Eventing.Reader.EventLogNotFoundException">
            <summary>
            The object requested by the operation is not found.
            </summary>
        </member>
        <member name="T:System.Diagnostics.Eventing.Reader.EventLogReadingException">
            <summary>
            The state of the reader cursor has become invalid, most likely due to the fact
            that the log has been cleared.  User needs to obtain a new reader object if
            they wish to continue navigating result set.
            </summary>
        </member>
        <member name="T:System.Diagnostics.Eventing.Reader.EventLogProviderDisabledException">
            <summary>
            Provider has been uninstalled while ProviderMetadata operations are being performed.
            Obtain a new ProviderMetadata object, when provider is reinstalled, to continue navigating
            provider's metadata.
            </summary>
        </member>
        <member name="T:System.Diagnostics.Eventing.Reader.EventLogInvalidDataException">
            <summary>
            Data obtained from the eventlog service, for the current operation, is invalid .
            </summary>
        </member>
        <member name="T:System.Diagnostics.Eventing.Reader.EventLogHandle">
            <summary>
            A SafeHandle implementation over native EVT_HANDLE
            obtained from EventLog Native Methods.
            </summary>
        </member>
        <member name="T:System.Diagnostics.Eventing.Reader.EventLogInformation">
            <summary>
            Describes the run-time properties of logs and external log files. An instance
            of this class is obtained from EventLogSession.
            </summary>
        </member>
        <member name="T:System.Diagnostics.Eventing.Reader.EventLogLink">
            <summary>
            Describes the metadata for a specific Log Reference defined
            by a Provider. An instance of this class is obtained from
            a ProviderMetadata object.
            </summary>
        </member>
        <member name="T:System.Diagnostics.Eventing.Reader.EventLogPropertySelector">
            <summary>
             Encapsulates the information for fast access to Event Values
             of an EventLogRecord.  An instance of this class is constructed
             and then passed to EventLogRecord.GetEventPropertyValues.
            </summary>
        </member>
        <member name="T:System.Diagnostics.Eventing.Reader.EventLogQuery">
            <summary>
            Allows a user to define events of interest. An instance of this
            class is passed to an EventReader to actually obtain the EventRecords.
            The EventLogQuery can be as simple specifying that all events are of
            interest, or it can contain query / xpath expressions that indicate exactly
            what characteristics events should have.
            </summary>
        </member>
        <member name="T:System.Diagnostics.Eventing.Reader.EventLogReader">
            <summary>
            This public class is used for reading event records from event log.
            </summary>
        </member>
        <member name="F:System.Diagnostics.Eventing.Reader.EventLogReader._eventsBuffer">
            <summary>
            events buffer holds batched event (handles).
            </summary>
        </member>
        <member name="F:System.Diagnostics.Eventing.Reader.EventLogReader._currentIndex">
            <summary>
            The current index where the function GetNextEvent is (inside the eventsBuffer).
            </summary>
        </member>
        <member name="F:System.Diagnostics.Eventing.Reader.EventLogReader._eventCount">
            <summary>
            The number of events read from the batch into the eventsBuffer
            </summary>
        </member>
        <member name="F:System.Diagnostics.Eventing.Reader.EventLogReader._isEof">
            <summary>
            When the reader finishes (will always return only ERROR_NO_MORE_ITEMS).
            For subscription, this means we need to wait for next event.
            </summary>
        </member>
        <member name="F:System.Diagnostics.Eventing.Reader.EventLogReader._cachedMetadataInformation">
            <summary>
            Maintains cached display / metadata information returned from
            EventRecords that were obtained from this reader.
            </summary>
        </member>
        <member name="T:System.Diagnostics.Eventing.Reader.SessionAuthentication">
            <summary>
            Session Login Type
            </summary>
        </member>
        <member name="T:System.Diagnostics.Eventing.Reader.PathType">
            <summary>
            The type: log / external log file to query
            </summary>
        </member>
        <member name="T:System.Diagnostics.Eventing.Reader.EventLogSession">
            <summary>
            Defines a session for Event Log operations. The session can
            be configured for a remote machine and can use specific
            user credentials.
            </summary>
        </member>
        <member name="T:System.Diagnostics.Eventing.Reader.EventLogStatus">
            <summary>
            Describes the status of a particular log with respect to
            an instantiated EventLogReader.  Since it is possible to
            instantiate an EventLogReader with a query containing
            multiple logs and the reader can be configured to tolerate
            errors in attaching to those logs, this class allows the
            user to determine exactly what the status of those logs is.
            </summary>
        </member>
        <member name="T:System.Diagnostics.Eventing.Reader.EventLogWatcher">
            <summary>
            Used for subscribing to event record notifications from
            event log.
            </summary>
        </member>
        <member name="F:System.Diagnostics.Eventing.Reader.EventLogWatcher.cachedMetadataInformation">
            <summary>
            Maintains cached display / metadata information returned from
            EventRecords that were obtained from this reader.
            </summary>
        </member>
        <member name="T:System.Diagnostics.Eventing.Reader.EventMetadata">
            <summary>
            Event Metadata
            </summary>
        </member>
        <member name="T:System.Diagnostics.Eventing.Reader.EventOpcode">
            <summary>
            The metadata for a specific Opcode defined by a Provider.
            An instance of this class is obtained from a ProviderMetadata object.
            </summary>
        </member>
        <member name="T:System.Diagnostics.Eventing.Reader.EventRecord">
            <summary>
            Represents an event obtained from an EventReader.
            </summary>
        </member>
        <member name="T:System.Diagnostics.Eventing.Reader.EventRecordWrittenEventArgs">
            <summary>
            The custom event handler args.
            </summary>
        </member>
        <member name="P:System.Diagnostics.Eventing.Reader.EventRecordWrittenEventArgs.EventRecord">
            <summary>
            The EventRecord being notified.
            NOTE: If non null, then caller is required to call Dispose().
            </summary>
        </member>
        <member name="P:System.Diagnostics.Eventing.Reader.EventRecordWrittenEventArgs.EventException">
            <summary>
            If any error occured during subscription, this will be non-null.
            After a notification containing an exception, no more notifications will
            be made for this subscription.
            </summary>
        </member>
        <member name="T:System.Diagnostics.Eventing.Reader.EventTask">
            <summary>
            Describes the metadata for a specific Task defined by a Provider.
            An instance of this class is obtained from a ProviderMetadata object.
            </summary>
        </member>
        <member name="T:System.Diagnostics.Eventing.Reader.NativeWrapper">
            <summary>
            This internal class contains wrapper methods over the Native
            Methods of the Eventlog API. Unlike the raw Native Methods,
            these methods throw EventLogExceptions, check platform
            availability and perform additional helper functionality
            specific to function. Also, all methods of this class expose
            the Link Demand for Unmanaged Permission to callers.
            </summary>
        </member>
        <member name="T:System.Diagnostics.Eventing.Reader.ProviderMetadata">
            <summary>
            Exposes all the metadata for a specific event Provider.  An instance
            of this class is obtained from EventLogManagement and is scoped to a
            single Locale.
            </summary>
        </member>
        <member name="T:System.Diagnostics.Eventing.Reader.ProviderMetadataCachedInformation">
            <summary>
            This class does not expose underlying Provider metadata objects. Instead it
            exposes a limited set of Provider metadata information from the cache. The reason
            for this is so the cache can easily Dispose the metadata object without worrying
            about who is using it.
            </summary>
        </member>
        <member name="T:System.Diagnostics.Eventing.Reader.StandardEventLevel">
            <summary>
            WindowsEventLevel
            </summary>
        </member>
        <member name="F:System.Diagnostics.Eventing.Reader.StandardEventLevel.LogAlways">
            <summary>
            Log always
            </summary>
        </member>
        <member name="F:System.Diagnostics.Eventing.Reader.StandardEventLevel.Critical">
            <summary>
            Only critical errors
            </summary>
        </member>
        <member name="F:System.Diagnostics.Eventing.Reader.StandardEventLevel.Error">
            <summary>
            All errors, including previous levels
            </summary>
        </member>
        <member name="F:System.Diagnostics.Eventing.Reader.StandardEventLevel.Warning">
            <summary>
            All warnings, including previous levels
            </summary>
        </member>
        <member name="F:System.Diagnostics.Eventing.Reader.StandardEventLevel.Informational">
            <summary>
            All informational events, including previous levels
            </summary>
        </member>
        <member name="F:System.Diagnostics.Eventing.Reader.StandardEventLevel.Verbose">
            <summary>
            All events, including previous levels
            </summary>
        </member>
        <member name="T:System.Diagnostics.Eventing.Reader.StandardEventTask">
            <summary>
            WindowsEventTask
            </summary>
        </member>
        <member name="F:System.Diagnostics.Eventing.Reader.StandardEventTask.None">
            <summary>
            Undefined task
            </summary>
        </member>
        <member name="T:System.Diagnostics.Eventing.Reader.StandardEventOpcode">
            <summary>
            EventOpcode
            </summary>
        </member>
        <member name="F:System.Diagnostics.Eventing.Reader.StandardEventOpcode.Info">
            <summary>
            An informational event
            </summary>
        </member>
        <member name="F:System.Diagnostics.Eventing.Reader.StandardEventOpcode.Start">
            <summary>
            An activity start event
            </summary>
        </member>
        <member name="F:System.Diagnostics.Eventing.Reader.StandardEventOpcode.Stop">
            <summary>
            An activity end event
            </summary>
        </member>
        <member name="F:System.Diagnostics.Eventing.Reader.StandardEventOpcode.DataCollectionStart">
            <summary>
            A trace collection start event
            </summary>
        </member>
        <member name="F:System.Diagnostics.Eventing.Reader.StandardEventOpcode.DataCollectionStop">
            <summary>
            A trace collection end event
            </summary>
        </member>
        <member name="F:System.Diagnostics.Eventing.Reader.StandardEventOpcode.Extension">
            <summary>
            An extensional event
            </summary>
        </member>
        <member name="F:System.Diagnostics.Eventing.Reader.StandardEventOpcode.Reply">
            <summary>
            A reply event
            </summary>
        </member>
        <member name="F:System.Diagnostics.Eventing.Reader.StandardEventOpcode.Resume">
            <summary>
            An event representing the activity resuming from the suspension
            </summary>
        </member>
        <member name="F:System.Diagnostics.Eventing.Reader.StandardEventOpcode.Suspend">
            <summary>
            An event representing the activity is suspended, pending another activity's completion
            </summary>
        </member>
        <member name="F:System.Diagnostics.Eventing.Reader.StandardEventOpcode.Send">
            <summary>
            An event representing the activity is transferred to another component, and can continue to work
            </summary>
        </member>
        <member name="F:System.Diagnostics.Eventing.Reader.StandardEventOpcode.Receive">
            <summary>
            An event representing receiving an activity transfer from another component
            </summary>
        </member>
        <member name="T:System.Diagnostics.Eventing.Reader.StandardEventKeywords">
            <summary>
            EventOpcode
            </summary>
        </member>
        <member name="F:System.Diagnostics.Eventing.Reader.StandardEventKeywords.None">
            <summary>
            Wild card value
            </summary>
        </member>
        <member name="F:System.Diagnostics.Eventing.Reader.StandardEventKeywords.ResponseTime">
            <summary>
            Events providing response time information
            </summary>
        </member>
        <member name="F:System.Diagnostics.Eventing.Reader.StandardEventKeywords.WdiContext">
            <summary>
            WDI context events
            </summary>
        </member>
        <member name="F:System.Diagnostics.Eventing.Reader.StandardEventKeywords.WdiDiagnostic">
            <summary>
            WDI diagnostic events
            </summary>
        </member>
        <member name="F:System.Diagnostics.Eventing.Reader.StandardEventKeywords.Sqm">
            <summary>
            SQM events
            </summary>
        </member>
        <member name="F:System.Diagnostics.Eventing.Reader.StandardEventKeywords.AuditFailure">
            <summary>
            FAiled security audits
            </summary>
        </member>
        <member name="F:System.Diagnostics.Eventing.Reader.StandardEventKeywords.AuditSuccess">
            <summary>
            Successful security audits
            </summary>
        </member>
        <member name="F:System.Diagnostics.Eventing.Reader.StandardEventKeywords.CorrelationHint">
            <summary>
            Incorrect CorrelationHint value mistakenly shipped in .NET 3.5. Don't use: duplicates AuditFailure.
            </summary>
        </member>
        <member name="F:System.Diagnostics.Eventing.Reader.StandardEventKeywords.CorrelationHint2">
            <summary>
            Transfer events where the related Activity ID is a computed value and not a GUID
            </summary>
        </member>
        <member name="F:System.Diagnostics.Eventing.Reader.StandardEventKeywords.EventLogClassic">
            <summary>
            Events raised using classic eventlog API
            </summary>
        </member>
        <member name="P:System.SR.BadLogName">
            <summary>Event log names must consist of printable characters and cannot contain \\, *, ?, or spaces</summary>
        </member>
        <member name="P:System.SR.CannotDeleteEqualSource">
            <summary>The event log source '{0}' cannot be deleted, because it's equal to the log name.</summary>
        </member>
        <member name="P:System.SR.CantMonitorEventLog">
            <summary>Cannot monitor EntryWritten events for this EventLog. This might be because the EventLog is on a remote machine which is not a supported scenario.</summary>
        </member>
        <member name="P:System.SR.CantOpenLog">
            <summary>Cannot open log {0} on computer '{1}'. {2}</summary>
        </member>
        <member name="P:System.SR.CantOpenLogAccess">
            <summary>Cannot open log for source '{0}'. You may not have write access.</summary>
        </member>
        <member name="P:System.SR.CantReadLogEntryAt">
            <summary>Cannot read log entry number {0}.  The event log may be corrupt.</summary>
        </member>
        <member name="P:System.SR.CantRetrieveEntries">
            <summary>Cannot retrieve all entries.</summary>
        </member>
        <member name="P:System.SR.DuplicateLogName">
            <summary>Only the first eight characters of a custom log name are significant, and there is already another log on the system using the first eight characters of the name given. Name given: '{0}', name of existing log: '{1}'.</summary>
        </member>
        <member name="P:System.SR.EventID">
            <summary>Invalid eventID value '{0}'. It must be in the range between '{1}' and '{2}'.</summary>
        </member>
        <member name="P:System.SR.IndexOutOfBounds">
            <summary>Index {0} is out of bounds.</summary>
        </member>
        <member name="P:System.SR.InitTwice">
            <summary>Cannot initialize the same object twice.</summary>
        </member>
        <member name="P:System.SR.InvalidCustomerLogName">
            <summary>The log name: '{0}' is invalid for customer log creation.</summary>
        </member>
        <member name="P:System.SR.InvalidParameter">
            <summary>Invalid value '{1}' for parameter '{0}'.</summary>
        </member>
        <member name="P:System.SR.InvalidParameterFormat">
            <summary>Invalid format for argument {0}.</summary>
        </member>
        <member name="P:System.SR.LocalLogAlreadyExistsAsSource">
            <summary>Log {0} has already been registered as a source on the local computer.</summary>
        </member>
        <member name="P:System.SR.LocalRegKeyMissing">
            <summary>Cannot open registry key {0}\\{1}\\{2}.</summary>
        </member>
        <member name="P:System.SR.LocalSourceAlreadyExists">
            <summary>Source {0} already exists on the local computer.</summary>
        </member>
        <member name="P:System.SR.LocalSourceNotRegistered">
            <summary>Source {0} is not registered on the local computer.</summary>
        </member>
        <member name="P:System.SR.LogDoesNotExists">
            <summary>The event log '{0}' on computer '{1}' does not exist.</summary>
        </member>
        <member name="P:System.SR.LogEntryTooLong">
            <summary>Log entry string is too long. A string written to the event log cannot exceed 32766 characters.</summary>
        </member>
        <member name="P:System.SR.LogSourceMismatch">
            <summary>The source '{0}' is not registered in log '{1}'. (It is registered in log '{2}'.) " The Source and Log properties must be matched, or you may set Log to the empty string, and it will automatically be matched to the Source property.NoAccountInfo=Cannot obta ...</summary>
        </member>
        <member name="P:System.SR.MaximumKilobytesOutOfRange">
            <summary>MaximumKilobytes must be between 64 KB and 4 GB, and must be in 64K increments.</summary>
        </member>
        <member name="P:System.SR.MessageNotFormatted">
            <summary>The description for Event ID '{0}' in Source '{1}' cannot be found.  The local computer may not have the necessary registry information or message DLL files to display the message, or you may not have permission to access them.  The following information i ...</summary>
        </member>
        <member name="P:System.SR.MissingLog">
            <summary>Cannot find Log {0} on computer '{1}'.</summary>
        </member>
        <member name="P:System.SR.MissingLogProperty">
            <summary>Log property value has not been specified.</summary>
        </member>
        <member name="P:System.SR.MissingParameter">
            <summary>Must specify value for {0}.</summary>
        </member>
        <member name="P:System.SR.NeedSourceToOpen">
            <summary>Source property was not set before opening the event log in write mode.</summary>
        </member>
        <member name="P:System.SR.NeedSourceToWrite">
            <summary>Source property was not set before writing to the event log.</summary>
        </member>
        <member name="P:System.SR.NoCurrentEntry">
            <summary>No current EventLog entry available, cursor is located before the first or after the last element of the enumeration.</summary>
        </member>
        <member name="P:System.SR.NoLogName">
            <summary>Log to delete was not specified.</summary>
        </member>
        <member name="P:System.SR.ParameterTooLong">
            <summary>The size of {0} is too big. It cannot be longer than {1} characters.</summary>
        </member>
        <member name="P:System.SR.PlatformNotSupported_EventLog">
            <summary>EventLog access is not supported on this platform.</summary>
        </member>
        <member name="P:System.SR.RegKeyMissing">
            <summary>Cannot open registry key {0}\\{1}\\{2} on computer '{3}'.</summary>
        </member>
        <member name="P:System.SR.RegKeyMissingShort">
            <summary>Cannot open registry key {0} on computer {1}.</summary>
        </member>
        <member name="P:System.SR.RegKeyNoAccess">
            <summary>Cannot open registry key {0} on computer {1}. You might not have access.</summary>
        </member>
        <member name="P:System.SR.RentionDaysOutOfRange">
            <summary>'retentionDays' must be between 1 and 365 days.</summary>
        </member>
        <member name="P:System.SR.SomeLogsInaccessible">
            <summary>The source was not found, but some or all event logs could not be searched.  Inaccessible logs: {0}.</summary>
        </member>
        <member name="P:System.SR.SomeLogsInaccessibleToCreate">
            <summary>The source was not found, but some or all event logs could not be searched.  To create the source, you need permission to read all event logs to make sure that the new source name is unique.  Inaccessible logs: {0}.</summary>
        </member>
        <member name="P:System.SR.SourceAlreadyExists">
            <summary>Source {0} already exists on the computer '{1}'.</summary>
        </member>
        <member name="P:System.SR.SourceNotRegistered">
            <summary>The source '{0}' is not registered on machine '{1}', or you do not have write access to the {2} registry key.</summary>
        </member>
        <member name="P:System.SR.TooManyReplacementStrings">
            <summary>The maximum allowed number of replacement strings is 255.</summary>
        </member>
        <member name="P:System.SR.LogAlreadyExistsAsSource">
            <summary>Log {0} has already been registered as a source on the local computer.</summary>
        </member>
        <member name="P:System.SR.NotSupported_IONonFileDevices">
            <summary>Opening Win32 devices other than file such as COM ports, printers, disk partitions and tape drives is not supported. Avoid use of "\\\\.\\" in the path.</summary>
        </member>
        <member name="T:Microsoft.Win32.UnsafeNativeMethods.EvtVariantType">
            <summary>
            Evt Variant types
            </summary>
        </member>
        <member name="T:Microsoft.Win32.UnsafeNativeMethods.EvtQueryPropertyId">
            <summary>
            The query flags to get information about query
            </summary>
        </member>
        <member name="T:Microsoft.Win32.UnsafeNativeMethods.EvtPublisherMetadataPropertyId">
            <summary>
            Publisher Metadata properties
            </summary>
        </member>
    </members>
</doc>
