//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Reflection;

[assembly: System.Reflection.AssemblyCompanyAttribute("C-True")]
[assembly: System.Reflection.AssemblyConfigurationAttribute("Debug")]
[assembly: System.Reflection.AssemblyDescriptionAttribute(("\r\n\t\t\tAn easy to use wrapper for SimConnect, for connection to Flight Simulator 20" +
    "20.\r\n\t\t\tContains SimConnect binaries, as distributed by the Flight Simulator 202" +
    "02 SDK 0.10.0 release.\r\n\t\t"))]
[assembly: System.Reflection.AssemblyFileVersionAttribute("*******")]
[assembly: System.Reflection.AssemblyInformationalVersionAttribute("*******+.348053d46ede6b6e3e9ef2c97c85f29ebaaaadf9")]
[assembly: System.Reflection.AssemblyProductAttribute("Flight Simulator Connect")]
[assembly: System.Reflection.AssemblyTitleAttribute("CTrue.FsConnect")]
[assembly: System.Reflection.AssemblyVersionAttribute("*******")]
[assembly: System.Reflection.AssemblyMetadataAttribute("RepositoryUrl", "https://github.com/c-true/FsConnect")]

// Generated by the MSBuild WriteCodeFragment class.

