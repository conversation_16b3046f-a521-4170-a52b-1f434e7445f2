Microsoft.CSharp|4.4.0
Microsoft.Win32.Primitives|4.3.0
Microsoft.Win32.Registry|4.4.0
runtime.debian.8-x64.runtime.native.System|4.3.0
runtime.debian.8-x64.runtime.native.System.IO.Compression|4.3.0
runtime.debian.8-x64.runtime.native.System.Net.Http|4.3.0
runtime.debian.8-x64.runtime.native.System.Net.Security|4.3.0
runtime.debian.8-x64.runtime.native.System.Security.Cryptography|4.3.0
runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl|4.3.0
runtime.fedora.23-x64.runtime.native.System|4.3.0
runtime.fedora.23-x64.runtime.native.System.IO.Compression|4.3.0
runtime.fedora.23-x64.runtime.native.System.Net.Http|4.3.0
runtime.fedora.23-x64.runtime.native.System.Net.Security|4.3.0
runtime.fedora.23-x64.runtime.native.System.Security.Cryptography|4.3.0
runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl|4.3.0
runtime.fedora.24-x64.runtime.native.System|4.3.0
runtime.fedora.24-x64.runtime.native.System.IO.Compression|4.3.0
runtime.fedora.24-x64.runtime.native.System.Net.Http|4.3.0
runtime.fedora.24-x64.runtime.native.System.Net.Security|4.3.0
runtime.fedora.24-x64.runtime.native.System.Security.Cryptography|4.3.0
runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl|4.3.0
runtime.opensuse.13.2-x64.runtime.native.System|4.3.0
runtime.opensuse.13.2-x64.runtime.native.System.IO.Compression|4.3.0
runtime.opensuse.13.2-x64.runtime.native.System.Net.Http|4.3.0
runtime.opensuse.13.2-x64.runtime.native.System.Net.Security|4.3.0
runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography|4.3.0
runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl|4.3.0
runtime.opensuse.42.1-x64.runtime.native.System|4.3.0
runtime.opensuse.42.1-x64.runtime.native.System.IO.Compression|4.3.0
runtime.opensuse.42.1-x64.runtime.native.System.Net.Http|4.3.0
runtime.opensuse.42.1-x64.runtime.native.System.Net.Security|4.3.0
runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography|4.3.0
runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl|4.3.0
runtime.osx.10.10-x64.runtime.native.System|4.3.0
runtime.osx.10.10-x64.runtime.native.System.IO.Compression|4.3.0
runtime.osx.10.10-x64.runtime.native.System.Net.Http|4.3.0
runtime.osx.10.10-x64.runtime.native.System.Net.Security|4.3.0
runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography|4.3.0
runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple|4.3.0
runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl|4.3.0
runtime.rhel.7-x64.runtime.native.System|4.3.0
runtime.rhel.7-x64.runtime.native.System.IO.Compression|4.3.0
runtime.rhel.7-x64.runtime.native.System.Net.Http|4.3.0
runtime.rhel.7-x64.runtime.native.System.Net.Security|4.3.0
runtime.rhel.7-x64.runtime.native.System.Security.Cryptography|4.3.0
runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl|4.3.0
runtime.ubuntu.14.04-x64.runtime.native.System|4.3.0
runtime.ubuntu.14.04-x64.runtime.native.System.IO.Compression|4.3.0
runtime.ubuntu.14.04-x64.runtime.native.System.Net.Http|4.3.0
runtime.ubuntu.14.04-x64.runtime.native.System.Net.Security|4.3.0
runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography|4.3.0
runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl|4.3.0
runtime.ubuntu.16.04-x64.runtime.native.System|4.3.0
runtime.ubuntu.16.04-x64.runtime.native.System.IO.Compression|4.3.0
runtime.ubuntu.16.04-x64.runtime.native.System.Net.Http|4.3.0
runtime.ubuntu.16.04-x64.runtime.native.System.Net.Security|4.3.0
runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography|4.3.0
runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl|4.3.0
runtime.ubuntu.16.10-x64.runtime.native.System|4.3.0
runtime.ubuntu.16.10-x64.runtime.native.System.IO.Compression|4.3.0
runtime.ubuntu.16.10-x64.runtime.native.System.Net.Http|4.3.0
runtime.ubuntu.16.10-x64.runtime.native.System.Net.Security|4.3.0
runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography|4.3.0
runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl|4.3.0
System.AppContext|4.3.0
System.Buffers|4.4.0
System.Collections|4.3.0
System.Collections.Concurrent|4.3.0
System.Collections.Immutable|1.4.0
System.Collections.NonGeneric|4.3.0
System.Collections.Specialized|4.3.0
System.ComponentModel|4.3.0
System.ComponentModel.EventBasedAsync|4.3.0
System.ComponentModel.Primitives|4.3.0
System.ComponentModel.TypeConverter|4.3.0
System.Console|4.3.0
System.Data.Common|4.3.0
System.Diagnostics.Contracts|4.3.0
System.Diagnostics.Debug|4.3.0
System.Diagnostics.DiagnosticSource|4.4.0
System.Diagnostics.FileVersionInfo|4.3.0
System.Diagnostics.Process|4.3.0
System.Diagnostics.StackTrace|4.3.0
System.Diagnostics.TextWriterTraceListener|4.3.0
System.Diagnostics.Tools|4.3.0
System.Diagnostics.TraceSource|4.3.0
System.Diagnostics.Tracing|4.3.0
System.Dynamic.Runtime|4.3.0
System.Globalization|4.3.0
System.Globalization.Calendars|4.3.0
System.Globalization.Extensions|4.3.0
System.IO|4.3.0
System.IO.Compression|4.3.0
System.IO.Compression.ZipFile|4.3.0
System.IO.FileSystem|4.3.0
System.IO.FileSystem.AccessControl|4.4.0
System.IO.FileSystem.DriveInfo|4.3.0
System.IO.FileSystem.Primitives|4.3.0
System.IO.FileSystem.Watcher|4.3.0
System.IO.IsolatedStorage|4.3.0
System.IO.MemoryMappedFiles|4.3.0
System.IO.Pipes|4.3.0
System.IO.UnmanagedMemoryStream|4.3.0
System.Linq|4.3.0
System.Linq.Expressions|4.3.0
System.Linq.Queryable|4.3.0
System.Net.Http|4.3.0
System.Net.NameResolution|4.3.0
System.Net.Primitives|4.3.0
System.Net.Requests|4.3.0
System.Net.Security|4.3.0
System.Net.Sockets|4.3.0
System.Net.WebHeaderCollection|4.3.0
System.ObjectModel|4.3.0
System.Private.DataContractSerialization|4.3.0
System.Reflection|4.3.0
System.Reflection.Emit|4.3.0
System.Reflection.Emit.ILGeneration|4.3.0
System.Reflection.Emit.Lightweight|4.3.0
System.Reflection.Extensions|4.3.0
System.Reflection.Metadata|1.5.0
System.Reflection.Primitives|4.3.0
System.Reflection.TypeExtensions|4.3.0
System.Resources.ResourceManager|4.3.0
System.Runtime|4.3.0
System.Runtime.Extensions|4.3.0
System.Runtime.Handles|4.3.0
System.Runtime.InteropServices|4.3.0
System.Runtime.InteropServices.RuntimeInformation|4.3.0
System.Runtime.Loader|4.3.0
System.Runtime.Numerics|4.3.0
System.Runtime.Serialization.Formatters|4.3.0
System.Runtime.Serialization.Json|4.3.0
System.Runtime.Serialization.Primitives|4.3.0
System.Security.AccessControl|4.4.0
System.Security.Claims|4.3.0
System.Security.Cryptography.Algorithms|4.3.0
System.Security.Cryptography.Cng|4.4.0
System.Security.Cryptography.Csp|4.3.0
System.Security.Cryptography.Encoding|4.3.0
System.Security.Cryptography.OpenSsl|4.4.0
System.Security.Cryptography.Primitives|4.3.0
System.Security.Cryptography.X509Certificates|4.3.0
System.Security.Cryptography.Xml|4.4.0
System.Security.Principal|4.3.0
System.Security.Principal.Windows|4.4.0
System.Text.Encoding|4.3.0
System.Text.Encoding.Extensions|4.3.0
System.Text.RegularExpressions|4.3.0
System.Threading|4.3.0
System.Threading.Overlapped|4.3.0
System.Threading.Tasks|4.3.0
System.Threading.Tasks.Extensions|4.3.0
System.Threading.Tasks.Parallel|4.3.0
System.Threading.Thread|4.3.0
System.Threading.ThreadPool|4.3.0
System.Threading.Timer|4.3.0
System.ValueTuple|4.3.0
System.Xml.ReaderWriter|4.3.0
System.Xml.XDocument|4.3.0
System.Xml.XmlDocument|4.3.0
System.Xml.XmlSerializer|4.3.0
System.Xml.XPath|4.3.0
System.Xml.XPath.XDocument|4.3.0
