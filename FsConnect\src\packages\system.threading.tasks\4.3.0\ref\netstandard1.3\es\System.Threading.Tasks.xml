﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Threading.Tasks</name>
  </assembly>
  <members>
    <member name="T:System.AggregateException">
      <summary>Representa uno o varios errores que se producen durante la ejecución de una aplicación.</summary>
    </member>
    <member name="M:System.AggregateException.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.AggregateException" /> con un mensaje proporcionado por el sistema que describe el error.</summary>
    </member>
    <member name="M:System.AggregateException.#ctor(System.Collections.Generic.IEnumerable{System.Exception})">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.AggregateException" /> que hace referencia a las excepciones internas que representan la causa de esta excepción.</summary>
      <param name="innerExceptions">Excepciones que representan la causa de la excepción actual.</param>
      <exception cref="T:System.ArgumentNullException">El argumento de <paramref name="innerExceptions" /> es nulo.</exception>
      <exception cref="T:System.ArgumentException">Un elemento de <paramref name="innerExceptions" /> es NULL.</exception>
    </member>
    <member name="M:System.AggregateException.#ctor(System.Exception[])">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.AggregateException" /> que hace referencia a las excepciones internas que representan la causa de esta excepción.</summary>
      <param name="innerExceptions">Excepciones que representan la causa de la excepción actual.</param>
      <exception cref="T:System.ArgumentNullException">El argumento de <paramref name="innerExceptions" /> es nulo.</exception>
      <exception cref="T:System.ArgumentException">Un elemento de <paramref name="innerExceptions" /> es NULL.</exception>
    </member>
    <member name="M:System.AggregateException.#ctor(System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.AggregateException" /> con un mensaje especificado que describe el error.</summary>
      <param name="message">Mensaje que describe la excepción.El llamador de este constructor debe asegurarse de que esta cadena se ha traducido para la actual referencia cultural del sistema.</param>
    </member>
    <member name="M:System.AggregateException.#ctor(System.String,System.Collections.Generic.IEnumerable{System.Exception})">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.AggregateException" /> con un mensaje de error especificado y referencias a las excepciones internas que representan la causa de esta excepción.</summary>
      <param name="message">Mensaje de error que explica la razón de la excepción.</param>
      <param name="innerExceptions">Excepciones que representan la causa de la excepción actual.</param>
      <exception cref="T:System.ArgumentNullException">El argumento de <paramref name="innerExceptions" /> es nulo.</exception>
      <exception cref="T:System.ArgumentException">Un elemento de <paramref name="innerExceptions" /> es NULL.</exception>
    </member>
    <member name="M:System.AggregateException.#ctor(System.String,System.Exception)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.AggregateException" /> con el mensaje de error especificado y una referencia a la excepción interna que representa la causa de esta excepción.</summary>
      <param name="message">Mensaje que describe la excepción.El llamador de este constructor debe asegurarse de que esta cadena se ha traducido para la actual referencia cultural del sistema.</param>
      <param name="innerException">La excepción que es la causa de la excepción actual.Si el parámetro <paramref name="innerException" /> no es null, la excepción actual se produce en un bloque catch que controla la excepción interna.</param>
      <exception cref="T:System.ArgumentNullException">El argumento de <paramref name="innerException" /> es nulo.</exception>
    </member>
    <member name="M:System.AggregateException.#ctor(System.String,System.Exception[])">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.AggregateException" /> con un mensaje de error especificado y referencias a las excepciones internas que representan la causa de esta excepción.</summary>
      <param name="message">Mensaje de error que explica la razón de la excepción.</param>
      <param name="innerExceptions">Excepciones que representan la causa de la excepción actual.</param>
      <exception cref="T:System.ArgumentNullException">El argumento de <paramref name="innerExceptions" /> es nulo.</exception>
      <exception cref="T:System.ArgumentException">Un elemento de <paramref name="innerExceptions" /> es NULL.</exception>
    </member>
    <member name="M:System.AggregateException.Flatten">
      <summary>Reduce las instancias de <see cref="T:System.AggregateException" /> a una sola instancia nueva.</summary>
      <returns>Nueva instancia reducida de <see cref="T:System.AggregateException" />.</returns>
    </member>
    <member name="M:System.AggregateException.GetBaseException">
      <summary>Devuelve la excepción <see cref="T:System.AggregateException" /> que es la causa principal de esta excepción.</summary>
      <returns>Devuelve la excepción <see cref="T:System.AggregateException" /> que es la causa principal de esta excepción.</returns>
    </member>
    <member name="M:System.AggregateException.Handle(System.Func{System.Exception,System.Boolean})">
      <summary>Invoca un controlador en cada objeto <see cref="T:System.Exception" /> contenido en esta excepción <see cref="T:System.AggregateException" />.</summary>
      <param name="predicate">Predicado que se va a ejecutar para cada excepción.El predicado acepta como argumento el objeto <see cref="T:System.Exception" /> que se va a procesar y devuelve un valor booleano para indicar si se controló la excepción.</param>
      <exception cref="T:System.ArgumentNullException">El argumento de <paramref name="predicate" /> es nulo.</exception>
      <exception cref="T:System.AggregateException">No se controló ninguna excepción incluida en esta excepción <see cref="T:System.AggregateException" />.</exception>
    </member>
    <member name="P:System.AggregateException.InnerExceptions">
      <summary>Obtiene una colección de solo lectura de las instancias de <see cref="T:System.Exception" /> que causaron la excepción actual.</summary>
      <returns>Devuelve una colección de solo lectura de las instancias de <see cref="T:System.Exception" /> que causaron la excepción actual.</returns>
    </member>
    <member name="M:System.AggregateException.ToString">
      <summary>Crea y devuelve una representación en forma de cadena del objeto <see cref="T:System.AggregateException" />.</summary>
      <returns>Representación de cadena de la excepción actual.</returns>
    </member>
    <member name="T:System.OperationCanceledException">
      <summary>Excepción que se produce en un subproceso al cancelar una operación que este estaba ejecutando.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.OperationCanceledException.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.OperationCanceledException" /> con un mensaje de error proporcionado por el sistema.</summary>
    </member>
    <member name="M:System.OperationCanceledException.#ctor(System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.OperationCanceledException" /> con el mensaje de error especificado.</summary>
      <param name="message">Objeto <see cref="T:System.String" /> que describe el error.</param>
    </member>
    <member name="M:System.OperationCanceledException.#ctor(System.String,System.Exception)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.OperationCanceledException" /> con el mensaje de error especificado y una referencia a la excepción interna que representa la causa de esta excepción.</summary>
      <param name="message">Mensaje de error que explica la razón de la excepción. </param>
      <param name="innerException">La excepción que es la causa de la excepción actual.Si el parámetro <paramref name="innerException" /> no es null, la excepción actual se produce en un bloque catch que controla la excepción interna.</param>
    </member>
    <member name="M:System.OperationCanceledException.#ctor(System.String,System.Exception,System.Threading.CancellationToken)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.OperationCanceledException" /> con un mensaje de error especificado, una referencia a la excepción interna que representa la causa de esta excepción y un token de cancelación.</summary>
      <param name="message">Mensaje de error que explica la razón de la excepción. </param>
      <param name="innerException">La excepción que es la causa de la excepción actual.Si el parámetro <paramref name="innerException" /> no es null, la excepción actual se produce en un bloque catch que controla la excepción interna.</param>
      <param name="token">Un token de cancelación asociado a la operación que se canceló.</param>
    </member>
    <member name="M:System.OperationCanceledException.#ctor(System.String,System.Threading.CancellationToken)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.OperationCanceledException" /> con un mensaje de error especificado y un token de cancelación.</summary>
      <param name="message">Mensaje de error que explica la razón de la excepción.</param>
      <param name="token">Un token de cancelación asociado a la operación que se canceló.</param>
    </member>
    <member name="M:System.OperationCanceledException.#ctor(System.Threading.CancellationToken)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.OperationCanceledException" /> con un token de cancelación.</summary>
      <param name="token">Un token de cancelación asociado a la operación que se canceló.</param>
    </member>
    <member name="P:System.OperationCanceledException.CancellationToken">
      <summary>Obtiene un token de cancelación asociado a la operación que se canceló.</summary>
      <returns>Un token de cancelación asociado a la operación que se canceló, o un token predeterminado.</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.AsyncTaskMethodBuilder">
      <summary>Representa un generador de métodos asincrónicos que devuelven una tarea.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.AwaitOnCompleted``2(``0@,``1@)">
      <summary>Programa la máquina de estados para continuar en la siguiente acción cuando se complete el awaiter especificado.</summary>
      <param name="awaiter">Awaiter.</param>
      <param name="stateMachine">Máquina de estados.</param>
      <typeparam name="TAwaiter">Tipo de awaiter.</typeparam>
      <typeparam name="TStateMachine">Tipo de la máquina de estados.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.AwaitUnsafeOnCompleted``2(``0@,``1@)">
      <summary>Programa la máquina de estados para continuar en la siguiente acción cuando se complete el awaiter especificado.Se puede llamar a este método desde el código de confianza parcial.</summary>
      <param name="awaiter">Awaiter.</param>
      <param name="stateMachine">Máquina de estados.</param>
      <typeparam name="TAwaiter">Tipo de awaiter.</typeparam>
      <typeparam name="TStateMachine">Tipo de la máquina de estados.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.Create">
      <summary>Cree una instancia de la clase <see cref="T:System.Runtime.CompilerServices.AsyncTaskMethodBuilder" />.</summary>
      <returns>Nueva instancia del generador.</returns>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetException(System.Exception)">
      <summary>Marca la tarea como errónea y enlaza la excepción especificada a la tarea.</summary>
      <param name="exception">Excepción que se va a enlazar a la tarea.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="exception" /> es null.</exception>
      <exception cref="T:System.InvalidOperationException">La tarea ya se ha completado.O bienEl generador no se inicializó.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetResult">
      <summary>Marca la tarea como finalizada correctamente.</summary>
      <exception cref="T:System.InvalidOperationException">La tarea ya se ha completado.O bienEl generador no se inicializó.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.SetStateMachine(System.Runtime.CompilerServices.IAsyncStateMachine)">
      <summary>Asocia el generador al equipo de estado especificado.</summary>
      <param name="stateMachine">La instancia de la máquina de estados que se va a asociar al generador.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" /> es null.</exception>
      <exception cref="T:System.InvalidOperationException">La máquina de estados que se estableció previamente.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.Start``1(``0@)">
      <summary>Comienza la ejecución del generador con el equipo de estado asociado.</summary>
      <param name="stateMachine">La instancia de la máquina de estados, pasada por referencia.</param>
      <typeparam name="TStateMachine">Tipo de la máquina de estados.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" /> es null.</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.AsyncTaskMethodBuilder.Task">
      <summary>Obtiene la tarea para este generador.</summary>
      <returns>La tarea para este generador.</returns>
      <exception cref="T:System.InvalidOperationException">El generador no se inicializó.</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1">
      <summary>Representa un generador de métodos asincrónicos que devuelve una tarea y proporciona un parámetro para el resultado.</summary>
      <typeparam name="TResult">El resultado que se va a usar para completar la tarea.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AwaitOnCompleted``2(``0@,``1@)">
      <summary>Programa la máquina de estados para continuar en la siguiente acción cuando se complete el awaiter especificado.</summary>
      <param name="awaiter">Awaiter.</param>
      <param name="stateMachine">Máquina de estados.</param>
      <typeparam name="TAwaiter">Tipo de awaiter.</typeparam>
      <typeparam name="TStateMachine">Tipo de la máquina de estados.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.AwaitUnsafeOnCompleted``2(``0@,``1@)">
      <summary>Programa la máquina de estados para continuar en la siguiente acción cuando se complete el awaiter especificado.Se puede llamar a este método desde el código de confianza parcial.</summary>
      <param name="awaiter">Awaiter.</param>
      <param name="stateMachine">Máquina de estados.</param>
      <typeparam name="TAwaiter">Tipo de awaiter.</typeparam>
      <typeparam name="TStateMachine">Tipo de la máquina de estados.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.Create">
      <summary>Cree una instancia de la clase <see cref="T:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1" />.</summary>
      <returns>Nueva instancia del generador.</returns>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetException(System.Exception)">
      <summary>Marca la tarea como errónea y enlaza la excepción especificada a la tarea.</summary>
      <param name="exception">Excepción que se va a enlazar a la tarea.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="exception" /> es null.</exception>
      <exception cref="T:System.InvalidOperationException">La tarea ya se ha completado.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetResult(`0)">
      <summary>Marca la tarea como finalizada correctamente.</summary>
      <param name="result">El resultado que se va a usar para completar la tarea.</param>
      <exception cref="T:System.InvalidOperationException">La tarea ya se ha completado.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.SetStateMachine(System.Runtime.CompilerServices.IAsyncStateMachine)">
      <summary>Asocia el generador al equipo de estado especificado.</summary>
      <param name="stateMachine">La instancia de la máquina de estados que se va a asociar al generador.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" /> es null.</exception>
      <exception cref="T:System.InvalidOperationException">La máquina de estados que se estableció previamente.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.Start``1(``0@)">
      <summary>Comienza la ejecución del generador con el equipo de estado asociado.</summary>
      <param name="stateMachine">La instancia de la máquina de estados, pasada por referencia.</param>
      <typeparam name="TStateMachine">Tipo de la máquina de estados.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" /> es null.</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.AsyncTaskMethodBuilder`1.Task">
      <summary>Obtiene la tarea para este generador.</summary>
      <returns>La tarea para este generador.</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.AsyncVoidMethodBuilder">
      <summary>Representa un generador de métodos asincrónicos que no devuelven un valor.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitOnCompleted``2(``0@,``1@)">
      <summary>Programa la máquina de estados para continuar en la siguiente acción cuando se complete el awaiter especificado.</summary>
      <param name="awaiter">Awaiter.</param>
      <param name="stateMachine">Máquina de estados.</param>
      <typeparam name="TAwaiter">Tipo de awaiter.</typeparam>
      <typeparam name="TStateMachine">Tipo de la máquina de estados.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted``2(``0@,``1@)">
      <summary>Programa la máquina de estados para continuar en la siguiente acción cuando se complete el awaiter especificado.Se puede llamar a este método desde el código de confianza parcial.</summary>
      <param name="awaiter">Awaiter.</param>
      <param name="stateMachine">Máquina de estados.</param>
      <typeparam name="TAwaiter">Tipo de awaiter.</typeparam>
      <typeparam name="TStateMachine">Tipo de la máquina de estados.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Create">
      <summary>Cree una instancia de la clase <see cref="T:System.Runtime.CompilerServices.AsyncVoidMethodBuilder" />.</summary>
      <returns>Nueva instancia del generador.</returns>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.SetException(System.Exception)">
      <summary>Enlaza una excepción al generador de métodos.</summary>
      <param name="exception">Excepción que se va a enlazar.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="exception" /> es null.</exception>
      <exception cref="T:System.InvalidOperationException">El generador no se inicializó.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.SetResult">
      <summary>Marca el generador de métodos como completado correctamente.</summary>
      <exception cref="T:System.InvalidOperationException">El generador no se inicializó.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.SetStateMachine(System.Runtime.CompilerServices.IAsyncStateMachine)">
      <summary>Asocia el generador al equipo de estado especificado.</summary>
      <param name="stateMachine">La instancia de la máquina de estados que se va a asociar al generador.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" /> es null.</exception>
      <exception cref="T:System.InvalidOperationException">La máquina de estados que se estableció previamente.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start``1(``0@)">
      <summary>Comienza la ejecución del generador con el equipo de estado asociado.</summary>
      <param name="stateMachine">La instancia de la máquina de estados, pasada por referencia.</param>
      <typeparam name="TStateMachine">Tipo de la máquina de estados.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stateMachine" /> es null.</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable">
      <summary>Proporciona un objeto que admite await que permite configurar objetos await en una tarea.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.GetAwaiter">
      <summary>Devuelve un awaiter para este objeto que admite await.</summary>
      <returns>Awaiter.</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1">
      <summary>Proporciona un objeto que admite await que permite configurar objetos await en una tarea.</summary>
      <typeparam name="TResult">El tipo del resultado generado por <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.GetAwaiter">
      <summary>Devuelve un awaiter para este objeto que admite await.</summary>
      <returns>Awaiter.</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter">
      <summary>Proporciona un objeto que ejecuta await para un objeto que admite await (<see cref="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1" />).</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter.GetResult">
      <summary>Finaliza la espera sobre la tarea completada.</summary>
      <returns>Resultado de la tarea completada.</returns>
      <exception cref="T:System.NullReferenceException">Awaiter no se ha inicializado correctamente.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">Se canceló la tarea.</exception>
      <exception cref="T:System.Exception">La tarea completada en un estado con error.</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter.IsCompleted">
      <summary>Obtiene un valor que especifica si la tarea que se espera está completada.</summary>
      <returns>true si se ha completado la tarea que estaba en espera; si no, false.</returns>
      <exception cref="T:System.NullReferenceException">Awaiter no se ha inicializado correctamente.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter.OnCompleted(System.Action)">
      <summary>Programa la acción de continuación para la tarea asociada a este awaiter.</summary>
      <param name="continuation">Acción que se va a invocar cuando se complete la operación await.</param>
      <exception cref="T:System.ArgumentNullException">El argumento <paramref name="continuation" /> es null.</exception>
      <exception cref="T:System.NullReferenceException">Awaiter no se ha inicializado correctamente.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter.UnsafeOnCompleted(System.Action)">
      <summary>Programa la acción de continuación para la tarea asociada a este awaiter. </summary>
      <param name="continuation">Acción que se va a invocar cuando se complete la operación await.</param>
      <exception cref="T:System.ArgumentNullException">El argumento <paramref name="continuation" /> es null.</exception>
      <exception cref="T:System.NullReferenceException">Awaiter no se ha inicializado correctamente.</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter">
      <summary>Proporciona un objeto que ejecuta await para un objeto (<see cref="T:System.Runtime.CompilerServices.ConfiguredTaskAwaitable" />) que admite await.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter.GetResult">
      <summary>Finaliza la espera sobre la tarea completada.</summary>
      <exception cref="T:System.NullReferenceException">Awaiter no se ha inicializado correctamente.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">Se canceló la tarea.</exception>
      <exception cref="T:System.Exception">La tarea completada en un estado con error.</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter.IsCompleted">
      <summary>Obtiene un valor que especifica si la tarea que se espera está completada.</summary>
      <returns>true si se ha completado la tarea que estaba en espera; si no, false.</returns>
      <exception cref="T:System.NullReferenceException">Awaiter no se ha inicializado correctamente.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter.OnCompleted(System.Action)">
      <summary>Programa la acción de continuación para la tarea asociada a este awaiter.</summary>
      <param name="continuation">Acción que se va a invocar cuando se complete la operación await.</param>
      <exception cref="T:System.ArgumentNullException">El argumento <paramref name="continuation" /> es null.</exception>
      <exception cref="T:System.NullReferenceException">Awaiter no se ha inicializado correctamente.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter.UnsafeOnCompleted(System.Action)">
      <summary>Programa la acción de continuación para la tarea asociada a este awaiter. </summary>
      <param name="continuation">Acción que se va a invocar cuando se complete la operación await.</param>
      <exception cref="T:System.ArgumentNullException">El argumento <paramref name="continuation" /> es null.</exception>
      <exception cref="T:System.NullReferenceException">Awaiter no se ha inicializado correctamente.</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.IAsyncStateMachine">
      <summary>Representa las máquinas de estado que se generan para los métodos asincrónicos.Este tipo solo es para uso del compilador.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.IAsyncStateMachine.MoveNext">
      <summary>Mueve el equipo de estado al estado siguiente.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.IAsyncStateMachine.SetStateMachine(System.Runtime.CompilerServices.IAsyncStateMachine)">
      <summary>Configura la máquina de estado con una réplica de asignación del montón.</summary>
      <param name="stateMachine">Réplica asignada por montón.</param>
    </member>
    <member name="T:System.Runtime.CompilerServices.ICriticalNotifyCompletion">
      <summary>Representa un elemento awaiter que programa continuaciones cuando se completa una operación await.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.ICriticalNotifyCompletion.UnsafeOnCompleted(System.Action)">
      <summary>Programa la acción de continuación que se invoca al completarse la instancia.</summary>
      <param name="continuation">Acción al que debe invocarse cuando se complete la operación.</param>
      <exception cref="T:System.ArgumentNullException">El argumento <paramref name="continuation" /> es NULL (Nothing en Visual Basic).</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.INotifyCompletion">
      <summary>Representa una operación que programa continuaciones cuando finaliza.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.INotifyCompletion.OnCompleted(System.Action)">
      <summary>Programa la acción de continuación que se invoca al completarse la instancia.</summary>
      <param name="continuation">Acción al que debe invocarse cuando se complete la operación.</param>
      <exception cref="T:System.ArgumentNullException">El argumento <paramref name="continuation" /> es NULL (Nothing en Visual Basic).</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.TaskAwaiter">
      <summary>Proporciona un objeto que espera la finalización de una tarea asincrónica. </summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter.GetResult">
      <summary>Finaliza la espera para la finalización de la tarea asincrónica.</summary>
      <exception cref="T:System.NullReferenceException">El objeto <see cref="T:System.Runtime.CompilerServices.TaskAwaiter" /> no se ha inicializado correctamente.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">Se canceló la tarea.</exception>
      <exception cref="T:System.Exception">La tarea completada en un estado de <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" />.</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.TaskAwaiter.IsCompleted">
      <summary>Obtiene un valor que indica si se ha completado la tarea asincrónica.</summary>
      <returns>true si la tarea se ha completado; de lo contrario, es false.</returns>
      <exception cref="T:System.NullReferenceException">El objeto <see cref="T:System.Runtime.CompilerServices.TaskAwaiter" /> no se ha inicializado correctamente.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter.OnCompleted(System.Action)">
      <summary>Establece la acción que se va a realizar cuando el objeto <see cref="T:System.Runtime.CompilerServices.TaskAwaiter" /> detiene la espera de la tarea asincrónica que se debe completar.</summary>
      <param name="continuation">La acción que se realizará cuando se complete la operación de espera.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="continuation" /> es null.</exception>
      <exception cref="T:System.NullReferenceException">El objeto <see cref="T:System.Runtime.CompilerServices.TaskAwaiter" /> no se ha inicializado correctamente.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter.UnsafeOnCompleted(System.Action)">
      <summary>Programa la acción de continuación para la tarea asincrónica que se asocia a este awaiter.</summary>
      <param name="continuation">Acción que se va a invocar cuando se complete la operación await.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="continuation" /> es null.</exception>
      <exception cref="T:System.InvalidOperationException">Awaiter no se ha inicializado correctamente.</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.TaskAwaiter`1">
      <summary>Representa un objeto que espera la finalización de una tarea asincrónica y proporciona un parámetro para el resultado.</summary>
      <typeparam name="TResult">Resultado de la tarea.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter`1.GetResult">
      <summary>Finaliza la espera para la finalización de la tarea asincrónica.</summary>
      <returns>Resultado de la tarea completada.</returns>
      <exception cref="T:System.NullReferenceException">El objeto <see cref="T:System.Runtime.CompilerServices.TaskAwaiter`1" /> no se ha inicializado correctamente.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">Se canceló la tarea.</exception>
      <exception cref="T:System.Exception">La tarea completada en un estado de <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" />.</exception>
    </member>
    <member name="P:System.Runtime.CompilerServices.TaskAwaiter`1.IsCompleted">
      <summary>Obtiene un valor que indica si se ha completado la tarea asincrónica.</summary>
      <returns>true si la tarea se ha completado; de lo contrario, es false.</returns>
      <exception cref="T:System.NullReferenceException">El objeto <see cref="T:System.Runtime.CompilerServices.TaskAwaiter`1" /> no se ha inicializado correctamente.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter`1.OnCompleted(System.Action)">
      <summary>Establece la acción que se va a realizar cuando el objeto <see cref="T:System.Runtime.CompilerServices.TaskAwaiter`1" /> detiene la espera de la tarea asincrónica que se debe completar.</summary>
      <param name="continuation">La acción que se realizará cuando se complete la operación de espera.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="continuation" /> es null.</exception>
      <exception cref="T:System.NullReferenceException">El objeto <see cref="T:System.Runtime.CompilerServices.TaskAwaiter`1" /> no se ha inicializado correctamente.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.TaskAwaiter`1.UnsafeOnCompleted(System.Action)">
      <summary>Programa la acción de continuación para la tarea asincrónica asociada a este awaiter.</summary>
      <param name="continuation">Acción que se va a invocar cuando se complete la operación await.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="continuation" /> es null.</exception>
      <exception cref="T:System.InvalidOperationException">Awaiter no se ha inicializado correctamente.</exception>
    </member>
    <member name="T:System.Runtime.CompilerServices.YieldAwaitable">
      <summary>Proporciona el contexto para esperar la conmutación asincrónica en un entorno de destino.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.YieldAwaitable.GetAwaiter">
      <summary>Recupera un objeto <see cref="T:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter" /> para esta instancia de la clase.</summary>
      <returns>Objeto que se usa para controlar la finalización de una operación asincrónica.</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter">
      <summary>Proporciona un awaiter para pasar a un entorno de destino.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter.GetResult">
      <summary>Termina la operación de espera.</summary>
    </member>
    <member name="P:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter.IsCompleted">
      <summary>Obtiene un valor que indica si un campo no se requiere.</summary>
      <returns>Siempre false, lo que indica que un valor es necesario siempre para <see cref="T:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter" />.</returns>
    </member>
    <member name="M:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter.OnCompleted(System.Action)">
      <summary>Establece la continuación que se va a invocar.</summary>
      <param name="continuation">La acción para invocar de forma asincrónica.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="continuation" /> es null.</exception>
    </member>
    <member name="M:System.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter.UnsafeOnCompleted(System.Action)">
      <summary>Vuelve a publicar <paramref name="continuation" /> en el contexto actual.</summary>
      <param name="continuation">La acción para invocar de forma asincrónica.</param>
      <exception cref="T:System.ArgumentNullException">El argumento <paramref name="continuation" /> es null.</exception>
    </member>
    <member name="T:System.Threading.CancellationToken">
      <summary>Propaga la notificación de que las operaciones deben cancelarse.</summary>
    </member>
    <member name="M:System.Threading.CancellationToken.#ctor(System.Boolean)">
      <summary>Inicializa el objeto <see cref="T:System.Threading.CancellationToken" />.</summary>
      <param name="canceled">El estado cancelado para el token.</param>
    </member>
    <member name="P:System.Threading.CancellationToken.CanBeCanceled">
      <summary>Obtiene si este token es capaz de existir en el estado cancelado.</summary>
      <returns>Es true si este token puede estar en estado cancelado; en caso contrario, es false.</returns>
    </member>
    <member name="M:System.Threading.CancellationToken.Equals(System.Object)">
      <summary>Determina si la instancia de <see cref="T:System.Threading.CancellationToken" /> actual es igual que la instancia de <see cref="T:System.Object" /> especificada.</summary>
      <returns>Es true si <paramref name="other" /> es <see cref="T:System.Threading.CancellationToken" /> y si las dos instancias son iguales; en caso contrario, es false.Dos tokens son iguales si están asociados al mismo <see cref="T:System.Threading.CancellationTokenSource" /> o si ambos se construyeron a partir de los constructores CancellationToken públicos y sus valores <see cref="P:System.Threading.CancellationToken.IsCancellationRequested" /> son iguales.</returns>
      <param name="other">Otro objeto con el que se va a comparar esta instancia.</param>
      <exception cref="T:System.ObjectDisposedException">An associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.Equals(System.Threading.CancellationToken)">
      <summary>Determina si la instancia de <see cref="T:System.Threading.CancellationToken" /> actual es igual que el token especificado.</summary>
      <returns>Es true si las instancias son iguales; en caso contrario, es false.Dos tokens son iguales si están asociados al mismo <see cref="T:System.Threading.CancellationTokenSource" /> o si ambos se construyeron a partir de los constructores CancellationToken públicos y sus valores <see cref="P:System.Threading.CancellationToken.IsCancellationRequested" /> son iguales.</returns>
      <param name="other">El otro objeto <see cref="T:System.Threading.CancellationToken" /> con el que se va a comparar esta instancia.</param>
    </member>
    <member name="M:System.Threading.CancellationToken.GetHashCode">
      <summary>Sirve como función hash de un objeto <see cref="T:System.Threading.CancellationToken" />.</summary>
      <returns>Código hash de la instancia de <see cref="T:System.Threading.CancellationToken" /> actual.</returns>
    </member>
    <member name="P:System.Threading.CancellationToken.IsCancellationRequested">
      <summary>Obtiene si se ha solicitado la cancelación para este token.</summary>
      <returns>Es true si se ha solicitado la cancelación para este token; en caso contrario, es false.</returns>
    </member>
    <member name="P:System.Threading.CancellationToken.None">
      <summary>Devuelve un valor <see cref="T:System.Threading.CancellationToken" /> vacío.</summary>
      <returns>Token de cancelación vacío. </returns>
    </member>
    <member name="M:System.Threading.CancellationToken.op_Equality(System.Threading.CancellationToken,System.Threading.CancellationToken)">
      <summary>Determina si dos instancias de <see cref="T:System.Threading.CancellationToken" /> son iguales.</summary>
      <returns>Es true si las instancias son iguales; en caso contrario, es false.</returns>
      <param name="left">Primera instancia.</param>
      <param name="right">Segunda instancia.</param>
      <exception cref="T:System.ObjectDisposedException">An associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.op_Inequality(System.Threading.CancellationToken,System.Threading.CancellationToken)">
      <summary>Determina si dos instancias de <see cref="T:System.Threading.CancellationToken" /> no son iguales.</summary>
      <returns>Es true, si las instancias no son iguales; en caso contrario, es false.</returns>
      <param name="left">Primera instancia.</param>
      <param name="right">Segunda instancia.</param>
      <exception cref="T:System.ObjectDisposedException">An associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.Register(System.Action)">
      <summary>Registra un delegado que se invocará cuando se cancele este objeto <see cref="T:System.Threading.CancellationToken" />.</summary>
      <returns>Instancia de <see cref="T:System.Threading.CancellationTokenRegistration" /> que se puede usar para eliminar del Registro la devolución de llamada.</returns>
      <param name="callback">Delegado que se va a ejecutar cuando se cancele <see cref="T:System.Threading.CancellationToken" />.</param>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="callback" /> is null.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.Register(System.Action,System.Boolean)">
      <summary>Registra un delegado que se invocará cuando se cancele este objeto <see cref="T:System.Threading.CancellationToken" />.</summary>
      <returns>Instancia de <see cref="T:System.Threading.CancellationTokenRegistration" /> que se puede usar para eliminar del Registro la devolución de llamada.</returns>
      <param name="callback">Delegado que se va a ejecutar cuando se cancele <see cref="T:System.Threading.CancellationToken" />.</param>
      <param name="useSynchronizationContext">Un valor booleano que indica si se va a capturar el <see cref="T:System.Threading.SynchronizationContext" /> actual y si se va a usar al invocar <paramref name="callback" />.</param>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="callback" /> is null.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.Register(System.Action{System.Object},System.Object)">
      <summary>Registra un delegado que se invocará cuando se cancele este objeto <see cref="T:System.Threading.CancellationToken" />.</summary>
      <returns>Instancia de <see cref="T:System.Threading.CancellationTokenRegistration" /> que se puede usar para eliminar del Registro la devolución de llamada.</returns>
      <param name="callback">Delegado que se va a ejecutar cuando se cancele <see cref="T:System.Threading.CancellationToken" />.</param>
      <param name="state">El estado que se va a pasar a <paramref name="callback" /> cuando se invoque al delegado.Puede ser null.</param>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="callback" /> is null.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.Register(System.Action{System.Object},System.Object,System.Boolean)">
      <summary>Registra un delegado que se invocará cuando se cancele este objeto <see cref="T:System.Threading.CancellationToken" />.</summary>
      <returns>Instancia de <see cref="T:System.Threading.CancellationTokenRegistration" /> que se puede usar para eliminar del Registro la devolución de llamada.</returns>
      <param name="callback">Delegado que se va a ejecutar cuando se cancele <see cref="T:System.Threading.CancellationToken" />.</param>
      <param name="state">El estado que se va a pasar a <paramref name="callback" /> cuando se invoque al delegado.Puede ser null.</param>
      <param name="useSynchronizationContext">Un valor booleano que indica si se va a capturar el <see cref="T:System.Threading.SynchronizationContext" /> actual y si se va a usar al invocar <paramref name="callback" />.</param>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="callback" /> is null.</exception>
    </member>
    <member name="M:System.Threading.CancellationToken.ThrowIfCancellationRequested">
      <summary>Produce <see cref="T:System.OperationCanceledException" /> si este token ha tenido una solicitud de cancelación.</summary>
      <exception cref="T:System.OperationCanceledException">The token has had cancellation requested.</exception>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
    </member>
    <member name="P:System.Threading.CancellationToken.WaitHandle">
      <summary>Obtiene un <see cref="T:System.Threading.WaitHandle" /> que se señala cuando el token se cancela.</summary>
      <returns>
        <see cref="T:System.Threading.WaitHandle" /> que se señala cuando el token se cancela.</returns>
      <exception cref="T:System.ObjectDisposedException">The associated <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
    </member>
    <member name="T:System.Threading.CancellationTokenRegistration">
      <summary>Representa un delegado de devolución de llamada que se ha registrado con un token <see cref="T:System.Threading.CancellationToken" />. </summary>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.Dispose">
      <summary>Libera todos los recursos usados por la instancia actual de la clase <see cref="T:System.Threading.CancellationTokenRegistration" />.</summary>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.Equals(System.Object)">
      <summary>Determina si la instancia de <see cref="T:System.Threading.CancellationTokenRegistration" /> actual es igual que la instancia de <see cref="T:System.Threading.CancellationTokenRegistration" /> especificada.</summary>
      <returns>True, si la instancia actual y <paramref name="obj" /> son iguales.De lo contrario, false.Dos instancias de <see cref="T:System.Threading.CancellationTokenRegistration" /> son iguales si ambas hacen referencia al resultado de una sola llamada al mismo método Register de un <see cref="T:System.Threading.CancellationToken" />.</returns>
      <param name="obj">Otro objeto con el que se va a comparar esta instancia.</param>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.Equals(System.Threading.CancellationTokenRegistration)">
      <summary>Determina si la instancia de <see cref="T:System.Threading.CancellationTokenRegistration" /> actual es igual que la instancia de <see cref="T:System.Threading.CancellationTokenRegistration" /> especificada.</summary>
      <returns>True, si la instancia actual y <paramref name="other" /> son iguales.De lo contrario, false. Dos instancias de <see cref="T:System.Threading.CancellationTokenRegistration" /> son iguales si ambas hacen referencia al resultado de una sola llamada al mismo método Register de un <see cref="T:System.Threading.CancellationToken" />.</returns>
      <param name="other">Otra instancia de <see cref="T:System.Threading.CancellationTokenRegistration" /> con la que se va a comparar esta instancia.</param>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.GetHashCode">
      <summary>Actúa como una función hash para <see cref="T:System.Threading.CancellationTokenRegistration" />.</summary>
      <returns>Código hash de la instancia de <see cref="T:System.Threading.CancellationTokenRegistration" /> actual.</returns>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.op_Equality(System.Threading.CancellationTokenRegistration,System.Threading.CancellationTokenRegistration)">
      <summary>Determina si dos instancias de <see cref="T:System.Threading.CancellationTokenRegistration" /> son iguales.</summary>
      <returns>True, si las instancias son iguales; en caso contrario, false.</returns>
      <param name="left">Primera instancia.</param>
      <param name="right">Segunda instancia.</param>
    </member>
    <member name="M:System.Threading.CancellationTokenRegistration.op_Inequality(System.Threading.CancellationTokenRegistration,System.Threading.CancellationTokenRegistration)">
      <summary>Determina si dos instancias de <see cref="T:System.Threading.CancellationTokenRegistration" /> no son iguales.</summary>
      <returns>True, si las instancias no son iguales; de lo contrario, false.</returns>
      <param name="left">Primera instancia.</param>
      <param name="right">Segunda instancia.</param>
    </member>
    <member name="T:System.Threading.CancellationTokenSource">
      <summary>Señala un objeto <see cref="T:System.Threading.CancellationToken" /> que debe cancelarse.</summary>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.CancellationTokenSource" />.</summary>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.#ctor(System.Int32)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.CancellationTokenSource" /> que se cancela después del retraso especificado en milisegundos.</summary>
      <param name="millisecondsDelay">Intervalo de tiempo en milisegundos que se esperará antes de cancelar este <see cref="T:System.Threading.CancellationTokenSource" />. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsDelay" /> is less than -1. </exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.#ctor(System.TimeSpan)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.CancellationTokenSource" /> que se cancela después del intervalo de tiempo especificado.</summary>
      <param name="delay">Intervalo de tiempo que se esperará antes de cancelar este <see cref="T:System.Threading.CancellationTokenSource" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="delay" />.<see cref="P:System.TimeSpan.TotalMilliseconds" /> is less than -1 or greater than <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.Cancel">
      <summary>Comunica una solicitud de cancelación.</summary>
      <exception cref="T:System.ObjectDisposedException">This <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.AggregateException">An aggregate exception containing all the exceptions thrown by the registered callbacks on the associated <see cref="T:System.Threading.CancellationToken" />.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.Cancel(System.Boolean)">
      <summary>Comunica una solicitud de cancelación y especifica si se deben procesar las devoluciones de llamada restantes y las operaciones cancelables.</summary>
      <param name="throwOnFirstException">true si las excepciones deben propagarse de inmediato; en caso contrario, false.</param>
      <exception cref="T:System.ObjectDisposedException">This <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.AggregateException">An aggregate exception containing all the exceptions thrown by the registered callbacks on the associated <see cref="T:System.Threading.CancellationToken" />.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.CancelAfter(System.Int32)">
      <summary>Programa una operación de cancelación en este <see cref="T:System.Threading.CancellationTokenSource" /> después del número especificado de milisegundos.</summary>
      <param name="millisecondsDelay">Intervalo de tiempo que hay que esperar antes de cancelar este <see cref="T:System.Threading.CancellationTokenSource" />.</param>
      <exception cref="T:System.ObjectDisposedException">The exception thrown when this <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The exception thrown when <paramref name="millisecondsDelay" /> is less than -1.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.CancelAfter(System.TimeSpan)">
      <summary>Programa una operación de cancelación en este <see cref="T:System.Threading.CancellationTokenSource" /> después del intervalo de tiempo especificado.</summary>
      <param name="delay">Intervalo de tiempo que hay que esperar antes de cancelar este <see cref="T:System.Threading.CancellationTokenSource" />.</param>
      <exception cref="T:System.ObjectDisposedException">The exception thrown when this <see cref="T:System.Threading.CancellationTokenSource" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The exception that is thrown when <paramref name="delay" /> is less than -1 or greater than Int32.MaxValue.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.CreateLinkedTokenSource(System.Threading.CancellationToken,System.Threading.CancellationToken)">
      <summary>Crea un <see cref="T:System.Threading.CancellationTokenSource" /> que tendrá el estado cancelado cuando alguno de los tokens de origen tenga el estado cancelado.</summary>
      <returns>
        <see cref="T:System.Threading.CancellationTokenSource" /> que está vinculado a los tokens de origen.</returns>
      <param name="token1">Primer token de cancelación que se va a observar.</param>
      <param name="token2">Segundo token de cancelación que se va a observar.</param>
      <exception cref="T:System.ObjectDisposedException">A <see cref="T:System.Threading.CancellationTokenSource" /> associated with one of the source tokens has been disposed.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.CreateLinkedTokenSource(System.Threading.CancellationToken[])">
      <summary>Crea un <see cref="T:System.Threading.CancellationTokenSource" /> que tendrá el estado cancelado cuando alguno de los tokens de origen del la matriz especificada tenga el estado cancelado.</summary>
      <returns>
        <see cref="T:System.Threading.CancellationTokenSource" /> que está vinculado a los tokens de origen.</returns>
      <param name="tokens">Matriz que contiene instancias de tokens de cancelación que se van a observar.</param>
      <exception cref="T:System.ObjectDisposedException">A <see cref="T:System.Threading.CancellationTokenSource" /> associated with one of the source tokens has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tokens" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="tokens" /> is empty.</exception>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.Dispose">
      <summary>Libera todos los recursos usados por la instancia actual de la clase <see cref="T:System.Threading.CancellationTokenSource" />.</summary>
    </member>
    <member name="M:System.Threading.CancellationTokenSource.Dispose(System.Boolean)">
      <summary>Libera los recursos no administrados usados que usa la clase <see cref="T:System.Threading.CancellationTokenSource" /> y, de forma opcional, libera los recursos administrados.</summary>
      <param name="disposing">Es true para liberar tanto recursos administrados como no administrados; es false para liberar únicamente recursos no administrados.</param>
    </member>
    <member name="P:System.Threading.CancellationTokenSource.IsCancellationRequested">
      <summary>Obtiene si se solicitó la cancelación de este <see cref="T:System.Threading.CancellationTokenSource" />.</summary>
      <returns>false si se solicitó la cancelación de este true; en caso contrario, <see cref="T:System.Threading.CancellationTokenSource" />.</returns>
    </member>
    <member name="P:System.Threading.CancellationTokenSource.Token">
      <summary>Obtiene el objeto <see cref="T:System.Threading.CancellationToken" /> asociado a <see cref="T:System.Threading.CancellationTokenSource" />.</summary>
      <returns>
        <see cref="T:System.Threading.CancellationToken" /> asociada a esta <see cref="T:System.Threading.CancellationTokenSource" />.</returns>
      <exception cref="T:System.ObjectDisposedException">The token source has been disposed.</exception>
    </member>
    <member name="T:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair">
      <summary>Proporciona programadores de tareas que se coordinan para ejecutar las tareas a la vez que garantizan que las tareas simultáneas puedan ejecutarse en paralelo y las tareas excluyentes nunca lo hagan así.</summary>
    </member>
    <member name="M:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair" />.</summary>
    </member>
    <member name="M:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.#ctor(System.Threading.Tasks.TaskScheduler)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair" /> que se destina al programador especificado.</summary>
      <param name="taskScheduler">El programador de destino en el que se debe ejecutar este par.</param>
    </member>
    <member name="M:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.#ctor(System.Threading.Tasks.TaskScheduler,System.Int32)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair" /> destinada al programador especificado con un nivel de simultaneidad máximo.</summary>
      <param name="taskScheduler">El programador de destino en el que se debe ejecutar este par.</param>
      <param name="maxConcurrencyLevel">Número máximo de tareas que se van a ejecutar simultáneamente.</param>
    </member>
    <member name="M:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.#ctor(System.Threading.Tasks.TaskScheduler,System.Int32,System.Int32)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair" /> destinada al programador especificado con un nivel de simultaneidad máximo y un número de tareas programadas máximo que se pueden procesar como una unidad.</summary>
      <param name="taskScheduler">El programador de destino en el que se debe ejecutar este par.</param>
      <param name="maxConcurrencyLevel">Número máximo de tareas que se van a ejecutar simultáneamente.</param>
      <param name="maxItemsPerTask">Número máximo de tareas para procesar para cada tarea programada subyacente usada por el par.</param>
    </member>
    <member name="M:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.Complete">
      <summary>Informa al par de programadores que no debe aceptar más tareas.</summary>
    </member>
    <member name="P:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.Completion">
      <summary>Obtiene un objeto <see cref="T:System.Threading.Tasks.Task" /> que se completará cuando el programador haya completado el procesamiento.</summary>
      <returns>La operación asincrónica que se completará cuando el programador finalice el procesamiento.</returns>
    </member>
    <member name="P:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.ConcurrentScheduler">
      <summary>Obtiene un objeto <see cref="T:System.Threading.Tasks.TaskScheduler" /> que se puede utilizar para programar tareas en este par que puede ejecutarse simultáneamente con otras tareas de este par.</summary>
      <returns>Un objeto que se puede utilizar para programar tareas simultáneamente.</returns>
    </member>
    <member name="P:System.Threading.Tasks.ConcurrentExclusiveSchedulerPair.ExclusiveScheduler">
      <summary>Obtiene un objeto <see cref="T:System.Threading.Tasks.TaskScheduler" /> que se puede utilizarse para programar tareas en este par que deben ejecutarse exclusivamente con respecto a otras tareas de este par.</summary>
      <returns>Un objeto que se puede utilizar para programar las tareas que no se ejecutan simultáneamente con otras tareas.</returns>
    </member>
    <member name="T:System.Threading.Tasks.Task">
      <summary>Representa una operación asincrónica.Para examinar el código fuente de .NET Framework de este tipo, consulte el origen de referencia.</summary>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action)">
      <summary>Inicializa una nueva instancia de <see cref="T:System.Threading.Tasks.Task" /> con la acción especificada.</summary>
      <param name="action">Delegado que representa el código que se va a ejecutar en la tarea.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action,System.Threading.CancellationToken)">
      <summary>Inicializa una nueva instancia de <see cref="T:System.Threading.Tasks.Task" /> con la acción especificada y <see cref="T:System.Threading.CancellationToken" />.</summary>
      <param name="action">Delegado que representa el código que se va a ejecutar en la tarea.</param>
      <param name="cancellationToken">El <see cref="T:System.Threading.CancellationToken" /> que observará la nueva tarea.</param>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Inicializa un nuevo objeto <see cref="T:System.Threading.Tasks.Task" /> con la acción y las opciones de creación especificadas.</summary>
      <param name="action">Delegado que representa el código que se va a ejecutar en la tarea.</param>
      <param name="cancellationToken">El <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> que observará la nueva tarea.</param>
      <param name="creationOptions">El objeto <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> que se usa para personalizar el comportamiento de la tarea.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Inicializa un nuevo objeto <see cref="T:System.Threading.Tasks.Task" /> con la acción y las opciones de creación especificadas.</summary>
      <param name="action">Delegado que representa el código que se va a ejecutar en la tarea.</param>
      <param name="creationOptions">El objeto <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> que se usa para personalizar el comportamiento de la tarea. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action{System.Object},System.Object)">
      <summary>Inicializa una nueva instancia de <see cref="T:System.Threading.Tasks.Task" /> con la acción y el estado especificados.</summary>
      <param name="action">Delegado que representa el código que se va a ejecutar en la tarea.</param>
      <param name="state">Objeto que representa los datos que la acción va a usar.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action{System.Object},System.Object,System.Threading.CancellationToken)">
      <summary>Inicializa un nuevo objeto <see cref="T:System.Threading.Tasks.Task" /> con la acción, el estado y las opciones especificados.</summary>
      <param name="action">Delegado que representa el código que se va a ejecutar en la tarea.</param>
      <param name="state">Objeto que representa los datos que la acción va a usar.</param>
      <param name="cancellationToken">El <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> que observará la nueva tarea.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action{System.Object},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Inicializa un nuevo objeto <see cref="T:System.Threading.Tasks.Task" /> con la acción, el estado y las opciones especificados.</summary>
      <param name="action">Delegado que representa el código que se va a ejecutar en la tarea.</param>
      <param name="state">Objeto que representa los datos que la acción va a usar.</param>
      <param name="cancellationToken">El <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> que observará la nueva tarea.</param>
      <param name="creationOptions">El objeto <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> que se usa para personalizar el comportamiento de la tarea.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.#ctor(System.Action{System.Object},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Inicializa un nuevo objeto <see cref="T:System.Threading.Tasks.Task" /> con la acción, el estado y las opciones especificados.</summary>
      <param name="action">Delegado que representa el código que se va a ejecutar en la tarea.</param>
      <param name="state">Objeto que representa los datos que la acción va a usar.</param>
      <param name="creationOptions">El objeto <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> que se usa para personalizar el comportamiento de la tarea.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
    </member>
    <member name="P:System.Threading.Tasks.Task.AsyncState">
      <summary>Obtiene el objeto de estado que se proporcionó al crearse el objeto <see cref="T:System.Threading.Tasks.Task" />, o null si no se proporcionó ningún objeto de estado.</summary>
      <returns>Un <see cref="T:System.Object" /> que representa los datos de estado que se pasaron a la tarea cuando se creó.</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.CompletedTask">
      <summary>Obtiene una tarea que ya ha finalizado correctamente. </summary>
      <returns>La tarea que finalizó correctamente. </returns>
    </member>
    <member name="M:System.Threading.Tasks.Task.ConfigureAwait(System.Boolean)">
      <summary>Configura un awaiter utilizado para esperar a este objeto <see cref="T:System.Threading.Tasks.Task" />.</summary>
      <returns>Objeto utilizado para esperar a esta tarea.</returns>
      <param name="continueOnCapturedContext">true para intentar calcular las referencias de la continuación de nuevo al contexto original capturado; en caso contrario, false.</param>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task})">
      <summary>Crea una continuación que se ejecuta de manera asincrónica cuando se completa el objeto <see cref="T:System.Threading.Tasks.Task" /> de destino.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task" /> de continuación.</returns>
      <param name="continuationAction">Acción que se va a ejecutar cuando se complete el objeto <see cref="T:System.Threading.Tasks.Task" />.Cuando se ejecute, al delegado se le pasará la tarea completada como un argumento.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task},System.Threading.CancellationToken)">
      <summary>Crea una continuación que recibe un token de cancelación y se ejecuta de forma asincrónica cuando el elemento <see cref="T:System.Threading.Tasks.Task" /> de destino se completa.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task" /> de continuación.</returns>
      <param name="continuationAction">Acción que se va a ejecutar cuando se complete el objeto <see cref="T:System.Threading.Tasks.Task" />.Cuando se ejecute, al delegado se le pasará la tarea completada como un argumento.</param>
      <param name="cancellationToken">El <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> que se asignará a la nueva tarea de continuación.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created the token has already been disposed. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea una continuación que se ejecuta cuando se completa la tarea de destino según el elemento <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> especificado.La continuación recibe un token de cancelación y usa un programador especificado.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task" /> de continuación.</returns>
      <param name="continuationAction">Una acción para ejecutarse según el <paramref name="continuationOptions" /> especificado.Cuando se ejecute, al delegado se le pasará la tarea completada como un argumento.</param>
      <param name="cancellationToken">El <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> que se asignará a la nueva tarea de continuación.</param>
      <param name="continuationOptions">Opciones para la programación y el comportamiento de la continuación.Incluye criterios, como <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, y opciones de ejecución, como <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <param name="scheduler">Objeto <see cref="T:System.Threading.Tasks.TaskScheduler" /> que se va a asociar a la tarea de continuación y se va a usar para su ejecución.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created the token has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crea una continuación que se ejecuta cuando se completa la tarea de destino según el elemento <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> especificado.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task" /> de continuación.</returns>
      <param name="continuationAction">Una acción para ejecutarse según el <paramref name="continuationOptions" /> especificado.Cuando se ejecute, al delegado se le pasará la tarea completada como un argumento.</param>
      <param name="continuationOptions">Opciones para la programación y el comportamiento de la continuación.Incluye criterios, como <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, y opciones de ejecución, como <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task},System.Threading.Tasks.TaskScheduler)">
      <summary>Crea una continuación que se ejecuta de manera asincrónica cuando se completa el objeto <see cref="T:System.Threading.Tasks.Task" /> de destino.La continuación usa un programador especificado.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task" /> de continuación.</returns>
      <param name="continuationAction">Acción que se va a ejecutar cuando se complete el objeto <see cref="T:System.Threading.Tasks.Task" />.Cuando se ejecute, al delegado se le pasará la tarea completada como un argumento.</param>
      <param name="scheduler">Objeto <see cref="T:System.Threading.Tasks.TaskScheduler" /> que se va a asociar a la tarea de continuación y se va a usar para su ejecución.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null. -or-The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task,System.Object},System.Object)">
      <summary>Crea una continuación que recibe información de estado proporcionada por el autor de la llamada y se ejecuta cuando el <see cref="T:System.Threading.Tasks.Task" /> de destino se completa. </summary>
      <returns>Una tarea de continuación nueva. </returns>
      <param name="continuationAction">Acción que se ejecutará cuando se complete la tarea.Cuando se ejecuta, se pasa al delegado la tarea completada y el objeto de estado proporcionado por el autor de la llamada como argumentos.</param>
      <param name="state">Objeto que representa los datos que la acción de continuación va a usar. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task,System.Object},System.Object,System.Threading.CancellationToken)">
      <summary>Crea una continuación que recibe información de estado proporcionada por el autor de la llamada y un token de cancelación y que se ejecuta de forma asincrónica cuando el elemento <see cref="T:System.Threading.Tasks.Task" /> de destino se completa.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task" /> de continuación.</returns>
      <param name="continuationAction">Acción que se va a ejecutar cuando se complete el objeto <see cref="T:System.Threading.Tasks.Task" />.Cuando se ejecute, se pasarán al delegado la tarea completada y el objeto de estado proporcionado por el llamador como argumentos.</param>
      <param name="state">Objeto que representa los datos que la acción de continuación va a usar.</param>
      <param name="cancellationToken">El <see cref="T:System.Threading.CancellationToken" /> que se asignará a la nueva tarea de continuación.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task,System.Object},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea una continuación que recibe información de estado proporcionada por el autor de la llamada y un token de cancelación y que se ejecuta cuando el elemento <see cref="T:System.Threading.Tasks.Task" /> de destino se completa.La continuación se ejecuta según un conjunto de condiciones especificadas y usa un programador especificado.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task" /> de continuación.</returns>
      <param name="continuationAction">Acción que se va a ejecutar cuando se complete el objeto <see cref="T:System.Threading.Tasks.Task" />.Cuando se ejecute, se pasarán al delegado la tarea completada y el objeto de estado proporcionado por el llamador como argumentos.</param>
      <param name="state">Objeto que representa los datos que la acción de continuación va a usar.</param>
      <param name="cancellationToken">El <see cref="T:System.Threading.CancellationToken" /> que se asignará a la nueva tarea de continuación.</param>
      <param name="continuationOptions">Opciones para la programación y el comportamiento de la continuación.Incluye criterios, como <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, y opciones de ejecución, como <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <param name="scheduler">Objeto <see cref="T:System.Threading.Tasks.TaskScheduler" /> que se va a asociar a la tarea de continuación y se va a usar para su ejecución.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task,System.Object},System.Object,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crea una continuación que recibe información de estado proporcionada por el autor de la llamada y se ejecuta cuando el elemento <see cref="T:System.Threading.Tasks.Task" /> de destino se completa.La continuación se ejecuta según un conjunto de condiciones especificadas.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task" /> de continuación.</returns>
      <param name="continuationAction">Acción que se va a ejecutar cuando se complete el objeto <see cref="T:System.Threading.Tasks.Task" />.Cuando se ejecute, se pasarán al delegado la tarea completada y el objeto de estado proporcionado por el llamador como argumentos.</param>
      <param name="state">Objeto que representa los datos que la acción de continuación va a usar.</param>
      <param name="continuationOptions">Opciones para la programación y el comportamiento de la continuación.Incluye criterios, como <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, y opciones de ejecución, como <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task,System.Object},System.Object,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea una continuación que recibe información de estado proporcionada por el autor de la llamada y se ejecuta de forma asincrónica cuando el elemento <see cref="T:System.Threading.Tasks.Task" /> de destino se completa.La continuación usa un programador especificado.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task" /> de continuación.</returns>
      <param name="continuationAction">Acción que se va a ejecutar cuando se complete el objeto <see cref="T:System.Threading.Tasks.Task" />.Cuando se ejecute, se pasarán al delegado la tarea completada y el objeto de estado proporcionado por el llamador como argumentos.</param>
      <param name="state">Objeto que representa los datos que la acción de continuación va a usar.</param>
      <param name="scheduler">Objeto <see cref="T:System.Threading.Tasks.TaskScheduler" /> que se va a asociar a la tarea de continuación y se va a usar para su ejecución.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,``0})">
      <summary>Crea una continuación que se ejecuta de forma asincrónica cuando el elemento <see cref="T:System.Threading.Tasks.Task`1" /> de destino se completa y devuelve un valor. </summary>
      <returns>Una tarea de continuación nueva. </returns>
      <param name="continuationFunction">Función que se va a ejecutar cuando se complete el objeto <see cref="T:System.Threading.Tasks.Task`1" />.Cuando se ejecute, al delegado se le pasará la tarea completada como un argumento.</param>
      <typeparam name="TResult"> Tipo de resultado generado por la continuación.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,``0},System.Threading.CancellationToken)">
      <summary>Crea una continuación que se ejecuta de forma asincrónica cuando el elemento <see cref="T:System.Threading.Tasks.Task" /> de destino se completa y devuelve un valor.La continuación recibe un token de cancelación.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task`1" /> de continuación.</returns>
      <param name="continuationFunction">Función que se va a ejecutar cuando se complete el objeto <see cref="T:System.Threading.Tasks.Task" />.Cuando se ejecute, al delegado se le pasará la tarea completada como un argumento.</param>
      <param name="cancellationToken">El <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> que se asignará a la nueva tarea de continuación.</param>
      <typeparam name="TResult"> Tipo de resultado generado por la continuación.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created the token has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,``0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea una continuación que se ejecuta según las opciones de continuación especificadas y devuelve un valor.Se pasa un token de cancelación a la continuación y usa un programador especificado.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task`1" /> de continuación.</returns>
      <param name="continuationFunction">Función que se ejecuta según la condición especificada en <paramref name="continuationOptions." />. Cuando se ejecute, al delegado se le pasará la tarea completada como un argumento.</param>
      <param name="cancellationToken">El <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> que se asignará a la nueva tarea de continuación.</param>
      <param name="continuationOptions">Opciones para la programación y el comportamiento de la continuación.Incluye criterios, como <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, y opciones de ejecución, como <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <param name="scheduler">Objeto <see cref="T:System.Threading.Tasks.TaskScheduler" /> que se va a asociar a la tarea de continuación y se va a usar para su ejecución.</param>
      <typeparam name="TResult"> Tipo de resultado generado por la continuación.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created the token has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,``0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crea una continuación que se ejecuta según las opciones de continuación especificadas y devuelve un valor. </summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task`1" /> de continuación.</returns>
      <param name="continuationFunction">Función que se ejecuta según la condición especificada en <paramref name="continuationOptions" />.Cuando se ejecute, al delegado se le pasará la tarea completada como un argumento.</param>
      <param name="continuationOptions">Opciones para la programación y el comportamiento de la continuación.Incluye criterios, como <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, y opciones de ejecución, como <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <typeparam name="TResult"> Tipo de resultado generado por la continuación.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,``0},System.Threading.Tasks.TaskScheduler)">
      <summary>Crea una continuación que se ejecuta de forma asincrónica cuando el elemento <see cref="T:System.Threading.Tasks.Task" /> de destino se completa y devuelve un valor.La continuación usa un programador especificado.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task`1" /> de continuación.</returns>
      <param name="continuationFunction">Función que se va a ejecutar cuando se complete el objeto <see cref="T:System.Threading.Tasks.Task" />.Cuando se ejecute, al delegado se le pasará la tarea completada como un argumento.</param>
      <param name="scheduler">Objeto <see cref="T:System.Threading.Tasks.TaskScheduler" /> que se va a asociar a la tarea de continuación y se va a usar para su ejecución.</param>
      <typeparam name="TResult"> Tipo de resultado generado por la continuación.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,System.Object,``0},System.Object)">
      <summary>Crea una continuación que recibe información de estado proporcionada por el autor de la llamada y se ejecuta de forma asincrónica cuando el elemento <see cref="T:System.Threading.Tasks.Task" /> de destino se completa y devuelve un valor. </summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task`1" /> de continuación.</returns>
      <param name="continuationFunction">Función que se va a ejecutar cuando se complete el objeto <see cref="T:System.Threading.Tasks.Task" />.Cuando se ejecute, se pasarán al delegado la tarea completada y el objeto de estado proporcionado por el llamador como argumentos.</param>
      <param name="state">Objeto que representa los datos que la función de continuación va a usar.</param>
      <typeparam name="TResult">Tipo de resultado generado por la continuación.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,System.Object,``0},System.Object,System.Threading.CancellationToken)">
      <summary>Crea una continuación que se ejecuta de forma asincrónica cuando el elemento <see cref="T:System.Threading.Tasks.Task" /> de destino se completa y devuelve un valor.La continuación recibe información de estado proporcionada por el autor de la llamada y un token de cancelación.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task`1" /> de continuación.</returns>
      <param name="continuationFunction">Función que se va a ejecutar cuando se complete el objeto <see cref="T:System.Threading.Tasks.Task" />.Cuando se ejecute, se pasarán al delegado la tarea completada y el objeto de estado proporcionado por el llamador como argumentos.</param>
      <param name="state">Objeto que representa los datos que la función de continuación va a usar.</param>
      <param name="cancellationToken">El <see cref="T:System.Threading.CancellationToken" /> que se asignará a la nueva tarea de continuación.</param>
      <typeparam name="TResult">Tipo de resultado generado por la continuación.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,System.Object,``0},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea una continuación que se ejecuta según las opciones de continuación de la tarea especificadas cuando el elemento <see cref="T:System.Threading.Tasks.Task" /> de destino se completa y devuelve un valor.La continuación recibe información de estado proporcionada por el autor de la llamada y un token de cancelación y usa el programador especificado.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task`1" /> de continuación.</returns>
      <param name="continuationFunction">Función que se va a ejecutar cuando se complete el objeto <see cref="T:System.Threading.Tasks.Task" />.Cuando se ejecute, se pasarán al delegado la tarea completada y el objeto de estado proporcionado por el llamador como argumentos.</param>
      <param name="state">Objeto que representa los datos que la función de continuación va a usar.</param>
      <param name="cancellationToken">El <see cref="T:System.Threading.CancellationToken" /> que se asignará a la nueva tarea de continuación.</param>
      <param name="continuationOptions">Opciones para la programación y el comportamiento de la continuación.Incluye criterios, como <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, y opciones de ejecución, como <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <param name="scheduler">Objeto <see cref="T:System.Threading.Tasks.TaskScheduler" /> que se va a asociar a la tarea de continuación y se va a usar para su ejecución.</param>
      <typeparam name="TResult">Tipo de resultado generado por la continuación.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,System.Object,``0},System.Object,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crea una continuación que se ejecuta según las opciones de continuación de la tarea especificadas cuando el elemento <see cref="T:System.Threading.Tasks.Task" /> de destino se completa.La continuación recibe información de estado proporcionada por el autor de la llamada.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task`1" /> de continuación.</returns>
      <param name="continuationFunction">Función que se va a ejecutar cuando se complete el objeto <see cref="T:System.Threading.Tasks.Task" />.Cuando se ejecute, se pasarán al delegado la tarea completada y el objeto de estado proporcionado por el llamador como argumentos.</param>
      <param name="state">Objeto que representa los datos que la función de continuación va a usar.</param>
      <param name="continuationOptions">Opciones para la programación y el comportamiento de la continuación.Incluye criterios, como <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, y opciones de ejecución, como <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <typeparam name="TResult">Tipo de resultado generado por la continuación.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.ContinueWith``1(System.Func{System.Threading.Tasks.Task,System.Object,``0},System.Object,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea una continuación que se ejecuta de manera asincrónica cuando se completa el objeto <see cref="T:System.Threading.Tasks.Task" /> de destino.La continuación recibe información de estado proporcionada por el autor de la llamada y usa a un programador especificado.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task`1" /> de continuación.</returns>
      <param name="continuationFunction">Función que se va a ejecutar cuando se complete el objeto <see cref="T:System.Threading.Tasks.Task" />.Cuando se ejecute, se pasarán al delegado la tarea completada y el objeto de estado proporcionado por el llamador como argumentos.</param>
      <param name="state">Objeto que representa los datos que la función de continuación va a usar.</param>
      <param name="scheduler">Objeto <see cref="T:System.Threading.Tasks.TaskScheduler" /> que se va a asociar a la tarea de continuación y se va a usar para su ejecución.</param>
      <typeparam name="TResult">Tipo de resultado generado por la continuación.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="P:System.Threading.Tasks.Task.CreationOptions">
      <summary>Obtiene el objeto <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> usado para crear esta tarea.</summary>
      <returns>Objeto <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> usado para crear esta tarea.</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.CurrentId">
      <summary>Devuelve el identificador único del objeto <see cref="T:System.Threading.Tasks.Task" /> que se está ejecutando actualmente.</summary>
      <returns>Entero que el sistema asignó a la tarea en ejecución.</returns>
    </member>
    <member name="M:System.Threading.Tasks.Task.Delay(System.Int32)">
      <summary>Crea una tarea que se completa después de un retraso. </summary>
      <returns>Una tarea que representa el retraso. </returns>
      <param name="millisecondsDelay">El número de milisegundos que se esperará antes de completar la tarea devuelta o -1 para esperar indefinidamente. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="millisecondsDelay" /> argument is less than -1.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Delay(System.Int32,System.Threading.CancellationToken)">
      <summary>Crea una tarea cancelable que se completa después de un retraso. </summary>
      <returns>Una tarea que representa el retraso. </returns>
      <param name="millisecondsDelay">El número de milisegundos que se esperará antes de completar la tarea devuelta o -1 para esperar indefinidamente. </param>
      <param name="cancellationToken">El token de cancelación que se comprobará antes de completar la tarea devuelta. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="millisecondsDelay" /> argument is less than -1. </exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled. </exception>
      <exception cref="T:System.ObjectDisposedException">The provided <paramref name="cancellationToken" /> has already been disposed. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Delay(System.TimeSpan)">
      <summary>Crea una tarea que se completa después de un intervalo de tiempo especificado. </summary>
      <returns>Una tarea que representa el retraso. </returns>
      <param name="delay">El intervalo de tiempo que espera antes de completar la tarea devuelta o TimeSpan.FromMilliseconds(-1) para esperar indefinidamente. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="delay" /> represents a negative time interval other than TimeSpan.FromMillseconds(-1). -or-The <paramref name="delay" /> argument's <see cref="P:System.TimeSpan.TotalMilliseconds" /> property is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Delay(System.TimeSpan,System.Threading.CancellationToken)">
      <summary>Crea una tarea cancelable que se completa después de un intervalo de tiempo específico. </summary>
      <returns>Una tarea que representa el retraso. </returns>
      <param name="delay">El intervalo de tiempo que espera antes de completar la tarea devuelta o TimeSpan.FromMilliseconds(-1) para esperar indefinidamente. </param>
      <param name="cancellationToken">El token de cancelación que se comprobará antes de completar la tarea devuelta. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="delay" /> represents a negative time interval other than TimeSpan.FromMillseconds(-1). -or-The <paramref name="delay" /> argument's <see cref="P:System.TimeSpan.TotalMilliseconds" /> property is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <paramref name="cancellationToken" /> has already been disposed. </exception>
    </member>
    <member name="P:System.Threading.Tasks.Task.Exception">
      <summary>Obtiene la excepción <see cref="T:System.AggregateException" /> que causó la finalización prematura del objeto <see cref="T:System.Threading.Tasks.Task" />.Si <see cref="T:System.Threading.Tasks.Task" /> se completó correctamente o no ha iniciado ninguna excepción, el valor devuelto será null.</summary>
      <returns>Excepción <see cref="T:System.AggregateException" /> que causó la finalización prematura del objeto <see cref="T:System.Threading.Tasks.Task" />.</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.Factory">
      <summary>Proporciona acceso a patrones de diseño para crear y configurar instancias de <see cref="T:System.Threading.Tasks.Task" /> y <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>Un objeto de fábrica que puede crear una variedad de objetos <see cref="T:System.Threading.Tasks.Task" /> y <see cref="T:System.Threading.Tasks.Task`1" />. </returns>
    </member>
    <member name="M:System.Threading.Tasks.Task.FromCanceled(System.Threading.CancellationToken)">
      <summary>Crea una <see cref="T:System.Threading.Tasks.Task" /> que se finaliza debido a la cancelación con un token de cancelación especificado.</summary>
      <returns>Tarea cancelada. </returns>
      <param name="cancellationToken">Token de cancelación con el que se finaliza la tarea. </param>
    </member>
    <member name="M:System.Threading.Tasks.Task.FromCanceled``1(System.Threading.CancellationToken)">
      <summary>Crea una <see cref="T:System.Threading.Tasks.Task`1" /> que se finaliza debido a la cancelación con un token de cancelación especificado.</summary>
      <returns>Tarea cancelada. </returns>
      <param name="cancellationToken">Token de cancelación con el que se finaliza la tarea. </param>
      <typeparam name="TResult">Tipo de resultado devuelto por la tarea. </typeparam>
    </member>
    <member name="M:System.Threading.Tasks.Task.FromException``1(System.Exception)">
      <summary>Crea una <see cref="T:System.Threading.Tasks.Task`1" /> que finalizó con una excepción especificada. </summary>
      <returns>Tarea con error. </returns>
      <param name="exception">Excepción con la que se finaliza la tarea. </param>
      <typeparam name="TResult">Tipo de resultado devuelto por la tarea. </typeparam>
    </member>
    <member name="M:System.Threading.Tasks.Task.FromException(System.Exception)">
      <summary>Crea una <see cref="T:System.Threading.Tasks.Task" /> que finalizó con una excepción especificada. </summary>
      <returns>Tarea con error. </returns>
      <param name="exception">Excepción con la que se finaliza la tarea. </param>
    </member>
    <member name="M:System.Threading.Tasks.Task.FromResult``1(``0)">
      <summary>Crea un objeto <see cref="T:System.Threading.Tasks.Task`1" /> que se ha completado correctamente con el resultado especificado.</summary>
      <returns>Tarea completada correctamente.</returns>
      <param name="result">Resultado que se va a almacenar en la tarea completada. </param>
      <typeparam name="TResult">Tipo de resultado devuelto por la tarea. </typeparam>
    </member>
    <member name="M:System.Threading.Tasks.Task.GetAwaiter">
      <summary>Obtiene un awaiter utilizado para esperar a este objeto <see cref="T:System.Threading.Tasks.Task" />.</summary>
      <returns>Una instancia de awaiter.</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.Id">
      <summary>Obtiene un identificador único para esta instancia de <see cref="T:System.Threading.Tasks.Task" />.</summary>
      <returns>Entero que el sistema asignó a esta instancia de la tarea. </returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.IsCanceled">
      <summary>Obtiene un valor que indica si esta instancia de <see cref="T:System.Threading.Tasks.Task" /> ha completado su ejecución debido a una cancelación.</summary>
      <returns>true si la tarea se ha completado debido a su cancelación; en caso contrario, false.</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.IsCompleted">
      <summary>Obtiene un valor que indica si se ha completado esta instancia de <see cref="T:System.Threading.Tasks.Task" />.</summary>
      <returns>true si la tarea se ha completado; en caso contrario, false.</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.IsFaulted">
      <summary>Obtiene un valor que indica si el objeto <see cref="T:System.Threading.Tasks.Task" /> se ha completado debido a una excepción no controlada.</summary>
      <returns>true si la tarea inició una excepción no controlada; en caso contrario, false.</returns>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run(System.Action)">
      <summary>Pone en cola el trabajo especificado para ejecutarlo en ThreadPool y devuelve un identificador de tarea para dicho trabajo.</summary>
      <returns>Tarea que representa el trabajo en cola para ejecutarse en ThreadPool.</returns>
      <param name="action">Trabajo que se va a ejecutar de forma asincrónica.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> parameter was null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run(System.Action,System.Threading.CancellationToken)">
      <summary>Pone en cola el trabajo especificado para ejecutarlo en ThreadPool y devuelve un identificador de tarea para dicho trabajo.</summary>
      <returns>Tarea que representa el trabajo en cola para ejecutarse en ThreadPool.</returns>
      <param name="action">Trabajo que se va a ejecutar de forma asincrónica.</param>
      <param name="cancellationToken">Token de cancelación que se debe usar para cancelar el trabajo.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="action" /> parameter was null.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with <paramref name="cancellationToken" /> was disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run``1(System.Func{System.Threading.Tasks.Task{``0}})">
      <summary>Pone en cola el trabajo especificado para ejecutarlo en ThreadPool y devuelve un proxy para Task(TResult) que devuelve <paramref name="function" />.</summary>
      <returns>Una Task(TResult) que representa un proxy para Task(TResult) devuelta por <paramref name="function" />.</returns>
      <param name="function">Trabajo que se va a ejecutar de forma asincrónica.</param>
      <typeparam name="TResult">Tipo de resultado devuelto por la tarea del proxy.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter was null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run``1(System.Func{System.Threading.Tasks.Task{``0}},System.Threading.CancellationToken)">
      <summary>Pone en cola el trabajo especificado para ejecutarlo en ThreadPool y devuelve un proxy para Task(TResult) que devuelve <paramref name="function" />.</summary>
      <returns>Una Task(TResult) que representa un proxy para Task(TResult) devuelta por <paramref name="function" />.</returns>
      <param name="function">Trabajo que se va a ejecutar de forma asincrónica.</param>
      <param name="cancellationToken">Token de cancelación que se debe usar para cancelar el trabajo.</param>
      <typeparam name="TResult">Tipo de resultado devuelto por la tarea del proxy.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter was null.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with <paramref name="cancellationToken" /> was disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run(System.Func{System.Threading.Tasks.Task})">
      <summary>Pone en cola el trabajo especificado para ejecutarlo en ThreadPool y devuelve un proxy para la tarea devuelta por <paramref name="function" />.</summary>
      <returns>Una tarea que representa un proxy para la tarea devuelta por <paramref name="function" />.</returns>
      <param name="function">Trabajo que se va a ejecutar de forma asincrónica.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter was null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run(System.Func{System.Threading.Tasks.Task},System.Threading.CancellationToken)">
      <summary>Pone en cola el trabajo especificado para ejecutarlo en ThreadPool y devuelve un proxy para la tarea devuelta por <paramref name="function" />.</summary>
      <returns>Una tarea que representa un proxy para la tarea devuelta por <paramref name="function" />.</returns>
      <param name="function">El trabajo que se ejecutará de forma asincrónica. </param>
      <param name="cancellationToken">Un token de cancelación que debería usarse para cancelar el trabajo. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter was null.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with <paramref name="cancellationToken" /> was disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run``1(System.Func{``0})">
      <summary>Pone en cola el trabajo especificado para ejecutarlo en el grupo de subprocesos y devuelve un objeto <see cref="T:System.Threading.Tasks.Task`1" /> que representa ese trabajo. </summary>
      <returns>Un objeto de tarea que representa el trabajo en cola para ejecutarse en el grupo de subprocesos. </returns>
      <param name="function">El trabajo que se ejecutará de forma asincrónica. </param>
      <typeparam name="TResult">El tipo devuelto de la tarea. </typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter is null. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Run``1(System.Func{``0},System.Threading.CancellationToken)">
      <summary>Pone en cola el trabajo especificado para ejecutarlo en el grupo de subprocesos y devuelve un identificador Task(TResult) para dicho trabajo.</summary>
      <returns>Una Task(TResult) que representa el trabajo en cola para ejecutarse en ThreadPool.</returns>
      <param name="function">Trabajo que se va a ejecutar de forma asincrónica.</param>
      <param name="cancellationToken">Token de cancelación que se debe usar para cancelar el trabajo.</param>
      <typeparam name="TResult">Tipo de resultado de la tarea.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> parameter was null.</exception>
      <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task has been canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with <paramref name="cancellationToken" /> was disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.RunSynchronously">
      <summary>Ejecuta sincrónicamente el objeto <see cref="T:System.Threading.Tasks.Task" /> en el objeto <see cref="T:System.Threading.Tasks.TaskScheduler" /> actual.</summary>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> instance has been disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Threading.Tasks.Task" /> is not in a valid state to be started.It may have already been started, executed, or canceled, or it may have been created in a manner that doesn't support direct scheduling.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.RunSynchronously(System.Threading.Tasks.TaskScheduler)">
      <summary>Ejecuta sincrónicamente el objeto <see cref="T:System.Threading.Tasks.Task" /> en el objeto <see cref="T:System.Threading.Tasks.TaskScheduler" /> proporcionado.</summary>
      <param name="scheduler">Programador en el que se va a intentar ejecutar esta tarea insertada.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> instance has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Threading.Tasks.Task" /> is not in a valid state to be started.It may have already been started, executed, or canceled, or it may have been created in a manner that doesn't support direct scheduling.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Start">
      <summary>Inicia el objeto <see cref="T:System.Threading.Tasks.Task" />, programando su ejecución en el objeto <see cref="T:System.Threading.Tasks.TaskScheduler" /> actual.</summary>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> instance has been disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Threading.Tasks.Task" /> is not in a valid state to be started.It may have already been started, executed, or canceled, or it may have been created in a manner that doesn't support direct scheduling.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Start(System.Threading.Tasks.TaskScheduler)">
      <summary>Inicia el objeto <see cref="T:System.Threading.Tasks.Task" />, programando su ejecución en el objeto <see cref="T:System.Threading.Tasks.TaskScheduler" /> especificado.</summary>
      <param name="scheduler">Objeto <see cref="T:System.Threading.Tasks.TaskScheduler" /> con el que se va a asociar y ejecutar esta tarea.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> instance has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Threading.Tasks.Task" /> is not in a valid state to be started.It may have already been started, executed, or canceled, or it may have been created in a manner that doesn't support direct scheduling.</exception>
    </member>
    <member name="P:System.Threading.Tasks.Task.Status">
      <summary>Obtiene el objeto <see cref="T:System.Threading.Tasks.TaskStatus" /> de esta tarea.</summary>
      <returns>Valor actual de la propiedad <see cref="T:System.Threading.Tasks.TaskStatus" /> de esta instancia de la tarea.</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task.System#IAsyncResult#AsyncWaitHandle">
      <summary>Obtiene un identificador <see cref="T:System.Threading.WaitHandle" /> que se puede usar para esperar a que se complete la tarea.</summary>
      <returns>Identificador <see cref="T:System.Threading.WaitHandle" /> que se puede usar para esperar a que se complete la tarea.</returns>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
    </member>
    <member name="P:System.Threading.Tasks.Task.System#IAsyncResult#CompletedSynchronously">
      <summary>Obtiene un valor que indica si la operación se ha completado sincrónicamente.</summary>
      <returns>true si la operación se completó de forma sincrónica; en caso contrario, false.</returns>
    </member>
    <member name="M:System.Threading.Tasks.Task.Wait">
      <summary>Espera a que se complete la ejecución del objeto <see cref="T:System.Threading.Tasks.Task" />.</summary>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.AggregateException">The task was canceled.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object.-or-An exception was thrown during the execution of the task.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Wait(System.Int32)">
      <summary>Espera a que el objeto <see cref="T:System.Threading.Tasks.Task" /> complete la ejecución dentro de un número especificado de milisegundos.</summary>
      <returns>true si <see cref="T:System.Threading.Tasks.Task" /> completó su ejecución en el tiempo asignado; en caso contrario, false.</returns>
      <param name="millisecondsTimeout">Número de milisegundos de espera o <see cref="F:System.Threading.Timeout.Infinite" /> (-1) para esperar indefinidamente.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.AggregateException">The task was canceled.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object.-or-An exception was thrown during the execution of the task.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Wait(System.Int32,System.Threading.CancellationToken)">
      <summary>Espera a que se complete la ejecución del objeto <see cref="T:System.Threading.Tasks.Task" />.La espera finalizará si transcurre un intervalo de tiempo de espera o un token de cancelación se cancela antes de que finalice la tarea.</summary>
      <returns>true si <see cref="T:System.Threading.Tasks.Task" /> completó su ejecución en el tiempo asignado; en caso contrario, false.</returns>
      <param name="millisecondsTimeout">Número de milisegundos de espera o <see cref="F:System.Threading.Timeout.Infinite" /> (-1) para esperar indefinidamente. </param>
      <param name="cancellationToken">Un token de cancelación que se observará mientras se espera a que se complete la tarea. </param>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.AggregateException">The task was canceled.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object.-or-An exception was thrown during the execution of the task.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Wait(System.Threading.CancellationToken)">
      <summary>Espera a que se complete la ejecución del objeto <see cref="T:System.Threading.Tasks.Task" />.La espera finalizará si un token de cancelación se cancela antes de que finalice la tarea.</summary>
      <param name="cancellationToken">Un token de cancelación que se observará mientras se espera a que se complete la tarea. </param>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The task has been disposed.</exception>
      <exception cref="T:System.AggregateException">The task was canceled.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object.-or-An exception was thrown during the execution of the task.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Wait(System.TimeSpan)">
      <summary>Espera a que <see cref="T:System.Threading.Tasks.Task" /> complete la ejecución dentro de un intervalo de tiempo especificado.</summary>
      <returns>true si <see cref="T:System.Threading.Tasks.Task" /> completó su ejecución en el tiempo asignado; en caso contrario, false.</returns>
      <param name="timeout">Estructura <see cref="T:System.TimeSpan" /> que representa el número de milisegundos de espera o estructura <see cref="T:System.TimeSpan" /> que representa -1 milisegundos para esperar indefinidamente.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> is a negative number other than -1 milliseconds, which represents an infinite time-out. -or-<paramref name="timeout" /> is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
      <exception cref="T:System.AggregateException">The task was canceled.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object.-or-An exception was thrown during the execution of the task.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAll(System.Threading.Tasks.Task[])">
      <summary>Espera que se complete la ejecución de todos los objetos <see cref="T:System.Threading.Tasks.Task" /> proporcionados.</summary>
      <param name="tasks">Matriz de instancias de <see cref="T:System.Threading.Tasks.Task" /> en las que se va a esperar.</param>
      <exception cref="T:System.ObjectDisposedException">One or more of the <see cref="T:System.Threading.Tasks.Task" /> objects in <paramref name="tasks" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.-or-The <paramref name="tasks" /> argument contains a null element.</exception>
      <exception cref="T:System.AggregateException">At least one of the <see cref="T:System.Threading.Tasks.Task" /> instances was canceled.If a task was canceled, the <see cref="T:System.AggregateException" /> exception contains an <see cref="T:System.OperationCanceledException" /> exception in its <see cref="P:System.AggregateException.InnerExceptions" /> collection.-or-An exception was thrown during the execution of at least one of the <see cref="T:System.Threading.Tasks.Task" /> instances. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAll(System.Threading.Tasks.Task[],System.Int32)">
      <summary>Espera a que todos los objetos proporcionados de <see cref="T:System.Threading.Tasks.Task" /> completen la ejecución dentro de un número especificado de milisegundos.</summary>
      <returns>true si todas las instancias de <see cref="T:System.Threading.Tasks.Task" /> completan su ejecución en el tiempo asignado; en caso contrario, false.</returns>
      <param name="tasks">Matriz de instancias de <see cref="T:System.Threading.Tasks.Task" /> en las que se va a esperar.</param>
      <param name="millisecondsTimeout">Número de milisegundos de espera o <see cref="F:System.Threading.Timeout.Infinite" /> (-1) para esperar indefinidamente.</param>
      <exception cref="T:System.ObjectDisposedException">One or more of the <see cref="T:System.Threading.Tasks.Task" /> objects in <paramref name="tasks" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">At least one of the <see cref="T:System.Threading.Tasks.Task" /> instances was canceled.If a task was canceled, the <see cref="T:System.AggregateException" /> contains an <see cref="T:System.OperationCanceledException" /> in its <see cref="P:System.AggregateException.InnerExceptions" /> collection.-or-An exception was thrown during the execution of at least one of the <see cref="T:System.Threading.Tasks.Task" /> instances. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAll(System.Threading.Tasks.Task[],System.Int32,System.Threading.CancellationToken)">
      <summary>Espera a que todos los objetos <see cref="T:System.Threading.Tasks.Task" /> proporcionados completen la ejecución dentro de un número especificado de milisegundos o hasta que se cancele la espera.</summary>
      <returns>true si todas las instancias de <see cref="T:System.Threading.Tasks.Task" /> completan su ejecución en el tiempo asignado; en caso contrario, false.</returns>
      <param name="tasks">Matriz de instancias de <see cref="T:System.Threading.Tasks.Task" /> en las que se va a esperar.</param>
      <param name="millisecondsTimeout">Número de milisegundos de espera o <see cref="F:System.Threading.Timeout.Infinite" /> (-1) para esperar indefinidamente.</param>
      <param name="cancellationToken">Un <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> que se observará mientras se espera a que se complete la tarea.</param>
      <exception cref="T:System.ObjectDisposedException">One or more of the <see cref="T:System.Threading.Tasks.Task" /> objects in <paramref name="tasks" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">At least one of the <see cref="T:System.Threading.Tasks.Task" /> instances was canceled.If a task was canceled, the <see cref="T:System.AggregateException" /> contains an <see cref="T:System.OperationCanceledException" /> in its <see cref="P:System.AggregateException.InnerExceptions" /> collection.-or-An exception was thrown during the execution of at least one of the <see cref="T:System.Threading.Tasks.Task" /> instances. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAll(System.Threading.Tasks.Task[],System.Threading.CancellationToken)">
      <summary>Espera que se complete la ejecución de todos los objetos <see cref="T:System.Threading.Tasks.Task" /> proporcionados, a menos que se cancele la espera. </summary>
      <param name="tasks">Matriz de instancias de <see cref="T:System.Threading.Tasks.Task" /> en las que se va a esperar.</param>
      <param name="cancellationToken">Un <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> que se observará mientras se espera a que se complete la tarea.</param>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">At least one of the <see cref="T:System.Threading.Tasks.Task" /> instances was canceled.If a task was canceled, the <see cref="T:System.AggregateException" /> contains an <see cref="T:System.OperationCanceledException" /> in its <see cref="P:System.AggregateException.InnerExceptions" /> collection.-or-An exception was thrown during the execution of at least one of the <see cref="T:System.Threading.Tasks.Task" /> instances. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
      <exception cref="T:System.ObjectDisposedException">One or more of the <see cref="T:System.Threading.Tasks.Task" /> objects in <paramref name="tasks" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAll(System.Threading.Tasks.Task[],System.TimeSpan)">
      <summary>Espera a que todos los objetos <see cref="T:System.Threading.Tasks.Task" /> cancelables que se hayan proporcionado completen la ejecución en un intervalo de tiempo especificado.</summary>
      <returns>true si todas las instancias de <see cref="T:System.Threading.Tasks.Task" /> completan su ejecución en el tiempo asignado; en caso contrario, false.</returns>
      <param name="tasks">Matriz de instancias de <see cref="T:System.Threading.Tasks.Task" /> en las que se va a esperar.</param>
      <param name="timeout">Estructura <see cref="T:System.TimeSpan" /> que representa el número de milisegundos de espera o estructura <see cref="T:System.TimeSpan" /> que representa -1 milisegundos para esperar indefinidamente.</param>
      <exception cref="T:System.ObjectDisposedException">One or more of the <see cref="T:System.Threading.Tasks.Task" /> objects in <paramref name="tasks" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null. </exception>
      <exception cref="T:System.AggregateException">At least one of the <see cref="T:System.Threading.Tasks.Task" /> instances was canceled.If a task was canceled, the <see cref="T:System.AggregateException" /> contains an <see cref="T:System.OperationCanceledException" /> in its <see cref="P:System.AggregateException.InnerExceptions" /> collection.-or-An exception was thrown during the execution of at least one of the <see cref="T:System.Threading.Tasks.Task" /> instances. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> is a negative number other than -1 milliseconds, which represents an infinite time-out. -or-<paramref name="timeout" /> is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAny(System.Threading.Tasks.Task[])">
      <summary>Espera a que se complete la ejecución de cualquiera de los objetos <see cref="T:System.Threading.Tasks.Task" /> proporcionados.</summary>
      <returns>Índice de la tarea completada en la matriz especificada por el parámetro <paramref name="tasks" />.</returns>
      <param name="tasks">Matriz de instancias de <see cref="T:System.Threading.Tasks.Task" /> en las que se va a esperar.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAny(System.Threading.Tasks.Task[],System.Int32)">
      <summary>Espera a que cualquiera de los objetos <see cref="T:System.Threading.Tasks.Task" /> que se hayan proporcionado complete su ejecución dentro de un número especificado de milisegundos.</summary>
      <returns>Índice de la tarea completada en la matriz especificada por el parámetro <paramref name="tasks" /> o -1 si se agotó el tiempo de espera.</returns>
      <param name="tasks">Matriz de instancias de <see cref="T:System.Threading.Tasks.Task" /> en las que se va a esperar.</param>
      <param name="millisecondsTimeout">Número de milisegundos de espera o <see cref="F:System.Threading.Timeout.Infinite" /> (-1) para esperar indefinidamente.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAny(System.Threading.Tasks.Task[],System.Int32,System.Threading.CancellationToken)">
      <summary>Espera a que cualquiera de los objetos <see cref="T:System.Threading.Tasks.Task" /> proporcionados complete la ejecución dentro de un número especificado de milisegundos o hasta que se cancele un token de cancelación.</summary>
      <returns>Índice de la tarea completada en la matriz especificada por el parámetro <paramref name="tasks" /> o -1 si se agotó el tiempo de espera.</returns>
      <param name="tasks">Matriz de instancias de <see cref="T:System.Threading.Tasks.Task" /> en las que se va a esperar. </param>
      <param name="millisecondsTimeout">Número de milisegundos de espera o <see cref="F:System.Threading.Timeout.Infinite" /> (-1) para esperar indefinidamente. </param>
      <param name="cancellationToken">Un <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> que se observará mientras se espera a que se complete la tarea. </param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="millisecondsTimeout" /> is a negative number other than -1, which represents an infinite time-out.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAny(System.Threading.Tasks.Task[],System.Threading.CancellationToken)">
      <summary>Espera que se complete la ejecución de cualquiera de los objetos <see cref="T:System.Threading.Tasks.Task" /> proporcionados, a menos que se cancele la espera.</summary>
      <returns>Índice de la tarea completada en la matriz especificada por el parámetro <paramref name="tasks" />.</returns>
      <param name="tasks">Matriz de instancias de <see cref="T:System.Threading.Tasks.Task" /> en las que se va a esperar. </param>
      <param name="cancellationToken">Un <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> que se observará mientras se espera a que se complete la tarea. </param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
      <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken" /> was canceled.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WaitAny(System.Threading.Tasks.Task[],System.TimeSpan)">
      <summary>Espera a que se complete la ejecución de cualquier objeto <see cref="T:System.Threading.Tasks.Task" /> proporcionado en un intervalo de tiempo especificado.</summary>
      <returns>Índice de la tarea completada en la matriz especificada por el parámetro <paramref name="tasks" /> o -1 si se agotó el tiempo de espera.</returns>
      <param name="tasks">Matriz de instancias de <see cref="T:System.Threading.Tasks.Task" /> en las que se va a esperar.</param>
      <param name="timeout">Estructura <see cref="T:System.TimeSpan" /> que representa el número de milisegundos de espera o estructura <see cref="T:System.TimeSpan" /> que representa -1 milisegundos para esperar indefinidamente.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="timeout" /> is a negative number other than -1 milliseconds, which represents an infinite time-out. -or-<paramref name="timeout" /> is greater than <see cref="F:System.Int32.MaxValue" />. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> argument contains a null element.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAll``1(System.Collections.Generic.IEnumerable{System.Threading.Tasks.Task{``0}})">
      <summary>Crea una tarea que se completará cuando todos los objetos <see cref="T:System.Threading.Tasks.Task`1" /> de una colección enumerable se hayan completado. </summary>
      <returns>Tarea que representa la finalización de todas las tareas proporcionadas. </returns>
      <param name="tasks">Tareas cuya finalización hay que esperar. </param>
      <typeparam name="TResult">Tipo de la tarea completada. </typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> collection contained a null task. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAll(System.Collections.Generic.IEnumerable{System.Threading.Tasks.Task})">
      <summary>Crea una tarea que se completará cuando todos los objetos <see cref="T:System.Threading.Tasks.Task" /> de una colección enumerable se hayan completado.</summary>
      <returns>Tarea que representa la finalización de todas las tareas proporcionadas. </returns>
      <param name="tasks">Tareas cuya finalización hay que esperar.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> collection contained a null task.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAll(System.Threading.Tasks.Task[])">
      <summary>Crea una tarea que se completará cuando todos los objetos <see cref="T:System.Threading.Tasks.Task" /> de una matriz se hayan completado. </summary>
      <returns>Tarea que representa la finalización de todas las tareas proporcionadas.</returns>
      <param name="tasks">Tareas cuya finalización hay que esperar.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null. </exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAll``1(System.Threading.Tasks.Task{``0}[])">
      <summary>Crea una tarea que se completará cuando todos los objetos <see cref="T:System.Threading.Tasks.Task`1" /> de una matriz se hayan completado. </summary>
      <returns>Tarea que representa la finalización de todas las tareas proporcionadas.</returns>
      <param name="tasks">Tareas cuya finalización hay que esperar.</param>
      <typeparam name="TResult">Tipo de la tarea completada.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAny``1(System.Collections.Generic.IEnumerable{System.Threading.Tasks.Task{``0}})">
      <summary>Crea una tarea que finalizará cuando se haya completado cualquiera de las tareas proporcionadas.</summary>
      <returns>Tarea que representa la finalización de una de las tareas proporcionadas.El resultado de la tarea devuelto es la tarea completada.</returns>
      <param name="tasks">Tareas cuya finalización hay que esperar.</param>
      <typeparam name="TResult">Tipo de la tarea completada.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task, or was empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAny(System.Collections.Generic.IEnumerable{System.Threading.Tasks.Task})">
      <summary>Crea una tarea que finalizará cuando se haya completado cualquiera de las tareas proporcionadas.</summary>
      <returns>Tarea que representa la finalización de una de las tareas proporcionadas.El resultado de la tarea devuelto es la tarea completada.</returns>
      <param name="tasks">Tareas cuya finalización hay que esperar.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task, or was empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAny(System.Threading.Tasks.Task[])">
      <summary>Crea una tarea que finalizará cuando se haya completado cualquiera de las tareas proporcionadas.</summary>
      <returns>Tarea que representa la finalización de una de las tareas proporcionadas.El resultado de la tarea devuelto es la tarea completada.</returns>
      <param name="tasks">Tareas cuya finalización hay que esperar.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task, or was empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.WhenAny``1(System.Threading.Tasks.Task{``0}[])">
      <summary>Crea una tarea que finalizará cuando se haya completado cualquiera de las tareas proporcionadas.</summary>
      <returns>Tarea que representa la finalización de una de las tareas proporcionadas.El resultado de la tarea devuelto es la tarea completada.</returns>
      <param name="tasks">Tareas cuya finalización hay que esperar.</param>
      <typeparam name="TResult">Tipo de la tarea completada.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="tasks" /> argument was null.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="tasks" /> array contained a null task, or was empty.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task.Yield">
      <summary>Crea una tarea que admite "await" que, de forma asincrónica, devuelve al contexto actual cuando es "awaited".</summary>
      <returns>Contexto que, cuando se espera, hará la transición de vuelta de forma asincrónica en el contexto actual en el momento de la espera.Si el objeto <see cref="T:System.Threading.SynchronizationContext" /> actual no es null, se trata como el contexto actual.Si no, el programador de tareas que está asociado a la tarea que se está ejecutando actualmente se trata como el contexto actual.</returns>
    </member>
    <member name="T:System.Threading.Tasks.Task`1">
      <summary>Representa una operación asincrónica que puede devolver un valor.</summary>
      <typeparam name="TResult">El tipo del resultado generado por esta <see cref="T:System.Threading.Tasks.Task`1" />. </typeparam>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{`0})">
      <summary>Inicializa una nueva <see cref="T:System.Threading.Tasks.Task`1" /> con la función especificada.</summary>
      <param name="function">Delegado que representa el código que se va a ejecutar en la tarea.Cuando se complete la función, se establecerá la propiedad <see cref="P:System.Threading.Tasks.Task`1.Result" /> de la tarea para que se devuelva el valor de resultado de la función.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{`0},System.Threading.CancellationToken)">
      <summary>Inicializa una nueva <see cref="T:System.Threading.Tasks.Task`1" /> con la función especificada.</summary>
      <param name="function">Delegado que representa el código que se va a ejecutar en la tarea.Cuando se complete la función, se establecerá la propiedad <see cref="P:System.Threading.Tasks.Task`1.Result" /> de la tarea para que se devuelva el valor de resultado de la función.</param>
      <param name="cancellationToken">El <see cref="T:System.Threading.CancellationToken" /> al que se va a asignar esta tarea.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Inicializa una nueva <see cref="T:System.Threading.Tasks.Task`1" /> con la función y opciones de creación especificadas.</summary>
      <param name="function">Delegado que representa el código que se va a ejecutar en la tarea.Cuando se complete la función, se establecerá la propiedad <see cref="P:System.Threading.Tasks.Task`1.Result" /> de la tarea para que se devuelva el valor de resultado de la función.</param>
      <param name="cancellationToken">El <see cref="T:System.Threading.CancellationToken" /> al que se va a asignar la nueva tarea.</param>
      <param name="creationOptions">El objeto <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> que se usa para personalizar el comportamiento de la tarea.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{`0},System.Threading.Tasks.TaskCreationOptions)">
      <summary>Inicializa una nueva <see cref="T:System.Threading.Tasks.Task`1" /> con la función y opciones de creación especificadas.</summary>
      <param name="function">Delegado que representa el código que se va a ejecutar en la tarea.Cuando se complete la función, se establecerá la propiedad <see cref="P:System.Threading.Tasks.Task`1.Result" /> de la tarea para que se devuelva el valor de resultado de la función.</param>
      <param name="creationOptions">El objeto <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> que se usa para personalizar el comportamiento de la tarea.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{System.Object,`0},System.Object)">
      <summary>Inicializa una nueva <see cref="T:System.Threading.Tasks.Task`1" /> con la función y estado especificados.</summary>
      <param name="function">Delegado que representa el código que se va a ejecutar en la tarea.Cuando se complete la función, se establecerá la propiedad <see cref="P:System.Threading.Tasks.Task`1.Result" /> de la tarea para que se devuelva el valor de resultado de la función.</param>
      <param name="state">Objeto que representa los datos que la acción va a usar.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{System.Object,`0},System.Object,System.Threading.CancellationToken)">
      <summary>Inicializa un nuevo objeto <see cref="T:System.Threading.Tasks.Task`1" /> con la acción, el estado y las opciones especificados.</summary>
      <param name="function">Delegado que representa el código que se va a ejecutar en la tarea.Cuando se complete la función, se establecerá la propiedad <see cref="P:System.Threading.Tasks.Task`1.Result" /> de la tarea para que se devuelva el valor de resultado de la función.</param>
      <param name="state">Un objeto que representa los datos que la función va a usar.</param>
      <param name="cancellationToken">El <see cref="T:System.Threading.CancellationToken" /> que se va a asignar a la nueva tarea.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{System.Object,`0},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Inicializa un nuevo objeto <see cref="T:System.Threading.Tasks.Task`1" /> con la acción, el estado y las opciones especificados.</summary>
      <param name="function">Delegado que representa el código que se va a ejecutar en la tarea.Cuando se complete la función, se establecerá la propiedad <see cref="P:System.Threading.Tasks.Task`1.Result" /> de la tarea para que se devuelva el valor de resultado de la función.</param>
      <param name="state">Un objeto que representa los datos que la función va a usar.</param>
      <param name="cancellationToken">El <see cref="T:System.Threading.CancellationToken" /> que se va a asignar a la nueva tarea.</param>
      <param name="creationOptions">El objeto <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> que se usa para personalizar el comportamiento de la tarea.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.#ctor(System.Func{System.Object,`0},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Inicializa un nuevo objeto <see cref="T:System.Threading.Tasks.Task`1" /> con la acción, el estado y las opciones especificados.</summary>
      <param name="function">Delegado que representa el código que se va a ejecutar en la tarea.Cuando se complete la función, se establecerá la propiedad <see cref="P:System.Threading.Tasks.Task`1.Result" /> de la tarea para que se devuelva el valor de resultado de la función.</param>
      <param name="state">Un objeto que representa los datos que la función va a usar.</param>
      <param name="creationOptions">El objeto <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> que se usa para personalizar el comportamiento de la tarea.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="creationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskCreationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="function" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ConfigureAwait(System.Boolean)">
      <summary>Configura un awaiter utilizado para esperar a este objeto <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>Objeto utilizado para esperar a esta tarea.</returns>
      <param name="continueOnCapturedContext">true para intentar calcular las referencias de la continuación de nuevo al contexto original capturado; de lo contrario, false.</param>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}})">
      <summary>Crea una continuación que se ejecuta de manera asincrónica cuando se completa la tarea de destino. </summary>
      <returns>Una tarea de continuación nueva. </returns>
      <param name="continuationAction">Una acción que se ejecuta cuando se completa el <see cref="T:System.Threading.Tasks.Task`1" /> antecedente.Cuando se ejecute, al delegado se le pasará la tarea completada como un argumento.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}},System.Threading.CancellationToken)">
      <summary>Crea una continuación cancelable que se ejecuta de manera asincrónica cuando se completa el <see cref="T:System.Threading.Tasks.Task`1" /> objetivo.</summary>
      <returns>Una tarea de continuación nueva. </returns>
      <param name="continuationAction">Acción que se va a ejecutar cuando se complete el objeto <see cref="T:System.Threading.Tasks.Task`1" />.Cuando se ejecuta, se le pasará al delegado la tarea completada como un argumento.</param>
      <param name="cancellationToken">El token de cancelación que se pasa a la nueva tarea de continuación. </param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has been disposed. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea una continuación que se ejecuta según la condición especificada en <paramref name="continuationOptions" />.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task" /> de continuación.</returns>
      <param name="continuationAction">Una acción que se ejecuta según la condición especificada en <paramref name="continuationOptions" />.Cuando se ejecute, al delegado se le pasará la tarea completada como un argumento.</param>
      <param name="cancellationToken">El <see cref="T:System.Threading.CancellationToken" /> que se asignará a la nueva tarea de continuación.</param>
      <param name="continuationOptions">Opciones para la programación y el comportamiento de la continuación.Incluye criterios, como <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, y opciones de ejecución, como <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <param name="scheduler">Objeto <see cref="T:System.Threading.Tasks.TaskScheduler" /> que se va a asociar a la tarea de continuación y se va a usar para su ejecución.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created <paramref name="cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crea una continuación que se ejecuta según la condición especificada en <paramref name="continuationOptions" />.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task" /> de continuación.</returns>
      <param name="continuationAction">Una acción según la condición especificada en <paramref name="continuationOptions" />.Cuando se ejecute, al delegado se le pasará la tarea completada como un argumento.</param>
      <param name="continuationOptions">Opciones para la programación y el comportamiento de la continuación.Incluye criterios, como <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, y opciones de ejecución, como <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}},System.Threading.Tasks.TaskScheduler)">
      <summary>Crea una continuación que se ejecuta de manera asincrónica cuando se completa el objeto <see cref="T:System.Threading.Tasks.Task`1" /> de destino.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task" /> de continuación.</returns>
      <param name="continuationAction">Acción que se va a ejecutar cuando se complete el objeto <see cref="T:System.Threading.Tasks.Task`1" />.Cuando se ejecute, al delegado se le pasará la tarea completada como un argumento.</param>
      <param name="scheduler">Objeto <see cref="T:System.Threading.Tasks.TaskScheduler" /> que se va a asociar a la tarea de continuación y se va a usar para su ejecución.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0},System.Object},System.Object)">
      <summary>Crea una continuación a la que se pasa información de estado y que se ejecuta cuando el objeto <see cref="T:System.Threading.Tasks.Task`1" /> de destino se completa. </summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task" /> de continuación.</returns>
      <param name="continuationAction">Acción que se va a ejecutar cuando se complete el objeto <see cref="T:System.Threading.Tasks.Task`1" />.Cuando se ejecuta, se pasan al delegado la tarea completada y el objeto de estado proporcionado por el autor de la llamada como argumentos.</param>
      <param name="state">Objeto que representa los datos que la acción de continuación va a usar. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0},System.Object},System.Object,System.Threading.CancellationToken)">
      <summary>Crea una continuación que se ejecuta cuando se completa el objeto <see cref="T:System.Threading.Tasks.Task`1" /> de destino.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task" /> de continuación.</returns>
      <param name="continuationAction">Acción que se va a ejecutar cuando se complete el objeto <see cref="T:System.Threading.Tasks.Task`1" />.Cuando se ejecute, se pasarán al delegado la tarea completada y el objeto de estado proporcionado por el llamador como argumentos.</param>
      <param name="state">Objeto que representa los datos que la acción de continuación va a usar.</param>
      <param name="cancellationToken">El <see cref="T:System.Threading.CancellationToken" /> que se asignará a la nueva tarea de continuación.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0},System.Object},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea una continuación que se ejecuta cuando se completa el objeto <see cref="T:System.Threading.Tasks.Task`1" /> de destino.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task" /> de continuación.</returns>
      <param name="continuationAction">Acción que se va a ejecutar cuando se complete el objeto <see cref="T:System.Threading.Tasks.Task`1" />.Cuando se ejecute, se pasarán al delegado la tarea completada y el objeto de estado proporcionado por el llamador como argumentos.</param>
      <param name="state">Objeto que representa los datos que la acción de continuación va a usar.</param>
      <param name="cancellationToken">El <see cref="T:System.Threading.CancellationToken" /> que se asignará a la nueva tarea de continuación.</param>
      <param name="continuationOptions">Opciones para la programación y el comportamiento de la continuación.Incluye criterios, como <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, y opciones de ejecución, como <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <param name="scheduler">Objeto <see cref="T:System.Threading.Tasks.TaskScheduler" /> que se va a asociar a la tarea de continuación y se va a usar para su ejecución.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0},System.Object},System.Object,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crea una continuación que se ejecuta cuando se completa el objeto <see cref="T:System.Threading.Tasks.Task`1" /> de destino.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task" /> de continuación.</returns>
      <param name="continuationAction">Acción que se va a ejecutar cuando se complete el objeto <see cref="T:System.Threading.Tasks.Task`1" />.Cuando se ejecute, se pasarán al delegado la tarea completada y el objeto de estado proporcionado por el llamador como argumentos.</param>
      <param name="state">Objeto que representa los datos que la acción de continuación va a usar.</param>
      <param name="continuationOptions">Opciones para la programación y el comportamiento de la continuación.Incluye criterios, como <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, y opciones de ejecución, como <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0},System.Object},System.Object,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea una continuación que se ejecuta cuando se completa el objeto <see cref="T:System.Threading.Tasks.Task`1" /> de destino.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task" /> de continuación.</returns>
      <param name="continuationAction">Acción que se va a ejecutar cuando se complete el objeto <see cref="T:System.Threading.Tasks.Task`1" />.Cuando se ejecute, se pasarán al delegado la tarea completada y el objeto de estado proporcionado por el llamador como argumentos.</param>
      <param name="state">Objeto que representa los datos que la acción de continuación va a usar.</param>
      <param name="scheduler">Objeto <see cref="T:System.Threading.Tasks.TaskScheduler" /> que se va a asociar a la tarea de continuación y se va a usar para su ejecución.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationAction" /> argument is null. </exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null. </exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},``0})">
      <summary>Crea una continuación que se ejecuta de manera asincrónica cuando se completa el objeto <see cref="T:System.Threading.Tasks.Task`1" /> de destino.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task`1" /> de continuación.</returns>
      <param name="continuationFunction">Función que se va a ejecutar cuando se complete el objeto <see cref="T:System.Threading.Tasks.Task`1" />.Cuando se ejecute, al delegado se le pasará la tarea completada como un argumento.</param>
      <typeparam name="TNewResult"> Tipo de resultado generado por la continuación.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},``0},System.Threading.CancellationToken)">
      <summary>Crea una continuación que se ejecuta de manera asincrónica cuando se completa el objeto <see cref="T:System.Threading.Tasks.Task`1" /> de destino.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task`1" /> de continuación.</returns>
      <param name="continuationFunction">Función que se va a ejecutar cuando se complete el objeto <see cref="T:System.Threading.Tasks.Task`1" />.Cuando se ejecute, al delegado se le pasará la tarea completada como un argumento.</param>
      <param name="cancellationToken">El <see cref="T:System.Threading.CancellationToken" /> al que se va a asignar la nueva tarea.</param>
      <typeparam name="TNewResult"> Tipo de resultado generado por la continuación.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},``0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea una continuación que se ejecuta según la condición especificada en <paramref name="continuationOptions" />.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task`1" /> de continuación.</returns>
      <param name="continuationFunction">Una función que se ejecuta según la condición especificada en <paramref name="continuationOptions" />.Cuando se ejecute, al delegado se le pasará la tarea completada como un argumento.</param>
      <param name="cancellationToken">El <see cref="T:System.Threading.CancellationToken" /> al que se va a asignar la nueva tarea.</param>
      <param name="continuationOptions">Opciones para la programación y el comportamiento de la continuación.Incluye criterios, como <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, y opciones de ejecución, como <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <param name="scheduler">Objeto <see cref="T:System.Threading.Tasks.TaskScheduler" /> que se va a asociar a la tarea de continuación y se va a usar para su ejecución.</param>
      <typeparam name="TNewResult"> Tipo de resultado generado por la continuación.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.-or-The <see cref="T:System.Threading.CancellationTokenSource" /> that created<paramref name=" cancellationToken" /> has already been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},``0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crea una continuación que se ejecuta según la condición especificada en <paramref name="continuationOptions" />.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task`1" /> de continuación.</returns>
      <param name="continuationFunction">Una función que se ejecuta según la condición especificada en <paramref name="continuationOptions" />.Cuando se ejecute, al delegado se le pasará la tarea completada como un argumento.</param>
      <param name="continuationOptions">Opciones para la programación y el comportamiento de la continuación.Incluye criterios, como <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, y opciones de ejecución, como <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <typeparam name="TNewResult"> Tipo de resultado generado por la continuación.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},``0},System.Threading.Tasks.TaskScheduler)">
      <summary>Crea una continuación que se ejecuta de manera asincrónica cuando se completa el objeto <see cref="T:System.Threading.Tasks.Task`1" /> de destino.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task`1" /> de continuación.</returns>
      <param name="continuationFunction">Función que se va a ejecutar cuando se complete el objeto <see cref="T:System.Threading.Tasks.Task`1" />.Cuando se ejecute, al delegado se le pasará la tarea completada como un argumento.</param>
      <param name="scheduler">Objeto <see cref="T:System.Threading.Tasks.TaskScheduler" /> que se va a asociar a la tarea de continuación y se va a usar para su ejecución.</param>
      <typeparam name="TNewResult"> Tipo de resultado generado por la continuación.</typeparam>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.Tasks.Task`1" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.-or-The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},System.Object,``0},System.Object)">
      <summary>Crea una continuación que se ejecuta cuando se completa el objeto <see cref="T:System.Threading.Tasks.Task`1" /> de destino.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task`1" /> de continuación.</returns>
      <param name="continuationFunction">Función que se va a ejecutar cuando se complete el objeto <see cref="T:System.Threading.Tasks.Task`1" />.Cuando se ejecute, se pasarán al delegado la tarea completada y el objeto de estado proporcionado por el llamador como argumentos.</param>
      <param name="state">Objeto que representa los datos que la función de continuación va a usar.</param>
      <typeparam name="TNewResult">Tipo de resultado generado por la continuación.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},System.Object,``0},System.Object,System.Threading.CancellationToken)">
      <summary>Crea una continuación que se ejecuta cuando se completa el objeto <see cref="T:System.Threading.Tasks.Task`1" /> de destino.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task`1" /> de continuación.</returns>
      <param name="continuationFunction">Función que se va a ejecutar cuando se complete el objeto <see cref="T:System.Threading.Tasks.Task`1" />.Cuando se ejecute, se pasarán al delegado la tarea completada y el objeto de estado proporcionado por el llamador como argumentos.</param>
      <param name="state">Objeto que representa los datos que la función de continuación va a usar.</param>
      <param name="cancellationToken">El <see cref="T:System.Threading.CancellationToken" /> al que se va a asignar la nueva tarea.</param>
      <typeparam name="TNewResult">Tipo de resultado generado por la continuación.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},System.Object,``0},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea una continuación que se ejecuta cuando se completa el objeto <see cref="T:System.Threading.Tasks.Task`1" /> de destino.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task`1" /> de continuación.</returns>
      <param name="continuationFunction">Función que se va a ejecutar cuando se complete el objeto <see cref="T:System.Threading.Tasks.Task`1" />.Cuando se ejecute, se pasarán al delegado la tarea completada y el objeto de estado proporcionado por el llamador como argumentos.</param>
      <param name="state">Objeto que representa los datos que la función de continuación va a usar.</param>
      <param name="cancellationToken">El <see cref="T:System.Threading.CancellationToken" /> al que se va a asignar la nueva tarea.</param>
      <param name="continuationOptions">Opciones para la programación y el comportamiento de la continuación.Incluye criterios, como <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, y opciones de ejecución, como <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <param name="scheduler">Objeto <see cref="T:System.Threading.Tasks.TaskScheduler" /> que se va a asociar a la tarea de continuación y se va a usar para su ejecución.</param>
      <typeparam name="TNewResult">Tipo de resultado generado por la continuación.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The  <paramref name="continuationOptions" />  argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The provided <see cref="T:System.Threading.CancellationToken" /> has already been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},System.Object,``0},System.Object,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crea una continuación que se ejecuta cuando se completa el objeto <see cref="T:System.Threading.Tasks.Task`1" /> de destino.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task`1" /> de continuación.</returns>
      <param name="continuationFunction">Función que se va a ejecutar cuando se complete el objeto <see cref="T:System.Threading.Tasks.Task`1" />.Cuando se ejecute, se pasarán al delegado la tarea completada y el objeto de estado proporcionado por el llamador como argumentos.</param>
      <param name="state">Objeto que representa los datos que la función de continuación va a usar.</param>
      <param name="continuationOptions">Opciones para la programación y el comportamiento de la continuación.Incluye criterios, como <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled" />, y opciones de ejecución, como <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</param>
      <typeparam name="TNewResult">Tipo de resultado generado por la continuación.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="continuationOptions" /> argument specifies an invalid value for <see cref="T:System.Threading.Tasks.TaskContinuationOptions" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.ContinueWith``1(System.Func{System.Threading.Tasks.Task{`0},System.Object,``0},System.Object,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea una continuación que se ejecuta cuando se completa el objeto <see cref="T:System.Threading.Tasks.Task`1" /> de destino.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task`1" /> de continuación.</returns>
      <param name="continuationFunction">Función que se va a ejecutar cuando se complete el objeto <see cref="T:System.Threading.Tasks.Task`1" />.Cuando se ejecute, se pasarán al delegado la tarea completada y el objeto de estado proporcionado por el llamador como argumentos.</param>
      <param name="state">Objeto que representa los datos que la función de continuación va a usar.</param>
      <param name="scheduler">Objeto <see cref="T:System.Threading.Tasks.TaskScheduler" /> que se va a asociar a la tarea de continuación y se va a usar para su ejecución.</param>
      <typeparam name="TNewResult">Tipo de resultado generado por la continuación.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="continuationFunction" /> argument is null.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="scheduler" /> argument is null.</exception>
    </member>
    <member name="P:System.Threading.Tasks.Task`1.Factory">
      <summary>Proporciona acceso a patrones de diseño para crear y configurar instancias de <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>Un objeto de fábrica que puede crear una variedad de objetos <see cref="T:System.Threading.Tasks.Task`1" />.</returns>
    </member>
    <member name="M:System.Threading.Tasks.Task`1.GetAwaiter">
      <summary>Obtiene un awaiter utilizado para esperar a este objeto <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>Una instancia de awaiter.</returns>
    </member>
    <member name="P:System.Threading.Tasks.Task`1.Result">
      <summary>Obtiene el valor de resultado de esta <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>El valor resultante de esta <see cref="T:System.Threading.Tasks.Task`1" />, que es el mismo que el parámetro de tipo de la tarea.</returns>
      <exception cref="T:System.AggregateException">The task was canceled.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains a <see cref="T:System.Threading.Tasks.TaskCanceledException" /> object.-or-An exception was thrown during the execution of the task.The <see cref="P:System.AggregateException.InnerExceptions" /> collection contains information about the exception or exceptions.</exception>
    </member>
    <member name="T:System.Threading.Tasks.TaskCanceledException">
      <summary>Representa una excepción utilizada para comunicar la cancelación de la tarea.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskCanceledException.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.Tasks.TaskCanceledException" /> con un mensaje proporcionado por el sistema que describe el error.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskCanceledException.#ctor(System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.Tasks.TaskCanceledException" /> con un mensaje de error especificado que describe el error.</summary>
      <param name="message">Mensaje que describe la excepción.El llamador de este constructor debe asegurarse de que esta cadena se ha traducido para la actual referencia cultural del sistema.</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskCanceledException.#ctor(System.String,System.Exception)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.Tasks.TaskCanceledException" /> con el mensaje de error especificado y una referencia a la excepción interna que representa la causa de esta excepción.</summary>
      <param name="message">Mensaje que describe la excepción.El llamador de este constructor debe asegurarse de que esta cadena se ha traducido para la actual referencia cultural del sistema.</param>
      <param name="innerException">La excepción que es la causa de la excepción actual.Si el parámetro <paramref name="innerException" /> no es null, la excepción actual se produce en un bloque catch que controla la excepción interna.</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskCanceledException.#ctor(System.Threading.Tasks.Task)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.Tasks.TaskCanceledException" /> con una referencia al objeto <see cref="T:System.Threading.Tasks.Task" /> que se ha cancelado.</summary>
      <param name="task">Tarea que se ha cancelado.</param>
    </member>
    <member name="P:System.Threading.Tasks.TaskCanceledException.Task">
      <summary>Obtiene la tarea asociada a esta excepción.</summary>
      <returns>Referencia al objeto <see cref="T:System.Threading.Tasks.Task" /> asociado a esta excepción.</returns>
    </member>
    <member name="T:System.Threading.Tasks.TaskCompletionSource`1">
      <summary>Representa el lado del productor de una <see cref="T:System.Threading.Tasks.Task`1" /> sin enlazar a un delegado, proporcionando acceso al lado del consumidor mediante la propiedad <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" />.</summary>
      <typeparam name="TResult">Tipo del valor de resultado asociado a este objeto <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" />.</typeparam>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.#ctor">
      <summary>Crea un objeto <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" />.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.#ctor(System.Object)">
      <summary>Crea un objeto <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" /> con el estado especificado.</summary>
      <param name="state">Estado que se va a usar como AsyncState de la <see cref="T:System.Threading.Tasks.Task`1" /> subyacente.</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.#ctor(System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crea un objeto <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" /> con el estado y las opciones especificados.</summary>
      <param name="state">Estado que se va a usar como AsyncState de la <see cref="T:System.Threading.Tasks.Task`1" /> subyacente.</param>
      <param name="creationOptions">Opciones que se van a usar al crear la <see cref="T:System.Threading.Tasks.Task`1" /> subyacente.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> representan las opciones no válidas para su uso con <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.#ctor(System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crea un objeto <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" /> con las opciones especificadas.</summary>
      <param name="creationOptions">Opciones que se van a usar al crear la <see cref="T:System.Threading.Tasks.Task`1" /> subyacente.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> representan las opciones no válidas para su uso con <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.SetCanceled">
      <summary>Transiciona la <see cref="T:System.Threading.Tasks.Task`1" /> subyacente al estado <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />.</summary>
      <exception cref="T:System.InvalidOperationException">La <see cref="T:System.Threading.Tasks.Task`1" /> subyacente ya está en uno de los tres estados finales: <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />, <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> o <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />, o si ya se ha eliminado la <see cref="T:System.Threading.Tasks.Task`1" /> subyacente.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.SetException(System.Collections.Generic.IEnumerable{System.Exception})">
      <summary>Transiciona la <see cref="T:System.Threading.Tasks.Task`1" /> subyacente al estado <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" />.</summary>
      <param name="exceptions">Colección de excepciones que se va a enlazar a esta <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <exception cref="T:System.ObjectDisposedException">Se eliminó <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" />.</exception>
      <exception cref="T:System.ArgumentNullException">El argumento de <paramref name="exceptions" /> es nulo.</exception>
      <exception cref="T:System.ArgumentException">Hay uno o más elementos NULL en <paramref name="exceptions" />.</exception>
      <exception cref="T:System.InvalidOperationException">La <see cref="T:System.Threading.Tasks.Task`1" /> subyacente ya está en uno de los tres estados finales: <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />, <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> o <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.SetException(System.Exception)">
      <summary>Transiciona la <see cref="T:System.Threading.Tasks.Task`1" /> subyacente al estado <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" />.</summary>
      <param name="exception">Excepción que se va a enlazar a esta <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <exception cref="T:System.ObjectDisposedException">Se eliminó <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" />.</exception>
      <exception cref="T:System.ArgumentNullException">El argumento de <paramref name="exception" /> es nulo.</exception>
      <exception cref="T:System.InvalidOperationException">La <see cref="T:System.Threading.Tasks.Task`1" /> subyacente ya está en uno de los tres estados finales: <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />, <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> o <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.SetResult(`0)">
      <summary>Transiciona la <see cref="T:System.Threading.Tasks.Task`1" /> subyacente al estado <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />.</summary>
      <param name="result">Valor de resultado que se va a enlazar a esta <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <exception cref="T:System.ObjectDisposedException">Se eliminó <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" />.</exception>
      <exception cref="T:System.InvalidOperationException">La <see cref="T:System.Threading.Tasks.Task`1" /> subyacente ya está en uno de los tres estados finales: <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />, <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" /> o <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />.</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskCompletionSource`1.Task">
      <summary>Obtiene la <see cref="T:System.Threading.Tasks.Task`1" /> creada por este objeto <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" />.</summary>
      <returns>Devuelve la <see cref="T:System.Threading.Tasks.Task`1" /> creada por este objeto <see cref="T:System.Threading.Tasks.TaskCompletionSource`1" />.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.TrySetCanceled">
      <summary>Intenta transicionar la <see cref="T:System.Threading.Tasks.Task`1" /> subyacente al estado <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />.</summary>
      <returns>Es true si la operación se realizó correctamente; es false si la operación no se realizó correctamente o ya se ha desechado el objeto.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.TrySetCanceled(System.Threading.CancellationToken)">
      <summary>Intenta transicionar la <see cref="T:System.Threading.Tasks.Task`1" /> subyacente al estado <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" /> y permite que se almacene un token de cancelación en la tarea cancelada. </summary>
      <returns>true si la operación es correcta; de lo contrario, false. </returns>
      <param name="cancellationToken">Token de cancelación. </param>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.TrySetException(System.Collections.Generic.IEnumerable{System.Exception})">
      <summary>Intenta transicionar la <see cref="T:System.Threading.Tasks.Task`1" /> subyacente al estado <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" />.</summary>
      <returns>Es true si la operación se realizó correctamente; en caso contrario, es false.</returns>
      <param name="exceptions">Colección de excepciones que se va a enlazar a esta <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <exception cref="T:System.ObjectDisposedException">Se eliminó <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" />.</exception>
      <exception cref="T:System.ArgumentNullException">El argumento de <paramref name="exceptions" /> es nulo.</exception>
      <exception cref="T:System.ArgumentException">Hay uno o más elementos NULL en <paramref name="exceptions" />.o bienLa colección <paramref name="exceptions" /> está vacía.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.TrySetException(System.Exception)">
      <summary>Intenta transicionar la <see cref="T:System.Threading.Tasks.Task`1" /> subyacente al estado <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" />.</summary>
      <returns>Es true si la operación se realizó correctamente; en caso contrario, es false.</returns>
      <param name="exception">Excepción que se va a enlazar a esta <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <exception cref="T:System.ObjectDisposedException">Se eliminó <see cref="P:System.Threading.Tasks.TaskCompletionSource`1.Task" />.</exception>
      <exception cref="T:System.ArgumentNullException">El argumento de <paramref name="exception" /> es nulo.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskCompletionSource`1.TrySetResult(`0)">
      <summary>Intenta transicionar la <see cref="T:System.Threading.Tasks.Task`1" /> subyacente al estado <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />.</summary>
      <returns>Es true si la operación se realizó correctamente; en caso contrario, es false. </returns>
      <param name="result">Valor de resultado que se va a enlazar a esta <see cref="T:System.Threading.Tasks.Task`1" />.</param>
    </member>
    <member name="T:System.Threading.Tasks.TaskContinuationOptions">
      <summary>Especifica el comportamiento de una tarea que se crea mediante el método <see cref="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)" /> o <see cref="M:System.Threading.Tasks.Task`1.ContinueWith(System.Action{System.Threading.Tasks.Task{`0}},System.Threading.Tasks.TaskContinuationOptions)" />.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.AttachedToParent">
      <summary>Especifica que la continuación, si es una tarea secundaria, se adjunta a un elemento primario en la jerarquía de tareas.La continuación puede ser una tarea secundaria solo si su antecedente también es una tarea secundaria.De forma predeterminada, una tarea secundaria (es decir, una tarea interna creada por una tarea externa) se ejecuta de forma independiente de la tarea principal.La opción <see cref="F:System.Threading.Tasks.TaskContinuationOptions.AttachedToParent" /> permite sincronizar las tareas primarias y secundarias.Tenga en cuenta que, si una tarea primaria se configura con la opción <see cref="F:System.Threading.Tasks.TaskCreationOptions.DenyChildAttach" />, la opción <see cref="F:System.Threading.Tasks.TaskCreationOptions.AttachedToParent" /> no tiene ningún efecto en la tarea secundaria y ésta se ejecutará como una tarea secundaria desasociada. Para obtener más información, vea el artículo sobre tareas secundarias adjuntas y desasociadas. </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.DenyChildAttach">
      <summary>Especifica que cualquier tarea secundaria (es decir, toda tarea interna anidada creada por su continuación) que se cree con la opción <see cref="F:System.Threading.Tasks.TaskCreationOptions.AttachedToParent" /> e intente ejecutarse como una tarea secundaria asociada no pueda adjuntar la tarea primaria y se ejecute como una tarea secundaria desasociada.Para obtener más información, vea Tareas secundarias asociadas y desasociadas.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously">
      <summary>Especifica que la tarea de continuación debe ejecutarse sincrónicamente.Si se especifica esta opción, la continuación se ejecuta en el mismo subproceso que causa la transición de la tarea anterior a su estado final.Si la tarea anterior ya se completó al crear la continuación, esta se ejecutará en el subproceso que crea la continuación.Si se desecha <see cref="T:System.Threading.CancellationTokenSource" /> del antecedente en un bloque finally (Finally en Visual Basic), se ejecutará una continuación con esta opción en ese bloque finally.Solo deben ejecutarse sincrónicamente las continuaciones de duración muy breve.Dado que la tarea se ejecuta de forma sincrónica, no es necesario llamar a un método como <see cref="M:System.Threading.Tasks.Task.Wait" /> para asegurarse de que el subproceso llamado espera a que se complete la tarea. </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.HideScheduler">
      <summary>Especifica que las tareas creadas por la continuación llamando a métodos como <see cref="M:System.Threading.Tasks.Task.Run(System.Action)" /> o <see cref="M:System.Threading.Tasks.Task.ContinueWith(System.Action{System.Threading.Tasks.Task})" /> vean el programador predeterminado (<see cref="P:System.Threading.Tasks.TaskScheduler.Default" />) en lugar del programador en el que esta continuación se ejecuta para ser el programador actual.  </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.LazyCancellation">
      <summary>En el caso de cancelación de continuación, evita la finalización de la continuación hasta que el antecedente se haya completado.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.LongRunning">
      <summary>Especifica que una continuación será una operación general de larga duración.Proporciona una sugerencia al <see cref="T:System.Threading.Tasks.TaskScheduler" /> de que se puede garantizar la sobresuscripción.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.None">
      <summary>Cuando no se indica ninguna opción de continuación, especifica que se debe usar el comportamiento predeterminado cuando se ejecuta una continuación.La continuación se ejecuta de forma asincrónica cuando se complete la tarea anterior, independientemente valor de propiedad de <see cref="P:System.Threading.Tasks.Task.Status" /> final del antecedente.Si la continuación es una tarea secundaria, se crea como una tarea anidada desasociada.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.NotOnCanceled">
      <summary>Especifica que no se programe la tarea de continuación si se cancela la tarea anterior.Un antecedente se cancela si su propiedad <see cref="P:System.Threading.Tasks.Task.Status" /> al finalizar es <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />.Esta opción no es válida para las continuaciones de varias tareas.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.NotOnFaulted">
      <summary>Especifica que no se debe programar la tarea de continuación si su antecedente produjo una excepción no controlada.Un antecedente produce una excepción no controlada si su propiedad <see cref="P:System.Threading.Tasks.Task.Status" /> al finalizar es <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" />.Esta opción no es válida para las continuaciones de varias tareas.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.NotOnRanToCompletion">
      <summary>Especifica que no se debe programar la tarea de continuación si su antecedente se ejecuta completamente.Un antecedente se ejecuta por completo si su propiedad <see cref="P:System.Threading.Tasks.Task.Status" /> al finalizar es <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />.Esta opción no es válida para las continuaciones de varias tareas.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnCanceled">
      <summary>Especifica que la continuación debe programarse únicamente si se cancela su antecedente.Un antecedente se cancela si su propiedad <see cref="P:System.Threading.Tasks.Task.Status" /> al finalizar es <see cref="F:System.Threading.Tasks.TaskStatus.Canceled" />.Esta opción no es válida para las continuaciones de varias tareas.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnFaulted">
      <summary>Especifica que la tarea de continuación solo se debe programar si su antecedente produjo una excepción no controlada.Un antecedente produce una excepción no controlada si su propiedad <see cref="P:System.Threading.Tasks.Task.Status" /> al finalizar es <see cref="F:System.Threading.Tasks.TaskStatus.Faulted" />.La opción <see cref="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnFaulted" /> garantiza que la propiedad <see cref="P:System.Threading.Tasks.Task.Exception" /> del antecedente no es null.Puede usar esa propiedad para detectar la excepción y ver qué excepción provocó el error de la tarea.Si no tiene acceso a la propiedad <see cref="P:System.Threading.Tasks.Task.Exception" />, no se controla la excepción.Asimismo, si intenta tener acceso a la propiedad <see cref="P:System.Threading.Tasks.Task`1.Result" /> de una tarea cancelada o con errores, se producirá una nueva excepción.Esta opción no es válida para las continuaciones de varias tareas. </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.OnlyOnRanToCompletion">
      <summary>Especifica que la continuación debe programarse únicamente si su antecedente se ejecuta completamente.Un antecedente se ejecuta por completo si su propiedad <see cref="P:System.Threading.Tasks.Task.Status" /> al finalizar es <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion" />.Esta opción no es válida para las continuaciones de varias tareas.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.PreferFairness">
      <summary>Sugerencia a un objeto <see cref="T:System.Threading.Tasks.TaskScheduler" /> para que programe las tareas en el orden en el que se programaron, de manera que haya más probabilidades de que las tareas programadas antes se ejecuten en primer lugar y las tareas programadas más tarde se ejecuten después. </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskContinuationOptions.RunContinuationsAsynchronously">
      <summary>Especifica que la tarea de continuación debe ejecutarse asincrónicamente.Esta opción tiene precedencia sobre <see cref="F:System.Threading.Tasks.TaskContinuationOptions.ExecuteSynchronously" />.</summary>
    </member>
    <member name="T:System.Threading.Tasks.TaskCreationOptions">
      <summary>Especifica las marcas que controlan el comportamiento opcional de la creación y ejecución de tareas. </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.AttachedToParent">
      <summary>Especifica que una tarea está adjunta a la tarea primaria en la jerarquía de tareas.De forma predeterminada, una tarea secundaria (es decir, una tarea interna creada por una tarea externa) se ejecuta de forma independiente de la tarea principal.La opción <see cref="F:System.Threading.Tasks.TaskContinuationOptions.AttachedToParent" /> permite sincronizar las tareas primarias y secundarias.Tenga en cuenta que, si una tarea primaria se configura con la opción <see cref="F:System.Threading.Tasks.TaskCreationOptions.DenyChildAttach" />, la opción <see cref="F:System.Threading.Tasks.TaskCreationOptions.AttachedToParent" /> no tiene ningún efecto en la tarea secundaria y ésta se ejecutará como una tarea secundaria desasociada. Para obtener más información, vea el artículo sobre tareas secundarias adjuntas y desasociadas. </summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.DenyChildAttach">
      <summary>Especifica que cualquier tarea secundaria que intente ejecutarse como una tarea secundaria asociada (es decir, creada con la opción <see cref="F:System.Threading.Tasks.TaskCreationOptions.AttachedToParent" />) no pueda adjuntar la tarea primaria y se ejecute como una tarea secundaria desasociada.Para más información, vea Tareas secundarias asociadas y desasociadas.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.HideScheduler">
      <summary>Impide que el programador de ambiente se vea como programador actual en la tarea creada.Esto significa que las operaciones como StartNew o ContinueWith que se realizan en la tarea creada verán <see cref="P:System.Threading.Tasks.TaskScheduler.Default" /> como programador actual.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.LongRunning">
      <summary>Especifica que una tarea será una operación general de larga duración que implica menos componentes mayores que los sistemas concretos.Proporciona una sugerencia al <see cref="T:System.Threading.Tasks.TaskScheduler" /> de que se puede garantizar la sobresuscripción.La sobresuscripción le permite crear más subprocesos que el número de subprocesos de hardware disponibles.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.None">
      <summary>Especifica que debe usarse el comportamiento predeterminado.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.PreferFairness">
      <summary>Sugerencia a un objeto <see cref="T:System.Threading.Tasks.TaskScheduler" /> para que programe las tareas de la manera más justa posible, lo cual significa que las tareas programadas antes probablemente se ejecuten en primer lugar y las tareas programadas más tarde se ejecuten después.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskCreationOptions.RunContinuationsAsynchronously">
      <summary>Fuerza la ejecución asincrónica de las continuaciones agregadas a la tarea actual. </summary>
    </member>
    <member name="T:System.Threading.Tasks.TaskExtensions">
      <summary>Proporciona un conjunto de métodos estáticos (Shared en Visual Basic) para que funcionen con determinados tipos de instancias de <see cref="T:System.Threading.Tasks.Task" />.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskExtensions.Unwrap``1(System.Threading.Tasks.Task{System.Threading.Tasks.Task{``0}})">
      <summary>Crea una <see cref="T:System.Threading.Tasks.Task" /> de proxy que representa la operación asincrónica de Task&lt;Task&lt;T&gt;&gt; (C#) o Task (Of Task(Of T)) (Visual Basic).</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task" /> que representa la operación asincrónica del Task&lt;Task&lt;T&gt;&gt; (C#) o Task (Of Task(Of T)) (Visual Basic) proporcionado.</returns>
      <param name="task">Task&lt;Task&lt;T&gt;&gt; (C#) o Task (Of Task(Of T)) (Visual Basic) que se va a desempaquetar.</param>
      <typeparam name="TResult">Tipo del resultado de la tarea.</typeparam>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce si el argumento <paramref name="task" /> es NULL.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskExtensions.Unwrap(System.Threading.Tasks.Task{System.Threading.Tasks.Task})">
      <summary>Crea un proxy <see cref="T:System.Threading.Tasks.Task" /> que representa el funcionamiento asincrónico de <see cref="M:System.Threading.Tasks.TaskScheduler.TryExecuteTaskInline(System.Threading.Tasks.Task,System.Boolean)" />.</summary>
      <returns>Tarea que representa la operación asincrónica del objeto System.Threading.Tasks.Task(Of Task) proporcionado.</returns>
      <param name="task">Task&lt;Task&gt; (C#) o Task (Of Task) (Visual Basic) que se va a desempaquetar.</param>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce si el argumento <paramref name="task" /> es NULL.</exception>
    </member>
    <member name="T:System.Threading.Tasks.TaskFactory">
      <summary>Proporciona compatibilidad para crear y programar objetos <see cref="T:System.Threading.Tasks.Task" />. </summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.#ctor">
      <summary>Inicializa una instancia de <see cref="T:System.Threading.Tasks.TaskFactory" /> con la configuración predeterminada.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.#ctor(System.Threading.CancellationToken)">
      <summary>Inicializa una instancia de <see cref="T:System.Threading.Tasks.TaskFactory" /> con la configuración especificada.</summary>
      <param name="cancellationToken">
        <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> que se asignará a las tareas creadas por este <see cref="T:System.Threading.Tasks.TaskFactory" /> a menos que se especifique explícitamente otro CancellationToken mientras se llama a los métodos del generador.</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.#ctor(System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Inicializa una instancia de <see cref="T:System.Threading.Tasks.TaskFactory" /> con la configuración especificada.</summary>
      <param name="cancellationToken">
        <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> predeterminado que se asignará a las tareas creadas por este <see cref="T:System.Threading.Tasks.TaskFactory" /> a menos que se especifique explícitamente otro CancellationToken mientras se llama a los métodos del generador.</param>
      <param name="creationOptions">Objeto <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> predeterminado que se va a usar al crear tareas con TaskFactory.</param>
      <param name="continuationOptions">Objeto <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> predeterminado que se va a usar al crear tareas de continuación con TaskFactory.</param>
      <param name="scheduler">Objeto <see cref="T:System.Threading.Tasks.TaskScheduler" /> predeterminado que se va a usar para programar las tareas creadas con TaskFactory.Un valor null indica que se debería emplear TaskScheduler.Current.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">El <paramref name="creationOptions" /> no válido que especifica el argumento <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> valor.Para obtener más información, vea la sección Comentarios para <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.o bienEl <paramref name="continuationOptions" /> argumento especifica un valor no válido.  </exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.#ctor(System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Inicializa una instancia de <see cref="T:System.Threading.Tasks.TaskFactory" /> con la configuración especificada.</summary>
      <param name="creationOptions">Objeto <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> predeterminado que se va a usar al crear tareas con TaskFactory.</param>
      <param name="continuationOptions">Objeto <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> predeterminado que se va a usar al crear tareas de continuación con TaskFactory.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">El <paramref name="creationOptions" /> no válido que especifica el argumento <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> valor.Para obtener más información, vea la sección Comentarios para <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" />.o bienEl <paramref name="continuationOptions" /> argumento especifica un valor no válido.  </exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.#ctor(System.Threading.Tasks.TaskScheduler)">
      <summary>Inicializa una instancia de <see cref="T:System.Threading.Tasks.TaskFactory" /> con la configuración especificada.</summary>
      <param name="scheduler">Objeto <see cref="T:System.Threading.Tasks.TaskScheduler" /> predeterminado que se va a usar para programar las tareas creadas con TaskFactory.Un valor null indica que se debería emplear el objeto TaskScheduler actual.</param>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory.CancellationToken">
      <summary>Obtiene el token de cancelación predeterminado para este generador de tareas.</summary>
      <returns>Token de cancelación de tareas predeterminado para este generador de tareas.</returns>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory.ContinuationOptions">
      <summary>Obtiene las opciones predeterminadas de continuación de tareas para este generador de tareas.</summary>
      <returns>Opciones predeterminadas de continuación de tareas para este generador de tareas.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task[]})">
      <summary>Crea una tarea de continuación que comienza cuando un conjunto de tareas especificadas se ha completado. </summary>
      <returns>Nueva tarea de continuación.</returns>
      <param name="tasks">Matriz de tareas desde la que se va a continuar.</param>
      <param name="continuationAction">Delegado de acción que se va a ejecutar cuando se completen todas las tareas de la matriz <paramref name="tasks" />.</param>
      <exception cref="T:System.ObjectDisposedException">Un elemento en el <paramref name="tasks" /> se ha eliminado la matriz.</exception>
      <exception cref="T:System.ArgumentNullException">El <paramref name="tasks" /> matriz es null.o bienEl argumento <paramref name="continuationAction" /> es null.</exception>
      <exception cref="T:System.ArgumentException">El <paramref name="tasks" /> matriz está vacío o contiene un valor null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task[]},System.Threading.CancellationToken)">
      <summary>Crea una tarea de continuación que comienza cuando un conjunto de tareas especificadas se ha completado.</summary>
      <returns>Nueva tarea de continuación.</returns>
      <param name="tasks">Matriz de tareas desde la que se va a continuar.</param>
      <param name="continuationAction">Delegado de acción que se va a ejecutar cuando se completen todas las tareas de la matriz <paramref name="tasks" />.</param>
      <param name="cancellationToken">Token de cancelación que se va a asignar a la nueva tarea de continuación.</param>
      <exception cref="T:System.ObjectDisposedException">Un elemento en el <paramref name="tasks" /> se ha eliminado la matriz.o bienEl <see cref="T:System.Threading.CancellationTokenSource" /> que creó <paramref name="cancellationToken" /> ya se ha eliminado.</exception>
      <exception cref="T:System.ArgumentNullException">El <paramref name="tasks" /> matriz es null.o bienEl argumento <paramref name="continuationAction" /> es null.</exception>
      <exception cref="T:System.ArgumentException">El <paramref name="tasks" /> matriz está vacío o contiene un valor null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task[]},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea una tarea de continuación que comienza cuando un conjunto de tareas especificadas se ha completado.</summary>
      <returns>Nueva tarea de continuación.</returns>
      <param name="tasks">Matriz de tareas desde la que se va a continuar.</param>
      <param name="continuationAction">Delegado de acción que se va a ejecutar cuando se completen todas las tareas de la matriz <paramref name="tasks" />.</param>
      <param name="cancellationToken">Token de cancelación que se va a asignar a la nueva tarea de continuación.</param>
      <param name="continuationOptions">Combinación bit a bit de los valores de enumeración que controlan el comportamiento de la nueva tarea de continuación.</param>
      <param name="scheduler">Objeto que se usa para programar la nueva tarea de continuación.</param>
      <exception cref="T:System.ArgumentNullException">El <paramref name="tasks" /> matriz es null.o bienEl argumento <paramref name="continuationAction" /> es null.o bienEl argumento <paramref name="scheduler" /> es null.</exception>
      <exception cref="T:System.ArgumentException">El <paramref name="tasks" /> matriz está vacío o contiene un valor null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task[]},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crea una tarea de continuación que comienza cuando un conjunto de tareas especificadas se ha completado.</summary>
      <returns>Nueva tarea de continuación.</returns>
      <param name="tasks">Matriz de tareas desde la que se va a continuar.</param>
      <param name="continuationAction">Delegado de acción que se va a ejecutar cuando se completen todas las tareas de la matriz <paramref name="tasks" />.</param>
      <param name="continuationOptions">Combinación bit a bit de los valores de enumeración que controlan el comportamiento de la nueva tarea de continuación.No se admiten los miembros NotOn* y OnlyOn*.</param>
      <exception cref="T:System.ObjectDisposedException">Un elemento en el <paramref name="tasks" /> se ha eliminado la matriz.</exception>
      <exception cref="T:System.ArgumentNullException">El <paramref name="tasks" /> matriz es null.o bienEl argumento <paramref name="continuationAction" /> es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El <paramref name="continuationOptions" /> argumento especifica un valor no válido. </exception>
      <exception cref="T:System.ArgumentException">El <paramref name="tasks" /> matriz está vacío o contiene un valor null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],``0})">
      <summary>Crea una tarea de continuación que comienza cuando un conjunto de tareas especificadas se ha completado.</summary>
      <returns>Nueva tarea de continuación.</returns>
      <param name="tasks">Matriz de tareas desde la que se va a continuar.</param>
      <param name="continuationFunction">Delegado de función que se va a ejecutar asincrónicamente cuando se completen todas las tareas de la matriz <paramref name="tasks" />.</param>
      <typeparam name="TResult">Tipo del resultado devuelto por el delegado de <paramref name="continuationFunction" /> y asociado a la tarea creada.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Un elemento en el <paramref name="tasks" /> se ha eliminado la matriz.</exception>
      <exception cref="T:System.ArgumentNullException">El <paramref name="tasks" /> matriz es null.o bienEl argumento <paramref name="continuationFunction" /> es null.</exception>
      <exception cref="T:System.ArgumentException">El <paramref name="tasks" /> matriz está vacío o contiene un valor null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],``0},System.Threading.CancellationToken)">
      <summary>Crea una tarea de continuación que comienza cuando un conjunto de tareas especificadas se ha completado.</summary>
      <returns>Nueva tarea de continuación.</returns>
      <param name="tasks">Matriz de tareas desde la que se va a continuar.</param>
      <param name="continuationFunction">Delegado de función que se va a ejecutar asincrónicamente cuando se completen todas las tareas de la matriz <paramref name="tasks" />.</param>
      <param name="cancellationToken">Token de cancelación que se va a asignar a la nueva tarea de continuación.</param>
      <typeparam name="TResult">Tipo del resultado devuelto por el delegado de <paramref name="continuationFunction" /> y asociado a la tarea creada.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Un elemento en el <paramref name="tasks" /> se ha eliminado la matriz.o bienEl <see cref="T:System.Threading.CancellationTokenSource" /> que creó <paramref name="cancellationToken" /> ya se ha eliminado.</exception>
      <exception cref="T:System.ArgumentNullException">El <paramref name="tasks" /> matriz es null.o bienEl argumento <paramref name="continuationFunction" /> es null.</exception>
      <exception cref="T:System.ArgumentException">El <paramref name="tasks" /> matriz está vacío o contiene un valor null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],``0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea una tarea de continuación que comienza cuando un conjunto de tareas especificadas se ha completado.</summary>
      <returns>Nueva tarea de continuación.</returns>
      <param name="tasks">Matriz de tareas desde la que se va a continuar.</param>
      <param name="continuationFunction">Delegado de función que se va a ejecutar asincrónicamente cuando se completen todas las tareas de la matriz <paramref name="tasks" />.</param>
      <param name="cancellationToken">Token de cancelación que se va a asignar a la nueva tarea de continuación.</param>
      <param name="continuationOptions">Combinación bit a bit de los valores de enumeración que controlan el comportamiento de la nueva tarea de continuación.No se admiten los miembros NotOn* y OnlyOn*.</param>
      <param name="scheduler">Objeto que se usa para programar la nueva tarea de continuación.</param>
      <typeparam name="TResult">Tipo del resultado devuelto por el delegado de <paramref name="continuationFunction" /> y asociado a la tarea creada.</typeparam>
      <exception cref="T:System.ArgumentNullException">El <paramref name="tasks" /> matriz es null.o bienEl argumento <paramref name="continuationFunction" /> es null.o bienEl argumento <paramref name="scheduler" /> es null.</exception>
      <exception cref="T:System.ArgumentException">El <paramref name="tasks" /> matriz está vacío o contiene un valor null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],``0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crea una tarea de continuación que comienza cuando un conjunto de tareas especificadas se ha completado.</summary>
      <returns>Nueva tarea de continuación.</returns>
      <param name="tasks">Matriz de tareas desde la que se va a continuar.</param>
      <param name="continuationFunction">Delegado de función que se va a ejecutar asincrónicamente cuando se completen todas las tareas de la matriz <paramref name="tasks" />.</param>
      <param name="continuationOptions">Combinación bit a bit de los valores de enumeración que controlan el comportamiento de la nueva tarea de continuación.No se admiten los miembros NotOn* y OnlyOn*.</param>
      <typeparam name="TResult">Tipo del resultado devuelto por el delegado de <paramref name="continuationFunction" /> y asociado a la tarea creada.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Un elemento en el <paramref name="tasks" /> se ha eliminado la matriz.</exception>
      <exception cref="T:System.ArgumentNullException">El <paramref name="tasks" /> matriz es null.o bienEl argumento <paramref name="continuationFunction" /> es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El <paramref name="continuationOptions" /> argumento especifica un valor no válido. </exception>
      <exception cref="T:System.ArgumentException">El <paramref name="tasks" /> matriz está vacío o contiene un valor null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}[]})">
      <summary>Crea una tarea de continuación que comienza cuando un conjunto de tareas especificadas se ha completado.</summary>
      <returns>Nueva tarea de continuación.</returns>
      <param name="tasks">Matriz de tareas desde la que se va a continuar.</param>
      <param name="continuationAction">Delegado de acción que se va a ejecutar cuando se completen todas las tareas de la matriz <paramref name="tasks" />.</param>
      <typeparam name="TAntecedentResult">Tipo del resultado del parámetro <paramref name="tasks" /> precedente.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Un elemento en el <paramref name="tasks" /> se ha eliminado la matriz.</exception>
      <exception cref="T:System.ArgumentNullException">El <paramref name="tasks" /> matriz es null.o bienEl argumento <paramref name="continuationAction" /> es null.</exception>
      <exception cref="T:System.ArgumentException">El <paramref name="tasks" /> matriz está vacío o contiene un valor null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}[]},System.Threading.CancellationToken)">
      <summary>Crea una tarea de continuación que comienza cuando un conjunto de tareas especificadas se ha completado.</summary>
      <returns>Nueva tarea de continuación.</returns>
      <param name="tasks">Matriz de tareas desde la que se va a continuar.</param>
      <param name="continuationAction">Delegado de acción que se va a ejecutar cuando se completen todas las tareas de la matriz <paramref name="tasks" />.</param>
      <param name="cancellationToken">Token de cancelación que se va a asignar a la nueva tarea de continuación.</param>
      <typeparam name="TAntecedentResult">Tipo del resultado del parámetro <paramref name="tasks" /> precedente.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Un elemento en el <paramref name="tasks" /> se ha eliminado la matriz.o bienEl <see cref="T:System.Threading.CancellationTokenSource" /> que creó <paramref name="cancellationToken" /> ya se ha eliminado.</exception>
      <exception cref="T:System.ArgumentNullException">El <paramref name="tasks" /> matriz es null.o bienEl argumento <paramref name="continuationAction" /> es null.</exception>
      <exception cref="T:System.ArgumentException">El <paramref name="tasks" /> matriz está vacío o contiene un valor null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}[]},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea una tarea de continuación que comienza cuando un conjunto de tareas especificadas se ha completado.</summary>
      <returns>Nueva tarea de continuación.</returns>
      <param name="tasks">Matriz de tareas desde la que se va a continuar.</param>
      <param name="continuationAction">Delegado de acción que se va a ejecutar cuando se completen todas las tareas de la matriz <paramref name="tasks" />.</param>
      <param name="cancellationToken">Token de cancelación que se va a asignar a la nueva tarea de continuación.</param>
      <param name="continuationOptions">Combinación bit a bit de los valores de enumeración que controlan el comportamiento de la nueva tarea de continuación.No se admiten los miembros NotOn* y OnlyOn*.</param>
      <param name="scheduler">Objeto que se usa para programar la nueva tarea de continuación.</param>
      <typeparam name="TAntecedentResult">Tipo del resultado del parámetro <paramref name="tasks" /> precedente.</typeparam>
      <exception cref="T:System.ArgumentNullException">El <paramref name="tasks" /> matriz es null.o bienEl argumento <paramref name="continuationAction" /> es null.o bienEl argumento <paramref name="scheduler" /> es null.</exception>
      <exception cref="T:System.ArgumentException">El <paramref name="tasks" /> matriz está vacío o contiene un valor null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}[]},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crea una tarea de continuación que comienza cuando un conjunto de tareas especificadas se ha completado.</summary>
      <returns>Nueva tarea de continuación.</returns>
      <param name="tasks">Matriz de tareas desde la que se va a continuar.</param>
      <param name="continuationAction">Delegado de acción que se va a ejecutar cuando se completen todas las tareas de la matriz <paramref name="tasks" />.</param>
      <param name="continuationOptions">Combinación bit a bit de los valores de enumeración que controlan el comportamiento de la nueva tarea de continuación.No se admiten los miembros NotOn* y OnlyOn*.</param>
      <typeparam name="TAntecedentResult">Tipo del resultado del parámetro <paramref name="tasks" /> precedente.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Un elemento en el <paramref name="tasks" /> se ha eliminado la matriz.</exception>
      <exception cref="T:System.ArgumentNullException">El <paramref name="tasks" /> matriz es null.o bienEl argumento <paramref name="continuationAction" /> es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El <paramref name="continuationOptions" /> argumento especifica un valor no válido. </exception>
      <exception cref="T:System.ArgumentException">El <paramref name="tasks" /> matriz está vacío o contiene un valor null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],``1})">
      <summary>Crea una tarea de continuación que comienza cuando un conjunto de tareas especificadas se ha completado.</summary>
      <returns>Nueva tarea de continuación.</returns>
      <param name="tasks">Matriz de tareas desde la que se va a continuar.</param>
      <param name="continuationFunction">Delegado de función que se va a ejecutar asincrónicamente cuando se completen todas las tareas de la matriz <paramref name="tasks" />.</param>
      <typeparam name="TAntecedentResult">Tipo del resultado del parámetro <paramref name="tasks" /> precedente.</typeparam>
      <typeparam name="TResult">Tipo del resultado devuelto por el delegado de <paramref name="continuationFunction" /> y asociado a la tarea creada.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Un elemento en el <paramref name="tasks" /> se ha eliminado la matriz.</exception>
      <exception cref="T:System.ArgumentNullException">El <paramref name="tasks" /> matriz es null.o bienEl argumento <paramref name="continuationFunction" /> es null.</exception>
      <exception cref="T:System.ArgumentException">El <paramref name="tasks" /> matriz está vacío o contiene un valor null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],``1},System.Threading.CancellationToken)">
      <summary>Crea una tarea de continuación que comienza cuando un conjunto de tareas especificadas se ha completado.</summary>
      <returns>Nueva tarea de continuación.</returns>
      <param name="tasks">Matriz de tareas desde la que se va a continuar.</param>
      <param name="continuationFunction">Delegado de función que se va a ejecutar asincrónicamente cuando se completen todas las tareas de la matriz <paramref name="tasks" />.</param>
      <param name="cancellationToken">Token de cancelación que se va a asignar a la nueva tarea de continuación.</param>
      <typeparam name="TAntecedentResult">Tipo del resultado del parámetro <paramref name="tasks" /> precedente.</typeparam>
      <typeparam name="TResult">Tipo del resultado devuelto por el delegado de <paramref name="continuationFunction" /> y asociado a la tarea creada.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Un elemento en el <paramref name="tasks" /> se ha eliminado la matriz.o bienEl <see cref="T:System.Threading.CancellationTokenSource" /> que creó<paramref name=" cancellationToken" /> ya se ha eliminado.</exception>
      <exception cref="T:System.ArgumentNullException">El <paramref name="tasks" /> matriz es null.o bienEl argumento <paramref name="continuationFunction" /> es null.</exception>
      <exception cref="T:System.ArgumentException">El <paramref name="tasks" /> matriz está vacío o contiene un valor null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],``1},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea una tarea de continuación que comienza cuando un conjunto de tareas especificadas se ha completado.</summary>
      <returns>Nueva tarea de continuación.</returns>
      <param name="tasks">Matriz de tareas desde la que se va a continuar.</param>
      <param name="continuationFunction">Delegado de función que se va a ejecutar asincrónicamente cuando se completen todas las tareas de la matriz <paramref name="tasks" />.</param>
      <param name="cancellationToken">Token de cancelación que se va a asignar a la nueva tarea de continuación.</param>
      <param name="continuationOptions">Combinación bit a bit de los valores de enumeración que controlan el comportamiento de la nueva tarea de continuación.No se admiten los miembros NotOn* y OnlyOn*.</param>
      <param name="scheduler">Objeto que se usa para programar la nueva tarea de continuación.</param>
      <typeparam name="TAntecedentResult">Tipo del resultado del parámetro <paramref name="tasks" /> precedente.</typeparam>
      <typeparam name="TResult">Tipo del resultado devuelto por el delegado de <paramref name="continuationFunction" /> y asociado a la tarea creada.</typeparam>
      <exception cref="T:System.ArgumentNullException">El <paramref name="tasks" /> matriz es null.o bienEl argumento <paramref name="continuationFunction" /> es null.o bienEl argumento <paramref name="scheduler" /> es null.</exception>
      <exception cref="T:System.ArgumentException">El <paramref name="tasks" /> matriz está vacío o contiene un valor null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El <paramref name="continuationOptions" /> argumento especifica un valor no válido. </exception>
      <exception cref="T:System.ObjectDisposedException">Un elemento en el <paramref name="tasks" /> se ha eliminado la matriz.o bienEl <see cref="T:System.Threading.CancellationTokenSource" /> que creó <paramref name="cancellationToken" /> ya se ha eliminado.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAll``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],``1},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crea una tarea de continuación que comienza cuando un conjunto de tareas especificadas se ha completado.</summary>
      <returns>Nueva tarea de continuación.</returns>
      <param name="tasks">Matriz de tareas desde la que se va a continuar.</param>
      <param name="continuationFunction">Delegado de función que se va a ejecutar asincrónicamente cuando se completen todas las tareas de la matriz <paramref name="tasks" />.</param>
      <param name="continuationOptions">Combinación bit a bit de los valores de enumeración que controlan el comportamiento de la nueva tarea de continuación.No se admiten los miembros NotOn* y OnlyOn*.</param>
      <typeparam name="TAntecedentResult">Tipo del resultado del parámetro <paramref name="tasks" /> precedente.</typeparam>
      <typeparam name="TResult">Tipo del resultado devuelto por el delegado de <paramref name="continuationFunction" /> y asociado a la tarea creada.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Un elemento en el <paramref name="tasks" /> se ha eliminado la matriz.</exception>
      <exception cref="T:System.ArgumentNullException">El <paramref name="tasks" /> matriz es null.o bienEl argumento <paramref name="continuationFunction" /> es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El <paramref name="continuationOptions" /> argumento especifica un valor no válido.</exception>
      <exception cref="T:System.ArgumentException">El <paramref name="tasks" /> matriz está vacío o contiene un valor null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task})">
      <summary>Crea un objeto <see cref="T:System.Threading.Tasks.Task" /> de continuación que se iniciará cuando se complete cualquier tarea del conjunto proporcionado.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task" /> de continuación.</returns>
      <param name="tasks">Matriz de tareas desde la que se va a continuar cuando se complete una tarea.</param>
      <param name="continuationAction">Delegado de acción que se va a ejecutar cuando se complete una tarea de la matriz <paramref name="tasks" />.</param>
      <exception cref="T:System.ObjectDisposedException">Uno de los elementos en el <paramref name="tasks" /> se ha eliminado la matriz. </exception>
      <exception cref="T:System.ArgumentNullException">El <paramref name="tasks" /> matriz es null. o bienEl el <paramref name="continuationAction" /> argumento es null. </exception>
      <exception cref="T:System.ArgumentException">El <paramref name="tasks" /> matriz contiene un null valor. o bienEl <paramref name="tasks" /> matriz está vacía.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task},System.Threading.CancellationToken)">
      <summary>Crea un objeto <see cref="T:System.Threading.Tasks.Task" /> de continuación que se iniciará cuando se complete cualquier tarea del conjunto proporcionado.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task" /> de continuación.</returns>
      <param name="tasks">Matriz de tareas desde la que se va a continuar cuando se complete una tarea.</param>
      <param name="continuationAction">Delegado de acción que se va a ejecutar cuando se complete una tarea de la matriz <paramref name="tasks" />.</param>
      <param name="cancellationToken">El <see cref="T:System.Threading.CancellationToken" /> que se asignará a la nueva tarea de continuación.</param>
      <exception cref="T:System.ObjectDisposedException">Uno de los elementos en el <paramref name="tasks" /> se ha eliminado la matriz. o bien<paramref name="cancellationToken" /> ya se ha eliminado. </exception>
      <exception cref="T:System.ArgumentNullException">El <paramref name="tasks" /> matriz es null. o bienEl argumento <paramref name="continuationAction" /> es null. </exception>
      <exception cref="T:System.ArgumentException">El <paramref name="tasks" /> matriz contiene un null valor. o bienEl <paramref name="tasks" /> matriz está vacía.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea un objeto <see cref="T:System.Threading.Tasks.Task" /> de continuación que se iniciará cuando se complete cualquier tarea del conjunto proporcionado.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task" /> de continuación.</returns>
      <param name="tasks">Matriz de tareas desde la que se va a continuar cuando se complete una tarea.</param>
      <param name="continuationAction">Delegado de acción que se va a ejecutar cuando se complete una tarea de la matriz <paramref name="tasks" />.</param>
      <param name="cancellationToken">El <see cref="T:System.Threading.CancellationToken" /> que se asignará a la nueva tarea de continuación.</param>
      <param name="continuationOptions">Valor de <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> que controla el comportamiento del objeto <see cref="T:System.Threading.Tasks.Task" /> de continuación creado.</param>
      <param name="scheduler">Objeto <see cref="T:System.Threading.Tasks.TaskScheduler" /> que se usa para programar el objeto <see cref="T:System.Threading.Tasks.Task" /> de continuación creado.</param>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce cuando el <paramref name="tasks" /> matriz es null.o bienLa excepción que se produce cuando la <paramref name="continuationAction" /> el argumento es null.o bienLa excepción que se produce cuando la <paramref name="scheduler" /> el argumento es null.</exception>
      <exception cref="T:System.ArgumentException">La excepción que se produce cuando el <paramref name="tasks" /> matriz contiene un valor null.o bienLa excepción que se produce cuando el <paramref name="tasks" /> matriz está vacía.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny(System.Threading.Tasks.Task[],System.Action{System.Threading.Tasks.Task},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crea un objeto <see cref="T:System.Threading.Tasks.Task" /> de continuación que se iniciará cuando se complete cualquier tarea del conjunto proporcionado.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task" /> de continuación.</returns>
      <param name="tasks">Matriz de tareas desde la que se va a continuar cuando se complete una tarea.</param>
      <param name="continuationAction">Delegado de acción que se va a ejecutar cuando se complete una tarea de la matriz <paramref name="tasks" />.</param>
      <param name="continuationOptions">Valor de <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> que controla el comportamiento del objeto <see cref="T:System.Threading.Tasks.Task" /> de continuación creado.</param>
      <exception cref="T:System.ObjectDisposedException">La excepción que se produce cuando uno de los elementos en el <paramref name="tasks" /> se ha eliminado la matriz.</exception>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce cuando el <paramref name="tasks" /> matriz es null.o bienLa excepción que se produce cuando la <paramref name="continuationAction" /> el argumento es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">La excepción que se produce cuando el <paramref name="continuationOptions" /> argumento especifica un valor TaskContinuationOptions no válido.</exception>
      <exception cref="T:System.ArgumentException">La excepción que se produce cuando el <paramref name="tasks" /> matriz contiene un valor null.o bienLa excepción que se produce cuando el <paramref name="tasks" /> matriz está vacía.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,``0})">
      <summary>Crea un objeto <see cref="T:System.Threading.Tasks.Task`1" /> de continuación que se iniciará cuando se complete cualquier tarea del conjunto proporcionado.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task`1" /> de continuación.</returns>
      <param name="tasks">Matriz de tareas desde la que se va a continuar cuando se complete una tarea.</param>
      <param name="continuationFunction">Delegado de función que se va a ejecutar asincrónicamente cuando se complete una tarea de la matriz <paramref name="tasks" />.</param>
      <typeparam name="TResult">Tipo del resultado devuelto por el delegado de <paramref name="continuationFunction" /> y asociado al objeto <see cref="T:System.Threading.Tasks.Task`1" /> creado.</typeparam>
      <exception cref="T:System.ObjectDisposedException">La excepción que se produce cuando uno de los elementos en el <paramref name="tasks" /> se ha eliminado la matriz.</exception>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce cuando el <paramref name="tasks" /> matriz es null.o bienLa excepción que se produce cuando la <paramref name="continuationFunction" /> el argumento es null.</exception>
      <exception cref="T:System.ArgumentException">La excepción que se produce cuando el <paramref name="tasks" /> matriz contiene un valor null.o bienLa excepción que se produce cuando el <paramref name="tasks" /> matriz está vacía.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,``0},System.Threading.CancellationToken)">
      <summary>Crea un objeto <see cref="T:System.Threading.Tasks.Task`1" /> de continuación que se iniciará cuando se complete cualquier tarea del conjunto proporcionado.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task`1" /> de continuación.</returns>
      <param name="tasks">Matriz de tareas desde la que se va a continuar cuando se complete una tarea.</param>
      <param name="continuationFunction">Delegado de función que se va a ejecutar asincrónicamente cuando se complete una tarea de la matriz <paramref name="tasks" />.</param>
      <param name="cancellationToken">El <see cref="T:System.Threading.CancellationToken" /> que se asignará a la nueva tarea de continuación.</param>
      <typeparam name="TResult">Tipo del resultado devuelto por el delegado de <paramref name="continuationFunction" /> y asociado al objeto <see cref="T:System.Threading.Tasks.Task`1" /> creado.</typeparam>
      <exception cref="T:System.ObjectDisposedException">La excepción que se produce cuando uno de los elementos en el <paramref name="tasks" /> se ha eliminado la matriz.o bienEl <see cref="T:System.Threading.CancellationToken" /> ya se ha eliminado.</exception>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce cuando el <paramref name="tasks" /> matriz es null.o bienLa excepción que se produce cuando la <paramref name="continuationFunction" /> el argumento es null.</exception>
      <exception cref="T:System.ArgumentException">La excepción que se produce cuando el <paramref name="tasks" /> matriz contiene un valor null.o bienLa excepción que se produce cuando el <paramref name="tasks" /> matriz está vacía.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,``0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea un objeto <see cref="T:System.Threading.Tasks.Task`1" /> de continuación que se iniciará cuando se complete cualquier tarea del conjunto proporcionado.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task`1" /> de continuación.</returns>
      <param name="tasks">Matriz de tareas desde la que se va a continuar cuando se complete una tarea.</param>
      <param name="continuationFunction">Delegado de función que se va a ejecutar asincrónicamente cuando se complete una tarea de la matriz <paramref name="tasks" />.</param>
      <param name="cancellationToken">El <see cref="T:System.Threading.CancellationToken" /> que se asignará a la nueva tarea de continuación.</param>
      <param name="continuationOptions">Valor de <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> que controla el comportamiento del objeto <see cref="T:System.Threading.Tasks.Task`1" /> de continuación creado.</param>
      <param name="scheduler">Objeto <see cref="T:System.Threading.Tasks.TaskScheduler" /> que se usa para programar el objeto <see cref="T:System.Threading.Tasks.Task`1" /> de continuación creado.</param>
      <typeparam name="TResult">Tipo del resultado devuelto por el delegado de <paramref name="continuationFunction" /> y asociado al objeto <see cref="T:System.Threading.Tasks.Task`1" /> creado.</typeparam>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce cuando el <paramref name="tasks" /> matriz es null.o bienLa excepción que se produce cuando la <paramref name="continuationFunction" /> el argumento es null.o bienLa excepción que se produce cuando la <paramref name="scheduler" /> el argumento es null.</exception>
      <exception cref="T:System.ArgumentException">La excepción que se produce cuando el <paramref name="tasks" /> matriz contiene un valor null.o bienLa excepción que se produce cuando el <paramref name="tasks" /> matriz está vacía.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,``0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crea un objeto <see cref="T:System.Threading.Tasks.Task`1" /> de continuación que se iniciará cuando se complete cualquier tarea del conjunto proporcionado.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task`1" /> de continuación.</returns>
      <param name="tasks">Matriz de tareas desde la que se va a continuar cuando se complete una tarea.</param>
      <param name="continuationFunction">Delegado de función que se va a ejecutar asincrónicamente cuando se complete una tarea de la matriz <paramref name="tasks" />.</param>
      <param name="continuationOptions">Valor de <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> que controla el comportamiento del objeto <see cref="T:System.Threading.Tasks.Task`1" /> de continuación creado.</param>
      <typeparam name="TResult">Tipo del resultado devuelto por el delegado de <paramref name="continuationFunction" /> y asociado al objeto <see cref="T:System.Threading.Tasks.Task`1" /> creado.</typeparam>
      <exception cref="T:System.ObjectDisposedException">La excepción que se produce cuando uno de los elementos en el <paramref name="tasks" /> se ha eliminado la matriz.</exception>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce cuando el <paramref name="tasks" /> matriz es null.o bienLa excepción que se produce cuando la <paramref name="continuationFunction" /> el argumento es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">La excepción que se produce cuando el <paramref name="continuationOptions" /> argumento especifica un valor TaskContinuationOptions no válido.</exception>
      <exception cref="T:System.ArgumentException">La excepción que se produce cuando el <paramref name="tasks" /> matriz contiene un valor null.o bienLa excepción que se produce cuando el <paramref name="tasks" /> matriz está vacía.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}})">
      <summary>Crea un objeto <see cref="T:System.Threading.Tasks.Task" /> de continuación que se iniciará cuando se complete cualquier tarea del conjunto proporcionado.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task" /> de continuación.</returns>
      <param name="tasks">Matriz de tareas desde la que se va a continuar cuando se complete una tarea.</param>
      <param name="continuationAction">Delegado de acción que se va a ejecutar cuando se complete una tarea de la matriz <paramref name="tasks" />.</param>
      <typeparam name="TAntecedentResult">Tipo del resultado del parámetro <paramref name="tasks" /> precedente.</typeparam>
      <exception cref="T:System.ObjectDisposedException">La excepción que se produce cuando uno de los elementos en el <paramref name="tasks" /> se ha eliminado la matriz.</exception>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce cuando el <paramref name="tasks" /> matriz es null.o bienLa excepción que se produce cuando la <paramref name="continuationAction" /> el argumento es null.</exception>
      <exception cref="T:System.ArgumentException">La excepción que se produce cuando el <paramref name="tasks" /> matriz contiene un valor null.o bienLa excepción que se produce cuando el <paramref name="tasks" /> matriz está vacía.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}},System.Threading.CancellationToken)">
      <summary>Crea un objeto <see cref="T:System.Threading.Tasks.Task" /> de continuación que se iniciará cuando se complete cualquier tarea del conjunto proporcionado.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task" /> de continuación.</returns>
      <param name="tasks">Matriz de tareas desde la que se va a continuar cuando se complete una tarea.</param>
      <param name="continuationAction">Delegado de acción que se va a ejecutar cuando se complete una tarea de la matriz <paramref name="tasks" />.</param>
      <param name="cancellationToken">El <see cref="T:System.Threading.CancellationToken" /> que se asignará a la nueva tarea de continuación.</param>
      <typeparam name="TAntecedentResult">Tipo del resultado del parámetro <paramref name="tasks" /> precedente.</typeparam>
      <exception cref="T:System.ObjectDisposedException">La excepción que se produce cuando uno de los elementos en el <paramref name="tasks" /> se ha eliminado la matriz.o bienEl <see cref="T:System.Threading.CancellationToken" /> ya se ha eliminado.</exception>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce cuando el <paramref name="tasks" /> matriz es null.o bienLa excepción que se produce cuando la <paramref name="continuationAction" /> el argumento es null.</exception>
      <exception cref="T:System.ArgumentException">La excepción que se produce cuando el <paramref name="tasks" /> matriz contiene un valor null.o bienLa excepción que se produce cuando el <paramref name="tasks" /> matriz está vacía.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea un objeto <see cref="T:System.Threading.Tasks.Task" /> de continuación que se iniciará cuando se complete cualquier tarea del conjunto proporcionado.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task" /> de continuación.</returns>
      <param name="tasks">Matriz de tareas desde la que se va a continuar cuando se complete una tarea.</param>
      <param name="continuationAction">Delegado de acción que se va a ejecutar cuando se complete una tarea de la matriz <paramref name="tasks" />.</param>
      <param name="cancellationToken">El <see cref="T:System.Threading.CancellationToken" /> que se asignará a la nueva tarea de continuación.</param>
      <param name="continuationOptions">Valor de <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> que controla el comportamiento del objeto <see cref="T:System.Threading.Tasks.Task" /> de continuación creado.</param>
      <param name="scheduler">Objeto <see cref="T:System.Threading.Tasks.TaskScheduler" /> que se usa para programar el objeto <see cref="T:System.Threading.Tasks.Task`1" /> de continuación creado.</param>
      <typeparam name="TAntecedentResult">Tipo del resultado del parámetro <paramref name="tasks" /> precedente.</typeparam>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce cuando el <paramref name="tasks" /> matriz es null.o bienLa excepción que se produce cuando la <paramref name="continuationAction" /> el argumento es null.o bienLa excepción que se produce cuando la <paramref name="scheduler" /> el argumento es null.</exception>
      <exception cref="T:System.ArgumentException">La excepción que se produce cuando el <paramref name="tasks" /> matriz contiene un valor null.o bienLa excepción que se produce cuando el <paramref name="tasks" /> matriz está vacía.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Action{System.Threading.Tasks.Task{``0}},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crea un objeto <see cref="T:System.Threading.Tasks.Task" /> de continuación que se iniciará cuando se complete cualquier tarea del conjunto proporcionado.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task" /> de continuación.</returns>
      <param name="tasks">Matriz de tareas desde la que se va a continuar cuando se complete una tarea.</param>
      <param name="continuationAction">Delegado de acción que se va a ejecutar cuando se complete una tarea de la matriz <paramref name="tasks" />.</param>
      <param name="continuationOptions">Valor de <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> que controla el comportamiento del objeto <see cref="T:System.Threading.Tasks.Task" /> de continuación creado.</param>
      <typeparam name="TAntecedentResult">Tipo del resultado del parámetro <paramref name="tasks" /> precedente.</typeparam>
      <exception cref="T:System.ObjectDisposedException">La excepción que se produce cuando uno de los elementos en el <paramref name="tasks" /> se ha eliminado la matriz.</exception>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce cuando el <paramref name="tasks" /> matriz es null.o bienLa excepción que se produce cuando la <paramref name="continuationAction" /> el argumento es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">La excepción que se produce cuando el <paramref name="continuationOptions" /> argumento especifica un valor TaskContinuationOptions no válido.</exception>
      <exception cref="T:System.ArgumentException">La excepción que se produce cuando el <paramref name="tasks" /> matriz contiene un valor null.o bienLa excepción que se produce cuando el <paramref name="tasks" /> matriz está vacía.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},``1})">
      <summary>Crea un objeto <see cref="T:System.Threading.Tasks.Task`1" /> de continuación que se iniciará cuando se complete cualquier tarea del conjunto proporcionado.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task`1" /> de continuación.</returns>
      <param name="tasks">Matriz de tareas desde la que se va a continuar cuando se complete una tarea.</param>
      <param name="continuationFunction">Delegado de función que se va a ejecutar asincrónicamente cuando se complete una tarea de la matriz <paramref name="tasks" />.</param>
      <typeparam name="TAntecedentResult">Tipo del resultado del parámetro <paramref name="tasks" /> precedente.</typeparam>
      <typeparam name="TResult">Tipo del resultado devuelto por el delegado de <paramref name="continuationFunction" /> y asociado al objeto <see cref="T:System.Threading.Tasks.Task`1" /> creado.</typeparam>
      <exception cref="T:System.ObjectDisposedException">La excepción que se produce cuando uno de los elementos en el <paramref name="tasks" /> se ha eliminado la matriz.</exception>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce cuando el <paramref name="tasks" /> matriz es null.o bienLa excepción que se produce cuando la <paramref name="continuationFunction" /> el argumento es null.</exception>
      <exception cref="T:System.ArgumentException">La excepción que se produce cuando el <paramref name="tasks" /> matriz contiene un valor null.o bienLa excepción que se produce cuando el <paramref name="tasks" /> matriz está vacía.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},``1},System.Threading.CancellationToken)">
      <summary>Crea un objeto <see cref="T:System.Threading.Tasks.Task`1" /> de continuación que se iniciará cuando se complete cualquier tarea del conjunto proporcionado.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task`1" /> de continuación.</returns>
      <param name="tasks">Matriz de tareas desde la que se va a continuar cuando se complete una tarea.</param>
      <param name="continuationFunction">Delegado de función que se va a ejecutar asincrónicamente cuando se complete una tarea de la matriz <paramref name="tasks" />.</param>
      <param name="cancellationToken">El <see cref="T:System.Threading.CancellationToken" /> que se asignará a la nueva tarea de continuación.</param>
      <typeparam name="TAntecedentResult">Tipo del resultado del parámetro <paramref name="tasks" /> precedente.</typeparam>
      <typeparam name="TResult">Tipo del resultado devuelto por el delegado de <paramref name="continuationFunction" /> y asociado al objeto <see cref="T:System.Threading.Tasks.Task`1" /> creado.</typeparam>
      <exception cref="T:System.ObjectDisposedException">La excepción que se produce cuando uno de los elementos en el <paramref name="tasks" /> se ha eliminado la matriz.o bienEl <see cref="T:System.Threading.CancellationToken" /> ya se ha eliminado.</exception>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce cuando el <paramref name="tasks" /> matriz es null.o bienLa excepción que se produce cuando la <paramref name="continuationFunction" /> el argumento es null.</exception>
      <exception cref="T:System.ArgumentException">La excepción que se produce cuando el <paramref name="tasks" /> matriz contiene un valor null.o bienLa excepción que se produce cuando el <paramref name="tasks" /> matriz está vacía.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},``1},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea un objeto <see cref="T:System.Threading.Tasks.Task`1" /> de continuación que se iniciará cuando se complete cualquier tarea del conjunto proporcionado.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task`1" /> de continuación.</returns>
      <param name="tasks">Matriz de tareas desde la que se va a continuar cuando se complete una tarea.</param>
      <param name="continuationFunction">Delegado de función que se va a ejecutar asincrónicamente cuando se complete una tarea de la matriz <paramref name="tasks" />.</param>
      <param name="cancellationToken">El <see cref="T:System.Threading.CancellationToken" /> que se asignará a la nueva tarea de continuación.</param>
      <param name="continuationOptions">Valor de <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> que controla el comportamiento del objeto <see cref="T:System.Threading.Tasks.Task`1" /> de continuación creado.</param>
      <param name="scheduler">Objeto <see cref="T:System.Threading.Tasks.TaskScheduler" /> que se usa para programar el objeto <see cref="T:System.Threading.Tasks.Task`1" /> de continuación creado.</param>
      <typeparam name="TAntecedentResult">Tipo del resultado del parámetro <paramref name="tasks" /> precedente.</typeparam>
      <typeparam name="TResult">Tipo del resultado devuelto por el delegado de <paramref name="continuationFunction" /> y asociado al objeto <see cref="T:System.Threading.Tasks.Task`1" /> creado.</typeparam>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce cuando el <paramref name="tasks" /> matriz es null.o bienLa excepción que se produce cuando la <paramref name="continuationFunction" /> el argumento es null.o bienLa excepción que se produce cuando la <paramref name="scheduler" /> el argumento es null.</exception>
      <exception cref="T:System.ArgumentException">La excepción que se produce cuando el <paramref name="tasks" /> matriz contiene un valor null.o bienLa excepción que se produce cuando el <paramref name="tasks" /> matriz está vacía.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.ContinueWhenAny``2(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},``1},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crea un objeto <see cref="T:System.Threading.Tasks.Task`1" /> de continuación que se iniciará cuando se complete cualquier tarea del conjunto proporcionado.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task`1" /> de continuación.</returns>
      <param name="tasks">Matriz de tareas desde la que se va a continuar cuando se complete una tarea.</param>
      <param name="continuationFunction">Delegado de función que se va a ejecutar asincrónicamente cuando se complete una tarea de la matriz <paramref name="tasks" />.</param>
      <param name="continuationOptions">Valor de <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> que controla el comportamiento del objeto <see cref="T:System.Threading.Tasks.Task`1" /> de continuación creado.</param>
      <typeparam name="TAntecedentResult">Tipo del resultado del parámetro <paramref name="tasks" /> precedente.</typeparam>
      <typeparam name="TResult">Tipo del resultado devuelto por el delegado de <paramref name="continuationFunction" /> y asociado al objeto <see cref="T:System.Threading.Tasks.Task`1" /> creado.</typeparam>
      <exception cref="T:System.ObjectDisposedException">La excepción que se produce cuando uno de los elementos en el <paramref name="tasks" /> se ha eliminado la matriz.</exception>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce cuando el <paramref name="tasks" /> matriz es null.o bienLa excepción que se produce cuando la <paramref name="continuationFunction" /> el argumento es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">La excepción que se produce cuando el <paramref name="continuationOptions" /> argumento especifica un valor TaskContinuationOptions no válido.</exception>
      <exception cref="T:System.ArgumentException">La excepción que se produce cuando el <paramref name="tasks" /> matriz contiene un valor null.o bienLa excepción que se produce cuando el <paramref name="tasks" /> matriz está vacía.</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory.CreationOptions">
      <summary>Obtiene las opciones predeterminadas de creación de tareas para este generador de tareas.</summary>
      <returns>Opciones predeterminadas de creación de tareas para este generador de tareas.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object)">
      <summary>Crea un objeto <see cref="T:System.Threading.Tasks.Task" /> que representa un par de métodos Begin y End que se ajustan al modelo de programación asincrónica.</summary>
      <returns>Objeto <see cref="T:System.Threading.Tasks.Task" /> creado que representa la operación asincrónica.</returns>
      <param name="beginMethod">Delegado que comienza la operación asincrónica.</param>
      <param name="endMethod">Delegado que finaliza la operación asincrónica.</param>
      <param name="state">Objeto que contiene los datos que el delegado de <paramref name="beginMethod" /> va a usar.</param>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce cuando la <paramref name="beginMethod" /> el argumento es null.o bienLa excepción que se produce cuando la <paramref name="endMethod" /> el argumento es null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crea un objeto <see cref="T:System.Threading.Tasks.Task" /> que representa un par de métodos Begin y End que se ajustan al modelo de programación asincrónica.</summary>
      <returns>Objeto <see cref="T:System.Threading.Tasks.Task" /> creado que representa la operación asincrónica.</returns>
      <param name="beginMethod">Delegado que comienza la operación asincrónica.</param>
      <param name="endMethod">Delegado que finaliza la operación asincrónica.</param>
      <param name="state">Objeto que contiene los datos que el delegado de <paramref name="beginMethod" /> va a usar.</param>
      <param name="creationOptions">Valor de TaskCreationOptions que controla el comportamiento del objeto <see cref="T:System.Threading.Tasks.Task" /> creado.</param>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce cuando la <paramref name="beginMethod" /> el argumento es null.o bienLa excepción que se produce cuando la <paramref name="endMethod" /> el argumento es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">La excepción que se produce cuando el <paramref name="creationOptions" /> argumento especifica un valor TaskCreationOptions no válido.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``0},System.Object)">
      <summary>Crea un objeto <see cref="T:System.Threading.Tasks.Task`1" /> que representa un par de métodos Begin y End que se ajustan al modelo de programación asincrónica.</summary>
      <returns>Objeto <see cref="T:System.Threading.Tasks.Task`1" /> creado que representa la operación asincrónica.</returns>
      <param name="beginMethod">Delegado que comienza la operación asincrónica.</param>
      <param name="endMethod">Delegado que finaliza la operación asincrónica.</param>
      <param name="state">Objeto que contiene los datos que el delegado de <paramref name="beginMethod" /> va a usar.</param>
      <typeparam name="TResult">Tipo del resultado que está disponible a través de <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce cuando la <paramref name="beginMethod" /> el argumento es null.o bienLa excepción que se produce cuando la <paramref name="endMethod" /> el argumento es null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``0},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crea un objeto <see cref="T:System.Threading.Tasks.Task`1" /> que representa un par de métodos Begin y End que se ajustan al modelo de programación asincrónica.</summary>
      <returns>Objeto <see cref="T:System.Threading.Tasks.Task`1" /> creado que representa la operación asincrónica.</returns>
      <param name="beginMethod">Delegado que comienza la operación asincrónica.</param>
      <param name="endMethod">Delegado que finaliza la operación asincrónica.</param>
      <param name="state">Objeto que contiene los datos que el delegado de <paramref name="beginMethod" /> va a usar.</param>
      <param name="creationOptions">Valor de TaskCreationOptions que controla el comportamiento del objeto <see cref="T:System.Threading.Tasks.Task`1" /> creado.</param>
      <typeparam name="TResult">Tipo del resultado que está disponible a través de <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce cuando la <paramref name="beginMethod" /> el argumento es null.o bienLa excepción que se produce cuando la <paramref name="endMethod" /> el argumento es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">La excepción que se produce cuando el <paramref name="creationOptions" /> argumento especifica un valor TaskCreationOptions no válido.La excepción que se produce cuando el <paramref name="creationOptions" /> argumento especifica un valor TaskCreationOptions no válido.Para obtener más información, vea la sección Comentarios para <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /></exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,System.Object)">
      <summary>Crea un objeto <see cref="T:System.Threading.Tasks.Task" /> que representa un par de métodos Begin y End que se ajustan al modelo de programación asincrónica.</summary>
      <returns>Objeto <see cref="T:System.Threading.Tasks.Task" /> creado que representa la operación asincrónica.</returns>
      <param name="beginMethod">Delegado que comienza la operación asincrónica.</param>
      <param name="endMethod">Delegado que finaliza la operación asincrónica.</param>
      <param name="arg1">Primer argumento que se pasa al delegado de <paramref name="beginMethod" />.</param>
      <param name="state">Objeto que contiene los datos que el delegado de <paramref name="beginMethod" /> va a usar.</param>
      <typeparam name="TArg1">Tipo del primer argumento que se pasa al delegado de <paramref name="beginMethod" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce cuando la <paramref name="beginMethod" /> el argumento es null.o bienLa excepción que se produce cuando la <paramref name="endMethod" /> el argumento es null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crea un objeto <see cref="T:System.Threading.Tasks.Task" /> que representa un par de métodos Begin y End que se ajustan al modelo de programación asincrónica.</summary>
      <returns>Objeto <see cref="T:System.Threading.Tasks.Task" /> creado que representa la operación asincrónica.</returns>
      <param name="beginMethod">Delegado que comienza la operación asincrónica.</param>
      <param name="endMethod">Delegado que finaliza la operación asincrónica.</param>
      <param name="arg1">Primer argumento que se pasa al delegado de <paramref name="beginMethod" />.</param>
      <param name="state">Objeto que contiene los datos que el delegado de <paramref name="beginMethod" /> va a usar.</param>
      <param name="creationOptions">Valor de TaskCreationOptions que controla el comportamiento del objeto <see cref="T:System.Threading.Tasks.Task" /> creado.</param>
      <typeparam name="TArg1">Tipo del primer argumento que se pasa al delegado de <paramref name="beginMethod" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce cuando la <paramref name="beginMethod" /> el argumento es null.o bienLa excepción que se produce cuando la <paramref name="endMethod" /> el argumento es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">La excepción que se produce cuando el <paramref name="creationOptions" /> argumento especifica un valor TaskCreationOptions no válido.La excepción que se produce cuando el <paramref name="creationOptions" /> argumento especifica un valor TaskCreationOptions no válido.Para obtener más información, vea la sección Comentarios para <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /></exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``2(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``1},``0,System.Object)">
      <summary>Crea un objeto <see cref="T:System.Threading.Tasks.Task`1" /> que representa un par de métodos Begin y End que se ajustan al modelo de programación asincrónica.</summary>
      <returns>Objeto <see cref="T:System.Threading.Tasks.Task`1" /> creado que representa la operación asincrónica.</returns>
      <param name="beginMethod">Delegado que comienza la operación asincrónica.</param>
      <param name="endMethod">Delegado que finaliza la operación asincrónica.</param>
      <param name="arg1">Primer argumento que se pasa al delegado de <paramref name="beginMethod" />.</param>
      <param name="state">Objeto que contiene los datos que el delegado de <paramref name="beginMethod" /> va a usar.</param>
      <typeparam name="TArg1">Tipo del primer argumento que se pasa al delegado de <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TResult">Tipo del resultado que está disponible a través de <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce cuando la <paramref name="beginMethod" /> el argumento es null.o bienLa excepción que se produce cuando la <paramref name="endMethod" /> el argumento es null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``2(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``1},``0,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crea un objeto <see cref="T:System.Threading.Tasks.Task`1" /> que representa un par de métodos Begin y End que se ajustan al modelo de programación asincrónica.</summary>
      <returns>Objeto <see cref="T:System.Threading.Tasks.Task`1" /> creado que representa la operación asincrónica.</returns>
      <param name="beginMethod">Delegado que comienza la operación asincrónica.</param>
      <param name="endMethod">Delegado que finaliza la operación asincrónica.</param>
      <param name="arg1">Primer argumento que se pasa al delegado de <paramref name="beginMethod" />.</param>
      <param name="state">Objeto que contiene los datos que el delegado de <paramref name="beginMethod" /> va a usar.</param>
      <param name="creationOptions">Valor de TaskCreationOptions que controla el comportamiento del objeto <see cref="T:System.Threading.Tasks.Task`1" /> creado.</param>
      <typeparam name="TArg1">Tipo del primer argumento que se pasa al delegado de <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TResult">Tipo del resultado que está disponible a través de <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce cuando la <paramref name="beginMethod" /> el argumento es null.o bienLa excepción que se produce cuando la <paramref name="endMethod" /> el argumento es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">La excepción que se produce cuando el <paramref name="creationOptions" /> argumento especifica un valor TaskCreationOptions no válido.La excepción que se produce cuando el <paramref name="creationOptions" /> argumento especifica un valor TaskCreationOptions no válido.Para obtener más información, vea la sección Comentarios para <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /></exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``2(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,``1,System.Object)">
      <summary>Crea un objeto <see cref="T:System.Threading.Tasks.Task" /> que representa un par de métodos Begin y End que se ajustan al modelo de programación asincrónica.</summary>
      <returns>Objeto <see cref="T:System.Threading.Tasks.Task" /> creado que representa la operación asincrónica.</returns>
      <param name="beginMethod">Delegado que comienza la operación asincrónica.</param>
      <param name="endMethod">Delegado que finaliza la operación asincrónica.</param>
      <param name="arg1">Primer argumento que se pasa al delegado de <paramref name="beginMethod" />.</param>
      <param name="arg2">Segundo argumento que se pasa al delegado de <paramref name="beginMethod" />.</param>
      <param name="state">Objeto que contiene los datos que el delegado de <paramref name="beginMethod" /> va a usar.</param>
      <typeparam name="TArg1">Tipo del segundo argumento que se pasa al delegado de <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg2">Tipo del primer argumento que se pasa al delegado de <paramref name="beginMethod" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce cuando la <paramref name="beginMethod" /> el argumento es null.o bienLa excepción que se produce cuando la <paramref name="endMethod" /> el argumento es null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``2(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,``1,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crea un objeto <see cref="T:System.Threading.Tasks.Task" /> que representa un par de métodos Begin y End que se ajustan al modelo de programación asincrónica.</summary>
      <returns>Objeto <see cref="T:System.Threading.Tasks.Task" /> creado que representa la operación asincrónica.</returns>
      <param name="beginMethod">Delegado que comienza la operación asincrónica.</param>
      <param name="endMethod">Delegado que finaliza la operación asincrónica.</param>
      <param name="arg1">Primer argumento que se pasa al delegado de <paramref name="beginMethod" />.</param>
      <param name="arg2">Segundo argumento que se pasa al delegado de <paramref name="beginMethod" />.</param>
      <param name="state">Objeto que contiene los datos que el delegado de <paramref name="beginMethod" /> va a usar.</param>
      <param name="creationOptions">Valor de TaskCreationOptions que controla el comportamiento del objeto <see cref="T:System.Threading.Tasks.Task" /> creado.</param>
      <typeparam name="TArg1">Tipo del segundo argumento que se pasa al delegado de <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg2">Tipo del primer argumento que se pasa al delegado de <paramref name="beginMethod" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce cuando la <paramref name="beginMethod" /> el argumento es null.o bienLa excepción que se produce cuando la <paramref name="endMethod" /> el argumento es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">La excepción que se produce cuando el <paramref name="creationOptions" /> argumento especifica un valor TaskCreationOptions no válido.La excepción que se produce cuando el <paramref name="creationOptions" /> argumento especifica un valor TaskCreationOptions no válido.Para obtener más información, vea la sección Comentarios para <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /></exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``3(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``2},``0,``1,System.Object)">
      <summary>Crea un objeto <see cref="T:System.Threading.Tasks.Task`1" /> que representa un par de métodos Begin y End que se ajustan al modelo de programación asincrónica.</summary>
      <returns>Objeto <see cref="T:System.Threading.Tasks.Task`1" /> creado que representa la operación asincrónica.</returns>
      <param name="beginMethod">Delegado que comienza la operación asincrónica.</param>
      <param name="endMethod">Delegado que finaliza la operación asincrónica.</param>
      <param name="arg1">Primer argumento que se pasa al delegado de <paramref name="beginMethod" />.</param>
      <param name="arg2">Segundo argumento que se pasa al delegado de <paramref name="beginMethod" />.</param>
      <param name="state">Objeto que contiene los datos que el delegado de <paramref name="beginMethod" /> va a usar.</param>
      <typeparam name="TArg1">Tipo del segundo argumento que se pasa al delegado de <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg2">Tipo del primer argumento que se pasa al delegado de <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TResult">Tipo del resultado que está disponible a través de <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce cuando la <paramref name="beginMethod" /> el argumento es null.o bienLa excepción que se produce cuando la <paramref name="endMethod" /> el argumento es null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``3(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``2},``0,``1,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crea un objeto <see cref="T:System.Threading.Tasks.Task`1" /> que representa un par de métodos Begin y End que se ajustan al modelo de programación asincrónica.</summary>
      <returns>Objeto <see cref="T:System.Threading.Tasks.Task`1" /> creado que representa la operación asincrónica.</returns>
      <param name="beginMethod">Delegado que comienza la operación asincrónica.</param>
      <param name="endMethod">Delegado que finaliza la operación asincrónica.</param>
      <param name="arg1">Primer argumento que se pasa al delegado de <paramref name="beginMethod" />.</param>
      <param name="arg2">Segundo argumento que se pasa al delegado de <paramref name="beginMethod" />.</param>
      <param name="state">Objeto que contiene los datos que el delegado de <paramref name="beginMethod" /> va a usar.</param>
      <param name="creationOptions">Valor de TaskCreationOptions que controla el comportamiento del objeto <see cref="T:System.Threading.Tasks.Task`1" /> creado.</param>
      <typeparam name="TArg1">Tipo del segundo argumento que se pasa al delegado de <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg2">Tipo del primer argumento que se pasa al delegado de <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TResult">Tipo del resultado que está disponible a través de <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce cuando la <paramref name="beginMethod" /> el argumento es null.o bienLa excepción que se produce cuando la <paramref name="endMethod" /> el argumento es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">La excepción que se produce cuando el <paramref name="creationOptions" /> argumento especifica un valor TaskCreationOptions no válido.La excepción que se produce cuando el <paramref name="creationOptions" /> argumento especifica un valor TaskCreationOptions no válido.Para obtener más información, vea la sección Comentarios para <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /></exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``3(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,``1,``2,System.Object)">
      <summary>Crea un objeto <see cref="T:System.Threading.Tasks.Task" /> que representa un par de métodos Begin y End que se ajustan al modelo de programación asincrónica.</summary>
      <returns>Objeto <see cref="T:System.Threading.Tasks.Task" /> creado que representa la operación asincrónica.</returns>
      <param name="beginMethod">Delegado que comienza la operación asincrónica.</param>
      <param name="endMethod">Delegado que finaliza la operación asincrónica.</param>
      <param name="arg1">Primer argumento que se pasa al delegado de <paramref name="beginMethod" />.</param>
      <param name="arg2">Segundo argumento que se pasa al delegado de <paramref name="beginMethod" />.</param>
      <param name="arg3">Tercer argumento que se pasa al delegado de <paramref name="beginMethod" />.</param>
      <param name="state">Objeto que contiene los datos que el delegado de <paramref name="beginMethod" /> va a usar.</param>
      <typeparam name="TArg1">Tipo del segundo argumento que se pasa al delegado de <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg2">Tipo del tercer argumento que se pasa al delegado de <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg3">Tipo del primer argumento que se pasa al delegado de <paramref name="beginMethod" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce cuando la <paramref name="beginMethod" /> el argumento es null.o bienLa excepción que se produce cuando la <paramref name="endMethod" /> el argumento es null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``3(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,``1,``2,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crea un objeto <see cref="T:System.Threading.Tasks.Task" /> que representa un par de métodos Begin y End que se ajustan al modelo de programación asincrónica.</summary>
      <returns>Objeto <see cref="T:System.Threading.Tasks.Task" /> creado que representa la operación asincrónica.</returns>
      <param name="beginMethod">Delegado que comienza la operación asincrónica.</param>
      <param name="endMethod">Delegado que finaliza la operación asincrónica.</param>
      <param name="arg1">Primer argumento que se pasa al delegado de <paramref name="beginMethod" />.</param>
      <param name="arg2">Segundo argumento que se pasa al delegado de <paramref name="beginMethod" />.</param>
      <param name="arg3">Tercer argumento que se pasa al delegado de <paramref name="beginMethod" />.</param>
      <param name="state">Objeto que contiene los datos que el delegado de <paramref name="beginMethod" /> va a usar.</param>
      <param name="creationOptions">Valor de TaskCreationOptions que controla el comportamiento del objeto <see cref="T:System.Threading.Tasks.Task" /> creado.</param>
      <typeparam name="TArg1">Tipo del segundo argumento que se pasa al delegado de <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg2">Tipo del tercer argumento que se pasa al delegado de <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg3">Tipo del primer argumento que se pasa al delegado de <paramref name="beginMethod" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce cuando la <paramref name="beginMethod" /> el argumento es null.o bienLa excepción que se produce cuando la <paramref name="endMethod" /> el argumento es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">La excepción que se produce cuando el <paramref name="creationOptions" /> argumento especifica un valor TaskCreationOptions no válido.La excepción que se produce cuando el <paramref name="creationOptions" /> argumento especifica un valor TaskCreationOptions no válido.Para obtener más información, vea la sección Comentarios para <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /></exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``4(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``3},``0,``1,``2,System.Object)">
      <summary>Crea un objeto <see cref="T:System.Threading.Tasks.Task`1" /> que representa un par de métodos Begin y End que se ajustan al modelo de programación asincrónica.</summary>
      <returns>Objeto <see cref="T:System.Threading.Tasks.Task`1" /> creado que representa la operación asincrónica.</returns>
      <param name="beginMethod">Delegado que comienza la operación asincrónica.</param>
      <param name="endMethod">Delegado que finaliza la operación asincrónica.</param>
      <param name="arg1">Primer argumento que se pasa al delegado de <paramref name="beginMethod" />.</param>
      <param name="arg2">Segundo argumento que se pasa al delegado de <paramref name="beginMethod" />.</param>
      <param name="arg3">Tercer argumento que se pasa al delegado de <paramref name="beginMethod" />.</param>
      <param name="state">Objeto que contiene los datos que el delegado de <paramref name="beginMethod" /> va a usar.</param>
      <typeparam name="TArg1">Tipo del segundo argumento que se pasa al delegado de <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg2">Tipo del tercer argumento que se pasa al delegado de <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg3">Tipo del primer argumento que se pasa al delegado de <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TResult">Tipo del resultado que está disponible a través de <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce cuando la <paramref name="beginMethod" /> el argumento es null.o bienLa excepción que se produce cuando la <paramref name="endMethod" /> el argumento es null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``4(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,``3},``0,``1,``2,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crea un objeto <see cref="T:System.Threading.Tasks.Task`1" /> que representa un par de métodos Begin y End que se ajustan al modelo de programación asincrónica.</summary>
      <returns>Objeto <see cref="T:System.Threading.Tasks.Task`1" /> creado que representa la operación asincrónica.</returns>
      <param name="beginMethod">Delegado que comienza la operación asincrónica.</param>
      <param name="endMethod">Delegado que finaliza la operación asincrónica.</param>
      <param name="arg1">Primer argumento que se pasa al delegado de <paramref name="beginMethod" />.</param>
      <param name="arg2">Segundo argumento que se pasa al delegado de <paramref name="beginMethod" />.</param>
      <param name="arg3">Tercer argumento que se pasa al delegado de <paramref name="beginMethod" />.</param>
      <param name="state">Objeto que contiene los datos que el delegado de <paramref name="beginMethod" /> va a usar.</param>
      <param name="creationOptions">Valor de TaskCreationOptions que controla el comportamiento del objeto <see cref="T:System.Threading.Tasks.Task`1" /> creado.</param>
      <typeparam name="TArg1">Tipo del segundo argumento que se pasa al delegado de <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg2">Tipo del tercer argumento que se pasa al delegado de <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg3">Tipo del primer argumento que se pasa al delegado de <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TResult">Tipo del resultado que está disponible a través de <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce cuando la <paramref name="beginMethod" /> el argumento es null.o bienLa excepción que se produce cuando la <paramref name="endMethod" /> el argumento es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">La excepción que se produce cuando el <paramref name="creationOptions" /> argumento especifica un valor TaskCreationOptions no válido.La excepción que se produce cuando el <paramref name="creationOptions" /> argumento especifica un valor TaskCreationOptions no válido.Para obtener más información, vea la sección Comentarios para <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /></exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync(System.IAsyncResult,System.Action{System.IAsyncResult})">
      <summary>Crea un objeto <see cref="T:System.Threading.Tasks.Task" /> que ejecuta una acción del método End cuando se completa la interfaz <see cref="T:System.IAsyncResult" /> especificada.</summary>
      <returns>Objeto <see cref="T:System.Threading.Tasks.Task" /> que representa la operación asincrónica.</returns>
      <param name="asyncResult">Interfaz IAsyncResult cuya finalización debe desencadenar el procesamiento de <paramref name="endMethod" />.</param>
      <param name="endMethod">Delegado de acción que procesa el objeto <paramref name="asyncResult" /> completado.</param>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce cuando la <paramref name="asyncResult" /> el argumento es null.o bienLa excepción que se produce cuando la <paramref name="endMethod" /> el argumento es null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync(System.IAsyncResult,System.Action{System.IAsyncResult},System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crea un objeto <see cref="T:System.Threading.Tasks.Task" /> que ejecuta una acción del método End cuando se completa la interfaz <see cref="T:System.IAsyncResult" /> especificada.</summary>
      <returns>Objeto <see cref="T:System.Threading.Tasks.Task" /> que representa la operación asincrónica.</returns>
      <param name="asyncResult">Interfaz IAsyncResult cuya finalización debe desencadenar el procesamiento de <paramref name="endMethod" />.</param>
      <param name="endMethod">Delegado de acción que procesa el objeto <paramref name="asyncResult" /> completado.</param>
      <param name="creationOptions">Valor de TaskCreationOptions que controla el comportamiento del objeto <see cref="T:System.Threading.Tasks.Task" /> creado.</param>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce cuando la <paramref name="asyncResult" /> el argumento es null.o bienLa excepción que se produce cuando la <paramref name="endMethod" /> el argumento es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">La excepción que se produce cuando el <paramref name="creationOptions" /> argumento especifica un valor TaskCreationOptions no válido.Para obtener más información, vea la sección Comentarios para <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /></exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync(System.IAsyncResult,System.Action{System.IAsyncResult},System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea un objeto <see cref="T:System.Threading.Tasks.Task" /> que ejecuta una acción del método End cuando se completa la interfaz <see cref="T:System.IAsyncResult" /> especificada.</summary>
      <returns>Objeto <see cref="T:System.Threading.Tasks.Task" /> creado que representa la operación asincrónica.</returns>
      <param name="asyncResult">Interfaz IAsyncResult cuya finalización debe desencadenar el procesamiento de <paramref name="endMethod" />.</param>
      <param name="endMethod">Delegado de acción que procesa el objeto <paramref name="asyncResult" /> completado.</param>
      <param name="creationOptions">Valor de TaskCreationOptions que controla el comportamiento del objeto <see cref="T:System.Threading.Tasks.Task" /> creado.</param>
      <param name="scheduler">Objeto <see cref="T:System.Threading.Tasks.TaskScheduler" /> que se usa para programar la tarea que ejecuta el método End.</param>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce cuando la <paramref name="asyncResult" /> el argumento es null.o bienLa excepción que se produce cuando la <paramref name="endMethod" /> el argumento es null.o bienLa excepción que se produce cuando la <paramref name="scheduler" /> el argumento es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">La excepción que se produce cuando el <paramref name="creationOptions" /> argumento especifica un valor TaskCreationOptions no válido.La excepción que se produce cuando el <paramref name="creationOptions" /> argumento especifica un valor TaskCreationOptions no válido.Para obtener más información, vea la sección Comentarios para <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /></exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.IAsyncResult,System.Func{System.IAsyncResult,``0})">
      <summary>Crea un objeto <see cref="T:System.Threading.Tasks.Task`1" /> que ejecuta una función del método End cuando se completa la interfaz <see cref="T:System.IAsyncResult" /> especificada.</summary>
      <returns>Objeto <see cref="T:System.Threading.Tasks.Task`1" /> que representa la operación asincrónica.</returns>
      <param name="asyncResult">Interfaz IAsyncResult cuya finalización debe desencadenar el procesamiento de <paramref name="endMethod" />.</param>
      <param name="endMethod">Delegado de función que procesa el objeto <paramref name="asyncResult" /> completado.</param>
      <typeparam name="TResult">Tipo del resultado que está disponible a través de <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce cuando la <paramref name="asyncResult" /> el argumento es null.o bienLa excepción que se produce cuando la <paramref name="endMethod" /> el argumento es null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.IAsyncResult,System.Func{System.IAsyncResult,``0},System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crea un objeto <see cref="T:System.Threading.Tasks.Task`1" /> que ejecuta una función del método End cuando se completa la interfaz <see cref="T:System.IAsyncResult" /> especificada.</summary>
      <returns>Objeto <see cref="T:System.Threading.Tasks.Task`1" /> que representa la operación asincrónica.</returns>
      <param name="asyncResult">Interfaz IAsyncResult cuya finalización debe desencadenar el procesamiento de <paramref name="endMethod" />.</param>
      <param name="endMethod">Delegado de función que procesa el objeto <paramref name="asyncResult" /> completado.</param>
      <param name="creationOptions">Valor de TaskCreationOptions que controla el comportamiento del objeto <see cref="T:System.Threading.Tasks.Task`1" /> creado.</param>
      <typeparam name="TResult">Tipo del resultado que está disponible a través de <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce cuando la <paramref name="asyncResult" /> el argumento es null.o bienLa excepción que se produce cuando la <paramref name="endMethod" /> el argumento es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">La excepción que se produce cuando el <paramref name="creationOptions" /> argumento especifica un valor TaskCreationOptions no válido.La excepción que se produce cuando el <paramref name="creationOptions" /> argumento especifica un valor TaskCreationOptions no válido.Para obtener más información, vea la sección Comentarios para <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /></exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.FromAsync``1(System.IAsyncResult,System.Func{System.IAsyncResult,``0},System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea un objeto <see cref="T:System.Threading.Tasks.Task`1" /> que ejecuta una función del método End cuando se completa la interfaz <see cref="T:System.IAsyncResult" /> especificada.</summary>
      <returns>Objeto <see cref="T:System.Threading.Tasks.Task`1" /> que representa la operación asincrónica.</returns>
      <param name="asyncResult">Interfaz IAsyncResult cuya finalización debe desencadenar el procesamiento de <paramref name="endMethod" />.</param>
      <param name="endMethod">Delegado de función que procesa el objeto <paramref name="asyncResult" /> completado.</param>
      <param name="creationOptions">Valor de TaskCreationOptions que controla el comportamiento del objeto <see cref="T:System.Threading.Tasks.Task`1" /> creado.</param>
      <param name="scheduler">Objeto <see cref="T:System.Threading.Tasks.TaskScheduler" /> que se usa para programar la tarea que ejecuta el método End.</param>
      <typeparam name="TResult">Tipo del resultado que está disponible a través de <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce cuando la <paramref name="asyncResult" /> el argumento es null.o bienLa excepción que se produce cuando la <paramref name="endMethod" /> el argumento es null.o bienLa excepción que se produce cuando la <paramref name="scheduler" /> el argumento es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">La excepción que se produce cuando el <paramref name="creationOptions" /> argumento especifica un valor TaskCreationOptions no válido.La excepción que se produce cuando el <paramref name="creationOptions" /> argumento especifica un valor TaskCreationOptions no válido.Para obtener más información, vea la sección Comentarios para <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /></exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory.Scheduler">
      <summary>Obtiene el programador de tareas predeterminado para este generador de tareas.</summary>
      <returns>Programador de tareas predeterminado para este generador de tareas.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action)">
      <summary>Crea e inicia una tarea.</summary>
      <returns>La tarea iniciada.</returns>
      <param name="action">Delegado de acción que se va a ejecutar de forma asincrónica.</param>
      <exception cref="T:System.ArgumentNullException">El <paramref name="action" /> el argumento es null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action,System.Threading.CancellationToken)">
      <summary>Crea e inicia un objeto <see cref="T:System.Threading.Tasks.Task" />.</summary>
      <returns>Objeto <see cref="T:System.Threading.Tasks.Task" /> iniciado.</returns>
      <param name="action">Delegado de acción que se va a ejecutar de forma asincrónica.</param>
      <param name="cancellationToken">El <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> al que se va a asignar la nueva tarea.</param>
      <exception cref="T:System.ObjectDisposedException">El <see cref="T:System.Threading.CancellationToken" /> ya se ha eliminado.</exception>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce cuando la <paramref name="action" /> el argumento es null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea e inicia un objeto <see cref="T:System.Threading.Tasks.Task" />.</summary>
      <returns>Objeto <see cref="T:System.Threading.Tasks.Task" /> iniciado.</returns>
      <param name="action">Delegado de acción que se va a ejecutar de forma asincrónica.</param>
      <param name="cancellationToken">
        <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> que se asignará a la nueva <see cref="T:System.Threading.Tasks.Task" /></param>
      <param name="creationOptions">Valor de TaskCreationOptions que controla el comportamiento del objeto <see cref="T:System.Threading.Tasks.Task" /> creado.</param>
      <param name="scheduler">Objeto <see cref="T:System.Threading.Tasks.TaskScheduler" /> que se usa para programar el objeto <see cref="T:System.Threading.Tasks.Task" /> creado.</param>
      <exception cref="T:System.ObjectDisposedException">El <see cref="T:System.Threading.CancellationToken" /> ya se ha eliminado.</exception>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce cuando la <paramref name="action" /> el argumento es null.o bienLa excepción que se produce cuando la <paramref name="scheduler" /> el argumento es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">La excepción que se produce cuando el <paramref name="creationOptions" /> argumento especifica un valor TaskCreationOptions no válido.La excepción que se produce cuando el <paramref name="creationOptions" /> argumento especifica un valor TaskCreationOptions no válido.Para obtener más información, vea la sección Comentarios para <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /></exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crea e inicia un objeto <see cref="T:System.Threading.Tasks.Task" />.</summary>
      <returns>Objeto <see cref="T:System.Threading.Tasks.Task" /> iniciado.</returns>
      <param name="action">Delegado de acción que se va a ejecutar de forma asincrónica.</param>
      <param name="creationOptions">Valor de TaskCreationOptions que controla el comportamiento del objeto <see cref="T:System.Threading.Tasks.Task" /> creado.</param>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce cuando la <paramref name="action" /> el argumento es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">La excepción que se produce cuando el <paramref name="creationOptions" /> argumento especifica un valor TaskCreationOptions no válido.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action{System.Object},System.Object)">
      <summary>Crea e inicia un objeto <see cref="T:System.Threading.Tasks.Task" />. </summary>
      <returns>Objeto <see cref="T:System.Threading.Tasks.Task" /> iniciado. </returns>
      <param name="action">Delegado de acción que se va a ejecutar de forma asincrónica. </param>
      <param name="state">Objeto que contiene los datos que el delegado de <paramref name="action" /> va a usar. </param>
      <exception cref="T:System.ArgumentNullException">El argumento <paramref name="action" /> es null. </exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action{System.Object},System.Object,System.Threading.CancellationToken)">
      <summary>Crea e inicia un objeto <see cref="T:System.Threading.Tasks.Task" />.</summary>
      <returns>Objeto <see cref="T:System.Threading.Tasks.Task" /> iniciado.</returns>
      <param name="action">Delegado de acción que se va a ejecutar de forma asincrónica.</param>
      <param name="state">Objeto que contiene los datos que el delegado de <paramref name="action" /> va a usar.</param>
      <param name="cancellationToken">
        <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> que se asignará a la nueva <see cref="T:System.Threading.Tasks.Task" /></param>
      <exception cref="T:System.ObjectDisposedException">El <see cref="T:System.Threading.CancellationToken" /> ya se ha eliminado.</exception>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce cuando la <paramref name="action" /> el argumento es null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action{System.Object},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea e inicia un objeto <see cref="T:System.Threading.Tasks.Task" />.</summary>
      <returns>Objeto <see cref="T:System.Threading.Tasks.Task" /> iniciado.</returns>
      <param name="action">Delegado de acción que se va a ejecutar de forma asincrónica.</param>
      <param name="state">Objeto que contiene los datos que el delegado de <paramref name="action" /> va a usar.</param>
      <param name="cancellationToken">El <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> al que se va a asignar la nueva tarea.</param>
      <param name="creationOptions">Valor de TaskCreationOptions que controla el comportamiento del objeto <see cref="T:System.Threading.Tasks.Task" /> creado.</param>
      <param name="scheduler">Objeto <see cref="T:System.Threading.Tasks.TaskScheduler" /> que se usa para programar el objeto <see cref="T:System.Threading.Tasks.Task" /> creado.</param>
      <exception cref="T:System.ObjectDisposedException">El <see cref="T:System.Threading.CancellationToken" /> ya se ha eliminado.</exception>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce cuando la <paramref name="action" /> el argumento es null.o bienLa excepción que se produce cuando la <paramref name="scheduler" /> el argumento es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">La excepción que se produce cuando el <paramref name="creationOptions" /> argumento especifica un valor TaskCreationOptions no válido.La excepción que se produce cuando el <paramref name="creationOptions" /> argumento especifica un valor TaskCreationOptions no válido.Para obtener más información, vea la sección Comentarios para <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /></exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew(System.Action{System.Object},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crea e inicia un objeto <see cref="T:System.Threading.Tasks.Task" />.</summary>
      <returns>Objeto <see cref="T:System.Threading.Tasks.Task" /> iniciado.</returns>
      <param name="action">Delegado de acción que se va a ejecutar de forma asincrónica.</param>
      <param name="state">Objeto que contiene los datos que el delegado de <paramref name="action" /> va a usar.</param>
      <param name="creationOptions">Valor de TaskCreationOptions que controla el comportamiento del objeto <see cref="T:System.Threading.Tasks.Task" /> creado.</param>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce cuando la <paramref name="action" /> el argumento es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">La excepción que se produce cuando el <paramref name="creationOptions" /> argumento especifica un valor TaskCreationOptions no válido.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{``0})">
      <summary>Crea e inicia un objeto <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>Objeto <see cref="T:System.Threading.Tasks.Task`1" /> iniciado.</returns>
      <param name="function">Delegado de función que devuelve el resultado futuro que va a estar disponible a través de <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <typeparam name="TResult">Tipo del resultado que está disponible a través de <see cref="T:System.Threading.Tasks.Task`1" />. </typeparam>
      <exception cref="T:System.ArgumentNullException">El argumento <paramref name="function" /> es null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{``0},System.Threading.CancellationToken)">
      <summary>Crea e inicia un objeto <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>Objeto <see cref="T:System.Threading.Tasks.Task`1" /> iniciado.</returns>
      <param name="function">Delegado de función que devuelve el resultado futuro que va a estar disponible a través de <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <param name="cancellationToken">
        <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> que se asignará a la nueva <see cref="T:System.Threading.Tasks.Task" /></param>
      <typeparam name="TResult">Tipo del resultado que está disponible a través de <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ObjectDisposedException">El <see cref="T:System.Threading.CancellationToken" /> ya se ha eliminado.</exception>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce cuando la <paramref name="function" /> el argumento es null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{``0},System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea e inicia un objeto <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>Objeto <see cref="T:System.Threading.Tasks.Task`1" /> iniciado.</returns>
      <param name="function">Delegado de función que devuelve el resultado futuro que va a estar disponible a través de <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <param name="cancellationToken">El <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> al que se va a asignar la nueva tarea.</param>
      <param name="creationOptions">Valor de TaskCreationOptions que controla el comportamiento del objeto <see cref="T:System.Threading.Tasks.Task`1" /> creado.</param>
      <param name="scheduler">Objeto <see cref="T:System.Threading.Tasks.TaskScheduler" /> que se usa para programar el objeto <see cref="T:System.Threading.Tasks.Task`1" /> creado.</param>
      <typeparam name="TResult">Tipo del resultado que está disponible a través de <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ObjectDisposedException">El <see cref="T:System.Threading.CancellationToken" /> ya se ha eliminado.</exception>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce cuando la <paramref name="function" /> el argumento es null.o bienLa excepción que se produce cuando la <paramref name="scheduler" /> el argumento es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">La excepción que se produce cuando el <paramref name="creationOptions" /> argumento especifica un valor TaskCreationOptions no válido.La excepción que se produce cuando el <paramref name="creationOptions" /> argumento especifica un valor TaskCreationOptions no válido.Para obtener más información, vea la sección Comentarios para <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /></exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{``0},System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crea e inicia un objeto <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>Objeto <see cref="T:System.Threading.Tasks.Task`1" /> iniciado.</returns>
      <param name="function">Delegado de función que devuelve el resultado futuro que va a estar disponible a través de <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <param name="creationOptions">Valor de TaskCreationOptions que controla el comportamiento del objeto <see cref="T:System.Threading.Tasks.Task`1" /> creado.</param>
      <typeparam name="TResult">Tipo del resultado que está disponible a través de <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce cuando la <paramref name="function" /> el argumento es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">La excepción que se produce cuando el <paramref name="creationOptions" /> argumento especifica un valor TaskCreationOptions no válido.La excepción que se produce cuando el <paramref name="creationOptions" /> argumento especifica un valor TaskCreationOptions no válido.Para obtener más información, vea la sección Comentarios para <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /></exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{System.Object,``0},System.Object)">
      <summary>Crea e inicia un objeto <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>Objeto <see cref="T:System.Threading.Tasks.Task`1" /> iniciado.</returns>
      <param name="function">Delegado de función que devuelve el resultado futuro que va a estar disponible a través de <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <param name="state">Objeto que contiene los datos que el delegado de <paramref name="function" /> va a usar.</param>
      <typeparam name="TResult">Tipo del resultado que está disponible a través de <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce cuando la <paramref name="function" /> el argumento es null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{System.Object,``0},System.Object,System.Threading.CancellationToken)">
      <summary>Crea e inicia un objeto <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>Objeto <see cref="T:System.Threading.Tasks.Task`1" /> iniciado.</returns>
      <param name="function">Delegado de función que devuelve el resultado futuro que va a estar disponible a través de <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <param name="state">Objeto que contiene los datos que el delegado de <paramref name="function" /> va a usar.</param>
      <param name="cancellationToken">
        <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> que se asignará a la nueva <see cref="T:System.Threading.Tasks.Task" /></param>
      <typeparam name="TResult">Tipo del resultado que está disponible a través de <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ObjectDisposedException">El <see cref="T:System.Threading.CancellationToken" /> ya se ha eliminado.</exception>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce cuando la <paramref name="function" /> el argumento es null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{System.Object,``0},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea e inicia un objeto <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>Objeto <see cref="T:System.Threading.Tasks.Task`1" /> iniciado.</returns>
      <param name="function">Delegado de función que devuelve el resultado futuro que va a estar disponible a través de <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <param name="state">Objeto que contiene los datos que el delegado de <paramref name="function" /> va a usar.</param>
      <param name="cancellationToken">El <see cref="P:System.Threading.Tasks.TaskFactory.CancellationToken" /> al que se va a asignar la nueva tarea.</param>
      <param name="creationOptions">Valor de TaskCreationOptions que controla el comportamiento del objeto <see cref="T:System.Threading.Tasks.Task`1" /> creado.</param>
      <param name="scheduler">Objeto <see cref="T:System.Threading.Tasks.TaskScheduler" /> que se usa para programar el objeto <see cref="T:System.Threading.Tasks.Task`1" /> creado.</param>
      <typeparam name="TResult">Tipo del resultado que está disponible a través de <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ObjectDisposedException">El <see cref="T:System.Threading.CancellationToken" /> ya se ha eliminado.</exception>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce cuando la <paramref name="function" /> el argumento es null.o bienLa excepción que se produce cuando la <paramref name="scheduler" /> el argumento es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">La excepción que se produce cuando el <paramref name="creationOptions" /> argumento especifica un valor TaskCreationOptions no válido.La excepción que se produce cuando el <paramref name="creationOptions" /> argumento especifica un valor TaskCreationOptions no válido.Para obtener más información, vea la sección Comentarios para <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /></exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory.StartNew``1(System.Func{System.Object,``0},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crea e inicia un objeto <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <returns>Objeto <see cref="T:System.Threading.Tasks.Task`1" /> iniciado.</returns>
      <param name="function">Delegado de función que devuelve el resultado futuro que va a estar disponible a través de <see cref="T:System.Threading.Tasks.Task`1" />.</param>
      <param name="state">Objeto que contiene los datos que el delegado de <paramref name="function" /> va a usar.</param>
      <param name="creationOptions">Valor de TaskCreationOptions que controla el comportamiento del objeto <see cref="T:System.Threading.Tasks.Task`1" /> creado.</param>
      <typeparam name="TResult">Tipo del resultado que está disponible a través de <see cref="T:System.Threading.Tasks.Task`1" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">La excepción que se produce cuando la <paramref name="function" /> el argumento es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">La excepción que se produce cuando el <paramref name="creationOptions" /> argumento especifica un valor TaskCreationOptions no válido.La excepción que se produce cuando el <paramref name="creationOptions" /> argumento especifica un valor TaskCreationOptions no válido.Para obtener más información, vea la sección Comentarios para <see cref="M:System.Threading.Tasks.TaskFactory.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},System.Object,System.Threading.Tasks.TaskCreationOptions)" /></exception>
    </member>
    <member name="T:System.Threading.Tasks.TaskFactory`1">
      <summary>Proporciona compatibilidad para crear y programar objetos <see cref="T:System.Threading.Tasks.Task`1" />.</summary>
      <typeparam name="TResult">Valor devuelto de los objetos <see cref="T:System.Threading.Tasks.Task`1" /> creados por los métodos de esta clase. </typeparam>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.#ctor">
      <summary>Inicializa una instancia de <see cref="T:System.Threading.Tasks.TaskFactory`1" /> con la configuración predeterminada.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.#ctor(System.Threading.CancellationToken)">
      <summary>Inicializa una instancia de <see cref="T:System.Threading.Tasks.TaskFactory`1" /> con la configuración predeterminada.</summary>
      <param name="cancellationToken">Token de cancelación predeterminado que se asignará a las tareas creadas por este <see cref="T:System.Threading.Tasks.TaskFactory" /> a menos que se especifique otro token de cancelación de forma explícita mientras se llama a los métodos de generador.</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.#ctor(System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Inicializa una instancia de <see cref="T:System.Threading.Tasks.TaskFactory`1" /> con la configuración especificada.</summary>
      <param name="cancellationToken">Token de cancelación predeterminado que se asignará a las tareas creadas por este <see cref="T:System.Threading.Tasks.TaskFactory" /> a menos que se especifique otro token de cancelación de forma explícita mientras se llama a los métodos de generador.</param>
      <param name="creationOptions">Opciones predeterminadas que se van a usar al crear tareas con este <see cref="T:System.Threading.Tasks.TaskFactory`1" />.</param>
      <param name="continuationOptions">Opciones predeterminadas que se van a usar al crear tareas de continuación con este <see cref="T:System.Threading.Tasks.TaskFactory`1" />.</param>
      <param name="scheduler">Programador predeterminado que se va a usar para programar las tareas creadas con este <see cref="T:System.Threading.Tasks.TaskFactory`1" />.Un valor null indica que se debería usar <see cref="P:System.Threading.Tasks.TaskScheduler.Current" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> o <paramref name="continuationOptions" /> especifica un valor no válido.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.#ctor(System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Inicializa una instancia de <see cref="T:System.Threading.Tasks.TaskFactory`1" /> con la configuración especificada.</summary>
      <param name="creationOptions">Opciones predeterminadas que se van a usar al crear tareas con este <see cref="T:System.Threading.Tasks.TaskFactory`1" />.</param>
      <param name="continuationOptions">Opciones predeterminadas que se van a usar al crear tareas de continuación con este <see cref="T:System.Threading.Tasks.TaskFactory`1" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="creationOptions" /> o <paramref name="continuationOptions" /> especifica un valor no válido.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.#ctor(System.Threading.Tasks.TaskScheduler)">
      <summary>Inicializa una instancia de <see cref="T:System.Threading.Tasks.TaskFactory`1" /> con la configuración especificada.</summary>
      <param name="scheduler">Programador que se va a usar para programar las tareas creadas con este <see cref="T:System.Threading.Tasks.TaskFactory`1" />.Un valor null indica que se debería usar el <see cref="T:System.Threading.Tasks.TaskScheduler" /> actual.</param>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory`1.CancellationToken">
      <summary>Obtiene el token de cancelación predeterminado para este generador de tareas.</summary>
      <returns>El token de cancelación predeterminado para este generador de tareas.</returns>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory`1.ContinuationOptions">
      <summary>Obtiene el valor de enumeración <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> para este generador de tareas.</summary>
      <returns>Uno de los valores de enumeración que especifica las opciones de continuación predeterminadas para este generador de tareas.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],`0})">
      <summary>Crea una tarea de continuación que se iniciará cuando se complete el conjunto de tareas proporcionado.</summary>
      <returns>Nueva tarea de continuación.</returns>
      <param name="tasks">Matriz de tareas desde la que se va a continuar.</param>
      <param name="continuationFunction">Delegado de función que se va a ejecutar asincrónicamente cuando se completen todas las tareas de la matriz <paramref name="tasks" />.</param>
      <exception cref="T:System.ObjectDisposedException">Uno de los elementos en el <paramref name="tasks" /> se ha eliminado la matriz.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="tasks" /> la matriz es null.o bienEl valor de <paramref name="continuationFunction" /> es null.</exception>
      <exception cref="T:System.ArgumentException">El <paramref name="tasks" /> matriz contiene un valor nulo o está vacío.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],`0},System.Threading.CancellationToken)">
      <summary>Crea una tarea de continuación que se iniciará cuando se complete el conjunto de tareas proporcionado.</summary>
      <returns>Nueva tarea de continuación.</returns>
      <param name="tasks">Matriz de tareas desde la que se va a continuar.</param>
      <param name="continuationFunction">Delegado de función que se va a ejecutar asincrónicamente cuando se completen todas las tareas de la matriz <paramref name="tasks" />.</param>
      <param name="cancellationToken">Token de cancelación que se va a asignar a la nueva tarea de continuación.</param>
      <exception cref="T:System.ObjectDisposedException">Uno de los elementos en el <paramref name="tasks" /> se ha eliminado la matriz.o bienEl <see cref="T:System.Threading.CancellationTokenSource" /> que creó<paramref name=" cancellationToken" /> ya se ha eliminado.</exception>
      <exception cref="T:System.ArgumentNullException">El <paramref name="tasks" /> matriz es null.o bienEl valor de <paramref name="continuationFunction" /> es null.</exception>
      <exception cref="T:System.ArgumentException">El <paramref name="tasks" /> matriz contiene un valor nulo o está vacío.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea una tarea de continuación que se iniciará cuando se complete el conjunto de tareas proporcionado.</summary>
      <returns>Nueva tarea de continuación.</returns>
      <param name="tasks">Matriz de tareas desde la que se va a continuar.</param>
      <param name="continuationFunction">Delegado de función que se va a ejecutar asincrónicamente cuando se completen todas las tareas de la matriz <paramref name="tasks" />.</param>
      <param name="cancellationToken">Token de cancelación que se va a asignar a la nueva tarea de continuación.</param>
      <param name="continuationOptions">Uno de los valores de enumeración que controla el comportamiento de la tarea de continuación creada.Los valores NotOn* u OnlyOn* no son válidos.</param>
      <param name="scheduler">Programador que se usa para programar la tarea de continuación creada.</param>
      <exception cref="T:System.ArgumentNullException">El <paramref name="tasks" /> matriz es null.o bienEl argumento <paramref name="continuationFunction" /> es null.o bienEl argumento <paramref name="scheduler" /> es null.</exception>
      <exception cref="T:System.ArgumentException">El <paramref name="tasks" /> matriz contiene un valor nulo o está vacío.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="continuationOptions" /> Especifica un valor no válido.</exception>
      <exception cref="T:System.ObjectDisposedException">Uno de los elementos en el <paramref name="tasks" /> se ha eliminado la matriz.o bienEl <see cref="T:System.Threading.CancellationTokenSource" /> que creó<paramref name=" cancellationToken" /> ya se ha eliminado.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task[],`0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crea una tarea de continuación que se iniciará cuando se complete el conjunto de tareas proporcionado.</summary>
      <returns>Nueva tarea de continuación.</returns>
      <param name="tasks">Matriz de tareas desde la que se va a continuar.</param>
      <param name="continuationFunction">Delegado de función que se va a ejecutar asincrónicamente cuando se completen todas las tareas de la matriz <paramref name="tasks" />.</param>
      <param name="continuationOptions">Uno de los valores de enumeración que controla el comportamiento de la tarea de continuación creada.Los valores NotOn* u OnlyOn* no son válidos.</param>
      <exception cref="T:System.ObjectDisposedException">Uno de los elementos en el <paramref name="tasks" /> se ha eliminado la matriz.</exception>
      <exception cref="T:System.ArgumentNullException">El <paramref name="tasks" /> matriz es null.o bienEl argumento <paramref name="continuationFunction" /> es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El <paramref name="continuationOptions" /> argumento especifica un valor no válido.</exception>
      <exception cref="T:System.ArgumentException">El <paramref name="tasks" /> matriz contiene un valor nulo o está vacío.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],`0})">
      <summary>Crea una tarea de continuación que se iniciará cuando se complete el conjunto de tareas proporcionado.</summary>
      <returns>Nueva tarea de continuación.</returns>
      <param name="tasks">Matriz de tareas desde la que se va a continuar.</param>
      <param name="continuationFunction">Delegado de función que se va a ejecutar asincrónicamente cuando se completen todas las tareas de la matriz <paramref name="tasks" />.</param>
      <typeparam name="TAntecedentResult">Tipo del resultado del parámetro <paramref name="tasks" /> precedente.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Uno de los elementos en el <paramref name="tasks" /> se ha eliminado la matriz.</exception>
      <exception cref="T:System.ArgumentNullException">El <paramref name="tasks" /> matriz es null.o bienEl argumento <paramref name="continuationFunction" /> es null.</exception>
      <exception cref="T:System.ArgumentException">El <paramref name="tasks" /> matriz contiene un valor nulo o está vacío.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],`0},System.Threading.CancellationToken)">
      <summary>Crea una tarea de continuación que se iniciará cuando se complete el conjunto de tareas proporcionado.</summary>
      <returns>Nueva tarea de continuación.</returns>
      <param name="tasks">Matriz de tareas desde la que se va a continuar.</param>
      <param name="continuationFunction">Delegado de función que se va a ejecutar asincrónicamente cuando se completen todas las tareas de la matriz <paramref name="tasks" />.</param>
      <param name="cancellationToken">Token de cancelación que se va a asignar a la nueva tarea de continuación.</param>
      <typeparam name="TAntecedentResult">Tipo del resultado del parámetro <paramref name="tasks" /> precedente.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Uno de los elementos en el <paramref name="tasks" /> se ha eliminado la matriz.o bienEl <see cref="T:System.Threading.CancellationTokenSource" /> que creó<paramref name=" cancellationToken" /> ya se ha eliminado.</exception>
      <exception cref="T:System.ArgumentNullException">El <paramref name="tasks" /> matriz es null.o bienEl argumento <paramref name="continuationFunction" /> es null.</exception>
      <exception cref="T:System.ArgumentException">El <paramref name="tasks" /> matriz contiene un valor nulo o está vacío.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea una tarea de continuación que se iniciará cuando se complete el conjunto de tareas proporcionado.</summary>
      <returns>Nueva tarea de continuación.</returns>
      <param name="tasks">Matriz de tareas desde la que se va a continuar.</param>
      <param name="continuationFunction">Delegado de función que se va a ejecutar asincrónicamente cuando se completen todas las tareas de la matriz <paramref name="tasks" />.</param>
      <param name="cancellationToken">Token de cancelación que se va a asignar a la nueva tarea de continuación.</param>
      <param name="continuationOptions">Uno de los valores de enumeración que controla el comportamiento de la tarea de continuación creada.Los valores NotOn* u OnlyOn* no son válidos.</param>
      <param name="scheduler">Programador que se usa para programar la tarea de continuación creada.</param>
      <typeparam name="TAntecedentResult">Tipo del resultado del parámetro <paramref name="tasks" /> precedente.</typeparam>
      <exception cref="T:System.ArgumentNullException">El <paramref name="tasks" /> matriz es null.o bienEl argumento <paramref name="continuationFunction" /> es null.o bienEl argumento <paramref name="scheduler" /> es null.</exception>
      <exception cref="T:System.ArgumentException">El <paramref name="tasks" /> matriz contiene un valor nulo o está vacío.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El <paramref name="continuationOptions" /> argumento especifica un valor no válido.</exception>
      <exception cref="T:System.ObjectDisposedException">Uno de los elementos en el <paramref name="tasks" /> se ha eliminado la matriz.o bienEl <see cref="T:System.Threading.CancellationTokenSource" /> que creó<paramref name=" cancellationToken" /> ya se ha eliminado.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAll``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0}[],`0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crea una tarea de continuación que se iniciará cuando se complete el conjunto de tareas proporcionado.</summary>
      <returns>Nueva tarea de continuación.</returns>
      <param name="tasks">Matriz de tareas desde la que se va a continuar.</param>
      <param name="continuationFunction">Delegado de función que se va a ejecutar asincrónicamente cuando se completen todas las tareas de la matriz <paramref name="tasks" />.</param>
      <param name="continuationOptions">Uno de los valores de enumeración que controla el comportamiento de la tarea de continuación creada.Los valores NotOn* u OnlyOn* no son válidos.</param>
      <typeparam name="TAntecedentResult">Tipo del resultado del parámetro <paramref name="tasks" /> precedente.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Uno de los elementos en el <paramref name="tasks" /> se ha eliminado la matriz.</exception>
      <exception cref="T:System.ArgumentNullException">El <paramref name="tasks" /> matriz es null.o bienEl argumento <paramref name="continuationFunction" /> es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El <paramref name="continuationOptions" /> argumento especifica un valor no válido.</exception>
      <exception cref="T:System.ArgumentException">El <paramref name="tasks" /> matriz contiene un valor nulo o está vacío.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,`0})">
      <summary>Crea una tarea de continuación que se iniciará cuando se complete cualquier tarea del conjunto proporcionado. </summary>
      <returns>Nueva tarea de continuación.</returns>
      <param name="tasks">Matriz de tareas desde la que se va a continuar cuando se complete una tarea.</param>
      <param name="continuationFunction">Delegado de función que se va a ejecutar asincrónicamente cuando se complete una tarea de la matriz <paramref name="tasks" />.</param>
      <exception cref="T:System.ObjectDisposedException">Uno de los elementos en el <paramref name="tasks" /> se ha eliminado la matriz.</exception>
      <exception cref="T:System.ArgumentNullException">El <paramref name="tasks" /> matriz es null.o bienEl argumento <paramref name="continuationFunction" /> es null.</exception>
      <exception cref="T:System.ArgumentException">El <paramref name="tasks" /> matriz contiene un valor nulo o está vacío.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,`0},System.Threading.CancellationToken)">
      <summary>Crea una tarea de continuación que se iniciará cuando se complete cualquier tarea del conjunto proporcionado.</summary>
      <returns>Nueva tarea de continuación.</returns>
      <param name="tasks">Matriz de tareas desde la que se va a continuar cuando se complete una tarea.</param>
      <param name="continuationFunction">Delegado de función que se va a ejecutar asincrónicamente cuando se complete una tarea de la matriz <paramref name="tasks" />.</param>
      <param name="cancellationToken">Token de cancelación que se va a asignar a la nueva tarea de continuación.</param>
      <exception cref="T:System.ObjectDisposedException">Uno de los elementos en el <paramref name="tasks" /> se ha eliminado la matriz.o bienEl <see cref="T:System.Threading.CancellationTokenSource" /> que creó<paramref name=" cancellationToken" /> ya se ha eliminado.</exception>
      <exception cref="T:System.ArgumentNullException">El <paramref name="tasks" /> matriz es null.o bienEl <paramref name="continuationFunction" /> el argumento es null.</exception>
      <exception cref="T:System.ArgumentException">El <paramref name="tasks" /> matriz contiene un valor null.o bienEl <paramref name="tasks" /> matriz está vacía.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea una tarea de continuación que se iniciará cuando se complete cualquier tarea del conjunto proporcionado.</summary>
      <returns>Nueva tarea de continuación.</returns>
      <param name="tasks">Matriz de tareas desde la que se va a continuar cuando se complete una tarea.</param>
      <param name="continuationFunction">Delegado de función que se va a ejecutar asincrónicamente cuando se complete una tarea de la matriz <paramref name="tasks" />.</param>
      <param name="cancellationToken">Token de cancelación que se va a asignar a la nueva tarea de continuación.</param>
      <param name="continuationOptions">Uno de los valores de enumeración que controla el comportamiento de la tarea de continuación creada.Los valores NotOn* o OnlyOn* no son válidos.</param>
      <param name="scheduler">Programador que se usa para programar la tarea de continuación creada.</param>
      <exception cref="T:System.ArgumentNullException">El <paramref name="tasks" /> matriz es null.o bienEl argumento <paramref name="continuationFunction" /> es null.o bienEl argumento <paramref name="scheduler" /> es null.</exception>
      <exception cref="T:System.ArgumentException">El <paramref name="tasks" /> matriz contiene un valor null.o bienEl <paramref name="tasks" /> matriz está vacía.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El <paramref name="continuationOptions" /> no válido que especifica el argumento <see cref="T:System.Threading.Tasks.TaskContinuationOptions" /> valor.</exception>
      <exception cref="T:System.ObjectDisposedException">Uno de los elementos en el <paramref name="tasks" /> se ha eliminado la matriz.o bienEl <see cref="T:System.Threading.CancellationTokenSource" /> que creó<paramref name=" cancellationToken" /> ya se ha eliminado. </exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny(System.Threading.Tasks.Task[],System.Func{System.Threading.Tasks.Task,`0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crea una tarea de continuación que se iniciará cuando se complete cualquier tarea del conjunto proporcionado.</summary>
      <returns>Nueva tarea de continuación.</returns>
      <param name="tasks">Matriz de tareas desde la que se va a continuar cuando se complete una tarea.</param>
      <param name="continuationFunction">Delegado de función que se va a ejecutar asincrónicamente cuando se complete una tarea de la matriz <paramref name="tasks" />.</param>
      <param name="continuationOptions">Uno de los valores de enumeración que controla el comportamiento de la tarea de continuación creada.Los valores NotOn* o OnlyOn* no son válidos.</param>
      <exception cref="T:System.ObjectDisposedException">Uno de los elementos en el <paramref name="tasks" /> se ha eliminado la matriz.</exception>
      <exception cref="T:System.ArgumentNullException">El <paramref name="tasks" /> matriz es null.o bienEl argumento <paramref name="continuationFunction" /> es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El <paramref name="continuationOptions" /> argumento especifica un valor de enumeración no válido.</exception>
      <exception cref="T:System.ArgumentException">El <paramref name="tasks" /> matriz contiene un valor null.o bienEl <paramref name="tasks" /> matriz está vacía.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},`0})">
      <summary>Crea una tarea de continuación que se iniciará cuando se complete cualquier tarea del conjunto proporcionado.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task`1" /> de continuación.</returns>
      <param name="tasks">Matriz de tareas desde la que se va a continuar cuando se complete una tarea.</param>
      <param name="continuationFunction">Delegado de función que se va a ejecutar asincrónicamente cuando se complete una tarea de la matriz <paramref name="tasks" />.</param>
      <typeparam name="TAntecedentResult">Tipo del resultado del parámetro <paramref name="tasks" /> precedente.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Uno de los elementos en el <paramref name="tasks" /> se ha eliminado la matriz.</exception>
      <exception cref="T:System.ArgumentNullException">El <paramref name="tasks" /> matriz es null.o bienEl argumento <paramref name="continuationFunction" /> es null.</exception>
      <exception cref="T:System.ArgumentException">El <paramref name="tasks" /> matriz contiene un valor null.o bienEl <paramref name="tasks" /> matriz está vacía.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},`0},System.Threading.CancellationToken)">
      <summary>Crea una tarea de continuación que se iniciará cuando se complete cualquier tarea del conjunto proporcionado.</summary>
      <returns>Nueva tarea de continuación.</returns>
      <param name="tasks">Matriz de tareas desde la que se va a continuar cuando se complete una tarea.</param>
      <param name="continuationFunction">Delegado de función que se va a ejecutar asincrónicamente cuando se complete una tarea de la matriz <paramref name="tasks" />.</param>
      <param name="cancellationToken">Token de cancelación que se va a asignar a la nueva tarea de continuación.</param>
      <typeparam name="TAntecedentResult">Tipo del resultado del parámetro <paramref name="tasks" /> precedente.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Uno de los elementos en el <paramref name="tasks" /> se ha eliminado la matriz.o bienEl <see cref="T:System.Threading.CancellationTokenSource" /> que creó<paramref name=" cancellationToken" /> ya se ha eliminado.</exception>
      <exception cref="T:System.ArgumentNullException">El <paramref name="tasks" /> matriz es null.o bienEl argumento <paramref name="continuationFunction" /> es null.</exception>
      <exception cref="T:System.ArgumentException">El <paramref name="tasks" /> matriz contiene un valor null.o bienEl <paramref name="tasks" /> matriz está vacía.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskContinuationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea una tarea de continuación que se iniciará cuando se complete cualquier tarea del conjunto proporcionado.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task`1" /> de continuación.</returns>
      <param name="tasks">Matriz de tareas desde la que se va a continuar cuando se complete una tarea.</param>
      <param name="continuationFunction">Delegado de función que se va a ejecutar asincrónicamente cuando se complete una tarea de la matriz <paramref name="tasks" />.</param>
      <param name="cancellationToken">Token de cancelación que se va a asignar a la nueva tarea de continuación.</param>
      <param name="continuationOptions">Uno de los valores de enumeración que controla el comportamiento de la tarea de continuación creada.Los valores NotOn* o OnlyOn* no son válidos.</param>
      <param name="scheduler">Objeto <see cref="T:System.Threading.Tasks.TaskScheduler" /> que se usa para programar el objeto <see cref="T:System.Threading.Tasks.Task`1" /> de continuación creado.</param>
      <typeparam name="TAntecedentResult">Tipo del resultado del parámetro <paramref name="tasks" /> precedente.</typeparam>
      <exception cref="T:System.ArgumentNullException">El <paramref name="tasks" /> matriz es null.o bienEl argumento <paramref name="continuationFunction" /> es null.o bienEl <paramref name="scheduler" /> el argumento es null.</exception>
      <exception cref="T:System.ArgumentException">El <paramref name="tasks" /> matriz contiene un valor null.o bienEl <paramref name="tasks" /> matriz está vacía.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El <paramref name="continuationOptions" /> argumento especifica un valor TaskContinuationOptions no válido.</exception>
      <exception cref="T:System.ObjectDisposedException">Uno de los elementos en el <paramref name="tasks" /> se ha eliminado la matriz.o bienEl <see cref="T:System.Threading.CancellationTokenSource" /> que creó<paramref name=" cancellationToken" /> ya se ha eliminado. </exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.ContinueWhenAny``1(System.Threading.Tasks.Task{``0}[],System.Func{System.Threading.Tasks.Task{``0},`0},System.Threading.Tasks.TaskContinuationOptions)">
      <summary>Crea una tarea de continuación que se iniciará cuando se complete cualquier tarea del conjunto proporcionado.</summary>
      <returns>Nuevo objeto <see cref="T:System.Threading.Tasks.Task`1" /> de continuación.</returns>
      <param name="tasks">Matriz de tareas desde la que se va a continuar cuando se complete una tarea.</param>
      <param name="continuationFunction">Delegado de función que se va a ejecutar asincrónicamente cuando se complete una tarea de la matriz <paramref name="tasks" />.</param>
      <param name="continuationOptions">Uno de los valores de enumeración que controla el comportamiento de la tarea de continuación creada.Los valores NotOn* o OnlyOn* no son válidos.</param>
      <typeparam name="TAntecedentResult">Tipo del resultado del parámetro <paramref name="tasks" /> precedente.</typeparam>
      <exception cref="T:System.ObjectDisposedException">Uno de los elementos en el <paramref name="tasks" /> se ha eliminado la matriz.</exception>
      <exception cref="T:System.ArgumentNullException">El <paramref name="tasks" /> matriz es null.o bienEl argumento <paramref name="continuationFunction" /> es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El <paramref name="continuationOptions" /> argumento especifica un valor de enumeración no válido.</exception>
      <exception cref="T:System.ArgumentException">El <paramref name="tasks" /> matriz contiene un valor null.o bienEl <paramref name="tasks" /> matriz está vacía.</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory`1.CreationOptions">
      <summary>Obtiene el valor de enumeración <see cref="T:System.Threading.Tasks.TaskCreationOptions" /> para este generador de tareas.</summary>
      <returns>Uno de los valores de enumeración que especifica las opciones de creación predeterminadas para este generador de tareas.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},System.Object)">
      <summary>Crea una tarea que representa un par de métodos Begin y End que se ajustan al modelo de programación asincrónica.</summary>
      <returns>Tarea creada que representa la operación asincrónica.</returns>
      <param name="beginMethod">Delegado que comienza la operación asincrónica.</param>
      <param name="endMethod">Delegado que finaliza la operación asincrónica.</param>
      <param name="state">Objeto que contiene los datos que el delegado de <paramref name="beginMethod" /> va a usar.</param>
      <exception cref="T:System.ArgumentNullException">El argumento <paramref name="beginMethod" /> es null.o bienEl argumento <paramref name="endMethod" /> es null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crea una tarea que representa un par de métodos Begin y End que se ajustan al modelo de programación asincrónica.</summary>
      <returns>Objeto <see cref="T:System.Threading.Tasks.Task`1" /> creado que representa la operación asincrónica.</returns>
      <param name="beginMethod">Delegado que comienza la operación asincrónica.</param>
      <param name="endMethod">Delegado que finaliza la operación asincrónica.</param>
      <param name="state">Objeto que contiene los datos que el delegado de <paramref name="beginMethod" /> va a usar.</param>
      <param name="creationOptions">Uno de los valores de enumeración que controla el comportamiento de la tarea creada.</param>
      <exception cref="T:System.ArgumentNullException">El argumento <paramref name="beginMethod" /> es null.o bienEl argumento <paramref name="endMethod" /> es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El <paramref name="creationOptions" /> argumento especifica un valor no válido.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``1(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,System.Object)">
      <summary>Crea una tarea que representa un par de métodos Begin y End que se ajustan al modelo de programación asincrónica.</summary>
      <returns>Tarea creada que representa la operación asincrónica.</returns>
      <param name="beginMethod">Delegado que comienza la operación asincrónica.</param>
      <param name="endMethod">Delegado que finaliza la operación asincrónica.</param>
      <param name="arg1">Primer argumento que se pasa al delegado de <paramref name="beginMethod" />.</param>
      <param name="state">Objeto que contiene los datos que el delegado de <paramref name="beginMethod" /> va a usar.</param>
      <typeparam name="TArg1">Tipo del primer argumento que se pasa al delegado de <paramref name="beginMethod" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">El argumento <paramref name="beginMethod" /> es null.o bienEl argumento <paramref name="endMethod" /> es null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``1(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crea una tarea que representa un par de métodos Begin y End que se ajustan al modelo de programación asincrónica.</summary>
      <returns>Tarea creada que representa la operación asincrónica.</returns>
      <param name="beginMethod">Delegado que comienza la operación asincrónica.</param>
      <param name="endMethod">Delegado que finaliza la operación asincrónica.</param>
      <param name="arg1">Primer argumento que se pasa al delegado de <paramref name="beginMethod" />.</param>
      <param name="state">Objeto que contiene los datos que el delegado de <paramref name="beginMethod" /> va a usar.</param>
      <param name="creationOptions">Uno de los valores de enumeración que controla el comportamiento de la tarea creada.</param>
      <typeparam name="TArg1">Tipo del primer argumento que se pasa al delegado de <paramref name="beginMethod" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">El argumento <paramref name="beginMethod" /> es null.o bienEl argumento <paramref name="endMethod" /> es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El <paramref name="creationOptions" /> parámetro especifica un valor no válido.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``2(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,``1,System.Object)">
      <summary>Crea una tarea que representa un par de métodos Begin y End que se ajustan al modelo de programación asincrónica.</summary>
      <returns>Tarea creada que representa la operación asincrónica.</returns>
      <param name="beginMethod">Delegado que comienza la operación asincrónica.</param>
      <param name="endMethod">Delegado que finaliza la operación asincrónica.</param>
      <param name="arg1">Primer argumento que se pasa al delegado de <paramref name="beginMethod" />.</param>
      <param name="arg2">Segundo argumento que se pasa al delegado de <paramref name="beginMethod" />.</param>
      <param name="state">Objeto que contiene los datos que el delegado de <paramref name="beginMethod" /> va a usar.</param>
      <typeparam name="TArg1">Tipo del segundo argumento que se pasa al delegado de <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg2">Tipo del primer argumento que se pasa al delegado de <paramref name="beginMethod" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">El argumento <paramref name="beginMethod" /> es null.o bienEl argumento <paramref name="endMethod" /> es null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``2(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,``1,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crea una tarea que representa un par de métodos Begin y End que se ajustan al modelo de programación asincrónica.</summary>
      <returns>Tarea creada que representa la operación asincrónica.</returns>
      <param name="beginMethod">Delegado que comienza la operación asincrónica.</param>
      <param name="endMethod">Delegado que finaliza la operación asincrónica.</param>
      <param name="arg1">Primer argumento que se pasa al delegado de <paramref name="beginMethod" />.</param>
      <param name="arg2">Segundo argumento que se pasa al delegado de <paramref name="beginMethod" />.</param>
      <param name="state">Objeto que contiene los datos que el delegado de <paramref name="beginMethod" /> va a usar.</param>
      <param name="creationOptions">Objeto que controla el comportamiento del <see cref="T:System.Threading.Tasks.Task`1" /> creado.</param>
      <typeparam name="TArg1">Tipo del segundo argumento que se pasa al delegado de <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg2">Tipo del primer argumento que se pasa al delegado de <paramref name="beginMethod" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">El argumento <paramref name="beginMethod" /> es null.o bienEl argumento <paramref name="endMethod" /> es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El <paramref name="creationOptions" /> parámetro especifica un valor no válido.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``3(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,``1,``2,System.Object)">
      <summary>Crea una tarea que representa un par de métodos Begin y End que se ajustan al modelo de programación asincrónica.</summary>
      <returns>Tarea creada que representa la operación asincrónica.</returns>
      <param name="beginMethod">Delegado que comienza la operación asincrónica.</param>
      <param name="endMethod">Delegado que finaliza la operación asincrónica.</param>
      <param name="arg1">Primer argumento que se pasa al delegado de <paramref name="beginMethod" />.</param>
      <param name="arg2">Segundo argumento que se pasa al delegado de <paramref name="beginMethod" />.</param>
      <param name="arg3">Tercer argumento que se pasa al delegado de <paramref name="beginMethod" />.</param>
      <param name="state">Objeto que contiene los datos que el delegado de <paramref name="beginMethod" /> va a usar.</param>
      <typeparam name="TArg1">Tipo del segundo argumento que se pasa al delegado de <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg2">Tipo del tercer argumento que se pasa al delegado de <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg3">Tipo del primer argumento que se pasa al delegado de <paramref name="beginMethod" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">El argumento <paramref name="beginMethod" /> es null.o bienEl argumento <paramref name="endMethod" /> es null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync``3(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,``1,``2,System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crea una tarea que representa un par de métodos Begin y End que se ajustan al modelo de programación asincrónica.</summary>
      <returns>Tarea creada que representa la operación asincrónica.</returns>
      <param name="beginMethod">Delegado que comienza la operación asincrónica.</param>
      <param name="endMethod">Delegado que finaliza la operación asincrónica.</param>
      <param name="arg1">Primer argumento que se pasa al delegado de <paramref name="beginMethod" />.</param>
      <param name="arg2">Segundo argumento que se pasa al delegado de <paramref name="beginMethod" />.</param>
      <param name="arg3">Tercer argumento que se pasa al delegado de <paramref name="beginMethod" />.</param>
      <param name="state">Objeto que contiene los datos que el delegado de <paramref name="beginMethod" /> va a usar.</param>
      <param name="creationOptions">Un objeto que controla el comportamiento de la tarea creada.</param>
      <typeparam name="TArg1">Tipo del segundo argumento que se pasa al delegado de <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg2">Tipo del tercer argumento que se pasa al delegado de <paramref name="beginMethod" />.</typeparam>
      <typeparam name="TArg3">Tipo del primer argumento que se pasa al delegado de <paramref name="beginMethod" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">El argumento <paramref name="beginMethod" /> es null.o bienEl argumento <paramref name="endMethod" /> es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El <paramref name="creationOptions" /> parámetro especifica un valor no válido.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync(System.IAsyncResult,System.Func{System.IAsyncResult,`0})">
      <summary>Crea una tarea que ejecuta una función del método End cuando se completa el <see cref="T:System.IAsyncResult" /> especificado.</summary>
      <returns>Objeto <see cref="T:System.Threading.Tasks.Task`1" /> que representa la operación asincrónica.</returns>
      <param name="asyncResult">
        <see cref="T:System.IAsyncResult" /> cuya finalización debe desencadenar el procesamiento de <paramref name="endMethod" />.</param>
      <param name="endMethod">Delegado de función que procesa el objeto <paramref name="asyncResult" /> completado.</param>
      <exception cref="T:System.ArgumentNullException">El argumento <paramref name="asyncResult" /> es null.o bienEl argumento <paramref name="endMethod" /> es null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync(System.IAsyncResult,System.Func{System.IAsyncResult,`0},System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crea una tarea que ejecuta una función del método End cuando se completa el <see cref="T:System.IAsyncResult" /> especificado.</summary>
      <returns>Tarea que representa la operación asincrónica.</returns>
      <param name="asyncResult">
        <see cref="T:System.IAsyncResult" /> cuya finalización debe desencadenar el procesamiento de <paramref name="endMethod" />.</param>
      <param name="endMethod">Delegado de función que procesa el objeto <paramref name="asyncResult" /> completado.</param>
      <param name="creationOptions">Uno de los valores de enumeración que controla el comportamiento de la tarea creada.</param>
      <exception cref="T:System.ArgumentNullException">El argumento <paramref name="asyncResult" /> es null.o bienEl argumento <paramref name="endMethod" /> es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El <paramref name="creationOptions" /> argumento especifica un valor no válido.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.FromAsync(System.IAsyncResult,System.Func{System.IAsyncResult,`0},System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea una tarea que ejecuta una función del método End cuando se completa el <see cref="T:System.IAsyncResult" /> especificado.</summary>
      <returns>Tarea creada que representa la operación asincrónica.</returns>
      <param name="asyncResult">
        <see cref="T:System.IAsyncResult" /> cuya finalización debe desencadenar el procesamiento de <paramref name="endMethod" />.</param>
      <param name="endMethod">Delegado de función que procesa el objeto <paramref name="asyncResult" /> completado.</param>
      <param name="creationOptions">Uno de los valores de enumeración que controla el comportamiento de la tarea creada.</param>
      <param name="scheduler">El programador de tareas que se usa para programar la tarea que ejecuta el método End.</param>
      <exception cref="T:System.ArgumentNullException">El argumento <paramref name="asyncResult" /> es null.o bienEl argumento <paramref name="endMethod" /> es null.o bienEl argumento <paramref name="scheduler" /> es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El <paramref name="creationOptions" /> parámetro especifica un valor no válido.</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskFactory`1.Scheduler">
      <summary>Obtiene el programador de tareas para este generador de tareas.</summary>
      <returns>El programador de tareas para este generador de tareas.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{`0})">
      <summary>Crea e inicia una tarea.</summary>
      <returns>La tarea iniciada.</returns>
      <param name="function">Delegado de función que devuelve el resultado futuro que va a estar disponible a través de la tarea.</param>
      <exception cref="T:System.ArgumentNullException">El argumento <paramref name="function" /> es null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{`0},System.Threading.CancellationToken)">
      <summary>Crea e inicia una tarea.</summary>
      <returns>La tarea iniciada.</returns>
      <param name="function">Delegado de función que devuelve el resultado futuro que va a estar disponible a través de la tarea.</param>
      <param name="cancellationToken">Token de cancelación que se va a asignar a la nueva tarea.</param>
      <exception cref="T:System.ObjectDisposedException">El token de cancelación de origen que crearon<paramref name="cancellationToken" /> ya se ha eliminado.</exception>
      <exception cref="T:System.ArgumentNullException">El argumento <paramref name="function" /> es null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{`0},System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea e inicia una tarea.</summary>
      <returns>La tarea iniciada.</returns>
      <param name="function">Delegado de función que devuelve el resultado futuro que va a estar disponible a través de la tarea.</param>
      <param name="cancellationToken">Token de cancelación que se va a asignar a la nueva tarea.</param>
      <param name="creationOptions">Uno de los valores de enumeración que controla el comportamiento de la tarea creada.</param>
      <param name="scheduler">Programador de tareas que se usó para programar la tarea creada.</param>
      <exception cref="T:System.ObjectDisposedException">El token de cancelación de origen que crearon<paramref name="cancellationToken" /> ya se ha eliminado.</exception>
      <exception cref="T:System.ArgumentNullException">El argumento <paramref name="function" /> es null.o bienEl argumento <paramref name="scheduler" /> es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El <paramref name="creationOptions" /> parámetro especifica un valor no válido.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{`0},System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crea e inicia una tarea.</summary>
      <returns>Objeto <see cref="T:System.Threading.Tasks.Task`1" /> iniciado.</returns>
      <param name="function">Delegado de función que devuelve el resultado futuro que va a estar disponible a través de la tarea.</param>
      <param name="creationOptions">Uno de los valores de enumeración que controla el comportamiento de la tarea creada.</param>
      <exception cref="T:System.ArgumentNullException">El argumento <paramref name="function" /> es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El <paramref name="creationOptions" /> parámetro especifica un valor no válido.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{System.Object,`0},System.Object)">
      <summary>Crea e inicia una tarea.</summary>
      <returns>La tarea iniciada.</returns>
      <param name="function">Delegado de función que devuelve el resultado futuro que va a estar disponible a través de la tarea.</param>
      <param name="state">Objeto que contiene los datos que el delegado de <paramref name="function" /> va a usar.</param>
      <exception cref="T:System.ArgumentNullException">El argumento <paramref name="function" /> es null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{System.Object,`0},System.Object,System.Threading.CancellationToken)">
      <summary>Crea e inicia una tarea.</summary>
      <returns>La tarea iniciada.</returns>
      <param name="function">Delegado de función que devuelve el resultado futuro que va a estar disponible a través de la tarea.</param>
      <param name="state">Objeto que contiene los datos que el delegado de <paramref name="function" /> va a usar.</param>
      <param name="cancellationToken">Token de cancelación que se va a asignar a la nueva tarea.</param>
      <exception cref="T:System.ObjectDisposedException">El token de cancelación de origen que crearon<paramref name="cancellationToken" /> ya se ha eliminado.</exception>
      <exception cref="T:System.ArgumentNullException">El argumento <paramref name="function" /> es null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{System.Object,`0},System.Object,System.Threading.CancellationToken,System.Threading.Tasks.TaskCreationOptions,System.Threading.Tasks.TaskScheduler)">
      <summary>Crea e inicia una tarea.</summary>
      <returns>La tarea iniciada.</returns>
      <param name="function">Delegado de función que devuelve el resultado futuro que va a estar disponible a través de la tarea.</param>
      <param name="state">Objeto que contiene los datos que el delegado de <paramref name="function" /> va a usar.</param>
      <param name="cancellationToken">Token de cancelación que se va a asignar a la nueva tarea.</param>
      <param name="creationOptions">Uno de los valores de enumeración que controla el comportamiento de la tarea creada.</param>
      <param name="scheduler">Programador de tareas que se usó para programar la tarea creada.</param>
      <exception cref="T:System.ObjectDisposedException">El token de cancelación de origen que crearon<paramref name="cancellationToken" /> ya se ha eliminado.</exception>
      <exception cref="T:System.ArgumentNullException">El argumento <paramref name="function" /> es null.o bienEl argumento <paramref name="scheduler" /> es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El <paramref name="creationOptions" /> parámetro especifica un valor no válido.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskFactory`1.StartNew(System.Func{System.Object,`0},System.Object,System.Threading.Tasks.TaskCreationOptions)">
      <summary>Crea e inicia una tarea.</summary>
      <returns>La tarea iniciada.</returns>
      <param name="function">Delegado de función que devuelve el resultado futuro que va a estar disponible a través de la tarea.</param>
      <param name="state">Objeto que contiene los datos que el delegado de <paramref name="function" /> va a usar.</param>
      <param name="creationOptions">Uno de los valores de enumeración que controla el comportamiento de la tarea creada.</param>
      <exception cref="T:System.ArgumentNullException">El argumento <paramref name="function" /> es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El <paramref name="creationOptions" /> parámetro especifica un valor no válido.</exception>
    </member>
    <member name="T:System.Threading.Tasks.TaskScheduler">
      <summary>Representa un objeto que administra el trabajo de bajo nivel de la puesta en cola de las tareas en los subprocesos.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.#ctor">
      <summary>Inicializa el objeto <see cref="T:System.Threading.Tasks.TaskScheduler" />.</summary>
    </member>
    <member name="P:System.Threading.Tasks.TaskScheduler.Current">
      <summary>Obtiene el objeto <see cref="T:System.Threading.Tasks.TaskScheduler" /> asociado a la tarea que se está ejecutando actualmente.</summary>
      <returns>Devuelve el objeto <see cref="T:System.Threading.Tasks.TaskScheduler" /> asociado a la tarea que se está ejecutando actualmente.</returns>
    </member>
    <member name="P:System.Threading.Tasks.TaskScheduler.Default">
      <summary>Obtiene la instancia predeterminada de <see cref="T:System.Threading.Tasks.TaskScheduler" /> proporcionada por .NET Framework.</summary>
      <returns>Devuelve la instancia predeterminada de <see cref="T:System.Threading.Tasks.TaskScheduler" />.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.FromCurrentSynchronizationContext">
      <summary>Crea un objeto <see cref="T:System.Threading.Tasks.TaskScheduler" /> asociado a la instancia actual de <see cref="T:System.Threading.SynchronizationContext" />.</summary>
      <returns>Objeto <see cref="T:System.Threading.Tasks.TaskScheduler" /> asociado a la instancia actual de <see cref="T:System.Threading.SynchronizationContext" />, tal y como determina la propiedad <see cref="P:System.Threading.SynchronizationContext.Current" />.</returns>
      <exception cref="T:System.InvalidOperationException">El SynchronizationContext actual no puede usarse como TaskScheduler.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.GetScheduledTasks">
      <summary>Solo por compatibilidad con el depurador, genera un enumerable de las instancias de <see cref="T:System.Threading.Tasks.Task" /> que se encuentran actualmente en la cola del programador a la espera de ser ejecutadas.</summary>
      <returns>Enumerable que permite a un depurador atravesar las tareas que se encuentran actualmente en la cola de este programador.</returns>
      <exception cref="T:System.NotSupportedException">Este programador no puede generar una lista de tareas en cola en este momento.</exception>
    </member>
    <member name="P:System.Threading.Tasks.TaskScheduler.Id">
      <summary>Obtiene el identificador único de este <see cref="T:System.Threading.Tasks.TaskScheduler" />.</summary>
      <returns>Devuelve el identificador único de este objeto <see cref="T:System.Threading.Tasks.TaskScheduler" />.</returns>
    </member>
    <member name="P:System.Threading.Tasks.TaskScheduler.MaximumConcurrencyLevel">
      <summary>Indica el nivel de simultaneidad máximo admitido por este <see cref="T:System.Threading.Tasks.TaskScheduler" />.</summary>
      <returns>Devuelve un entero que representa el nivel máximo de simultaneidad.El programador predeterminado devuelve la propiedad <see cref="F:System.Int32.MaxValue" />.</returns>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.QueueTask(System.Threading.Tasks.Task)">
      <summary>Pone un objeto <see cref="T:System.Threading.Tasks.Task" /> en la cola del programador. </summary>
      <param name="task">Objeto <see cref="T:System.Threading.Tasks.Task" /> que se va a poner en la cola.</param>
      <exception cref="T:System.ArgumentNullException">El argumento de <paramref name="task" /> es nulo.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.TryDequeue(System.Threading.Tasks.Task)">
      <summary>Intenta quitar un objeto <see cref="T:System.Threading.Tasks.Task" /> de la cola de este programador.</summary>
      <returns>Valor booleano que indica si el argumento <paramref name="task" /> se quitó correctamente de la cola.</returns>
      <param name="task">Objeto <see cref="T:System.Threading.Tasks.Task" /> que se va a quitar de la cola.</param>
      <exception cref="T:System.ArgumentNullException">El argumento de <paramref name="task" /> es nulo.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.TryExecuteTask(System.Threading.Tasks.Task)">
      <summary>Intenta ejecutar el objeto <see cref="T:System.Threading.Tasks.Task" /> especificado en este programador.</summary>
      <returns>Valor booleano que es true si <paramref name="task" /> se ejecutó correctamente; de lo contrario, es false.Normalmente, los errores de ejecución son debidos a que la tarea ya se ha ejecutado anteriormente o está a punto de ser ejecutada por otro subproceso.</returns>
      <param name="task">Objeto <see cref="T:System.Threading.Tasks.Task" /> que se va a ejecutar.</param>
      <exception cref="T:System.InvalidOperationException">La <paramref name="task" /> no está asociada a este programador.</exception>
    </member>
    <member name="M:System.Threading.Tasks.TaskScheduler.TryExecuteTaskInline(System.Threading.Tasks.Task,System.Boolean)">
      <summary>Determina si el objeto <see cref="T:System.Threading.Tasks.Task" /> especificado puede ejecutarse sincrónicamente en esta llamada y, en caso afirmativo, lo ejecuta.</summary>
      <returns>Valor booleano que indica si se ejecutó la tarea alineada.</returns>
      <param name="task">Objeto <see cref="T:System.Threading.Tasks.Task" /> que se va a ejecutar.</param>
      <param name="taskWasPreviouslyQueued">Valor booleano que indica si la tarea se ha puesto anteriormente en la cola.Si este parámetro es True, la tarea se ha puesto en la cola (programado) anteriormente; si su valor es False, la tarea no se ha puesto anteriormente en la cola y esta llamada se realiza para ejecutar la tarea insertada sin ponerla en la cola.</param>
      <exception cref="T:System.ArgumentNullException">El argumento de <paramref name="task" /> es nulo.</exception>
      <exception cref="T:System.InvalidOperationException">La <paramref name="task" /> ya se ejecutó.</exception>
    </member>
    <member name="E:System.Threading.Tasks.TaskScheduler.UnobservedTaskException">
      <summary>Se produce cuando una excepción no controlada de una tarea con un error está a punto de desencadenar la directiva de elevación de excepciones, lo que de forma predeterminada terminaría el proceso.</summary>
    </member>
    <member name="T:System.Threading.Tasks.TaskSchedulerException">
      <summary>Representa una excepción que se usa para comunicar una operación no válida de un objeto <see cref="T:System.Threading.Tasks.TaskScheduler" />.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskSchedulerException.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.Tasks.TaskSchedulerException" /> con un mensaje proporcionado por el sistema que describe el error.</summary>
    </member>
    <member name="M:System.Threading.Tasks.TaskSchedulerException.#ctor(System.Exception)">
      <summary>Inicializa una instancia nueva de la clase <see cref="T:System.Threading.Tasks.TaskSchedulerException" /> con el mensaje de error especificado y una referencia a la excepción interna que representa la causa de esta excepción.</summary>
      <param name="innerException">La excepción que es la causa de la excepción actual.</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskSchedulerException.#ctor(System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.Tasks.TaskSchedulerException" /> con un mensaje de error especificado que describe el error.</summary>
      <param name="message">Mensaje que describe la excepción.El llamador de este constructor debe asegurarse de que esta cadena se ha traducido para la actual referencia cultural del sistema.</param>
    </member>
    <member name="M:System.Threading.Tasks.TaskSchedulerException.#ctor(System.String,System.Exception)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.Tasks.TaskSchedulerException" /> con el mensaje de error especificado y una referencia a la excepción interna que representa la causa de esta excepción.</summary>
      <param name="message">Mensaje que describe la excepción.El llamador de este constructor debe asegurarse de que esta cadena se ha traducido para la actual referencia cultural del sistema.</param>
      <param name="innerException">La excepción que es la causa de la excepción actual.Si el parámetro <paramref name="innerException" /> no es null, la excepción actual se produce en un bloque catch que controla la excepción interna.</param>
    </member>
    <member name="T:System.Threading.Tasks.TaskStatus">
      <summary>Representa la fase actual del ciclo de vida de una <see cref="T:System.Threading.Tasks.Task" />.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.Canceled">
      <summary>La tarea confirmó la cancelación iniciando una excepción OperationCanceledException con su propio CancellationToken mientras el token estaba en estado señalado o el CancellationToken de la tarea ya se había señalado antes de que la tarea empezara a ejecutarse.Para más información, vea Cancelación de tareas.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.Created">
      <summary>La tarea se ha inicializado pero aún no se ha programado.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.Faulted">
      <summary>La tarea se completó debido a una excepción no controlada.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.RanToCompletion">
      <summary>La tarea terminó de ejecutarse correctamente.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.Running">
      <summary>La tarea se está ejecutando pero aún no se ha completado.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.WaitingForActivation">
      <summary>La tarea está a la espera de ser activada y programada internamente por la infraestructura de .NET Framework.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.WaitingForChildrenToComplete">
      <summary>La tarea ha terminado de ejecutarse y está implícitamente a la espera de que se completen las tareas secundarias asociadas.</summary>
    </member>
    <member name="F:System.Threading.Tasks.TaskStatus.WaitingToRun">
      <summary>Se ha programado la ejecución de la tarea pero la ejecución aún no ha comenzado.</summary>
    </member>
    <member name="T:System.Threading.Tasks.UnobservedTaskExceptionEventArgs">
      <summary>Proporciona datos para el evento que se genera cuando una excepción del objeto <see cref="T:System.Threading.Tasks.Task" /> erróneo pasa inadvertida.</summary>
    </member>
    <member name="M:System.Threading.Tasks.UnobservedTaskExceptionEventArgs.#ctor(System.AggregateException)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Threading.Tasks.UnobservedTaskExceptionEventArgs" /> con la excepción inadvertida.</summary>
      <param name="exception">Excepción que ha pasado inadvertida.</param>
    </member>
    <member name="P:System.Threading.Tasks.UnobservedTaskExceptionEventArgs.Exception">
      <summary>Excepción que pasó inadvertida.</summary>
      <returns>Excepción que pasó inadvertida.</returns>
    </member>
    <member name="P:System.Threading.Tasks.UnobservedTaskExceptionEventArgs.Observed">
      <summary>Obtiene datos sobre si esta excepción se ha marcado como "advertida".</summary>
      <returns>True, si esta excepción se ha marcado como "observed"; de lo contrario, false.</returns>
    </member>
    <member name="M:System.Threading.Tasks.UnobservedTaskExceptionEventArgs.SetObserved">
      <summary>Marca <see cref="P:System.Threading.Tasks.UnobservedTaskExceptionEventArgs.Exception" /> como "advertida", evitando así que desencadene la directiva de elevación de excepciones que, de forma predeterminada, termina el proceso.</summary>
    </member>
  </members>
</doc>