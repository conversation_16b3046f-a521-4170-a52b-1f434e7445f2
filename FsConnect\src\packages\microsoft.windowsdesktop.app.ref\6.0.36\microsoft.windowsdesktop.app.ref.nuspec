﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2011/08/nuspec.xsd">
  <metadata>
    <id>Microsoft.WindowsDesktop.App.Ref</id>
    <version>6.0.36</version>
    <authors>Microsoft</authors>
    <requireLicenseAcceptance>true</requireLicenseAcceptance>
    <license type="file">LICENSE</license>
    <licenseUrl>https://aka.ms/deprecateLicenseUrl</licenseUrl>
    <icon>Icon.png</icon>
    <projectUrl>https://github.com/dotnet/windowsdesktop</projectUrl>
    <description>Package Description</description>
    <copyright>© Microsoft Corporation. All rights reserved.</copyright>
    <serviceable>true</serviceable>
    <packageTypes>
      <packageType name="DotnetPlatform" />
    </packageTypes>
    <repository type="git" url="https://github.com/dotnet/windowsdesktop" commit="320a638db589cc9b24e3e5217d7ca34df840104a" />
  </metadata>
</package>