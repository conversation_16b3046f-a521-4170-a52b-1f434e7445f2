{"version": 3, "targets": {"net8.0": {"Serilog/2.10.0": {"type": "package", "compile": {"lib/netstandard2.1/Serilog.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Serilog.dll": {"related": ".xml"}}}, "CTrue.FsConnect/1.4.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"Serilog": "2.10.0"}, "compile": {"bin/placeholder/CTrue.FsConnect.dll": {}}, "runtime": {"bin/placeholder/CTrue.FsConnect.dll": {}}}}}, "libraries": {"Serilog/2.10.0": {"sha512": "+QX0hmf37a0/OZLxM3wL7V6/ADvC1XihXN4Kq/p6d8lCPfgkRdiuhbWlMaFjR9Av0dy5F0+MBeDmDdRZN/YwQA==", "type": "package", "path": "serilog/2.10.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net45/Serilog.dll", "lib/net45/Serilog.xml", "lib/net46/Serilog.dll", "lib/net46/Serilog.xml", "lib/netstandard1.0/Serilog.dll", "lib/netstandard1.0/Serilog.xml", "lib/netstandard1.3/Serilog.dll", "lib/netstandard1.3/Serilog.xml", "lib/netstandard2.0/Serilog.dll", "lib/netstandard2.0/Serilog.xml", "lib/netstandard2.1/Serilog.dll", "lib/netstandard2.1/Serilog.xml", "serilog.2.10.0.nupkg.sha512", "serilog.nuspec"]}, "CTrue.FsConnect/1.4.0": {"type": "project", "path": "../CTrue.FsConnect/CTrue.FsConnect.csproj", "msbuildProject": "../CTrue.FsConnect/CTrue.FsConnect.csproj"}}, "projectFileDependencyGroups": {"net8.0": ["CTrue.FsConnect >= 1.4.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.4.0", "restore": {"projectUniqueName": "C:\\dev\\Personal\\FlightPig\\FsConnect\\src\\CTrue.FsConnect.ExampleConsole\\CTrue.FsConnect.ExampleConsole.csproj", "projectName": "CTrue.FsConnect.ExampleConsole", "projectPath": "C:\\dev\\Personal\\FlightPig\\FsConnect\\src\\CTrue.FsConnect.ExampleConsole\\CTrue.FsConnect.ExampleConsole.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\dev\\Personal\\FlightPig\\FsConnect\\src\\CTrue.FsConnect.ExampleConsole\\obj\\", "projectStyle": "PackageReference", "centralPackageVersionsManagementEnabled": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\dev\\Personal\\FlightPig\\FsConnect\\src\\CTrue.FsConnect\\CTrue.FsConnect.csproj": {"projectPath": "C:\\dev\\Personal\\FlightPig\\FsConnect\\src\\CTrue.FsConnect\\CTrue.FsConnect.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "centralPackageVersions": {"CommandLineParser": "[2.8.0, )", "Microsoft.NET.Test.Sdk": "[16.9.1, )", "NUnit": "[3.13.1, )", "NUnit3TestAdapter": "[3.17.0, )", "Serilog": "[2.10.0, )", "Serilog.Sinks.Console": "[3.1.1, )"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.408/PortableRuntimeIdentifierGraph.json"}}}}