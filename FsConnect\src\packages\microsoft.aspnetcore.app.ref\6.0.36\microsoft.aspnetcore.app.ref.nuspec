﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2011/08/nuspec.xsd">
  <metadata>
    <id>Microsoft.AspNetCore.App.Ref</id>
    <version>6.0.36</version>
    <authors>Microsoft</authors>
    <requireLicenseAcceptance>true</requireLicenseAcceptance>
    <license type="expression">MIT</license>
    <licenseUrl>https://licenses.nuget.org/MIT</licenseUrl>
    <icon>Icon.png</icon>
    <projectUrl>https://asp.net/</projectUrl>
    <description>Provides a default set of APIs for building an ASP.NET Core application. Contains reference assemblies, documentation, and other design-time assets.

This package is an internal implementation of the .NET Core SDK and is not meant to be used as a normal PackageReference.

This package was built from the source code at https://github.com/dotnet/aspnetcore/tree/64ea4108e7dcf1ca575f8dd2028363b0b1ef6ebc</description>
    <copyright>© Microsoft Corporation. All rights reserved.</copyright>
    <tags>aspnetcore targeting-pack</tags>
    <serviceable>true</serviceable>
    <packageTypes>
      <packageType name="DotnetPlatform" />
    </packageTypes>
    <repository type="git" url="https://github.com/dotnet/aspnetcore" commit="64ea4108e7dcf1ca575f8dd2028363b0b1ef6ebc" />
  </metadata>
</package>