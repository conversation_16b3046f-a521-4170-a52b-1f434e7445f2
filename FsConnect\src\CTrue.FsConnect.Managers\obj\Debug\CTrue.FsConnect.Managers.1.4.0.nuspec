﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>CTrue.FsConnect.Managers</id>
    <version>1.4.0</version>
    <authors>C-True</authors>
    <requireLicenseAcceptance>true</requireLicenseAcceptance>
    <license type="file">LICENSE.txt</license>
    <licenseUrl>https://aka.ms/deprecateLicenseUrl</licenseUrl>
    <description>Managers that supports common operations working with Microsoft Flightsimulator 2020, through the use of FsConnect.
      Supports:
        - Aircraft information
        - Retrieving sim objects
        - Setting world time
        - Controlling COM and NAV frequencies
        - Controlling transponder code
        - Initial Autopilot manager</description>
    <releaseNotes>Updated SDK version to ******** * Upgraded to .NET 6.0 with C# 8.0 support</releaseNotes>
    <tags>msfs flight-simulator simconnect</tags>
    <repository type="git" url="https://github.com/c-true/FsConnect" commit="348053d46ede6b6e3e9ef2c97c85f29ebaaaadf9" />
    <dependencies>
      <group targetFramework="net8.0">
        <dependency id="CTrue.FsConnect" version="1.4.0" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
  <files>
    <file src="C:\dev\Personal\FlightPig\FsConnect\src\CTrue.FsConnect.Managers\bin\Debug\net8.0\CTrue.FsConnect.Managers.dll" target="lib\net8.0\CTrue.FsConnect.Managers.dll" />
    <file src="C:\dev\Personal\FlightPig\FsConnect\src\CTrue.FsConnect.Managers\bin\Debug\net8.0\CTrue.FsConnect.Managers.xml" target="lib\net8.0\CTrue.FsConnect.Managers.xml" />
    <file src="C:\dev\Personal\FlightPig\FsConnect\src\CTrue.FsConnect.Managers\licenses\LICENSE.txt" target="LICENSE.txt" />
  </files>
</package>