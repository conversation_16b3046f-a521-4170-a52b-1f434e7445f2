<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Extensions.Logging.EventLog</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Extensions.Logging.EventLoggerFactoryExtensions">
            <summary>
            Extension methods for the <see cref="T:Microsoft.Extensions.Logging.ILoggerFactory"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.EventLoggerFactoryExtensions.AddEventLog(Microsoft.Extensions.Logging.ILoggingBuilder)">
            <summary>
            Adds an event logger named 'EventLog' to the factory.
            </summary>
            <param name="builder">The extension method argument.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.EventLoggerFactoryExtensions.AddEventLog(Microsoft.Extensions.Logging.ILoggingBuilder,Microsoft.Extensions.Logging.EventLog.EventLogSettings)">
            <summary>
            Adds an event logger. Use <paramref name="settings"/> to enable logging for specific <see cref="T:Microsoft.Extensions.Logging.LogLevel"/>s.
            </summary>
            <param name="builder">The extension method argument.</param>
            <param name="settings">The <see cref="T:Microsoft.Extensions.Logging.EventLog.EventLogSettings"/>.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.EventLoggerFactoryExtensions.AddEventLog(Microsoft.Extensions.Logging.ILoggingBuilder,System.Action{Microsoft.Extensions.Logging.EventLog.EventLogSettings})">
            <summary>
            Adds an event logger. Use <paramref name="configure"/> to enable logging for specific <see cref="T:Microsoft.Extensions.Logging.LogLevel"/>s.
            </summary>
            <param name="builder">The extension method argument.</param>
            <param name="configure">A delegate to configure the <see cref="T:Microsoft.Extensions.Logging.EventLog.EventLogSettings"/>.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> so that additional calls can be chained.</returns>
        </member>
        <member name="T:Microsoft.Extensions.Logging.EventLog.EventLogLogger">
            <summary>
            A logger that writes messages to Windows Event Log.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.EventLog.EventLogLogger.#ctor(System.String,Microsoft.Extensions.Logging.EventLog.EventLogSettings,Microsoft.Extensions.Logging.IExternalScopeProvider)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Extensions.Logging.EventLog.EventLogLogger"/> class.
            </summary>
            <param name="name">The name of the logger.</param>
            <param name="settings">The <see cref="T:Microsoft.Extensions.Logging.EventLog.EventLogSettings"/>.</param>
            <param name="externalScopeProvider">The <see cref="T:Microsoft.Extensions.Logging.IExternalScopeProvider"/>.</param>
        </member>
        <member name="M:Microsoft.Extensions.Logging.EventLog.EventLogLogger.BeginScope``1(``0)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Extensions.Logging.EventLog.EventLogLogger.IsEnabled(Microsoft.Extensions.Logging.LogLevel)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Extensions.Logging.EventLog.EventLogLogger.Log``1(Microsoft.Extensions.Logging.LogLevel,Microsoft.Extensions.Logging.EventId,``0,System.Exception,System.Func{``0,System.Exception,System.String})">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.Extensions.Logging.EventLog.EventLogLoggerProvider">
            <summary>
            The provider for the <see cref="T:Microsoft.Extensions.Logging.EventLog.EventLogLogger"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.EventLog.EventLogLoggerProvider.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Extensions.Logging.EventLog.EventLogLoggerProvider"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.EventLog.EventLogLoggerProvider.#ctor(Microsoft.Extensions.Logging.EventLog.EventLogSettings)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Extensions.Logging.EventLog.EventLogLoggerProvider"/> class.
            </summary>
            <param name="settings">The <see cref="T:Microsoft.Extensions.Logging.EventLog.EventLogSettings"/>.</param>
        </member>
        <member name="M:Microsoft.Extensions.Logging.EventLog.EventLogLoggerProvider.#ctor(Microsoft.Extensions.Options.IOptions{Microsoft.Extensions.Logging.EventLog.EventLogSettings})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Extensions.Logging.EventLog.EventLogLoggerProvider"/> class.
            </summary>
            <param name="options">The <see cref="T:Microsoft.Extensions.Options.IOptions`1"/>.</param>
        </member>
        <member name="M:Microsoft.Extensions.Logging.EventLog.EventLogLoggerProvider.CreateLogger(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Extensions.Logging.EventLog.EventLogLoggerProvider.Dispose">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Extensions.Logging.EventLog.EventLogLoggerProvider.SetScopeProvider(Microsoft.Extensions.Logging.IExternalScopeProvider)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.Extensions.Logging.EventLog.EventLogSettings">
            <summary>
            Settings for <see cref="T:Microsoft.Extensions.Logging.EventLog.EventLogLogger"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Logging.EventLog.EventLogSettings.LogName">
            <summary>
            Name of the event log. If <c>null</c> or not specified, "Application" is the default.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Logging.EventLog.EventLogSettings.SourceName">
            <summary>
            Name of the event log source. If <c>null</c> or not specified, ".NET Runtime" is the default.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Logging.EventLog.EventLogSettings.MachineName">
            <summary>
            Name of the machine having the event log. If <c>null</c> or not specified, local machine is the default.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Logging.EventLog.EventLogSettings.Filter">
            <summary>
            The function used to filter events based on the log level.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Logging.NullExternalScopeProvider">
            <summary>
            Scope provider that does nothing.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Logging.NullExternalScopeProvider.Instance">
            <summary>
            Returns a cached instance of <see cref="T:Microsoft.Extensions.Logging.NullExternalScopeProvider"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.NullExternalScopeProvider.Microsoft#Extensions#Logging#IExternalScopeProvider#ForEachScope``1(System.Action{System.Object,``0},``0)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Extensions.Logging.NullExternalScopeProvider.Microsoft#Extensions#Logging#IExternalScopeProvider#Push(System.Object)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.Extensions.Logging.NullScope">
            <summary>
            An empty scope without any logic
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.NullScope.Dispose">
            <inheritdoc />
        </member>
        <member name="T:System.Runtime.Versioning.OSPlatformAttribute">
            <summary>
            Base type for all platform-specific API attributes.
            </summary>
        </member>
        <member name="T:System.Runtime.Versioning.TargetPlatformAttribute">
            <summary>
            Records the platform that the project targeted.
            </summary>
        </member>
        <member name="T:System.Runtime.Versioning.SupportedOSPlatformAttribute">
             <summary>
             Records the operating system (and minimum version) that supports an API. Multiple attributes can be
             applied to indicate support on multiple operating systems.
             </summary>
             <remarks>
             Callers can apply a <see cref="T:System.Runtime.Versioning.SupportedOSPlatformAttribute" />
             or use guards to prevent calls to APIs on unsupported operating systems.
            
             A given platform should only be specified once.
             </remarks>
        </member>
        <member name="T:System.Runtime.Versioning.UnsupportedOSPlatformAttribute">
            <summary>
            Marks APIs that were removed in a given operating system version.
            </summary>
            <remarks>
            Primarily used by OS bindings to indicate APIs that are only available in
            earlier versions.
            </remarks>
        </member>
        <member name="T:System.Runtime.Versioning.SupportedOSPlatformGuardAttribute">
             <summary>
             Annotates a custom guard field, property or method with a supported platform name and optional version.
             Multiple attributes can be applied to indicate guard for multiple supported platforms.
             </summary>
             <remarks>
             Callers can apply a <see cref="T:System.Runtime.Versioning.SupportedOSPlatformGuardAttribute" /> to a field, property or method
             and use that field, property or method in a conditional or assert statements in order to safely call platform specific APIs.
            
             The type of the field or property should be boolean, the method return type should be boolean in order to be used as platform guard.
             </remarks>
        </member>
        <member name="T:System.Runtime.Versioning.UnsupportedOSPlatformGuardAttribute">
             <summary>
             Annotates the custom guard field, property or method with an unsupported platform name and optional version.
             Multiple attributes can be applied to indicate guard for multiple unsupported platforms.
             </summary>
             <remarks>
             Callers can apply a <see cref="T:System.Runtime.Versioning.UnsupportedOSPlatformGuardAttribute" /> to a field, property or method
             and use that  field, property or method in a conditional or assert statements as a guard to safely call APIs unsupported on those platforms.
            
             The type of the field or property should be boolean, the method return type should be boolean in order to be used as platform guard.
             </remarks>
        </member>
    </members>
</doc>
