<CodeCoverage>
  
  <!--
    The communication timeout between the logger and its clients in miliseconds (minimum value is 10 seconds).
  -->
  <CommunicationTimeout>30000</CommunicationTimeout>

  <!-- 
    Determines if data should be collected from child processes.  If this value is false, data will only be collected from the process created by the logger.
    This should be set to true for Silverlight collection because most web browsers create multiple processes.
  -->
  <CollectFromChildProcesses>true</CollectFromChildProcesses>

  <!--
    Determines if managed instrumentation is verifiable.  If false, managed instrumentation will not be verifiable, but data collection will perform better.
    Unverifiable instrumentation will cause an exception if the code does not have the unverifiable code permission.
    This value must be true for Silverlight and other "less-than-full trust" applications.
    Only set this value to false if you are observing collection-related performance problems on a full-trust application.
  -->
  <UseVerifiableInstrumentation>true</UseVerifiableInstrumentation>

  <!--
    Determines if low integrity processes (such as "protected mode" IE8) can connect to the logger.
    Only enable this setting if you trust the code running in the low integrity process.
  -->
  <AllowLowIntegrityProcesses>true</AllowLowIntegrityProcesses>

  <!--
    Determines if auto-generated modules should be included for code coverage. Code for these modules is generated on the fly by the compiler.
    and the modules are tagged with a compilergenerated assembly attribute. Modules generated by ASP.NET for web pages(.aspx) come under this category.
  -->
  <ExcludeCompilerAutoGeneratedModules>true</ExcludeCompilerAutoGeneratedModules>

  <!--
    The list of additional users allowed to connect to the logger. The logger process owner will always be an allowed user.
  -->
  <AllowedUsers />

  <!--
    Additional paths to search for symbol files. Symbols must be found for modules to be instrumented.
  -->
  <SymbolSearchPaths />

  <!--
    About include/exclude lists:
    Empty "Include" clauses imply all; empty "Exclude" clauses imply none.
    Each element in the list is a regular expression (ECMAScript syntax).
    An item must first match at least one entry in the include list to be included.
    Included items must then not match any entries in the exclude list to remain included.
    It is considered an error to exclude all items from instrumentation as no data would be collected.
  -->

  <!--
    The module include/exclude list by the full path from where the module loaded.
    Entries in this list are case-insensitive.
  -->
  <ModulePaths />

  <!--
    The company name include/exclude list.  This is matched against the company name in a module's version information.
    Entries in this list are case-insensitive.
  -->
  <CompanyNames>
    <Exclude>
      <!-- Exclude all modules with "microsoft" in the company name. -->
      <CompanyName>.*microsoft.*</CompanyName>
    </Exclude>
  </CompanyNames>

  <!--
    The module public key token include/exclude list.
    For native modules, the public key token is derived from the signing certificate's thumbprint.
    Entries in this list are case-insensitive.
  -->
  <PublicKeyTokens>
    <Exclude>
      <!-- Microsoft public key token -->
      <PublicKeyToken>^B77A5C561934E089$</PublicKeyToken>
      <PublicKeyToken>^B03F5F7F11D50A3A$</PublicKeyToken>
      <PublicKeyToken>^31BF3856AD364E35$</PublicKeyToken>
      <PublicKeyToken>^89845DCD8080CC91$</PublicKeyToken>
      <PublicKeyToken>^71E9BCE111E9429C$</PublicKeyToken>
      <PublicKeyToken>^8F50407C4E9E73B6$</PublicKeyToken>
      <PublicKeyToken>^E361AF139669C375$</PublicKeyToken>
    </Exclude>
  </PublicKeyTokens>

  <!--
    The source file path include/exclude list.  This is matched against source information in a .pdb.
    Entries in this list are case-insensitive.
  -->
  <Sources>
    <Exclude>
      <!-- Exclude files from ATL/MFC -->
      <Source>.*\\atlmfc\\.*</Source>
      <!-- Exclude files from the CRT -->
      <Source>.*\\vctools\\.*</Source>
      <!-- Exclude files from Microsoft's SDKs -->
      <Source>.*\\public\\sdk\\.*</Source>
      <Source>.*\\externalapis\\.*</Source>
      <Source>.*\\microsoft sdks\\.*</Source>
      <!-- Exclude files from VC++'s include directory -->
      <Source>.*\\vc\\include\\.*</Source>
      <Source>.*\\msclr\\.*</Source>
      <Source>.*\\ucrt\\.*</Source>
    </Exclude>
  </Sources>

  <!--
    The attributes include/exclude list.  This is matched against a managed method's attributes.
    The attributes are also checked on the method's type and the type's outter type, if the type is a nested type.
    Entries in this list are case-sensitive attribute type names.
  -->
  <Attributes>
    <Exclude>
      <Attribute>^System.Diagnostics.DebuggerHiddenAttribute$</Attribute>
      <Attribute>^System.Diagnostics.DebuggerNonUserCodeAttribute$</Attribute>
      <Attribute>^System.Runtime.CompilerServices.CompilerGeneratedAttribute$</Attribute>
      <Attribute>^System.CodeDom.Compiler.GeneratedCodeAttribute$</Attribute>
      <Attribute>^System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverageAttribute$</Attribute>
    </Exclude>
  </Attributes>

  <!--
    The function name include/exclude list.
    Entries in this list are case-sensitive.
  -->
  <Functions>
    <Exclude>
      <!-- Exclude symbols in the C++ std namespace -->
      <Function>^std::.*</Function>
      <!-- Exclude symbols in the ATL namespace -->
      <Function>^ATL::.*</Function>
      <!-- Exclude TEST_METHOD macro -->
      <Function>.*::__GetTestMethodInfo.*</Function>
      <!-- Exclude compiler generated entry point --> 
      <Function>.*__CxxPureMSILEntry.*</Function>
      <!-- Exclude symbols in the code coverage namespace -->
      <Function>^Microsoft::VisualStudio::CppCodeCoverageFramework::.*</Function>
      <!-- Exclude symbols in the unit test namespace -->
      <Function>^Microsoft::VisualStudio::CppUnitTestFramework::.*</Function>
      <!-- Exclude functions related to CPPUnitTest macros -->
      <Function>.*::YOU_CAN_ONLY_DESIGNATE_ONE_.*</Function>
      <!-- Exclude internal c functions -->
      <Function>^__.*</Function>
      <!-- Exclude internal cpp functions -->
      <Function>.*::__.*</Function>
    </Exclude>
  </Functions>

</CodeCoverage>